const admin = require("firebase-admin");

const projectId = "mevolve-dev";
process.env.FIRESTORE_EMULATOR_HOST = "localhost:8080";
admin.initializeApp({ projectId });

const db = admin.firestore();

// Add seed data
async function seedData(name) {
  try {
    const json = {
      userConfigs: `./fake-data/${name}/userConfigs.json`,
      userFutureActions: `./fake-data/${name}/userFutureActions.json`,
      userPastActions: `./fake-data/${name}/userPastActions.json`,
    };
    for (const path in json) {
      await db.collection(path).add(require(json[path]));
    }

    console.log("database seed was successful");
  } catch (error) {
    console.log(error, "database seed failed");
  }
}

// Delete seed data
async function resetData() {
  try {
    fetch(
      "http://localhost:8080/emulator/v1/projects/mevolve-dev/databases/(default)/documents",
      {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
    console.log("database reset was successful");
  } catch (error) {
    console.log(error, "database reset failed");
  }
}

module.exports = { seedData, resetData };
