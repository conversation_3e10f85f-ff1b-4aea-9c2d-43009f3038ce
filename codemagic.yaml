workflows:
  macos-workflow:
    name: macOS Workflow
    max_build_duration: 120
    integrations:
      app_store_connect: Publish App From Codemagic  # Name of your API key in CodeMagic UI
    environment:
      # These variables will be populated from GitHub Actions
      # Default values are provided as fallbacks if not passed from GitHub Actions
      vars:
        BUNDLE_ID: ${BUNDLE_ID:-app.mevolve.daily.mobile.dev}
        ENVIRONMENT: ${ENVIRONMENT:-dev}
        PROJECT_BUILD_NUMBER: ${PROJECT_BUILD_NUMBER:-1}
        BUILD_DATETIME: ${BUILD_DATETIME:-$(date +'%Y-%m-%d %H:%M:%S')}
        FLUTTER_VERSION: ${FLUTTER_VERSION:-stable}
        FLUTTER_CHANNEL: ${FLUTTER_CHANNEL:-stable}
      # Environment variable groups are now configured in the UI
      # No need to specify groups here in the new CodeMagic UI
      flutter: $FLUTTER_VERSION
      xcode: latest
      cocoapods: default
    scripts:
      - name: Debug Environment Variables
        script: |
          # Print all important environment variables for debugging
          echo "============= ENVIRONMENT VARIABLES ============="
          echo "BUNDLE_ID: $BUNDLE_ID"
          echo "ENVIRONMENT: $ENVIRONMENT"
          echo "PROJECT_BUILD_NUMBER: $PROJECT_BUILD_NUMBER"
          echo "BUILD_DATETIME: $BUILD_DATETIME"
          echo "FLUTTER_VERSION: $FLUTTER_VERSION"
          echo "FLUTTER_CHANNEL: $FLUTTER_CHANNEL"
          echo "FCI_BUILD_DIR: $FCI_BUILD_DIR"

          # Check for certificate private key in various formats
          echo "CM_CERTIFICATE_PRIVATE_KEY available: $(if [ -n "$CM_CERTIFICATE_PRIVATE_KEY" ]; then echo "Yes"; else echo "No"; fi)"
          echo "CERTIFICATE_PRIVATE_KEY available: $(if [ -n "$CERTIFICATE_PRIVATE_KEY" ]; then echo "Yes"; else echo "No"; fi)"

          # Check for App Store Connect API credentials
          echo "APP_STORE_CONNECT_ISSUER_ID available: $(if [ -n "$APP_STORE_CONNECT_ISSUER_ID" ]; then echo "Yes"; else echo "No"; fi)"
          echo "APP_STORE_CONNECT_KEY_IDENTIFIER available: $(if [ -n "$APP_STORE_CONNECT_KEY_IDENTIFIER" ]; then echo "Yes"; else echo "No"; fi)"
          echo "APP_STORE_CONNECT_PRIVATE_KEY available: $(if [ -n "$APP_STORE_CONNECT_PRIVATE_KEY" ]; then echo "Yes"; else echo "No"; fi)"

          # Check for CodeMagic specific variables
          echo "CM_ENV_VARS_COUNT: $(env | grep -c "^CM_" || echo "0")"
          echo "FCI_ENV_VARS_COUNT: $(env | grep -c "^FCI_" || echo "0")"

          # List all certificate-related variables (names only)
          echo "Certificate-related variables:"
          env | grep -i "CERTIFICATE\|CERT\|PRIVATE_KEY" | cut -d= -f1 || echo "None found"

          echo "================================================="

          # Fail fast on errors
          set -e

      - name: Set up keychain to be used for code signing
        script: |
          keychain initialize

      - name: Update bundle identifier in project files
        script: |
          # Fail fast on errors
          set -e

          echo "🔄 Updating bundle identifier to: $BUNDLE_ID"

          # 1. Update Info.plist (if it exists)
          INFO_PLIST_PATH="$FCI_BUILD_DIR/macos/Runner/Info.plist"
          if [ -f "$INFO_PLIST_PATH" ]; then
            echo "📝 Updating Info.plist bundle identifier"
            /usr/libexec/PlistBuddy -c "Set :CFBundleIdentifier $BUNDLE_ID" "$INFO_PLIST_PATH" || {
              echo "⚠️ Warning: Could not update CFBundleIdentifier in Info.plist"
              echo "Current Info.plist content:"
              /usr/libexec/PlistBuddy -c "Print" "$INFO_PLIST_PATH"
            }
          else
            echo "ℹ️ Info.plist not found at $INFO_PLIST_PATH"
          fi

          # 2. Update AppInfo.xcconfig (if it exists)
          XCCONFIG_PATH="$FCI_BUILD_DIR/macos/Runner/Configs/AppInfo.xcconfig"
          if [ -f "$XCCONFIG_PATH" ]; then
            echo "📝 Updating AppInfo.xcconfig bundle identifier"
            # Replace the PRODUCT_BUNDLE_IDENTIFIER line
            sed -i '' "s/PRODUCT_BUNDLE_IDENTIFIER = .*/PRODUCT_BUNDLE_IDENTIFIER = $BUNDLE_ID/" "$XCCONFIG_PATH" || {
              echo "⚠️ Warning: Could not update PRODUCT_BUNDLE_IDENTIFIER in AppInfo.xcconfig"
              echo "Current AppInfo.xcconfig content:"
              cat "$XCCONFIG_PATH"
            }
          else
            echo "ℹ️ AppInfo.xcconfig not found at $XCCONFIG_PATH"
          fi

          # 3. Create a temporary xcconfig file with our bundle ID
          TEMP_XCCONFIG="$FCI_BUILD_DIR/macos/bundle_id.xcconfig"
          echo "PRODUCT_BUNDLE_IDENTIFIER = $BUNDLE_ID" > "$TEMP_XCCONFIG"
          echo "📝 Created temporary xcconfig at $TEMP_XCCONFIG"

      - name: Fetch signing files with dynamic bundle ID
        script: |
          # Fail fast on errors
          set -e

          echo "🔐 Fetching signing files for bundle ID: $BUNDLE_ID"

          # In the new CodeMagic UI, certificate private key should be added as CM_CERTIFICATE_PRIVATE_KEY
          # Check for certificate private key in different environment variables
          if [ -n "$CM_CERTIFICATE_PRIVATE_KEY" ]; then
            echo "✅ Found certificate private key in CM_CERTIFICATE_PRIVATE_KEY"
            CERT_KEY="$CM_CERTIFICATE_PRIVATE_KEY"
          elif [ -n "$CERTIFICATE_PRIVATE_KEY" ]; then
            echo "✅ Found certificate private key in CERTIFICATE_PRIVATE_KEY"
            CERT_KEY="$CERTIFICATE_PRIVATE_KEY"
          else
            # Try to find any variable that might contain a certificate key
            echo "⚠️ Looking for certificate private key in environment variables..."
            for VAR_NAME in $(env | cut -d= -f1 | grep -i "CERTIFICATE\|CERT\|PRIVATE_KEY"); do
              VAR_VALUE=$(eval echo \$$VAR_NAME)
              if [ -n "$VAR_VALUE" ] && [[ "$VAR_VALUE" == *"BEGIN"* && "$VAR_VALUE" == *"PRIVATE KEY"* ]]; then
                echo "✅ Found certificate private key in $VAR_NAME"
                CERT_KEY="$VAR_VALUE"
                break
              fi
            done
          fi

          # If we found a certificate key, use it
          if [ -n "$CERT_KEY" ]; then
            # Create a temporary file for the certificate private key
            CERT_KEY_FILE=$(mktemp)
            echo "$CERT_KEY" > "$CERT_KEY_FILE"
            echo "📝 Created temporary certificate key file"

            # Fetch signing files with certificate private key
            app-store-connect fetch-signing-files "$BUNDLE_ID" \
              --platform MAC_OS \
              --type MAC_APP_STORE \
              --create \
              --certificate-key "$CERT_KEY_FILE"

            FETCH_RESULT=$?

            # Clean up the temporary file
            rm -f "$CERT_KEY_FILE"
          else
            echo "⚠️ Certificate private key is not available, trying without it"
            app-store-connect fetch-signing-files "$BUNDLE_ID" \
              --platform MAC_OS \
              --type MAC_APP_STORE \
              --create

            FETCH_RESULT=$?
          fi

          # Verify the signing files were fetched
          if [ $FETCH_RESULT -ne 0 ]; then
            echo "❌ Failed to fetch signing files for $BUNDLE_ID"
            echo "❌ Please add a CM_CERTIFICATE_PRIVATE_KEY variable in the CodeMagic UI"
            echo "❌ The value should be your certificate private key in PEM format"
            echo "❌ Make sure to mark it as 'Secure' and include the BEGIN/END PRIVATE KEY lines"
            exit 1
          else
            echo "✅ Successfully fetched signing files for $BUNDLE_ID"
          fi

      - name: Fetch Mac Installer Distribution certificates
        script: |
          # Fail fast on errors
          set -e

          echo "🔐 Fetching Mac Installer Distribution certificates"

          # In the new CodeMagic UI, certificate private key should be added as CM_CERTIFICATE_PRIVATE_KEY
          # Check for certificate private key in different environment variables
          if [ -n "$CM_CERTIFICATE_PRIVATE_KEY" ]; then
            echo "✅ Found certificate private key in CM_CERTIFICATE_PRIVATE_KEY"
            CERT_KEY="$CM_CERTIFICATE_PRIVATE_KEY"
          elif [ -n "$CERTIFICATE_PRIVATE_KEY" ]; then
            echo "✅ Found certificate private key in CERTIFICATE_PRIVATE_KEY"
            CERT_KEY="$CERTIFICATE_PRIVATE_KEY"
          else
            # Try to find any variable that might contain a certificate key
            echo "⚠️ Looking for certificate private key in environment variables..."
            for VAR_NAME in $(env | cut -d= -f1 | grep -i "CERTIFICATE\|CERT\|PRIVATE_KEY"); do
              VAR_VALUE=$(eval echo \$$VAR_NAME)
              if [ -n "$VAR_VALUE" ] && [[ "$VAR_VALUE" == *"BEGIN"* && "$VAR_VALUE" == *"PRIVATE KEY"* ]]; then
                echo "✅ Found certificate private key in $VAR_NAME"
                CERT_KEY="$VAR_VALUE"
                break
              fi
            done
          fi

          # If we found a certificate key, use it
          if [ -n "$CERT_KEY" ]; then
            # Create a temporary file for the certificate private key
            CERT_KEY_FILE=$(mktemp)
            echo "$CERT_KEY" > "$CERT_KEY_FILE"
            echo "📝 Created temporary certificate key file"

            # Try to fetch or create Mac Installer Distribution certificate with certificate private key
            app-store-connect certificates list --type MAC_INSTALLER_DISTRIBUTION --save --certificate-key "$CERT_KEY_FILE" || \
            app-store-connect certificates create --type MAC_INSTALLER_DISTRIBUTION --save --certificate-key "$CERT_KEY_FILE"

            CERT_RESULT=$?

            # Clean up the temporary file
            rm -f "$CERT_KEY_FILE"
          else
            echo "⚠️ Certificate private key is not available, trying without it"
            app-store-connect certificates list --type MAC_INSTALLER_DISTRIBUTION --save || \
            app-store-connect certificates create --type MAC_INSTALLER_DISTRIBUTION --save

            CERT_RESULT=$?
          fi

          if [ $CERT_RESULT -ne 0 ]; then
            echo "⚠️ Warning: Could not fetch or create Mac Installer Distribution certificate"
            # This is a warning, not an error, as you might already have the certificate
          else
            echo "✅ Successfully fetched/created Mac Installer Distribution certificate"
          fi

      - name: Set up signing certificate
        script: |
          keychain add-certificates

          # Verify certificates were added
          CERT_COUNT=$(keychain list-certificates | jq '. | length')
          echo "📊 Found $CERT_COUNT certificates in keychain"
          if [ "$CERT_COUNT" -eq 0 ]; then
            echo "⚠️ Warning: No certificates found in keychain"
          fi

      - name: Set up code signing settings on Xcode project
        script: |
          # Fail fast on errors
          set -e

          echo "🔧 Setting up code signing in Xcode project"
          xcode-project use-profiles

          # Directly update the bundle identifier in the Xcode project
          cd "$FCI_BUILD_DIR/macos"
          echo "📝 Updating Xcode project bundle identifier to $BUNDLE_ID"
          xcode-project build-setting "PRODUCT_BUNDLE_IDENTIFIER" "$BUNDLE_ID" --target Runner

          # Verify the bundle identifier was set correctly
          ACTUAL_BUNDLE_ID=$(xcode-project build-setting "PRODUCT_BUNDLE_IDENTIFIER" --target Runner)
          echo "🔍 Verifying bundle identifier: $ACTUAL_BUNDLE_ID"
          if [ "$ACTUAL_BUNDLE_ID" != "$BUNDLE_ID" ]; then
            echo "⚠️ Warning: Bundle identifier mismatch: $ACTUAL_BUNDLE_ID != $BUNDLE_ID"
            echo "Will attempt to use xcconfig during build instead"
          else
            echo "✅ Bundle identifier correctly set to $BUNDLE_ID in Xcode project"
          fi

      - name: Get Flutter packages
        script: |
          # Fail fast on errors
          set -e

          echo "📦 Getting Flutter packages"
          cd $FCI_BUILD_DIR
          flutter pub get

      - name: Install pods
        script: |
          echo "📦 Installing CocoaPods"
          find . -name "Podfile" -execdir pod install \;

      - name: Flutter analyze
        script: |
          echo "🔍 Running Flutter analyze"
          flutter analyze
        ignore_failure: true

      - name: Flutter unit tests
        script: |
          echo "🧪 Running Flutter tests"
          flutter test
        ignore_failure: true
      - name: Build Flutter macOS
        script: |
          # Fail fast on errors
          set -e

          echo "🏗️ Building Flutter macOS app"
          echo "Bundle ID: $BUNDLE_ID"
          echo "Build number: $PROJECT_BUILD_NUMBER"
          echo "Environment: $ENVIRONMENT"

          # Enable macOS desktop
          flutter config --enable-macos-desktop

          # Build with our custom xcconfig to ensure bundle ID is set
          TEMP_XCCONFIG="$FCI_BUILD_DIR/macos/bundle_id.xcconfig"

          # Build the app with all our parameters
          flutter build macos --release \
            --build-name=1.0.$PROJECT_BUILD_NUMBER \
            --build-number=$PROJECT_BUILD_NUMBER \
            --dart-define=ENVIRONMENT=$ENVIRONMENT \
            --dart-define=BUILD_DATETIME="$BUILD_DATETIME" \
            -t lib/main.dart \
            --xcconfig="$TEMP_XCCONFIG"

          # Verify the build succeeded
          if [ $? -ne 0 ]; then
            echo "❌ Flutter build failed"
            exit 1
          fi

          # Verify the app was built
          APP_PATH="$FCI_BUILD_DIR/build/macos/Build/Products/Release/mevolve.app"
          if [ ! -d "$APP_PATH" ]; then
            # Try alternative paths
            APP_PATH=$(find "$FCI_BUILD_DIR/build/macos" -name "*.app" -type d | head -n 1)
            if [ -z "$APP_PATH" ]; then
              echo "❌ Could not find built app"
              exit 1
            fi
          fi

          echo "✅ App built successfully at: $APP_PATH"

          # Verify the bundle identifier in the built app
          BUILT_BUNDLE_ID=$(/usr/libexec/PlistBuddy -c "Print :CFBundleIdentifier" "$APP_PATH/Contents/Info.plist")
          echo "🔍 Built app bundle identifier: $BUILT_BUNDLE_ID"
          if [ "$BUILT_BUNDLE_ID" != "$BUNDLE_ID" ]; then
            echo "⚠️ Warning: Built app has incorrect bundle identifier: $BUILT_BUNDLE_ID (expected: $BUNDLE_ID)"
          else
            echo "✅ Built app has correct bundle identifier: $BUNDLE_ID"
          fi

      - name: Package application
        script: |
          set -ex

          # Command to find the path to your generated app
          APP_PATH=$(find "$FCI_BUILD_DIR/build/macos" -name "*.app" -type d | head -n 1)
          if [ -z "$APP_PATH" ]; then
            echo "❌ Could not find built app"
            exit 1
          fi

          echo "📦 Packaging app: $APP_PATH"
          cd $(dirname "$APP_PATH")

          # Create an unsigned package
          PACKAGE_NAME=$(basename "$APP_PATH" .app).pkg
          xcrun productbuild --component "$APP_PATH" /Applications/ unsigned.pkg

          # Find the installer certificate common name in keychain
          INSTALLER_CERT_NAME=$(keychain list-certificates \
            | jq '.[]
            | select(.common_name
            | contains("Mac Developer Installer"))
            | .common_name' \
            | xargs)

          echo "🔐 Using installer certificate: $INSTALLER_CERT_NAME"

          # Sign the package
          xcrun productsign --sign "$INSTALLER_CERT_NAME" unsigned.pkg "$PACKAGE_NAME"

          # Verify the package was created
          if [ -f "$PACKAGE_NAME" ]; then
            echo "✅ Package created successfully: $PACKAGE_NAME"
            # Print package info
            pkgutil --check-signature "$PACKAGE_NAME" || echo "⚠️ Warning: Package signature verification failed"
          else
            echo "❌ Failed to create package"
            exit 1
          fi

          rm -f unsigned.pkg
    artifacts:
      - build/macos/**/*.pkg
    publishing:
      email:
        recipients:
          - <EMAIL>
          - <EMAIL>
        notify:
          success: true
          failure: false
      app_store_connect:
        # Use the integration defined at the workflow level
        auth: integration

        # Configuration related to TestFlight (optional)
        # Note: This action is performed during post-processing.
        submit_to_testflight: true
        beta_groups: # Specify the names of beta tester groups that will get access to the build once it has passed beta review.
          - group name 1
          - group name 2

        # Configuration related to App Store (optional)
        # Note: This action is performed during post-processing.
        submit_to_app_store: false