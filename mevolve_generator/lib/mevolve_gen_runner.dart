library mevolve_generator;

import 'dart:async';
import 'dart:io';

import 'package:build/build.dart';
import 'package:mevolve_generator/core/mevolve_generator.dart';

Builder build(BuilderOptions options) => MevolveGenBuilder();

class MevolveGenBuilder extends Builder {
  // Source file paths (generator-only configs, not bundled in app)
  static const String _configDirPath = 'mevolve_generator_configs/';
  static const String releaseConfigPath = '${_configDirPath}releaseConfig.json';
  static const String translationsPath = '${_configDirPath}translations.json';
  static const String colorsPath = '${_configDirPath}colors.json';

  // Path where releaseConfig.json will be copied to
  static const String functionsFolderPath = 'functions/src/';
  static const String functionsCopyFileName = 'releaseConfig.json';
  static const String functionsCopyPath =
      '$functionsFolderPath$functionsCopyFileName';

  // Output directory for all generated files
  static const String outputDir = 'lib/generated/';

  late final generator = MevolveGenerator(
    releaseConfigFile: File(releaseConfigPath),
    translationsFile: File(translationsPath),
    colorsFile: File(colorsPath),
    outputDir: outputDir,
  );

  @override
  Future<void> build(BuildStep buildStep) async {
    // Validate input files
    if (!generator.validateInputFiles()) {
      log.severe('Invalid input files detected');
      return;
    }

    // Clean up existing files before generating new ones
    await _cleanupExistingFiles();

    // Copy releaseConfig.json to functions folder
    await _copyReleaseConfigToFunctions(buildStep);

    // Generate all files using the generator
    await generator.build(
      writer: (contents, paths) {
        for (final path in paths) {
          buildStep.writeAsString(
            AssetId(buildStep.inputId.package, path),
            contents,
          );
        }
      },
    );

    log.info('Generator completed successfully');
  }

  /// Deletes existing generated files before creating new ones
  Future<void> _cleanupExistingFiles() async {
    try {
      log.info('Cleaning up existing generated files...');

      // Get all the file paths that will be generated
      final allOutputFiles = generator.getAllOutputFilePaths();
      allOutputFiles.add(functionsCopyPath);

      // Delete each file if it exists
      for (final filePath in allOutputFiles) {
        final file = File(filePath);
        if (await file.exists()) {
          await file.delete();
          log.fine('Deleted existing file: $filePath');
        }
      }

      log.info('Cleanup completed successfully');
    } catch (e) {
      log.warning('Error during cleanup: $e');
    }
  }

  /// Copies the releaseConfig.json file to the functions folder
  Future<void> _copyReleaseConfigToFunctions(BuildStep buildStep) async {
    try {
      // Read the source file content
      final releaseConfigContent = await File(releaseConfigPath).readAsString();

      // Ensure functions directory exists
      final functionsDir = Directory(functionsFolderPath);
      if (!await functionsDir.exists()) {
        await functionsDir.create(recursive: true);
        log.info('Created functions directory: $functionsFolderPath');
      }

      // Write to the destination, overwriting if it exists
      buildStep.writeAsString(
        AssetId(buildStep.inputId.package, functionsCopyPath),
        releaseConfigContent,
      );

      log.info('Successfully copied releaseConfig.json to functions folder');
    } catch (e) {
      log.severe('Failed to copy releaseConfig.json to functions folder: $e');
    }
  }

  @override
  Map<String, List<String>> get buildExtensions {
    // Get all output paths
    final outputPaths = generator.getAllOutputFilePaths();
    outputPaths.add(functionsCopyPath);

    return {
      r'$package$': outputPaths,
    };
  }
}
