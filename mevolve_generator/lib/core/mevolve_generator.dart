import 'dart:convert';
import 'dart:io';
import 'package:mevolve_generator/generators/release_config/release_config_generator.dart';
import 'package:mevolve_generator/generators/translations/translations_generator.dart';
import 'package:mevolve_generator/generators/colors/colors_generator.dart';

typedef FileWriter = void Function(String contents, List<String> paths);

class MevolveGenerator {
  MevolveGenerator({
    required this.releaseConfigFile,
    required this.translationsFile,
    required this.colorsFile,
    this.outputDir = 'lib/generated/',
  });

  final File releaseConfigFile;
  final File translationsFile;
  final File colorsFile;
  final String outputDir;

  // Individual generators
  late final ReleaseConfigGenerator _releaseConfigGenerator =
      ReleaseConfigGenerator(outputPaths: [outputDir]);

  late final TranslationsGenerator _translationsGenerator =
      TranslationsGenerator(outputPaths: [outputDir]);

  late final ColorsGenerator _colorsGenerator =
      ColorsGenerator(outputPaths: [outputDir]);

  Future<void> build({FileWriter? writer}) async {
    if (writer == null) return;

    stdout.writeln('MevolveGen starting...');

    // Load JSON files directly
    final releaseConfigMap = _loadJsonFile(releaseConfigFile);
    final translationsData = _loadJsonFile(translationsFile);
    final colorsData = _loadJsonFile(colorsFile);

    // Extract translations and colors data
    final translationsMap =
        translationsData['data']['en'] as Map<String, dynamic>;
    final colorsMap = colorsData['data'] as Map<String, dynamic>;

    // Generate release config
    _releaseConfigGenerator.generate(
      releaseConfigMap,
      colorsMap,
      writer,
      sourceFile: releaseConfigFile,
    );

    // Generate translations
    _translationsGenerator.generate(
      translationsMap,
      writer,
      sourceFile: translationsFile,
    );

    // Generate colors
    _colorsGenerator.generate(
      colorsMap,
      writer,
      sourceFile: colorsFile,
    );

    stdout.writeln('MevolveGen finished.');
  }

  /// Load and parse a JSON file
  Map<String, dynamic> _loadJsonFile(File file) {
    try {
      final content = file.readAsStringSync();
      return jsonDecode(content) as Map<String, dynamic>;
    } catch (e) {
      throw Exception('Failed to load JSON file ${file.path}: $e');
    }
  }

  /// Get all output file paths for cleanup purposes
  List<String> getAllOutputFilePaths() {
    return [
      '$outputDir${_releaseConfigGenerator.fileName}',
      '$outputDir${_translationsGenerator.fileName}',
      '$outputDir${_colorsGenerator.fileName}',
    ];
  }

  /// Validate all input files
  bool validateInputFiles() {
    return releaseConfigFile.existsSync() &&
        translationsFile.existsSync() &&
        colorsFile.existsSync();
  }
}
