String get header {
  return '''/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  Mevolve Generator
/// *****************************************************

''';
}

String get colorsImports {
  return '''
import 'dart:convert';
import 'dart:ui';

import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/utilities/utility_methods.dart';
''';
}

String get colorsConstructor {
  return '''
MeAppColors({
    required this.colorsMap,
    this.currentColor,
  });

  final Map<String, dynamic> colorsMap;
  final String? currentColor;

  static MeAppColors? _instance;

  // static MeAppColors get instance => _instance;

  static MeAppColors get instance {
    assert(_instance != null,
        'No instance of MeColors was loaded. Try to initialize the MeColors delegate before accessing MeTranslations.instance.',);

    return _instance!;
  }

''';
}

String colorsMethods(String colorName) {
  return '''
static MeAppColors load(Map<String, dynamic> colorsMap,
      {String? currentColor,}) {
    final instance = MeAppColors(
      colorsMap: colorsMap,
      currentColor: currentColor,
    );
    MeAppColors._instance = instance;
    return instance;
  }

  void setCurrentColor({required String currentColor}) {
    // Extract available theme keys
    final themes = colorsMap['data']['themes'];
    final availableThemes = themes.keys.toList();

    // Determine the primary theme or fallback to the first one
    final wantedTheme = currentColor.capitalize();
    final selectedTheme = availableThemes.contains(wantedTheme)
        ? wantedTheme
        : availableThemes.first;
  
    _lightColorScheme = _updateColorValues(
      _lightColorScheme,
      themes[selectedTheme]['Light'],
    );
    _darkColorScheme = _updateColorValues(
      _darkColorScheme,
      themes[selectedTheme]['Dark'],
    );
  }

''';
}
