import 'dart:convert';
import 'dart:io';
import 'package:dart_style/dart_style.dart';
import 'package:path/path.dart';
import 'package:mevolve_generator/generators/colors/colors_helpers.dart';

typedef FileWriter = void Function(String contents, List<String> paths);

class ColorsGenerator {
  ColorsGenerator({
    this.fileName = 'me_app_colors.gen.dart',
    this.outputPaths = const ['lib/generated/'],
  });

  final String fileName;
  final List<String> outputPaths;

  void generate(
    Map<String, dynamic> colorsMap,
    FileWriter writer, {
    required File sourceFile,
  }) {
    // Create directories if they don't exist
    for (final basePath in outputPaths) {
      final directory = Directory(normalize(basePath));
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }
    }

    final formatter = DartFormatter(pageWidth: 80, lineEnding: '\n');

    // Generate output paths
    final genPaths = outputPaths
        .map((basePath) => normalize(join(basePath, fileName)))
        .toList();

    final buffer = StringBuffer();
    Map<String, dynamic> mainColorsMap = colorsMap['themes'];
    String colorName = colorsMap['defaultUserTheme'];
    String className = 'MeAppColors';
    buffer.writeln(header);
    buffer.writeln(colorsImports);
    buffer.writeln('class $className {');

    // Constructor
    buffer.writeln(colorsConstructor);

    buffer.writeln(colorsMethods(colorName));

    buffer.writeln('''
    MeColorScheme _updateColorValues(
    MeColorScheme currentColor,
    Map<String, dynamic> updateMap,
    ) {
    final colorScheme = MeColorScheme(
    ''');

    final colorMap = mainColorsMap[mainColorsMap.keys.first]
            [mainColorsMap[mainColorsMap.keys.first].keys.first]
        as Map<String, dynamic>;
    for (var item in colorMap.keys) {
      final num = item.substring(6);
      buffer.writeln(
        'color$num: parseHextoColor(updateMap[\'Color $num\']) ?? currentColor.color$num,',
      );
    }
    buffer.writeln('''
    );
    return colorScheme;
    }
  ''');

    for (String colorType in mainColorsMap[colorName].keys) {
      buffer.writeln('/// $colorType Theme Colors');
      final colorVarName =
          ('${colorType[0].toLowerCase()}${colorType.substring(1)}Color');

      final colorMap =
          mainColorsMap[colorName][colorType] as Map<String, dynamic>;
      for (var item in colorMap.keys) {
        final num = item.substring(6);
        String? colorVal = colorMap['Color $num'];
        if (colorVal == null) {
          continue;
        }
        if (colorVal.length == 7) {
          colorVal = 'FF${colorVal.substring(1)}';
        } else if (colorVal.length == 9) {
          colorVal = colorVal.substring(7) + colorVal.substring(1, 7);
        }
        buffer.writeln(
          'static const Color $colorVarName$num = Color(0x$colorVal);',
        );
      }
      buffer.writeln();
      buffer.writeln(
        'MeColorScheme get ${colorVarName}Scheme => _${colorVarName}Scheme;',
      );
      buffer.writeln();
      buffer.writeln(
        'static MeColorScheme _${colorVarName}Scheme = const MeColorScheme(',
      );
      for (var item in colorMap.keys) {
        final num = item.substring(6);
        buffer.writeln(
          'color$num: $colorVarName$num,',
        );
      }
      buffer.writeln(');');
      buffer.writeln();
    }

    // Add static method to return the full colors map
    buffer.writeln('''
  /// Returns the complete colors configuration map from generator
  static Map<String, dynamic> getOriginalColorsMap() {
    final colorsMap = ${_generateColorsMapString(sourceFile)};
    return Map<String, dynamic>.from(jsonDecode(jsonEncode(colorsMap)));
  }
''');

    buffer.writeln('}');

    final formattedContent = formatter.format(buffer.toString());
    writer(formattedContent, genPaths);

    for (final path in genPaths) {
      stdout.writeln('Generated Colors: $path');
    }
  }

  /// Helper method to generate a Dart representation of the colors map
  String _generateColorsMapString(File sourceFile) {
    try {
      final fileContent = sourceFile.readAsStringSync();
      // Parse the JSON and return it as a Dart map string
      final jsonContent = jsonDecode(fileContent) as Map<String, dynamic>;
      return _mapToString(jsonContent);
    } catch (e) {
      throw Exception('Failed to parse colors file ${sourceFile.path}: $e');
    }
  }

  /// Helper method to convert any map/list to a Dart string representation
  String _mapToString(dynamic obj) {
    if (obj == null) {
      return 'null';
    } else if (obj is Map<String, dynamic>) {
      if (obj.isEmpty) return '{}';
      final entries = obj.entries
          .map(
            (e) => "'${e.key}': ${_mapToString(e.value)}",
          )
          .join(',\n    ');
      return '{\n    $entries,\n  }';
    } else if (obj is List) {
      if (obj.isEmpty) return '[]';
      final items = obj.map((item) => _mapToString(item)).join(', ');
      return '[$items]';
    } else if (obj is String) {
      // Handle multiline strings and escape quotes properly
      if (obj.contains('\n') || obj.contains('\r')) {
        // Use triple quotes for multiline strings and escape any existing triple quotes and dollar signs
        final escaped =
            obj.replaceAll("'''", "\\'\\'\\'").replaceAll('\$', '\\\$');
        return "'''$escaped'''";
      } else {
        // Escape single quotes and dollar signs in single-line strings
        final escaped = obj.replaceAll("'", "\\'").replaceAll('\$', '\\\$');
        return "'$escaped'";
      }
    } else if (obj is num) {
      return obj.toString();
    } else if (obj is bool) {
      return obj.toString();
    } else {
      return "'${obj.toString()}'";
    }
  }
}
