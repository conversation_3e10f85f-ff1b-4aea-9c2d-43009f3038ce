import 'dart:convert';
import 'dart:io';
import 'package:dart_style/dart_style.dart';
import 'package:path/path.dart';
import 'package:mevolve_generator/generators/translations/translations_helpers.dart';

typedef FileWriter = void Function(String contents, List<String> paths);

class TranslationsGenerator {
  TranslationsGenerator({
    this.fileName = 'metranslations.gen.dart',
    this.outputPaths = const ['lib/generated/'],
  });

  final String fileName;
  final List<String> outputPaths;

  void generate(
    Map<String, dynamic> translationsMap,
    FileWriter writer, {
    required File sourceFile,
  }) {
    // Create directories if they don't exist
    for (final basePath in outputPaths) {
      final directory = Directory(normalize(basePath));
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }
    }

    final formatter = DartFormatter(pageWidth: 80, lineEnding: '\n');

    // Generate output paths
    final genPaths = outputPaths
        .map((basePath) => normalize(join(basePath, fileName)))
        .toList();

    final buffer = StringBuffer();

    String className = 'MeTranslations';
    buffer.writeln(header);
    buffer.writeln(ignore);
    buffer.writeln(translationsImports);
    buffer.writeln('class $className {');

    // CONSTRUCTOR
    buffer.writeln('const MeTranslations({required this.translationsMap});');
    buffer.writeln();
    buffer.writeln('final Map<String, dynamic> translationsMap;');
    buffer.writeln();
    buffer.writeln(translationsMethods);

    for (var key in translationsMap.keys) {
      if (!key.startsWith('@')) {
        if (translationsMap.containsKey('@$key')) {
          buffer.writeln('MeString $key({');
          List<Map<String, dynamic>> vars =
              List<Map<String, dynamic>>.from(translationsMap['@$key']);
          for (var item in vars) {
            buffer.writeln('required String ${item['var']},');
          }
          buffer.writeln('}) {');
          buffer.writeln('String result = translationsMap[\'$key\'];');
          for (var item in vars) {
            buffer.writeln(
              'result = result.replaceAll(\'{{${item['var']}}}\', ${item['var']});',
            );
          }
          buffer.writeln('return MeString(result);');
          buffer.writeln('}');
        } else {
          buffer.writeln(
            'MeString get $key => MeString(translationsMap[\'$key\']);',
          );
          buffer.writeln();
        }
      }
    }

    // Add static method to return the full translations map
    buffer.writeln('''
  /// Returns the complete translations configuration map from generator
  static Map<String, dynamic> getOriginalTranslationsMap() {
    final translationsMap = ${_generateTranslationsMapString(sourceFile)};
    return Map<String, dynamic>.from(jsonDecode(jsonEncode(translationsMap)));
  }
''');

    buffer.writeln('}');

    final formattedContent = formatter.format(buffer.toString());
    writer(formattedContent, genPaths);

    for (final path in genPaths) {
      stdout.writeln('Generated Translations: $path');
    }
  }

  /// Helper method to generate a Dart representation of the translations map
  String _generateTranslationsMapString(File sourceFile) {
    // If source file is provided, read and return its exact content
    try {
      final fileContent = sourceFile.readAsStringSync();
      // Parse the JSON and return it as a Dart map string
      final jsonContent = jsonDecode(fileContent) as Map<String, dynamic>;
      return _mapToString(jsonContent);
    } catch (e) {
      throw Exception(
        'Failed to parse translations file ${sourceFile.path}: $e',
      );
    }
  }

  /// Helper method to convert any map/list to a Dart string representation
  String _mapToString(dynamic obj) {
    if (obj == null) {
      return 'null';
    } else if (obj is Map<String, dynamic>) {
      if (obj.isEmpty) return '{}';
      final entries = obj.entries
          .map(
            (e) => "'${e.key}': ${_mapToString(e.value)}",
          )
          .join(',\n    ');
      return '{\n    $entries,\n  }';
    } else if (obj is List) {
      if (obj.isEmpty) return '[]';
      final items = obj.map((item) => _mapToString(item)).join(', ');
      return '[$items]';
    } else if (obj is String) {
      // Handle multiline strings and escape quotes properly
      if (obj.contains('\n') || obj.contains('\r')) {
        // Use triple quotes for multiline strings and escape any existing triple quotes and dollar signs
        final escaped =
            obj.replaceAll("'''", "\\'\\'\\'").replaceAll('\$', '\\\$');
        return "'''$escaped'''";
      } else {
        // Escape single quotes and dollar signs in single-line strings
        final escaped = obj.replaceAll("'", "\\'").replaceAll('\$', '\\\$');
        return "'$escaped'";
      }
    } else if (obj is num) {
      return obj.toString();
    } else if (obj is bool) {
      return obj.toString();
    } else {
      return "'${obj.toString()}'";
    }
  }
}
