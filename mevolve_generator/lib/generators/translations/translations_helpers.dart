String get header {
  return '''/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  Mevolve Generator
/// *****************************************************

''';
}

String get ignore {
  return '''// ignore_for_file: non_constant_identifier_names
  
''';
}

String get translationsImports {
  return '''import 'dart:async';
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/translations/metranslations_delegate.dart';
  
''';
}

String get translationsMethods {
  return '''static MeTranslations? _instance;

  static MeTranslations get instance {
    assert(_instance != null,
        'No instance of MeTranslations was loaded. Try to initialize the MeTranslations delegate before accessing MeTranslations.instance.');
    return _instance!;
  }

  static const MeTranslationsDelegate delegate = MeTranslationsDelegate();

  static MeTranslations load(Map<String, dynamic> translationsMap) {
    final instance = MeTranslations(translationsMap: translationsMap);
    MeTranslations._instance = instance;
    return instance;
  }

  /// Create a temporary MeTranslations instance for a specific locale without affecting the global singleton
  static Future<MeTranslations> createTemporaryForLocale(
    Map<String, dynamic> translationsMap,
  ) async {
    // Create a temporary instance without updating the global _instance
    return MeTranslations(translationsMap: translationsMap);
  }

''';
}
