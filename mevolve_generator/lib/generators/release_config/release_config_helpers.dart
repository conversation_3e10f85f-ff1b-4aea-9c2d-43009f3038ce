import 'dart:convert';
import 'dart:io';

String get header {
  return '''/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  Mevolve Generator
/// *****************************************************

''';
}

// Helper function to safely escape string content for single quotes
String escapeSingleQuotes(String text) {
  return text.replaceAll("'", "\\'");
}

// convert a Dart List<String> into a string representation of that list where each string item is properly quoted.
String getListwithQuotes(List<String> list) {
  String listString = '[';
  for (var item in list) {
    // Use the escapeSingleQuotes function for consistency
    String escapedItem = escapeSingleQuotes(item);
    listString += '\'$escapedItem\'';
    if (item != list.last) {
      listString += ', ';
    }
  }
  listString += ']';
  return listString;
}

String _generateReleaseConfigMapString(File sourceFile) {
  // If source file is provided, read and return its exact content
  try {
    final fileContent = sourceFile.readAsStringSync();
    // Parse the JSON and return it as a Dart map string
    final jsonContent = jsonDecode(fileContent) as Map<String, dynamic>;
    return _mapToString(jsonContent);
  } catch (e) {
    throw Exception(
      'Failed to parse releaseConfig file ${sourceFile.path}: $e',
    );
  }
}

/// Helper method to convert any map/list to a Dart string representation
String _mapToString(dynamic obj) {
  if (obj == null) {
    return 'null';
  } else if (obj is Map<String, dynamic>) {
    if (obj.isEmpty) return '{}';
    final entries = obj.entries
        .map(
          (e) => "'${e.key}': ${_mapToString(e.value)}",
        )
        .join(',\n    ');
    return '{\n    $entries,\n  }';
  } else if (obj is List) {
    if (obj.isEmpty) return '[]';
    final items = obj.map((item) => _mapToString(item)).join(', ');
    return '[$items]';
  } else if (obj is String) {
    // Handle multiline strings and escape quotes properly
    if (obj.contains('\n') || obj.contains('\r')) {
      // Use triple quotes for multiline strings and escape any existing triple quotes and dollar signs
      final escaped =
          obj.replaceAll("'''", "\\'\\'\\'").replaceAll('\$', '\\\$');
      return "'''$escaped'''";
    } else {
      // Escape single quotes and dollar signs in single-line strings
      final escaped = obj.replaceAll("'", "\\'").replaceAll('\$', '\\\$');
      return "'$escaped'";
    }
  } else if (obj is num) {
    return obj.toString();
  } else if (obj is bool) {
    return obj.toString();
  } else {
    return "'${obj.toString()}'";
  }
}

/// New method to generate environment config
String generateEnvironmentConfig(
  Map<String, dynamic> map,
  String environment,
  String defaultThemeColor,
  String defaultThemeMode,
) {
  final envData = map['envs'][environment] ?? map['envs']['dev'];
  final enabledPlatforms = List<String>.from(envData['enabledPlatforms'] ?? []);

  return '''
    Environment.$environment: EnvironmentConfig(
      id: '${escapeSingleQuotes(map['id'] as String)}',
      dbVersion: ${map['dbVersion'] as int},
      rid: ${map['rid'].toDouble()},
      androidAvailability: ${enabledPlatforms.contains('android').toString()},
      iosAvailability: ${enabledPlatforms.contains('ios').toString()},
      webAvailability: ${enabledPlatforms.contains('web').toString()},
      windowsAvailability: ${enabledPlatforms.contains('windows').toString()},
      macAvailability: ${enabledPlatforms.contains('mac').toString()},
      linuxAvailability: ${enabledPlatforms.contains('linux').toString()},
      enabledSegments: ${getListwithQuotes(
    List<String>.from(
      envData['enabledSegments'] ?? ['ALL'],
    ),
  )},
      debugScreen: ${getListwithQuotes(
    List<String>.from(
      envData['featureFlags']?['debugScreen'] ?? ['ALL'],
    ),
  )},
      support: ${getListwithQuotes(
    List<String>.from(
      envData['featureFlags']?['support'] ?? ['ALL'],
    ),
  )},
      allowShorebirdPatchInstall: ${getListwithQuotes(
    List<String>.from(
      envData['allowShorebirdPatchInstall'] ?? [],
    ),
  )},
      defaultThemeColor: '${escapeSingleQuotes(defaultThemeColor.toLowerCase())}',
      defaultThemeMode: '${escapeSingleQuotes(defaultThemeMode.toLowerCase())}',
      maxPasscodeAttempts: ${envData['parameters']?['maxPasscodeAttempts_n'] ?? 5},
      passcodeReAuthTimeout: ${envData['parameters']?['passcodeReAuthTimeout_n'] ?? 300000},
      modalBarrierOpacity: ${envData['parameters']?['modalBarrierOpacity_n'] ?? 70},
      colorsVersion: ${envData['parameters']?['colorsVersion_n'] ?? 1},
      showAds: ${envData['parameters']?['showAds_b'] ?? false},
      adWaitTimeInSec: ${envData['parameters']?['adWaitTimeInSec_n'] ?? 5},
      noOfInteractionsForAds: ${envData['parameters']?['noOfInteractionsForAds_n'] ?? 5},
      gracePeriodInMin: ${envData['parameters']?['gracePeriodInMin_n'] ?? 43200},
      translationsVersion: ${envData['parameters']?['translationsVersion_n'] ?? 1},
      externalDescription: {
        'en': '${escapeSingleQuotes(map['externalDescription']?['en'] ?? '')}',
        'de': '${escapeSingleQuotes(map['externalDescription']?['de'] ?? '')}',
        'fr': '${escapeSingleQuotes(map['externalDescription']?['fr'] ?? '')}',
        'es': '${escapeSingleQuotes(map['externalDescription']?['es'] ?? '')}',
        'it': '${escapeSingleQuotes(map['externalDescription']?['it'] ?? '')}',
        'pt': '${escapeSingleQuotes(map['externalDescription']?['pt'] ?? '')}',
      },
      cloudUpdatedAt: DateTime.parse('${DateTime.now().toIso8601String()}'),
    ),
''';
}

String mapReleaseConfig(
  Map<String, dynamic> map,
  String defaultThemeColor,
  String defaultThemeMode,
  File sourceFile,
) {
  // Generate the complete file with all environments
  return '''
// ignore_for_file: dangling_library_doc_comments
/// Enum representing available environments
enum Environment {
  dev,
  qa,
  staging,
  prod,
  hotfix,
}

/// Configuration for a specific environment
class EnvironmentConfig {
  EnvironmentConfig({
    required this.id,
    required this.dbVersion,
    required this.rid,
    required this.androidAvailability,
    required this.iosAvailability,
    required this.webAvailability,
    required this.windowsAvailability,
    required this.macAvailability,
    required this.linuxAvailability,
    required this.enabledSegments,
    required this.debugScreen,
    required this.support,
    required this.allowShorebirdPatchInstall,
    required this.defaultThemeColor,
    required this.defaultThemeMode,
    required this.maxPasscodeAttempts,
    required this.passcodeReAuthTimeout,
    required this.modalBarrierOpacity,
    required this.colorsVersion,
    required this.showAds,
    required this.adWaitTimeInSec,
    required this.noOfInteractionsForAds,
    required this.gracePeriodInMin,
    required this.translationsVersion,
    required this.externalDescription,
    required this.cloudUpdatedAt,
  });
  final String id;
  final int dbVersion;
  final double rid;
  final bool androidAvailability;
  final bool iosAvailability;
  final bool webAvailability;
  final bool windowsAvailability;
  final bool macAvailability;
  final bool linuxAvailability;
  final List<String> enabledSegments;
  final List<String> debugScreen;
  final List<String> support;
  final List<String> allowShorebirdPatchInstall;
  final String defaultThemeColor;
  final String defaultThemeMode;
  final int maxPasscodeAttempts;
  final int passcodeReAuthTimeout;
  final int modalBarrierOpacity;
  final int colorsVersion;
  final bool showAds;
  final int adWaitTimeInSec;
  final int noOfInteractionsForAds;
  final int gracePeriodInMin;
  final int translationsVersion;
  final Map<String, dynamic> externalDescription;
  final DateTime cloudUpdatedAt;

  EnvironmentConfig copyWith({
    String? id,
    int? dbVersion,
    double? rid,
    bool? androidAvailability,
    bool? iosAvailability,
    bool? webAvailability,
    bool? windowsAvailability,
    bool? macAvailability,
    bool? linuxAvailability,
    List<String>? enabledSegments,
    List<String>? debugScreen,
    List<String>? support,
    List<String>? allowShorebirdPatchInstall,
    String? defaultThemeColor,
    String? defaultThemeMode,
    int? maxPasscodeAttempts,
    int? passcodeReAuthTimeout,
    int? modalBarrierOpacity,
    int? colorsVersion,
    bool? showAds,
    int? adWaitTimeInSec,
    int? noOfInteractionsForAds,
    int? gracePeriodInMin,
    int? translationsVersion,
    Map<String, dynamic>? externalDescription,
    DateTime? cloudUpdatedAt,
  }) {
    return EnvironmentConfig(
      id: id ?? this.id,
      dbVersion: dbVersion ?? this.dbVersion,
      rid: rid ?? this.rid,
      androidAvailability: androidAvailability ?? this.androidAvailability,
      iosAvailability: iosAvailability ?? this.iosAvailability,
      webAvailability: webAvailability ?? this.webAvailability,
      windowsAvailability: windowsAvailability ?? this.windowsAvailability,
      macAvailability: macAvailability ?? this.macAvailability,
      linuxAvailability: linuxAvailability ?? this.linuxAvailability,
      enabledSegments: enabledSegments ?? this.enabledSegments,
      debugScreen: debugScreen ?? this.debugScreen,
      support: support ?? this.support,
      allowShorebirdPatchInstall: allowShorebirdPatchInstall ?? this.allowShorebirdPatchInstall,
      defaultThemeColor: defaultThemeColor ?? this.defaultThemeColor,
      defaultThemeMode: defaultThemeMode ?? this.defaultThemeMode,
      maxPasscodeAttempts: maxPasscodeAttempts ?? this.maxPasscodeAttempts,
      passcodeReAuthTimeout: passcodeReAuthTimeout ?? this.passcodeReAuthTimeout,
      modalBarrierOpacity: modalBarrierOpacity ?? this.modalBarrierOpacity,
      colorsVersion: colorsVersion ?? this.colorsVersion,
      showAds: showAds ?? this.showAds,
      adWaitTimeInSec: adWaitTimeInSec ?? this.adWaitTimeInSec,
      noOfInteractionsForAds: noOfInteractionsForAds ?? this.noOfInteractionsForAds,
      gracePeriodInMin: gracePeriodInMin ?? this.gracePeriodInMin,
      translationsVersion: translationsVersion ?? this.translationsVersion,
      externalDescription: externalDescription ?? this.externalDescription,
      cloudUpdatedAt: cloudUpdatedAt ?? this.cloudUpdatedAt,
    );
  }
}

class ReleaseConfig {
  ReleaseConfig._();

  static final ReleaseConfig _instance = ReleaseConfig._();

  static ReleaseConfig get instance => _instance;

  // Get the current environment from flutter build flavor
  static final Environment _currentEnvironment = _getCurrentEnvironmentFromFlavor();

  // Method to get environment from flutter build flavor
  static Environment _getCurrentEnvironmentFromFlavor() {
    const String flavorName = String.fromEnvironment('flavor', defaultValue: 'dev');
    try {
      return Environment.values.byName(flavorName);
    } catch (e) {
      // Fallback to dev if invalid flavor name
      return Environment.dev;
    }
  }

  // Map storing config for all environments
  final Map<Environment, EnvironmentConfig> _environmentConfigs = {
    ${generateEnvironmentConfig(map, 'dev', defaultThemeColor, defaultThemeMode)}
    ${generateEnvironmentConfig(map, 'qa', defaultThemeColor, defaultThemeMode)}
    ${generateEnvironmentConfig(map, 'staging', defaultThemeColor, defaultThemeMode)}
    ${generateEnvironmentConfig(map, 'prod', defaultThemeColor, defaultThemeMode)}
    ${generateEnvironmentConfig(map, 'hotfix', defaultThemeColor, defaultThemeMode)}
  };

  // Helper method to get current environment config
  EnvironmentConfig get currentConfig {
    return _environmentConfigs[_currentEnvironment] ??
        _environmentConfigs[Environment.dev]!;
  }

  // Update method that takes EnvironmentConfig directly, not ReleaseConfigModel
  void updateConfig(EnvironmentConfig config) {
    _environmentConfigs[_currentEnvironment] = config;
  }

  // Getters for all properties - they now return the current environment's values
  String get id => currentConfig.id;
  int get dbVersion => currentConfig.dbVersion;
  double get rid => currentConfig.rid;
  bool get androidAvailability => currentConfig.androidAvailability;
  bool get iosAvailability => currentConfig.iosAvailability;
  bool get webAvailability => currentConfig.webAvailability;
  bool get windowsAvailability => currentConfig.windowsAvailability;
  bool get macAvailability => currentConfig.macAvailability;
  bool get linuxAvailability => currentConfig.linuxAvailability;
  List<String> get enabledSegments => currentConfig.enabledSegments;
  List<String> get debugScreen => currentConfig.debugScreen;
  List<String> get support => currentConfig.support;
  List<String> get allowShorebirdPatchInstall =>
      currentConfig.allowShorebirdPatchInstall;
  String get defaultThemeColor => currentConfig.defaultThemeColor;
  String get defaultThemeMode => currentConfig.defaultThemeMode;
  int get maxPasscodeAttempts => currentConfig.maxPasscodeAttempts;
  int get modalBarrierOpacity => currentConfig.modalBarrierOpacity;
  int get passcodeReAuthTimeout => currentConfig.passcodeReAuthTimeout;
  int get colorsVersion => currentConfig.colorsVersion;
  bool get showAds => currentConfig.showAds;
  int get adWaitTimeInSec => currentConfig.adWaitTimeInSec;
  int get noOfInteractionsForAds => currentConfig.noOfInteractionsForAds;
  int get gracePeriodInMin => currentConfig.gracePeriodInMin;
  int get translationsVersion => currentConfig.translationsVersion;
  Map<String, dynamic> get externalDescription =>
      currentConfig.externalDescription;
  DateTime get cloudUpdatedAt => currentConfig.cloudUpdatedAt;

  /// Returns the complete release configuration map from generator
  static Map<String, dynamic> getOriginalReleaseConfigMap() {
    final releaseConfigMap = ${_generateReleaseConfigMapString(sourceFile)};
    return Map<String, dynamic>.from(jsonDecode(jsonEncode(releaseConfigMap)));
  }
}
''';
}
