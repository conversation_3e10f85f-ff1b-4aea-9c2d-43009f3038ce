import 'dart:io';
import 'package:dart_style/dart_style.dart';
import 'package:path/path.dart';
import 'package:mevolve_generator/generators/release_config/release_config_helpers.dart';

typedef FileWriter = void Function(String contents, List<String> paths);

class ReleaseConfigGenerator {
  ReleaseConfigGenerator({
    this.fileName = 'release_config.gen.dart',
    this.outputPaths = const ['lib/generated/'],
  });

  final String fileName;
  final List<String> outputPaths;

  void generate(
    Map<String, dynamic> releaseConfigMap,
    Map<String, dynamic> colorsMap,
    FileWriter writer, {
    required File sourceFile,
  }) {
    // Create directories if they don't exist
    for (final basePath in outputPaths) {
      final directory = Directory(normalize(basePath));
      if (!directory.existsSync()) {
        directory.createSync(recursive: true);
      }
    }

    final formatter = DartFormatter(pageWidth: 80, lineEnding: '\n');

    // Generate output paths
    final genPaths = outputPaths
        .map((basePath) => normalize(join(basePath, fileName)))
        .toList();

    final buffer = StringBuffer();
    buffer.writeln(header);

    buffer.writeln(
      mapReleaseConfig(
        releaseConfigMap,
        colorsMap['defaultUserTheme'],
        colorsMap['defaultUserMode'],
        sourceFile,
      ),
    );

    final formattedContent = formatter.format(buffer.toString());
    writer(formattedContent, genPaths);

    for (final path in genPaths) {
      stdout.writeln('Generated Release config: $path');
    }
  }
}
