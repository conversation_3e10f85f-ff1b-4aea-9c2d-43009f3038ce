targets:
  $default:
    builders:
      generators:
        enabled: true
      json_serializable:
        options:
          any_map: true
          checked: true
          create_factory: true
          create_to_json: false
          disallow_unrecognized_keys: false
          explicit_to_json: false
          field_rename: none
          generic_argument_factories: false
          ignore_unannotated: false
          include_if_null: true

builders:
  generators:
    target: ":generators"
    import: "package:mevolve_generator/mevolve_gen_runner.dart"
    builder_factories: ['build']
    build_extensions: { "$package$": [".gen.dart"] }
    auto_apply: dependents
    build_to: source