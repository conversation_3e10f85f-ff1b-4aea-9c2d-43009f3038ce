plugins {
    id "com.android.application"
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
    // START: FlutterFire Configuration
    id "com.google.gms.google-services"
    id "com.google.firebase.crashlytics"
    // END: FlutterFire Configuration
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def debugKeystoreProperties = new Properties()
def debugKeystorePropertiesFile = rootProject.file('key-debug.properties')
if (debugKeystorePropertiesFile.exists()) {
    debugKeystoreProperties.load(new FileInputStream(debugKeystorePropertiesFile))
}

android {
    namespace "app.mevolve.daily.mobile"
    compileSdk flutter.compileSdkVersion
    ndkVersion "25.2.9519653"

    compileOptions {
        coreLibraryDesugaringEnabled true
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "app.mevolve.daily.mobile"
        minSdkVersion 24
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
        resValue "string", "app_name", "Mevolve"
        manifestPlaceholders['foregroundServiceType'] = 'dataSync'
    }

    signingConfigs {
        debug {
            if (debugKeystoreProperties['keyAlias']) {
                keyAlias debugKeystoreProperties['keyAlias']
                keyPassword debugKeystoreProperties['keyPassword']
                storeFile debugKeystoreProperties['storeFile'] ? rootProject.file(debugKeystoreProperties['storeFile']) : null
                storePassword debugKeystoreProperties['storePassword']
            }
        }
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        profile {
            signingConfig signingConfigs.release['keyAlias'] != null ? signingConfigs.release : signingConfigs.debug
        }
        debug {
            signingConfig signingConfigs.debug
        }
    }

    buildFeatures {
        flavorDimensions = ["environment"]
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion = "1.5.14"
    }
    productFlavors {
        dev {
            dimension "environment"
            applicationIdSuffix ".dev"
            manifestPlaceholders.envName = "dev"
        }
        qa {
            dimension "environment"
            applicationIdSuffix ".qa"
            manifestPlaceholders.envName = "qa"
        }
        staging {
            dimension "environment"
            applicationIdSuffix ".staging"
            manifestPlaceholders.envName = "staging"
        }
        prod {
            dimension "environment"
            applicationIdSuffix ".prod"
            manifestPlaceholders.envName = "prod"
        }
        hotfix {
            dimension "environment"
            applicationIdSuffix ".hotfix"
            manifestPlaceholders.envName = "hotfix"
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation platform('com.google.firebase:firebase-bom:33.16.0')
    // implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.android.support:multidex:1.0.3'
    coreLibraryDesugaring 'com.android.tools:desugar_jdk_libs:1.1.5'
    implementation 'androidx.window:window:1.0.0'
    implementation 'androidx.window:window-java:1.0.0'
    implementation "androidx.work:work-runtime-ktx:2.8.1"
    implementation "androidx.glance:glance-appwidget:1.1.1"
}