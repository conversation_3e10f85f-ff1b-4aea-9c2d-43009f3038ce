rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
  	function isSupportUser() {
      return request.auth.token.email.matches('.*@realtime-innovations[.]com')
    }
   function hashEmail(email) {
      let hash = hashing.sha256(email).toHexString().lower();
      return hash;
   }
   function isMember(collectionName, documentId) {
       return firestore.exists(/databases/(default)/documents/$(collectionName)/$(documentId)) &&
       hashEmail(request.auth.token.email) in
              firestore.get(/databases/(default)/documents/$(collectionName)/$(documentId))
                 .data.members.memberHashedEmails;
      }
   function isPublic(collectionName, documentId) {
          return firestore.exists(/databases/(default)/documents/$(collectionName)/$(documentId)) &&
            firestore.get(/databases/(default)/documents/$(collectionName)/$(documentId))
                    .data.isPublic;
         }

    match /userData/attachments/{userId}/{collectionName}/{documentId}/{allPaths=**} {
        allow read: if request.auth.uid == userId ||
                    isMember(collectionName, documentId) ||
                    isPublic(collectionName, documentId);
        allow write: if request.auth.uid == userId ||
                     isMember(collectionName, documentId);
    }
    match /supportApp/media/{userId}/{allPaths=**} {
      allow read, write: if request.auth.uid == userId || isSupportUser();
    }
    match /supportApp/media/{userId}/{allPaths=**} {
      allow read, write: if request.auth.uid == userId || isSupportUser();
    }
    match /supportApp/media/{userId}/{allPaths=**} {
      allow read, write: if request.auth.uid == userId || isSupportUser();
    }
    match /userBackup/{dbVer}/{userId}/{allPaths=**} {
      allow read: if request.auth.uid == userId;
    }
    match /supportApp/userSegmentActivities/{allPaths=**} {
      allow read, write: if isSupportUser();
    }
  }
}