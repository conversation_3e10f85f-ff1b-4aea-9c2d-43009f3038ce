# This file configures the analyzer, which statically analyzes Dart code to
# check for errors, warnings, and lints.
#
# The issues identified by the analyzer are surfaced in the UI of Dart-enabled
# IDEs (https://dart.dev/tools#ides-and-editors). The analyzer can also be
# invoked from the command line by running `flutter analyze`.

# The following line activates a set of recommended lints for Flutter apps,
# packages, and plugins designed to encourage good coding practices.
include: package:flutter_lints/flutter.yaml

# We are excluding generated files as we need not analyze them.
analyzer:
  exclude:
    - '**/*.g.dart'
    - "**/*.g.part.dart"
    - "lib/generated/**"
    - "supportapp/**"



linter:
  # The lint rules applied to this project can be customized in the
  # section below to disable rules from the `package:flutter_lints/flutter.yaml`
  # included above or to enable additional rules. A list of all available lints
  # and their documentation is published at
  # https://dart-lang.github.io/linter/lints/index.html.
  #
  # Instead of disabling a lint rule for the entire project in the
  # section below, it can also be suppressed for a single line of code
  # or a specific dart file by using the `// ignore: name_of_lint` and
  # `// ignore_for_file: name_of_lint` syntax on the line or in the file
  # producing the lint.
  rules:
    # avoid_print: false  # Uncomment to disable the `avoid_print` rule
    # prefer_single_quotes: true  # Uncomment to enable the `prefer_single_quotes` rule

    # Style rules
    camel_case_types: true
    avoid_empty_else: true
    unnecessary_brace_in_string_interps: true
    prefer_single_quotes: true

    # Code related rules
    always_declare_return_types: true
    close_sinks: true
    prefer_adjacent_string_concatenation: true

    # Formatting
    always_use_package_imports: true
    sort_constructors_first: true
    prefer_relative_imports: false

    # Doc comments
    slash_for_doc_comments: true
    require_trailing_commas: true
    use_late_for_private_fields_and_variables: true









# Additional information about this file can be found at
# https://dart.dev/guides/language/analysis-options