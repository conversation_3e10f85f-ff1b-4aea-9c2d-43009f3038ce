#!/bin/bash

# Find project root by looking for pubspec.yaml
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# Walk up directories until we find pubspec.yaml
while [[ "$PROJECT_ROOT" != "/" && ! -f "$PROJECT_ROOT/pubspec.yaml" ]]; do
    PROJECT_ROOT="$(dirname "$PROJECT_ROOT")"
done

if [[ ! -f "$PROJECT_ROOT/pubspec.yaml" ]]; then
    echo "❌ Error: Could not find project root (pubspec.yaml not found)"
    exit 1
fi

echo "📁 Project root: $PROJECT_ROOT"

# Set paths relative to project root
LOGOS_DIR="$PROJECT_ROOT/generate_app_logos"
BASE_DIR="$LOGOS_DIR/base_svgs"
ENV_BOX_DIR="$LOGOS_DIR/env_box"
TEMP_DIR="/tmp/mevolve_icons_$$"

# Create directory structure for each environment
mkdir -p "$LOGOS_DIR"/{prod,dev,qa,staging,hotfix}
mkdir -p "$TEMP_DIR"

ENVS=("prod" "dev" "qa" "staging" "hotfix")

echo "Generating PNGs from base SVGs..."

# Step 1: Generate prod SVGs (base versions without env labels)
echo "Step 1: Generating prod base files..."
magick -background none "$BASE_DIR/main.svg" -resize 1024x1024 "$LOGOS_DIR/prod/main.png"
magick -background none "$BASE_DIR/android_adaptive_foreground.svg" -resize 432x432 "$LOGOS_DIR/prod/adaptive_foreground.png"
magick -background none "$BASE_DIR/android_adaptive_monochrome.svg" -resize 432x432 "$LOGOS_DIR/prod/adaptive_monochrome.png"
magick -background none "$BASE_DIR/ios_dark.svg" -resize 1024x1024 "$LOGOS_DIR/prod/dark.png"
magick -background none "$BASE_DIR/ios_tinted.svg" -resize 1024x1024 "$LOGOS_DIR/prod/tinted.png"
echo "✓ Prod base files completed"

# Step 2: For other environments, create combined SVGs and generate PNGs
for env in "${ENVS[@]}"; do
    if [ "$env" != "prod" ]; then
        echo "Step 2: Processing $env..."
        
        ENV_DIR="$LOGOS_DIR/$env"
        
        # Create combined SVGs by superimposing env_box on base SVGs
        # Main icon
        cat > "$TEMP_DIR/${env}_main.svg" << EOF
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
$(sed '1d' "$BASE_DIR/main.svg" | sed '$d')
$(sed '1d' "$ENV_BOX_DIR/$env.svg" | sed '$d')
</svg>
EOF
        
        # Android adaptive foreground (with env label)
        cat > "$TEMP_DIR/${env}_adaptive_foreground.svg" << EOF
<svg width="432" height="432" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
$(sed '1d' "$BASE_DIR/android_adaptive_foreground.svg" | sed '$d')
$(sed '1d' "$ENV_BOX_DIR/$env.svg" | sed '$d')
</svg>
EOF
        
        # Android adaptive monochrome (with env label in white)
        cat > "$TEMP_DIR/${env}_adaptive_monochrome.svg" << EOF
<svg width="432" height="432" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
$(sed '1d' "$BASE_DIR/android_adaptive_monochrome.svg" | sed '$d')
$(sed '1d' "$ENV_BOX_DIR/$env.svg" | sed '$d' | sed 's/#FFFFFD/#FFFFFF/g')
</svg>
EOF
        
        # iOS dark (with env label)
        cat > "$TEMP_DIR/${env}_dark.svg" << EOF
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
$(sed '1d' "$BASE_DIR/ios_dark.svg" | sed '$d')
$(sed '1d' "$ENV_BOX_DIR/$env.svg" | sed '$d')
</svg>
EOF
        
        # iOS tinted (with env label in grayscale)
        cat > "$TEMP_DIR/${env}_tinted.svg" << EOF
<svg width="512" height="512" viewBox="0 0 512 512" fill="none" xmlns="http://www.w3.org/2000/svg">
$(sed '1d' "$BASE_DIR/ios_tinted.svg" | sed '$d')
$(sed '1d' "$ENV_BOX_DIR/$env.svg" | sed '$d' | sed 's/#FFFFFD/#424242/g')
</svg>
EOF
        
        # Generate PNGs from combined SVGs
        magick -background none "$TEMP_DIR/${env}_main.svg" -resize 1024x1024 "$ENV_DIR/main.png"
        magick -background none "$TEMP_DIR/${env}_adaptive_foreground.svg" -resize 432x432 "$ENV_DIR/adaptive_foreground.png"
        magick -background none "$TEMP_DIR/${env}_adaptive_monochrome.svg" -resize 432x432 "$ENV_DIR/adaptive_monochrome.png"
        magick -background none "$TEMP_DIR/${env}_dark.svg" -resize 1024x1024 "$ENV_DIR/dark.png"
        magick -background none "$TEMP_DIR/${env}_tinted.svg" -resize 1024x1024 "$ENV_DIR/tinted.png"
        
        echo "✓ Completed $env"
    fi
done

# Clean up temporary files
rm -rf "$TEMP_DIR"

echo "All PNG generation complete!"