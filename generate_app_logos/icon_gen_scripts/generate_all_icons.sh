#!/bin/bash

# Generate flutter_launcher_icons for all environments without maintaining YAML files
# This script creates temporary configs in memory and executes them
# Automatically finds project root for flexibility

# Find project root by looking for pubspec.yaml
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$SCRIPT_DIR"

# Walk up directories until we find pubspec.yaml
while [[ "$PROJECT_ROOT" != "/" && ! -f "$PROJECT_ROOT/pubspec.yaml" ]]; do
    PROJECT_ROOT="$(dirname "$PROJECT_ROOT")"
done

if [[ ! -f "$PROJECT_ROOT/pubspec.yaml" ]]; then
    echo "❌ Error: Could not find project root (pubspec.yaml not found)"
    exit 1
fi

echo "📁 Project root: $PROJECT_ROOT"
cd "$PROJECT_ROOT"

ENVS=("prod" "dev" "qa" "staging" "hotfix")

echo "🚀 Generating app icons for all environments..."
echo "================================================"

for env in "${ENVS[@]}"; do
    echo "📱 Processing $env environment..."
    
    # Create flavor-specific config file that flutter_launcher_icons expects
    FLAVOR_CONFIG="flutter_launcher_icons-${env}.yaml"
    
    cat > "$FLAVOR_CONFIG" << EOF
flutter_launcher_icons:
  image_path: "generate_app_logos/${env}/main.png"
  android: true
  ios: true
  image_path_ios: "generate_app_logos/${env}/main.png"
  image_path_ios_dark_transparent: "generate_app_logos/${env}/dark.png"
  image_path_ios_tinted_grayscale: "generate_app_logos/${env}/tinted.png"
  remove_alpha_ios: false
  desaturate_tinted_to_grayscale_ios: false
  adaptive_icon_background: "#0D9B78"
  adaptive_icon_foreground: "generate_app_logos/${env}/adaptive_foreground.png"
  adaptive_icon_monochrome: "generate_app_logos/${env}/adaptive_monochrome.png"
  adaptive_icon_foreground_inset: 16
  windows:
    generate: false
    image_path: "generate_app_logos/${env}/main.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "generate_app_logos/${env}/main.png"
EOF
    
    # Execute flutter_launcher_icons with flavor-specific config
    echo "   Generating icons..."
    dart run flutter_launcher_icons -f "$FLAVOR_CONFIG"
    
    # Clean up flavor config file
    rm -f "$FLAVOR_CONFIG"
    
    echo "   ✅ Completed $env"
    echo ""
done

echo "🎉 All app icons generated successfully!"
echo ""
echo "Generated icons for environments: ${ENVS[*]}"
echo "Icons are now ready for build and deployment."