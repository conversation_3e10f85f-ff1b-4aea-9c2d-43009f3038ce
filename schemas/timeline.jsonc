{"userId": "uuid", "unscheduledTodos": {}, "dates": {"date": "Date", "pendingTodos": [], "habitItems": {"habitId": {"version": 1, "id": "uuid", "reminder": {}, "createdAt": "DateTime", "createdBy": "string", "title": "string", "frequency": {"sunday": true, "monday": true, "tuesday": true, "wednesday": true, "thursday": true, "friday": true, "saturday": true}, "due-date": "date", "orderNo": "1000", "status": "active", "notes": "Some value", "ansnwerChoice": "", "answerUnit": "", "answerValue": ""}, "habitId2": {"notes": "Some value", "ansnwerChoice": "", "answerUnit": "", "answerValue": ""}}, "todoItems": {}, "journalItems": {}, "computedValues": "List"}}