# Miscellaneous
*.class
*.lock
*.log
*.pyc
*.swp
.DS_Store
.atom/
.buildlog/
.history
.svn/
debug_setup_script/backup/
debug_setup_script/cloudflare_test_keys.txt
debug_setup_script/appcheck_debug_tokens_prod.json
debug_setup_script/appcheck_debug_tokens_staging.json
debug_setup_script/firebase_local.json
api-keys.prod.json

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/*

# Visual Studio Code related
.classpath
.project
.settings/
.vscode/*

# Flutter repo-specific
/bin/cache/
/bin/mingit/
/bin/internal/bootstrap.bat
/bin/internal/bootstrap.sh
/dev/benchmarks/mega_gallery/
/dev/bots/.recipe_deps
/dev/bots/android_tools/
/dev/docs/doc/
/dev/docs/flutter.docs.zip
/dev/docs/lib/
/dev/docs/pubspec.yaml
/dev/integration_tests/**/xcuserdata
/dev/integration_tests/**/Pods
/dev/devicelab/ABresults*.json
/packages/flutter/coverage/
version
analysis_benchmark.json

# packages file containing multi-root paths
.packages.generated

# Flutter/Dart/Pub related
**/doc/api/
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
build/
flutter_*.png
linked_*.ds
unlinked.ds
unlinked_spec.ds
.fvm/
**/generated_plugin_registrant.dart

# Android related
**/android/.gradle
**/android/captures/
**/android/service_account_key.json
**/android/android_keys.zip
**/android/gradlew.bat
**/android/local.properties
**/android/**/GeneratedPluginRegistrant.java
**/android/key.properties
# Allow shared debug keystore and config (safe to commit)
!shared-debug.keystore
!**/android/key-debug.properties
**/android/.idea/
*.jks
**/android/**/gradle-wrapper.jar
.gradle/
**/android/gradlew

# iOS/XCode related
**/ios/**/*.mode1v3
**/ios/**/*.mode2v3
**/ios/**/*.moved-aside
**/ios/**/*.pbxuser
**/ios/**/*.perspectivev3
**/ios/**/*sync/
**/ios/**/.sconsign.dblite
**/ios/**/.tags*
**/ios/**/.vagrant/
ios/Build/
**/ios/**/DerivedData/
**/ios/**/Icon?
**/ios/**/Pods/
**/ios/**/.symlinks/
**/ios/**/profile
**/ios/**/xcuserdata
**/ios/.generated/
**/ios/Flutter/App.framework
**/ios/Flutter/Flutter.framework
**/ios/Flutter/Flutter.podspec
**/ios/Flutter/Generated.xcconfig
**/ios/Flutter/app.flx
**/ios/Flutter/app.zip
**/ios/Flutter/.last_build_id
**/ios/Flutter/flutter_assets/
**/ios/Flutter/flutter_export_environment.sh
**/ios/ServiceDefinitions.json
**/ios/Runner/GeneratedPluginRegistrant.*
**/ios/Flutter/ephemeral

# macOS
**/Flutter/ephemeral/
**/Pods/
**/macos/Flutter/GeneratedPluginRegistrant.swift
**/macos/Flutter/ephemeral
**/xcuserdata/
macos/Build/
**/macos/**/DerivedData/

# Windows
**/windows/flutter/generated_plugin_registrant.cc
**/windows/flutter/generated_plugin_registrant.h
**/windows/flutter/generated_plugins.cmake

# Linux
**/linux/flutter/generated_plugin_registrant.cc
**/linux/flutter/generated_plugin_registrant.h
**/linux/flutter/generated_plugins.cmake

# Coverage
coverage/

# Submodules
!pubspec.lock
packages/**/pubspec.lock

# Web related
.firebase/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Exceptions to the above rules.
!**/ios/**/default.mode1v3
!**/ios/**/default.mode2v3
!**/ios/**/default.pbxuser
!**/ios/**/default.perspectivev3
!/packages/flutter_tools/test/data/dart_dependencies_test/**/.packages
!/dev/ci/**/Gemfile.lock
!.vscode/extensions.json
!.vscode/launch.json
!.idea/codeStyles/
!.idea/dictionaries/
!.idea/runConfigurations/
/.idea/codeStyles/Project.xml
devtools_options.yaml

# Personal development files
personal-workspace/
claude.md
emulatorTesting/
