@import url(./icon-fonts.scss);
@import 'ngx-toastr/toastr';

* {
    margin: 0;
    padding: 0;
}

:root {
    --primary-color: #0D9B78;
    --primary-light: #02D09E;
    --secondary-color: #323238;
    --tertiary-color: #A6DEC4;
    --color1: #FFFFFF;
    --color2: #FFFFFD;
    --color3: #F2FFF9;
    --color4: #949C9F;
    --color35: #02D09E;
    --background1: #000000;
    --background2: #121212;
    --background3: #1A1C23;
    --background4: #4E5464;
    --background5: #000000;
    --border-color: #121212;
    --ri-font-primary: 'Roboto';
    --loader-size: 60px;

    --fs-10px: calc(10px * 0.8)/calc(12px * 0.8) var(--ri-font-primary), serif;
    --fs-12px: calc(12px * 0.8)/calc(14px * 0.8) var(--ri-font-primary), serif;
    --fs-14px: calc(14px * 0.8)/calc(21px * 0.8) var(--ri-font-primary), serif;
    --fs-16px: calc(16px * 0.8)/calc(24px * 0.8) var(--ri-font-primary), serif;
    --fs-18px: calc(18px * 0.8)/calc(27px * 0.8) var(--ri-font-primary), serif;
    --fs-20px: calc(20px * 0.8)/calc(30px * 0.8) var(--ri-font-primary), serif;
    --fs-22px: calc(22px * 0.8)/calc(30.8px * 0.8) var(--ri-font-primary), serif;
    --fs-24px: calc(24px * 0.8)/calc(36px * 0.8) var(--ri-font-primary), serif;
    --fs-32px: calc(32px * 0.8)/calc(48px * 0.8) var(--ri-font-primary), serif;
    --fs-36px: calc(36px * 0.8)/calc(50.4px * 0.8) var(--ri-font-primary), serif;
    --fs-40px: calc(40px * 0.8)/calc(56px * 0.8) var(--ri-font-primary), serif;
    --fs-48px: calc(48px * 0.8)/calc(72px * 0.8) var(--ri-font-primary), serif;
    --fs-58px: calc(58px * 0.8)/calc(81.2px * 0.8) var(--ri-font-primary), serif;
}

html {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
    font-family: "Roboto", sans-serif;
    width: 100%;
    height: 100%;
}

// Mode changes

body.me-dark-mode {
    background-color: var(--background3) !important;
}

.me-light-mode {
    .emoji-block .emoji .emoji-bg {
        transition: 0.3s ease-in-out;
        fill: #E5E5E5;
    }
}

// Mode changes end

body.me-light-mode {
    --color1: #323238;
    --color2: #FFFFFE;
    --color4: #949C9E;
    --color35: #0D9B74;
    --primary-light: #02D09E;
    --background1: #FFFFFF;
    --background2: #F2F2F2;
    --background3: #FFFFFD;
    --background4: #0D9B77;
    --border-color: #F2F2F2;
}

.me-dark-mode .user-badge {
    background-color: var(--primary-color);
    border: 1px solid var(--primary-color);

    .user-initial {
        color: var(--color2);
    }
}

.me-light-mode .user-badge {
    background-color: var(--background1);
    border: 1px solid var(--background1);

    .user-initial {
        color: var(--primary-color);
    }
}

// Container

.container-medium {
    width: 100%;
    max-width: 750px;
    margin-left: auto;
    margin-right: auto;
}

// Colors

.color-primary {
    color: var(--primary-color);
}

.color-primary-light {
    color: var(--primary-light);
}

.color-secondary {
    color: var(--secondary-color);
}

.color-1 {
    color: var(--color1);
}

.color-2 {
    color: var(--color2);
}

.color-3 {
    color: var(--color3);
}

.color-4 {
    color: var(--color4);
}

.bg-1 {
    background-color: var(--background1);
}

.bg-2 {
    background-color: var(--background2);
}

.bg-3 {
    background-color: var(--background3);
}

.bg-4 {
    background-color: var(--background4);
}

.bg-5 {
    background-color: var(--background5);
}

// Text

.text-12-400 {
    font-size: 12px;
    font-weight: 400;
    line-height: 14px;
}

.text-14-400 {
    font-size: 14px;
    font-weight: 400;
    line-height: 14px;
}

.text-14-500 {
    font-size: 14px;
    font-weight: 500;
    line-height: 21px;
}

.text-16-400 {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
}

.text-16-500 {
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
}

.text-18-400 {
    font-size: 18px;
    font-weight: 400;
    line-height: 27px;
}

.text-18-500 {
    font-size: 18px;
    font-weight: 500;
    line-height: 27px;
}

.text-20-500 {
    font-size: 20px;
    font-weight: 500;
    line-height: 30px;
}

.text-20-600 {
    font-size: 20px;
    font-weight: 600;
    line-height: 30px;
}

.text-24-500 {
    font-size: 24px;
    font-weight: 500;
    line-height: 36px;
}

// Padding 

.ri-pb-1 {
    padding-bottom: 4px;
}

.ri-pe-1 {
    padding-right: 4px;
}

.ri-pt-2 {
    padding-top: 8px;
}

.ri-pb-2 {
    padding-bottom: 8px;
}

.ri-pt-3 {
    padding-top: 12px;
}

.ri-pb-3 {
    padding-bottom: 12px;
}

.ri-pe-3 {
    padding-right: 12px;
}

.ri-ps-3 {
    padding-left: 12px;
}

.ri-py-3 {
    padding-top: 12px;
    padding-bottom: 12px;
}

.ri-pt-4 {
    padding-top: 16px;
}

.ri-pb-4 {
    padding-bottom: 16px;
}

.ri-ps-4 {
    padding-left: 16px;
}

.ri-pe-4 {
    padding-right: 16px;
}

.ri-px-4 {
    padding-left: 16px;
    padding-right: 16px;
}

.ri-py-4 {
    padding-top: 16px;
    padding-bottom: 16px;
}

.ri-p-4 {
    padding: 16px;
}

.ri-pt-5 {
    padding-top: 20px;
}

.ri-pt-6 {
    padding-top: 24px;
}

.ri-pb-6 {
    padding-bottom: 24px;
}

.ri-ps-6 {
    padding-left: 24px;
}

.ri-pe-6 {
    padding-right: 24px;
}

.ri-py-6 {
    padding-top: 24px;
    padding-bottom: 24px;
}

.ri-pb-7 {
    padding-bottom: 32px;
}

// Margins

.ri-me-3 {
    margin-right: 12px;
}

.ri-mt-6 {
    margin-top: 24px;
}

.ri-mb-6 {
    margin-bottom: 24px;
}

.ri-ms-6 {
    margin-left: 24px;
}

.ri-me-6 {
    margin-right: 24px;
}

// Borders

.ri-bt-1-3 {
    border-top: 1px solid var(--background2);
}

.ri-bb-1 {
    border-bottom: 1px solid var(--background2);
}

.ri-bb-2 {
    border-bottom: 2px solid var(--background2);
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

// Others

.cursor-pointer {
    cursor: pointer;
}

.cursor-move {
    cursor: move;
}

.ri-td-none {
    text-decoration: none;
}

// User Badge

.user-badge {
    width: 22px;
    min-width: 22px;
    height: 22px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50px;

    .user-initial {
        font-size: 14px;
        line-height: 16.41px;
        color: var(--color2);
        font-weight: 500;
    }
}

// Emoji

.emoji-block .emoji .emoji-bg {
    transition: 0.3s ease-in-out;
}

.emoji-block .emoji.active-emoji .emoji-bg {
    fill: var(--tertiary-color);
}

// Quill editor 

.note-block {
    .ql-container {
        border: 0 !important;
    }

    .ql-editor {
        font-size: 16px;
        line-height: 24px;
        padding: 0;

        p {
            line-height: 24px;
        }

        strong {
            font-weight: 600;
        }

        a {
            color: var(--primary-color);
        }
    }

    .ql-editor.ql-blank {
        display: none;
    }
}

// Bottom sheet

.bs-header {
    padding: 15px 16px 14px 16px;
}

.mat-bottom-sheet-container {
    border-top-left-radius: 16px !important;
    border-top-right-radius: 16px !important;
    max-width: 750px !important;
    min-width: 750px !important;
    padding: 0px !important;
}

@media(max-width: 750px) {
    .mat-bottom-sheet-container {
        min-width: 100vw !important;
    }
}

// Scroll bar codes

/* Custom Scrollbar for Webkit-based browsers (Chrome, Safari, Edge) */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

/* Scrollbar track */
::-webkit-scrollbar-track {
    background: transparent;
}

/* Scrollbar thumb */
::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 10px;
    transition: background-color 0.3s ease-in-out;
}

/* Scrollbar thumb on hover */
::-webkit-scrollbar-thumb:hover {
    background-color: rgba(0, 0, 0, 0.3);
}

/* Scrollbar thumb when active (clicked) */
::-webkit-scrollbar-thumb:active {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Container hover state to show scrollbar */
:hover::-webkit-scrollbar-thumb {
    background-color: rgba(0, 0, 0, 0.2);
}


// Play

.vg-icon-play_arrow:before {
    content: '\e900' !important;
    font-family: 'MyIcons' !important;
    background-color: #5F636C;
    padding: 20px;
    font-size: 24px;
    border-radius: 100px;
    width: 75px;
    height: 75px;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;
    transform: none !important;
}


.vg-overlay-play,
.vg-buffering {
    top: 0;
    left: 0;
}

// Menus

.info-menu {
    border: 2px solid var(--background2);
    border-radius: 8px;
    background-color: var(--background3) !important;

    .mat-mdc-menu-content {
        padding-top: 0;
        padding-bottom: 0;
        background-color: var(--background3);

        .mat-mdc-menu-item {
            padding: 15px 12px;
            min-width: 150px;
            background-color: var(--background3);
            color: var(--color1);
            font-size: 14px;
            font-weight: 400;
            line-height: 20px;

            .img {
                width: 24px;
            }
        }

        .mat-mdc-menu-item:not(:last-child) {
            border-bottom: 1px solid var(--background2);
        }
    }
}

// Loader 

.loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: #7c6e6e2e;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

// .loader {
//     border: 8px solid #F3F3F3;
//     border-top: 8px solid var(--primary-color);
//     border-radius: 50%;
//     width: 60px;
//     height: 60px;
//     animation: spin 2s linear infinite;
// }

.loader {
    width: calc(var(--loader-size)/3);
    height: calc(var(--loader-size)/3);
    border-radius: 50%;
    display: inline-block;
    position: relative;
    background: #FFFFFF;
    box-shadow: calc(-1 * var(--loader-size) / 2) 0 #FFFFFF, calc(var(--loader-size) / 2) 0 #FFFFFF;
    animation: shadowPulse 2s linear infinite;
}

@keyframes shadowPulse {
    33% {
        background: #FFFFFF;
        box-shadow: calc(-1 * var(--loader-size) / 2) 0 var(--primary-color), calc(var(--loader-size) / 2) 0 #FFFFFF;
    }

    66% {
        background: var(--primary-color);
        box-shadow: calc(-1 * var(--loader-size) / 2) 0 #FFFFFF, calc(var(--loader-size) / 2) 0 #FFFFFF;
    }

    100% {
        background: #FFFFFF;
        box-shadow: calc(-1 * var(--loader-size) / 2) 0#FFFFFF, calc(var(--loader-size) / 2) 0 var(--primary-color);
    }

}

app-svg {
    display: inline-block;
}

.mat-mdc-dialog-surface {
    background-color: transparent !important;
}

// Toastr

.custom-toastr {
  background-color: #4E5464 !important;
  color: #FFFFFF !important;
  border-radius: 6px !important;
  padding: 16px 24px !important;
  min-width: 280px;
  max-width: 90%;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3) !important;
  font-size: 14px;
  background-image: none !important;
}

.custom-toastr .toast-dismiss {
  color: #FFFFFF;
  margin-left: 16px;
  cursor: pointer;
}

.mat-mdc-dialog-inner-container {
    padding-left: 16px;
    padding-right: 16px;
}

.action-icon {
    width: 24px;
    margin-left: 12px;
}

// Text

p,h6,h5,h4 {
    word-break: break-word;
}

#qrCodeCanvas svg,
#qrCodeCanvas canvas {
    width: 150px !important;
    height: 150px !important;
}