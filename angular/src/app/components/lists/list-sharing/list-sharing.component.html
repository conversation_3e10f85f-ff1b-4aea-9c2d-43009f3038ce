<div class="container-medium main-block" [ngClass]="cc.mode === 'dark' ? 'me-dark-mode' : 'me-light-mode'">
    <div class="top-section" *ngIf="!isErrors">
        <div class="header-section bg-4 ri-px-4 ri-pt-4">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="text-18-500 color-2 mb-0">{{ listData ? listData.title : '-' }}</h5>
                <div class="d-flex align-items-center">
                    <div class="user-badge ri-me-6 ri-ms-6" *ngIf="cc.getUserDisplayLetter()" role="button" (click)="cc.viewPublicUser()"><span class="user-initial">{{ cc.getUserDisplayLetter() }}</span></div>
                    <svg *ngIf="listData.items.length > 0" class="ri-me-6" width="24" style="min-width: 24px;" type="button" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg" (click)="toggleFilter(isFilter)">
                        <path d="M17.7975 6.84498V6.9784L12.6439 12.7046V17.5897L11.4986 17.0171V12.7046L6.34498 6.9784V6.84498H17.7975ZM18.9427 5.69973H5.19973V7.4176L10.3534 13.1439V17.7249L13.7891 19.4427V13.1439L18.9427 7.4176V5.69973Z" fill="#FFFFFD"/>
                        <path [attr.fill-rule]="isFilter ? null : 'evenodd'" clip-rule="evenodd" d="M12.6439 12.7046L17.7975 6.9784V6.84498H6.34498V6.9784L11.4986 12.7046V17.0171L12.6439 17.5897V12.7046ZM12.5457 17.4309V12.667L17.6971 6.94314H6.44531L11.5968 12.667V16.9564L12.5457 17.4309ZM19.0409 5.60156V7.45527L13.8873 13.1815V19.6016L10.2552 17.7855V13.1815L5.10156 7.45527V5.60156H19.0409ZM10.3534 13.1439V17.7249L13.7891 19.4427V13.1439L18.9427 7.4176V5.69973H5.19973V7.4176L10.3534 13.1439Z" fill="#FFFFFD"/>
                    </svg>
                    <app-svg class="" name="more" role="button" style="min-width: 24px;" [matMenuTriggerFor]="infoMenu"></app-svg>
    
                    <mat-menu #infoMenu="matMenu" class="info-menu">
                        <!-- <button mat-menu-item class="text-14-400 color-1"><img class="img img-fluid ri-me-3" src="assets/icons/eye.svg">View Profile</button> -->
                        <button mat-menu-item class="text-14-400 color-1" (click)="cc.copyLink()"><app-svg class="ri-me-3" name="copy" [color]="cc.color35"></app-svg>{{cc.text('copyLink')}}</button>
                        <button mat-menu-item class="text-14-400 color-1" (click)="viewQr()"><app-svg class="ri-me-3" name="qrCode" [color]="cc.color35"></app-svg>{{cc.text('scanQr')}}</button>
                        <button mat-menu-item class="text-14-400 color-1" (click)="cc.openLanguageDialog()"><app-svg class="ri-me-3" name="language" role="button" [color]="cc.color35"></app-svg>{{cc.text('language')}}</button>
                    </mat-menu>
    
                </div>
            </div>
        </div>
        <div class="body-section bg-3">
            <div class="filter-block ri-px-4 d-flex ri-bg-dark-1 ri-bb-2" [ngClass]="{'h-auto filtered': isFilter}">
                <div class="btn btn-outline input-btn d-flex align-items-center me-2 search-input-block" [ngClass]="{'active': listSearch.value }">
                    <app-svg class="me-2" name="search" [color]="listSearch.value ? '#FFFFFD' :'#949C9F'" style="width: 14px; min-width: 14px;"></app-svg>
                    <input class="search-input w-100 p-0" type="text" [attr.placeholder]="cc.text('search')" name="riListSearch" [formControl]="listSearch" autocomplete="off">
                    <div class="search-close ms-2" *ngIf="listSearch.value" role="button" (click)="listSearch.setValue('')">
                        <app-svg class="d-flex align-items-center justify-content-center" name="closeSm" color="#0D9B78" style="width: 10px; height: 10px; margin-top: -2px;"></app-svg>
                    </div>
                </div>
                <button class="btn btn-outline btn-toggle me-2" (click)="filterType = (filterType === 'UNCHECKED' ? '' : 'UNCHECKED');onFilterChange()" [ngClass]="{'active': filterType === 'UNCHECKED'}">{{cc.text('unchecked')}}</button>
                <button class="btn btn-outline btn-toggle me-2" (click)="filterType = (filterType === 'CHECKED' ? '' : 'CHECKED');onFilterChange()" [ngClass]="{'active': filterType === 'CHECKED'}">{{cc.text('checked')}}</button>
            </div>
            <div class="list-block ri-px-4 ri-py-4" *ngFor="let item of listItems; let i = index;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <p class="text-16-400 color-1 mb-0">{{ item.item }}</p>
                        <p class="text-12-400 color-4 mb-0 ri-pt-2" *ngIf="item.description">{{ item.description }}</p>
                    </div>
                    <div class="custom-checkbox ms-3">
                        <input type="checkbox" [id]="'listCheck'+i" [checked]="item.done" readonly disabled>
                        <label [for]="'listCheck'+i"></label>
                    </div>
                </div>
            </div>
            <div class="no-result-block d-flex align-items-center justify-content-center flex-column" [ngClass]="{'filter-applied': isFilter}" *ngIf="(listItems.length === 0 || listData.items.length === 0) && !cc.isLoading">
                <img *ngIf="listData.items.length === 0" class="img img-fluid" src="assets/icons/no-list-item.svg">
                <img *ngIf="listData.items.length !== 0 && listItems.length === 0" class="img img-fluid" src="assets/icons/no-result.svg">
                <p class="text-14-400 color-1 ri-pt-4 mb-0">{{cc.text( listData.items.length === 0 ? 'noListItems' : 'noData')}}</p>
            </div>
        </div>
    </div>
    <div class="footer-section ri-px-4 ri-py-4 bg-3" *ngIf="!isErrors">
        <div class="text-end">
            <a class="d-block text-16-500 color-primary-light ri-td-none" 
            href="#" 
            (click)="saveEvent($event)">
            {{cc.text('saveList')}}
         </a>
        </div>
    </div>
    <div class="h-100 d-flex align-items-center justify-content-center flex-column" *ngIf="isErrors">
        <img class="img img-fluid" src="assets/icons/no-result.svg">
        <p class="text-center text-16-500 color-1 mb-0 pt-4">{{cc.text('listUnavailable')}}</p>
        <p class="text-center text-14-400 color-4 mb-0 pt-3">{{cc.text('listUnavailableContent')}}</p>
    </div>
</div>