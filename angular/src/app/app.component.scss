.h-100vh {
    height: 100vh; /* Fallback */
    height: 100dvh;
}

.me2-toastr-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #000000B2;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9998;

    .toastr-block {
        position: fixed;
        top: 24px;
        right: 24px;
        background: linear-gradient(110deg, #0D9B78 8%, #02D09E 18%, #0D9B78 33%);
        background-size: 200% 100%;
        animation: shimmer 2s infinite;
        border-radius: 8px;
        z-index: 9999;
        color: white;
        padding: 10px 24px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }

    p {
        color: #FFFFFF;
        font: var(--fs-20px);
        font-weight: 500;
        padding: 10px 20px;
    }
}

@keyframes shimmer {
    0% {
        background-position: -200% 0;
    }

    100% {
        background-position: 200% 0;
    }
}