import { Inject, Injectable, PLATFORM_ID, signal, WritableSignal } from '@angular/core';
import { CloudFlareService } from './cloudflare.service';
import { IUSER } from '../_interfaces/user.interface';
import { BehaviorSubject, Observable, Subject, takeUntil } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { Config } from '../_interfaces/config.interface';
import { Clipboard } from '@angular/cdk/clipboard';
import { Router } from '@angular/router';
import { isPlatformBrowser } from '@angular/common';
import { StorageService } from './storage.servive';
import { FormControl } from '@angular/forms';
import { countryToLanguageMap, language, languages } from '../_datas/languages.data';
import { translationData } from '../_datas/translation.data';
import { LanguageDialogComponent } from '../shared/language-dialog/language-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { AnalyticsService } from './analytics.service';
import { getNewId } from '../_utils/utils';
import { SubscriptionDialogComponent } from '../shared/subscription-dialog/subscription-dialog.component';
import { ToastrService } from 'ngx-toastr';
import { formatDate } from '@angular/common';
import { environment } from '../../environments/environment';
import { Meta } from '@angular/platform-browser';
import { AlertService } from './alert.service';
@Injectable({
  providedIn: 'root',
})

export class CacheService {

  public configSubject = new BehaviorSubject<Config | null>(null);
  public config$: Observable<Config | null> = this.configSubject.asObservable();
  user!: IUSER;
  mevolveUserId: string = '';
  unSubscribe = new Subject<void>();
  public config: Config | any = {};
  languageCodes: WritableSignal<string[]> = signal([]);
  isLoading: boolean = false;
  language: FormControl = new FormControl('en');
  languageList: { code: string, name: string }[] = languages;
  translatedTexts: { [name: string]: { [langCode: string]: string } } = translationData;
  mode: 'light' | 'dark' = this.isDarkMode() ? 'dark' : 'light';
  color1: string = '#0D9B78';

  countryToLanguageMap: { [key: string]: string } = countryToLanguageMap;
  isOpeningAppstore: boolean = false;

  public cacheSubject = new BehaviorSubject<boolean>(false);
  public cache$: Observable<boolean> = this.cacheSubject.asObservable();

  private userAgent: string;

  constructor(
    private cfs: CloudFlareService,
    private http: HttpClient,
    private clipboard: Clipboard,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: any,
    private ss: StorageService,
    private dialog: MatDialog,
    private as: AnalyticsService,
    private toastr: ToastrService,
    private meta: Meta,
    private alertService: AlertService
  ) {
    if (this.isBrowser()) {
      this.userAgent = window.navigator.userAgent.toLowerCase();
    } else {
      this.userAgent = '';
    }
  }

  get color35(): string {
    return this.mode === 'dark' ? '#02D09E' : '#0D9B74';
  }

  get color7(): string {
    return this.mode === 'dark' ? '#949C9F' : '#949C9E';
  }

  async init() {
    if (this.isBrowser()) {

      // Country code and language set
      const countryCode = this.ss.getCountryCode();
      if (!countryCode) {
        try {
          const countryCode = await this.getCountryCode();
          this.ss.setCountryCode(countryCode);
          this.as.commonProperties.country = countryCode;
          this.setLanguage(countryCode);
        } catch (error) {
          this.setLanguage('NA');
        }
      } else {
        this.as.commonProperties.country = countryCode;
        this.setLanguage(countryCode);
      }

      // User Id set
      const uid = this.ss.getUid();
      if (!uid) {
        const newUid = getNewId();
        this.as.commonProperties.muid = newUid;
        this.ss.setUid(newUid);
      } else {
        this.as.commonProperties.muid = uid;
      }

      // Device Id set
      const deviceId = this.ss.getDeviceId();
      if (!deviceId) {
        const newDeviceId = getNewId();
        this.as.commonProperties.device_id = newDeviceId;
        this.ss.setDeviceId(newDeviceId);
      } else {
        this.as.commonProperties.device_id = deviceId;
      }

      // Session Id set
      const sessionId = this.ss.getSessionId();
      if (!sessionId) {
        const newSessionId = getNewId();
        this.as.commonProperties.session_id = newSessionId;
        this.ss.setSessionId(newSessionId);
      } else {
        this.as.commonProperties.session_id = sessionId;
      }

      // Device Type set
      const deviceType = this.ss.getDeviceType();
      if (!deviceType) {
        const deviceType = this.getDeviceType();
        this.as.commonProperties.device_type = deviceType;
        this.ss.setDeviceType(deviceType);
      } else {
        this.as.commonProperties.device_type = deviceType;
      }

      // Device OS set
      const deviceOs = this.ss.getDeviceOs();
      if (!deviceOs) {
        const deviceOs = this.getOS();
        this.as.commonProperties.device_os = deviceOs;
        this.ss.setDeviceOs(deviceOs);
      } else {
        this.as.commonProperties.device_os = deviceOs;
      }

      // Browser set
      const browser = this.ss.getBrowser();
      if (!browser) {
        const browser = this.getBrowser();
        this.as.commonProperties.browser = browser;
        this.ss.setBrowser(browser);
      } else {
        this.as.commonProperties.browser = browser;
      }

      this.cacheSubject.next(true);
    }
  }

  setLanguage(countryCode: string) {
    const localSelectedLanguage = this.ss.getLanguageCode();
    if (!localSelectedLanguage) {
      const languageCode = this.countryToLanguageMap[countryCode];
      if (this.isLanguageSupported(languageCode || 'en')) {
        this.ss.setLanguageCode(languageCode || 'en');
        this.as.commonProperties.language = language[languageCode] || language['en'];
        this.language.setValue(languageCode || 'en');
      }
    } else {
      this.language.setValue(localSelectedLanguage || 'en');
      this.as.commonProperties.language = language[localSelectedLanguage];
    }
  }

  getCountryCode(): Promise<string> {
    const GEOLOCATION_API = 'https://ipapi.co/json/'; // 'http://ip-api.com/json/'

    return new Promise((resolve, reject) => {
      fetch(GEOLOCATION_API)
        .then(response => response.json())
        .then(data => {
          const countryCode = data.country_code?.toLowerCase();
          if (countryCode) {
            resolve(countryCode);
          } else {
            reject('Country code not found'); // Reject if the country code is missing
          }
        })
        .catch(error => {
          console.error('Error fetching country:', error);
          reject(error); // Reject on API failure
        });
    });
  }

  isMobile(): boolean {
    return /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(this.userAgent);
  }

  isTablet(): boolean {
    return /(ipad|tablet|playbook|silk)|(android(?!.*mobile))/i.test(this.userAgent);
  }

  isDesktop(): boolean {
    return !this.isMobile() && !this.isTablet();
  }

  getDeviceType(): string {
    if (this.isMobile()) return 'mobile';
    if (this.isTablet()) return 'tablet';
    return 'desktop';
  }

  getOS(): string {
    const userAgent = navigator.userAgent;
    if (/Windows NT 10.0/.test(userAgent)) return 'Windows 10';
    if (/Windows NT 6.3/.test(userAgent)) return 'Windows 8.1';
    if (/Windows NT 6.2/.test(userAgent)) return 'Windows 8';
    if (/Windows NT 6.1/.test(userAgent)) return 'Windows 7';
    if (/Mac OS X/.test(userAgent)) return 'macOS';
    if (/Android/.test(userAgent)) return 'Android';
    if (/iPhone|iPad|iPod/.test(userAgent)) return 'iOS';
    if (/Linux/.test(userAgent)) return 'Linux';
    return 'Unknown OS';
  }

  getBrowser(): string {
    const userAgent = navigator.userAgent;
    if (/Chrome\/\d+/.test(userAgent) && !/Edg/.test(userAgent)) return 'Chrome';
    if (/Safari\/\d+/.test(userAgent) && !/Chrome/.test(userAgent)) return 'Safari';
    if (/Firefox\/\d+/.test(userAgent)) return 'Firefox';
    if (/Edg\/\d+/.test(userAgent)) return 'Edge';
    if (/MSIE \d+/.test(userAgent) || /Trident\/.*rv:\d+/.test(userAgent)) return 'Internet Explorer';
    return 'Unknown Browser';
  }

  text(id: string): string {
    return this.translatedTexts[id][this.language.value];
  }

  setCurrentLanguage(langCode: string) {
    this.ss.setLanguageCode(langCode);
    this.language.setValue(langCode || 'en');
  }

  isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  isDarkMode(): boolean {
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  }

  getPublicUser(uid: string) {
    if (!this.user || !this.user.uid) {
      this.cfs.getKvData(uid, 'user').pipe(takeUntil(this.unSubscribe)).subscribe(response => {
        if (response.success && response.data) {
          this.user = response.data;
          this.mevolveUserId = this.user.mevolveId;
        } else {
          throw new Error(`Failed to get value for id ${uid}`);
        }
      })
    }
  }

  openLanguageDialog() {
    this.dialog.open(LanguageDialogComponent, {
      maxWidth: '400px',
      width: '100%',
    });
  }

  openSubscriptionDialog() {
    this.dialog.open(SubscriptionDialogComponent, {
      maxWidth: '500px',
      width: '100%',
    });
  }

  viewPublicUser() {
    this.router.navigate(['/user', this.mevolveUserId]);
    // if (this.mevolveUserId) {
    // } else {
    // }
    // this.fbs.getMevolveIdFromUid(uid).pipe(
    //   takeUntil(this.unSubscribe)
    // ).subscribe({
    //   next: (response: any) => {
    //     if (response.success) {
    //       this.isLoading = false;
    //       this.mevolveUserId = response.data.mevolveId;
    //       this.router.navigate(['/user', response.data.mevolveId]);
    //       this.getPublicUser(uid);
    //     } else {
    //       this.isLoading = false;
    //       this.router.navigate(['/user', uid]);
    //     }
    //   },
    //   error: (err) => {
    //     console.error('API call failed:', err);
    //     this.isLoading = false;
    //     this.router.navigate(['/user', uid]);
    //   },
    //   complete: () => {

    //   }
    // });
  }

  getUserDisplayLetter(): string {
    return this.user ? this.user.name.charAt(0).toUpperCase() : '';
  }

  stopSubscription() {
    this.unSubscribe?.next();
    this.unSubscribe?.complete();
  }

  downloadFile(url: string, fileName: string): void {
    this.http.get(url, { responseType: 'blob' }).pipe(takeUntil(this.unSubscribe)).subscribe((blob: Blob | MediaSource) => {
      const a = document.createElement('a');
      const objectUrl = URL.createObjectURL(blob);
      a.href = objectUrl;
      a.download = fileName;
      a.click();
      URL.revokeObjectURL(objectUrl); // Clean up memory
    });
  }

  detectPlatform(): 'ios' | 'android' | 'other' {
    const userAgent = navigator.userAgent.toLowerCase();
    // iOS detection
    if (/iphone|ipad|ipod/.test(userAgent)) {
      return 'ios';
    }
    // Android detection
    if (/android/.test(userAgent)) {
      return 'android';
    }
    return 'other';
  }

  handleSharedModulesSave(event: Event, from: 'list' | 'note' | 'user', id: string): void {
    event.preventDefault();

    const platform = this.detectPlatform();

    switch (platform) {
      case 'ios':
        // window.location.href = environment.appStoreLink;
        // this.openInApp(`${from}/${id}`);
        const appStoreLink = environment.appStoreLink;
        window.location.href = appStoreLink;
        break;
      case 'android':
        window.location.href = environment.playStoreLink;
        // this.openSubscriptionDialog();
        break;
      default:
        // Use Angular router for desktop/other platforms
        this.router.navigate(['/no-access', from === 'list' ? 'save-list' : from === 'note' ? 'save-note' : from === 'user' ? 'follow-user' : '']);
        break;
    }
  }

  async detectUserLanguage() {
    const GEOLOCATION_API = 'https://ipapi.co/json/';

    try {
      // First, try browser language settings
      const browserLanguage = this.getBrowserLanguage();
      if (browserLanguage) {
        console.log("i am called browser language:", browserLanguage)
        return browserLanguage;
      }

      // If browser language isn't available or doesn't match our supported languages,
      // try IP geolocation

      const response = await fetch(GEOLOCATION_API);
      const data = await response.json();

      const countryCode = data.country_code?.toLowerCase();
      const languageCode = this.countryToLanguageMap[countryCode];

      // Check if the language is supported
      if (this.isLanguageSupported(languageCode || 'en')) {
        return languageCode;
      }

      // Default to English if no other matches found
      return 'en';
    } catch (error) {
      return 'en'; // Fallback to English
    }
  }

  getBrowserLanguage() {
    const browserLang = navigator.language;
    const primaryLang = browserLang.split('-')[0].toLowerCase();
    if (this.isLanguageSupported(primaryLang)) {
      return primaryLang;
    }
    return null;
  }

  updateMetaTag(id: string): void {
    const content = `app-id=${environment.appStoreId}, app-argument=${environment.webDomain}/${id}`;

    // Remove old tag if exists
    this.meta.removeTag("name='apple-itunes-app'");

    // Add updated tag
    this.meta.addTag({
      name: 'apple-itunes-app',
      content,
    });
  }

  openInApp(id: string) {
    this.isOpeningAppstore = true;
    const appLink = `hla://${id}`;
    const appStoreLink = environment.appStoreLink;
    let hiddenDetected = false;

    const visibilityChangeHandler = () => {
      if (document.hidden) {
        hiddenDetected = true;
      }
    };

    document.addEventListener('visibilitychange', visibilityChangeHandler);

    // Start measuring time
    const start = Date.now();

    // Try opening the app
    window.location.href = appLink;

    // Poll every 100ms to check if the app was opened
    const interval = setInterval(() => {
      const elapsed = Date.now() - start;

      if (hiddenDetected) {
        clearInterval(interval);
        document.removeEventListener('visibilitychange', visibilityChangeHandler);
        this.isOpeningAppstore = false;
      } else if (elapsed > 1000) { // wait max 1 seconds
        clearInterval(interval);
        document.removeEventListener('visibilitychange', visibilityChangeHandler);

        window.location.href = appStoreLink;
        this.isOpeningAppstore = false;
      }
    }, 200);
  }

  isLanguageSupported(langCode: string) {
    return this.languageList.some(lang => lang.code === langCode);
  }

  copyLink() {
    this.clipboard.copy(window.location.href);
    this.toastr.success(
      `<div class="d-flex justify-content-between align-items-center">${this.text('linkCopied')}<span class="toast-dismiss" onclick="this.parentElement.parentElement.style.display='none'">${this.text('dismiss')}</span></div>`,
    );
  }

  qrDownloaded() {
    this.toastr.success(
      `<div class="d-flex justify-content-between align-items-center">${this.text('QRDownloaded')}<span class="toast-dismiss" onclick="this.parentElement.parentElement.style.display='none'">${this.text('dismiss')}</span></div>`,
    );
  }

  getFormattedDate(date: Date, format: string): string {
    return formatDate(date, format, this.language.value);
  }
}
