name = "##CLOUDFLARE_PAGE_NAME##"
compatibility_date = "##CLOUDFLARE_COMPATIBILITY_DATE##"
pages_build_output_dir = "dist/angular/browser"

[[env.production.kv_namespaces]]
binding = "CF_LIST_KV"
id = "##CLOUDFLARE_LIST_KV_ID##"

[[env.production.kv_namespaces]]
binding = "CF_NOTE_KV"
id = "##CLOUDFLARE_NOTE_KV_ID##"

[[env.production.kv_namespaces]]
binding = "CF_USER_KV"
id = "##CLOUDFLARE_USER_KV_ID##"

[[env.production.kv_namespaces]]
binding = "CF_MEVID_UID_MAPPER_KV"
id = "##CLOUDFLARE_MEVID_UID_MAPPER_KV_ID##"