# CodeRabbit Configuration for mevolve Flutter project
language: en-US
tone_instructions: ''
early_access: false
enable_free_tier: true

# PR Title and Description Features:
# - Auto Title: Set PR title as '@coderabbitai' when creating new PR (won't work on edits)
# - Auto Description: Enabled by default, adds "Summary by CodeRabbit" automatically
# - To disable auto description: Add '@coderabbitai ignore' in PR description
# 
# Manual Triggers (use in PR comments):
# - '@coderabbitai generate title' - Generate title suggestion for existing PR
# - '@coderabbitai summary' - Generate high-level summary

reviews:
  profile: chill
  request_changes_workflow: false
  high_level_summary: true  # Controls if summaries are generated in PR description
  high_level_summary_placeholder: '@coderabbitai summary'
  high_level_summary_in_walkthrough: false
  auto_title_placeholder: '@coderabbitai'  # Use this as PR title when creating new PR
  auto_title_instructions: ''
  review_status: true
  commit_status: true
  fail_commit_status: false
  collapse_walkthrough: false
  changed_files_summary: true
  sequence_diagrams: true
  assess_linked_issues: true
  related_issues: true
  related_prs: true
  suggested_labels: true
  auto_apply_labels: false
  suggested_reviewers: true
  auto_assign_reviewers: false
  poem: true
  labeling_instructions: []
  path_filters: []
  path_instructions: []
  abort_on_close: true
  disable_cache: false
  auto_review:
    enabled: true
    auto_incremental_review: true
    ignore_title_keywords: []
    labels: []
    drafts: false
    base_branches: []
  finishing_touches:
    docstrings:
      enabled: true
    unit_tests:
      enabled: true
  tools:
    # Flutter/Dart specific tools
    ast-grep:
      rule_dirs: []
      util_dirs: []
      essential_rules: true
      packages: []
    
    # Keep for shell scripts in project
    shellcheck:
      enabled: true
    
    # Keep for markdown files (README, docs)
    markdownlint:
      enabled: true
    
    # Keep for GitHub Actions
    github-checks:
      enabled: true
      timeout_ms: 90000
    actionlint:
      enabled: true
    
    # Keep for language/grammar checks
    languagetool:
      enabled: true
      enabled_rules: []
      disabled_rules: []
      enabled_categories: []
      disabled_categories: []
      enabled_only: false
      level: default
    
    # Keep for iOS native code
    swiftlint:
      enabled: true
    
    # Keep for YAML files (pubspec, configs)
    yamllint:
      enabled: true
    
    # Keep for security scanning
    gitleaks:
      enabled: true
    semgrep:
      enabled: true
    
    # Keep for JavaScript/TypeScript (Angular, functions)
    eslint:
      enabled: true
    biome:
      enabled: true
    oxc:
      enabled: true
    
    # Keep for SQL files if any
    sqlfluff:
      enabled: true
    
    # Disable tools not needed for Flutter project
    ruff:
      enabled: false  # Python linter
    phpstan:
      enabled: false  # PHP static analyzer
    golangci-lint:
      enabled: false  # Go linter
    rubocop:
      enabled: false  # Ruby linter
    buf:
      enabled: false  # Protocol buffer linter
    regal:
      enabled: false  # Rego linter
    pmd:
      enabled: false  # Java/XML analyzer
    cppcheck:
      enabled: false  # C++ analyzer
    prismaLint:
      enabled: false  # Prisma ORM linter
    shopifyThemeCheck:
      enabled: false  # Shopify theme linter
    luacheck:
      enabled: false  # Lua linter
    hadolint:
      enabled: false  # Dockerfile linter (no Docker files seen)
    detekt:
      enabled: false  # Kotlin linter (using Dart/Flutter)
    checkov:
      enabled: false  # Infrastructure as code scanner
    circleci:
      enabled: false  # CircleCI config validator (using Codemagic)
chat:
  auto_reply: true
  integrations:
    jira:
      usage: auto
    linear:
      usage: auto
knowledge_base:
  opt_out: false
  web_search:
    enabled: true
  learnings:
    scope: auto
  issues:
    scope: auto
  jira:
    usage: auto
    project_keys: []
  linear:
    usage: auto
    team_keys: []
  pull_requests:
    scope: auto
code_generation:
  docstrings:
    language: en-US
    path_instructions: []
  unit_tests:
    path_instructions: []
