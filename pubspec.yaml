name: mevolve
description: Mevolve
version: 0.104.132+3
publish_to: none

environment:
  sdk: ">=3.0.0 <4.0.0"

dependencies:
  # ================================
  # FLUTTER SDK
  # ================================
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  flutter_web_plugins:
    sdk: flutter
  flutter_driver:
    sdk: flutter

  # ================================
  # STATE MANAGEMENT & ARCHITECTURE
  # ================================
  bloc: ^8.1.4
  flutter_bloc: ^8.1.6
  equatable: ^2.0.5
  rxdart: ^0.28.0
  formz: ^0.4.1

  # ================================
  # NAVIGATION & ROUTING
  # ================================
  go_router: ^16.0.0

  # ================================
  # UI & DESIGN COMPONENTS
  # ================================
  flutter_svg: ^2.0.10+1
  google_fonts: ^6.2.1
  flutter_staggered_animations: ^1.1.1
  animated_list_plus: ^0.5.2
  percent_indicator: ^4.2.3
  loading_indicator: ^3.1.1
  loading_animation_widget: ^1.2.1
  dotted_line: ^3.2.2
  sliver_tools: ^0.2.12
  infinite_listview: ^1.1.0
  photo_view: ^0.15.0
  carousel_slider: ^5.0.0
  flutter_speed_dial: ^7.0.0
  modal_bottom_sheet: ^3.0.0
  flextras: ^1.0.0
  rive: ^0.13.13
  flutter_context_menu: ^0.2.0
  visibility_detector: ^0.4.0+2
  scrollable_positioned_list: ^0.3.8

  # ================================
  # UI COMPONENTS - CUSTOM FORKS
  # ================================
  flutter_slidable:
    git:
      url: https://github.com/realtime-innovations/flutter_slidable

  # ================================
  # TEXT & RICH TEXT EDITING
  # ================================
  flutter_quill: 10.8.0
  flutter_quill_delta_from_html: ^1.3.12
  vsc_quill_delta_to_html: ^1.0.4
  flutter_html: 3.0.0-beta.2
  html_to_flutter: ^0.2.3-dev.8
  html2md: ^1.3.1
  linkfy_text: ^1.1.6

  # ================================
  # FIREBASE & BACKEND SERVICES
  # ================================
  firebase_core: 3.12.1
  firebase_auth: ^5.3.3
  firebase_database: ^11.1.3
  firebase_messaging: ^15.2.4
  firebase_crashlytics: ^4.1.2
  firebase_analytics: ^11.3.2
  firebase_storage: ^12.4.4
  # Open issue on windows cloud firestore https://github.com/firebase/flutterfire/issues/13212
  cloud_firestore: ^5.4.2
  cloud_functions: ^5.1.2
  firebase_app_check:

  # ================================
  # FIREBASE - CUSTOM COMPONENTS
  # ================================
  firebase_cached_image:
    git:
      url: https://github.com/realtime-innovations/firebase_cached_image_rl.git
      ref: feature/me-encryption-support

  # ================================
  # AUTHENTICATION & SECURITY
  # ================================
  sign_in_with_apple: ^6.1.4
  google_sign_in: ^7.0.0
  extension_google_sign_in_as_googleapis_auth: ^3.0.0
  local_auth: ^2.3.0
  flutter_secure_storage: ^9.2.2
  encrypt: ^5.0.3
  crypto: ^3.0.5
  webcrypto: ^0.5.7
  basic_utils: ^5.7.0
  eciesdart: ^0.2.0

  # ================================
  # MEDIA & MULTIMEDIA
  # ================================
  # Camera & Image Processing
  camera: ^0.11.2
  image_picker: ^0.8.5+3
  flutter_image_compress: ^2.3.0
  image: ^4.2.0
  image_size_getter: ^2.2.0
  gal: ^2.3.0
  
  # Video Processing
  media_kit: ^1.1.11
  media_kit_video: ^1.2.5
  media_kit_libs_video: ^1.0.5
  flutter_video_info: ^1.3.2
  video_compress: ^3.1.3
  
  # Audio Processing & Recording
  audio_service: ^0.18.15
  just_audio: ^0.10.4
  audioplayers: ^6.1.0
  audio_session: ^0.1.21
  audio_waveforms: ^1.3.0 # for iOS and Android
  record: ^6.0.0 # for web, mac, windows and linux
  speech_to_text: ^7.0.0
  flutter_volume_controller: ^1.3.3

  # Screenshots & Screen Capture
  screenshot:
    git: https://github.com/realtime-innovations/screenshot.git

  # ================================
  # NETWORKING & HTTP
  # ================================
  http: ^1.2.2
  connectivity_plus: ^6.0.5
  googleapis: ^14.0.0
  googleapis_auth: ^1.6.0

  # ================================
  # DATABASE & LOCAL STORAGE
  # ================================
  drift: ^2.26.0
  drift_flutter: ^0.2.4
  sqlite3_flutter_libs: ^0.5.32
  sqlite3: ^2.7.5
  shared_preferences: ^2.3.2
  flutter_cache_manager: ^3.4.1

  # ================================
  # DEVICE & PLATFORM SERVICES
  # ================================
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2
  permission_handler: ^11.3.1
  app_settings: ^5.1.1
  open_settings_plus: ^0.3.3
  vibration: ^2.0.0
  flutter_udid: ^4.0.0
  devicelocale: ^0.8.1
  restart_app: ^1.2.1
  window_manager: ^0.4.2

  # ================================
  # BACKGROUND SERVICES & TASKS
  # ================================
  flutter_background_service: ^5.0.10
  workmanager:
    git:
      url: https://github.com/realtime-innovations/flutter_workmanager.git
      ref: b783000
  isolate_manager: ^5.2.1+3
  system_info2: ^4.0.0

  # ================================
  # NOTIFICATIONS & ACTIVITIES
  # ================================
  flutter_local_notifications: ^17.2.2
  live_activities: ^1.9.0
  home_widget: ^0.7.0+1

  # ================================
  # UTILITIES & HELPERS
  # ================================
  # Core Utilities
  collection: ^1.18.0
  path: ^1.9.0
  uuid: ^4.5.0
  logger: ^1.1.0
  easy_debounce: ^2.0.3
  mime: ^1.0.6
  xml: ^6.5.0
  json_patch: ^3.0.0
  
  # Date & Time
  intl: ^0.19.0
  jiffy: ^6.3.1
  get_time_ago: ^2.0.0
  timeago: ^3.7.0
  timezone: ^0.9.4
  flutter_timezone: ^3.0.1
  rrule: ^0.2.16
  ntp: ^2.0.0

  # File & Cross Platform
  file: ^7.0.0
  cross_file: ^0.3.4+2
  file_picker: ^9.2.3
  open_file_plus: ^3.4.1+1
  path_provider: ^2.1.4
  universal_platform: ^1.1.0
  universal_html: ^2.2.4
  universal_io: ^2.2.2

  # ================================
  # WEB & WEBVIEW
  # ================================
  webview_flutter: ^4.9.0
  webview_flutter_web: ^0.2.3
  flutter_js: ^0.8.1

  # ================================
  # UI EXTENSIONS & INPUT
  # ================================
  flutter_keyboard_visibility:
  keyboard_attachable: ^2.2.0
  flutter_native_splash: ^2.4.3
  url_launcher: ^6.3.0
  share_plus: ^7.0.0
  qr_flutter: ^4.1.0

  # ================================
  # MONETIZATION & ANALYTICS
  # ================================
  purchases_flutter: ^8.5.2
  in_app_review: ^2.0.9
  google_mobile_ads: ^5.1.0
  advertising_id: ^2.7.1
  android_play_install_referrer: ^0.4.0
  device_region: ^1.4.0

  # ================================
  # CODE PUSH & UPDATES
  # ================================
  shorebird_code_push: ^2.0.3

  # ================================
  # FUTURE/DISABLED DEPENDENCIES
  # ================================
  # Uncomment when needed
  #  google_mlkit_commons: ^0.7.1
  #  google_mlkit_language_id: ^0.11.0
  #  ffmpeg_kit_flutter: ^6.0.3

dev_dependencies:
  build_runner: ^2.4.12
  dependency_validator: ^4.1.0
  flutter_gen: ^5.7.0
  flutter_gen_runner: ^5.7.0
  flutter_launcher_icons: ^0.14.4
  flutter_lints: ^2.0.1
  flutter_test:
    sdk: flutter
  mevolve_generator:
    path: mevolve_generator
  drift_dev: ^2.26.0
  analyzer: ^6.7.0

dependency_overrides:
  # Remove when keyboard_attachable is updated.
  flutter_keyboard_visibility: ^6.0.0
  flutter_colorpicker:
    git:
      url: https://github.com/realtime-innovations/flutter_colorpicker
      ref: master

  # Once this PR merged for firebase_app_check we can safely get with the pub.dev package and remove below github dependencies:
  # https://github.com/firebase/flutterfire/pull/16942
  firebase_app_check:
    git:
      url: https://github.com/realtime-innovations/flutterfire.git
      ref: app_check_debug_tokens
      path: packages/firebase_app_check/firebase_app_check
  firebase_app_check_platform_interface:
    git:
      url: https://github.com/realtime-innovations/flutterfire.git
      ref: app_check_debug_tokens
      path: packages/firebase_app_check/firebase_app_check_platform_interface
  firebase_app_check_web:
    git:
      url: https://github.com/realtime-innovations/flutterfire.git
      ref: app_check_debug_tokens
      path: packages/firebase_app_check/firebase_app_check_web
#  process: ^5.0.2

flutter_icons:
  android: "ic_launcher"
  ios: true
  remove_alpha_ios: true
  image_path: "assets/mevolve/app_icon.png"

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/mevolve/
    - assets/svg/
    - assets/social/
    - assets/png/
    - assets/png/splash/
    - assets/animations/
    - assets/tones/
    - assets/config/
    - shorebird.yaml
    - assets/config/serviceaccounts/

  fonts:
    - family: NotoColorEmoji
      fonts:
        - asset: assets/fonts/NotoColorEmoji.ttf

# Flutter Gen Configuration required to generate the file refrences of assets
flutter_gen:
  output: lib/generated/ # Optional (default: lib/gen/)
  line_length: 80 # Optional (default: 80)

  # Optional
  integrations:
    flutter_svg: true
    # can be set true if needed in the future (default: false)
  #    flare_flutter: true
  #    rive: true
  #    lottie: true