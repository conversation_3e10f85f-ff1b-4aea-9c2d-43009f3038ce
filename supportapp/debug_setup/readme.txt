# Setup staging build debugging:
1. Create a file `app_debug_tokens_env.json` in this location with the following content and fill it:
   ```
   {
   "supportAppWebAppCheckDebugToken": "ask-this-from-devs",
   }
   ```
2. Then go index.html and add the following script tag in body at start:
   ```
   <!-- Firebase App Check Debug Token -->
   <script>
     self.FIREBASE_APPCHECK_DEBUG_TOKEN = "value-of-supportAppWebAppCheckDebugToken";
   </script>
   ```
3. Run the app in staging env with buildFlavor as `staging` and you should see the debug logs in console.

