{"functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"]}], "emulators": {"auth": {"port": 9099, "host": "0.0.0.0"}, "functions": {"port": 5001, "host": "0.0.0.0"}, "firestore": {"port": 8080, "host": "0.0.0.0"}, "hosting": {"port": 5002, "host": "0.0.0.0"}, "storage": {"port": 9199, "host": "0.0.0.0"}, "ui": {"enabled": true, "port": 4000}, "eventarc": {"port": 9299}, "database": {"port": 9000}, "pubsub": {"port": 8085}, "singleProjectMode": true}, "hosting": {"target": "mevolve_support", "public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"]}, "flutter": {"platforms": {"android": {"default": {"projectId": "mevolve-dev", "appId": "1:825137847857:android:736b0a55fb53a918ff3f10", "fileOutput": "android/app/google-services.json"}, "buildConfigurations": {"src/dev": {"projectId": "mevolve-dev", "appId": "1:825137847857:android:736b0a55fb53a918ff3f10", "fileOutput": "android/app/src/dev/google-services.json"}, "src/qa": {"projectId": "mevolve-qa", "appId": "1:922862417651:android:bf208bb7ac8258387fe986", "fileOutput": "android/app/src/qa/google-services.json"}, "src/staging": {"projectId": "mevolve-staging", "appId": "1:953131275065:android:4643e68b236c29910a8ee8", "fileOutput": "android/app/src/staging/google-services.json"}, "src/prod": {"projectId": "mevolve-prod", "appId": "1:176451695467:android:f7e14798f69802d8d4922e", "fileOutput": "android/app/src/prod/google-services.json"}, "src/hotfix": {"projectId": "mevolve-hotfix", "appId": "1:872725797921:android:04e04bd86b53c916661760", "fileOutput": "android/app/src/hotfix/google-services.json"}}}, "ios": {"default": {"projectId": "mevolve-dev", "appId": "1:825137847857:ios:efefbdcd3be96949ff3f10", "uploadDebugSymbols": false, "fileOutput": "ios/Runner/GoogleService-Info.plist"}, "buildConfigurations": {"Config/qa": {"projectId": "mevolve-qa", "appId": "1:922862417651:ios:efefbdcd3be96949ff3f10", "uploadDebugSymbols": false, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.support.qa.plist"}, "Config/staging": {"projectId": "mevolve-staging", "appId": "1:953131275065:ios:efefbdcd3be96949ff3f10", "uploadDebugSymbols": false, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.support.staging.plist"}, "Config/prod": {"projectId": "mevolve-prod", "appId": "1:176451695467:ios:efefbdcd3be96949ff3f10", "uploadDebugSymbols": false, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.support.prod.plist"}, "Config/hotfix": {"projectId": "mevolve-hotfix", "appId": "1:872725797921:ios:efefbdcd3be96949ff3f10", "uploadDebugSymbols": false, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.support.hotfix.plist"}}}, "macos": {"default": {"projectId": "mevolve-staging", "appId": "1:953131275065:ios:7730ee7c0857b7c90a8ee8", "uploadDebugSymbols": false, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}, "dart": {"lib/firebase_options_dev.dart": {"projectId": "mevolve-dev", "configurations": {"android": "1:825137847857:android:736b0a55fb53a918ff3f10", "ios": "1:825137847857:ios:efefbdcd3be96949ff3f10", "web": "1:825137847857:web:369c2a41142c32b9ff3f10"}}, "lib/firebase_options_qa.dart": {"projectId": "mevolve-qa", "configurations": {"android": "1:922862417651:android:bf208bb7ac8258387fe986", "web": "1:922862417651:web:5955ee70cbfa7f727fe986"}}, "lib/firebase_options_staging.dart": {"projectId": "mevolve-staging", "configurations": {"android": "1:953131275065:android:4643e68b236c29910a8ee8", "web": "1:953131275065:web:645ba3f92a71ee290a8ee8"}}, "lib/firebase_options_prod.dart": {"projectId": "mevolve-prod", "configurations": {"android": "1:176451695467:android:f7e14798f69802d8d4922e", "web": "1:176451695467:web:09625f594e7f361cd4922e"}}, "lib/firebase_options_hotfix.dart": {"projectId": "mevolve-hotfix", "configurations": {"android": "1:872725797921:android:04e04bd86b53c916661760", "web": "1:872725797921:web:4badb38cd130dd3f661760"}}}}}}