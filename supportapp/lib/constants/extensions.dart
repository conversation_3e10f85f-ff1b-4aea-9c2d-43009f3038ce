import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

extension DateTimeExtension on DateTime {
  bool isAfterOrEqualTo(DateTime dateTime) {
    final date = this;

    final isAtSameMomentAs = dateTime.isAtSameMomentAs(date);
    return isAtSameMomentAs | date.isAfter(dateTime);
  }

  /// Returns a date with the first second of the day
  /// Example: 2022-01-01 12:23:45 -> 2022-01-01 00:00:00
  DateTime onlyDate() => DateTime(year, month, day);

  /// Returns a date with the last second of the day
  /// Example: 2021-01-01 00:00:00 -> 2021-01-01 23:59:59
  DateTime onlyDateWithLastSecond() => DateTime(year, month, day, 23, 59, 59);

  /// Returns the last day of the month
  /// Example: 2021-01-01 00:00:00 -> 31
  int lastDay() => DateTime(year, month + 1, 0).day;

  /// Returns a previous date to this date with the last second of the day
  /// Example: 2021-01-01 00:00:00 -> 2020-12-31 23:59:59
  DateTime yesterday() =>
      subtract(const Duration(days: 1)).onlyDateWithLastSecond();

  /// Returns a next date to this date with the first second of the day
  /// Example: 2021-01-01 23:59:59 -> 2021-01-02 00:00:00
  DateTime tomorrow() => add(const Duration(days: 1)).onlyDate();

  String onlyDateString() => DateFormat('yyyy-MM-dd').format(this);

  String formattedDate() => DateTime.now().isSameDate(this)
      ? 'Today'
      : this == DateTime.now().tomorrow()
          ? 'Tomorrow'
          : DateFormat('dd MMM yyyy').format(this);

  ///Returns the Date with short Day e.g. 2023-01-01 -> 1 Jan 2023, Sun
  String formattedDayDate() => DateTime.now().isSameDate(this)
      ? 'Today'
      : DateFormat('d MMM yyyy, EE').format(this);

  ///Returns the Date with short Day e.g. 2023-01-01 -> 1 Jan 2023, Sun
  String formattedDayDateTime() =>
      // DateTime.now().isSameDate(this)
      //     ? 'Today, ${DateFormat('h:mm a').format(this)}'
      //     :
      DateFormat('d MMM yyyy, h:mm a').format(this);

  /// Returns a date with day suffixed with st, nd, rd or th
  /// and month in short form.
  /// Example: 2021-01-01 -> 1st Jan
  String formattedDateMonthSuffix({bool showYear = false}) {
    final date = this;
    final day = date.day;
    final monthInString = DateFormat('MMM').format(date);
    final year = date.year;
    final suffix = day == 1
        ? 'st'
        : day == 2
            ? 'nd'
            : day == 3
                ? 'rd'
                : 'th';
    return DateTime.now().isSameDate(date)
        ? 'Today'
        : '$day$suffix $monthInString ${showYear ? year : ''}';
  }

  bool isBeforeOrEqualTo(DateTime dateTime) {
    final date = this;

    final isAtSameMomentAs = dateTime.isAtSameMomentAs(date);
    return isAtSameMomentAs | date.isBefore(dateTime);
  }

  bool isSameDate(DateTime dateTime) {
    final date = this;
    return date.year == dateTime.year &&
        date.month == dateTime.month &&
        date.day == dateTime.day;
  }

  bool isBetween(
    DateTime fromDateTime,
    DateTime toDateTime,
  ) {
    final date = this;
    final isAfter = date.isAfterOrEqualTo(fromDateTime);
    final isBefore = date.isBeforeOrEqualTo(toDateTime);
    return isAfter && isBefore;
  }

  /// return ISO date string with timezone offset. The time is in local time.
  /// Example: 2022-01-01 12:23:45 -> 2022-01-01T12:23:45+08:00
  String toIsoDateStringWithOffset() {
    var offset = timeZoneOffset;
    var hours =
        offset.inHours > 0 ? offset.inHours : 1; // For fixing divide by 0
    String val = toIso8601String();
    if (!offset.isNegative) {
      val =
          "$val+${offset.inHours.toString().padLeft(2, '0')}:${(offset.inMinutes % (hours * 60)).toString().padLeft(2, '0')}";
    } else {
      val =
          "$val-${(-offset.inHours).toString().padLeft(2, '0')}:${(offset.inMinutes % (hours * 60)).toString().padLeft(2, '0')}";
    }
    return val;
  }
}

extension TimeOfDayExtension on TimeOfDay {
  int compareTo(TimeOfDay time2) =>
      DateTime.now().onlyDate().copyWith(hour: hour, minute: minute).compareTo(
            DateTime.now()
                .onlyDate()
                .copyWith(hour: time2.hour, minute: time2.minute),
          );
}

extension IntColorComponents on Color {
  int get intAlpha =>
      (a * 255).toInt(); // Convert alpha (double) to int between 0 and 255
  int get intRed =>
      (r * 255).toInt(); // Convert red (double) to int between 0 and 255
  int get intGreen =>
      (g * 255).toInt(); // Convert green (double) to int between 0 and 255
  int get intBlue =>
      (b * 255).toInt(); // Convert blue (double) to int between 0 and 255
}

extension HexColor on Color {
  String toHex({bool leadingHashSign = true}) => '${leadingHashSign ? '#' : ''}'
          '${intAlpha.toRadixString(16).padLeft(2, '0')}'
          '${intRed.toRadixString(16).padLeft(2, '0')}'
          '${intGreen.toRadixString(16).padLeft(2, '0')}'
          '${intBlue.toRadixString(16).padLeft(2, '0')}'
      .replaceAll('#ff', '#');
}
