abstract class AppConstant {
  static const int maxLimit = 10000;
}

abstract class FirebaseConstants {
  /// the ip that the firebase can use inside the app.
  /// if you are using emulator the the default IP
  /// is [********] for android and [localhost] for iOS
  /// if you are using real device then the default IP
  /// would be the IP of your machine and this machine should
  /// be connected to the same network as your device.

  static const String firebaseLocalHostAndroidIP = '********';
  static const String firebaseLocalHostIOSIP = 'localhost';
  static const String firebaseLocalHostWebIP = 'localhost';

  /// These are the default ports for the firebase emulator.
  /// If these ports aren't available then firebase emulator will auto assign
  /// a new port for the emulator which needs to be updated here.

  static const int firebaseAuthenticatorPort = 9099;
  static const int firebaseFunctionsPort = 5001;
  static const int firebaseFirestorePort = 8080;
  static const int firebaseDatabasePort = 9000;
  static const int firebaseStoragePort = 9199;
}
