import 'package:flutter/foundation.dart';
import 'package:mevolvesupport/constants/release_config.dart';
import 'package:mevolvesupport/enums/environment_type.dart';
import 'package:package_info_plus/package_info_plus.dart';

class AppConfig {
  AppConfig._();

  static final AppConfig _instance = AppConfig._();

  static AppConfig get instance => _instance;

  // This version comes from release_config.json when cloud db version is changed
  static int dbVersion = ReleaseConfig.instance.dbVersion;

  final EnvironmentType _environmentType = EnvironmentType.values
      .byName(const String.fromEnvironment('flavor', defaultValue: 'dev'));
  String _version = '';
  String _packageName = '';
  int _appMajorVersion = 0;

  EnvironmentType get environmentType => _environmentType;

  String get version => _version;

  String get packageName => _packageName;

  int get appMajorVersion => _appMajorVersion;

  bool get isTestEnv =>
      _environmentType == EnvironmentType.dev ||
      _environmentType == EnvironmentType.qa ||
      _environmentType == EnvironmentType.staging;

  /// Developer mode flag - shows technical debug info only to developers
  /// Production builds will have clean UI for support employees
  static bool get isDeveloperMode => kDebugMode;

  Future<void> initAppConfig() async {
    final packageInfo = await PackageInfo.fromPlatform();
    _version = packageInfo.version;
    _packageName = packageInfo.packageName;
    _appMajorVersion = int.tryParse(_version.split('.').first) ?? 1;
  }
}
