/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  Mevolve Generator
/// *****************************************************

// ignore_for_file: dangling_library_doc_comments
/// Enum representing available environments
enum Environment {
  dev,
  qa,
  staging,
  prod,
  hotfix,
}

/// Configuration for a specific environment
class EnvironmentConfig {
  EnvironmentConfig({
    required this.id,
    required this.dbVersion,
    required this.rid,
    required this.androidAvailability,
    required this.iosAvailability,
    required this.webAvailability,
    required this.windowsAvailability,
    required this.macAvailability,
    required this.linuxAvailability,
    required this.enabledSegments,
    required this.debugScreen,
    required this.support,
    required this.allowShorebirdPatchInstall,
    required this.defaultThemeColor,
    required this.defaultThemeMode,
    required this.maxPasscodeAttempts,
    required this.passcodeReAuthTimeout,
    required this.modalBarrierOpacity,
    required this.colorsVersion,
    required this.showAds,
    required this.adWaitTimeInSec,
    required this.noOfInteractionsForAds,
    required this.gracePeriodInMin,
    required this.translationsVersion,
    required this.externalDescription,
    required this.cloudUpdatedAt,
  });
  final String id;
  final int dbVersion;
  final double rid;
  final bool androidAvailability;
  final bool iosAvailability;
  final bool webAvailability;
  final bool windowsAvailability;
  final bool macAvailability;
  final bool linuxAvailability;
  final List<String> enabledSegments;
  final List<String> debugScreen;
  final List<String> support;
  final List<String> allowShorebirdPatchInstall;
  final String defaultThemeColor;
  final String defaultThemeMode;
  final int maxPasscodeAttempts;
  final int passcodeReAuthTimeout;
  final int modalBarrierOpacity;
  final int colorsVersion;
  final bool showAds;
  final int adWaitTimeInSec;
  final int noOfInteractionsForAds;
  final int gracePeriodInMin;
  final int translationsVersion;
  final Map<String, dynamic> externalDescription;
  final DateTime cloudUpdatedAt;

  EnvironmentConfig copyWith({
    String? id,
    int? dbVersion,
    double? rid,
    bool? androidAvailability,
    bool? iosAvailability,
    bool? webAvailability,
    bool? windowsAvailability,
    bool? macAvailability,
    bool? linuxAvailability,
    List<String>? enabledSegments,
    List<String>? debugScreen,
    List<String>? support,
    List<String>? allowShorebirdPatchInstall,
    String? defaultThemeColor,
    String? defaultThemeMode,
    int? maxPasscodeAttempts,
    int? passcodeReAuthTimeout,
    int? modalBarrierOpacity,
    int? colorsVersion,
    bool? showAds,
    int? adWaitTimeInSec,
    int? noOfInteractionsForAds,
    int? gracePeriodInMin,
    int? translationsVersion,
    Map<String, dynamic>? externalDescription,
    DateTime? cloudUpdatedAt,
  }) {
    return EnvironmentConfig(
      id: id ?? this.id,
      dbVersion: dbVersion ?? this.dbVersion,
      rid: rid ?? this.rid,
      androidAvailability: androidAvailability ?? this.androidAvailability,
      iosAvailability: iosAvailability ?? this.iosAvailability,
      webAvailability: webAvailability ?? this.webAvailability,
      windowsAvailability: windowsAvailability ?? this.windowsAvailability,
      macAvailability: macAvailability ?? this.macAvailability,
      linuxAvailability: linuxAvailability ?? this.linuxAvailability,
      enabledSegments: enabledSegments ?? this.enabledSegments,
      debugScreen: debugScreen ?? this.debugScreen,
      support: support ?? this.support,
      allowShorebirdPatchInstall:
          allowShorebirdPatchInstall ?? this.allowShorebirdPatchInstall,
      defaultThemeColor: defaultThemeColor ?? this.defaultThemeColor,
      defaultThemeMode: defaultThemeMode ?? this.defaultThemeMode,
      maxPasscodeAttempts: maxPasscodeAttempts ?? this.maxPasscodeAttempts,
      passcodeReAuthTimeout:
          passcodeReAuthTimeout ?? this.passcodeReAuthTimeout,
      modalBarrierOpacity: modalBarrierOpacity ?? this.modalBarrierOpacity,
      colorsVersion: colorsVersion ?? this.colorsVersion,
      showAds: showAds ?? this.showAds,
      adWaitTimeInSec: adWaitTimeInSec ?? this.adWaitTimeInSec,
      noOfInteractionsForAds:
          noOfInteractionsForAds ?? this.noOfInteractionsForAds,
      gracePeriodInMin: gracePeriodInMin ?? this.gracePeriodInMin,
      translationsVersion: translationsVersion ?? this.translationsVersion,
      externalDescription: externalDescription ?? this.externalDescription,
      cloudUpdatedAt: cloudUpdatedAt ?? this.cloudUpdatedAt,
    );
  }
}

class ReleaseConfig {
  ReleaseConfig._();

  static final ReleaseConfig _instance = ReleaseConfig._();

  static ReleaseConfig get instance => _instance;

  // Get the current environment from flutter build flavor
  static final Environment _currentEnvironment =
      _getCurrentEnvironmentFromFlavor();

  // Method to get environment from flutter build flavor
  static Environment _getCurrentEnvironmentFromFlavor() {
    const String flavorName =
        String.fromEnvironment('flavor', defaultValue: 'dev');
    try {
      return Environment.values.byName(flavorName);
    } catch (e) {
      // Fallback to dev if invalid flavor name
      return Environment.dev;
    }
  }

  // Map storing config for all environments
  final Map<Environment, EnvironmentConfig> _environmentConfigs = {
    Environment.dev: EnvironmentConfig(
      id: '5df00a99-c38d-4185-92ae-7d2c84f27928',
      dbVersion: 132,
      rid: 0.104,
      androidAvailability: true,
      iosAvailability: true,
      webAvailability: true,
      windowsAvailability: true,
      macAvailability: true,
      linuxAvailability: true,
      enabledSegments: ['ALL'],
      debugScreen: ['ALL'],
      support: ['ALL'],
      allowShorebirdPatchInstall: [],
      defaultThemeColor: 'theme1',
      defaultThemeMode: 'dark',
      maxPasscodeAttempts: 5,
      passcodeReAuthTimeout: 300000,
      modalBarrierOpacity: 70,
      colorsVersion: 38,
      showAds: true,
      adWaitTimeInSec: 5,
      noOfInteractionsForAds: 5,
      gracePeriodInMin: 43200,
      translationsVersion: 227,
      externalDescription: {
        'en': 'Hide features shared with you without afttecting others view',
        'de':
            'Mit Ihnen geteilte Funktionen verbergen, ohne die Ansicht anderer zu beeinträchtigen',
        'fr':
            'Masquer les fonctionnalités partagées avec vous sans affecter la vue des autres',
        'es':
            'Ocultar funciones compartidas contigo sin afectar la vista de los demás',
        'it':
            'Nascondi le funzionalità condivise con te senza compromettere la vista degli altri',
        'pt':
            'Ocultar recursos compartilhados com você sem detectar a visualização de outras pessoas',
      },
      cloudUpdatedAt: DateTime.parse('2025-07-15T00:20:25.174003'),
    ),
    Environment.qa: EnvironmentConfig(
      id: '5df00a99-c38d-4185-92ae-7d2c84f27928',
      dbVersion: 132,
      rid: 0.104,
      androidAvailability: true,
      iosAvailability: true,
      webAvailability: true,
      windowsAvailability: true,
      macAvailability: true,
      linuxAvailability: true,
      enabledSegments: ['ALL'],
      debugScreen: ['ALL'],
      support: ['ALL'],
      allowShorebirdPatchInstall: [],
      defaultThemeColor: 'theme1',
      defaultThemeMode: 'dark',
      maxPasscodeAttempts: 5,
      passcodeReAuthTimeout: 300000,
      modalBarrierOpacity: 70,
      colorsVersion: 38,
      showAds: true,
      adWaitTimeInSec: 5,
      noOfInteractionsForAds: 5,
      gracePeriodInMin: 43200,
      translationsVersion: 227,
      externalDescription: {
        'en': 'Hide features shared with you without afttecting others view',
        'de':
            'Mit Ihnen geteilte Funktionen verbergen, ohne die Ansicht anderer zu beeinträchtigen',
        'fr':
            'Masquer les fonctionnalités partagées avec vous sans affecter la vue des autres',
        'es':
            'Ocultar funciones compartidas contigo sin afectar la vista de los demás',
        'it':
            'Nascondi le funzionalità condivise con te senza compromettere la vista degli altri',
        'pt':
            'Ocultar recursos compartilhados com você sem detectar a visualização de outras pessoas',
      },
      cloudUpdatedAt: DateTime.parse('2025-07-15T00:20:25.174927'),
    ),
    Environment.staging: EnvironmentConfig(
      id: '5df00a99-c38d-4185-92ae-7d2c84f27928',
      dbVersion: 132,
      rid: 0.104,
      androidAvailability: true,
      iosAvailability: true,
      webAvailability: true,
      windowsAvailability: true,
      macAvailability: true,
      linuxAvailability: false,
      enabledSegments: ['ALL'],
      debugScreen: ['ALL', 'staff'],
      support: ['ALL'],
      allowShorebirdPatchInstall: [],
      defaultThemeColor: 'theme1',
      defaultThemeMode: 'dark',
      maxPasscodeAttempts: 5,
      passcodeReAuthTimeout: 300000,
      modalBarrierOpacity: 70,
      colorsVersion: 38,
      showAds: true,
      adWaitTimeInSec: 5,
      noOfInteractionsForAds: 5,
      gracePeriodInMin: 43200,
      translationsVersion: 227,
      externalDescription: {
        'en': 'Hide features shared with you without afttecting others view',
        'de':
            'Mit Ihnen geteilte Funktionen verbergen, ohne die Ansicht anderer zu beeinträchtigen',
        'fr':
            'Masquer les fonctionnalités partagées avec vous sans affecter la vue des autres',
        'es':
            'Ocultar funciones compartidas contigo sin afectar la vista de los demás',
        'it':
            'Nascondi le funzionalità condivise con te senza compromettere la vista degli altri',
        'pt':
            'Ocultar recursos compartilhados com você sem detectar a visualização de outras pessoas',
      },
      cloudUpdatedAt: DateTime.parse('2025-07-15T00:20:25.174935'),
    ),
    Environment.prod: EnvironmentConfig(
      id: '5df00a99-c38d-4185-92ae-7d2c84f27928',
      dbVersion: 132,
      rid: 0.104,
      androidAvailability: true,
      iosAvailability: true,
      webAvailability: true,
      windowsAvailability: true,
      macAvailability: true,
      linuxAvailability: true,
      enabledSegments: ['ALL'],
      debugScreen: ['ALL', 'staff'],
      support: ['ALL'],
      allowShorebirdPatchInstall: [],
      defaultThemeColor: 'theme1',
      defaultThemeMode: 'dark',
      maxPasscodeAttempts: 5,
      passcodeReAuthTimeout: 300000,
      modalBarrierOpacity: 70,
      colorsVersion: 38,
      showAds: true,
      adWaitTimeInSec: 5,
      noOfInteractionsForAds: 7,
      gracePeriodInMin: 43200,
      translationsVersion: 227,
      externalDescription: {
        'en': 'Hide features shared with you without afttecting others view',
        'de':
            'Mit Ihnen geteilte Funktionen verbergen, ohne die Ansicht anderer zu beeinträchtigen',
        'fr':
            'Masquer les fonctionnalités partagées avec vous sans affecter la vue des autres',
        'es':
            'Ocultar funciones compartidas contigo sin afectar la vista de los demás',
        'it':
            'Nascondi le funzionalità condivise con te senza compromettere la vista degli altri',
        'pt':
            'Ocultar recursos compartilhados com você sem detectar a visualização de outras pessoas',
      },
      cloudUpdatedAt: DateTime.parse('2025-07-15T00:20:25.174940'),
    ),
    Environment.hotfix: EnvironmentConfig(
      id: '5df00a99-c38d-4185-92ae-7d2c84f27928',
      dbVersion: 132,
      rid: 0.104,
      androidAvailability: true,
      iosAvailability: true,
      webAvailability: true,
      windowsAvailability: true,
      macAvailability: true,
      linuxAvailability: true,
      enabledSegments: ['ALL'],
      debugScreen: ['staff'],
      support: ['ALL'],
      allowShorebirdPatchInstall: [],
      defaultThemeColor: 'theme1',
      defaultThemeMode: 'dark',
      maxPasscodeAttempts: 5,
      passcodeReAuthTimeout: 300000,
      modalBarrierOpacity: 70,
      colorsVersion: 38,
      showAds: true,
      adWaitTimeInSec: 5,
      noOfInteractionsForAds: 5,
      gracePeriodInMin: 43200,
      translationsVersion: 227,
      externalDescription: {
        'en': 'Hide features shared with you without afttecting others view',
        'de':
            'Mit Ihnen geteilte Funktionen verbergen, ohne die Ansicht anderer zu beeinträchtigen',
        'fr':
            'Masquer les fonctionnalités partagées avec vous sans affecter la vue des autres',
        'es':
            'Ocultar funciones compartidas contigo sin afectar la vista de los demás',
        'it':
            'Nascondi le funzionalità condivise con te senza compromettere la vista degli altri',
        'pt':
            'Ocultar recursos compartilhados com você sem detectar a visualização de outras pessoas',
      },
      cloudUpdatedAt: DateTime.parse('2025-07-15T00:20:25.174944'),
    ),
  };

  // Helper method to get current environment config
  EnvironmentConfig get currentConfig {
    return _environmentConfigs[_currentEnvironment] ??
        _environmentConfigs[Environment.dev]!;
  }

  // Update method that takes EnvironmentConfig directly, not ReleaseConfigModel
  void updateConfig(EnvironmentConfig config) {
    _environmentConfigs[_currentEnvironment] = config;
  }

  // Getters for all properties - they now return the current environment's values
  String get id => currentConfig.id;
  int get dbVersion => currentConfig.dbVersion;
  double get rid => currentConfig.rid;
  bool get androidAvailability => currentConfig.androidAvailability;
  bool get iosAvailability => currentConfig.iosAvailability;
  bool get webAvailability => currentConfig.webAvailability;
  bool get windowsAvailability => currentConfig.windowsAvailability;
  bool get macAvailability => currentConfig.macAvailability;
  bool get linuxAvailability => currentConfig.linuxAvailability;
  List<String> get enabledSegments => currentConfig.enabledSegments;
  List<String> get debugScreen => currentConfig.debugScreen;
  List<String> get support => currentConfig.support;
  List<String> get allowShorebirdPatchInstall =>
      currentConfig.allowShorebirdPatchInstall;
  String get defaultThemeColor => currentConfig.defaultThemeColor;
  String get defaultThemeMode => currentConfig.defaultThemeMode;
  int get maxPasscodeAttempts => currentConfig.maxPasscodeAttempts;
  int get modalBarrierOpacity => currentConfig.modalBarrierOpacity;
  int get passcodeReAuthTimeout => currentConfig.passcodeReAuthTimeout;
  int get colorsVersion => currentConfig.colorsVersion;
  bool get showAds => currentConfig.showAds;
  int get adWaitTimeInSec => currentConfig.adWaitTimeInSec;
  int get noOfInteractionsForAds => currentConfig.noOfInteractionsForAds;
  int get gracePeriodInMin => currentConfig.gracePeriodInMin;
  int get translationsVersion => currentConfig.translationsVersion;
  Map<String, dynamic> get externalDescription =>
      currentConfig.externalDescription;
  DateTime get cloudUpdatedAt => currentConfig.cloudUpdatedAt;
}
