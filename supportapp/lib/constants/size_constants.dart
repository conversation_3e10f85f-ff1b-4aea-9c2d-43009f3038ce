import 'package:flutter/material.dart';

abstract class SizeConstants {
  static const double choiceChipBorderRadius = 6;
  static const double choiceChipCircularBorderRadius = 20;

  static const double bottomSheetBorderRadius = 16;

  static const double dialogBorderRadius = 16;

  static const double appBarElevation = 0;
  static const double appBarTitleSpacing = 0;
  static const double appBarHeight = 60;
  static const double appBarHeightWithBottomTabs = 37;
  static const double appBarSecondaryHeight = 53;
  static const double tabBarHeight = 48;

  static const double drawerWidth = 211;

  static const double buttonRadius = 6;
  static const double buttonMaxHeight = 40;
  static const double buttonMinWidth = 73;

  static const double iconButtonIconContainerSize = 24;

  static const double presetRadius = 4;
  static const double presetMaxHeight = 35;
  static const double presetMinWidth = 174;

  static const EdgeInsets tabLabelPadding = EdgeInsets.fromLTRB(24, 8, 24, 0);

  // We use this to adjust the font size of all the font styles by a factor.
  static const double textFontSizeAdjuster = 1;
}
