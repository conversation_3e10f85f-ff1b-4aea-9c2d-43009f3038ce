import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolvesupport/constants/app_config.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/enums/delete_reason_type.dart';
import 'package:mevolvesupport/enums/issue_report_status.dart';
import 'package:mevolvesupport/enums/purchase/user_entitlements.dart';
import 'package:mevolvesupport/enums/snippet_metadata_type.dart';
import 'package:mevolvesupport/enums/snippet_status.dart';
import 'package:mevolvesupport/models/chat_message.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/chats_count.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/models/issue_report.dart';
import 'package:mevolvesupport/models/issue_reports_count.dart';
import 'package:mevolvesupport/models/segment.dart';
import 'package:mevolvesupport/models/snippet.dart';
import 'package:mevolvesupport/models/snippets_metadata.dart';
import 'package:mevolvesupport/models/snippets_metadata_filter.dart';
import 'package:mevolvesupport/models/subscription_transaction.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/models/user/feature_usage_info.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/providers/firebase_firestore.dart';
import 'package:mevolvesupport/providers/firebase_functions.dart';

class DatabaseRepository {
  DatabaseRepository({
    FirebaseFirestoreRepository? firebaseFirestoreRepository,
    FirebaseFunctionsRepository? firebaseFunctionsRepository,
  })  : _firebaseFirestoreRepository =
            firebaseFirestoreRepository ?? FirebaseFirestoreRepository(),
        _firebaseFunctionsRepository =
            firebaseFunctionsRepository ?? FirebaseFunctionsRepository();
  static int currentDbVersion = AppConfig.dbVersion;

  final FirebaseFirestoreRepository _firebaseFirestoreRepository;
  final FirebaseFunctionsRepository _firebaseFunctionsRepository;

  Future<void> useEmulator() async =>
      await _firebaseFirestoreRepository.useEmulator();

  Future<SupportUser> saveSupportUser({
    required User user,
  }) async {
    return await _firebaseFirestoreRepository.saveSupportUser(user: user);
  }

  Future<void> updateSupportUser({
    required SupportUser supportUser,
  }) async {
    return await _firebaseFirestoreRepository.updateSupportUser(
      supportUser: supportUser,
    );
  }

  Future<void> updateSuperSubscription({
    required String uid,
    required MeUserEntitlement subType,
  }) async {
    return await _firebaseFirestoreRepository.updateSuperSubscription(
      uid: uid,
      subType: subType,
    );
  }

  Stream<UserMetaData?> listenUser({required String uid}) {
    return _firebaseFirestoreRepository.listenUser(uid: uid);
  }

  Future<UserMetaData?> fetchUserData(String uid) async {
    return await _firebaseFirestoreRepository.fetchUserData(uid);
  }

  Future<ChatUser?> fetchChatUser(String uid) async {
    return await _firebaseFirestoreRepository.fetchChatUser(uid);
  }

  Future<String?> fetchUidFromEmail(String email) async {
    return await _firebaseFirestoreRepository.fetchUidFromEmail(email);
  }

  Future<String?> fetchEmailFromUid(String uid) async {
    return await _firebaseFirestoreRepository.fetchEmailFromUid(uid);
  }

  Future<String?> fetchUserSupportLanguage(String uid) async {
    return await _firebaseFirestoreRepository.fetchUserSupportLanguage(uid);
  }

  Stream<String?> listenUserSupportLanguage(String uid) {
    return _firebaseFirestoreRepository.listenUserSupportLanguage(uid);
  }

  Future<List<IssueReport>> fetchUserIssueReports(String uid) async {
    return await _firebaseFirestoreRepository.fetchUserIssueReports(uid);
  }

  Future<List<FeedbackModel>> fetchUserFeedbacks(String uid) async {
    return await _firebaseFirestoreRepository.fetchUserFeedbacks(uid);
  }

  Future<List<TransactionModel>> fetchUserTransactions(
    String uid,
  ) async {
    return await _firebaseFirestoreRepository.fetchUserTransactions(uid);
  }

  Future<List<DeleteReport>> fetchUserDeleteReports(String uid) async {
    return await _firebaseFirestoreRepository.fetchUserDeleteReports(uid);
  }

  Future<Map<String, dynamic>> fetchUsers({
    Map<String, dynamic> withFilters = const {},
    int limit = 10,
  }) async {
    return _firebaseFunctionsRepository.fetchFilterUsers(filters: withFilters);
  }

  /// NEW: Direct Firestore users fetching (goodbye BigQuery!)
  Stream<List<UserMetaData>> listenUsers({
    Map<String, dynamic> filters = const {},
    int limit = 50,
  }) {
    return _firebaseFirestoreRepository.listenUsers(
      filters: filters,
      limit: limit,
    );
  }

  Future<List<UserMetaData>> fetchUsersFromFirestore({
    Map<String, dynamic> filters = const {},
    int limit = 50,
    DateTime? startAfter,
  }) async {
    return _firebaseFirestoreRepository.fetchUsersFromFirestore(
      filters: filters,
      limit: limit,
      startAfter: startAfter,
    );
  }

  Future<int> fetchUsersCount({
    Map<String, dynamic> filters = const {},
    DateTime? startAfter,
  }) async {
    return _firebaseFirestoreRepository.fetchUsersCount(
      filters: filters,
      startAfter: startAfter,
    );
  }

  /// NEW: Real-time count listener for users
  Stream<int> listenUsersCount({
    Map<String, dynamic> filters = const {},
  }) {
    return _firebaseFirestoreRepository.listenUsersCount(
      filters: filters,
    );
  }

  Future<Map<String, dynamic>> translateToolMsg({
    Map<String, dynamic> data = const {},
  }) async {
    return _firebaseFunctionsRepository.translateToolMsg(data: data);
  }

  Future<List<ChatUser>> fetchChats({
    required ChatStatus status,
    DateTime? lastLocalDateTime,
    DateTime? lastCloudDateTime,
    int limit = 10,
    String? byFilter,
  }) async {
    // Convert 'all' to null for Firestore repository
    final filterToPass = (byFilter == 'all') ? null : byFilter;

    return await _firebaseFirestoreRepository.fetchChats(
      status: status,
      lastLocalDateTime: lastLocalDateTime,
      lastCloudDateTime: lastCloudDateTime,
      limit: limit,
      byFilter: filterToPass,
    );
  }

  Stream<List<ChatUser>> listenChatUpdates({
    required ChatStatus status,
    int count = 0,
    String? byFilter,
  }) {
    // Convert 'all' to null for Firestore repository
    final filterToPass = (byFilter == 'all') ? null : byFilter;

    return _firebaseFirestoreRepository.listenChatUpdates(
      status: status,
      count: count,
      byFilter: filterToPass,
    );
  }

  /// Listen for updates to specific chat UIDs
  Stream<List<ChatUser>> listenChatUpdatesByUIDs(List<String> uids) {
    return _firebaseFirestoreRepository.listenChatUpdatesByUIDs(uids);
  }

  /// Listen for updates to specific user UIDs
  Stream<List<UserMetaData>> listenUserUpdatesByUIDs(List<String> uids) {
    return _firebaseFirestoreRepository.listenUserUpdatesByUIDs(uids);
  }

  Future<List<ChatMessage>> fetchMessages({
    required String uid,
    DateTime? lastMessageCloudTime,
    DateTime? lastMessageLocalTime,
  }) async {
    return await _firebaseFirestoreRepository.fetchMessages(
      uid: uid,
      lastMessageCloudTime: lastMessageCloudTime,
      lastMessageLocalTime: lastMessageLocalTime,
    );
  }

  Stream<List<ChatMessage>> listenMessages({
    required String uid,
    DateTime? firstMessageCloudTime,
    DateTime? firstMessageLocalTime,
  }) {
    return _firebaseFirestoreRepository.listenMessages(
      uid: uid,
      firstMessageCloudTime: firstMessageCloudTime,
      firstMessageLocalTime: firstMessageLocalTime,
    );
  }

  Future<bool> sendMessage({
    required ChatUser chatUser,
    required ChatMessage chatMessage,
    bool updateCounts = true,
  }) async {
    return await _firebaseFirestoreRepository.sendMessage(
      chatUser: chatUser,
      chatMessage: chatMessage,
      updateCounts: updateCounts,
    );
  }

  Future<bool> updateSupportStatus({
    required ChatUser chatUser,
    bool isClarifySwitch = false,
    bool updateCounts = true,
  }) async {
    return await _firebaseFirestoreRepository.updateSupportStatus(
      chatUser: chatUser,
      isClarifySwitch: isClarifySwitch,
      updateCounts: updateCounts,
    );
  }

  Stream<ChatsCount> listenChatsCount() {
    return _firebaseFirestoreRepository.listenChatsCount();
  }

  Future<ChatsCount> fetchChatsCount() async {
    return await _firebaseFirestoreRepository.fetchChatsCount();
  }

  /// Get filtered count for specific chat status and support person filter
  Future<int> fetchFilteredChatsCount({
    required ChatStatus status,
    String? byFilter,
  }) async {
    // Convert 'all' to null for Firestore repository
    final filterToPass = (byFilter == 'all') ? null : byFilter;

    return await _firebaseFirestoreRepository.fetchFilteredChatsCount(
      status: status,
      byFilter: filterToPass,
    );
  }

  /// Listen to filtered count for specific chat status and support person filter
  Stream<int> listenFilteredChatsCount({
    required ChatStatus status,
    String? byFilter,
  }) {
    // Convert 'all' to null for Firestore repository
    final filterToPass = (byFilter == 'all') ? null : byFilter;

    return _firebaseFirestoreRepository.listenFilteredChatsCount(
      status: status,
      byFilter: filterToPass,
    );
  }

  Stream<ChatUser?> listenChatBlock({
    required String uid,
  }) {
    return _firebaseFirestoreRepository.listenChatBlock(uid: uid);
  }

  Stream<ChatMessage?> listenMessageBlock({
    required String id,
  }) {
    return _firebaseFirestoreRepository.listenMessageBlock(id: id);
  }

  Future<int> updateSupportTabCount() async {
    return await _firebaseFirestoreRepository.updateSupportTabCount();
  }

  Future<int> updateIssueReportsTabCount() async {
    return await _firebaseFirestoreRepository.updateIssueReportsTabCount();
  }

  Future<List<FeedbackModel>> fetchFeedbacks({
    DateTime? lastReportedDateTime,
    List<String>? byFilter,
    int limit = 10,
    DateTime? lastCloudDateTime,
    DateTime? lastLocalDateTime,
  }) async {
    return await _firebaseFirestoreRepository.fetchFeedbacks(
      lastReportedDateTime: lastReportedDateTime,
      byFilter: byFilter,
      limit: limit,
      lastLocalDateTime: lastLocalDateTime,
    );
  }

  Stream<List<FeedbackModel>> listenFeedbacks({
    int count = 0,
    List<String>? byFilter,
  }) {
    return _firebaseFirestoreRepository.listenFeedbacks(
      count: count,
      byFilter: byFilter,
    );
  }

  Stream<List<FeedbackModel>> listenFeedbackUpdatesByIDs(List<String> ids) {
    return _firebaseFirestoreRepository.listenFeedbackUpdatesByIDs(ids);
  }

  Stream<List<DeleteReport>> listenDeleteReportUpdatesByIDs(
    List<String> ids,
  ) {
    return _firebaseFirestoreRepository.listenDeleteReportUpdatesByIDs(ids);
  }

  Future<List<IssueReport>> fetchIssueReports({
    DateTime? lastReportedDateTime,
    IssueReportStatus? reportStatus,
    String? byFilter,
    int limit = 10,
    DateTime? lastCloudDateTime,
    DateTime? lastLocalDateTime,
  }) async {
    // Convert 'all' to null for Firestore repository
    final filterToPass = (byFilter == 'all') ? null : byFilter;

    return await _firebaseFirestoreRepository.fetchIssueReports(
      lastReportedDateTime: lastReportedDateTime,
      reportStatus: reportStatus,
      byFilter: filterToPass,
      limit: limit,
      lastCloudDateTime: lastCloudDateTime,
      lastLocalDateTime: lastLocalDateTime,
    );
  }

  Stream<List<IssueReport>> listenIssueReports({
    int count = 0,
    IssueReportStatus? reportStatus,
    String? byFilter,
  }) {
    // Convert 'all' to null for Firestore repository
    final filterToPass = (byFilter == 'all') ? null : byFilter;

    return _firebaseFirestoreRepository.listenIssueReports(
      count: count,
      reportStatus: reportStatus,
      byFilter: filterToPass,
    );
  }

  Stream<IssueReportsCount> listenIssueReportsCount() {
    return _firebaseFirestoreRepository.listenIssueReportsCount();
  }

  Future<IssueReportsCount> fetchIssueReportsCount() async {
    return await _firebaseFirestoreRepository.fetchIssueReportsCount();
  }

  Future<bool> updateIssueReportStatus({
    required IssueReport issueReport,
  }) async {
    return await _firebaseFirestoreRepository.updateIssueReportStatus(
      issueReport: issueReport,
    );
  }

  Stream<IssueReport?> listenIssueReportBlock({
    required String id,
  }) {
    return _firebaseFirestoreRepository.listenIssueReportBlock(id: id);
  }

  Future<bool> updateFeedbacktatus({
    required FeedbackModel feedback,
  }) async {
    return await _firebaseFirestoreRepository.updateFeedbacktatus(
      feedback: feedback,
    );
  }

  Stream<FeedbackModel?> listenFeedbackBlock({
    required String id,
  }) {
    return _firebaseFirestoreRepository.listenFeedbackBlock(id: id);
  }

  Stream<IssueReport?> listenReportNotReviewed({
    required String uid,
  }) {
    return _firebaseFirestoreRepository.listenReportNotReviewed(uid: uid);
  }

  Future<List<DeleteReport>> fetchDeleteReports({
    DateTime? lastReportDateTime,
    DeletedReasonType? deletedReasonType,
  }) async {
    return await _firebaseFirestoreRepository.fetchDeleteReports(
      lastReportDateTime: lastReportDateTime,
      deletedReasonType: deletedReasonType,
    );
  }

  Stream<List<DeleteReport>> listenDeleteReports({
    int count = 0,
    DateTime? lastReportDateTime,
    DeletedReasonType? deletedReasonType,
  }) {
    return _firebaseFirestoreRepository.listenDeleteReports(
      count: count,
      lastReportDateTime: lastReportDateTime,
      deletedReasonType: deletedReasonType,
    );
  }

  Future<int> fetchDeleteReportsCount({
    DeletedReasonType? deletedReasonType,
  }) async {
    return await _firebaseFirestoreRepository.fetchDeleteReportsCount(
      deletedReasonType: deletedReasonType,
    );
  }

  /// Real-time listener for user-specific deleted reports
  Stream<List<DeleteReport>> listenUserDeleteReports({required String uid}) {
    return _firebaseFirestoreRepository.listenUserDeleteReports(uid: uid);
  }

  /// Real-time listener for user-specific feedbacks
  Stream<List<FeedbackModel>> listenUserFeedbacks({required String uid}) {
    return _firebaseFirestoreRepository.listenUserFeedbacks(uid: uid);
  }

  /// Real-time listener for user-specific payment transactions
  Stream<List<TransactionModel>> listenUserTransactions({required String uid}) {
    return _firebaseFirestoreRepository.listenUserTransactions(uid: uid);
  }

  Stream<int> listenFeedbacksCount() {
    return _firebaseFirestoreRepository.listenFeedbacksCount();
  }

  Future<int> fetchFeedbacksCount({
    List<String>? byFilter,
  }) async {
    return await _firebaseFirestoreRepository.fetchFeedbacksCount(
      byFilter: byFilter,
    );
  }

  Future<FeatureUsageInfo> fetchFeaturesCount(String uid) async {
    return await _firebaseFirestoreRepository.fetchFeaturesCount(uid);
  }

  Future<List<Snippet>> fetchAllSnippets() async {
    return await _firebaseFirestoreRepository.fetchAllSnippets();
  }

  Stream<List<Snippet>> listenSnippets({
    required SnippetStatus status,
    DateTime? lastDateTime,
    String? byFilter,
  }) {
    // Convert 'all' to null for Firestore repository
    final filterToPass = (byFilter == 'all') ? null : byFilter;

    return _firebaseFirestoreRepository.listenSnippets(
      status: status,
      lastDateTime: lastDateTime,
      byFilter: filterToPass,
    );
  }

  Future<List<SnippetsMetadata>> fetchAllSnippetsMetadata() async {
    return await _firebaseFirestoreRepository.fetchAllSnippetsMetadata();
  }

  Stream<List<SnippetsMetadata>> listenSnippetsMetadata({
    required SnippetMetadataType type,
    DateTime? lastDateTime,
    SnippetsMetadataFilter? byFilter,
  }) {
    return _firebaseFirestoreRepository.listenSnippetsMetadata(
      type: type,
      lastDateTime: lastDateTime,
      byFilter: byFilter,
    );
  }

  Future<int> updateSupportPerms({
    required Map<String, dynamic> supportUserMap,
  }) async {
    return _firebaseFunctionsRepository.updateSupportPerms(
      supportUserMap: supportUserMap,
    );
  }

  Future<Map<String, dynamic>> getDbSchema() async {
    return _firebaseFunctionsRepository.getDbSchema();
  }

  /// Segment-related methods
  Future<List<Segment>> fetchSegments({
    String? searchQuery,
    DateTime? lastCreatedDateTime,
    int limit = 20,
  }) async {
    return await _firebaseFirestoreRepository.fetchSegments(
      searchQuery: searchQuery,
      lastCreatedDateTime: lastCreatedDateTime,
      limit: limit,
    );
  }

  Future<int> fetchSegmentsCount({
    String? searchQuery,
  }) async {
    return await _firebaseFirestoreRepository.fetchSegmentsCount(
      searchQuery: searchQuery,
    );
  }

  Stream<List<Segment>> listenSegments({
    String? searchQuery,
    int count = 20,
  }) {
    return _firebaseFirestoreRepository.listenSegments(
      searchQuery: searchQuery,
      count: count,
    );
  }

  Stream<List<Segment>> listenSegmentUpdatesByIDs(List<String> ids) {
    return _firebaseFirestoreRepository.listenSegmentUpdatesByIDs(ids);
  }

  Stream<Segment?> listenSegment({required String id}) {
    return _firebaseFirestoreRepository.listenSegment(id: id);
  }

  Stream<List<SegmentActivity>> listenSegmentActivities({
    required String segmentId,
  }) {
    return _firebaseFirestoreRepository.listenSegmentActivities(
      segmentId: segmentId,
    );
  }

  Future<int> refreshSegmentUserCount({
    required String segmentId,
    required String segmentName,
  }) async {
    return await _firebaseFirestoreRepository.refreshSegmentUserCount(
      segmentId: segmentId,
      segmentName: segmentName,
    );
  }

  Future<void> createSegment({
    required String id,
    required String name,
    required String purpose,
    required String createdBy,
    int initialUserCount = 0,
  }) async {
    return await _firebaseFirestoreRepository.createSegment(
      id: id,
      name: name,
      purpose: purpose,
      createdBy: createdBy,
      initialUserCount: initialUserCount,
    );
  }

  Future<void> createSegmentActivity({
    required String id,
    required String userSegmentId,
    required String activityTitle,
    required String csvPath,
    required int csvUserCount,
    required int updatedUserCount,
    required String createdBy,
    required String activityType,
  }) async {
    return await _firebaseFirestoreRepository.createSegmentActivity(
      id: id,
      userSegmentId: userSegmentId,
      activityTitle: activityTitle,
      csvPath: csvPath,
      csvUserCount: csvUserCount,
      updatedUserCount: updatedUserCount,
      createdBy: createdBy,
      activityType: activityType,
    );
  }

  Future<void> updateSegment({
    required String id,
    required String name,
    required String purpose,
    required String updatedBy,
  }) async {
    return await _firebaseFirestoreRepository.updateSegment(
      id: id,
      name: name,
      purpose: purpose,
      updatedBy: updatedBy,
    );
  }

  Future<void> deleteSegment({
    required String id,
    required String updatedBy,
  }) async {
    return await _firebaseFirestoreRepository.deleteSegment(
      id: id,
      updatedBy: updatedBy,
    );
  }

  Future<int> addUsersToSegment({
    required String segmentName,
    required List<String> userIds,
  }) async {
    return await _firebaseFirestoreRepository.addUsersToSegment(
      segmentName: segmentName,
      userIds: userIds,
    );
  }

  Future<int> removeUsersFromSegment({
    required String segmentName,
    required List<String> userIds,
  }) async {
    return await _firebaseFirestoreRepository.removeUsersFromSegment(
      segmentName: segmentName,
      userIds: userIds,
    );
  }

  Future<void> updateSegmentNameInUsers({
    required String oldName,
    required String newName,
  }) async {
    return await _firebaseFirestoreRepository.updateSegmentNameInUsers(
      oldName: oldName,
      newName: newName,
    );
  }

  Future<void> removeSegmentFromAllUsers({
    required String segmentName,
  }) async {
    return await _firebaseFirestoreRepository.removeSegmentFromAllUsers(
      segmentName: segmentName,
    );
  }

  Future<List<SupportUser>> fetchSupportUsers({
    String? searchQuery,
    DateTime? lastLoginDateTime,
    int limit = 10,
  }) async {
    return await _firebaseFirestoreRepository.fetchSupportUsers(
      searchQuery: searchQuery,
      lastLoginDateTime: lastLoginDateTime,
      limit: limit,
    );
  }

  Future<int> fetchSupportUsersCount({
    String? searchQuery,
  }) async {
    return await _firebaseFirestoreRepository.fetchSupportUsersCount(
      searchQuery: searchQuery,
    );
  }

  Stream<List<SupportUser>> listenSupportUsers({
    String? searchQuery,
    int count = 10,
  }) {
    return _firebaseFirestoreRepository.listenSupportUsers(
      searchQuery: searchQuery,
      count: count,
    );
  }

  Stream<List<SupportUser>> listenSupportUserUpdatesByIDs(List<String> ids) {
    return _firebaseFirestoreRepository.listenSupportUserUpdatesByIDs(ids);
  }
}
