import 'dart:convert';

import 'package:mevolvesupport/models/activity.dart';
import 'package:mevolvesupport/providers/firebase_functions.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

/// Repository for business logic operations that use Firebase Functions
class FunctionsRepository {
  FunctionsRepository({
    FirebaseFunctionsRepository? firebaseFunctionsRepository,
  }) : _firebaseFunctionsRepository =
            firebaseFunctionsRepository ?? FirebaseFunctionsRepository();

  final FirebaseFunctionsRepository _firebaseFunctionsRepository;

  /// Fetch user activities with optional filters and pagination
  Future<List<ActivityItem>> fetchUserActivities({
    required String userId,
    String? eventName,
    String? eventType,
    DateTime? startFrom,
    String? startAfter,
    String? startAfterTime,
    int? skip,
  }) async {
    // Build parameters for the function call
    final Map<String, dynamic> params = {'userId': userId};

    Log.d(
      'BigQueryAnalytics: 🔍 FUNCTION CALL - About to call Firebase Function for analytics data',
    );
    Log.d(
      'BigQueryAnalytics: 📋 Parameters: userId=$userId, eventName=$eventName, eventType=$eventType, startFrom=$startFrom',
    );

    if (eventName != null && eventName.isNotEmpty) {
      params['eventName'] = eventName;
    }

    if (eventType != null) {
      params['eventType'] = eventType;
    }

    if (startFrom != null) {
      params['startFrom'] = startFrom.toUtc().toIso8601String();
    }

    if (startAfter != null) {
      params['startAfter'] = startAfter;
    }

    if (startAfterTime != null) {
      params['startAfterTime'] = startAfterTime;
    }

    if (skip != null) {
      params['skip'] = skip.toString();
    }

    // Call the Firebase Function
    final functionName =
        'v${DatabaseRepository.currentDbVersion}-supportapp-fetchUserActivities';

    Log.d(
      'BigQueryAnalytics: 🚀 CALLING FUNCTION - $functionName with params: $params',
    );
    final callStartTime = DateTime.now();

    final response = await _firebaseFunctionsRepository.callFunction(
      functionName: functionName,
      parameters: params,
    );

    final callDuration = DateTime.now().difference(callStartTime);
    Log.d(
      'BigQueryAnalytics: ✅ FUNCTION RESPONSE - Call completed in ${callDuration.inMilliseconds}ms, status: ${response['statusCode']}',
    );

    if (response['statusCode'] == 200) {
      try {
        final data = List.from(jsonDecode(response['body']));
        Log.d(
          'BigQueryAnalytics: 📊 PARSING RESPONSE - Processing ${data.length} activity records from analytics',
        );

        // Convert raw data to ActivityItem objects
        final activities = data.map((item) {
          // Handle new analytics format where event_timestamp is an object with 'value'
          DateTime eventTime;
          dynamic eventTimestamp = item['event_timestamp'];

          if (eventTimestamp is Map && eventTimestamp.containsKey('value')) {
            // New format: {"value": "2025-07-08T11:41:09.070Z"}
            final timestampStr = eventTimestamp['value'] as String;
            eventTime = DateTime.parse(timestampStr).toLocal();
            Log.d(
              'BigQueryAnalytics: 📅 New timestamp format detected: $timestampStr',
            );
          } else if (eventTimestamp is int) {
            // Old format: microseconds since epoch
            eventTime =
                DateTime.fromMicrosecondsSinceEpoch(eventTimestamp, isUtc: true)
                    .toLocal();
            Log.d(
              'BigQueryAnalytics: 📅 Old timestamp format detected: $eventTimestamp',
            );
          } else if (eventTimestamp is String) {
            // String format
            eventTime = DateTime.parse(eventTimestamp).toLocal();
            Log.d(
              'BigQueryAnalytics: 📅 String timestamp format detected: $eventTimestamp',
            );
          } else {
            // Fallback to current time
            eventTime = DateTime.now();
            Log.e(
              'BigQueryAnalytics: ⚠️  Unknown timestamp format: $eventTimestamp, using current time',
            );
          }

          final String eventName = item['event_name'] ?? 'Unnamed Event';
          final dynamic params = item['event_params'] ?? [];

          // Parse event_params if it's a JSON string
          dynamic parsedParams = params;
          if (params is String && params.isNotEmpty) {
            try {
              parsedParams = jsonDecode(params);
              Log.d('BigQueryAnalytics: 📋 Parsed event_params JSON string');
            } catch (e) {
              Log.d(
                'BigQueryAnalytics: ⚠️  Could not parse event_params as JSON: $e',
              );
              parsedParams = params; // Keep as string if parsing fails
            }
          }

          // Create clean rawMap for JSON viewer with readable timestamp
          final Map<String, dynamic> rawMap = {
            'event_timestamp': eventTime.toUtc().toIso8601String(),
            // Clean ISO timestamp
            'event_name': eventName,
            'event_params': parsedParams,
            // Parsed object instead of JSON string
            'event_date': item['event_date'],
            // Include event_date if available
          };

          return ActivityItem(
            eventName: eventName,
            eventTime: eventTime,
            rawData: rawMap,
          );
        }).toList();

        Log.d(
          'BigQueryAnalytics: ✅ SUCCESS - Returning ${activities.length} parsed activities to UI',
        );
        return activities;
      } catch (e) {
        Log.e(
          'BigQueryAnalytics: ❌ PARSING ERROR - Failed to parse analytics response: $e',
        );
        throw Exception('Error parsing activities response: $e');
      }
    } else {
      Log.e(
        'BigQueryAnalytics: ❌ FUNCTION ERROR - Analytics function failed with status ${response['statusCode']}: ${response['body']}',
      );
      throw Exception(
        'Failed to fetch activities: ${response['statusCode']} - ${response['body']}',
      );
    }
  }

  /// Translate tool messages
  Future<Map<String, dynamic>> translateToolMsg({
    Map<String, dynamic> data = const {},
  }) async {
    return _firebaseFunctionsRepository.translateToolMsg(data: data);
  }

  /// Add other function calls as needed
}
