import 'package:flutter/foundation.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/providers/firebase_storage.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

class StorageRepository {
  StorageRepository({
    FirebaseStorageRepository? firebaseStorageRepository,
    required this.databaseRepository,
  }) : _firebaseStorageRepository =
            firebaseStorageRepository ?? FirebaseStorageRepository();

  final FirebaseStorageRepository _firebaseStorageRepository;
  final DatabaseRepository databaseRepository;

  Future<void> useEmulator() => _firebaseStorageRepository.useEmulator();

  Future<bool> uploadImage({
    required String id,
    required String imagePath,
    required Uint8List imageBytes,
    required String userId,
    required FirebaseDocCollectionType docType,
  }) async {
    try {
      return FirebaseStorageRepository().uploadFile(
        filePath: imagePath,
        userId: userId,
        documentId: id,
        documentType: docType,
        fileBytes: imageBytes,
      );
    } catch (ex) {
      Log.e(ex);
      return false;
    }
  }
}
