// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_dev.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyBlAHaW-E_2f7qePc7t8I7qbRPRbUiOoe0',
    appId: '1:825137847857:web:369c2a41142c32b9ff3f10',
    messagingSenderId: '825137847857',
    projectId: 'mevolve-dev',
    authDomain: 'mevolve-dev.firebaseapp.com',
    databaseURL: 'https://mevolve-dev-default-rtdb.firebaseio.com',
    storageBucket: 'mevolve-dev.appspot.com',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBTt4Lmz5d38Vjsr_olewzgPry8lTfgHcU',
    appId: '1:825137847857:android:736b0a55fb53a918ff3f10',
    messagingSenderId: '825137847857',
    projectId: 'mevolve-dev',
    databaseURL: 'https://mevolve-dev-default-rtdb.firebaseio.com',
    storageBucket: 'mevolve-dev.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBWNp4ReHqiszAAuEBAxP3T7_R5c9rixdA',
    appId: '1:825137847857:ios:efefbdcd3be96949ff3f10',
    messagingSenderId: '825137847857',
    projectId: 'mevolve-dev',
    databaseURL: 'https://mevolve-dev-default-rtdb.firebaseio.com',
    storageBucket: 'mevolve-dev.appspot.com',
    androidClientId:
        '825137847857-0j73f4569eb7fl4tglsha4beohhem6h6.apps.googleusercontent.com',
    iosClientId:
        '825137847857-ojl62uop2rhc5iqq0o0h5dqeuublmetd.apps.googleusercontent.com',
    iosBundleId: 'app.mevolve.support.dev',
  );
}
