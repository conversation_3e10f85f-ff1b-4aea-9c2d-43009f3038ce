// ignore_for_file: constant_identifier_names

import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mevolvesupport/constants/size_constants.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';

/// Note: Before adding any new font style here confirm that from the text
/// guideline sheet in figma. If the font style is not there then provide
/// the Design team the figma link to where it is used. During that period we
/// can use the none font style. And when they update the text guideline sheet
/// with the new font style then replace the none font style with the new font
/// style at those places.
enum MeFontStyle {
  // For A base font style
  A1,
  A2,
  A5,
  A7,
  A8,
  A12,
  A35,

  // For B base font style
  B1,
  B2,
  B7,
  B8,
  B10,
  B11,
  B12,
  B14,

  // For C base font style
  C1,
  C2,
  C4,
  C5,
  C7,
  C8,
  C8Title,
  C10,
  C11,
  C12,
  C14,
  C27, // <-- Missing report to design team to add in guideline https://www.figma.com/file/M23KJz70JjGCLX7pckt9FM/Master---Today-%26-Future?type=design&node-id=2%3A16368&mode=design&t=VveKEtwFE8gcbbFl-1

  // For D base font style
  D1,
  D2, // <-- Missing report to design team to add in guideline https://www.figma.com/file/OXrvX719TWsI6CM58b5RGO/Master---Setup-%26-Actions?type=design&node-id=3%3A17311&mode=design&t=Q1oiNOYH2xnUBipX-1
  D3,
  D5,
  D7,
  D8,
  D10,
  D11,
  D12,
  D23,

  // For E base font style
  E1,
  E2,
  E5,
  E7,
  E8,
  E11,
  E12,

  // For F base font style
  F1,
  F2,
  F4,
  F5,
  F7,
  F8,
  F10,
  F11,
  F12,
  F14,

  // For G base font style
  G1,
  G5,
  G8,
  G11,
  G12,
  G14,

  // For H base font style
  H1,
  H7,
  H8,
  H12,
  H22,

  // For I base font style
  I1,
  I2, // <-- Missing report to design team to add in guideline https://www.figma.com/file/OXrvX719TWsI6CM58b5RGO/Master---Setup-%26-Actions?type=design&node-id=3%3A16471&mode=design&t=62jeJWW5xqHa8Epr-1
  I3,
  I5,
  I7,
  I8,
  I11,
  I12,
  I14,

  // For J base font style
  J1,
  J7,
  J8,
  J9,
  J11,

  // For L base font style
  L2,
  L12,

  // For N base font style
  N7,
  N8,
  N12,

  // For O base font style
  O8,

  // For P base font style
  P8,
  P10,

  // For Q base font style
  Q8,

  // For R base font style
  R1,
  R8,
  R12,

  // For S base font style
  S8,

  // For no matching base font style and we will use it to search for the
  // missing font styles periodically.
  none,
}

class MeTextTheme {
  const MeTextTheme({required this.fontSize, required this.fontWeight});

  final double fontSize;
  final FontWeight fontWeight;

  TextStyle get _style => GoogleFonts.roboto(
        fontSize: fontSize,
        fontWeight: fontWeight,
      );

  static TextStyle fromFigma({
    required TextStyle textStyle,
    required Color color,
    double? height,
  }) {
    // This is to adjust the font size from figma to a adjusted font size.
    double? fontSize = textStyle.fontSize == null
        ? null
        : textStyle.fontSize! * SizeConstants.textFontSizeAdjuster;
    // This is to convert the figma line height to flutter line height.
    double? flutterLineHeight =
        fontSize == null || height == null ? null : height / fontSize;
    return textStyle.copyWith(
      color: color,
      height: flutterLineHeight,
      fontSize: fontSize,
      // This is to center the text vertically in the line height.
      leadingDistribution: TextLeadingDistribution.even,
    );
  }

  static TextStyle A =
      const MeTextTheme(fontSize: 18, fontWeight: FontWeight.w500)._style;
  static TextStyle B =
      const MeTextTheme(fontSize: 16, fontWeight: FontWeight.w500)._style;
  static TextStyle C =
      const MeTextTheme(fontSize: 16, fontWeight: FontWeight.w400)._style;
  static TextStyle D =
      const MeTextTheme(fontSize: 14, fontWeight: FontWeight.w500)._style;
  static TextStyle E =
      const MeTextTheme(fontSize: 14, fontWeight: FontWeight.w400)._style;
  static TextStyle F =
      const MeTextTheme(fontSize: 12, fontWeight: FontWeight.w400)._style;
  static TextStyle G =
      const MeTextTheme(fontSize: 10, fontWeight: FontWeight.w400)._style;
  static TextStyle H =
      const MeTextTheme(fontSize: 14, fontWeight: FontWeight.w500)._style;
  static TextStyle I =
      const MeTextTheme(fontSize: 12, fontWeight: FontWeight.w400)._style;
  static TextStyle J =
      const MeTextTheme(fontSize: 10, fontWeight: FontWeight.w500)._style;
  static TextStyle L =
      const MeTextTheme(fontSize: 14, fontWeight: FontWeight.w500)._style;
  static TextStyle N =
      const MeTextTheme(fontSize: 10, fontWeight: FontWeight.normal)._style;
  static TextStyle O =
      const MeTextTheme(fontSize: 20, fontWeight: FontWeight.w500)._style;
  static TextStyle P =
      const MeTextTheme(fontSize: 32, fontWeight: FontWeight.normal)._style;
  static TextStyle Q =
      const MeTextTheme(fontSize: 24, fontWeight: FontWeight.normal)._style;
  static TextStyle R =
      const MeTextTheme(fontSize: 24, fontWeight: FontWeight.w500)._style;
  static TextStyle S =
      const MeTextTheme(fontSize: 8, fontWeight: FontWeight.normal)._style;

  static TextStyle getMeFontStyle({
    required MeFontStyle fontStyle,
    required BuildContext context,
    double? height,
  }) {
    MeColorScheme colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    switch (fontStyle) {
      // For A base font style
      case MeFontStyle.A1:
        return fromFigma(textStyle: A, color: colorScheme.color1);
      case MeFontStyle.A2:
        return fromFigma(textStyle: A, color: colorScheme.color2);
      case MeFontStyle.A5:
        return fromFigma(textStyle: A, color: colorScheme.color5);
      case MeFontStyle.A7:
        return fromFigma(textStyle: A, color: colorScheme.color7);
      case MeFontStyle.A8:
        return fromFigma(textStyle: A, color: colorScheme.color8);
      case MeFontStyle.A12:
        return fromFigma(textStyle: A, color: colorScheme.color12);
      case MeFontStyle.A35:
        return fromFigma(textStyle: A, color: colorScheme.color35);

      // For B base font style
      case MeFontStyle.B1:
        return fromFigma(textStyle: B, color: colorScheme.color1);
      case MeFontStyle.B2:
        return fromFigma(
          textStyle: B,
          color: colorScheme.color2,
          height: height,
        );
      case MeFontStyle.B7:
        return fromFigma(textStyle: B, color: colorScheme.color7);
      case MeFontStyle.B8:
        return fromFigma(
          textStyle: B,
          color: colorScheme.color8,
          height: height,
        );
      case MeFontStyle.B10:
        return fromFigma(textStyle: B, color: colorScheme.color10);
      case MeFontStyle.B11:
        return fromFigma(textStyle: B, color: colorScheme.color11);
      case MeFontStyle.B12:
        return fromFigma(textStyle: B, color: colorScheme.color12);
      case MeFontStyle.B14:
        return fromFigma(textStyle: B, color: colorScheme.color14);

      // For C base font style
      case MeFontStyle.C1:
        return fromFigma(textStyle: C, color: colorScheme.color1);
      case MeFontStyle.C2:
        return fromFigma(textStyle: C, color: colorScheme.color2);
      case MeFontStyle.C4:
        return fromFigma(textStyle: C, color: colorScheme.color4);
      case MeFontStyle.C5:
        return fromFigma(textStyle: C, color: colorScheme.color5);
      case MeFontStyle.C7:
        return fromFigma(textStyle: C, color: colorScheme.color7);
      case MeFontStyle.C8:
        return fromFigma(textStyle: C, color: colorScheme.color8);
      case MeFontStyle.C8Title:
        return fromFigma(textStyle: C, color: colorScheme.color8);
      case MeFontStyle.C10:
        return fromFigma(textStyle: C, color: colorScheme.color10);
      case MeFontStyle.C11:
        return fromFigma(textStyle: C, color: colorScheme.color11);
      case MeFontStyle.C12:
        return fromFigma(textStyle: C, color: colorScheme.color12);
      case MeFontStyle.C14:
        return fromFigma(textStyle: C, color: colorScheme.color14);
      case MeFontStyle.C27:
        return fromFigma(textStyle: C, color: colorScheme.color27);

      // For D base font style
      case MeFontStyle.D1:
        return fromFigma(textStyle: D, color: colorScheme.color1);
      case MeFontStyle.D2:
        return fromFigma(textStyle: D, color: colorScheme.color2);
      case MeFontStyle.D5:
        return fromFigma(textStyle: D, color: colorScheme.color5);
      case MeFontStyle.D7:
        return fromFigma(textStyle: D, color: colorScheme.color7);
      case MeFontStyle.D8:
        return fromFigma(textStyle: D, color: colorScheme.color8);
      case MeFontStyle.D10:
        return fromFigma(textStyle: D, color: colorScheme.color10);
      case MeFontStyle.D11:
        return fromFigma(textStyle: D, color: colorScheme.color11);
      case MeFontStyle.D12:
        return fromFigma(textStyle: D, color: colorScheme.color12);
      case MeFontStyle.D23:
        return fromFigma(textStyle: D, color: colorScheme.color23);

      // For E base font style
      case MeFontStyle.E1:
        return fromFigma(textStyle: E, color: colorScheme.color1);
      case MeFontStyle.E2:
        return fromFigma(textStyle: E, color: colorScheme.color2);
      case MeFontStyle.E5:
        return fromFigma(textStyle: E, color: colorScheme.color5);
      case MeFontStyle.E7:
        return fromFigma(textStyle: E, color: colorScheme.color7);
      case MeFontStyle.E8:
        return fromFigma(textStyle: E, color: colorScheme.color8);
      case MeFontStyle.E11:
        return fromFigma(textStyle: E, color: colorScheme.color11);
      case MeFontStyle.E12:
        return fromFigma(textStyle: E, color: colorScheme.color12);

      // For F base font style
      case MeFontStyle.F1:
        return fromFigma(textStyle: F, color: colorScheme.color1);
      case MeFontStyle.F2:
        return fromFigma(textStyle: F, color: colorScheme.color2);
      case MeFontStyle.F4:
        return fromFigma(textStyle: F, color: colorScheme.color4);
      case MeFontStyle.F5:
        return fromFigma(textStyle: F, color: colorScheme.color5);
      case MeFontStyle.F7:
        return fromFigma(textStyle: F, color: colorScheme.color7);
      case MeFontStyle.F8:
        return fromFigma(textStyle: F, color: colorScheme.color8);
      case MeFontStyle.F10:
        return fromFigma(textStyle: F, color: colorScheme.color10);
      case MeFontStyle.F11:
        return fromFigma(textStyle: F, color: colorScheme.color11);
      case MeFontStyle.F12:
        return fromFigma(textStyle: F, color: colorScheme.color12);
      case MeFontStyle.F14:
        return fromFigma(textStyle: F, color: colorScheme.color14);

      // For g base font style
      case MeFontStyle.G1:
        return fromFigma(textStyle: G, color: colorScheme.color1);
      case MeFontStyle.G5:
        return fromFigma(textStyle: G, color: colorScheme.color5);
      case MeFontStyle.G8:
        return fromFigma(textStyle: G, color: colorScheme.color8);
      case MeFontStyle.G11:
        return fromFigma(textStyle: G, color: colorScheme.color11);
      case MeFontStyle.G12:
        return fromFigma(textStyle: G, color: colorScheme.color12);
      case MeFontStyle.G14:
        return fromFigma(textStyle: G, color: colorScheme.color14);

      // For H base font style
      case MeFontStyle.H1:
        return fromFigma(textStyle: H, color: colorScheme.color1);
      case MeFontStyle.H7:
        return fromFigma(textStyle: H, color: colorScheme.color7);
      case MeFontStyle.H8:
        return fromFigma(textStyle: H, color: colorScheme.color8);
      case MeFontStyle.H12:
        return fromFigma(textStyle: H, color: colorScheme.color12);
      case MeFontStyle.H22:
        return fromFigma(textStyle: H, color: colorScheme.color22);

      // For I base font style
      case MeFontStyle.I1:
        return fromFigma(textStyle: I, color: colorScheme.color1);
      case MeFontStyle.I2:
        return fromFigma(textStyle: I, color: colorScheme.color2);
      case MeFontStyle.I3:
        return fromFigma(textStyle: I, color: colorScheme.color3);
      case MeFontStyle.I5:
        return fromFigma(textStyle: I, color: colorScheme.color5);
      case MeFontStyle.I7:
        return fromFigma(textStyle: I, color: colorScheme.color7);
      case MeFontStyle.I8:
        return fromFigma(textStyle: I, color: colorScheme.color8, height: 18);
      case MeFontStyle.I11:
        return fromFigma(textStyle: I, color: colorScheme.color11);
      case MeFontStyle.I12:
        return fromFigma(textStyle: I, color: colorScheme.color12);
      case MeFontStyle.I14:
        return fromFigma(textStyle: I, color: colorScheme.color14);

      // For J base font style
      case MeFontStyle.J1:
        return fromFigma(textStyle: J, color: colorScheme.color1);
      case MeFontStyle.J7:
        return fromFigma(textStyle: J, color: colorScheme.color7);
      case MeFontStyle.J8:
        return fromFigma(textStyle: J, color: colorScheme.color8);
      case MeFontStyle.J9:
        return fromFigma(textStyle: J, color: colorScheme.color9);
      case MeFontStyle.J11:
        return fromFigma(textStyle: J, color: colorScheme.color11);

      // For L base font style
      case MeFontStyle.L2:
        return fromFigma(textStyle: L, color: colorScheme.color2);
      case MeFontStyle.L12:
        return fromFigma(textStyle: L, color: colorScheme.color12);

      // For N base font style
      case MeFontStyle.N7:
        return fromFigma(textStyle: N, color: colorScheme.color7);
      case MeFontStyle.N8:
        return fromFigma(textStyle: N, color: colorScheme.color8);

      // For O base font style
      case MeFontStyle.O8:
        return fromFigma(textStyle: O, color: colorScheme.color8);

      // For P base font style
      case MeFontStyle.P8:
        return fromFigma(textStyle: P, color: colorScheme.color8);
      case MeFontStyle.P10:
        return fromFigma(textStyle: P, color: colorScheme.color10);

      // For Q base font style
      case MeFontStyle.Q8:
        return fromFigma(textStyle: Q, color: colorScheme.color8, height: 36);

      // For R base font style
      case MeFontStyle.R1:
        return fromFigma(textStyle: R, color: colorScheme.color1);
      case MeFontStyle.R8:
        return fromFigma(textStyle: R, color: colorScheme.color8);
      case MeFontStyle.R12:
        return fromFigma(textStyle: R, color: colorScheme.color12);

      // For S base font style
      case MeFontStyle.S8:
        return fromFigma(textStyle: S, color: colorScheme.color8);

      // For no matching base font style
      default:
        return const TextStyle();
    }
  }
}
