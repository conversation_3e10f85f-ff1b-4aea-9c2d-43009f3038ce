import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:mevolvesupport/constants/size_constants.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';

abstract class MeTheme {
  static ThemeData theme(
    final MeColorScheme colorScheme,
    final Brightness brightness,
  ) {
    return ThemeData(
      // brightness: brightness,
      extensions: [
        colorScheme,
      ],
      useMaterial3: false,
      tabBarTheme: TabBarTheme(
        indicatorColor: colorScheme.color12,
        indicator: const UnderlineTabIndicator(
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.0),
            topRight: Radius.circular(16.0),
          ),
        ),
      ),
      primaryColor: colorScheme.color1,
      disabledColor: colorScheme.color4,
      buttonTheme: ButtonThemeData(
        height: SizeConstants.buttonMaxHeight,
        minWidth: SizeConstants.buttonMinWidth,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(SizeConstants.buttonRadius),
        ),
      ),
      appBarTheme: AppBarTheme(
        elevation: SizeConstants.appBarElevation,
        backgroundColor: colorScheme.color9,
        iconTheme: IconThemeData(color: colorScheme.color5),
        toolbarHeight: SizeConstants.appBarHeight,
        centerTitle: false,
      ),
      textTheme: GoogleFonts.robotoTextTheme(),
      fontFamily: GoogleFonts.roboto().fontFamily,
      scaffoldBackgroundColor: colorScheme.color6,
      drawerTheme: DrawerThemeData(
        backgroundColor: colorScheme.color6,
        width: SizeConstants.drawerWidth,
      ),
      chipTheme: ChipThemeData(
        showCheckmark: false,
        deleteIconColor: colorScheme.color12,
        elevation: 0,
        selectedColor: colorScheme.color1,
        backgroundColor: colorScheme.color5,
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        labelPadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(
            SizeConstants.choiceChipCircularBorderRadius,
          ),
          side: BorderSide(
            color: colorScheme.color4,
          ),
        ),
      ),
      bottomSheetTheme: BottomSheetThemeData(
        backgroundColor: colorScheme.color6,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(SizeConstants.bottomSheetBorderRadius),
          ),
        ),
      ),
      listTileTheme: ListTileThemeData(
        tileColor: colorScheme.color5,
      ),
      colorScheme: ColorScheme.fromSwatch().copyWith(
        primary: colorScheme.color1,
        secondary: colorScheme.color4,
      ),
      dialogTheme: DialogTheme(
        backgroundColor: colorScheme.color5,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
      ),
      iconTheme: IconThemeData(color: colorScheme.color1),
      inputDecorationTheme: InputDecorationTheme(
        hintStyle: TextStyle(
          fontSize: 16,
          color: colorScheme.color7,
          fontWeight: FontWeight.w400,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ButtonStyle(
          shape: WidgetStateProperty.all(
            RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(SizeConstants.buttonRadius),
            ),
          ),
          backgroundColor: WidgetStateProperty.resolveWith((states) {
            if (states.contains(WidgetState.disabled)) {
              return colorScheme.color4;
            }
            return colorScheme.color1;
          }),
        ),
      ),
      checkboxTheme: CheckboxThemeData(
        checkColor: WidgetStateProperty.all(colorScheme.color12),
        fillColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return null;
          }
          if (states.contains(WidgetState.selected)) {
            return colorScheme.color1;
          }
          return colorScheme.color7;
        }),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(
            Radius.circular(4),
          ),
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: WidgetStateProperty.all(colorScheme.color7),
      ).copyWith(
        fillColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return null;
          }
          if (states.contains(WidgetState.selected)) {
            return colorScheme.color1;
          }
          return colorScheme.color7;
        }),
      ),
      switchTheme: SwitchThemeData(
        trackColor: WidgetStateProperty.resolveWith(
          (states) => states.contains(WidgetState.selected)
              ? colorScheme.color1
              : colorScheme.color10,
        ),
        thumbColor: WidgetStateProperty.all(colorScheme.color5),
      ).copyWith(
        thumbColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return null;
          }
          if (states.contains(WidgetState.selected)) {
            return colorScheme.color5;
          }
          return null;
        }),
        trackColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.disabled)) {
            return null;
          }
          if (states.contains(WidgetState.selected)) {
            return colorScheme.color1;
          }
          return null;
        }),
      ),
      scrollbarTheme: ScrollbarThemeData(
        thumbColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          if (states.contains(WidgetState.hovered)) {
            return colorScheme.color1.withValues(alpha: 0.8);
          }
          if (states.contains(WidgetState.dragged)) {
            return colorScheme.color1;
          }
          return colorScheme.color7.withValues(alpha: 0.6);
        }),
        trackColor:
            WidgetStateProperty.resolveWith<Color?>((Set<WidgetState> states) {
          return colorScheme.color6.withValues(alpha: 0.3);
        }),
        trackBorderColor: WidgetStateProperty.all(Colors.transparent),
        thickness: WidgetStateProperty.all(8.0),
        radius: const Radius.circular(4.0),
        crossAxisMargin: 2.0,
        mainAxisMargin: 4.0,
      ),
    );
  }
}
