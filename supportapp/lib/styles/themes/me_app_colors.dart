import 'dart:ui';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';

abstract class MeAppColors {
  /// For Hexadecimal color codes with alpha use a convertor online like https://www.rapidtables.com/convert/color/hex-to-rgb.html
  /// As we want to keep color constants in the code, we will use the RGB values for those colors.

  /// Blue Light Theme Colors
  // Blue light/Blue light 1
  // #1DA1F2
  // Blue light/Blue light 10
  // #E5E5E5
  // Blue light/Blue light 11
  // #F34642
  // Blue light/Blue light 12
  // #FFFFFF
  // Blue light/Blue light 13
  // #000000
  // 40%
  // Blue light/Blue light 2
  // #0E8AD7
  // Blue light/Blue light 3
  // #EDF8FF
  // Blue light/Blue light 4
  // #96D7FF
  // Blue light/Blue light 5
  // #FFFFFF
  // Blue light/Blue light 6
  // #F2F2F2
  // Blue light/Blue light 7
  // #949C9E
  // Blue light/Blue light 8
  // #323238
  // Blue light/Blue light 9
  // #1DA1F2

  static const Color blueLight1 = Color(0xff1DA1F1);
  static const Color blueLight2 = Color(0xff1DA1F2);
  static const Color blueLight3 = Color(0xffEDF8FF);
  static const Color blueLight4 = Color(0xff96D7FF);
  static const Color blueLight5 = Color(0xffFFFFFF);
  static const Color blueLight6 = Color(0xffF2F2F2);
  static const Color blueLight7 = Color(0xff949C9E);
  static const Color blueLight8 = Color(0xff323238);
  static const Color blueLight9 = Color(0xff1DA1F3);
  static const Color blueLight10 = Color(0xffE5E5E5);
  static const Color blueLight11 = Color(0xffF34642);
  static const Color blueLight12 = Color(0xffFFFFFF);
  static const Color blueLight13 = Color.fromRGBO(0, 0, 0, 0.4);
  static const Color blueLight14 = Color(0xffFF6700);
  static const Color blueLight15 = Color(0xffEDF8FF);
  static const Color blueLight16 = Color(0xff323239);
  static const Color blueLight17 = Color(0xffFFFFFD);
  static const Color blueLight18 = Color(0xffF2F2F3);
  static const Color blueLight19 = Color(0xff32323A);
  static const Color blueLight20 = Color(0xffEDF8FD);
  static const Color blueLight21 = Color(0xffEDF8FC);
  static const Color blueLight22 = Color(0xff96D7FE);
  static const Color blueLight23 = Color(0xff949C9C);
  static const Color blueLight24 = Color(0xff96D7FD);
  static const Color blueLight25 = Color(0xff31A8F1);
  static const Color blueLight26 = Color(0xff5F636D);
  static const Color blueLight27 = Color(0xff96D7FC);
  static const Color blueLight28 = Color(0xffFFDBC4);
  static const Color blueLight29 = Color(0xff5E626C);
  static const Color blueLight30 = Color(0xff1FA3F4);
  static const Color blueLight31 = Color(0xffFFFFFC);
  static const Color blueLight32 = Color(0xffFFE5E5);

  static const MeColorScheme blueLightColorScheme = MeColorScheme(
    color1: blueLight1,
    color2: blueLight2,
    color3: blueLight3,
    color4: blueLight4,
    color5: blueLight5,
    color6: blueLight6,
    color7: blueLight7,
    color8: blueLight8,
    color9: blueLight9,
    color10: blueLight10,
    color11: blueLight11,
    color12: blueLight12,
    color13: blueLight13,
    color14: blueLight14,
    color15: blueLight15,
    color16: blueLight16,
    color17: blueLight17,
    color18: blueLight18,
    color19: blueLight19,
    color20: blueLight20,
    color21: blueLight21,
    color22: blueLight22,
    color23: blueLight23,
    color24: blueLight24,
    color25: blueLight25,
    color26: blueLight26,
    color27: blueLight27,
    color28: blueLight28,
    color29: blueLight29,
    color30: blueLight30,
    color31: blueLight31,
    color32: blueLight32,
  );

  /// Green Light Theme Colors

  // Green light/Green light 1
  // #5CC091
  // Green light/Green light 10
  // #E5E5E5
  // Green light/Green light 11
  // #F34642
  // Green light/Green light 12
  // #FFFFFF
  // Green light/Green light 13
  // #000000
  // 40%
  // Green light/Green light 2
  // #41A676
  // Green light/Green light 3
  // #F2FFF9
  // Green light/Green light 4
  // #A6DEC4
  // Green light/Green light 5
  // #FFFFFF
  // Green light/Green light 6
  // #F2F2F2
  // Green light/Green light 7
  // #949C9E
  // Green light/Green light 8
  // #323238
  // Green light/Green light 9
  // #5CC091

  static const Color greenLight1 = Color(0xff5A9C7D);
  static const Color greenLight2 = Color(0xff5A9C7D);
  static const Color greenLight3 = Color(0xffF2FFF9);
  static const Color greenLight4 = Color(0xffA6DEC4);
  static const Color greenLight5 = Color(0xffFFFFFF);
  static const Color greenLight6 = Color(0xffF2F2F2);
  static const Color greenLight7 = Color(0xff949C9E);
  static const Color greenLight8 = Color(0xff323238);
  static const Color greenLight9 = Color(0xff5A9C7D);
  static const Color greenLight10 = Color(0xffE5E5E5);
  static const Color greenLight11 = Color(0xffF34642);
  static const Color greenLight12 = Color(0xffFFFFFF);
  static const Color greenLight13 = Color.fromRGBO(0, 0, 0, 0.4);
  static const Color greenLight14 = Color(0xffFF6700);
  static const Color greenLight15 = Color(0xffF2FFF9);
  static const Color greenLight16 = Color(0xff323238);
  static const Color greenLight17 = Color(0xffFFFFFF);
  static const Color greenLight18 = Color(0xffF2F2F2);
  static const Color greenLight19 = Color(0xff323238);
  static const Color greenLight20 = Color(0xffF2FFF9);
  static const Color greenLight21 = Color(0xffF2FFF9);
  static const Color greenLight22 = Color(0xffA6DEC4);
  static const Color greenLight23 = Color(0xffF2FFF9);
  static const Color greenLight24 = Color(0xffA6DEC4);
  static const Color greenLight25 = Color.fromRGBO(90, 156, 125, 0.9);
  static const Color greenLight26 = Color(0xff5F636D);
  static const Color greenLight27 = Color(0xffA6DEC2);
  static const Color greenLight28 = Color(0xffFFDBC3);
  static const Color greenLight29 = Color(0xff5E626C);
  static const Color greenLight30 = Color(0xff5A9C7C);
  static const Color greenLight31 = Color(0xffFFFFFC);
  static const Color greenLight32 = Color(0xffFFE5E5);
  static const Color greenLight35 = Color(0xFF5A9C71);

  static const MeColorScheme greenLightColorScheme = MeColorScheme(
    color1: greenLight1,
    color2: greenLight2,
    color3: greenLight3,
    color4: greenLight4,
    color5: greenLight5,
    color6: greenLight6,
    color7: greenLight7,
    color8: greenLight8,
    color9: greenLight9,
    color10: greenLight10,
    color11: greenLight11,
    color12: greenLight12,
    color13: greenLight13,
    color14: greenLight14,
    color15: greenLight15,
    color16: greenLight16,
    color17: greenLight17,
    color18: greenLight18,
    color19: greenLight19,
    color20: greenLight20,
    color21: greenLight21,
    color22: greenLight22,
    color23: greenLight23,
    color24: greenLight24,
    color25: greenLight25,
    color26: greenLight26,
    color27: greenLight27,
    color28: greenLight28,
    color29: greenLight29,
    color30: greenLight30,
    color32: greenLight32,
    color35: greenLight35,
  );

  /// Purple Light Theme Colors

  // Purple light/Purple light 1
  // #7662FD
  // Purple light/Purple light 10
  // #E5E5E5
  // Purple light/Purple light 11
  // #F34642
  // Purple light/Purple light 12
  // #FFFFFF
  // Purple light/Purple light 13
  // #000000
  // 40%
  // Purple light/Purple light 2
  // #5242BD
  // Purple light/Purple light 3
  // #EBE8FF
  // Purple light/Purple light 4
  // #B1A5FF
  // Purple light/Purple light 5
  // #FFFFFF
  // Purple light/Purple light 6
  // #F2F2F2
  // Purple light/Purple light 7
  // #949C9E
  // Purple light/Purple light 8
  // #323238
  // Purple light/Purple light 9
  // #7662FD

  static const Color purpleLight1 = Color(0xff7662FD);
  static const Color purpleLight2 = Color(0xff5242BD);
  static const Color purpleLight3 = Color(0xffEBE8FF);
  static const Color purpleLight4 = Color(0xffB1A5FF);
  static const Color purpleLight5 = Color(0xffFFFFFF);
  static const Color purpleLight6 = Color(0xffF2F2F2);
  static const Color purpleLight7 = Color(0xff949C9E);
  static const Color purpleLight8 = Color(0xff323238);
  static const Color purpleLight9 = Color(0xff7662FD);
  static const Color purpleLight10 = Color(0xffE5E5E5);
  static const Color purpleLight11 = Color(0xffF34642);
  static const Color purpleLight12 = Color(0xffFFFFFF);
  static const Color purpleLight13 = Color.fromRGBO(0, 0, 0, 0.4);
  static const MeColorScheme purpleLightColorScheme = MeColorScheme(
    color1: purpleLight1,
    color2: purpleLight2,
    color3: purpleLight3,
    color4: purpleLight4,
    color5: purpleLight5,
    color6: purpleLight6,
    color7: purpleLight7,
    color8: purpleLight8,
    color9: purpleLight9,
    color10: purpleLight10,
    color11: purpleLight11,
    color12: purpleLight12,
    color13: purpleLight13,
  );

  /// Red Light Theme Colors

  // Red light/Red light 1
  // #F24C4C
  // Red light/Red light 10
  // #E5E5E5
  // Red light/Red light 11
  // #F34642
  // Red light/Red light 12
  // #FFFFFF
  // Red light/Red light 13
  // #000000
  // 40%
  // Red light/Red light 2
  // #C83F3F
  // Red light/Red light 3
  // #FFEAEA
  // Red light/Red light 4
  // #FFA3A3
  // Red light/Red light 5
  // #FFFFFF
  // Red light/Red light 6
  // #F2F2F2
  // Red light/Red light 7
  // #949C9E
  // Red light/Red light 8
  // #323238
  // Red light/Red light 9
  // #F24C4C

  static const Color redLight1 = Color(0xffF24C4C);
  static const Color redLight2 = Color(0xffC83F3F);
  static const Color redLight3 = Color(0xffFFEAEA);
  static const Color redLight4 = Color(0xffFFA3A3);
  static const Color redLight5 = Color(0xffFFFFFF);
  static const Color redLight6 = Color(0xffF2F2F2);
  static const Color redLight7 = Color(0xff949C9E);
  static const Color redLight8 = Color(0xff323238);
  static const Color redLight9 = Color(0xffF24C4C);
  static const Color redLight10 = Color(0xffE5E5E5);
  static const Color redLight11 = Color(0xffF34642);
  static const Color redLight12 = Color(0xffFFFFFF);
  static const Color redLight13 = Color.fromRGBO(0, 0, 0, 0.4);
  static const MeColorScheme redLightColorScheme = MeColorScheme(
    color1: redLight1,
    color2: redLight2,
    color3: redLight3,
    color4: redLight4,
    color5: redLight5,
    color6: redLight6,
    color7: redLight7,
    color8: redLight8,
    color9: redLight9,
    color10: redLight10,
    color11: redLight11,
    color12: redLight12,
    color13: redLight13,
  );

  /// Pink Light Theme Colors

  // Pink light/Pink light 1
  // #FF3B6F
  // Pink light/Pink light 10
  // #E5E5E5
  // Pink light/Pink light 11
  // #F34642
  // Pink light/Pink light 12
  // #FFFFFF
  // Pink light/Pink light 13
  // #000000
  // 40%
  // Pink light/Pink light 2
  // #CD3E63
  // Pink light/Pink light 3
  // #FFD2DE
  // Pink light/Pink light 4
  // #FF9DB6
  // Pink light/Pink light 5
  // #FFFFFF
  // Pink light/Pink light 6
  // #F2F2F2
  // Pink light/Pink light 7
  // #949C9E
  // Pink light/Pink light 8
  // #323238
  // Pink light/Pink light 9
  // #FF3B6F

  static const Color pinkLight1 = Color(0xffFF3B6F);
  static const Color pinkLight2 = Color(0xffCD3E63);
  static const Color pinkLight3 = Color(0xffFFD2DE);
  static const Color pinkLight4 = Color(0xffFF9DB6);
  static const Color pinkLight5 = Color(0xffFFFFFF);
  static const Color pinkLight6 = Color(0xffF2F2F2);
  static const Color pinkLight7 = Color(0xff949C9E);
  static const Color pinkLight8 = Color(0xff323238);
  static const Color pinkLight9 = Color(0xFFFF3B6F);
  static const Color pinkLight10 = Color(0xffE5E5E5);
  static const Color pinkLight11 = Color(0xffF34642);
  static const Color pinkLight12 = Color(0xffFFFFFF);
  static const Color pinkLight13 = Color.fromRGBO(0, 0, 0, 0.4);
  static const MeColorScheme pinkLightColorScheme = MeColorScheme(
    color1: pinkLight1,
    color2: pinkLight2,
    color3: pinkLight3,
    color4: pinkLight4,
    color5: pinkLight5,
    color6: pinkLight6,
    color7: pinkLight7,
    color8: pinkLight8,
    color9: pinkLight9,
    color10: pinkLight10,
    color11: pinkLight11,
    color12: pinkLight12,
    color13: pinkLight13,
  );

  /// Dark Theme Colors

  /// Blue Dark Theme Colors

// Blue dark/Blue dark 1
// #1DA1F2
// Blue dark/Blue dark 10
// #4B4B4B
// Blue dark/Blue dark 11
// #F34642
// Blue dark/Blue dark 12
// #FFFFFF
// Blue dark/Blue dark 13
// #000000
// 70%
// Blue dark/Blue dark 2
// #0E8AD7
// Blue dark/Blue dark 3
// #EDF8FF
// Blue dark/Blue dark 4
// #96D7FF
// Blue dark/Blue dark 5
// #22252D
// Blue dark/Blue dark 6
// #121212
// Blue dark/Blue dark 7
// #949C9E
// Blue dark/Blue dark 8
// #FFFFFF
// Blue dark/Blue dark 9
// #22252D

  static const Color blueDark1 = Color(0xff1DA1F1);
  static const Color blueDark2 = Color(0xffFFFFFE);
  static const Color blueDark3 = Color(0xff4E5465);
  static const Color blueDark4 = Color(0xff156393);
  static const Color blueDark5 = Color(0xff22252D);
  static const Color blueDark6 = Color(0xff121212);
  static const Color blueDark7 = Color(0xff949C9F);
  static const Color blueDark8 = Color(0xffFFFFFF);
  static const Color blueDark9 = Color(0xff4E5464);
  static const Color blueDark10 = Color(0xff4B4B4B);
  static const Color blueDark11 = Color(0xffF34643);
  static const Color blueDark12 = Color(0xffFFFFFD);
  static const Color blueDark13 = Color.fromRGBO(0, 0, 0, 0.8);
  static const Color blueDark14 = Color(0xffFF6701);
  static const Color blueDark15 = Color(0xff1DA1F2);
  static const Color blueDark16 = Color(0xff22252E);
  static const Color blueDark17 = Color(0xff949C9B);
  static const Color blueDark18 = Color(0xff4E5466);
  static const Color blueDark19 = Color(0xff4E5467);
  static const Color blueDark20 = Color(0xff22252C);
  static const Color blueDark21 = Color(0xffEDF8FC);
  static const Color blueDark22 = Color(0xffB3E1FC);
  static const Color blueDark23 = Color(0xff949C9C);
  static const Color blueDark24 = Color(0xffFFFFF7);
  static const Color blueDark25 = Color(0xff4A4F5E);
  static const Color blueDark26 = Color(0xff5F636C);
  static const Color blueDark27 = Color(0xff949C9D);
  static const Color blueDark28 = Color(0xffFFDBC4);
  static const Color blueDark29 = Color(0xff949C9E);
  static const Color blueDark30 = Color(0xff4B4B4C);
  static const Color blueDark31 = Color(0xff4E5468);
  static const Color blueDark32 = Color(0xffFFE5E5);

  static const MeColorScheme blueDarkColorScheme = MeColorScheme(
    color1: blueDark1,
    color2: blueDark2,
    color3: blueDark3,
    color4: blueDark4,
    color5: blueDark5,
    color6: blueDark6,
    color7: blueDark7,
    color8: blueDark8,
    color9: blueDark9,
    color10: blueDark10,
    color11: blueDark11,
    color12: blueDark12,
    color13: blueDark13,
    color14: blueDark14,
    color15: blueDark15,
    color16: blueDark16,
    color17: blueDark17,
    color18: blueDark18,
    color19: blueDark19,
    color20: blueDark20,
    color21: blueDark21,
    color22: blueDark22,
    color23: blueDark23,
    color24: blueDark24,
    color25: blueDark25,
    color26: blueDark26,
    color27: blueDark27,
    color28: blueDark28,
    color29: blueDark29,
    color30: blueDark30,
    color31: blueDark31,
    color32: blueDark32,
  );

  /// Green Dark Theme Colors
// Green dark/Green dark 1
// #5CC091
// Green dark/Green dark 10
// #4B4B4B
// Green dark/Green dark 11
// #F34642
// Green dark/Green dark 12
// #FFFFFF
// Green dark/Green dark 13
// #000000
// 80%
// Green dark/Green dark 2
// #41A676
// Green dark/Green dark 3
// #F2FFF9
// Green dark/Green dark 4
// #A6DEC4
// Green dark/Green dark 5
// #22252D
// Green dark/Green dark 6
// #121212
// Green dark/Green dark 7
// #949C9E
// Green dark/Green dark 8
// #FFFFFF
// Green dark/Green dark 9
// #22252D
//

  static const Color greenDark1 = Color(0xff5A9C7D);
  static const Color greenDark2 = Color(0xffFFFFFE);
  static const Color greenDark3 = Color(0xff4E5465);
  static const Color greenDark4 = Color(0xff3E6155);
  static const Color greenDark5 = Color(0xff22252D);
  static const Color greenDark6 = Color(0xff121212);
  static const Color greenDark7 = Color(0xff949C9F);
  static const Color greenDark8 = Color(0xffFFFFFF);
  static const Color greenDark9 = Color(0xff4E5464);
  static const Color greenDark10 = Color(0xff4B4B4B);
  static const Color greenDark11 = Color(0xffF34643);
  static const Color greenDark12 = Color(0xffFFFFFD);
  static const Color greenDark13 = Color.fromRGBO(0, 0, 0, 0.8);
  static const Color greenDark14 = Color(0xffFF6701);
  static const Color greenDark15 = Color(0xff5A9C7E);
  static const Color greenDark16 = Color(0xff22252E);
  static const Color greenDark17 = Color(0xff949C9B);
  static const Color greenDark18 = Color(0xff4E5466);
  static const Color greenDark19 = Color(0xff4E5467);
  static const Color greenDark20 = Color(0xff22252C);
  static const Color greenDark21 = Color(0xffF2FFF9);
  static const Color greenDark22 = Color(0xffA6DEC4);
  static const Color greenDark23 = Color(0xff949C9C);
  static const Color greenDark24 = Color(0xffFFFFF7);
  static const Color greenDark25 = Color(0xff4A4F5E);
  static const Color greenDark26 = Color(0xff5F636C);
  static const Color greenDark27 = Color(0xff949C9D);
  static const Color greenDark28 = Color(0xffFFDBC4);
  static const Color greenDark29 = Color(0xff949C9E);
  static const Color greenDark30 = Color(0xff4B4B4C);
  static const Color greenDark31 = Color(0xff4E5468);
  static const Color greenDark32 = Color(0xffFFE5E5);
  static const Color greenDark35 = Color(0xFF81CBA8);

  static const MeColorScheme greenDarkColorScheme = MeColorScheme(
    color1: greenDark1,
    color2: greenDark2,
    color3: greenDark3,
    color4: greenDark4,
    color5: greenDark5,
    color6: greenDark6,
    color7: greenDark7,
    color8: greenDark8,
    color9: greenDark9,
    color10: greenDark10,
    color11: greenDark11,
    color12: greenDark12,
    color13: greenDark13,
    color14: greenDark14,
    color15: greenDark15,
    color16: greenDark16,
    color17: greenDark17,
    color18: greenDark18,
    color19: greenDark19,
    color20: greenDark20,
    color21: greenDark21,
    color22: greenDark22,
    color23: greenDark23,
    color24: greenDark24,
    color25: greenDark25,
    color26: greenDark26,
    color27: greenDark27,
    color28: greenDark28,
    color29: greenDark29,
    color30: greenDark30,
    color31: greenDark31,
    color32: greenDark32,
    color35: greenDark35,
  );

  /// Purple Dark Theme Colors

// Purple dark/Purple dark 1
// #7662FD
// Purple dark/Purple dark 10
// #4B4B4B
// Purple dark/Purple dark 11
// #F34642
// Purple dark/Purple dark 12
// #FFFFFF
// Purple dark/Purple dark 13
// #000000
// 80%
// Purple dark/Purple dark 2
// #5242BD
// Purple dark/Purple dark 3
// #EBE8FF
// Purple dark/Purple dark 4
// #B1A5FF
// Purple dark/Purple dark 5
// #22252D
// Purple dark/Purple dark 6
// #121212
// Purple dark/Purple dark 7
// #949C9E
// Purple dark/Purple dark 8
// #FFFFFF
// Purple dark/Purple dark 9
// #22252D

  static const Color purpleDark1 = Color(0xff7662FD);
  static const Color purpleDark2 = Color(0xff5242BD);
  static const Color purpleDark3 = Color(0xffEBE8FF);
  static const Color purpleDark4 = Color(0xffB1A5FF);
  static const Color purpleDark5 = Color(0xff22252D);
  static const Color purpleDark6 = Color(0xff121212);
  static const Color purpleDark7 = Color(0xff949C9E);
  static const Color purpleDark8 = Color(0xffFFFFFF);

  // todo: to change the dark color 9 to #575F73;
  static const Color purpleDark9 = Color(0xff22252D);
  static const Color purpleDark10 = Color(0xff4B4B4B);
  static const Color purpleDark11 = Color(0xffF34642);
  static const Color purpleDark12 = Color(0xffFFFFFF);
  static const Color purpleDark13 = Color.fromRGBO(0, 0, 0, 0.8);
  static const MeColorScheme purpleDarkColorScheme = MeColorScheme(
    color1: purpleDark1,
    color2: purpleDark2,
    color3: purpleDark3,
    color4: purpleDark4,
    color5: purpleDark5,
    color6: purpleDark6,
    color7: purpleDark7,
    color8: purpleDark8,
    color9: purpleDark9,
    color10: purpleDark10,
    color11: purpleDark11,
    color12: purpleDark12,
    color13: purpleDark13,
  );

  /// Red Dark Theme Colors

// Red dark/Red dark 1
// #F24C4C
// Red dark/Red dark 10
// #4B4B4B
// Red dark/Red dark 11
// #F34642
// Red dark/Red dark 12
// #FFFFFF
// Red dark/Red dark 13
// #000000
// 80%
// Red dark/Red dark 2
// #C83F3F
// Red dark/Red dark 3
// #FFEAEA
// Red dark/Red dark 4
// #FFA3A3
// Red dark/Red dark 5
// #22252D
// Red dark/Red dark 6
// #121212
// Red dark/Red dark 7
// #949C9E
// Red dark/Red dark 8
// #FFFFFF
// Red dark/Red dark 9
// #22252D

  static const Color redDark1 = Color(0xffF24C4C);
  static const Color redDark2 = Color(0xffC83F3F);
  static const Color redDark3 = Color(0xffFFEAEA);
  static const Color redDark4 = Color(0xffFFA3A3);
  static const Color redDark5 = Color(0xff22252D);
  static const Color redDark6 = Color(0xff121212);
  static const Color redDark7 = Color(0xff949C9E);
  static const Color redDark8 = Color(0xffFFFFFF);
  static const Color redDark9 = Color(0xff22252D);
  static const Color redDark10 = Color(0xff4B4B4B);
  static const Color redDark11 = Color(0xffF34642);
  static const Color redDark12 = Color(0xffFFFFFF);
  static const Color redDark13 = Color.fromRGBO(0, 0, 0, 0.8);
  static const MeColorScheme redDarkColorScheme = MeColorScheme(
    color1: redDark1,
    color2: redDark2,
    color3: redDark3,
    color4: redDark4,
    color5: redDark5,
    color6: redDark6,
    color7: redDark7,
    color8: redDark8,
    color9: redDark9,
    color10: redDark10,
    color11: redDark11,
    color12: redDark12,
    color13: redDark13,
  );

  /// Pink Dark Theme Colors

// Pink dark/Pink dark 1
// #FF3B6F
// Pink dark/Pink dark 10
// #4B4B4B
// Pink dark/Pink dark 11
// #F34642
// Pink dark/Pink dark 12
// #FFFFFF
// Pink dark/Pink dark 13
// #000000
// 80%
// Pink dark/Pink dark 2
// #CD3E63
// Pink dark/Pink dark 3
// #FFD2DE
// Pink dark/Pink dark 4
// #FF9DB6
// Pink dark/Pink dark 5
// #22252D
// Pink dark/Pink dark 6
// #121212
// Pink dark/Pink dark 7
// #949C9E
// Pink dark/Pink dark 8
// #FFFFFF
// Pink dark/Pink dark 9
// #22252D

  static const Color pinkDark1 = Color(0xffFF3B6F);
  static const Color pinkDark2 = Color(0xffCD3E63);
  static const Color pinkDark3 = Color(0xffFFD2DE);
  static const Color pinkDark4 = Color(0xffFF9DB6);
  static const Color pinkDark5 = Color(0xff22252D);
  static const Color pinkDark6 = Color(0xff121212);
  static const Color pinkDark7 = Color(0xff949C9E);
  static const Color pinkDark8 = Color(0xffFFFFFF);
  static const Color pinkDark9 = Color(0xff22252D);
  static const Color pinkDark10 = Color(0xff4B4B4B);
  static const Color pinkDark11 = Color(0xffF34642);
  static const Color pinkDark12 = Color(0xffFFFFFF);
  static const Color pinkDark13 = Color.fromRGBO(0, 0, 0, 0.8);
  static const MeColorScheme pinkDarkColorScheme = MeColorScheme(
    color1: pinkDark1,
    color2: pinkDark2,
    color3: pinkDark3,
    color4: pinkDark4,
    color5: pinkDark5,
    color6: pinkDark6,
    color7: pinkDark7,
    color8: pinkDark8,
    color9: pinkDark9,
    color10: pinkDark10,
    color11: pinkDark11,
    color12: pinkDark12,
    color13: pinkDark13,
  );
}
