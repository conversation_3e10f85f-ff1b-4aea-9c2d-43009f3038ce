import 'package:flutter/material.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class MeText extends StatelessWidget {
  const MeText({
    super.key,
    required this.text,
    required this.meFontStyle,
    this.textAlign = TextAlign.left,
    this.textOverflow = TextOverflow.ellipsis,
    this.softWrap,
    this.maxLines,
    this.disabled = false,
    this.textScaler,
  });

  final String text;
  final MeFontStyle meFontStyle;
  final TextAlign textAlign;
  final TextOverflow? textOverflow;
  final int? maxLines;
  final bool disabled;
  final bool? softWrap;
  final TextScaler? textScaler;

  @override
  Widget build(BuildContext context) {
    TextStyle fontStyle = MeTextTheme.getMeFontStyle(
      context: context,
      fontStyle: meFontStyle,
    );
    return Text(
      text,
      overflow: textOverflow,
      textScaler: textScaler,
      softWrap: softWrap,
      maxLines: maxLines,
      textAlign: textAlign,
      style: fontStyle.copyWith(
        fontSize: fontStyle.fontSize,
        color: disabled ? fontStyle.color?.withValues(alpha: 0.5) : null,
      ),
    );
  }
}
