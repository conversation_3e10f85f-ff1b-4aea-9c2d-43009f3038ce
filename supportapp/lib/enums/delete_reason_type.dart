enum DeletedReasonType {
  none,
  lotsOfBugs,
  lackOfUse,
  issues,
  userExperience,
  compatibility,
  recentChanges,
  privacyConcerns,
  manyNotifs,
  other,
  ;

  String toReadableString() {
    switch (this) {
      case DeletedReasonType.lotsOfBugs:
        return 'Lot of bugs';
      case DeletedReasonType.lackOfUse:
        return 'Lack of use';
      case DeletedReasonType.issues:
        return 'Technical issues';
      case DeletedReasonType.userExperience:
        return 'User experience';
      case DeletedReasonType.compatibility:
        return 'Device compatibility';
      case DeletedReasonType.recentChanges:
        return 'Recent changes to the app';
      case DeletedReasonType.privacyConcerns:
        return 'Privacy concerns';
      case DeletedReasonType.manyNotifs:
        return 'Too many notifications';
      case DeletedReasonType.other:
        return 'Other';
      case DeletedReasonType.none:
        return 'All';
    }
  }
}
