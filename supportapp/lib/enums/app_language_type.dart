enum LanguageType {
  german,
  english,
  spanish,
  french,
  italian,
  portuguese;

  @override
  String toString() {
    switch (this) {
      case LanguageType.english:
        return 'English';
      case LanguageType.french:
        return 'Français';
      case LanguageType.german:
        return 'Deutsch';
      case LanguageType.italian:
        return 'Italiano';
      case LanguageType.portuguese:
        return 'Português';
      case LanguageType.spanish:
        return 'Español';
    }
  }

  static LanguageType fromString(String value) {
    switch (value) {
      case 'English':
        return LanguageType.english;
      case 'Français':
        return LanguageType.french;
      case 'Deutsch':
        return LanguageType.german;
      case 'Italiano':
        return LanguageType.italian;
      case 'Português':
        return LanguageType.portuguese;
      case 'Español':
        return LanguageType.spanish;
      default:
        return LanguageType.english;
    }
  }

  String toLanguageCode() {
    switch (this) {
      case LanguageType.english:
        return 'en';
      case LanguageType.french:
        return 'fr';
      case LanguageType.german:
        return 'de';
      case LanguageType.italian:
        return 'it';
      case LanguageType.portuguese:
        return 'pt';
      case LanguageType.spanish:
        return 'es';
    }
  }
}
