import 'package:flutter/material.dart';

enum ThemeType {
  systemDefault,
  light,
  dark,
  ;

  String toReadableString() {
    switch (this) {
      case ThemeType.systemDefault:
        return 'System default';
      case ThemeType.light:
        return 'Light';
      case ThemeType.dark:
        return 'Dark';
    }
  }
}

extension ThemeTypeFromString on String {
  ThemeType toThemeType() {
    switch (this) {
      case 'sys_def':
        return ThemeType.systemDefault;
      case 'light':
        return ThemeType.light;
      case 'dark':
        return ThemeType.dark;
      default:
        return ThemeType.systemDefault;
    }
  }
}

ThemeMode themeTypeToThemeMode(ThemeType themeType) {
  switch (themeType) {
    case ThemeType.systemDefault:
      return ThemeMode.system;
    case ThemeType.light:
      return ThemeMode.light;
    case ThemeType.dark:
      return ThemeMode.dark;
  }
}
