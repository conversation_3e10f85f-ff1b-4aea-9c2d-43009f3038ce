enum PermissionType {
  allUsers,
  userDetails,
  supportChat,
  feedbacks,
  deletedReports,
  nugget,
  adminPanel,
  segments,
  viewEmail,
  pitrRecovery;

  String toReadableString() {
    switch (this) {
      case PermissionType.allUsers:
        return 'Users list';
      case PermissionType.userDetails:
        return 'User info';
      case PermissionType.supportChat:
        return 'Support chat';
      case PermissionType.feedbacks:
        return 'Feedbacks';
      case PermissionType.deletedReports:
        return 'Deleted report list';
      case PermissionType.nugget:
        return 'Nugget';
      case PermissionType.adminPanel:
        return 'Admin panel';
      case PermissionType.segments:
        return 'Segments';
      case PermissionType.viewEmail:
        return 'View Email';
      case PermissionType.pitrRecovery:
        return 'PITR Recovery';
    }
  }

  // Get permission string
  String get permissionString {
    switch (this) {
      case PermissionType.allUsers:
        return 'allUsers';
      case PermissionType.userDetails:
        return 'userDetails';
      case PermissionType.supportChat:
        return 'supportChat';
      case PermissionType.feedbacks:
        return 'feedbacks';
      case PermissionType.deletedReports:
        return 'deletedReports';
      case PermissionType.nugget:
        return 'nugget';
      case PermissionType.adminPanel:
        return 'adminPanel';
      case PermissionType.segments:
        return 'segments';
      case PermissionType.viewEmail:
        return 'viewEmail';
      case PermissionType.pitrRecovery:
        return 'pitrRecovery';
    }
  }
}
