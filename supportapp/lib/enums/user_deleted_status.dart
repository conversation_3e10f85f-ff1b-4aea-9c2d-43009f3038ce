// ignore_for_file: constant_identifier_names

enum UserDeletedStatus {
  deleted,
  deleted_u,
  deleted_a,
  remove_u,
  remove_a;

  String toReadableString() {
    switch (this) {
      case UserDeletedStatus.deleted:
      case UserDeletedStatus.deleted_u:
        return 'Deleted by user';
      case UserDeletedStatus.deleted_a:
        return 'Deleted by admin';
      case UserDeletedStatus.remove_u:
        return 'Deleted by user';
      case UserDeletedStatus.remove_a:
        return 'Deleted by admin';
    }
  }
}
