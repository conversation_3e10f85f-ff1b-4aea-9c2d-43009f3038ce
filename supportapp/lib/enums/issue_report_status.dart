import 'package:mevolvesupport/enums/subtab_type.dart';

enum IssueReportStatus {
  toBeReviewed,
  inReview,
  inProcess,
  resolved,
  none;

  String toReadableString() {
    switch (this) {
      case IssueReportStatus.toBeReviewed:
        return 'To be Reviewed';
      case IssueReportStatus.inReview:
        return 'In review';
      case IssueReportStatus.inProcess:
        return 'In process';
      case IssueReportStatus.resolved:
        return 'Resolved';
      default:
        return 'none';
    }
  }

  String toQueryString() {
    switch (this) {
      case IssueReportStatus.toBeReviewed:
        return 'toBeReviewed';
      case IssueReportStatus.inReview:
        return 'inReview';
      case IssueReportStatus.inProcess:
        return 'inProcess';
      case IssueReportStatus.resolved:
        return 'resolved';
      default:
        return 'none';
    }
  }

  SubtabType toSubtabType() {
    switch (this) {
      case IssueReportStatus.toBeReviewed:
        return SubtabType.toBeReviwedReport;
      case IssueReportStatus.inReview:
        return SubtabType.inReviewReport;
      case IssueReportStatus.inProcess:
        return SubtabType.inProcessReport;
      case IssueReportStatus.resolved:
        return SubtabType.resolvedReport;
      default:
        return SubtabType.toBeReviwedReport;
    }
  }
}
