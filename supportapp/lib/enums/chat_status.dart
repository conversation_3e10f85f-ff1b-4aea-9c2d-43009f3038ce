import 'package:mevolvesupport/enums/subtab_type.dart';

enum ChatStatus {
  notReplied,
  replied,
  resolved,
  clarify,
  none;

  String toReadableString() {
    switch (this) {
      case ChatStatus.notReplied:
        return 'Not replied';
      case ChatStatus.replied:
        return 'Replied';
      case ChatStatus.resolved:
        return 'Resolved';
      case ChatStatus.clarify:
        return 'Clarify';
      default:
        return 'Not replied';
    }
  }

  String toQueryString() {
    switch (this) {
      case ChatStatus.notReplied:
        return 'notReplied';
      case ChatStatus.replied:
        return 'replied';
      case ChatStatus.resolved:
        return 'resolved';
      case ChatStatus.clarify:
        return 'clarify';
      default:
        return 'notReplied';
    }
  }

  SubtabType toSubtabType() {
    switch (this) {
      case ChatStatus.notReplied:
        return SubtabType.notRepliedChat;
      case ChatStatus.replied:
        return SubtabType.repliedChat;
      case ChatStatus.resolved:
        return SubtabType.resolvedChat;
      case ChatStatus.clarify:
        return SubtabType.clarifyChat;
      default:
        return SubtabType.notRepliedChat;
    }
  }
}
