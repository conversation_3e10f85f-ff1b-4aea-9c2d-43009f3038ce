enum SnoozeType {
  five,
  ten,
  fifteen,
  twenty,
  twentyFive,
  thirty;

  @override
  String toString() {
    switch (this) {
      case SnoozeType.five:
        return '5 mins';
      case SnoozeType.ten:
        return '10 mins';
      case SnoozeType.fifteen:
        return '15 mins';
      case SnoozeType.twenty:
        return '20 mins';
      case SnoozeType.twentyFive:
        return '25 mins';
      case SnoozeType.thirty:
        return '30 mins';
    }
  }
}
