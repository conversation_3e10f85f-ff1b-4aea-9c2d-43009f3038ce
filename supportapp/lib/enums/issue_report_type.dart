enum IssueReportType {
  technicalIssue,
  compatibility,
  recentChanges,
  performance,
  security,
  usability,
  subscription,
  other,
  ;

  String toReadableString() {
    switch (this) {
      case IssueReportType.technicalIssue:
        return 'Technical issue';
      case IssueReportType.compatibility:
        return 'Device compatibility';
      case IssueReportType.recentChanges:
        return 'Recent changes to the app';
      case IssueReportType.performance:
        return 'Performance';
      case IssueReportType.security:
        return 'Security';
      case IssueReportType.usability:
        return 'Usability';
      case IssueReportType.subscription:
        return 'Subscription';
      case IssueReportType.other:
        return 'Other';
    }
  }

  String toQueryString() {
    switch (this) {
      case IssueReportType.technicalIssue:
        return 'technicalIssue';
      case IssueReportType.compatibility:
        return 'compatibility';
      case IssueReportType.recentChanges:
        return 'recentChanges';
      case IssueReportType.performance:
        return 'performance';
      case IssueReportType.security:
        return 'security';
      case IssueReportType.usability:
        return 'usability';
      case IssueReportType.subscription:
        return 'subscription';
      case IssueReportType.other:
        return 'other';
    }
  }
}
