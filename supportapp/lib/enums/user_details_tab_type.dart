import 'package:mevolvesupport/generated/assets.gen.dart';

/// Enum representing the different tabs available in the user details panel
enum UserDetailsTabType {
  /// User information and profile details
  userDetails,

  /// Chat messages and conversations
  messages,

  /// User feedback and reviews
  feedback,

  /// Subscription and billing information
  subscription,

  /// User activity and history
  activity,
}

extension UserDetailsTabTypeExtension on UserDetailsTabType {
  /// Get the display name for the tab
  String get displayName {
    switch (this) {
      case UserDetailsTabType.userDetails:
        return 'User Details';
      case UserDetailsTabType.messages:
        return 'Messages';
      case UserDetailsTabType.feedback:
        return 'Feedback';
      case UserDetailsTabType.subscription:
        return 'Subscription';
      case UserDetailsTabType.activity:
        return 'Activity';
    }
  }

  /// Get the icon path for the tab
  String get iconPath {
    switch (this) {
      case UserDetailsTabType.userDetails:
        return Assets.svg.personIcon.path;
      case UserDetailsTabType.messages:
        return Assets.svg.messagesIcon.path;
      case UserDetailsTabType.feedback:
        return Assets.svg.feedbackIcon.path;
      case UserDetailsTabType.subscription:
        return Assets.svg.starCircleIcon.path;
      case UserDetailsTabType.activity:
        return Assets.svg.clockIcon.path;
    }
  }

  /// Get the tab index (for backwards compatibility)
  int get index {
    switch (this) {
      case UserDetailsTabType.userDetails:
        return 0;
      case UserDetailsTabType.messages:
        return 1;
      case UserDetailsTabType.feedback:
        return 2;
      case UserDetailsTabType.subscription:
        return 3;
      case UserDetailsTabType.activity:
        return 4;
    }
  }
}
