import 'package:mevolvesupport/constants/extensions.dart';

enum AllUsersFilterType {
  subscriptionType,
  superSubscription,
  subscriptionState,
  deleted,
  subscriptionExpDate,
  createdAt,
  chatStatus,
  reportStatus,
  appTheme,
  isPasscodeEnabled,
  isBiometricEnabled,
  isDailyAgendaEmail,
  isDailyAgendaMobile,
  searchEmailOrUid;

  String toReadableString() {
    switch (this) {
      case AllUsersFilterType.subscriptionType:
        return 'Plan';
      case AllUsersFilterType.superSubscription:
        return 'Super Subscription';
      case AllUsersFilterType.subscriptionState:
        return 'Subscription';
      case AllUsersFilterType.deleted:
        return 'Deleted';
      case AllUsersFilterType.subscriptionExpDate:
        return 'Expiring in';
      case AllUsersFilterType.createdAt:
        return 'Since';
      case AllUsersFilterType.chatStatus:
        return 'Chat';
      case AllUsersFilterType.reportStatus:
        return 'Report issues';
      case AllUsersFilterType.appTheme:
        return 'Theme';
      case AllUsersFilterType.isPasscodeEnabled:
        return 'Mevolve PIN';
      case AllUsersFilterType.isBiometricEnabled:
        return 'Biometrics';
      case AllUsersFilterType.isDailyAgendaEmail:
        return 'Daily agenda via email';
      case AllUsersFilterType.isDailyAgendaMobile:
        return 'Daily agenda via notification';
      case AllUsersFilterType.searchEmailOrUid:
        return 'Search by Email or UID';
    }
  }

  String toQueryString() {
    switch (this) {
      case AllUsersFilterType.subscriptionType:
        return 'subscriptionType';
      case AllUsersFilterType.superSubscription:
        return 'superSubscription';
      case AllUsersFilterType.subscriptionState:
        return 'subscriptionState';
      case AllUsersFilterType.deleted:
        return 'userDeletedStatus';
      case AllUsersFilterType.subscriptionExpDate:
        return 'subscriptionExpDate';
      case AllUsersFilterType.createdAt:
        return 'createdAt';
      case AllUsersFilterType.chatStatus:
        return 'chatStatus';
      case AllUsersFilterType.reportStatus:
        return 'reportStatus';
      case AllUsersFilterType.appTheme:
        return 'appTheme';
      case AllUsersFilterType.isPasscodeEnabled:
        return 'isPasscodeEnabled';
      case AllUsersFilterType.isBiometricEnabled:
        return 'isBiometricEnabled';
      case AllUsersFilterType.isDailyAgendaEmail:
        return 'isDailyAgendaEmail';
      case AllUsersFilterType.isDailyAgendaMobile:
        return 'isDailyAgendaMobile';
      case AllUsersFilterType.searchEmailOrUid:
        return 'searchEmailOrUid';
    }
  }

  List<TypeOption> getTypeOptions() {
    switch (this) {
      case AllUsersFilterType.subscriptionType:
        return [
          TypeOption(name: 'Basic', value: 'basic'),
          TypeOption(name: 'Pro Monthly', value: 'pro-monthly'),
          TypeOption(name: 'Pro Yearly', value: 'pro-yearly'),
          TypeOption(name: 'Plus Monthly', value: 'plus-monthly'),
          TypeOption(name: 'Plus Yearly', value: 'plus-yearly'),
          // TypeOption(name: 'Ultra Monthly', value: 'ultra-monthly'),
          // TypeOption(name: 'Ultra Yearly', value: 'ultra-yearly'),
          // TypeOption(name: 'Custom', value: 'custom'),
        ];
      case AllUsersFilterType.superSubscription:
        return [
          TypeOption(name: 'None', value: null),
          TypeOption(name: 'Basic', value: 'basic'),
          TypeOption(name: 'Pro', value: 'pro'),
          TypeOption(name: 'Plus', value: 'plus'),
        ];
      case AllUsersFilterType.subscriptionState:
        return [
          TypeOption(name: 'Free', value: 'none'),
          TypeOption(name: 'Subscribed', value: 'subscribed'),
          TypeOption(name: 'Expired', value: 'subscriptionExpired'),
        ];
      case AllUsersFilterType.deleted:
        return [
          TypeOption(name: 'Deleted by user', value: 'remove_u'),
          TypeOption(name: 'Deleted by admin', value: 'delete_a'),
        ];
      case AllUsersFilterType.subscriptionExpDate:
        return [
          TypeOption(name: 'None', value: null),
          TypeOption(
            name: '1 day',
            value: DateTime.now()
                .add(const Duration(days: 1))
                .onlyDateWithLastSecond()
                .toIso8601String(),
          ),
          TypeOption(
            name: '3 days',
            value: DateTime.now()
                .add(const Duration(days: 3))
                .onlyDateWithLastSecond()
                .toIso8601String(),
          ),
          TypeOption(
            name: '7 days',
            value: DateTime.now()
                .add(const Duration(days: 7))
                .onlyDateWithLastSecond()
                .toIso8601String(),
          ),
          TypeOption(
            name: '15 days',
            value: DateTime.now()
                .add(const Duration(days: 15))
                .onlyDateWithLastSecond()
                .toIso8601String(),
          ),
          TypeOption(
            name: '30 days',
            value: DateTime.now()
                .add(const Duration(days: 30))
                .onlyDateWithLastSecond()
                .toIso8601String(),
          ),
          TypeOption(
            name: '3 months',
            value: DateTime.now()
                .add(const Duration(days: 90))
                .onlyDateWithLastSecond()
                .toIso8601String(),
          ),
          TypeOption(
            name: 'More than 1 year',
            value: DateTime.now()
                .add(const Duration(days: 365))
                .onlyDateWithLastSecond()
                .toIso8601String(),
          ),
        ];
      case AllUsersFilterType.createdAt:
        return [
          TypeOption(name: 'None', value: null),
          TypeOption(
            name: '3 months',
            value: DateTime.now()
                .subtract(const Duration(days: 90))
                .onlyDate()
                .toIso8601String(),
          ),
          TypeOption(
            name: '6 months',
            value: DateTime.now()
                .subtract(const Duration(days: 180))
                .onlyDate()
                .toIso8601String(),
          ),
          TypeOption(
            name: '1 year',
            value: DateTime.now()
                .subtract(const Duration(days: 365))
                .onlyDate()
                .toIso8601String(),
          ),
          TypeOption(
            name: 'More than 1 year',
            value: DateTime.now()
                .subtract(const Duration(days: 366))
                .onlyDate()
                .toIso8601String(),
          ),
        ];
      case AllUsersFilterType.chatStatus:
        return [
          TypeOption(name: 'Not Replied', value: 'notReplied'),
          TypeOption(name: 'Clarify', value: 'clarify'),
          TypeOption(name: 'Replied', value: 'replied'),
          TypeOption(name: 'Resolved', value: 'resolved'),
        ];
      case AllUsersFilterType.reportStatus:
        return [
          TypeOption(name: 'To be review', value: 'toBeReview'),
          TypeOption(name: 'In review', value: 'inReview'),
          TypeOption(name: 'In process', value: 'inProcess'),
          TypeOption(name: 'Resolved', value: 'resolved'),
        ];

      case AllUsersFilterType.appTheme:
        return [
          TypeOption(name: 'None', value: null),
          TypeOption(name: 'System default', value: 'sysDefault'),
          TypeOption(name: 'Light', value: 'light'),
          TypeOption(name: 'Dark', value: 'dark'),
        ];
      case AllUsersFilterType.isPasscodeEnabled:
        return [
          TypeOption(name: 'None', value: null),
          TypeOption(name: 'Enabled', value: true),
          TypeOption(name: 'Disabled', value: false),
        ];
      case AllUsersFilterType.isBiometricEnabled:
        return [
          TypeOption(name: 'None', value: null),
          TypeOption(name: 'Enabled', value: true),
          TypeOption(name: 'Disabled', value: false),
        ];
      case AllUsersFilterType.isDailyAgendaEmail:
        return [
          TypeOption(name: 'None', value: null),
          TypeOption(name: 'Enabled', value: true),
          TypeOption(name: 'Disabled', value: false),
        ];
      case AllUsersFilterType.isDailyAgendaMobile:
        return [
          TypeOption(name: 'None', value: null),
          TypeOption(name: 'Enabled', value: true),
          TypeOption(name: 'Disabled', value: false),
        ];
      default:
        return [];
    }
  }

  bool isMultiChoice() {
    switch (this) {
      case AllUsersFilterType.subscriptionType:
        return true;
      case AllUsersFilterType.superSubscription:
        return true;
      case AllUsersFilterType.subscriptionState:
        return true;
      case AllUsersFilterType.deleted:
        return true;
      case AllUsersFilterType.subscriptionExpDate:
        return false;
      case AllUsersFilterType.createdAt:
        return false;
      case AllUsersFilterType.chatStatus:
        return true;
      case AllUsersFilterType.reportStatus:
        return true;
      case AllUsersFilterType.appTheme:
        return false;
      case AllUsersFilterType.isPasscodeEnabled:
        return false;
      case AllUsersFilterType.isBiometricEnabled:
        return false;
      case AllUsersFilterType.isDailyAgendaEmail:
        return false;
      case AllUsersFilterType.isDailyAgendaMobile:
        return false;
      case AllUsersFilterType.searchEmailOrUid:
        return false;
    }
  }
}

class TypeOption {
  TypeOption({
    required this.name,
    required this.value,
  });

  final String name;
  final dynamic value;
}
