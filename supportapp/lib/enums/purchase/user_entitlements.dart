enum MeUserEntitlement {
  free,
  basic,
  pro,
  plus;

  String toReadableString() {
    switch (this) {
      case MeUserEntitlement.free:
        return 'Free';
      case MeUserEntitlement.basic:
        return 'Basic';
      case MeUserEntitlement.pro:
        return 'Pro';
      case MeUserEntitlement.plus:
        return 'Plus';
    }
  }

  String getSuperString() {
    switch (this) {
      case MeUserEntitlement.free:
        return 'None';
      case MeUserEntitlement.basic:
        return 'Basic';
      case MeUserEntitlement.pro:
        return 'Pro';
      case MeUserEntitlement.plus:
        return 'Plus';
    }
  }

  dynamic toQueryValue() {
    switch (this) {
      case MeUserEntitlement.free:
        return null;
      case MeUserEntitlement.basic:
        return 'basic';
      case MeUserEntitlement.pro:
        return 'pro';
      case MeUserEntitlement.plus:
        return 'plus';
    }
  }

  int getMaxStorage() {
    switch (this) {
      case MeUserEntitlement.free:
        return 0;
      case MeUserEntitlement.basic:
        return 1;
      case MeUserEntitlement.pro:
        return 10;
      case MeUserEntitlement.plus:
        return 100;
    }
  }

  int getSubInteger() {
    switch (this) {
      case MeUserEntitlement.free:
        return 0;
      case MeUserEntitlement.basic:
        return 1;
      case MeUserEntitlement.pro:
        return 2;
      case MeUserEntitlement.plus:
        return 3;
    }
  }
}
