enum SubscriptionType {
  custom,
  monthly,
  yearly;

  String toReadableString() {
    switch (this) {
      case SubscriptionType.custom:
        return 'Custom';
      case SubscriptionType.monthly:
        return 'Monthly';
      case SubscriptionType.yearly:
        return 'Yearly';
    }
  }

  String toQueryString() {
    switch (this) {
      case SubscriptionType.custom:
        return 'custom';
      case SubscriptionType.monthly:
        return 'monthly';
      case SubscriptionType.yearly:
        return 'yearly';
    }
  }
}
