enum SubscriptionState {
  none,
  trial,
  trialExpired,
  subscribed,
  subscriptionExpired;

  String toReadableString() {
    switch (this) {
      case SubscriptionState.none:
        return 'Basic';
      case SubscriptionState.trial:
        return 'Trial';
      case SubscriptionState.trialExpired:
        return 'Trial expired';
      case SubscriptionState.subscribed:
        return 'Subscribed';
      case SubscriptionState.subscriptionExpired:
        return 'Subscription expired';
    }
  }

  String toQueryString() {
    switch (this) {
      case SubscriptionState.none:
        return 'none';
      case SubscriptionState.trial:
        return 'trial';
      case SubscriptionState.trialExpired:
        return 'trialExpired';
      case SubscriptionState.subscribed:
        return 'subscribed';
      case SubscriptionState.subscriptionExpired:
        return 'subscriptionExpired';
    }
  }
}
