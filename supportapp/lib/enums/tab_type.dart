import 'package:mevolvesupport/generated/assets.gen.dart';

enum TabType {
  allUsers,
  supportChat,
  feedback,
  deleteReports,
  segments,
  pitrRecovery,
  adminPanel,
  schema;

  String toReadableString() {
    switch (this) {
      case TabType.allUsers:
        return 'Users list';
      case TabType.supportChat:
        return 'Support chat';
      case TabType.feedback:
        return 'Feedback';
      case TabType.deleteReports:
        return 'Deleted report list';
      case TabType.segments:
        return 'Segments';
      case TabType.pitrRecovery:
        return 'PITR Recovery';
      case TabType.adminPanel:
        return 'Admin panel';
      case TabType.schema:
        return 'Schema';
    }
  }

  String getTabIcon() {
    switch (this) {
      case TabType.allUsers:
        return Assets.svg.personIcon.path;
      case TabType.supportChat:
        return Assets.svg.messagesIcon.path;
      case TabType.feedback:
        return Assets.svg.feedbackIcon.path;
      case TabType.deleteReports:
        return Assets.svg.trashIcon.path;
      case TabType.pitrRecovery:
        return Assets.svg.warningTriangle.path;
      case TabType.segments:
        return Assets.svg.segmentIcon.path;
      case TabType.adminPanel:
        return Assets.svg.adminPanelIcon.path;
      case TabType.schema:
        return Assets.svg.dbIcon.path;
    }
  }
}
