enum SoundType {
  none,
  mevolve1,
  mevolve2,
  mevolve3,
  mevolve4,
  mevolve5;

  @override
  String toString() {
    switch (this) {
      case SoundType.none:
        return 'None';
      case SoundType.mevolve1:
        return 'Mevolve 1';
      case SoundType.mevolve2:
        return 'Mevolve 2';
      case SoundType.mevolve3:
        return 'Mevolve 3';
      case SoundType.mevolve4:
        return 'Mevolve 4';
      case SoundType.mevolve5:
        return 'Mevolve 5';
    }
  }
}
