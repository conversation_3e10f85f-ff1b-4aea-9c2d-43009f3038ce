enum ActivityFilterType {
  eventName,
  eventType,
  startFrom;

  String toReadableString() {
    switch (this) {
      case ActivityFilterType.eventName:
        return 'Event Name';
      case ActivityFilterType.eventType:
        return 'Event Type';
      case ActivityFilterType.startFrom:
        return 'Start From';
    }
  }

  String toQueryString() {
    switch (this) {
      case ActivityFilterType.eventName:
        return 'eventName';
      case ActivityFilterType.eventType:
        return 'eventType';
      case ActivityFilterType.startFrom:
        return 'startFrom';
    }
  }

  List<TypeOption> getTypeOptions() {
    switch (this) {
      case ActivityFilterType.eventType:
        return [
          TypeOption(name: 'All', value: null),
          TypeOption(name: 'Marketing', value: 'Marketing'),
          TypeOption(name: 'Product', value: 'Product'),
          TypeOption(name: 'User', value: 'User'),
          TypeOption(name: 'System', value: 'System'),
        ];
      case ActivityFilterType.startFrom:
        return [
          TypeOption(name: 'All Time', value: null),
          TypeOption(
            name: 'Last 7 Days',
            value: DateTime.now().subtract(const Duration(days: 7)),
          ),
          TypeOption(
            name: 'Last 30 Days',
            value: DateTime.now().subtract(const Duration(days: 30)),
          ),
          TypeOption(
            name: 'Last 90 Days',
            value: DateTime.now().subtract(const Duration(days: 90)),
          ),
          TypeOption(
            name: 'Custom...',
            value: 'custom',
          ),
        ];
      case ActivityFilterType.eventName:
        return [
          TypeOption(name: 'None', value: null),
        ];
    }
  }

  bool isMultiChoice() {
    switch (this) {
      case ActivityFilterType.eventType:
        return false;
      case ActivityFilterType.startFrom:
        return false;
      case ActivityFilterType.eventName:
        return false;
    }
  }
}

class TypeOption {
  TypeOption({
    required this.name,
    required this.value,
  });

  final String name;
  final dynamic value;
}
