// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_qa.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyAWQgvCspxxygBTUxl29XLM_hUY8AhzR88',
    appId: '1:922862417651:web:5955ee70cbfa7f727fe986',
    messagingSenderId: '922862417651',
    projectId: 'mevolve-qa',
    authDomain: 'mevolve-qa.firebaseapp.com',
    databaseURL:
        'https://mevolve-qa-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'mevolve-qa.firebasestorage.app',
    measurementId: 'G-KX5XP9KZPW',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyDTMWlEdtmzf68dN8ajMYF9fpwrjf3CZT8',
    appId: '1:922862417651:android:bf208bb7ac8258387fe986',
    messagingSenderId: '922862417651',
    projectId: 'mevolve-qa',
    databaseURL:
        'https://mevolve-qa-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'mevolve-qa.firebasestorage.app',
  );
}
