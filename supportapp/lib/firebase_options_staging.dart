// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options_staging.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for ios - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCQQrhCu_s_x2yXbvkZxZtK-ynxJ28dqmg',
    appId: '1:953131275065:web:645ba3f92a71ee290a8ee8',
    messagingSenderId: '953131275065',
    projectId: 'mevolve-staging',
    authDomain: 'mevolve-staging.firebaseapp.com',
    databaseURL:
        'https://mevolve-staging-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'mevolve-staging.appspot.com',
    measurementId: 'G-QK7LZ9KX1J',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyCwWSvrZ0BoNzXanPjAKFV_-cpgSmA4uMI',
    appId: '1:953131275065:android:4643e68b236c29910a8ee8',
    messagingSenderId: '953131275065',
    projectId: 'mevolve-staging',
    databaseURL:
        'https://mevolve-staging-default-rtdb.europe-west1.firebasedatabase.app',
    storageBucket: 'mevolve-staging.appspot.com',
  );
}
