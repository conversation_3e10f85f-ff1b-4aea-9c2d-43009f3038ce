import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'feedbacks_updates_state.dart';

class FeedbacksUpdatesCubit extends Cubit<FeedbacksUpdatesState> {
  FeedbacksUpdatesCubit(this._databaseRepository)
      : super(FeedbacksUpdatesInitial());

  final DatabaseRepository _databaseRepository;
  StreamSubscription<List<FeedbackModel>>? _listenFeedbacksUpdates;
  List<FeedbackModel> _currentFeedbacks = [];

  void listenFeedbacksUpdates({
    required List<FeedbackModel> currentFeedbacks,
    required List<String>? currentByFilter,
  }) {
    _currentFeedbacks = currentFeedbacks;
    _listenFeedbacksUpdates?.cancel();
    _listenFeedbacksUpdates = _databaseRepository
        .listenFeedbacks(
      byFilter: currentByFilter,
    )
        .listen((feedbacks) {
      if (feedbacks.isNotEmpty &&
          feedbacks.first.id != _currentFeedbacks.first.id) {
        emit(FeedbacksUpdatesAvailable());
      }
    });
  }

  @override
  Future<void> close() {
    _listenFeedbacksUpdates?.cancel();
    return super.close();
  }
}
