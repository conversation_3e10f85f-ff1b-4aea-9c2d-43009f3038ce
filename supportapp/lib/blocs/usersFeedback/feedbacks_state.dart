part of 'feedbacks_bloc.dart';

abstract class FeedbacksState extends Equatable {
  const FeedbacksState();

  @override
  List<Object> get props => [];
}

class FeedbacksInitial extends FeedbacksState {}

class FeedbacksLoading extends FeedbacksState {}

class FeedbacksLoaded extends FeedbacksState {
  const FeedbacksLoaded({
    required this.feedbacks,
    this.isUpdate = false,
    this.feedbacksCount = 0,
  });
  final List<FeedbackModel> feedbacks;
  final bool isUpdate;
  final int feedbacksCount;

  @override
  List<Object> get props => [feedbacks, isUpdate];
}

class FeedbacksError extends FeedbacksState {}

class FeedbacksEmpty extends FeedbacksState {}
