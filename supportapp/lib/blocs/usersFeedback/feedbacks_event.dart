part of 'feedbacks_bloc.dart';

abstract class FeedbacksEvent extends Equatable {
  const FeedbacksEvent();

  @override
  List<Object> get props => [];
}

class LoadFeedbacks extends FeedbacksEvent {
  const LoadFeedbacks({
    this.byFilter = const [],
  });

  final List<String>? byFilter;
}

class LoadMoreFeedbacks extends FeedbacksEvent {
  const LoadMoreFeedbacks({
    this.lastReportDateTime,
    this.byFilter = const [],
  });

  final DateTime? lastReportDateTime;
  final List<String>? byFilter;
}

class UpdateFeedbacks extends FeedbacksEvent {
  const UpdateFeedbacks();
}
