import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/issue_report_status.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

part 'feedbacks_event.dart';
part 'feedbacks_state.dart';

class FeedbacksBloc extends Bloc<FeedbacksEvent, FeedbacksState> {
  FeedbacksBloc(this._databaseRepository) : super(FeedbacksInitial()) {
    on<LoadFeedbacks>(_onLoadFeedbacks);
    on<LoadMoreFeedbacks>(_onLoadMoreFeedbacks);
    on<UpdateFeedbacks>(_onUpdateFeedbacks);
  }
  final DatabaseRepository _databaseRepository;

  IssueReportStatus currentIssueReportStatus = IssueReportStatus.toBeReviewed;
  List<String>? currentByFilter = [];

  DateTime? _lastLocalDateTime;
  DateTime? _lastCloudDateTime;
  StreamSubscription<List<FeedbackModel>>? _listenNewFeedbacks;
  int id = 1;
  int _feedbacksCount = 0;

  @override
  Future<void> close() {
    _listenNewFeedbacks?.cancel();
    return super.close();
  }

  void resumeListenNewReports() {
    _listenNewFeedbacks?.pause();
  }

  void pauseListenNewReports() {
    _listenNewFeedbacks?.resume();
  }

  void _onLoadFeedbacks(
    LoadFeedbacks event,
    Emitter<FeedbacksState> emit,
  ) async {
    try {
      id = 1;
      await _listenNewFeedbacks?.cancel();
      emit(FeedbacksLoading());
      currentByFilter = event.byFilter;
      _feedbacksCount = await _databaseRepository.fetchFeedbacksCount(
        byFilter: currentByFilter,
      );
      final reports = await _databaseRepository.fetchFeedbacks(
        lastCloudDateTime: _lastCloudDateTime,
        byFilter: currentByFilter,
      );
      if (reports.isNotEmpty) {
        _lastLocalDateTime = reports.last.localUpdatedAt;
        _lastCloudDateTime = reports.last.cloudUpdatedAt;
      }
      emit(
        FeedbacksLoaded(
          feedbacks: reports.toList(),
          isUpdate: false,
          feedbacksCount: _feedbacksCount,
        ),
      );
    } catch (e) {
      Log.e('Error on load feedbacks: $e');
      emit(FeedbacksError());
    }
  }

  void _onLoadMoreFeedbacks(
    LoadMoreFeedbacks event,
    Emitter<FeedbacksState> emit,
  ) async {
    try {
      await _listenNewFeedbacks?.cancel();
      final reports = await _databaseRepository.fetchFeedbacks(
        byFilter: currentByFilter,
        lastCloudDateTime: _lastCloudDateTime,
        lastLocalDateTime: _lastLocalDateTime,
      );

      if (reports.isNotEmpty) {
        _lastLocalDateTime = reports.last.localUpdatedAt;
        _lastCloudDateTime = reports.last.cloudUpdatedAt;

        final currentReports = (state as FeedbacksLoaded).feedbacks;
        final totalReports = [
          ...currentReports,
          ...reports,
        ];
        emit(
          FeedbacksLoaded(
            feedbacks: totalReports,
            isUpdate: false,
            feedbacksCount: _feedbacksCount,
          ),
        );
      }
    } catch (e) {
      Log.e('Error on load feedbacks: $e');
      emit(FeedbacksError());
    }
  }

  void _onUpdateFeedbacks(
    UpdateFeedbacks event,
    Emitter<FeedbacksState> emit,
  ) {
    add(const LoadFeedbacks());
  }
}
