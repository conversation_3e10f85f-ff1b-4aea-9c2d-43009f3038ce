part of 'snippets_metadata_bloc.dart';

abstract class SnippetsMetadataEvent extends Equatable {
  const SnippetsMetadataEvent();

  @override
  List<Object> get props => [];
}

class LoadAllSnippetsMetadata extends SnippetsMetadataEvent {
  const LoadAllSnippetsMetadata();
}

class LoadSnippetsMetadata extends SnippetsMetadataEvent {
  const LoadSnippetsMetadata({
    required this.status,
    this.byFilter = const SnippetsMetadataFilter(),
  });

  final SnippetMetadataType status;
  final SnippetsMetadataFilter byFilter;
}

class LoadMoreSnippetsMetadata extends SnippetsMetadataEvent {
  const LoadMoreSnippetsMetadata({
    this.status,
    this.byFilter,
  });

  final SnippetMetadataType? status;
  final SnippetsMetadataFilter? byFilter;
}

class NewSnippetsMetadataLoaded extends SnippetsMetadataEvent {
  const NewSnippetsMetadataLoaded({
    required this.numOfUpdates,
    required this.snippets,
    this.snippetsCount = const SnippetsMetadataCount(
      screen: 0,
      userSegment: 0,
      position: 0,
    ),
  });

  final int numOfUpdates;
  final List<SnippetsMetadata> snippets;
  final SnippetsMetadataCount snippetsCount;
}

class UpdateSnippetsMetadata extends SnippetsMetadataEvent {
  const UpdateSnippetsMetadata();
}
