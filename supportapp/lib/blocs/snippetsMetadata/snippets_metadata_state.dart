part of 'snippets_metadata_bloc.dart';

abstract class NuggetsMetadataState extends Equatable {
  const NuggetsMetadataState();

  @override
  List<Object> get props => [];
}

class SnippetsMetadataInitial extends NuggetsMetadataState {}

class SnippetsMetadataLoading extends NuggetsMetadataState {}

class SnippetsMetadataLoaded extends NuggetsMetadataState {
  const SnippetsMetadataLoaded({
    required this.snippets,
    this.isUpdate = false,
    this.numOfUpdates = 0,
    this.snippetsCount = const SnippetsMetadataCount(
      screen: 0,
      userSegment: 0,
      position: 0,
    ),
  });

  final List<SnippetsMetadata> snippets;
  final bool isUpdate;
  final int numOfUpdates;
  final SnippetsMetadataCount snippetsCount;

  SnippetsMetadataLoaded copyWith({
    List<SnippetsMetadata>? snippets,
    bool? isUpdate,
    int? numOfUpdates,
    SnippetsMetadataCount? snippetsCount,
  }) {
    return SnippetsMetadataLoaded(
      snippets: snippets ?? this.snippets,
      isUpdate: isUpdate ?? this.isUpdate,
      numOfUpdates: numOfUpdates ?? this.numOfUpdates,
      snippetsCount: snippetsCount ?? this.snippetsCount,
    );
  }
}

class SnippetsMetadataError extends NuggetsMetadataState {}
