import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:mevolvesupport/enums/snippet_metadata_type.dart';
import 'package:mevolvesupport/models/snippets_metadata.dart';
import 'package:mevolvesupport/models/snippets_metadata_count.dart';
import 'package:mevolvesupport/models/snippets_metadata_filter.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'snippets_metadata_event.dart';
part 'snippets_metadata_state.dart';

class NuggetsMetadataBloc
    extends Bloc<SnippetsMetadataEvent, NuggetsMetadataState> {
  NuggetsMetadataBloc(this._databaseRepository)
      : super(SnippetsMetadataInitial()) {
    on<LoadAllSnippetsMetadata>(_onLoadAllSnippetsMetadata);
    on<LoadSnippetsMetadata>(_onLoadSnippetsMetadata);
    on<LoadMoreSnippetsMetadata>(_onLoadMoreSnippetsMetadata);
    on<NewSnippetsMetadataLoaded>(_onNewSnippetsMetadataLoaded);
    on<UpdateSnippetsMetadata>(_onUpdateSnippetsMetadata);
  }
  final DatabaseRepository _databaseRepository;

  DateTime? _lastSnippetDateTime;
  int _listenLimit = 10;
  SnippetMetadataType currentSnippetCustomType =
      SnippetMetadataType.userSegment;
  SnippetsMetadataFilter currentByFilter = const SnippetsMetadataFilter();
  StreamSubscription<List<SnippetsMetadata>>? _listenSnippets;
  List<SnippetsMetadata> _allSnippetCustomList = [];
  List<SnippetsMetadata> _currentSnippetList = [];

  final SnippetsMetadataCount _snippetsCustomCount =
      const SnippetsMetadataCount(
    screen: 0,
    userSegment: 0,
    position: 0,
  );

  @override
  Future<void> close() {
    _listenSnippets?.cancel();
    return super.close();
  }

  void resumeListenChatUpdates() {
    _listenSnippets?.pause();
  }

  void pauseListenChatUpdates() {
    _listenSnippets?.resume();
  }

  Future<void> _onLoadAllSnippetsMetadata(
    LoadAllSnippetsMetadata event,
    Emitter<NuggetsMetadataState> emit,
  ) async {
    try {
      emit(SnippetsMetadataLoading());
      await _listenSnippets?.cancel();
      // _snippetsCount = await _databaseRepository.fetchChatsCount();
      _allSnippetCustomList =
          await _databaseRepository.fetchAllSnippetsMetadata();

      if (_allSnippetCustomList.isNotEmpty) {
        _lastSnippetDateTime = _allSnippetCustomList.fold<DateTime>(
          DateTime(2000),
          (max, e) => e.cloudUpdatedAt!.isAfter(max) ? e.cloudUpdatedAt! : max,
        );
        _currentSnippetList = _allSnippetCustomList.take(_listenLimit).toList();

        // Count snippets based on their status
        for (var snippet in _allSnippetCustomList) {
          if (snippet.type == SnippetMetadataType.screen) {
            _snippetsCustomCount.copyWith(
              screen: _snippetsCustomCount.screen + 1,
            );
          } else if (snippet.type == SnippetMetadataType.userSegment) {
            _snippetsCustomCount.copyWith(
              userSegment: _snippetsCustomCount.userSegment + 1,
            );
          } else if (snippet.type == SnippetMetadataType.position) {
            _snippetsCustomCount.copyWith(
              position: _snippetsCustomCount.position + 1,
            );
          }
        }

        emit(
          SnippetsMetadataLoaded(
            snippets: _currentSnippetList.take(10).toList(),
            snippetsCount: _snippetsCustomCount,
          ),
        );
      } else {
        emit(
          SnippetsMetadataLoaded(
            snippets: const [],
            snippetsCount: _snippetsCustomCount,
          ),
        );
      }
      _listenSnippets = _databaseRepository
          .listenSnippetsMetadata(
        type: currentSnippetCustomType,
        lastDateTime: _lastSnippetDateTime,
      )
          .listen((List<SnippetsMetadata> newSnippets) async {
        final oldSnippets = _allSnippetCustomList;
        if (!listEquals(oldSnippets, newSnippets)) {
          // _snippetsCount = await _databaseRepository.fetchChatsCount();
          final updateList = newSnippets.toList();
          updateList.removeWhere((chat) => oldSnippets.contains(chat));

          if (newSnippets.isNotEmpty) {
            add(
              NewSnippetsMetadataLoaded(
                snippets: (state as SnippetsMetadataLoaded).snippets,
                numOfUpdates: newSnippets.length,
                snippetsCount: _snippetsCustomCount,
              ),
            );
          }
        }
      });
    } catch (e) {
      emit(SnippetsMetadataError());
    }
  }

  Future<void> _onLoadSnippetsMetadata(
    LoadSnippetsMetadata event,
    Emitter<NuggetsMetadataState> emit,
  ) async {
    try {
      _listenSnippets?.cancel();
      emit(SnippetsMetadataLoading());
      currentSnippetCustomType = event.status;
      currentByFilter = event.byFilter;
      _listenSnippets?.cancel();

      if (_allSnippetCustomList.isNotEmpty) {
        _currentSnippetList = _allSnippetCustomList
            .where(
              (element) => (element.type == currentSnippetCustomType &&
                  (currentByFilter.status != null
                      ? element.status == currentByFilter.status
                      : true) &&
                  (currentByFilter.sid != null
                      ? element.updatedBy == currentByFilter.sid
                      : true)),
            )
            .toList();

        emit(
          SnippetsMetadataLoaded(
            snippets: _currentSnippetList.take(10).toList(),
            snippetsCount: _snippetsCustomCount,
          ),
        );
      }
    } catch (e) {
      emit(SnippetsMetadataError());
    }
  }

  Future<void> _onLoadMoreSnippetsMetadata(
    LoadMoreSnippetsMetadata event,
    Emitter<NuggetsMetadataState> emit,
  ) async {
    try {
      _listenLimit += 10;

      emit(
        SnippetsMetadataLoaded(
          snippets: _currentSnippetList.take(_listenLimit).toList(),
        ),
      );

      _listenSnippets?.cancel();
      _listenSnippets = _databaseRepository
          .listenSnippetsMetadata(
        type: event.status ?? currentSnippetCustomType,
        byFilter: event.byFilter ?? currentByFilter,
      )
          .listen((List<SnippetsMetadata> newChats) async {
        final oldChats = (state as SnippetsMetadataLoaded).snippets;
        if (!listEquals(oldChats, newChats)) {
          // _snippetsCount = await _databaseRepository.fetchChatsCount();
          final updateList = newChats.toList();
          updateList.removeWhere((chat) => oldChats.contains(chat));
          if (updateList.isNotEmpty) {
            add(
              NewSnippetsMetadataLoaded(
                snippets: (state as SnippetsMetadataLoaded).snippets,
                numOfUpdates: updateList.length,
                snippetsCount: _snippetsCustomCount,
              ),
            );
          }
        }
      });
    } catch (e) {
      emit(SnippetsMetadataError());
    }
  }

  void _onNewSnippetsMetadataLoaded(
    NewSnippetsMetadataLoaded event,
    Emitter<NuggetsMetadataState> emit,
  ) {
    emit(SnippetsMetadataLoading());
    emit(
      SnippetsMetadataLoaded(
        snippets: event.snippets.toList(),
        isUpdate: true,
        numOfUpdates: event.numOfUpdates,
        snippetsCount: event.snippetsCount,
      ),
    );
  }

  void _onUpdateSnippetsMetadata(
    UpdateSnippetsMetadata event,
    Emitter<NuggetsMetadataState> emit,
  ) {
    add(LoadSnippetsMetadata(status: currentSnippetCustomType));
  }
}
