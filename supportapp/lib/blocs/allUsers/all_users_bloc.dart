import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/allusers_filter_types.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/userslist_data.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'all_users_event.dart';

part 'all_users_state.dart';

class AllUsersBloc extends Bloc<AllUsersEvent, AllUsersState> {
  AllUsersBloc(this._databaseRepository) : super(AllUsersInitial()) {
    on<LoadUsersEvent>(_loadUsersToState);
    on<LoadMoreUsersEvent>(_loadMoreUsersToState);
  }

  final int limit = 10; // Number of documents to load per page
  DateTime? lastDocument;
  final DatabaseRepository _databaseRepository;
  String currentSelectedUser = '';
  Map<String, dynamic> userFilters = {};
  List<UserslistData> fullUsersList = [];
  int usersCount = 0;
  int id = 1;
  int start = 0;
  int loadedUsers = 0;

  Future<void> _loadUsersToState(
    AllUsersEvent event,
    Emitter<AllUsersState> emit,
  ) async {
    try {
      emit(AllUsersLoading());
      id = 1;
      userFilters = Map.from((event as LoadUsersEvent).userFilters);
      if (userFilters.containsKey(
            AllUsersFilterType.searchEmailOrUid.name,
          ) &&
          (userFilters[AllUsersFilterType.searchEmailOrUid.name] as String)
              .contains('@')) {
        final uid = await _databaseRepository.fetchUidFromEmail(
          userFilters[AllUsersFilterType.searchEmailOrUid.name],
        );
        userFilters[AllUsersFilterType.searchEmailOrUid.name] = uid;
      }
      Map<String, dynamic> result =
          await _databaseRepository.fetchUsers(withFilters: userFilters);
      if (result.isNotEmpty) {
        usersCount = result['count'];
        // if (usersCount < fullUsersList.length) {
        //   usersCount = fullUsersList.length;
        // }
        fullUsersList = List<UserslistData>.from(result['usersList']);
        List<UserslistData> userList = fullUsersList.length > 10
            ? fullUsersList.sublist(start, start + 10)
            : fullUsersList;
        if (userList.isNotEmpty) {
          lastDocument = fullUsersList.last.createdAt;
          loadedUsers = userList.length;
          for (var user in userList) {
            if (user.userDeletedStatus != null &&
                (user.userDeletedStatus == UserDeletedStatus.remove_a ||
                    user.userDeletedStatus == UserDeletedStatus.remove_u)) {
              final newUser = user.copyWith(
                name:
                    'Deleted user ${user.pseudoName.isNotEmpty ? user.pseudoName : id}',
              );
              id++;
              userList[userList.indexOf(user)] = newUser;
            }
          }
        }

        emit(
          AllUsersLoaded(
            userList: userList,
            hasMore: userList.length == limit && userList.length != usersCount,
            count: usersCount,
          ),
        );
      } else {
        emit(
          AllUsersLoaded(
            userList: const [],
            hasMore: false,
            count: 0,
          ),
        );
      }
    } catch (e) {
      emit(AllUsersError());
    }
  }

  Future<void> _loadMoreUsersToState(
    AllUsersEvent event,
    Emitter<AllUsersState> emit,
  ) async {
    try {
      if (loadedUsers < fullUsersList.length) {
        final tempList = fullUsersList.sublist(loadedUsers);
        List<UserslistData> moreUserList = tempList.length > 10
            ? tempList.sublist(start, start + 10)
            : tempList;
        List<UserslistData> finalUserList = moreUserList.toList();
        if (moreUserList.isNotEmpty) {
          loadedUsers += moreUserList.length;
          for (var user in moreUserList) {
            if (user.userDeletedStatus != null &&
                (user.userDeletedStatus == UserDeletedStatus.remove_a ||
                    user.userDeletedStatus == UserDeletedStatus.remove_u)) {
              final newReport = user.copyWith(
                name:
                    'Deleted user ${user.pseudoName.isNotEmpty ? user.pseudoName : id}',
              );
              id++;
              finalUserList[finalUserList.indexOf(user)] = newReport;
            }
          }
        }

        final userList = [
          ...(state as AllUsersLoaded).userList,
          ...finalUserList,
        ];
        emit(
          AllUsersLoaded(
            userList: userList,
            hasMore: userList.length < (state as AllUsersLoaded).count,
            count: usersCount,
          ),
        );
      } else {
        if (lastDocument != null) {
          userFilters['startAfter'] = lastDocument!.toIso8601String();
          Map<String, dynamic> result = await _databaseRepository.fetchUsers(
            withFilters: userFilters,
            limit: 10,
          );
          // usersCount += result['count'] as int;
          fullUsersList.addAll(result['usersList']);

          List<UserslistData> moreUserList = fullUsersList.sublist(
            start,
            start + 10 > fullUsersList.length ? null : start + 10,
          );
          List<UserslistData> finalUserList = moreUserList.toList();
          if (moreUserList.isNotEmpty) {
            lastDocument = fullUsersList.last.createdAt;
            loadedUsers += moreUserList.length;
            for (var user in moreUserList) {
              if ((state as AllUsersLoaded).userList.contains(user)) {
                finalUserList.remove(user);
              } else {
                if (user.userDeletedStatus != null &&
                    (user.userDeletedStatus == UserDeletedStatus.remove_a ||
                        user.userDeletedStatus == UserDeletedStatus.remove_u)) {
                  final newReport = user.copyWith(
                    name:
                        'Deleted user ${user.pseudoName.isNotEmpty ? user.pseudoName : id}',
                  );
                  id++;
                  finalUserList[finalUserList.indexOf(user)] = newReport;
                }
              }
            }
          }
          final userList = [
            ...(state as AllUsersLoaded).userList,
            ...finalUserList,
          ];
          emit(
            AllUsersLoaded(
              userList: userList,
              hasMore: userList.length < (state as AllUsersLoaded).count,
              count: usersCount,
            ),
          );
        }
      }
    } catch (e) {
      emit(AllUsersError());
    }
  }
}
