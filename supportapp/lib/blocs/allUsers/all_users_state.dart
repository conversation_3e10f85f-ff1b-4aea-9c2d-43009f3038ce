part of 'all_users_bloc.dart';

abstract class AllUsersState extends Equatable {
  @override
  List<Object?> get props => [];
}

class AllUsersInitial extends AllUsersState {}

class AllUsersLoading extends AllUsersState {}

class AllUsersLoaded extends AllUsersState {
  AllUsersLoaded({
    required this.userList,
    required this.hasMore,
    required this.count,
  });

  final List<UserslistData> userList;
  final bool hasMore;
  final int count;

  @override
  List<Object?> get props => [userList, hasMore];
}

class AllUsersError extends AllUsersState {
  AllUsersError({
    this.error,
  });
  final String? error;
}
