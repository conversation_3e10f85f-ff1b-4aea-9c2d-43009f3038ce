part of 'all_users_bloc.dart';

abstract class AllUsersEvent extends Equatable {
  const AllUsersEvent();

  @override
  List<Object?> get props => [];
}

class LoadUsersEvent extends AllUsersEvent {
  const LoadUsersEvent({
    this.userFilters = const {
      'subscriptionState': ['subscribed'],
    },
  });
  final Map<String, dynamic> userFilters;
}

class LoadMoreUsersEvent extends AllUsersEvent {}
