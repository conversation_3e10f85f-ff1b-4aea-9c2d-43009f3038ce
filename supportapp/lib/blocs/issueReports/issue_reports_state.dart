part of 'issue_reports_bloc.dart';

abstract class IssueReportsState extends Equatable {
  const IssueReportsState();

  @override
  List<Object> get props => [];
}

class IssueReportsInitial extends IssueReportsState {}

class IssueReportsLoading extends IssueReportsState {}

class IssueReportsLoaded extends IssueReportsState {
  const IssueReportsLoaded({
    required this.issueReports,
    this.isUpdate = false,
  });
  final List<IssueReport> issueReports;
  final bool isUpdate;

  @override
  List<Object> get props => [issueReports, isUpdate];
}

class IssueReportsError extends IssueReportsState {}

class IssueReportsEmpty extends IssueReportsState {}
