part of 'issue_reports_bloc.dart';

abstract class IssueReportsEvent extends Equatable {
  const IssueReportsEvent();

  @override
  List<Object> get props => [];
}

class LoadIssueReports extends IssueReportsEvent {
  const LoadIssueReports({
    required this.reportStatus,
    this.byFilter = const [null, 'All'],
  });

  final IssueReportStatus reportStatus;
  final List<String?> byFilter;
}

class LoadMoreIssueReports extends IssueReportsEvent {
  const LoadMoreIssueReports({
    this.reportStatus,
    this.lastReportDateTime,
    this.byFilter = const [null, 'All'],
  });

  final IssueReportStatus? reportStatus;
  final DateTime? lastReportDateTime;
  final List<String?> byFilter;
}

class UpdateIssueReports extends IssueReportsEvent {
  const UpdateIssueReports();
}
