import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/issue_report_status.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/issue_report.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'issue_reports_event.dart';
part 'issue_reports_state.dart';

class IssueReportsBloc extends Bloc<IssueReportsEvent, IssueReportsState> {
  IssueReportsBloc(this._databaseRepository) : super(IssueReportsInitial()) {
    on<LoadIssueReports>(_onLoadIssueReports);
    on<LoadMoreIssueReports>(_onLoadMoreIssueReports);
    on<UpdateIssueReports>(_onUpdateIssueReports);
  }
  final DatabaseRepository _databaseRepository;

  IssueReportStatus currentIssueReportStatus = IssueReportStatus.toBeReviewed;
  List<String?> currentByFilter = [null, 'All'];

  DateTime? _lastReportDateTime;
  DateTime? _lastLocalDateTime;
  DateTime? _lastCloudDateTime;
  StreamSubscription<List<IssueReport>>? _listenNewReports;
  int id = 1;

  @override
  Future<void> close() {
    _listenNewReports?.cancel();
    return super.close();
  }

  void resumeListenNewReports() {
    _listenNewReports?.pause();
  }

  void pauseListenNewReports() {
    _listenNewReports?.resume();
  }

  void _onLoadIssueReports(
    LoadIssueReports event,
    Emitter<IssueReportsState> emit,
  ) async {
    try {
      id = 1;
      await _listenNewReports?.cancel();
      emit(IssueReportsLoading());
      currentIssueReportStatus = event.reportStatus;
      currentByFilter = event.byFilter;
      _lastReportDateTime = null;

      final reports = await _databaseRepository.fetchIssueReports(
        lastReportedDateTime: _lastReportDateTime,
        reportStatus: currentIssueReportStatus,
        byFilter: currentByFilter[0],
      );
      if (reports.isNotEmpty) {
        _lastReportDateTime = reports.last.reportedAt;
        _lastLocalDateTime = reports.last.localUpdatedAt;
        _lastCloudDateTime = reports.last.cloudUpdatedAt;
        for (var report in reports) {
          if (report.userDeletedStatus != null &&
              (report.userDeletedStatus == UserDeletedStatus.remove_a ||
                  report.userDeletedStatus == UserDeletedStatus.remove_u)) {
            final newReport = report.copyWith(
              uname:
                  'Deleted user ${report.uname.isNotEmpty ? report.uname : id}',
            );
            id++;
            reports[reports.indexOf(report)] = newReport;
          }
        }
      }
      emit(
        IssueReportsLoaded(
          issueReports: reports.toList(),
          isUpdate: false,
        ),
      );
    } catch (e) {
      emit(IssueReportsError());
    }
  }

  void _onLoadMoreIssueReports(
    LoadMoreIssueReports event,
    Emitter<IssueReportsState> emit,
  ) async {
    try {
      await _listenNewReports?.cancel();
      final reports = await _databaseRepository.fetchIssueReports(
        lastReportedDateTime: _lastReportDateTime,
        reportStatus: currentIssueReportStatus,
        byFilter: currentByFilter[0],
        lastCloudDateTime: _lastCloudDateTime,
        lastLocalDateTime: _lastLocalDateTime,
      );

      if (reports.isNotEmpty) {
        _lastReportDateTime = reports.last.reportedAt;
        _lastLocalDateTime = reports.last.localUpdatedAt;
        _lastCloudDateTime = reports.last.cloudUpdatedAt;
        for (var report in reports) {
          if (report.userDeletedStatus != null &&
              (report.userDeletedStatus == UserDeletedStatus.remove_a ||
                  report.userDeletedStatus == UserDeletedStatus.remove_u)) {
            final newReport = report.copyWith(
              uname:
                  'Deleted user ${report.uname.isNotEmpty ? report.uname : id}',
            );
            id++;
            reports[reports.indexOf(report)] = newReport;
          }
        }

        final currentReports = (state as IssueReportsLoaded).issueReports;
        final totalReports = [
          ...currentReports,
          ...reports,
        ];
        emit(
          IssueReportsLoaded(
            issueReports: totalReports,
            isUpdate: false,
          ),
        );
      }
    } catch (e) {
      emit(IssueReportsError());
    }
  }

  void _onUpdateIssueReports(
    UpdateIssueReports event,
    Emitter<IssueReportsState> emit,
  ) {
    add(LoadIssueReports(reportStatus: currentIssueReportStatus));
  }
}
