part of 'messages_bloc.dart';

abstract class MessagesState extends Equatable {
  const MessagesState();

  @override
  List<Object?> get props => [];
}

class MessagesInitial extends MessagesState {}

class MessagesLoading extends MessagesState {}

class MessagesLoaded extends MessagesState {
  const MessagesLoaded({
    required this.messages,
    this.supportLanguage = 'english',
    this.chatUser,
  });
  final List<ChatMessage> messages;
  final String? supportLanguage;
  final ChatUser? chatUser;

  @override
  List<Object?> get props => [messages, chatUser];
}

class MoreMessagesLoaded extends MessagesState {
  const MoreMessagesLoaded({
    required this.messages,
    this.supportLanguage = 'english',
    this.chatUser,
  });
  final List<ChatMessage> messages;
  final String? supportLanguage;
  final ChatUser? chatUser;

  @override
  List<Object?> get props => [messages, chatUser];
}

class MessagesError extends MessagesState {}
