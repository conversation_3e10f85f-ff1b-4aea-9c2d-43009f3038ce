part of 'messages_bloc.dart';

abstract class MessagesEvent extends Equatable {
  const MessagesEvent();

  @override
  List<Object> get props => [];
}

class LoadMessages extends MessagesEvent {
  const LoadMessages({
    required this.uid,
    this.hasChatUser = false,
  });

  final String uid;
  final bool hasChatUser;
}

class AddPendingMsg extends MessagesEvent {
  const AddPendingMsg({
    required this.msg,
  });

  final ChatMessage msg;
}

class LoadMoreMessages extends MessagesEvent {
  const LoadMoreMessages({required this.uid, this.lastMessageId});

  final String uid;
  final String? lastMessageId;
}

class NewMessagesLoaded extends MessagesEvent {
  const NewMessagesLoaded({
    required this.messages,
    this.supportLanguage = 'english',
  });

  final List<ChatMessage> messages;
  final String? supportLanguage;
}
