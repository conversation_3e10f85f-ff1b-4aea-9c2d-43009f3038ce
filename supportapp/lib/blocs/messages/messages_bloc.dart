import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/chat_message.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'messages_event.dart';
part 'messages_state.dart';

class MessagesBloc extends Bloc<MessagesEvent, MessagesState> {
  MessagesBloc(this._databaseRepository) : super(MessagesInitial()) {
    on<LoadMessages>(_onLoadMessages);
    on<LoadMoreMessages>(_onLoadMoreMessages);
    on<NewMessagesLoaded>(_onNewMessagesLoaded);
    on<AddPendingMsg>(_onAddPendingMsg);
  }
  final DatabaseRepository _databaseRepository;

  DateTime? _firstMessageCloudTime;
  DateTime? _firstMessageLocalTime;
  DateTime? _lastMessageCloudTime;
  DateTime? _lastMessageLocalTime;
  StreamSubscription<List<ChatMessage>>? _listenNewMsg;

  @override
  Future<void> close() {
    _listenNewMsg?.cancel();
    return super.close();
  }

  void resumeListenNewMsg() {
    _listenNewMsg?.pause();
  }

  void pauseListenNewMsg() {
    _listenNewMsg?.resume();
  }

  void _onAddPendingMsg(AddPendingMsg event, Emitter<MessagesState> emit) {
    emit(
      MessagesLoaded(
        messages: [event.msg, ...(state as MessagesLoaded).messages],
      ),
    );
  }

  void _onLoadMessages(LoadMessages event, Emitter<MessagesState> emit) async {
    try {
      // Properly handle the chatUser based on the event
      ChatUser? chatUser;

      chatUser = await _databaseRepository.fetchChatUser(event.uid);

      await _listenNewMsg?.cancel();
      emit(MessagesLoading());

      _firstMessageCloudTime = null;
      _firstMessageLocalTime = null;
      _lastMessageCloudTime = null;
      _lastMessageLocalTime = null;

      final messages = await _databaseRepository.fetchMessages(
        uid: event.uid,
        lastMessageCloudTime: _lastMessageCloudTime,
      );

      final supportLanguage =
          await _databaseRepository.fetchUserSupportLanguage(
        event.uid,
      );
      if (messages.isNotEmpty) {
        _firstMessageCloudTime = messages.first.cloudUpdatedAt;
        _firstMessageLocalTime = messages.first.localUpdatedAt;
        _lastMessageCloudTime = messages.last.cloudUpdatedAt;
        _lastMessageLocalTime = messages.last.localUpdatedAt;
      }
      // messages.sort(
      //   (b, a) => a.cloudUpdatedAt != null &&
      //           b.cloudUpdatedAt != null &&
      //           a.cloudUpdatedAt!.isAtSameMomentAs(b.cloudUpdatedAt!)
      //       ? a.localUpdatedAt!.compareTo(b.localUpdatedAt!)
      //       : (a.cloudUpdatedAt ?? a.localUpdatedAt!)
      //           .compareTo(b.cloudUpdatedAt ?? b.localUpdatedAt!),
      // );
      messages.sort(
        (b, a) => a.localUpdatedAt!.compareTo(b.localUpdatedAt!),
      );
      emit(
        MessagesLoaded(
          messages: messages.toList(),
          supportLanguage: supportLanguage,
          chatUser: chatUser,
        ),
      );
      await _listenNewMsg?.cancel();
      _listenNewMsg = _databaseRepository
          .listenMessages(
        uid: event.uid,
        firstMessageCloudTime: _firstMessageCloudTime,
        firstMessageLocalTime: _firstMessageLocalTime,
      )
          .listen((List<ChatMessage> newMessages) async {
        if (newMessages.isNotEmpty) {
          var oldMessages = (state as MessagesLoaded).messages;
          for (var item in newMessages) {
            oldMessages.removeWhere((element) => element.id == item.id);
          }

          _firstMessageCloudTime =
              newMessages.first.cloudUpdatedAt ?? messages.first.localUpdatedAt;
          _firstMessageLocalTime = messages.first.localUpdatedAt;
          final totalList = [
            ...newMessages,
            ...oldMessages,
          ];
          totalList.sort(
            (b, a) => a.localUpdatedAt!.compareTo(b.localUpdatedAt!),
          );
          try {
            add(
              NewMessagesLoaded(
                messages: totalList,
                supportLanguage: supportLanguage,
              ),
            );
          } catch (e) {
            //
          }
        }
      });
    } catch (e) {
      emit(MessagesError());
    }
  }

  void _onLoadMoreMessages(
    LoadMoreMessages event,
    Emitter<MessagesState> emit,
  ) async {
    try {
      final messages = await _databaseRepository.fetchMessages(
        uid: event.uid,
        lastMessageCloudTime: _lastMessageCloudTime,
        lastMessageLocalTime: _lastMessageLocalTime,
      );
      if (messages.isNotEmpty) {
        _lastMessageCloudTime = messages.last.cloudUpdatedAt;
        _lastMessageLocalTime = messages.last.localUpdatedAt;
      }

      // Preserve the chatUser from the current state
      final currentState = state as MessagesLoaded;

      emit(
        MessagesLoaded(
          messages: [...currentState.messages, ...messages],
          supportLanguage: currentState.supportLanguage,
          chatUser: currentState.chatUser,
        ),
      );
    } catch (e) {
      emit(MessagesError());
    }
  }

  void _onNewMessagesLoaded(
    NewMessagesLoaded event,
    Emitter<MessagesState> emit,
  ) {
    // Preserve the chatUser if not provided in the event
    final chatUser = (state as MessagesLoaded).chatUser;

    emit(
      MessagesLoaded(
        messages: event.messages.toList(),
        supportLanguage: event.supportLanguage,
        chatUser: chatUser,
      ),
    );
  }
}
