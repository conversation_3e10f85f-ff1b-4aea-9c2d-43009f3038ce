part of 'chats_bloc.dart';

abstract class ChatsState extends Equatable {
  const ChatsState();

  @override
  List<Object> get props => [];
}

class ChatsInitial extends ChatsState {}

class ChatsLoading extends ChatsState {}

class ChatsLoaded extends ChatsState {
  const ChatsLoaded({
    required this.chats,
    this.moreChats = 0,
    this.isEmpty = false,
  });

  final List<ChatUser> chats;
  final int moreChats;
  final bool isEmpty;

  ChatsLoaded copyWith({
    List<ChatUser>? chats,
    bool? isUpdate,
    int? numOfUpdates,
  }) {
    return ChatsLoaded(
      chats: chats ?? this.chats,
    );
  }

  @override
  List<Object> get props => [
        chats,
      ];
}

class ChatsError extends ChatsState {}
