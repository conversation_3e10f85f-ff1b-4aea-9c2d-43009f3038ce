import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'chats_event.dart';
part 'chats_state.dart';

class ChatsBloc extends Bloc<ChatsEvent, ChatsState> {
  ChatsBloc(this._databaseRepository) : super(ChatsInitial()) {
    on<LoadChats>(_onLoadChats);
    on<LoadMoreChats>(_onLoadMoreChats);
    on<UpdateChats>(_onUpdateChats);
  }
  final DatabaseRepository _databaseRepository;

  DateTime? _lastLocalDateTime;
  DateTime? _lastCloudDateTime;
  ChatStatus currentChatStatus = ChatStatus.notReplied;
  List<String?> currentByFilter = [null, 'All'];
  StreamSubscription<List<ChatUser>>? _listenChatUpdates;
  int id = 1;

  @override
  Future<void> close() {
    _listenChatUpdates?.cancel();
    return super.close();
  }

  void resumeListenChatUpdates() {
    _listenChatUpdates?.pause();
  }

  void pauseListenChatUpdates() {
    _listenChatUpdates?.resume();
  }

  // Future<void> _onLoadChats(
  //   LoadChats event,
  //   Emitter<ChatsState> emit,
  // ) async {
  //   try {
  //     id = 1;
  //     await _listenChatUpdates?.cancel();
  //     emit(ChatsLoading());
  //     currentChatStatus = event.status;
  //     currentByFilter = event.byFilter;
  //     final chats = await _databaseRepository.fetchChats(
  //       status: currentChatStatus,
  //       byFilter: currentByFilter[0],
  //     );
  //     if (chats.isNotEmpty) {
  //       _lastLocalDateTime = chats.last.localUpdatedAt;
  //       _lastCloudDateTime = chats.last.cloudUpdatedAt;
  //       for (var chat in chats) {
  //         if (chat.userDeletedStatus != null &&
  //             (chat.userDeletedStatus == UserDeletedStatus.remove_a ||
  //                 chat.userDeletedStatus == UserDeletedStatus.remove_u)) {
  //           final newChat = chat.copyWith(
  //             uname: 'Deleted user ${chat.uname.isNotEmpty ? chat.uname : id}',
  //           );
  //           id++;
  //           chats[chats.indexOf(chat)] = newChat;
  //         }
  //       }
  //     }
  //     emit(
  //       ChatsLoaded(
  //         chats: chats,
  //       ),
  //     );
  //   } catch (e) {
  //     emit(ChatsError());
  //   }
  // }

  // Future<void> _onLoadMoreChats(
  //   LoadMoreChats event,
  //   Emitter<ChatsState> emit,
  // ) async {
  //   try {
  //     await _listenChatUpdates?.cancel();
  //     final chats = await _databaseRepository.fetchChats(
  //       status: event.status ?? currentChatStatus,
  //       lastLocalDateTime: _lastLocalDateTime,
  //       lastCloudDateTime: _lastCloudDateTime,
  //       byFilter: event.byFilter?[0] ?? currentByFilter[0],
  //     );
  //     if (chats.isNotEmpty) {
  //       _lastLocalDateTime = chats.last.localUpdatedAt;
  //       _lastCloudDateTime = chats.last.cloudUpdatedAt;
  //       for (var chat in chats) {
  //         if (chat.userDeletedStatus != null &&
  //             (chat.userDeletedStatus == UserDeletedStatus.remove_a ||
  //                 chat.userDeletedStatus == UserDeletedStatus.remove_u)) {
  //           final newReport = chat.copyWith(
  //             uname: 'Deleted user ${chat.uname.isNotEmpty ? chat.uname : id}',
  //           );
  //           id++;
  //           chats[chats.indexOf(chat)] = newReport;
  //         }
  //       }
  //     }
  //     final totalChats = [...(state as ChatsLoaded).chats, ...chats];
  //     emit(
  //       ChatsLoaded(
  //         chats: totalChats,
  //         moreChats: (totalChats.length % 10) - 1,
  //         isEmpty: chats.isEmpty,
  //       ),
  //     );
  //   } catch (e) {
  //     emit(ChatsError());
  //   }
  // }

  Future<void> _onLoadChats(
    LoadChats event,
    Emitter<ChatsState> emit,
  ) async {
    try {
      id = 1;
      await _listenChatUpdates?.cancel();
      emit(ChatsLoading());
      currentChatStatus = event.status;
      currentByFilter = event.byFilter;
      final chats = await _databaseRepository.fetchChats(
        status: currentChatStatus,
        byFilter: currentByFilter[0],
      );
      if (chats.isNotEmpty) {
        _lastLocalDateTime = chats.last.localUpdatedAt;
        _lastCloudDateTime = chats.last.cloudUpdatedAt;
        for (var chat in chats) {
          if (chat.userDeletedStatus != null &&
              (chat.userDeletedStatus == UserDeletedStatus.remove_a ||
                  chat.userDeletedStatus == UserDeletedStatus.remove_u)) {
            final newChat = chat.copyWith(
              uname: 'Deleted user ${chat.uname.isNotEmpty ? chat.uname : id}',
            );
            id++;
            chats[chats.indexOf(chat)] = newChat;
          }
        }

        // Sort chats by cloudUpdatedAt (most recent first)
        chats.sort((a, b) {
          final aTime = a.cloudUpdatedAt;
          final bTime = b.cloudUpdatedAt;
          if (aTime == null && bTime == null) return 0;
          if (aTime == null) return 1;
          if (bTime == null) return -1;
          return bTime.compareTo(aTime);
        });
      }

      emit(
        ChatsLoaded(
          chats: chats,
        ),
      );
    } catch (e) {
      emit(ChatsError());
    }
  }

  Future<void> _onLoadMoreChats(
    LoadMoreChats event,
    Emitter<ChatsState> emit,
  ) async {
    try {
      await _listenChatUpdates?.cancel();
      final chats = await _databaseRepository.fetchChats(
        status: event.status ?? currentChatStatus,
        lastLocalDateTime: _lastLocalDateTime,
        lastCloudDateTime: _lastCloudDateTime,
        byFilter: event.byFilter?[0] ?? currentByFilter[0],
      );
      if (chats.isNotEmpty) {
        _lastLocalDateTime = chats.last.localUpdatedAt;
        _lastCloudDateTime = chats.last.cloudUpdatedAt;
        for (var chat in chats) {
          if (chat.userDeletedStatus != null &&
              (chat.userDeletedStatus == UserDeletedStatus.remove_a ||
                  chat.userDeletedStatus == UserDeletedStatus.remove_u)) {
            final newReport = chat.copyWith(
              uname: 'Deleted user ${chat.uname.isNotEmpty ? chat.uname : id}',
            );
            id++;
            chats[chats.indexOf(chat)] = newReport;
          }
        }
      }

      final totalChats = [...(state as ChatsLoaded).chats, ...chats];

      // Sort combined list by cloudUpdatedAt (most recent first)
      totalChats.sort((a, b) {
        final aTime = a.cloudUpdatedAt;
        final bTime = b.cloudUpdatedAt;
        if (aTime == null && bTime == null) return 0;
        if (aTime == null) return 1;
        if (bTime == null) return -1;
        return bTime.compareTo(aTime);
      });

      emit(
        ChatsLoaded(
          chats: totalChats,
          moreChats: (totalChats.length % 10) - 1,
          isEmpty: chats.isEmpty,
        ),
      );
    } catch (e) {
      emit(ChatsError());
    }
  }

  void _onUpdateChats(
    UpdateChats event,
    Emitter<ChatsState> emit,
  ) {
    add(LoadChats(status: currentChatStatus));
  }
}
