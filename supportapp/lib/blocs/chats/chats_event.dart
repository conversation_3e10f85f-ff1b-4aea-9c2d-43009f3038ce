part of 'chats_bloc.dart';

abstract class ChatsEvent extends Equatable {
  const ChatsEvent();

  @override
  List<Object> get props => [];
}

class LoadChats extends ChatsEvent {
  const LoadChats({
    required this.status,
    this.byFilter = const [null, 'All'],
  });

  final ChatStatus status;
  final List<String?> byFilter;
}

class LoadMoreChats extends ChatsEvent {
  const LoadMoreChats({
    this.status,
    this.byFilter = const [null, 'All'],
  });

  final ChatStatus? status;
  final List<String?>? byFilter;
}

class UpdateChats extends ChatsEvent {
  const UpdateChats();
}
