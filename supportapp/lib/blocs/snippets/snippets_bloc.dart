import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:mevolvesupport/enums/snippet_status.dart';
import 'package:mevolvesupport/models/snippet.dart';
import 'package:mevolvesupport/models/snippets_count.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'snippets_event.dart';
part 'snippets_state.dart';

class SnippetsBloc extends Bloc<SnippetsEvent, SnippetsState> {
  SnippetsBloc(this._databaseRepository) : super(SnippetsInitial()) {
    on<LoadAllSnippets>(_onLoadAllSnippets);
    on<LoadSnippets>(_onLoadSnippets);
    on<LoadMoreSnippets>(_onLoadMoreSnippets);
    on<NewSnippetsLoaded>(_onNewSnippetsLoaded);
    on<UpdateSnippets>(_onUpdateSnippets);
  }
  final DatabaseRepository _databaseRepository;

  DateTime? _lastSnippetDateTime;
  int _listenLimit = 10;
  SnippetStatus currentSnippetStatus = SnippetStatus.none;
  List<String?> currentByFilter = [null, 'All'];
  StreamSubscription<List<Snippet>>? _listenSnippets;
  List<Snippet> _allSnippetList = [];
  List<Snippet> _currentSnippetList = [];

  final SnippetsCount _snippetsCount = const SnippetsCount(
    all: 0,
    draft: 0,
    inReview: 0,
    approved: 0,
    published: 0,
  );

  @override
  Future<void> close() {
    _listenSnippets?.cancel();
    return super.close();
  }

  void resumeListenChatUpdates() {
    _listenSnippets?.pause();
  }

  void pauseListenChatUpdates() {
    _listenSnippets?.resume();
  }

  Future<void> _onLoadAllSnippets(
    LoadAllSnippets event,
    Emitter<SnippetsState> emit,
  ) async {
    try {
      emit(SnippetsLoading());
      await _listenSnippets?.cancel();
      _allSnippetList = await _databaseRepository.fetchAllSnippets();

      if (_allSnippetList.isNotEmpty) {
        _lastSnippetDateTime = _allSnippetList.fold<DateTime>(
          DateTime(2000),
          (max, e) => e.cloudUpdatedAt!.isAfter(max) ? e.cloudUpdatedAt! : max,
        );
        _currentSnippetList = _allSnippetList.take(_listenLimit).toList();

        // Count snippets based on their status
        _snippetsCount.copyWith(all: _allSnippetList.length);
        for (var snippet in _allSnippetList) {
          if (snippet.status == SnippetStatus.draft) {
            _snippetsCount.copyWith(draft: _snippetsCount.draft + 1);
          } else if (snippet.status == SnippetStatus.inReview) {
            _snippetsCount.copyWith(inReview: _snippetsCount.inReview + 1);
          } else if (snippet.status == SnippetStatus.approved) {
            _snippetsCount.copyWith(approved: _snippetsCount.approved + 1);
          } else if (snippet.status == SnippetStatus.published) {
            _snippetsCount.copyWith(published: _snippetsCount.published + 1);
          }
        }

        emit(
          SnippetsLoaded(
            snippets: _currentSnippetList.take(10).toList(),
            snippetsCount: _snippetsCount,
          ),
        );
      } else {
        emit(
          SnippetsLoaded(
            snippets: const [],
            snippetsCount: _snippetsCount,
          ),
        );
      }
      _listenSnippets = _databaseRepository
          .listenSnippets(
        status: currentSnippetStatus,
        lastDateTime: _lastSnippetDateTime,
      )
          .listen((List<Snippet> newSnippets) async {
        final oldSnippets = _allSnippetList;
        if (!listEquals(oldSnippets, newSnippets)) {
          // _snippetsCount = await _databaseRepository.fetchChatsCount();
          final updateList = newSnippets.toList();
          updateList.removeWhere((chat) => oldSnippets.contains(chat));

          if (newSnippets.isNotEmpty) {
            add(
              NewSnippetsLoaded(
                snippets: (state as SnippetsLoaded).snippets,
                numOfUpdates: newSnippets.length,
                snippetsCount: _snippetsCount,
              ),
            );
          }
        }
      });
    } catch (e) {
      emit(SnippetsError());
    }
  }

  Future<void> _onLoadSnippets(
    LoadSnippets event,
    Emitter<SnippetsState> emit,
  ) async {
    try {
      _listenSnippets?.cancel();
      emit(SnippetsLoading());
      currentSnippetStatus = event.status;
      currentByFilter = event.byFilter;
      _listenSnippets?.cancel();
      // _snippetsCount = await _databaseRepository.fetchChatsCount();

      if (_allSnippetList.isNotEmpty) {
        _currentSnippetList = _allSnippetList
            .where((element) => element.status == currentSnippetStatus)
            .toList();

        emit(
          SnippetsLoaded(
            snippets: _currentSnippetList.take(10).toList(),
            snippetsCount: _snippetsCount,
          ),
        );
      }
    } catch (e) {
      emit(SnippetsError());
    }
  }

  Future<void> _onLoadMoreSnippets(
    LoadMoreSnippets event,
    Emitter<SnippetsState> emit,
  ) async {
    try {
      _listenLimit += 10;

      emit(
        SnippetsLoaded(
          snippets: _currentSnippetList.take(_listenLimit).toList(),
        ),
      );

      _listenSnippets?.cancel();
      _listenSnippets = _databaseRepository
          .listenSnippets(
        status: event.status ?? currentSnippetStatus,
        byFilter: event.byFilter?[0] ?? currentByFilter[0],
      )
          .listen((List<Snippet> newChats) async {
        final oldChats = (state as SnippetsLoaded).snippets;
        if (!listEquals(oldChats, newChats)) {
          // _snippetsCount = await _databaseRepository.fetchChatsCount();
          final updateList = newChats.toList();
          updateList.removeWhere((chat) => oldChats.contains(chat));
          if (updateList.isNotEmpty) {
            add(
              NewSnippetsLoaded(
                snippets: (state as SnippetsLoaded).snippets,
                numOfUpdates: updateList.length,
                snippetsCount: _snippetsCount,
              ),
            );
          }
        }
      });
    } catch (e) {
      emit(SnippetsError());
    }
  }

  void _onNewSnippetsLoaded(
    NewSnippetsLoaded event,
    Emitter<SnippetsState> emit,
  ) {
    emit(SnippetsLoading());
    emit(
      SnippetsLoaded(
        snippets: event.snippets.toList(),
        isUpdate: true,
        numOfUpdates: event.numOfUpdates,
        snippetsCount: event.snippetsCount,
      ),
    );
  }

  void _onUpdateSnippets(
    UpdateSnippets event,
    Emitter<SnippetsState> emit,
  ) {
    add(LoadSnippets(status: currentSnippetStatus));
  }
}
