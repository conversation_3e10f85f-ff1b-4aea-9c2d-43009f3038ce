part of 'snippets_bloc.dart';

abstract class SnippetsState extends Equatable {
  const SnippetsState();

  @override
  List<Object> get props => [];
}

class SnippetsInitial extends SnippetsState {}

class SnippetsLoading extends SnippetsState {}

class SnippetsLoaded extends SnippetsState {
  const SnippetsLoaded({
    required this.snippets,
    this.isUpdate = false,
    this.numOfUpdates = 0,
    this.snippetsCount = const SnippetsCount(
      all: 0,
      draft: 0,
      inReview: 0,
      approved: 0,
      published: 0,
    ),
  });

  final List<Snippet> snippets;
  final bool isUpdate;
  final int numOfUpdates;
  final SnippetsCount snippetsCount;

  SnippetsLoaded copyWith({
    List<Snippet>? snippets,
    bool? isUpdate,
    int? numOfUpdates,
    SnippetsCount? snippetsCount,
  }) {
    return SnippetsLoaded(
      snippets: snippets ?? this.snippets,
      isUpdate: isUpdate ?? this.isUpdate,
      numOfUpdates: numOfUpdates ?? this.numOfUpdates,
      snippetsCount: snippetsCount ?? this.snippetsCount,
    );
  }
}

class SnippetsError extends SnippetsState {}
