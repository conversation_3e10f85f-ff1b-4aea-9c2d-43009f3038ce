part of 'snippets_bloc.dart';

abstract class SnippetsEvent extends Equatable {
  const SnippetsEvent();

  @override
  List<Object> get props => [];
}

class LoadAllSnippets extends SnippetsEvent {
  const LoadAllSnippets();
}

class LoadSnippets extends SnippetsEvent {
  const LoadSnippets({
    required this.status,
    this.byFilter = const [null, 'All'],
  });

  final SnippetStatus status;
  final List<String?> byFilter;
}

class LoadMoreSnippets extends SnippetsEvent {
  const LoadMoreSnippets({
    this.status,
    this.byFilter = const [null, 'All'],
  });

  final SnippetStatus? status;
  final List<String?>? byFilter;
}

class NewSnippetsLoaded extends SnippetsEvent {
  const NewSnippetsLoaded({
    required this.numOfUpdates,
    required this.snippets,
    this.snippetsCount = const SnippetsCount(
      all: 0,
      draft: 0,
      inReview: 0,
      approved: 0,
      published: 0,
    ),
  });

  final int numOfUpdates;
  final List<Snippet> snippets;
  final SnippetsCount snippetsCount;
}

class UpdateSnippets extends SnippetsEvent {
  const UpdateSnippets();
}
