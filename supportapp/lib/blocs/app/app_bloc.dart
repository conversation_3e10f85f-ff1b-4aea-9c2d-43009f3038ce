import 'dart:async';
import 'dart:ui';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolvesupport/enums/theme_type.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/providers/firebase_authentication.dart';
import 'package:mevolvesupport/providers/shared_prefs.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

part 'app_event.dart';
part 'app_state.dart';

class AppBloc extends Bloc<AppEvent, AppState> {
  AppBloc({
    required FirebaseAuthenticationRepository authenticationRepository,
    required DatabaseRepository databaseRepository,
    required SharedPreferencesClient sharedPreferencesClient,
  })  : _authenticationRepository = authenticationRepository,
        _databaseRepository = databaseRepository,
        // _sharedPreferencesClient = sharedPreferencesClient,
        super(
          const AppState.splash(passcodeVerified: false),
        ) {
    on<FirebaseUserChanged>(_onFirebaseUserChanged);
    on<AppLoginProcessStatusChanged>(_onAppLoginProcessStatusChanged);
    on<AppLifecycleStateChanged>(_onAppLifecycleStateChanged);
    on<AppLogoutRequested>(_onLogoutRequested);
    on<AppUpdateStatusChanged>(_onAppUpdateStateChanged);
    on<SupportUserChanged>(_onSupportUserChanged);
    on<ResetAppTheme>(_onResetAppTheme);
    on<RefreshUserPermissions>(_onRefreshUserPermissions);
    on<TokenRefreshFailed>(_onTokenRefreshFailed);

    _firebaseUserSubscription = _authenticationRepository.user.listen(
      (user) => add(FirebaseUserChanged(user: user)),
    );
  }

  final FirebaseAuthenticationRepository _authenticationRepository;
  final DatabaseRepository _databaseRepository;

  // final SharedPreferencesClient _sharedPreferencesClient;
  late final StreamSubscription<User?> _firebaseUserSubscription;
  Timer? _tokenRefreshTimer;
  int? appInBgTimeStamp;

  Future<void> _onFirebaseUserChanged(
    FirebaseUserChanged event,
    Emitter<AppState> emit,
  ) async {
    Log.d('🔥 AppBloc: Firebase User Changed');
    if (event.user == null) {
      Log.d('❌ AppBloc: User is null, emitting unauthenticated state');
      _tokenRefreshTimer?.cancel();
      emit(AppState.unauthenticated(isForcedLogOut: state.isForcedLogOut));
    } else {
      final currentUser = event.user!;
      Log.d('✅ AppBloc: User authenticated: ${currentUser.email}');
      emit(state.copyWith(user: currentUser));
      if (state.loginProcessStatus == LoginProcessStatus.none) {
        Log.d(
          '🔄 AppBloc: Login process status is none, triggering loginCompleted',
        );
        add(
          AppLoginProcessStatusChanged(
            appLoginProcessStatus: LoginProcessStatus.loginCompleted,
            user: currentUser,
          ),
        );
      } else {
        Log.d(
          'ℹ️ AppBloc: Login process status is already: ${state.loginProcessStatus}',
        );
      }
    }
  }

  Future<FutureOr<void>> _onAppLoginProcessStatusChanged(
    AppLoginProcessStatusChanged event,
    Emitter<AppState> emit,
  ) async {
    Log.d(
      '🔄 AppBloc: Login process status changed to: ${event.appLoginProcessStatus}',
    );
    if (event.appLoginProcessStatus == LoginProcessStatus.loginCompleted ||
        event.appLoginProcessStatus == LoginProcessStatus.freshLoginCompleted) {
      if (event.user != null) {
        Log.d(
          '👤 AppBloc: Processing login completion for user: ${event.user!.email}',
        );
        try {
          Log.d('💾 AppBloc: Saving support user to database...');
          final supportUser =
              await _databaseRepository.saveSupportUser(user: event.user!);
          Log.d(
            '✅ AppBloc: Support user saved successfully: ${supportUser.email}',
          );

          Log.d('🔑 AppBloc: Refreshing user token...');
          await _authenticationRepository.refreshUserToken();
          Log.d('✅ AppBloc: User token refreshed');

          Log.d('🎉 AppBloc: Emitting authenticated state');
          emit(
            state.copyWith(
              appAuthStatus: AppStatus.authenticated,
              loginProcessStatus: event.appLoginProcessStatus,
              isNewUser: _authenticationRepository.isNewUser,
              supportUser: supportUser,
              user: event.user,
            ),
          );
          Log.d('✅ AppBloc: Authenticated state emitted successfully');

          // Start periodic token refresh
          _startTokenRefreshTimer();
        } catch (e) {
          Log.e('❌ AppBloc: Error during login completion: $e');
          emit(
            state.copyWith(
              loginProcessStatus: LoginProcessStatus.none,
            ),
          );
        }
      } else {
        Log.e('❌ AppBloc: Event user is null during login completion');
      }
    } else {
      Log.d(
        'ℹ️ AppBloc: Login process status updated to: ${event.appLoginProcessStatus}',
      );
      emit(
        state.copyWith(
          loginProcessStatus: event.appLoginProcessStatus,
          isNewUser: _authenticationRepository.isNewUser,
        ),
      );
    }
  }

  Future<void> _onLogoutRequested(
    AppLogoutRequested event,
    Emitter<AppState> emit,
  ) async {
    Log.d('on Logout requested');
    _tokenRefreshTimer?.cancel();
    emit(AppState.unauthenticated(isForcedLogOut: event.isForcedLogout));
  }

  @override
  Future<void> close() {
    _firebaseUserSubscription.cancel();
    _tokenRefreshTimer?.cancel();
    return super.close();
  }

  void changeAppLifecycleState(AppLifecycleState state) {
    if (state != AppLifecycleState.resumed) {
      appInBgTimeStamp = DateTime.now().millisecondsSinceEpoch;
    } else {
      // App resumed - check if we need to refresh token
      _handleAppResume();
    }
    add(AppLifecycleStateChanged(appLifecycleState: state));
  }

  FutureOr<void> _onAppLifecycleStateChanged(
    AppLifecycleStateChanged event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(appLifecycleState: event.appLifecycleState));
  }

  /// Handle app resume - refresh token if app was idle for too long or token is expiring
  Future<void> _handleAppResume() async {
    if (state.user == null) return;

    final now = DateTime.now().millisecondsSinceEpoch;
    final idleTimeMinutes =
        appInBgTimeStamp != null ? (now - appInBgTimeStamp!) / (1000 * 60) : 0;

    // Check if we should refresh the token
    bool shouldRefresh = false;
    String reason = '';

    // If app was idle for more than 30 minutes, refresh the token
    if (idleTimeMinutes > 30) {
      shouldRefresh = true;
      reason = 'app was idle for ${idleTimeMinutes.toInt()} minutes';
    } else {
      // Check if token is expiring soon
      try {
        final isTokenValid = await _authenticationRepository.isTokenValid();
        if (!isTokenValid) {
          shouldRefresh = true;
          reason = 'token is expiring soon or invalid';
        }
      } catch (e) {
        Log.e('❌ AppBloc: Error checking token validity: $e');
        shouldRefresh = true;
        reason = 'token validity check failed';
      }
    }

    if (shouldRefresh) {
      Log.d('🔄 AppBloc: Refreshing token because $reason...');
      try {
        await _authenticationRepository.refreshUserToken();
        Log.d('✅ AppBloc: Token refreshed successfully after app resume');
      } catch (e) {
        Log.e('❌ AppBloc: Error refreshing token on app resume: $e');
        // If token refresh fails, the user might need to re-authenticate
        if (e.toString().contains('permission-denied') ||
            e.toString().contains('unauthenticated') ||
            e.toString().contains('invalid-user-token')) {
          add(TokenRefreshFailed(error: e.toString()));
        }
      }
    }
  }

  Future<void> updateSupportUser({required SupportUser supportUser}) async {
    return _databaseRepository.updateSupportUser(
      supportUser: supportUser.copyWith(lastLogin: DateTime.now()),
    );
  }

  FutureOr<void> _onResetAppTheme(
    ResetAppTheme event,
    Emitter<AppState> emit,
  ) {
    emit(
      state.copyWith(
        supportUser: state.supportUser!.copyWith(appTheme: ThemeType.light),
      ),
    );
  }

  FutureOr<void> _onSupportUserChanged(
    SupportUserChanged event,
    Emitter<AppState> emit,
  ) {
    emit(
      state.copyWith(
        supportUser: event.supportUser!.copyWith(lastLogin: DateTime.now()),
      ),
    );
    _databaseRepository.updateSupportUser(
      supportUser: event.supportUser!.copyWith(lastLogin: DateTime.now()),
    );
  }

  FutureOr<void> _onAppUpdateStateChanged(
    AppUpdateStatusChanged event,
    Emitter<AppState> emit,
  ) {
    emit(state.copyWith(appUpdateState: event.appUpdateState));
  }

  /// Event handler for refreshing user permissions
  Future<void> _onRefreshUserPermissions(
    RefreshUserPermissions event,
    Emitter<AppState> emit,
  ) async {
    if (state.user == null) return;

    try {
      // Force refresh the ID token to get updated custom claims
      await _authenticationRepository.refreshUserToken();

      // Get the updated token with new permissions
      final token = await state.user!.getIdTokenResult(true); // Force refresh
      final updatedPermissions =
          List<String>.from(token.claims?['permissions'] ?? []);

      // Update the support user with new permissions
      if (state.supportUser != null) {
        final updatedSupportUser = state.supportUser!.copyWith(
          permissions: updatedPermissions,
        );

        emit(state.copyWith(supportUser: updatedSupportUser));
      }
    } catch (e) {
      Log.e('❌ AppBloc: Error refreshing user permissions: $e');
    }
  }

  FutureOr<void> _onTokenRefreshFailed(
    TokenRefreshFailed event,
    Emitter<AppState> emit,
  ) {
    Log.w('🚨 AppBloc: Token refresh failed, forcing logout: ${event.error}');
    _tokenRefreshTimer?.cancel();
    emit(const AppState.unauthenticated(isForcedLogOut: true));
  }

  /// Start periodic token refresh timer (every 45 minutes)
  void _startTokenRefreshTimer() {
    _tokenRefreshTimer?.cancel();

    // Refresh token every 45 minutes (Firebase tokens expire after 1 hour)
    _tokenRefreshTimer = Timer.periodic(
      const Duration(minutes: 45),
      (timer) => _performPeriodicTokenRefresh(),
    );

    Log.d(
      '🔄 AppBloc: Started periodic token refresh timer (every 45 minutes)',
    );
  }

  /// Perform periodic token refresh
  Future<void> _performPeriodicTokenRefresh() async {
    if (state.user == null) {
      Log.d('ℹ️ AppBloc: No user for periodic token refresh, stopping timer');
      _tokenRefreshTimer?.cancel();
      return;
    }

    try {
      Log.d('🔄 AppBloc: Performing periodic token refresh...');
      await _authenticationRepository.refreshUserToken();
      Log.d('✅ AppBloc: Periodic token refresh successful');
    } catch (e) {
      Log.e('❌ AppBloc: Periodic token refresh failed: $e');

      // If token refresh fails, the user might need to re-authenticate
      if (e.toString().contains('permission-denied') ||
          e.toString().contains('unauthenticated') ||
          e.toString().contains('invalid-user-token')) {
        add(TokenRefreshFailed(error: e.toString()));
      }
    }
  }
}
