part of 'app_bloc.dart';

abstract class AppEvent extends Equatable {
  const AppEvent();

  @override
  List<Object?> get props => [];
}

class AppLogoutRequested extends AppEvent {
  const AppLogoutRequested({this.isForcedLogout = false});

  final bool isForcedLogout;
}

class FirebaseUserChanged extends AppEvent {
  const FirebaseUserChanged({required this.user});

  final User? user;

  @override
  List<Object?> get props => [user];
}

class SupportUserChanged extends AppEvent {
  const SupportUserChanged({
    required this.supportUser,
  });

  final SupportUser? supportUser;

  @override
  List<Object?> get props => [supportUser];
}

class ResetAppTheme extends AppEvent {
  const ResetAppTheme();

  @override
  List<Object?> get props => [];
}

class PasscodeStatusChanged extends AppEvent {
  const PasscodeStatusChanged({
    required this.userData,
  });

  final UserMetaData userData;

  @override
  List<Object?> get props => [];
}

class AppLifecycleStateChanged extends AppEvent {
  const AppLifecycleStateChanged({
    required this.appLifecycleState,
  });

  final AppLifecycleState appLifecycleState;

  @override
  List<Object?> get props => [appLifecycleState];
}

class AppLoginProcessStatusChanged extends AppEvent {
  const AppLoginProcessStatusChanged({
    required this.appLoginProcessStatus,
    this.user,
  });

  final LoginProcessStatus appLoginProcessStatus;
  final User? user;

  @override
  List<Object?> get props => [appLoginProcessStatus];
}

class ConnectionStatusChanged extends AppEvent {
  const ConnectionStatusChanged({required this.isConnected});

  final bool isConnected;
}

class AppUpdateStatusChanged extends AppEvent {
  const AppUpdateStatusChanged({required this.appUpdateState});

  final AppUpdateState appUpdateState;
}

class RefreshUserPermissions extends AppEvent {
  const RefreshUserPermissions();

  @override
  List<Object?> get props => [];
}

class TokenRefreshFailed extends AppEvent {
  const TokenRefreshFailed({required this.error});

  final String error;

  @override
  List<Object?> get props => [error];
}
