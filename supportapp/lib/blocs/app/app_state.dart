part of 'app_bloc.dart';

enum FirebaseUserStatus {
  authenticated,
  unAuthenticated,
}

enum LoginProcessStatus {
  none,
  loginInProgress,
  loginCompleted,
  freshLoginCompleted,
}

enum AppStatus {
  unauthenticated,
  authenticated,
  splash,
}

enum AppUpdateState {
  none,
  minorUpdate,
  optionalUpdate,
  mandatoryUpdate,
}

enum InternetConnectionState {
  unknown,
  connected,
  disconnected,
}

class AppState extends Equatable {
  const AppState._({
    required this.appAuthStatus,
    this.supportUser,
    this.user,
    this.appLifecycleState = AppLifecycleState.detached,
    this.loginProcessStatus = LoginProcessStatus.none,
    this.internetConnectionState = InternetConnectionState.unknown,
    this.isNewUser = false,
    this.isForcedLogOut = false,
    this.appUpdateState = AppUpdateState.none,
  });

  const AppState.unauthenticated({bool isForcedLogOut = false})
      : this._(
          appAuthStatus: AppStatus.unauthenticated,
          isForcedLogOut: isForcedLogOut,
        );

  const AppState.splash({required bool passcodeVerified})
      : this._(appAuthStatus: AppStatus.splash);

  final AppStatus appAuthStatus;
  final User? user;
  final SupportUser? supportUser;
  final LoginProcessStatus loginProcessStatus;
  final AppLifecycleState appLifecycleState;
  final InternetConnectionState internetConnectionState;
  final bool isNewUser;
  final bool isForcedLogOut;
  final AppUpdateState appUpdateState;

  @override
  List<Object?> get props => [
        appAuthStatus,
        user,
        supportUser,
        appLifecycleState,
        loginProcessStatus,
        internetConnectionState,
        isNewUser,
        appUpdateState,
      ];

  AppState copyWith({
    AppStatus? appAuthStatus,
    User? user,
    SupportUser? supportUser,
    AppLifecycleState? appLifecycleState,
    bool? isForcedLogOut,
    LoginProcessStatus? loginProcessStatus,
    InternetConnectionState? internetConnectionState,
    bool? isNewUser,
    AppUpdateState? appUpdateState,
  }) {
    return AppState._(
      appAuthStatus: appAuthStatus ?? this.appAuthStatus,
      supportUser: supportUser ?? this.supportUser,
      user: user ?? this.user,
      appLifecycleState: appLifecycleState ?? this.appLifecycleState,
      loginProcessStatus: loginProcessStatus ?? this.loginProcessStatus,
      internetConnectionState:
          internetConnectionState ?? this.internetConnectionState,
      isForcedLogOut: isForcedLogOut ?? this.isForcedLogOut,
      isNewUser: isNewUser ?? this.isNewUser,
      appUpdateState: appUpdateState ?? this.appUpdateState,
    );
  }
}
