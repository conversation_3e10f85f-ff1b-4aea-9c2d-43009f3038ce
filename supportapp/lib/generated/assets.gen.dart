/// GENERATED CODE - DO NOT MODIFY BY HAND
/// *****************************************************
///  FlutterGen
/// *****************************************************

// coverage:ignore-file
// ignore_for_file: type=lint
// ignore_for_file: directives_ordering,unnecessary_import,implicit_dynamic_list_literal,deprecated_member_use

import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_svg/flutter_svg.dart' as _svg;
import 'package:vector_graphics/vector_graphics.dart' as _vg;

class $AssetsPngGen {
  const $AssetsPngGen();

  /// File path: assets/png/placeholder.png
  AssetGenImage get placeholder =>
      const AssetGenImage('assets/png/placeholder.png');

  /// List of all assets
  List<AssetGenImage> get values => [placeholder];
}

class $AssetsSvgGen {
  const $AssetsSvgGen();

  /// File path: assets/svg/add_user.svg
  SvgGenImage get addUser => const SvgGenImage('assets/svg/add_user.svg');

  /// File path: assets/svg/admin_panel_icon.svg
  SvgGenImage get adminPanelIcon =>
      const SvgGenImage('assets/svg/admin_panel_icon.svg');

  /// File path: assets/svg/arrow_drop_down.svg
  SvgGenImage get arrowDropDown =>
      const SvgGenImage('assets/svg/arrow_drop_down.svg');

  /// File path: assets/svg/attach_icon.svg
  SvgGenImage get attachIcon => const SvgGenImage('assets/svg/attach_icon.svg');

  /// File path: assets/svg/checkmark.svg
  SvgGenImage get checkmark => const SvgGenImage('assets/svg/checkmark.svg');

  /// File path: assets/svg/clock_icon.svg
  SvgGenImage get clockIcon => const SvgGenImage('assets/svg/clock_icon.svg');

  /// File path: assets/svg/close_icon.svg
  SvgGenImage get closeIcon => const SvgGenImage('assets/svg/close_icon.svg');

  /// File path: assets/svg/close_icon_filled.svg
  SvgGenImage get closeIconFilled =>
      const SvgGenImage('assets/svg/close_icon_filled.svg');

  /// File path: assets/svg/dashboard_icon.svg
  SvgGenImage get dashboardIcon =>
      const SvgGenImage('assets/svg/dashboard_icon.svg');

  /// File path: assets/svg/db_icon.svg
  SvgGenImage get dbIcon => const SvgGenImage('assets/svg/db_icon.svg');

  /// File path: assets/svg/download_icon.svg
  SvgGenImage get downloadIcon =>
      const SvgGenImage('assets/svg/download_icon.svg');

  /// File path: assets/svg/edit_icon.svg
  SvgGenImage get editIcon => const SvgGenImage('assets/svg/edit_icon.svg');

  /// File path: assets/svg/empty_screen.svg
  SvgGenImage get emptyScreen =>
      const SvgGenImage('assets/svg/empty_screen.svg');

  /// File path: assets/svg/empty_screen_messages.svg
  SvgGenImage get emptyScreenMessages =>
      const SvgGenImage('assets/svg/empty_screen_messages.svg');

  /// File path: assets/svg/feedback_icon.svg
  SvgGenImage get feedbackIcon =>
      const SvgGenImage('assets/svg/feedback_icon.svg');

  /// File path: assets/svg/filter_icon.svg
  SvgGenImage get filterIcon => const SvgGenImage('assets/svg/filter_icon.svg');

  /// File path: assets/svg/google_icon.svg
  SvgGenImage get googleIcon => const SvgGenImage('assets/svg/google_icon.svg');

  /// File path: assets/svg/ic_log_thumbnail.svg
  SvgGenImage get icLogThumbnail =>
      const SvgGenImage('assets/svg/ic_log_thumbnail.svg');

  /// File path: assets/svg/login_image.svg
  SvgGenImage get loginImage => const SvgGenImage('assets/svg/login_image.svg');

  /// File path: assets/svg/logout_icon.svg
  SvgGenImage get logoutIcon => const SvgGenImage('assets/svg/logout_icon.svg');

  /// File path: assets/svg/messages_icon.svg
  SvgGenImage get messagesIcon =>
      const SvgGenImage('assets/svg/messages_icon.svg');

  /// File path: assets/svg/mevolve_icon.svg
  SvgGenImage get mevolveIcon =>
      const SvgGenImage('assets/svg/mevolve_icon.svg');

  /// File path: assets/svg/mevolve_support_icon.svg
  SvgGenImage get mevolveSupportIcon =>
      const SvgGenImage('assets/svg/mevolve_support_icon.svg');

  /// File path: assets/svg/no_access_icon.svg
  SvgGenImage get noAccessIcon =>
      const SvgGenImage('assets/svg/no_access_icon.svg');

  /// File path: assets/svg/nugget_icon.svg
  SvgGenImage get nuggetIcon => const SvgGenImage('assets/svg/nugget_icon.svg');

  /// File path: assets/svg/password_eye.svg
  SvgGenImage get passwordEye =>
      const SvgGenImage('assets/svg/password_eye.svg');

  /// File path: assets/svg/password_eye_open.svg
  SvgGenImage get passwordEyeOpen =>
      const SvgGenImage('assets/svg/password_eye_open.svg');

  /// File path: assets/svg/person_icon.svg
  SvgGenImage get personIcon => const SvgGenImage('assets/svg/person_icon.svg');

  /// File path: assets/svg/refresh_icon.svg
  SvgGenImage get refreshIcon =>
      const SvgGenImage('assets/svg/refresh_icon.svg');

  /// File path: assets/svg/reload_icon.svg
  SvgGenImage get reloadIcon => const SvgGenImage('assets/svg/reload_icon.svg');

  /// File path: assets/svg/remove_user.svg
  SvgGenImage get removeUser => const SvgGenImage('assets/svg/remove_user.svg');

  /// File path: assets/svg/report_icon.svg
  SvgGenImage get reportIcon => const SvgGenImage('assets/svg/report_icon.svg');

  /// File path: assets/svg/search_icon.svg
  SvgGenImage get searchIcon => const SvgGenImage('assets/svg/search_icon.svg');

  /// File path: assets/svg/segment_icon.svg
  SvgGenImage get segmentIcon =>
      const SvgGenImage('assets/svg/segment_icon.svg');

  /// File path: assets/svg/send_icon.svg
  SvgGenImage get sendIcon => const SvgGenImage('assets/svg/send_icon.svg');

  /// File path: assets/svg/star_circle_icon.svg
  SvgGenImage get starCircleIcon =>
      const SvgGenImage('assets/svg/star_circle_icon.svg');

  /// File path: assets/svg/trash_icon.svg
  SvgGenImage get trashIcon => const SvgGenImage('assets/svg/trash_icon.svg');

  /// File path: assets/svg/warning_triangle.svg
  SvgGenImage get warningTriangle =>
      const SvgGenImage('assets/svg/warning_triangle.svg');

  /// List of all assets
  List<SvgGenImage> get values => [
        addUser,
        adminPanelIcon,
        arrowDropDown,
        attachIcon,
        checkmark,
        clockIcon,
        closeIcon,
        closeIconFilled,
        dashboardIcon,
        dbIcon,
        downloadIcon,
        editIcon,
        emptyScreen,
        emptyScreenMessages,
        feedbackIcon,
        filterIcon,
        googleIcon,
        icLogThumbnail,
        loginImage,
        logoutIcon,
        messagesIcon,
        mevolveIcon,
        mevolveSupportIcon,
        noAccessIcon,
        nuggetIcon,
        passwordEye,
        passwordEyeOpen,
        personIcon,
        refreshIcon,
        reloadIcon,
        removeUser,
        reportIcon,
        searchIcon,
        segmentIcon,
        sendIcon,
        starCircleIcon,
        trashIcon,
        warningTriangle
      ];
}

class Assets {
  Assets._();

  static const $AssetsPngGen png = $AssetsPngGen();
  static const $AssetsSvgGen svg = $AssetsSvgGen();
}

class AssetGenImage {
  const AssetGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  });

  final String _assetName;

  final Size? size;
  final Set<String> flavors;

  Image image({
    Key? key,
    AssetBundle? bundle,
    ImageFrameBuilder? frameBuilder,
    ImageErrorWidgetBuilder? errorBuilder,
    String? semanticLabel,
    bool excludeFromSemantics = false,
    double? scale,
    double? width,
    double? height,
    Color? color,
    Animation<double>? opacity,
    BlendMode? colorBlendMode,
    BoxFit? fit,
    AlignmentGeometry alignment = Alignment.center,
    ImageRepeat repeat = ImageRepeat.noRepeat,
    Rect? centerSlice,
    bool matchTextDirection = false,
    bool gaplessPlayback = false,
    bool isAntiAlias = false,
    String? package,
    FilterQuality filterQuality = FilterQuality.low,
    int? cacheWidth,
    int? cacheHeight,
  }) {
    return Image.asset(
      _assetName,
      key: key,
      bundle: bundle,
      frameBuilder: frameBuilder,
      errorBuilder: errorBuilder,
      semanticLabel: semanticLabel,
      excludeFromSemantics: excludeFromSemantics,
      scale: scale,
      width: width,
      height: height,
      color: color,
      opacity: opacity,
      colorBlendMode: colorBlendMode,
      fit: fit,
      alignment: alignment,
      repeat: repeat,
      centerSlice: centerSlice,
      matchTextDirection: matchTextDirection,
      gaplessPlayback: gaplessPlayback,
      isAntiAlias: isAntiAlias,
      package: package,
      filterQuality: filterQuality,
      cacheWidth: cacheWidth,
      cacheHeight: cacheHeight,
    );
  }

  ImageProvider provider({
    AssetBundle? bundle,
    String? package,
  }) {
    return AssetImage(
      _assetName,
      bundle: bundle,
      package: package,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}

class SvgGenImage {
  const SvgGenImage(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = false;

  const SvgGenImage.vec(
    this._assetName, {
    this.size,
    this.flavors = const {},
  }) : _isVecFormat = true;

  final String _assetName;
  final Size? size;
  final Set<String> flavors;
  final bool _isVecFormat;

  _svg.SvgPicture svg({
    Key? key,
    bool matchTextDirection = false,
    AssetBundle? bundle,
    String? package,
    double? width,
    double? height,
    BoxFit fit = BoxFit.contain,
    AlignmentGeometry alignment = Alignment.center,
    bool allowDrawingOutsideViewBox = false,
    WidgetBuilder? placeholderBuilder,
    String? semanticsLabel,
    bool excludeFromSemantics = false,
    _svg.SvgTheme? theme,
    ColorFilter? colorFilter,
    Clip clipBehavior = Clip.hardEdge,
    @deprecated Color? color,
    @deprecated BlendMode colorBlendMode = BlendMode.srcIn,
    @deprecated bool cacheColorFilter = false,
  }) {
    final _svg.BytesLoader loader;
    if (_isVecFormat) {
      loader = _vg.AssetBytesLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
      );
    } else {
      loader = _svg.SvgAssetLoader(
        _assetName,
        assetBundle: bundle,
        packageName: package,
        theme: theme,
      );
    }
    return _svg.SvgPicture(
      loader,
      key: key,
      matchTextDirection: matchTextDirection,
      width: width,
      height: height,
      fit: fit,
      alignment: alignment,
      allowDrawingOutsideViewBox: allowDrawingOutsideViewBox,
      placeholderBuilder: placeholderBuilder,
      semanticsLabel: semanticsLabel,
      excludeFromSemantics: excludeFromSemantics,
      colorFilter: colorFilter ??
          (color == null ? null : ColorFilter.mode(color, colorBlendMode)),
      clipBehavior: clipBehavior,
      cacheColorFilter: cacheColorFilter,
    );
  }

  String get path => _assetName;

  String get keyName => _assetName;
}
