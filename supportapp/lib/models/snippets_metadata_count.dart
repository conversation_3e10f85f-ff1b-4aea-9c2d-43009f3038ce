class SnippetsMetadataCount {
  const SnippetsMetadataCount({
    required this.screen,
    required this.userSegment,
    required this.position,
  });

  final int screen;
  final int userSegment;
  final int position;

  SnippetsMetadataCount copyWith({
    int? screen,
    int? userSegment,
    int? position,
  }) {
    return SnippetsMetadataCount(
      screen: screen ?? this.screen,
      userSegment: userSegment ?? this.userSegment,
      position: position ?? this.position,
    );
  }
}
