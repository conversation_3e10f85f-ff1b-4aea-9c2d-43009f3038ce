import 'package:cloud_firestore/cloud_firestore.dart';

/// Enum for segment activity types
enum SegmentActivityType {
  addition,
  removal;

  static SegmentActivityType fromString(String value) {
    switch (value.toLowerCase()) {
      case 'addition':
        return SegmentActivityType.addition;
      case 'removal':
        return SegmentActivityType.removal;
      default:
        return SegmentActivityType.addition; // Default fallback
    }
  }

  String get value {
    switch (this) {
      case SegmentActivityType.addition:
        return 'addition';
      case SegmentActivityType.removal:
        return 'removal';
    }
  }
}

/// Model for userSegments collection
class Segment {
  Segment({
    required this.id,
    required this.name,
    required this.purpose,
    required this.usersCount,
    required this.createdAt,
    required this.cloudUpdatedAt,
    required this.createdBy,
    required this.updatedBy,
    this.deletedAt,
  });

  factory Segment.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return Segment(
      id: data['id'] ?? doc.id,
      name: data['name'] ?? '',
      purpose: data['purpose'] ?? '',
      usersCount: data['usersCount'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp?) ?? Timestamp.now(),
      cloudUpdatedAt: (data['cloudUpdatedAt'] as Timestamp?) ?? Timestamp.now(),
      createdBy: data['createdBy'] ?? '',
      updatedBy: data['updatedBy'] ?? '',
      deletedAt: data['deletedAt'] as Timestamp?,
    );
  }

  final String id;
  final String name;
  final String purpose;
  final int usersCount;
  final Timestamp createdAt;
  final Timestamp cloudUpdatedAt;
  final String createdBy;
  final String updatedBy;
  final Timestamp? deletedAt;
}

/// Model for userSegmentActivities collection
class SegmentActivity {
  SegmentActivity({
    required this.id,
    required this.userSegmentId,
    required this.activityTitle,
    required this.csvPath,
    required this.csvUserCount,
    required this.updatedUserCount,
    required this.createdAt,
    required this.cloudUpdatedAt,
    required this.createdBy,
    required this.activityType,
  });

  factory SegmentActivity.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return SegmentActivity(
      id: data['id'] ?? doc.id,
      userSegmentId: data['userSegmentId'] ?? '',
      activityTitle: data['activityTitle'] ?? '',
      csvPath: data['csvPath'] ?? '',
      csvUserCount: data['csvUserCount'] ?? 0,
      updatedUserCount: data['updatedUserCount'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp?) ?? Timestamp.now(),
      cloudUpdatedAt: (data['cloudUpdatedAt'] as Timestamp?) ?? Timestamp.now(),
      createdBy: data['createdBy'] ?? '',
      activityType:
          SegmentActivityType.fromString(data['activityType'] ?? 'addition'),
    );
  }

  final String id;
  final String userSegmentId;
  final String activityTitle;
  final String csvPath;
  final int csvUserCount;
  final int updatedUserCount;
  final Timestamp createdAt;
  final Timestamp cloudUpdatedAt;
  final String createdBy;
  final SegmentActivityType activityType;
}
