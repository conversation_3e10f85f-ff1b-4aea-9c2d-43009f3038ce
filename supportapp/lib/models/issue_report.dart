import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/issue_report_type.dart';
import 'package:mevolvesupport/enums/issue_report_status.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/constants/extensions.dart';

class IssueReport extends Equatable {
  IssueReport({
    required this.id,
    required this.uid,
    required this.uname,
    required this.reportedAt,
    required this.updatedBy,
    this.lastSid,
    this.sname,
    this.localUpdatedAt,
    this.cloudUpdatedAt,
    required this.status,
    this.prevStatus,
    required this.type,
    required this.description,
    this.resolvedReason,
    required this.attachments,
    this.userDeletedStatus,
  });

  factory IssueReport.fromFirestore(Map<String, dynamic> map) {
    List<MeAttachmentInfo> attachments = [];
    if (map['attachments'] != null) {
      attachments = (map['attachments'] as List)
          .map((e) => MeAttachmentInfo.fromMap(e))
          .toList();
    }
    return IssueReport(
      id: map['id'] as String,
      uid: map['uid'] as String,
      uname: map['uname'] as String,
      reportedAt: map['reportedAt']?.toDate(),
      updatedBy: map['updatedBy'] as String,
      lastSid: map['lastSid'],
      sname: map['sname'],
      localUpdatedAt: map['localUpdatedAt']?.toDate(),
      cloudUpdatedAt: map['cloudUpdatedAt']?.toDate(),
      status: map['status'] != null
          ? IssueReportStatus.values.byName(map['status'])
          : IssueReportStatus.none,
      prevStatus: map['prevStatus'] != null
          ? IssueReportStatus.values.byName(map['prevStatus'])
          : null,
      type: map['type'] != null
          ? IssueReportType.values.byName(map['type'])
          : IssueReportType.technicalIssue,
      description: map['description'] as String,
      resolvedReason: map['resolvedReason'],
      attachments: attachments,
      userDeletedStatus: map['userDeletedStatus'] != null
          ? UserDeletedStatus.values.byName(map['userDeletedStatus'])
          : null,
    );
  }

  final int docVer = DatabaseRepository.currentDbVersion;
  final String id;
  final String uid;
  final String uname;
  final DateTime reportedAt;
  final String updatedBy;
  final String? lastSid;
  final String? sname;
  final DateTime? localUpdatedAt;
  final DateTime? cloudUpdatedAt;
  final IssueReportStatus status;
  final IssueReportStatus? prevStatus;
  final IssueReportType type;
  final String description;
  final String? resolvedReason;
  final List<MeAttachmentInfo> attachments;
  final UserDeletedStatus? userDeletedStatus;

  IssueReport copyWith({
    String? id,
    String? uid,
    String? uname,
    DateTime? reportedAt,
    String? updatedBy,
    String? lastSid,
    String? sname,
    DateTime? localUpdatedAt,
    DateTime? cloudUpdatedAt,
    IssueReportStatus? status,
    IssueReportStatus? prevStatus,
    IssueReportType? type,
    String? description,
    String? resolvedReason,
    List<MeAttachmentInfo>? attachments,
    UserDeletedStatus? userDeletedStatus,
  }) {
    return IssueReport(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      uname: uname ?? this.uname,
      reportedAt: reportedAt ?? this.reportedAt,
      updatedBy: updatedBy ?? this.updatedBy,
      lastSid: lastSid ?? this.lastSid,
      sname: sname ?? this.sname,
      localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
      cloudUpdatedAt: cloudUpdatedAt ?? this.cloudUpdatedAt,
      status: status ?? this.status,
      prevStatus: prevStatus ?? this.prevStatus,
      type: type ?? this.type,
      description: description ?? this.description,
      resolvedReason: resolvedReason ?? this.resolvedReason,
      attachments: attachments ?? this.attachments,
      userDeletedStatus: userDeletedStatus ?? this.userDeletedStatus,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'docVer': docVer,
      'uid': uid,
      'uname': uname,
      'reportedAt': reportedAt.toUtc().toIso8601String(),
      'updatedBy': updatedBy,
      'lastSid': lastSid,
      'sname': sname,
      'localUpdatedAt': localUpdatedAt?.toUtc().toIso8601String(),
      'cloudUpdatedAt': cloudUpdatedAt?.toUtc().toIso8601String(),
      'status': status.name,
      'prevStatus': prevStatus?.name,
      'type': type.name,
      'description': description,
      'resolvedReason': resolvedReason,
      'attachments': attachments,
      'attachmentsDeletedAt':
          attachments.map((e) => e.deletedAt?.onlyDateString()).toList(),
      'userDeletedStatus': userDeletedStatus?.name,
    };
  }

  @override
  List<Object?> get props {
    return [
      id,
      type,
      description,
      updatedBy,
      status,
      cloudUpdatedAt,
      // lastSid,
      // sname,
      resolvedReason,
      // attachments,
      userDeletedStatus,
    ];
  }
}
