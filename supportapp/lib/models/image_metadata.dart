class ImageMetadata {
  factory ImageMetadata.clone(ImageMetadata source) {
    return ImageMetadata(
      height: source.height,
      width: source.width,
    );
  }

  factory ImageMetadata.fromMap(Map<String, dynamic> map) {
    return ImageMetadata(
      height: map['height'],
      width: map['width'],
    );
  }

  ImageMetadata({
    this.height = 0,
    this.width = 0,
  });

  int height;
  int width;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'height': height,
      'width': width,
    };
  }
}
