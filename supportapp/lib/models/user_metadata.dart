import 'package:equatable/equatable.dart';
import 'package:flutter_helper_utils/flutter_helper_utils.dart';

import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/enums/issue_report_status.dart';
import 'package:mevolvesupport/enums/purchase/user_entitlements.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/favourite_widget.dart';
import 'package:mevolvesupport/models/user/app_settings.dart';
import 'package:mevolvesupport/models/user/feature_usage_info.dart';
import 'package:mevolvesupport/models/user/notification_settings.dart';
import 'package:mevolvesupport/models/user/security_settings.dart';
import 'package:mevolvesupport/models/user/subscription_info.dart';
import 'package:mevolvesupport/models/user/user_info.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

class UserMetaData extends Equatable {
  UserMetaData({
    required this.id,
    required this.uid,
    required this.localUpdatedAt,
    this.cloudUpdatedAt,
    required this.userInfo,
    required this.subscriptionInfo,
    required this.appSettings,
    required this.securitySettings,
    required this.featureUsageInfo,
    required this.notificationSettings,
    this.chatStatus = ChatStatus.none,
    this.reportStatus = IssueReportStatus.none,
    this.userDeletedStatus,
    this.superSubscription = MeUserEntitlement.free,
    this.muid,
  });

  factory UserMetaData.fromFirestore(Map<String, dynamic> map) {
    jsonMap = map.makeEncodable;
    List<FavoriteWidgetsModel> favMap = [];
    if (map['favorites'] != null) {
      for (int i = 0; i < map['favorites'].length; i++) {
        favMap.add(FavoriteWidgetsModel.fromFirestore(map['favorites'][i]));
      }
    }
    final userData = UserMetaData(
      id: map['id'],
      uid: map['uid'],
      localUpdatedAt: map['localUpdatedAt']?.toDate() ?? DateTime.now(),
      cloudUpdatedAt: map['cloudUpdatedAt']?.toDate(),
      userInfo: UserInfo.fromMap(
        map['userInfo'],
      ),
      subscriptionInfo: SubscriptionInfo.fromMap(
        map['subscriptionInfo'],
      ),
      appSettings: map['appSettings'] != null
          ? AppSettings.fromMap(
              map['appSettings'],
            )
          : AppSettings(),
      securitySettings: map['securitySettings'] != null
          ? SecuritySettings.fromMap(
              map['securitySettings'],
            )
          : SecuritySettings(),
      notificationSettings: map['notificationSettings'] != null
          ? NotificationSettings.fromMap(
              map['notificationSettings'],
            )
          : const NotificationSettings(),
      featureUsageInfo: map['featureUsageInfo'] == null
          ? FeatureUsageInfo()
          : FeatureUsageInfo.fromMap(
              map['featureUsageInfo'],
            ),
      chatStatus: map['chatStatus'] != null
          ? ChatStatus.values.byName(map['chatStatus'])
          : ChatStatus.none,
      reportStatus: map['reportStatus'] != null
          ? IssueReportStatus.values.byName(map['reportStatus'])
          : IssueReportStatus.none,
      userDeletedStatus: map['userDeletedStatus'] != null
          ? UserDeletedStatus.values.byName(map['userDeletedStatus'])
          : null,
      superSubscription: map['superSubscription'] != null
          ? MeUserEntitlement.values.byName(map['superSubscription'])
          : MeUserEntitlement.free,
      muid: map['muid'],
    );
    return userData;
  }

  final String id;

  final int docVer = DatabaseRepository.currentDbVersion;

  final FirebaseDocCollectionType docCollection =
      FirebaseDocCollectionType.usersMetadata;
  final String uid;
  final DateTime localUpdatedAt;
  final DateTime? cloudUpdatedAt;
  final UserInfo userInfo;
  final SubscriptionInfo subscriptionInfo;
  final AppSettings appSettings;
  final SecuritySettings securitySettings;
  final FeatureUsageInfo featureUsageInfo;
  final ChatStatus chatStatus;
  final IssueReportStatus reportStatus;
  final UserDeletedStatus? userDeletedStatus;
  final MeUserEntitlement? superSubscription;
  final String? muid;
  final NotificationSettings notificationSettings;

  static late Map<String, dynamic> jsonMap;

  Map<String, dynamic> get getJsonMap => jsonMap;

  UserMetaData copyWith({
    DateTime? localUpdatedAt,
    UserInfo? userInfo,
    SubscriptionInfo? subscriptionInfo,
    AppSettings? appSettings,
    SecuritySettings? securitySettings,
    FeatureUsageInfo? featureUsageInfo,
    NotificationSettings? notificationSettings,
    ChatStatus? chatStatus,
    IssueReportStatus? reportStatus,
    UserDeletedStatus? userDeletedStatus,
    MeUserEntitlement? superSubscription,
    String? muid,
  }) {
    final userData = UserMetaData(
      id: id,
      uid: uid,
      localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
      userInfo: userInfo ?? this.userInfo,
      subscriptionInfo: subscriptionInfo ?? this.subscriptionInfo,
      appSettings: appSettings ?? this.appSettings,
      securitySettings: securitySettings ?? this.securitySettings,
      featureUsageInfo: featureUsageInfo ?? this.featureUsageInfo,
      chatStatus: chatStatus ?? this.chatStatus,
      reportStatus: reportStatus ?? this.reportStatus,
      userDeletedStatus: userDeletedStatus ?? this.userDeletedStatus,
      notificationSettings: notificationSettings ?? this.notificationSettings,
      superSubscription: superSubscription ?? this.superSubscription,
      muid: muid ?? this.muid,
    );
    return userData;
  }

  @override
  List<Object?> get props {
    return [
      id,
      docVer,
      docCollection,
      uid,
      localUpdatedAt,
      cloudUpdatedAt,
      userInfo,
      subscriptionInfo,
      appSettings,
      securitySettings,
      featureUsageInfo,
      chatStatus,
      reportStatus,
      userDeletedStatus,
      superSubscription,
      muid,
    ];
  }
}
