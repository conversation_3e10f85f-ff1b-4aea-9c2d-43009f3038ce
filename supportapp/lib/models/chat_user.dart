import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class ChatUser extends Equatable {
  ChatUser({
    required this.uid,
    required this.uname,
    this.message,
    required this.updatedBy,
    this.localUpdatedAt,
    this.cloudUpdatedAt,
    this.createdAt,
    required this.status,
    this.prevStatus,
    this.clarify = false,
    this.attachmentCount = 0,
    this.lastSid,
    this.sname,
    this.userDeletedStatus,
  });

  factory ChatUser.fromFirestore(Map<String, dynamic> map) {
    return ChatUser(
      uid: map['uid'] as String,
      uname: map['uname'] as String,
      message: map['message'] as String,
      updatedBy: map['updatedBy'] as String,
      localUpdatedAt: parseDate(map['localUpdatedAt'])!,
      cloudUpdatedAt: parseDate(map['cloudUpdatedAt']),
      createdAt: parseDate(map['createdAt']) ?? DateTime.now(),
      status: map['status'] != null
          ? ChatStatus.values.byName(map['status'])
          : ChatStatus.notReplied,
      prevStatus: map['prevStatus'] != null
          ? ChatStatus.values.byName(map['prevStatus'])
          : null,
      clarify: map['clarify'] ?? false,
      attachmentCount: map['attachmentCount'] ?? 0,
      lastSid: map['lastSid'],
      sname: map['sname'],
      userDeletedStatus: map['userDeletedStatus'] != null
          ? UserDeletedStatus.values.byName(map['userDeletedStatus'])
          : null,
    );
  }

  final int docVer = DatabaseRepository.currentDbVersion;

  String get id => idFromUid(uid);
  final String uid;
  final String uname;
  final String? message;
  final String updatedBy;
  final DateTime? localUpdatedAt;
  final DateTime? cloudUpdatedAt;
  final DateTime? createdAt;
  final ChatStatus status;
  final ChatStatus? prevStatus;
  final bool clarify;
  final int? attachmentCount;
  final String? lastSid;
  final String? sname;
  final UserDeletedStatus? userDeletedStatus;

  static String idFromUid(String uid) => 'cU-$uid';

  ChatUser copyWith({
    String? uid,
    String? uname,
    String? message,
    String? updatedBy,
    DateTime? localUpdatedAt,
    DateTime? cloudUpdatedAt,
    DateTime? createdAt,
    ChatStatus? status,
    ChatStatus? prevStatus,
    bool? clarify,
    int? attachmentCount,
    String? lastSid,
    String? sname,
    UserDeletedStatus? userDeletedStatus,
  }) {
    return ChatUser(
      uid: uid ?? this.uid,
      uname: uname ?? this.uname,
      message: message ?? this.message,
      updatedBy: updatedBy ?? this.updatedBy,
      localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
      cloudUpdatedAt: cloudUpdatedAt ?? this.cloudUpdatedAt,
      createdAt: createdAt ?? this.createdAt,
      status: status ?? this.status,
      prevStatus: prevStatus ?? this.prevStatus,
      clarify: clarify ?? this.clarify,
      attachmentCount: attachmentCount ?? this.attachmentCount,
      lastSid: lastSid ?? this.lastSid,
      sname: sname ?? this.sname,
      userDeletedStatus: userDeletedStatus ?? this.userDeletedStatus,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'docVer': docVer,
      'uid': uid,
      'id': id,
      'uname': uname,
      'message': message,
      'updatedBy': updatedBy,
      'localUpdatedAt': localUpdatedAt?.toUtc().toIso8601String(),
      'cloudUpdatedAt': cloudUpdatedAt?.toUtc().toIso8601String(),
      'createdAt': createdAt?.toUtc().toIso8601String(),
      'status': status.name,
      'prevStatus': prevStatus?.name,
      'clarify': clarify,
      'attachmentCount': attachmentCount,
      'lastSid': lastSid,
      'sname': sname,
      'userDeletedStatus': userDeletedStatus?.name,
    };
  }

  @override
  List<Object?> get props {
    return [
      id,
      uid,
      // uname,
      message,
      updatedBy,
      status,
      clarify,
      userDeletedStatus,
    ];
  }

  // toString
  @override
  String toString() {
    return 'ChatUser(uid: $uid, uname: $uname, message: $message, updatedBy: $updatedBy, localUpdatedAt: $localUpdatedAt, cloudUpdatedAt: $cloudUpdatedAt, status: $status, prevStatus: $prevStatus, clarify: $clarify, attachmentCount: $attachmentCount, lastSid: $lastSid, sname: $sname, userDeletedStatus: $userDeletedStatus)';
  }
}
