import 'package:equatable/equatable.dart';

import 'package:mevolvesupport/utilities/utility_methods.dart';

class TransactionModel extends Equatable {
  const TransactionModel({
    required this.orderid,
    required this.uid,
    this.entitlement,
    this.planType,
    this.planStartDate,
    this.planEndDate,
  });

  factory TransactionModel.fromFirestore(Map<String, dynamic> map) {
    return TransactionModel(
      orderid: map['orderid'] as String,
      uid: map['uid'] as String,
      entitlement: map['revenuecatEvent']?['product_id'] != null
          ? (map['revenuecatEvent']['product_id'] as String).split('_')[0]
          : '',
      planType: map['planType'],
      planStartDate: parseDate(map['planStartDate']),
      planEndDate: parseDate(map['planEndDate']),
    );
  }

  final String orderid;
  final String uid;
  final String? entitlement;
  final String? planType;
  final DateTime? planStartDate;
  final DateTime? planEndDate;

  TransactionModel copyWith({
    String? orderid,
    String? uid,
    String? entitlement,
    String? planType,
    DateTime? planStartDate,
    DateTime? planEndDate,
  }) {
    return TransactionModel(
      orderid: orderid ?? this.orderid,
      uid: uid ?? this.uid,
      entitlement: entitlement ?? this.entitlement,
      planType: planType ?? this.planType,
      planStartDate: planStartDate ?? this.planStartDate,
      planEndDate: planEndDate ?? this.planEndDate,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': orderid,
      'uid': uid,
      'entitlement': entitlement,
      'planType': planType,
      'localUpdatedAt': planStartDate?.toUtc().toIso8601String(),
      'cloudUpdatedAt': planEndDate?.toUtc().toIso8601String(),
    };
  }

  @override
  List<Object?> get props {
    return [
      orderid,
      uid,
      planType,
      planStartDate,
      planEndDate,
    ];
  }
}
