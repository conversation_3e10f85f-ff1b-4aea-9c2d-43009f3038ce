import 'package:equatable/equatable.dart';

import 'package:mevolvesupport/enums/purchase/subscription_state.dart';
import 'package:mevolvesupport/enums/purchase/subscription_type.dart';
import 'package:mevolvesupport/enums/purchase/user_entitlements.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class UserslistData extends Equatable {
  const UserslistData({
    required this.id,
    required this.uid,
    required this.name,
    required this.email,
    required this.pseudoName,
    required this.createdAt,
    required this.localUpdatedAt,
    this.cloudUpdatedAt,
    this.deletedAt,
    this.subscriptionState = SubscriptionState.trial,
    this.subscriptionType = SubscriptionType.custom,
    required this.subscriptionStartDate,
    required this.subscriptionExpDate,
    this.userDeletedStatus,
    this.superSubscription = MeUserEntitlement.free,
  });

  factory UserslistData.fromFirestore(Map<String, dynamic> map) {
    final userData = UserslistData(
      id: map['id'],
      uid: map['uid'],
      localUpdatedAt: parseDate(map['localUpdatedAt']),
      cloudUpdatedAt: parseDate(map['cloudUpdatedAt']),
      name: map['userInfo_name'],
      email: map['userInfo_email'],
      pseudoName: map['userInfo_pseudoName'] ?? '',
      createdAt: DateTime.parse(map['userInfo_createdAt']['value']),
      deletedAt: map['userInfo_deletedAt'] == null
          ? null
          : DateTime.tryParse(map['userInfo_deletedAt']['value']),
      subscriptionState: map['subscriptionInfo_subscriptionState'] != null
          ? SubscriptionState.values
              .byName(map['subscriptionInfo_subscriptionState'])
          : SubscriptionState.trial,
      subscriptionType: map['subscriptionInfo_subscriptionType'] != null
          ? SubscriptionType.values
              .byName(map['subscriptionInfo_subscriptionType'])
          : SubscriptionType.custom,
      subscriptionStartDate:
          map['subscriptionInfo_subscriptionStartDate'] != null
              ? DateTime.parse(
                  map['subscriptionInfo_subscriptionStartDate']?['value'],
                )
              : null,
      subscriptionExpDate: map['subscriptionInfo_subscriptionExpDate'] != null
          ? DateTime.parse(
              map['subscriptionInfo_subscriptionExpDate']?['value'],
            )
          : null,
      userDeletedStatus: map['userDeletedStatus'] != null
          ? UserDeletedStatus.values.byName(map['userDeletedStatus'])
          : null,
      superSubscription: map['superSubscription'] != null
          ? MeUserEntitlement.values.byName(map['superSubscription'])
          : null,
    );
    return userData;
  }

  final String id;
  final String uid;
  final String? name;
  final String? email;
  final String pseudoName;
  final DateTime createdAt;
  final DateTime? localUpdatedAt;
  final DateTime? cloudUpdatedAt;
  final DateTime? deletedAt;
  final SubscriptionState subscriptionState;
  final DateTime? subscriptionStartDate;
  final DateTime? subscriptionExpDate;
  final SubscriptionType subscriptionType;
  final UserDeletedStatus? userDeletedStatus;
  final MeUserEntitlement? superSubscription;

  Map<String, Object?> toMap() => {
        'id': id,
        'uid': uid,
        'localUpdatedAt': localUpdatedAt?.toUtc().toIso8601String(),
        'cloudUpdatedAt': null,
        'userInfo_name': name,
        'userInfo_email': email,
        'userInfo_pseudoName': pseudoName,
        'userInfo_createdAt': createdAt.toUtc().toIso8601String(),
        'userInfo_deletedAt': null,
        'subscriptionInfo_subscriptionState': subscriptionState.name,
        'subscriptionInfo_subscriptionType': subscriptionType.name,
        'subscriptionInfo_subscriptionStartDate':
            subscriptionStartDate?.toUtc().toIso8601String(),
        'subscriptionInfo_subscriptionExpDate':
            subscriptionExpDate?.toUtc().toIso8601String(),
        'userDeletedStatus': userDeletedStatus?.name,
        'superSubscription': superSubscription?.name,
      };

  UserslistData copyWith({
    String? name,
    String? email,
    String? pseudoName,
    DateTime? localUpdatedAt,
    SubscriptionType? subscriptionType,
    SubscriptionState? subscriptionState,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionExpDate,
    UserDeletedStatus? userDeletedStatus,
  }) {
    final userData = UserslistData(
      id: id,
      uid: uid,
      name: name ?? this.name,
      email: email ?? this.email,
      pseudoName: pseudoName ?? this.pseudoName,
      createdAt: createdAt,
      deletedAt: deletedAt,
      localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
      subscriptionState: subscriptionState ?? this.subscriptionState,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionStartDate:
          subscriptionStartDate ?? this.subscriptionStartDate,
      subscriptionExpDate: subscriptionExpDate ?? this.subscriptionExpDate,
      userDeletedStatus: userDeletedStatus ?? this.userDeletedStatus,
    );
    return userData;
  }

  @override
  List<Object?> get props {
    return [
      id,
      uid,
      name,
      email,
      pseudoName,
      createdAt,
      localUpdatedAt,
      cloudUpdatedAt,
      deletedAt,
      subscriptionState,
      subscriptionType,
      subscriptionStartDate,
      subscriptionExpDate,
      userDeletedStatus,
    ];
  }
}
