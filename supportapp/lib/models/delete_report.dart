import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/delete_reason_type.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';

class DeleteReport extends Equatable {
  const DeleteReport({
    required this.id,
    required this.uid,
    this.uname,
    required this.reason,
    this.feedback,
    required this.deletedAt,
    this.userDeletedStatus,
  });

  factory DeleteReport.fromFirestore(Map<String, dynamic> map) {
    return DeleteReport(
      id: map['id'],
      uid: map['uid'],
      uname: map['uname'],
      reason: map['reason'] != null
          ? DeletedReasonType.values.byName(map['reason'])
          : DeletedReasonType.none,
      feedback: map['feedback'] as String,
      deletedAt: map['deletedAt']?.toDate(),
      userDeletedStatus: map['userDeletedStatus'] != null
          ? UserDeletedStatus.values.byName(map['userDeletedStatus'])
          : null,
    );
  }

  final String id;
  final String? uname;
  final String uid;
  final DeletedReasonType reason;
  final String? feedback;
  final DateTime deletedAt;
  final UserDeletedStatus? userDeletedStatus;

  DeleteReport copyWith({
    String? id,
    String? uid,
    String? uname,
    DeletedReasonType? reason,
    String? feedback,
    DateTime? deletedAt,
    UserDeletedStatus? userDeletedStatus,
  }) {
    return DeleteReport(
      id: id ?? this.id,
      uid: uid ?? this.uid,
      uname: uname ?? this.uname,
      reason: reason ?? this.reason,
      feedback: feedback ?? this.feedback,
      deletedAt: deletedAt ?? this.deletedAt,
      userDeletedStatus: userDeletedStatus ?? this.userDeletedStatus,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'uid': uid,
      'uname': uname,
      'reason': reason.name,
      'feedback': feedback,
      'deletedAt': deletedAt.toUtc().toIso8601String(),
      'userDeletedStatus': userDeletedStatus?.name,
    };
  }

  @override
  List<Object?> get props {
    return [
      id,
      uid,
      // uname,
      reason,
      // feedback,
      deletedAt,
      // userDeletedStatus,
    ];
  }
}
