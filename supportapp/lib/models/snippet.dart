import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/snippet_status.dart';

import 'package:mevolvesupport/enums/snippet_type.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

class Snippet extends Equatable {
  Snippet({
    this.id,
    required this.type,
    this.title,
    required this.content,
    this.author,
    required this.userSegments,
    required this.position,
    this.status = SnippetStatus.none,
    required this.updatedBy,
    required this.sname,
    required this.localUpdatedAt,
    this.cloudUpdatedAt,
  });

  factory Snippet.fromFirestore(Map<String, dynamic> map) {
    final snippet = Snippet(
      id: map['id'] as String,
      type: SnippetType.values.byName(map['type']),
      title: map['title'] as String?,
      content: map['content'] as String,
      author: map['footer'] as String?,
      userSegments: map['userSegments'] != null
          ? List<String>.from(map['userSegments'])
          : [],
      position: double.parse(map['position'].toString()),
      status: SnippetStatus.values.byName(map['status']),
      updatedBy: map['updatedBy'] as String,
      sname: map['sname'] as String,
      localUpdatedAt: map['localUpdatedAt']?.toDate(),
      cloudUpdatedAt: map['cloudUpdatedAt']?.toDate(),
    );
    return snippet;
  }

  final String? id;
  final int docVer = DatabaseRepository.currentDbVersion;

  final SnippetType type;

  final String? title;
  final String content;
  final String? author;
  final List<String> userSegments;
  final double position;
  final SnippetStatus status;
  final String updatedBy;
  final String sname;
  final DateTime localUpdatedAt;
  final DateTime? cloudUpdatedAt;

  Snippet copyWith({
    SnippetType? type,
    String? title,
    String? content,
    String? author,
    List<String>? userSegments,
    double? position,
    SnippetStatus? status,
    String? updatedBy,
    String? sname,
    DateTime? localUpdatedAt,
  }) {
    return Snippet(
      type: type ?? this.type,
      title: title ?? this.title,
      content: content ?? this.content,
      author: author ?? this.author,
      userSegments: userSegments ?? this.userSegments,
      position: position ?? this.position,
      status: status ?? this.status,
      updatedBy: updatedBy ?? this.updatedBy,
      sname: sname ?? this.sname,
      localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'docVer': docVer,
      'type': type.name,
      'title': title,
      'content': content,
      'footer': author,
      'userSegments': userSegments,
      'position': position,
      'status': status.name,
      'updatedBy': updatedBy,
      'sname': sname,
      'localUpdatedAt': localUpdatedAt.toUtc().toIso8601String(),
      'cloudUpdatedAt': cloudUpdatedAt?.toUtc().toIso8601String(),
    };
  }

  @override
  List<Object?> get props {
    return [
      id,
      docVer,
      type,
      content,
      author,
      userSegments,
      position,
      status,
      updatedBy,
      sname,
      localUpdatedAt,
      cloudUpdatedAt,
    ];
  }
}
