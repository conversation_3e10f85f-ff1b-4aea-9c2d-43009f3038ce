import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:mevolvesupport/constants/extensions.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class MeDateTime extends Equatable {
  factory MeDateTime.fromFirestore(Map<String, dynamic> data) {
    return MeDateTime(
      dateTime: parseDate(data['dateTime'])!,
      timeString: data['timeString'],
      dateString: data['dateString'],
      timeWithOffset: data['timeWithOffset'],
    );
  }

  const MeDateTime({
    required this.dateTime,
    required this.timeString,
    required this.dateString,
    required this.timeWithOffset,
  });

  static MeDateTime? fromDate(DateTime? dateTime) {
    if (dateTime == null) return null;
    return MeDateTime(
      dateTime: dateTime,
      timeString:
          '${dateTime.hour.toString().padLeft(2, '0').toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}',
      dateString:
          '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')}',
      timeWithOffset: dateTime.toIsoDateStringWithOffset(),
    );
  }

  final DateTime dateTime;
  final String timeString;
  final String dateString;
  final String timeWithOffset;

  Map<String, dynamic> toMap() {
    return {
      'dateTime': dateTime.toUtc().toIso8601String(),
      'timeString': timeString,
      'dateString': dateString,
      'timeWithOffset': timeWithOffset,
    };
  }

  TimeOfDay toTimeOfDay() {
    return TimeOfDay(hour: dateTime.hour, minute: dateTime.minute);
  }

  MeDateTime copyWith({
    DateTime? dateTime,
    String? timeString,
    String? dateString,
    String? timeWithOffset,
  }) {
    return MeDateTime(
      dateTime: dateTime ?? this.dateTime,
      timeString: timeString ?? this.timeString,
      dateString: dateString ?? this.dateString,
      timeWithOffset: timeWithOffset ?? this.timeWithOffset,
    );
  }

  @override
  bool operator ==(Object other) {
    return (other is MeDateTime) && other.dateTime == dateTime;
  }

  @override
  int get hashCode => dateTime.hashCode;

  @override
  List<Object?> get props => [dateTime, timeString, dateString, timeWithOffset];
}
