enum Feature { todo, habit, journal, note, favorite, moneyTracker }

enum WidgetType {
  total,
  days,
  stats,
  numericalTotal,
  moods,
  streak,
  average,
  chart,
  calendar,
  hashtag,
}

class FavoriteWidgetsModel {
  factory FavoriteWidgetsModel.fromFirestore(Map<String, dynamic> parsedJson) {
    return FavoriteWidgetsModel(
      id: parsedJson['id'],
      widgetType: WidgetType.values.byName(parsedJson['widgetType']),
      feature: Feature.values.byName(parsedJson['feature']),
    );
  }

  FavoriteWidgetsModel({
    required this.id,
    this.feature = Feature.todo,
    this.widgetType = WidgetType.total,
  });

  final Feature feature;

  final WidgetType widgetType;

  final String id;

  // @override
  // List<Object?> get props => [feature, widgetType, id];

  Map<String, Object?> toMap() => {
        'id': id,
        'widgetType': widgetType.name,
        'feature': feature.name,
      };
}
