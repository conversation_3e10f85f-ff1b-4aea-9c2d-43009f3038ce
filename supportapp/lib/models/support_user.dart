import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/theme_type.dart';

class SupportUser extends Equatable {
  const SupportUser({
    required this.sid,
    required this.sname,
    required this.email,
    this.permissions = const [],
    required this.lastLogin,
    this.appTheme = ThemeType.light,
  });

  factory SupportUser.fromFirestore(Map<String, dynamic> map) {
    return SupportUser(
      sid: map['sid'] as String,
      sname: map['sname'] as String,
      email: map['email'] as String,
      permissions: List<String>.from(map['permissions'] ?? []),
      lastLogin: map['lastLogin'] != null
          ? DateTime.parse(map['lastLogin'].toString())
          : DateTime.now(),
      // Default to current time if null
      appTheme: map['appTheme'] != null
          ? ThemeType.values.byName(map['appTheme'])
          : ThemeType.light,
    );
  }

  final String sid;
  final String sname;
  final String email;
  final List<String> permissions;
  final DateTime lastLogin;
  final ThemeType appTheme;

  SupportUser copyWith({
    String? sid,
    String? sname,
    String? email,
    List<String>? permissions,
    DateTime? lastLogin,
    ThemeType? appTheme,
  }) {
    return SupportUser(
      sid: sid ?? this.sid,
      sname: sname ?? this.sname,
      email: email ?? this.email,
      permissions: permissions ?? this.permissions,
      lastLogin: lastLogin ?? this.lastLogin,
      appTheme: appTheme ?? this.appTheme,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'sid': sid,
      'sname': sname,
      'email': email,
      // 'permissions': permissions,
      'lastLogin': lastLogin.toUtc().toIso8601String(),
      'appTheme': appTheme.name,
    };
  }

  @override
  List<Object?> get props => [
        sid,
        sname,
        email,
        permissions,
        lastLogin,
        appTheme,
      ];
}
