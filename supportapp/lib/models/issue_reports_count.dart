class IssueReportsCount {
  factory IssueReportsCount.fromFirestore(Map<String, dynamic> map) {
    return IssueReportsCount(
      toBeReviewed: map['toBeReviewed'] as int,
      inReview: map['inReview'] as int,
      inProcess: map['inProcess'] as int,
      resolved: map['resolved'] as int,
    );
  }
  const IssueReportsCount({
    required this.toBeReviewed,
    required this.inReview,
    required this.inProcess,
    required this.resolved,
  });

  final int toBeReviewed;
  final int inReview;
  final int inProcess;
  final int resolved;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'toBeReviewed': toBeReviewed,
      'inReview': inReview,
      'inProcess': inProcess,
      'resolved': resolved,
    };
  }
}
