import 'dart:convert';
import 'package:mevolvesupport/enums/today_view_type.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class FeatureSettings {
  factory FeatureSettings.fromMap(Map<String, dynamic> map) {
    return FeatureSettings(
      showTodoFeature: parseBool(map['showTodoFeature']) ?? true,
      showHabitFeature: parseBool(map['showHabitFeature']) ?? true,
      showJournalFeature: parseBool(map['showJournalFeature']) ?? true,
      showNoteFeature: parseBool(map['showNoteFeature']) ?? true,
      showListFeature: parseBool(map['showListFeature']) ?? true,
      userGoal: map['userGoal'],
      showUserGoal: parseBool(map['showUserGoal']) ?? false,
      showFeatureLabels: parseBool(map['showFeatureLabels']) ?? false,
      viewType: map['viewType'] != null
          ? TodayViewType.values.byName(map['viewType'])
          : TodayViewType.chronological,
    );
  }

  factory FeatureSettings.fromJson(String source) =>
      FeatureSettings.fromMap(jsonDecode(source) as Map<String, dynamic>);

  const FeatureSettings({
    this.showTodoFeature = true,
    this.showHabitFeature = true,
    this.showJournalFeature = true,
    this.showNoteFeature = true,
    this.showListFeature = true,
    this.showFeatureLabels = false,
    this.userGoal,
    this.showUserGoal = false,
    this.viewType = TodayViewType.chronological,
  });

  final bool showTodoFeature;
  final bool showHabitFeature;
  final bool showJournalFeature;
  final bool showNoteFeature;
  final bool showListFeature;
  final bool showFeatureLabels;
  final String? userGoal;
  final bool showUserGoal;
  final TodayViewType viewType;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'showTodoFeature': showTodoFeature,
      'showHabitFeature': showHabitFeature,
      'showJournalFeature': showJournalFeature,
      'showNoteFeature': showNoteFeature,
      'showListFeature': showListFeature,
      'userGoal': userGoal,
      'showUserGoal': showUserGoal,
      'showFeatureLabels': showFeatureLabels,
      'viewType': viewType.name,
    };
  }

  String toJson() => jsonEncode(toMap());

  FeatureSettings copyWith({
    bool? showTodoFeature,
    bool? showHabitFeature,
    bool? showJournalFeature,
    bool? showNoteFeature,
    bool? showListFeature,
    bool? showFeatureLabels,
    String? userGoal,
    bool? showUserGoal,
    TodayViewType? viewType,
  }) {
    return FeatureSettings(
      showTodoFeature: showTodoFeature ?? this.showTodoFeature,
      showHabitFeature: showHabitFeature ?? this.showHabitFeature,
      showJournalFeature: showJournalFeature ?? this.showJournalFeature,
      showNoteFeature: showNoteFeature ?? this.showNoteFeature,
      showListFeature: showListFeature ?? this.showListFeature,
      showFeatureLabels: showFeatureLabels ?? this.showFeatureLabels,
      userGoal: userGoal ?? this.userGoal,
      showUserGoal: showUserGoal ?? this.showUserGoal,
      viewType: viewType ?? this.viewType,
    );
  }
}
