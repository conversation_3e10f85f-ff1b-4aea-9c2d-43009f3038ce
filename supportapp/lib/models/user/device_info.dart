import 'dart:convert';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class DeviceInfo {
  factory DeviceInfo.fromMap(Map<String, dynamic> map) {
    return DeviceInfo(
      deviceName: map['deviceName'] as String,
      version: map['version'] as String,
      firstSeen: parseDate(map['firstSeen'])!,
      lastSeen: parseDate(map['lastSeen']),
    );
  }

  factory DeviceInfo.fromJson(String source) =>
      DeviceInfo.fromMap(json.decode(source) as Map<String, dynamic>);
  DeviceInfo({
    required this.deviceName,
    required this.version,
    required this.firstSeen,
    this.lastSeen,
  });

  final String deviceName;
  final String version;
  final DateTime firstSeen;
  final DateTime? lastSeen;

  DeviceInfo copyWith({
    String? deviceName,
    String? version,
    DateTime? firstSeen,
    DateTime? lastSeen,
  }) {
    return DeviceInfo(
      deviceName: deviceName ?? this.deviceName,
      version: version ?? this.version,
      firstSeen: firstSeen ?? this.firstSeen,
      lastSeen: lastSeen ?? this.lastSeen,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'deviceName': deviceName,
      'version': version,
      'firstSeen': firstSeen.toUtc().toIso8601String(),
      'lastSeen': lastSeen?.toUtc().toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());
}
