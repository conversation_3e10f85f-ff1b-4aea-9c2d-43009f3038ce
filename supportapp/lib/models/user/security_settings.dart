import 'dart:convert';

import 'package:mevolvesupport/utilities/nullable.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class SecuritySettings {
  factory SecuritySettings.fromMap(Map<String, dynamic> map) {
    return SecuritySettings(
      passcode: map['passcode'],
      isPasscodeEnabled: parseBool(map['isPasscodeEnabled']) ?? false,
      isBiometricEnabled: parseBool(map['isBiometricEnabled']) ?? false,
    );
  }

  factory SecuritySettings.fromJson(String source) =>
      SecuritySettings.fromMap(json.decode(source) as Map<String, dynamic>);

  SecuritySettings({
    this.isPasscodeEnabled = false,
    this.isBiometricEnabled = false,
    this.passcode,
  });

  final String? passcode;
  final bool isPasscodeEnabled;
  final bool isBiometricEnabled;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'passcode': passcode,
      'isPasscodeEnabled': isPasscodeEnabled,
      'isBiometricEnabled': isBiometricEnabled,
    };
  }

  String toJson() => json.encode(toMap());

  SecuritySettings copyWith({
    Nullable<String>? passcode,
    bool? isPasscodeEnabled,
    bool? isBiometricEnabled,
  }) {
    return SecuritySettings(
      isPasscodeEnabled: isPasscodeEnabled ?? this.isPasscodeEnabled,
      isBiometricEnabled: isBiometricEnabled ?? this.isBiometricEnabled,
      passcode: passcode == null ? this.passcode : passcode.value,
    );
  }
}
