import 'dart:convert';

class FeatureUsageInfo {
  factory FeatureUsageInfo.fromJson(String source) =>
      FeatureUsageInfo.fromMap(json.decode(source) as Map<String, dynamic>);

  factory FeatureUsageInfo.fromMap(Map<String, dynamic> map) {
    return FeatureUsageInfo(
      todosCount: map['todosCount'] ?? 0,
      todoUpdatedAt: map['todoUpdatedAt']?.toDate(),
      notesCount: map['notesCount'] ?? 0,
      noteUpdatedAt: map['noteUpdatedAt']?.toDate(),
      habitsActionsCount: map['habitsCount'] ?? 0,
      habitActionsUpdatedAt: map['habitUpdatedAt']?.toDate(),
      habitsSetupsCount: map['habitsSetupsCount'] ?? 0,
      habitSetupsUpdatedAt: map['habitsSetupsUpdatedAt']?.toDate(),
      journalActionsCount: map['journalsCount'] ?? 0,
      journalActionsUpdatedAt: map['journalUpdatedAt']?.toDate(),
      journalSetupsCount: map['journalSetupsCount'] ?? 0,
      journalSetupsUpdatedAt: map['journalSetupsUpdatedAt']?.toDate(),
      listsCount: map['listsCount'] ?? 0,
      listUpdatedAt: map['listUpdatedAt']?.toDate(),
      featuresUpdatedAt: map['featuresUpdatedAt']?.toDate(),
    );
  }

  FeatureUsageInfo({
    this.todosCount = 0,
    this.todoUpdatedAt,
    this.notesCount = 0,
    this.noteUpdatedAt,
    this.habitsActionsCount = 0,
    this.habitActionsUpdatedAt,
    this.habitsSetupsCount = 0,
    this.habitSetupsUpdatedAt,
    this.journalActionsCount = 0,
    this.journalActionsUpdatedAt,
    this.journalSetupsCount = 0,
    this.journalSetupsUpdatedAt,
    this.listsCount = 0,
    this.listUpdatedAt,
    this.featuresUpdatedAt,
  });

  final int todosCount;
  final DateTime? todoUpdatedAt;
  final int notesCount;
  final DateTime? noteUpdatedAt;
  final int habitsActionsCount;
  final DateTime? habitActionsUpdatedAt;
  final int habitsSetupsCount;
  final DateTime? habitSetupsUpdatedAt;
  final int journalActionsCount;
  final DateTime? journalActionsUpdatedAt;
  final int journalSetupsCount;
  final DateTime? journalSetupsUpdatedAt;
  final int listsCount;
  final DateTime? listUpdatedAt;
  final DateTime? featuresUpdatedAt;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'todosCount': todosCount,
      'todoUpdatedAt': todoUpdatedAt,
      'notesCount': notesCount,
      'noteUpdatedAt': noteUpdatedAt,
      'habitsCount': habitsActionsCount,
      'habitUpdatedAt': habitActionsUpdatedAt,
      'habitsSetupsCount': habitsSetupsCount,
      'habitsSetupsUpdatedAt': habitSetupsUpdatedAt,
      'journalsCount': journalActionsCount,
      'journalUpdatedAt': journalActionsUpdatedAt,
      'journalSetupsCount': journalSetupsCount,
      'journalSetupsUpdatedAt': journalSetupsUpdatedAt,
      'listsCount': listsCount,
      'listUpdatedAt': listUpdatedAt,
      'featuresUpdatedAt': featuresUpdatedAt,
    };
  }

  String toJson() => json.encode(toMap());

  FeatureUsageInfo copyWith({
    int? todosCount,
    DateTime? todoUpdatedAt,
    int? notesCount,
    DateTime? noteUpdatedAt,
    int? habitsActionsCount,
    DateTime? habitActionsUpdatedAt,
    int? habitsSetupsCount,
    DateTime? habitSetupsUpdatedAt,
    int? journalActionsCount,
    DateTime? journalActionsUpdatedAt,
    int? journalSetupsCount,
    DateTime? journalSetupsUpdatedAt,
    int? listsCount,
    DateTime? listUpdatedAt,
    DateTime? featuresUpdatedAt,
  }) {
    return FeatureUsageInfo(
      todosCount: todosCount ?? this.todosCount,
      todoUpdatedAt: todoUpdatedAt ?? this.todoUpdatedAt,
      notesCount: notesCount ?? this.notesCount,
      noteUpdatedAt: noteUpdatedAt ?? this.noteUpdatedAt,
      habitsActionsCount: habitsActionsCount ?? this.habitsActionsCount,
      habitActionsUpdatedAt:
          habitActionsUpdatedAt ?? this.habitActionsUpdatedAt,
      journalActionsCount: journalActionsCount ?? this.journalActionsCount,
      journalActionsUpdatedAt:
          journalActionsUpdatedAt ?? this.journalActionsUpdatedAt,
      listsCount: listsCount ?? this.listsCount,
      listUpdatedAt: listUpdatedAt ?? this.listUpdatedAt,
      featuresUpdatedAt: featuresUpdatedAt ?? this.featuresUpdatedAt,
    );
  }
}
