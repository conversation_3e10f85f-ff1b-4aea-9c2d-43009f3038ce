import 'dart:convert';

import 'package:mevolvesupport/enums/description_type.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class NoteSettings {
  factory NoteSettings.fromJson(String source) =>
      NoteSettings.fromMap(json.decode(source) as Map<String, dynamic>);

  factory NoteSettings.fromMap(Map<String, dynamic> map) {
    return NoteSettings(
      noteDescriptionType: map['noteDescriptionType'] != null
          ? DescriptionType.values.byName(map['noteDescriptionType'])
          : DescriptionType.short,
      noteViewTime: parseBool(map['noteViewTime']) ?? true,
      noteViewImage: parseBool(map['noteViewImage']) ?? true,
      noteViewMood: parseBool(map['noteViewMood']) ?? true,
      noteViewTags: parseBool(map['noteViewTags']) ?? false,
    );
  }
  NoteSettings({
    this.noteDescriptionType = DescriptionType.short,
    this.noteViewTime = true,
    this.noteViewImage = true,
    this.noteViewMood = true,
    this.noteViewTags = false,
  });

  final DescriptionType noteDescriptionType;
  final bool noteViewTime;
  final bool noteViewImage;
  final bool noteViewMood;
  final bool noteViewTags;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'noteDescriptionType': noteDescriptionType.name,
      'noteViewTime': noteViewTime,
      'noteViewImage': noteViewImage,
      'noteViewMood': noteViewMood,
      'noteViewTags': noteViewTags,
    };
  }

  String toJson() => json.encode(toMap());

  NoteSettings copyWith({
    DescriptionType? noteDescriptionType,
    bool? noteViewTime,
    bool? noteViewImage,
    bool? noteViewMood,
    bool? noteViewTags,
  }) {
    return NoteSettings(
      noteDescriptionType: noteDescriptionType ?? this.noteDescriptionType,
      noteViewTime: noteViewTime ?? this.noteViewTime,
      noteViewImage: noteViewImage ?? this.noteViewImage,
      noteViewMood: noteViewMood ?? this.noteViewMood,
      noteViewTags: noteViewTags ?? this.noteViewTags,
    );
  }
}
