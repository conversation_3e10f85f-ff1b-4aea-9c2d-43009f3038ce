import 'dart:convert';

import 'package:mevolvesupport/enums/app_language_type.dart';
import 'package:mevolvesupport/enums/theme_type.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class AppSettings {
  factory AppSettings.fromJson(String source) =>
      AppSettings.fromMap(json.decode(source) as Map<String, dynamic>);

  factory AppSettings.fromMap(Map<String, dynamic> map) {
    return AppSettings(
      themeColor: map['themeColor'] ?? 'green',
      appTheme: map['appTheme'] != null
          ? ThemeType.values.byName(map['appTheme'])
          : ThemeType.dark,
      isVibrationEnabled: parseBool(map['isVibrationEnabled']) ?? true,
      language: map['language'] != null
          ? LanguageType.values.byName(map['language'])
          : LanguageType.english,
    );
  }

  AppSettings({
    this.themeColor = 'green',
    this.isVibrationEnabled = true,
    this.appTheme = ThemeType.dark,
    this.language = LanguageType.english,
  });

  final String themeColor;
  final ThemeType appTheme;
  final bool isVibrationEnabled;
  final LanguageType language;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'themeColor': themeColor.toLowerCase(),
      'appTheme': appTheme.name,
      'isVibrationEnabled': isVibrationEnabled,
      'language': language.name,
    };
  }

  String toJson() => json.encode(toMap());

  AppSettings copyWith({
    String? themeColor,
    ThemeType? appTheme,
    bool? isVibrationEnabled,
    LanguageType? language,
  }) {
    return AppSettings(
      themeColor: themeColor ?? this.themeColor,
      appTheme: appTheme ?? this.appTheme,
      isVibrationEnabled: isVibrationEnabled ?? this.isVibrationEnabled,
      language: language ?? this.language,
    );
  }
}
