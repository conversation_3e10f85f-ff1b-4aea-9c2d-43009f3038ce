import 'dart:convert';

import 'package:collection/collection.dart';

import 'package:mevolvesupport/models/favourite_widget.dart';
import 'package:mevolvesupport/models/hashtag.dart';
import 'package:mevolvesupport/models/user/device_info.dart';
import 'package:mevolvesupport/utilities/nullable.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class UserInfo {
  factory UserInfo.fromMap(Map<String, dynamic> map, {bool fromSql = false}) {
    List<FavoriteWidgetsModel> favMap = [];
    List<MeHashtag> tagsMap = [];
    List<int> remindMeTypesMap = [];
    List<int> alarmsTypesMap = [];
    List<DeviceInfo> devicesMap = [];
    if (map['favorites'] != null) {
      if (fromSql) {
        favMap = map['favorites']
            .map<FavoriteWidgetsModel>(
              (item) => FavoriteWidgetsModel.fromFirestore(
                jsonDecode(
                  item,
                ),
              ),
            )
            .toList();
      } else {
        for (int i = 0; i < map['favorites'].length; i++) {
          favMap.add(FavoriteWidgetsModel.fromFirestore(map['favorites'][i]));
        }
      }
    }
    if (map['tags'] != null) {
      if (fromSql) {
        tagsMap = map['tags']
            .map<MeHashtag>(
              (item) => MeHashtag.fromMap(
                jsonDecode(
                  item,
                ),
              ),
            )
            .toList();
      } else {
        tagsMap =
            (map['tags'] as List).map((e) => MeHashtag.fromMap(e)).toList();
      }
    }
    if (map['remindMeTypes'] != null) {
      remindMeTypesMap = List<int>.from(
        fromSql ? map['remindMeTypes'] : map['remindMeTypes'] as List,
      );
    }

    if (map['alarmsTypes'] != null) {
      alarmsTypesMap = List<int>.from(
        fromSql ? jsonDecode(map['alarmsTypes']) : map['alarmsTypes'] as List,
      );
    }
    if (map['devicesList'] != null) {
      if (fromSql) {
        devicesMap = map['devicesList']
            .map<DeviceInfo>(
              (item) => DeviceInfo.fromMap(
                jsonDecode(
                  item,
                ),
              ),
            )
            .toList();
      } else {
        devicesMap = (map['devicesList'] as List)
            .map((e) => DeviceInfo.fromMap(e))
            .toList();
      }
    }

    return UserInfo(
      name: map['name'] as String,
      email: map['email'] as String,
      createdAt: parseDate(map['createdAt'])!,
      deletedAt: parseDate(map['deletedAt']),
      favorites: favMap,
      tags: tagsMap,
      storageUsed: map['storageUsed'].toDouble(),
      remindMeTypes: remindMeTypesMap,
      alarmsTypes: alarmsTypesMap,
      tokensValidAfterTime: parseDate(map['tokensValidAfterTime']),
      resetPasscode: parseBool(map['resetPasscode']) ?? false,
      pseudoName: map['pseudoName'],
      devicesList: devicesMap,
    );
  }

  UserInfo({
    required this.name,
    required this.email,
    required this.createdAt,
    this.deletedAt,
    this.favorites,
    this.tags = const [],
    this.storageUsed = 0,
    this.remindMeTypes = const [-30, -10, 0, 10],
    this.alarmsTypes = const [-30, -10, 0, 10],
    this.tokensValidAfterTime,
    this.resetPasscode = false,
    this.pseudoName,
    this.devicesList = const [],
  });

  final String name;
  final String email;
  final DateTime createdAt;
  final DateTime? deletedAt;
  final List<FavoriteWidgetsModel>? favorites;
  final List<MeHashtag> tags;
  final double storageUsed;
  final List<int> remindMeTypes;
  final List<int> alarmsTypes;
  final DateTime? tokensValidAfterTime;
  final bool resetPasscode;
  final String? pseudoName;
  final List<DeviceInfo> devicesList;

  Map<String, dynamic> toMap({bool toSql = false}) {
    return <String, dynamic>{
      'name': name,
      'email': email,
      'createdAt': createdAt.toUtc().toIso8601String(),
      'deletedAt': deletedAt?.toUtc().toIso8601String(),
      'favorites': toSql
          ? favorites?.map((e) => jsonEncode(e.toMap())).toList()
          : favorites?.map((e) => e.toMap()).toList(),
      'tags': toSql
          ? tags.map((e) => jsonEncode(e.toMap())).toList()
          : tags.map((e) => e.toMap()).toList(),
      'storageUsed': storageUsed,
      'remindMeTypes': toSql ? jsonEncode(remindMeTypes) : remindMeTypes,
      'alarmsTypes': toSql ? jsonEncode(alarmsTypes) : alarmsTypes,
      'tokensValidAfterTime': tokensValidAfterTime?.toUtc().toIso8601String(),
      'resetPasscode': resetPasscode,
      'pseudoName': pseudoName,
      'devicesList': toSql
          ? devicesList.map((e) => jsonEncode(e.toMap())).toList()
          : devicesList.map((e) => e.toMap()).toList(),
    };
  }

  String toJson() => jsonEncode(toMap(toSql: true));

  Map<String, String> getTagsMap() =>
      tags.isNotEmpty ? {for (var v in tags) v.id!: v.tag!} : {};

  List<String> getTagsId() => getTagsMap().keys.toList();

  List<String> getTags() => getTagsMap().values.toList();

  String? getTagById(String id) =>
      tags.firstWhereOrNull((element) => element.id == id)?.tag!;

  String get storageUsedInGb {
    double storageInGB = storageUsed / 1000000;
    if (storageInGB < 0.01) {
      return storageInGB.toStringAsFixed(3);
    } else if (storageUsed < 0.1) {
      return storageInGB.toStringAsFixed(2);
    }
    return storageInGB.toStringAsFixed(1);
  }

  String get getStorageUsedFormatted {
    if (storageUsed == 0) {
      return '0';
    }
    if (storageUsed < 1024) {
      // Convert to KB
      return '${storageUsed.toStringAsFixed(0)} KB';
    } else if (storageUsed < (1024 * 1024)) {
      // Convert to MB
      double storageInMB = storageUsed / 1024;
      return '${storageInMB.toStringAsFixed(2)} MB';
    } else {
      // Convert to GB
      double storageInGB = storageUsed / (1024 * 1024);
      return '${storageInGB.toStringAsFixed(2)} GB';
    }
  }

  UserInfo copyWith({
    String? name,
    String? email,
    DateTime? createdAt,
    Nullable<DateTime?>? deletedAt,
    List<FavoriteWidgetsModel>? favorites,
    List<MeHashtag>? tags,
    double? storageUsed,
    List<int>? remindMeTypes,
    List<int>? alarmsTypes,
    DateTime? tokensValidAfterTime,
    bool? resetPasscode,
    List<DeviceInfo>? devicesList,
  }) {
    return UserInfo(
      name: name ?? this.name,
      email: email ?? this.email,
      createdAt: createdAt ?? this.createdAt,
      deletedAt: deletedAt == null ? this.deletedAt : deletedAt.value,
      favorites: favorites ?? this.favorites,
      tags: tags ?? this.tags,
      storageUsed: storageUsed ?? this.storageUsed,
      remindMeTypes: remindMeTypes ?? this.remindMeTypes,
      alarmsTypes: alarmsTypes ?? this.alarmsTypes,
      tokensValidAfterTime: tokensValidAfterTime ?? this.tokensValidAfterTime,
      resetPasscode: resetPasscode ?? this.resetPasscode,
      pseudoName: pseudoName,
      devicesList: devicesList ?? this.devicesList,
    );
  }
}
