import 'dart:convert';

import 'package:mevolvesupport/models/user/insight/insight_habit_settings.dart';
import 'package:mevolvesupport/models/user/insight/insight_journal_settings.dart';
import 'package:mevolvesupport/models/user/insight/insight_todo_settings.dart';

class InsightSettings {
  factory InsightSettings.fromMap(Map<String, dynamic>? map) {
    if (map == null) {
      return InsightSettings();
    }
    return InsightSettings(
      insightHabitSettings: InsightHabitSettings.fromMap(
        map['insightHabitSettings'] as Map<String, dynamic>,
      ),
      insightJournalSettings: InsightJournalSettings.fromMap(
        map['insightJournalSettings'] as Map<String, dynamic>,
      ),
      insightTodoSettings: InsightTodoSettings.fromMap(
        map['insightTodoSettings'] as Map<String, dynamic>,
      ),
    );
  }

  factory InsightSettings.fromJson(String source) {
    return InsightSettings.fromMap(json.decode(source) as Map<String, dynamic>);
  }

  InsightSettings({
    InsightHabitSettings? insightHabitSettings,
    InsightJournalSettings? insightJournalSettings,
    InsightTodoSettings? insightTodoSettings,
  })  : insightHabitSettings = insightHabitSettings ?? InsightHabitSettings(),
        insightJournalSettings =
            insightJournalSettings ?? InsightJournalSettings(),
        insightTodoSettings = insightTodoSettings ?? InsightTodoSettings();

  final InsightHabitSettings insightHabitSettings;
  final InsightJournalSettings insightJournalSettings;
  final InsightTodoSettings insightTodoSettings;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'insightHabitSettings': insightHabitSettings.toMap(),
      'insightJournalSettings': insightJournalSettings.toMap(),
      'insightTodoSettings': insightTodoSettings.toMap(),
    };
  }

  String toJson() => json.encode(toMap());

  InsightSettings copyWith({
    InsightHabitSettings? insightHabitSettings,
    InsightJournalSettings? insightJournalSettings,
    InsightTodoSettings? insightTodoSettings,
  }) {
    return InsightSettings(
      insightHabitSettings: insightHabitSettings ?? this.insightHabitSettings,
      insightJournalSettings:
          insightJournalSettings ?? this.insightJournalSettings,
      insightTodoSettings: insightTodoSettings ?? this.insightTodoSettings,
    );
  }
}
