import 'dart:convert';

import 'package:mevolvesupport/utilities/utility_methods.dart';

class InsightTodoSettings {
  factory InsightTodoSettings.fromMap(Map<String, dynamic> map) {
    return InsightTodoSettings(
      resultInPercentage: parseBool(map['resultInPercentage']) ?? false,
    );
  }

  factory InsightTodoSettings.fromJson(String source) =>
      InsightTodoSettings.fromMap(json.decode(source) as Map<String, dynamic>);

  InsightTodoSettings({
    this.resultInPercentage = false,
  });

  final bool resultInPercentage;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'resultInPercentage': resultInPercentage,
    };
  }

  String toJson() => json.encode(toMap());

  InsightTodoSettings copyWith({
    bool? resultInPercentage,
  }) {
    return InsightTodoSettings(
      resultInPercentage: resultInPercentage ?? this.resultInPercentage,
    );
  }
}
