import 'dart:convert';

import 'package:mevolvesupport/utilities/utility_methods.dart';

class InsightJournalSettings {
  factory InsightJournalSettings.fromMap(Map<String, dynamic> map) {
    return InsightJournalSettings(
      resultInPercentage: parseBool(map['resultInPercentage']) ?? false,
    );
  }

  factory InsightJournalSettings.fromJson(String source) =>
      InsightJournalSettings.fromMap(
        json.decode(source) as Map<String, dynamic>,
      );

  InsightJournalSettings({
    this.resultInPercentage = false,
  });

  final bool resultInPercentage;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'resultInPercentage': resultInPercentage,
    };
  }

  String toJson() => json.encode(toMap());

  InsightJournalSettings copyWith({
    bool? resultInPercentage,
  }) {
    return InsightJournalSettings(
      resultInPercentage: resultInPercentage ?? this.resultInPercentage,
    );
  }
}
