import 'dart:convert';

import 'package:mevolvesupport/utilities/utility_methods.dart';

class InsightHabitSettings {
  factory InsightHabitSettings.fromMap(Map<String, dynamic> map) {
    return InsightHabitSettings(
      resultInPercentage: parseBool(map['resultInPercentage']) ?? false,
    );
  }

  factory InsightHabitSettings.fromJson(String source) =>
      InsightHabitSettings.fromMap(json.decode(source) as Map<String, dynamic>);

  InsightHabitSettings({
    this.resultInPercentage = false,
  });

  final bool resultInPercentage;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'resultInPercentage': resultInPercentage,
    };
  }

  String toJson() => json.encode(toMap());

  InsightHabitSettings copyWith({
    bool? resultInPercentage,
  }) {
    return InsightHabitSettings(
      resultInPercentage: resultInPercentage ?? this.resultInPercentage,
    );
  }
}
