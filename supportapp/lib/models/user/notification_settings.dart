import 'dart:convert';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/remind_me_type.dart';
import 'package:mevolvesupport/enums/snooze_type.dart';
import 'package:mevolvesupport/enums/sound_type.dart';
import 'package:mevolvesupport/models/custom/me_date_time.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class NotificationSettings extends Equatable {
  factory NotificationSettings.fromMap(
    Map<String, dynamic> map, {
    bool fromSql = false,
  }) {
    List<int> remindMeTypesMap = [];
    List<int> alarmsTypesMap = [];
    if (map['remindMeTypes'] != null) {
      remindMeTypesMap = List<int>.from(
        fromSql ? map['remindMeTypes'] : map['remindMeTypes'] as List,
      );
    }

    if (map['alarmsTypes'] != null) {
      alarmsTypesMap = List<int>.from(
        fromSql ? jsonDecode(map['alarmsTypes']) : map['alarmsTypes'] as List,
      );
    }
    return NotificationSettings(
      isDailyAgendaEmailNotificationEnabled:
          parseBool(map['isDailyAgendaEmailNotificationEnabled']) ?? false,
      isOverdueEmailNotificationEnabled:
          parseBool(map['isOverdueEmailNotificationEnabled']) ?? false,
      remindMeType: map['remindMeType'] != null
          ? RemindMeType.values.byName(map['remindMeType'])
          : RemindMeType.sameDay,
      emailNotificationTime: map['emailNotificationTime'] == null ||
              map['emailNotificationTime'] is String
          ? MeDateTime.fromDate(
              DateTime(
                DateTime.now().year,
                DateTime.now().month,
                DateTime.now().day,
                7,
                0,
              ),
            )
          : MeDateTime.fromFirestore(
              map['emailNotificationTime'],
            ),
      pinReminder: parseBool(map['pinReminder']) ?? false,
      emailNotificationTimezone: map['emailNotificationTimezone'],
      muteAllDevice: map['muteAllDevice'] == null
          ? null
          : MeDateTime.fromFirestore(map['muteAllDevice']),
      muteAllDeviceStartAt: map['muteAllDeviceStartAt'] == null
          ? null
          : MeDateTime.fromFirestore(map['muteAllDeviceStartAt']),
      isDailyAgendaMobileNotificationEnabled:
          parseBool(map['isDailyAgendaMobileNotificationEnabled']) ?? false,
      mobileSnoozeType: map['mobileSnoozeType'] != null
          ? SnoozeType.values.byName(map['mobileSnoozeType'])
          : SnoozeType.five,
      mobileSoundType: map['mobileSoundType'] != null
          ? SoundType.values.byName(map['mobileSoundType'])
          : SoundType.mevolve1,
      mobileDailyAgendaNotificationTime:
          map['mobileDailyAgendaNotificationTime'] == null ||
                  map['mobileDailyAgendaNotificationTime'] is String
              ? MeDateTime.fromDate(
                  DateTime(
                    DateTime.now().year,
                    DateTime.now().month,
                    DateTime.now().day,
                    7,
                    0,
                  ),
                )
              : MeDateTime.fromFirestore(
                  map['mobileDailyAgendaNotificationTime'],
                ),
      muteAllCustomPresetSelected:
          parseBool(map['muteAllCustomPresetSelected']) ?? false,
      isDailyAgendaTmzDependent:
          parseBool(map['isDailyAgendaTmzDependent']) ?? false,
      isMuteAllDeviceTmzDependent:
          parseBool(map['isMuteAllDeviceTmzDependent']) ?? true,
      remindMeTypes: remindMeTypesMap,
      alarmsTypes: alarmsTypesMap,
    );
  }

  factory NotificationSettings.fromJson(String source) =>
      NotificationSettings.fromMap(json.decode(source) as Map<String, dynamic>);

  const NotificationSettings({
    this.isDailyAgendaEmailNotificationEnabled = false,
    this.isOverdueEmailNotificationEnabled = false,
    this.emailNotificationTime,
    this.emailNotificationTimezone,
    this.remindMeType = RemindMeType.sameDay,
    this.pinReminder = false,
    this.muteAllDevice,
    this.muteAllDeviceStartAt,
    this.isDailyAgendaMobileNotificationEnabled = false,
    this.mobileSnoozeType = SnoozeType.five,
    this.mobileSoundType = SoundType.mevolve1,
    this.mobileDailyAgendaNotificationTime,
    this.muteAllCustomPresetSelected = false,
    this.isDailyAgendaTmzDependent = false,
    this.isMuteAllDeviceTmzDependent = true,
    this.remindMeTypes = const [-30, -10, 0, 10],
    this.alarmsTypes = const [-30, -10, 0, 10],
  });

  final bool isDailyAgendaMobileNotificationEnabled;
  final bool isDailyAgendaEmailNotificationEnabled;
  final bool isOverdueEmailNotificationEnabled;
  final MeDateTime? emailNotificationTime;
  final String? emailNotificationTimezone;
  final RemindMeType remindMeType;
  final bool pinReminder;
  final SnoozeType mobileSnoozeType;
  final SoundType mobileSoundType;
  final MeDateTime? muteAllDevice;
  final MeDateTime? muteAllDeviceStartAt;
  final MeDateTime? mobileDailyAgendaNotificationTime;
  final bool muteAllCustomPresetSelected;
  final bool isDailyAgendaTmzDependent;
  final bool isMuteAllDeviceTmzDependent;
  final List<int> remindMeTypes;
  final List<int> alarmsTypes;

  Map<String, dynamic> toMap({bool toSql = false}) {
    return <String, dynamic>{
      'isDailyAgendaEmailNotificationEnabled':
          isDailyAgendaEmailNotificationEnabled,
      'isOverdueEmailNotificationEnabled': isOverdueEmailNotificationEnabled,
      'remindMeType': remindMeType.name,
      'emailNotificationTime': emailNotificationTime?.toMap(),
      'pinReminder': pinReminder,
      'emailNotificationTimezone': emailNotificationTimezone,
      'muteAllDevice': muteAllDevice?.toMap(),
      'muteAllDeviceStartAt': muteAllDeviceStartAt?.toMap(),
      'isDailyAgendaMobileNotificationEnabled':
          isDailyAgendaMobileNotificationEnabled,
      'mobileSnoozeType': mobileSnoozeType.name,
      'mobileSoundType': mobileSoundType.name,
      'mobileDailyAgendaNotificationTime':
          mobileDailyAgendaNotificationTime?.toMap(),
      'muteAllCustomPresetSelected': muteAllCustomPresetSelected,
      'isDailyAgendaTmzDependent': isDailyAgendaTmzDependent,
      'isMuteAllDeviceTmzDependent': isMuteAllDeviceTmzDependent,
      'remindMeTypes': toSql ? jsonEncode(remindMeTypes) : remindMeTypes,
      'alarmsTypes': toSql ? jsonEncode(alarmsTypes) : alarmsTypes,
    };
  }

  @override
  List<Object?> get props => [
        isDailyAgendaEmailNotificationEnabled,
        isOverdueEmailNotificationEnabled,
        remindMeType,
        emailNotificationTime,
        pinReminder,
        emailNotificationTimezone,
        muteAllDevice,
        muteAllDeviceStartAt,
        isDailyAgendaMobileNotificationEnabled,
        mobileSnoozeType,
        mobileSoundType,
        mobileDailyAgendaNotificationTime,
        muteAllCustomPresetSelected,
        isDailyAgendaTmzDependent,
        isMuteAllDeviceTmzDependent,
      ];
}
