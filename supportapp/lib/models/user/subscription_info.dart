import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/purchase/subscription_state.dart';
import 'package:mevolvesupport/enums/purchase/subscription_type.dart';
import 'package:mevolvesupport/enums/purchase/user_entitlements.dart';
import 'package:mevolvesupport/utilities/nullable.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class SubscriptionInfo extends Equatable {
  factory SubscriptionInfo.fromMap(Map<String, dynamic> map) {
    return SubscriptionInfo(
      subscriptionState: map['subscriptionState'] != null
          ? SubscriptionState.values.byName(map['subscriptionState'])
          : SubscriptionState.trial,
      subscriptionType: map['subscriptionType'] != null
          ? SubscriptionType.values.byName(map['subscriptionType'])
          : SubscriptionType.custom,
      subscriptionStartDate: parseDate(map['subscriptionStartDate']),
      subscriptionExpDate: parseDate(map['subscriptionExpDate']),
      unsubscribedAt: parseDate(map['unsubscribedAt']),
      storeType: map['storeType'],
      productId: map['productId'],
      entitlement: map['entitlement'] != null
          ? map['entitlement'] == 'ultra'
              ? MeUserEntitlement.plus
              : MeUserEntitlement.values.byName(map['entitlement'])
          : MeUserEntitlement.free,
    );
  }

  factory SubscriptionInfo.fromJson(String source) =>
      SubscriptionInfo.fromMap(json.decode(source) as Map<String, dynamic>);

  const SubscriptionInfo({
    this.subscriptionState = SubscriptionState.none,
    this.subscriptionType = SubscriptionType.custom,
    required this.subscriptionStartDate,
    required this.subscriptionExpDate,
    this.storeType,
    this.productId,
    this.entitlement = MeUserEntitlement.free,
    this.unsubscribedAt,
  });

  final SubscriptionState subscriptionState;
  final DateTime? subscriptionStartDate;
  final DateTime? subscriptionExpDate;
  final SubscriptionType subscriptionType;
  final String? storeType;
  final String? productId;
  final MeUserEntitlement entitlement;
  final DateTime? unsubscribedAt;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'subscriptionState': subscriptionState.name,
      'subscriptionType': subscriptionType.name,
      'subscriptionStartDate': subscriptionStartDate?.toUtc().toIso8601String(),
      'subscriptionExpDate': subscriptionExpDate?.toUtc().toIso8601String(),
      'storeType': storeType,
      'productId': productId,
      'entitlement': entitlement.name,
      'unsubscribedAt': unsubscribedAt?.toUtc().toIso8601String(),
    };
  }

  String toJson() => json.encode(toMap());

  SubscriptionInfo copyWith({
    SubscriptionState? subscriptionState,
    DateTime? subscriptionStartDate,
    DateTime? subscriptionExpDate,
    SubscriptionType? subscriptionType,
    String? storeType,
    String? productId,
    MeUserEntitlement? entitlement,
    Nullable<DateTime>? unsubscribedAt,
  }) {
    return SubscriptionInfo(
      subscriptionState: subscriptionState ?? this.subscriptionState,
      subscriptionType: subscriptionType ?? this.subscriptionType,
      subscriptionStartDate:
          subscriptionStartDate ?? this.subscriptionStartDate,
      subscriptionExpDate: subscriptionExpDate ?? this.subscriptionExpDate,
      storeType: storeType ?? this.storeType,
      productId: productId ?? this.productId,
      entitlement: entitlement ?? this.entitlement,
      unsubscribedAt: unsubscribedAt?.value ?? this.unsubscribedAt,
    );
  }

  @override
  List<Object?> get props => [
        subscriptionState,
        subscriptionStartDate,
        subscriptionExpDate,
        subscriptionType,
        storeType,
        productId,
        entitlement,
        unsubscribedAt,
      ];
}
