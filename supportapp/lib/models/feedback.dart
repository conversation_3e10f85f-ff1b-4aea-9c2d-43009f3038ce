import 'dart:convert';

import 'package:equatable/equatable.dart';

import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class FeedbackModel extends Equatable {
  FeedbackModel({
    required this.id,
    required this.uid,
    this.localUpdatedAt,
    this.cloudUpdatedAt,
    required this.emotion,
    this.feedbackText,
    this.isStoreFeedbackGiven,
    required this.attachments,
    this.isArchived = false,
    this.userDeletedStatus,
    this.uname,
    this.note,
    required this.updatedBy,
    this.lastSid,
    this.sname,
  });

  factory FeedbackModel.fromFirestore(Map<String, dynamic> map) {
    List<MeAttachmentInfo> attachments = [];
    attachments = map['attachments'] == null
        ? []
        : (map['attachments'] as List)
            .map((e) => MeAttachmentInfo.fromMap(e))
            .toList();
    return FeedbackModel(
      id: map['id'],
      uid: map['uid'] as String,
      localUpdatedAt: parseDate(map['localUpdatedAt'])!,
      cloudUpdatedAt: parseDate(map['cloudUpdatedAt']),
      uname: map['uname'],
      updatedBy: map['updatedBy'] ?? map['uid'],
      lastSid: map['lastSid'],
      sname: map['sname'],
      emotion: map['emotion'] as int,
      feedbackText: map['feedbackText'],
      isStoreFeedbackGiven: map['isStoreFeedbackGiven'],
      isArchived: map['isArchived'] ?? false,
      attachments: attachments,
      userDeletedStatus: map['userDeletedStatus'] != null
          ? UserDeletedStatus.values.byName(map['userDeletedStatus'])
          : null,
      note: map['note'],
    );
  }

  factory FeedbackModel.fromJson(String source) =>
      FeedbackModel.fromFirestore(json.decode(source) as Map<String, dynamic>);

  final String id;
  final int docVer = DatabaseRepository.currentDbVersion;
  final FirebaseDocCollectionType docCollection =
      FirebaseDocCollectionType.chatMessages;
  final String uid;
  final DateTime? localUpdatedAt;
  final DateTime? cloudUpdatedAt;
  final String updatedBy;
  final String? lastSid;
  final String? sname;
  final int emotion;
  final String? feedbackText;
  final bool? isStoreFeedbackGiven;
  final bool? isArchived;
  final List<MeAttachmentInfo> attachments;
  final UserDeletedStatus? userDeletedStatus;
  final String? uname;
  final String? note;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'docVer': docVer,
      'docCollection': docCollection.name,
      'uid': uid,
      'localUpdatedAt': localUpdatedAt?.toUtc().toIso8601String(),
      'cloudUpdatedAt': cloudUpdatedAt?.toUtc().toIso8601String(),
      'updatedBy': updatedBy,
      'lastSid': lastSid,
      'sname': sname,
      'emotion': emotion,
      'feedbackText': feedbackText,
      'isStoreFeedbackGiven': isStoreFeedbackGiven,
      'isArchived': isArchived,
      'attachments': attachments.map((e) => e.toMap()).toList(),
      'userDeletedStatus': userDeletedStatus?.name,
      'uname': uname,
      'note': note,
      // 'encData': {'dek': '', 'encFields': []},
    };
  }

  String toJson() => json.encode(toMap());

  FeedbackModel copyWith({
    DateTime? localUpdatedAt,
    String? uid,
    String? updatedBy,
    String? lastSid,
    String? sname,
    int? emotion,
    String? feedbackText,
    bool? isStoreFeedbackGiven,
    bool? isArchived,
    List<MeAttachmentInfo>? attachments,
    UserDeletedStatus? userDeletedStatus,
    String? uname,
    String? note,
  }) {
    return FeedbackModel(
      id: id,
      uid: uid ?? this.uid,
      updatedBy: updatedBy ?? this.updatedBy,
      lastSid: lastSid ?? this.lastSid,
      sname: sname ?? this.sname,
      emotion: emotion ?? this.emotion,
      feedbackText: feedbackText ?? this.feedbackText,
      isStoreFeedbackGiven: isStoreFeedbackGiven ?? this.isStoreFeedbackGiven,
      localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
      isArchived: isArchived ?? isArchived,
      attachments: attachments ?? this.attachments,
      userDeletedStatus: userDeletedStatus ?? this.userDeletedStatus,
      uname: uname ?? this.uname,
      note: note ?? this.note,
    );
  }

  @override
  List<Object?> get props => [
        id,
        emotion,
        feedbackText,
        isStoreFeedbackGiven,
        isArchived,
        uname,
        note,
        updatedBy,
        // attachments,
      ];
}
