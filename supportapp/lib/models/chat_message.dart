import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/app_language_type.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class ChatMessage extends Equatable {
  factory ChatMessage.fromJson(String source) =>
      ChatMessage.fromFirestore(json.decode(source) as Map<String, dynamic>);
  ChatMessage({
    required this.id,
    required this.uid,
    this.sname,
    required this.updatedBy,
    this.localUpdatedAt,
    required this.message,
    required this.supportLanguage,
    this.cloudUpdatedAt,
    this.attachments,
  });

  factory ChatMessage.fromFirestore(Map<String, dynamic> map) {
    List<MeAttachmentInfo>? attachments;
    if (map['attachments'] != null) {
      attachments = (map['attachments'] as List)
          .map((e) => MeAttachmentInfo.fromMap(e))
          .toList();
    }
    final chatMessage = ChatMessage(
      id: map['id'],
      uid: map['uid'] as String,
      sname: map['sname'],
      updatedBy: map['updatedBy'] as String,
      localUpdatedAt: parseDate(map['localUpdatedAt'])!,
      cloudUpdatedAt: parseDate(map['cloudUpdatedAt']),
      message: map['message'] is String
          ? MessageData.fromString(map['message'])
          : MessageData.fromMap(map['message']),
      supportLanguage:
          map['supportLanguage'] != null && map['supportLanguage'] != 'test'
              ? LanguageType.values.byName(map['supportLanguage'])
              : LanguageType.english,
      attachments: attachments,
    );
    return chatMessage;
  }

  final String id;
  final int docVer = DatabaseRepository.currentDbVersion;
  final FirebaseDocCollectionType docCollection =
      FirebaseDocCollectionType.chatMessages;
  final String uid;
  final String? sname;
  final String updatedBy;

  final DateTime? localUpdatedAt;
  final DateTime? cloudUpdatedAt;
  final MessageData message;
  final LanguageType supportLanguage;
  final List<MeAttachmentInfo>? attachments;

  ChatMessage copyWith({
    String? uid,
    String? sname,
    String? updatedBy,
    DateTime? localUpdatedAt,
    DateTime? cloudUpdatedAt,
    MessageData? message,
    List<MeAttachmentInfo>? attachments,
    LanguageType? supportLanguage,
  }) {
    return ChatMessage(
      id: id,
      uid: uid ?? this.uid,
      sname: sname ?? this.sname,
      updatedBy: updatedBy ?? this.updatedBy,
      localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
      cloudUpdatedAt: cloudUpdatedAt ?? this.cloudUpdatedAt,
      message: message ?? this.message,
      supportLanguage: supportLanguage ?? this.supportLanguage,
      attachments: attachments ?? this.attachments,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'docVer': docVer,
      'docCollection': docCollection.name,
      'uid': uid,
      'sname': sname,
      'updatedBy': updatedBy,
      'createdAt': localUpdatedAt?.toUtc().toIso8601String(),
      'deletedAt': null,
      'permaDeletedAt': null,
      'localUpdatedAt': localUpdatedAt?.toUtc().toIso8601String(),
      'cloudUpdatedAt': cloudUpdatedAt?.toUtc().toIso8601String(),
      'message': message.toMap(),
      'supportLanguage': supportLanguage.name,
      'attachments': attachments?.map((e) => e.toMap()).toList(),
      'source': 'client',
      'encData': {'dek': '0', 'encFields': []},
      'sessionId': id,
    };
  }

  String toJson() => json.encode(toMap());

  @override
  List<Object?> get props => [
        id,
        message,
        // uid,
        // cloudUpdatedAt,
        attachments,
      ];
}

class MessageData {
  MessageData({
    this.en = '',
    this.fr = '',
    this.de = '',
    this.it = '',
    this.pt = '',
    this.es = '',
  });

  factory MessageData.fromMap(Map<String, dynamic> map) {
    return MessageData(
      en: map['en'] ?? '',
      fr: map['fr'] ?? '',
      de: map['de'] ?? '',
      it: map['it'] ?? '',
      pt: map['pt'] ?? '',
      es: map['es'] ?? '',
    );
  }

  factory MessageData.fromString(String msg) {
    return MessageData(
      en: msg,
      fr: '',
      de: '',
      it: '',
      pt: '',
      es: '',
    );
  }

  factory MessageData.fromJson(String source) =>
      MessageData.fromMap(json.decode(source) as Map<String, dynamic>);

  factory MessageData.fromText(LanguageType language, String text) {
    return MessageData(
      en: language == LanguageType.english ? text : '',
      fr: language == LanguageType.french ? text : '',
      de: language == LanguageType.german ? text : '',
      it: language == LanguageType.italian ? text : '',
      pt: language == LanguageType.portuguese ? text : '',
      es: language == LanguageType.spanish ? text : '',
    );
  }

  final String en;
  final String fr;
  final String de;
  final String it;
  final String pt;
  final String es;

  MessageData copyWith({
    String? en,
    String? fr,
    String? de,
    String? it,
    String? pt,
    String? es,
  }) {
    return MessageData(
      en: en ?? this.en,
      fr: fr ?? this.fr,
      de: de ?? this.de,
      it: it ?? this.it,
      pt: pt ?? this.pt,
      es: es ?? this.es,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'en': en,
      'fr': fr,
      'de': de,
      'it': it,
      'pt': pt,
      'es': es,
    };
  }

  String toJson() => json.encode(toMap());

  @override
  String toString() {
    return 'MessageData(en: $en, fr: $fr, de: $de, it: $it, pt: $pt, es: $es)';
  }

  String getMessageFromLang(LanguageType language) {
    final map = toMap();
    return map[language.toLanguageCode()];
  }
}
