import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/models/image_metadata.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class MeAttachmentInfo extends Equatable {
  factory MeAttachmentInfo.clone(MeAttachmentInfo source) {
    return MeAttachmentInfo(
      id: source.id,
      createdAt: source.createdAt,
      size: source.size,
      metadata: source.metadata,
      status: source.status,
      deletedAt: source.deletedAt,
      localFilePath: source.localFilePath,
      format: source.format,
      fileType: source.fileType,
    );
  }

  factory MeAttachmentInfo.fromMap(Map<String, dynamic> map) {
    return MeAttachmentInfo(
      id: map['id'],
      createdAt: parseDate(map['createdAt']),
      size: map['size'],
      metadata: map['metadata'],
      status: AttachmentUploadStatus.values.byName(map['status']),
      deletedAt: parseDate(map['deletedAt']),
      localFilePath: map['localPath'],
      format: map['format'],
      fileType: map['fileType'] != null
          ? FileType.values.byName(map['fileType'])
          : FileType.image,
    );
  }

  const MeAttachmentInfo({
    this.id,
    this.createdAt,
    this.size = 0,
    this.metadata,
    this.status = AttachmentUploadStatus.temporary,
    this.deletedAt,
    this.localFilePath,
    this.format = 'jpeg',
    this.fileType = FileType.image,
  });

  final int? id;
  final DateTime? createdAt;
  final AttachmentUploadStatus status;
  final int size;
  final Object? metadata;
  final DateTime? deletedAt;
  final String? localFilePath;
  final String? format;
  final FileType fileType;

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'createdAt': createdAt!.toUtc().toIso8601String(),
      'status': status.name,
      'size': size,
      'metadata': metadata != null && fileType == FileType.image
          ? (metadata is ImageMetadata)
              ? (metadata as ImageMetadata).toMap()
              : metadata
          : null,
      'deletedAt': deletedAt?.toUtc().toIso8601String(),
      'localPath': localFilePath,
      'format': format,
      'fileType': fileType.name,
    };
  }

  String getAttachmentUrl({
    required String userId,
    required imageType,
    required FileType fileType,
    bool isSupportMedia = false,
    required String docId,
    required FirebaseDocCollectionType docType,
  }) {
    if (status == AttachmentUploadStatus.cloud) {
      final folderName = isSupportMedia ? 'supportApp' : 'userData';
      if (fileType == FileType.image) {
        switch (imageType) {
          // case ImageType.original:
          //   return '$folderName/media/originalFiles/$userId/${docId != null ? '$docId/' : ''}$id.jpeg';
          // case ImageType.optimized:
          //   return '$folderName/media/optimizedImages/$userId/${docId != null ? '$docId/' : ''}$id.jpeg';
          // case ImageType.thumbnail:
          //   return '$folderName/media/thumbnails/$userId/${docId != null ? '$docId/' : ''}$id.jpeg';

          case ImageType.original:
            return '$folderName/media/$userId/${docType.name}/$docId/originalFiles/$id.jpeg';
          case ImageType.optimized:
            return '$folderName/media/$userId/${docType.name}/$docId/optimizedFiles/$id.jpeg';
          case ImageType.thumbnail:
            return '$folderName/media/$userId/${docType.name}/$docId/thumbnails/$id.jpeg';
        }
      } else {
        return '$folderName/media/$userId/${docType.name}/$docId/originalFiles/$id.$format';
      }
      // switch (imageType) {
      //   case ImageType.original:
      //     return 'gs://${FirebaseStorage.instance.bucket}/$folderName/media/originalFiles/$userId/$id.jpeg';
      //   case ImageType.optimized:
      //     return 'gs://${FirebaseStorage.instance.bucket}/$folderName/media/optimizedImages/$userId/$id.jpeg';
      //   case ImageType.thumbnail:
      //     return 'gs://${FirebaseStorage.instance.bucket}/$folderName/media/thumbnails/$userId/$id.jpeg';
      // }
    }

    return localFilePath!;
  }

  @override
  bool operator ==(Object other) =>
      other is MeAttachmentInfo &&
      other.runtimeType == runtimeType &&
      other.id == id &&
      other.status == status &&
      other.format == format &&
      other.createdAt == createdAt &&
      other.localFilePath == localFilePath &&
      other.deletedAt == deletedAt &&
      other.metadata == metadata &&
      other.size == size &&
      other.fileType == fileType;

  @override
  int get hashCode => id.hashCode;

  @override
  List<Object?> get props => [
        id,
        status,
        deletedAt,
      ];
}

enum ImageType {
  original,
  optimized,
  thumbnail,
}

enum AttachmentUploadStatus {
  local,
  cloud,
  temporary,
}

enum FileType {
  image,
  video,
  audio,
  txt,
  document;
}
