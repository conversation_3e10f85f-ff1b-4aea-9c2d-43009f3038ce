class SnippetsCount {
  const SnippetsCount({
    required this.all,
    required this.draft,
    required this.inReview,
    required this.approved,
    required this.published,
  });

  final int all;
  final int draft;
  final int inReview;
  final int approved;
  final int published;

  SnippetsCount copyWith({
    int? all,
    int? draft,
    int? inReview,
    int? approved,
    int? published,
  }) {
    return SnippetsCount(
      all: all ?? this.all,
      draft: draft ?? this.draft,
      inReview: inReview ?? this.inReview,
      approved: approved ?? this.approved,
      published: published ?? this.published,
    );
  }
}
