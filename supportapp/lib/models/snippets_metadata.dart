import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/snippet_metadata_status.dart';
import 'package:mevolvesupport/enums/snippet_metadata_type.dart';

import 'package:mevolvesupport/repositories/database_repository.dart';

class SnippetsMetadata extends Equatable {
  SnippetsMetadata({
    required this.id,
    required this.type,
    this.description,
    this.status = SnippetMetadataStatus.draft,
    required this.updatedBy,
    required this.sname,
    required this.localUpdatedAt,
    this.cloudUpdatedAt,
  });

  factory SnippetsMetadata.fromFirestore(Map<String, dynamic> map) {
    final snippet = SnippetsMetadata(
      id: map['id'] as String,
      type: SnippetMetadataType.values.byName(map['type']),
      description: map['title'] as String?,
      status: SnippetMetadataStatus.values.byName(map['status']),
      updatedBy: map['updatedBy'] as String,
      sname: map['sname'] as String,
      localUpdatedAt: map['localUpdatedAt']?.toDate(),
      cloudUpdatedAt: map['cloudUpdatedAt']?.toDate(),
    );
    return snippet;
  }

  final String id;
  final int docVer = DatabaseRepository.currentDbVersion;
  final SnippetMetadataType type;
  final String? description;
  final SnippetMetadataStatus status;
  final String updatedBy;
  final String sname;
  final DateTime localUpdatedAt;
  final DateTime? cloudUpdatedAt;

  SnippetsMetadata copyWith({
    SnippetMetadataType? type,
    String? description,
    String? content,
    String? author,
    List<String>? screens,
    List<String>? userSegments,
    List<String>? screenPosition,
    double? position,
    SnippetMetadataStatus? status,
    String? updatedBy,
    String? sname,
    DateTime? localUpdatedAt,
  }) {
    return SnippetsMetadata(
      id: id,
      type: type ?? this.type,
      description: description ?? this.description,
      status: status ?? this.status,
      updatedBy: updatedBy ?? this.updatedBy,
      sname: sname ?? this.sname,
      localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'docVer': docVer,
      'type': type.name,
      'title': description,
      'status': status.name,
      'updatedBy': updatedBy,
      'sname': sname,
      'localUpdatedAt': localUpdatedAt.toUtc().toIso8601String(),
      'cloudUpdatedAt': cloudUpdatedAt?.toUtc().toIso8601String(),
    };
  }

  @override
  List<Object?> get props {
    return [
      id,
      docVer,
      description,
      type,
      status,
      updatedBy,
      sname,
      localUpdatedAt,
      cloudUpdatedAt,
    ];
  }
}
