import 'package:mevolvesupport/enums/snippet_metadata_status.dart';

class SnippetsMetadataFilter {
  const SnippetsMetadataFilter({
    this.status,
    this.sid,
  });

  final SnippetMetadataStatus? status;
  final String? sid;

  SnippetsMetadataFilter copyWith({
    SnippetMetadataStatus? status,
    String? sid,
  }) {
    return SnippetsMetadataFilter(
      status: status ?? this.status,
      sid: sid ?? this.sid,
    );
  }
}
