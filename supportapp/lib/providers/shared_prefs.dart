import 'package:shared_preferences/shared_preferences.dart';

class SharedPreferencesClient {
  late SharedPreferences _sharedPreferences;
  final String _lastSync = 'lastSync';

  Future<SharedPreferences> initSharedPrefs() async =>
      _sharedPreferences = await SharedPreferences.getInstance();

  Future<void> setLastSync(String lastSyncTime) async {
    await _sharedPreferences.setString(_lastSync, lastSyncTime);
  }

  String? getLastSync() {
    return _sharedPreferences.getString(_lastSync);
  }

  Future<void> removeLastSync() async {
    await _sharedPreferences.remove(_lastSync);
  }
}
