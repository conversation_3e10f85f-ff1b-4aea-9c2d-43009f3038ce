import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/delete_report.dart';

/// State specifically for deleted reports data management
class DeleteReportsDataState extends Equatable {
  const DeleteReportsDataState({
    required this.deleteReports,
    required this.isLoading,
    required this.isLoadingMore,
    required this.hasMore,
    required this.totalCount,
    required this.lastUpdate,
    this.error,
    this.hasNewUpdates = false,
    this.filters = const [],
  });

  /// Current list of deleted reports being displayed
  final List<DeleteReport> deleteReports;

  /// Loading states
  final bool isLoading; // Initial load
  final bool isLoadingMore; // Loading next batch
  final bool hasMore; // Can load more reports

  /// Counts and metadata
  final int totalCount; // Total count from Firestore
  final DateTime lastUpdate; // Last time data was updated
  final String? error; // Current error if any
  final bool hasNewUpdates; // New reports available

  /// Current filters applied (DeletedReasonType as string list)
  final List<String> filters;

  /// Initial state
  static DeleteReportsDataState initial() {
    return DeleteReportsDataState(
      deleteReports: const [],
      isLoading: true,
      isLoadingMore: false,
      hasMore: true,
      totalCount: 0,
      lastUpdate: DateTime.now(),
      error: null,
      hasNewUpdates: false,
      filters: const [], // Default: show all deleted reports
    );
  }

  /// Create a copy with updated values
  DeleteReportsDataState copyWith({
    List<DeleteReport>? deleteReports,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    int? totalCount,
    DateTime? lastUpdate,
    String? error,
    bool? hasNewUpdates,
    List<String>? filters,
  }) {
    return DeleteReportsDataState(
      deleteReports: deleteReports ?? this.deleteReports,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      totalCount: totalCount ?? this.totalCount,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error,
      hasNewUpdates: hasNewUpdates ?? this.hasNewUpdates,
      filters: filters ?? this.filters,
    );
  }

  @override
  List<Object?> get props => [
        deleteReports,
        isLoading,
        isLoadingMore,
        hasMore,
        totalCount,
        lastUpdate,
        error,
        hasNewUpdates,
        filters,
      ];
}
