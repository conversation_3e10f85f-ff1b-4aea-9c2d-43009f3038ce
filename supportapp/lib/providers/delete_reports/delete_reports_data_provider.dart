import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:mevolvesupport/enums/delete_reason_type.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/providers/delete_reports/delete_reports_data_state.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

/// **DeleteReportsDataProvider - Advanced Real-time Deleted Reports List Manager**
///
/// This provider implements a sophisticated deleted reports list management system with:
/// - Real-time updates for displayed items only (performance optimized)
/// - Intelligent staleness detection to notify when list needs refresh
/// - Cursor-based pagination with smart totalCount adjustment
/// - Deleted user handling with status indicators
/// - Flexible filtering system for delete reason types
///
/// ## 🏗️ ARCHITECTURE OVERVIEW
///
/// Follows the same patterns as FeedbackDataProvider:
/// - Initial load with total count
/// - Targeted real-time listeners for displayed items
/// - Background staleness detection
/// - Smart pagination with duplicate handling
class DeleteReportsDataProvider extends Cubit<DeleteReportsDataState> {
  DeleteReportsDataProvider({
    required DatabaseRepository databaseRepository,
  })  : _databaseRepository = databaseRepository,
        super(DeleteReportsDataState.initial());

  final DatabaseRepository _databaseRepository;

  // ============================================================================
  // PRIVATE STATE VARIABLES
  // ============================================================================

  // --- Real-time Stream Subscriptions ---

  /// Listener for displayed deleted reports updates (efficient ID-based targeting)
  StreamSubscription? _displayedReportsListener;

  /// Listener for staleness detection (background monitoring, non-blocking)
  StreamSubscription? _stalenessDetectionListener;

  // --- Pagination & Cursor State ---

  /// Pagination cursor using deletedAt timestamp
  DateTime? _lastDeletedDateTime;

  /// Items per page for initial load
  static const int _itemsPerPage = 10;

  /// Batch size for loadMore operations
  static const int _loadMoreBatchSize = 10;

  /// Maximum attempts for shortfall handling
  static const int _maxShortfallAttempts = 5;

  /// Counter for generating unique placeholder names for deleted users
  int _deletedUserIdCounter = 1;

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  @override
  Future<void> close() {
    _displayedReportsListener?.cancel();
    _stalenessDetectionListener?.cancel();
    return super.close();
  }

  // ============================================================================
  // PUBLIC API METHODS
  // ============================================================================

  /// Initialize deleted reports list with filters and set up real-time listeners
  void initialize({List<String>? filters}) {
    Log.d(
      '🔄 DeleteReportsDataProvider: Initializing with filters: $filters',
    );
    Log.d('  🗑️ Cancelling old listeners...');
    _cancelListeners();
    Log.d('  🔄 Resetting pagination...');
    _resetPagination();

    Log.d('  🎨 Emitting loading state with new filters...');
    emit(
      DeleteReportsDataState.initial().copyWith(
        filters: filters,
      ),
    );

    Log.d('  👂 Setting up new listeners...');
    _setupListeners();
  }

  /// Refresh entire deleted reports list and clear staleness indicators
  Future<void> refresh() async {
    Log.d('🔄 DeleteReportsDataProvider: Refreshing deleted reports data');

    // Clear new updates flag when refreshing
    emit(state.copyWith(hasNewUpdates: false));

    // Re-initialize with current filters
    initialize(filters: state.filters);
  }

  /// Change filters and reload data
  void changeFilters(List<String> filters) {
    Log.d('🎛️ DeleteReportsDataProvider: Changing filters');
    Log.d('  🔄 OLD filters: ${state.filters}');
    Log.d('  ✨ NEW filters: $filters');

    initialize(filters: filters);
  }

  /// Load more deleted reports with pagination
  Future<void> loadMore() async {
    if (state.isLoadingMore || !state.hasMore) return;

    Log.d('📄 LoadMore: Loading next $_loadMoreBatchSize deleted reports...');
    emit(state.copyWith(isLoadingMore: true));

    try {
      // Convert string filters to enum
      DeletedReasonType? reasonFilter;
      if (state.filters.isNotEmpty && state.filters.first != 'none') {
        reasonFilter = DeletedReasonType.values.firstWhereOrNull(
          (type) => type.name == state.filters.first,
        );
      }

      // Initial fetch
      var newReports = await _databaseRepository.fetchDeleteReports(
        lastReportDateTime: _lastDeletedDateTime,
        deletedReasonType: reasonFilter,
      );

      if (newReports.isEmpty) {
        // Handle empty case
        emit(
          state.copyWith(
            isLoadingMore: false,
            hasMore: false,
            totalCount: state.deleteReports.length,
          ),
        );
        return;
      }

      // Process deleted users
      var processedReports = _processDeletedUsers(newReports);
      final existingIds = state.deleteReports.map((r) => r.id).toSet();
      var newUniqueReports =
          processedReports.where((r) => !existingIds.contains(r.id)).toList();

      // Handle duplicate shortfall with cursor advancement
      int shortfallAttempts = 0;

      while (newUniqueReports.length < _loadMoreBatchSize &&
          newReports.length == _loadMoreBatchSize &&
          shortfallAttempts < _maxShortfallAttempts) {
        shortfallAttempts++;
        Log.d(
          '🔄 Shortfall attempt $shortfallAttempts/$_maxShortfallAttempts: ${newUniqueReports.length} < $_loadMoreBatchSize, fetching more...',
        );

        // Sort ALL processed items to advance cursor
        _sortReportsByTimestamp(processedReports);

        // Update cursor from sorted batch
        if (processedReports.isNotEmpty) {
          _lastDeletedDateTime = processedReports.last.deletedAt;
          Log.d('🎯 Cursor advanced to: $_lastDeletedDateTime');
        }

        // Fetch next batch
        final additionalBatch = await _databaseRepository.fetchDeleteReports(
          lastReportDateTime: _lastDeletedDateTime,
          deletedReasonType: reasonFilter,
        );

        // Check for end conditions
        if (additionalBatch.isEmpty ||
            additionalBatch.length < _loadMoreBatchSize) {
          Log.d('🏁 Reached database end: ${additionalBatch.length} items');
          newReports = additionalBatch;
          break;
        }

        // Process additional batch
        final processedAdditional = _processDeletedUsers(additionalBatch);
        final allIds = [...existingIds, ...newUniqueReports.map((r) => r.id)];
        final uniqueFromAdditional =
            processedAdditional.where((r) => !allIds.contains(r.id)).toList();

        // Add to collections
        newUniqueReports.addAll(uniqueFromAdditional);
        processedReports = [...processedReports, ...processedAdditional];
        newReports = additionalBatch;

        Log.d(
          '📊 Additional: ${processedAdditional.length} processed, ${uniqueFromAdditional.length} unique',
        );
      }

      // Safety check
      if (shortfallAttempts >= _maxShortfallAttempts) {
        Log.w(
            '⚠️ LoadMore: Reached max shortfall attempts ($_maxShortfallAttempts). '
            'Proceeding with ${newUniqueReports.length} unique items to prevent infinite loop.');
      }

      // Final merge and sort
      final allReports = [...state.deleteReports, ...newUniqueReports];
      _sortReportsByTimestamp(allReports);

      // Update final cursor
      if (allReports.isNotEmpty) {
        _lastDeletedDateTime = allReports.last.deletedAt;
      }

      // Determine hasMore
      final gotPartialBatch = newReports.length < _loadMoreBatchSize;
      final reachedExpectedTotal = allReports.length >= state.totalCount;
      final hasMoreData = !gotPartialBatch && !reachedExpectedTotal;
      final adjustedTotalCount =
          gotPartialBatch ? allReports.length : state.totalCount;

      Log.d(
        '✅ LoadMore complete: ${newUniqueReports.length} unique items added',
      );

      emit(
        state.copyWith(
          deleteReports: allReports,
          isLoadingMore: false,
          hasMore: hasMoreData,
          totalCount: adjustedTotalCount,
          lastUpdate: DateTime.now(),
        ),
      );

      // Update listeners
      _updateStalenessListenerCount();
      _displayedReportsListener?.cancel();
      _setupDisplayedReportsListener();
    } catch (error) {
      Log.e('❌ LoadMore error: $error');
      emit(
        state.copyWith(
          isLoadingMore: false,
          error: 'Failed to load more deleted reports: ${error.toString()}',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE SETUP & INITIALIZATION
  // ============================================================================

  Future<void> _setupListeners() async {
    await _loadInitialData();

    Log.d(
      '👂 DeleteReportsDataProvider: Setting up listeners for filters: ${state.filters}',
    );

    _setupDisplayedReportsListener();
  }

  void _cancelListeners() {
    _displayedReportsListener?.cancel();
    _displayedReportsListener = null;
    _stalenessDetectionListener?.cancel();
    _stalenessDetectionListener = null;
  }

  void _resetPagination() {
    _lastDeletedDateTime = null;
    _deletedUserIdCounter = 1;
  }

  // ============================================================================
  // PRIVATE DATA LOADING
  // ============================================================================

  Future<void> _loadInitialData() async {
    Log.d(
      '📥 DeleteReportsDataProvider: Loading initial deleted reports data',
    );
    Log.d('  📋 Filters: ${state.filters}');

    try {
      // Convert string filters to enum
      DeletedReasonType? reasonFilter;
      if (state.filters.isNotEmpty && state.filters.first != 'none') {
        reasonFilter = DeletedReasonType.values.firstWhereOrNull(
          (type) => type.name == state.filters.first,
        );
      }

      // Parallel fetch for optimal performance
      final results = await Future.wait([
        _databaseRepository.fetchDeleteReports(
          deletedReasonType: reasonFilter,
        ),
        _databaseRepository.fetchDeleteReportsCount(
          deletedReasonType: reasonFilter,
        ),
      ]);

      final reports = results[0] as List<DeleteReport>;
      final totalCount = results[1] as int;

      Log.d(
        '✅ DeleteReportsDataProvider: Loaded ${reports.length} initial reports, total count: $totalCount',
      );

      // Apply deleted user processing
      final processedReports = _processDeletedUsers(reports);

      // Sort by timestamp (newest first)
      _sortReportsByTimestamp(processedReports);

      // Set pagination cursor
      if (processedReports.isNotEmpty) {
        _lastDeletedDateTime = processedReports.last.deletedAt;
      }

      // Determine if more pages available
      final hasMoreData = processedReports.length < totalCount;

      emit(
        state.copyWith(
          deleteReports: processedReports,
          totalCount: totalCount,
          isLoading: false,
          error: null,
          lastUpdate: DateTime.now(),
          hasMore: hasMoreData,
          hasNewUpdates: false,
        ),
      );

      // Set up staleness detection after initial data is loaded
      if (processedReports.isNotEmpty) {
        _setupStalenessDetectionListener();
      }
    } catch (error) {
      Log.e(
        '❌ DeleteReportsDataProvider: Error loading initial reports: $error',
      );
      emit(
        state.copyWith(
          isLoading: false,
          error: 'Failed to load deleted reports: $error',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE UTILITY & HELPER METHODS
  // ============================================================================

  List<DeleteReport> _processDeletedUsers(List<DeleteReport> reports) {
    final processedReports = <DeleteReport>[];

    for (var report in reports) {
      if (report.userDeletedStatus != null &&
          (report.userDeletedStatus == UserDeletedStatus.remove_a ||
              report.userDeletedStatus == UserDeletedStatus.remove_u)) {
        final newReport = report.copyWith(
          uname:
              'Deleted user ${report.uname?.isNotEmpty == true ? report.uname : _deletedUserIdCounter}',
        );
        _deletedUserIdCounter++;
        processedReports.add(newReport);
      } else {
        processedReports.add(report);
      }
    }

    return processedReports;
  }

  void _sortReportsByTimestamp(List<DeleteReport> reports) {
    reports.sort((a, b) {
      return b.deletedAt.compareTo(a.deletedAt); // Most recent first
    });
  }

  void _setupStalenessDetectionListener() {
    Log.d(
      '📡 Setting up staleness detection listener - filters: ${state.filters}',
    );

    // Convert string filters to enum
    DeletedReasonType? reasonFilter;
    if (state.filters.isNotEmpty && state.filters.first != 'none') {
      reasonFilter = DeletedReasonType.values.firstWhereOrNull(
        (type) => type.name == state.filters.first,
      );
    }

    final initialCount = state.deleteReports.isNotEmpty
        ? state.deleteReports.length
        : _itemsPerPage;

    _stalenessDetectionListener = _databaseRepository
        .listenDeleteReports(
      deletedReasonType: reasonFilter,
      count: initialCount,
    )
        .listen(
      (realtimeReports) {
        Log.d(
          '🔍 Staleness check: Received ${realtimeReports.length} reports',
        );

        if (state.deleteReports.isNotEmpty) {
          final processedRealtimeReports =
              _processDeletedUsers(realtimeReports);
          _sortReportsByTimestamp(processedRealtimeReports);

          // Compare content by ID sets
          final currentIds = state.deleteReports.map((r) => r.id).toSet();
          final realtimeIds = processedRealtimeReports.map((r) => r.id).toSet();

          final hasChanges =
              !(const SetEquality().equals(currentIds, realtimeIds));

          Log.d(
            '🔍 ID Comparison - Current: ${currentIds.length} items, Realtime: ${realtimeIds.length} items, HasChanges: $hasChanges',
          );

          if (hasChanges) {
            Log.d('🚨 Staleness detected! Setting hasNewUpdates to true');
            emit(
              state.copyWith(
                hasNewUpdates: true,
                lastUpdate: DateTime.now(),
              ),
            );
          }
        }
      },
      onError: (error) {
        Log.e(
          '❌ DeleteReportsDataProvider: Staleness detection error: $error',
        );
      },
    );
  }

  void _updateStalenessListenerCount() {
    if (state.deleteReports.isNotEmpty) {
      Log.d(
        '🔄 Updating staleness listener to match displayed items: ${state.deleteReports.length}',
      );

      _stalenessDetectionListener?.cancel();
      _setupStalenessDetectionListener();
    }
  }

  void _setupDisplayedReportsListener() {
    if (state.deleteReports.isEmpty) {
      Log.d('📡 No displayed items to listen for updates');
      return;
    }

    final displayedIds = state.deleteReports.map((r) => r.id).toList();
    Log.d(
      '📡 Setting up listener for ${displayedIds.length} displayed IDs',
    );

    _displayedReportsListener =
        _databaseRepository.listenDeleteReportUpdatesByIDs(displayedIds).listen(
      (updatedReports) async {
        Log.d(
          '📡 Real-time: Received ${updatedReports.length} updated reports',
        );

        if (updatedReports.isNotEmpty) {
          final currentReports = List<DeleteReport>.from(state.deleteReports);
          final processedUpdatedReports = _processDeletedUsers(updatedReports);

          bool hasChanges = false;
          for (final updatedReport in processedUpdatedReports) {
            final existingIndex =
                currentReports.indexWhere((r) => r.id == updatedReport.id);
            if (existingIndex != -1) {
              currentReports[existingIndex] = updatedReport;
              hasChanges = true;
              Log.d('📡 Updated report: ${updatedReport.id}');
            }
          }

          if (hasChanges) {
            _sortReportsByTimestamp(currentReports);

            if (currentReports.isNotEmpty) {
              _lastDeletedDateTime = currentReports.last.deletedAt;
            }

            Log.d(
              '📡 Real-time: Updated displayed items',
            );

            emit(
              state.copyWith(
                deleteReports: currentReports,
                lastUpdate: DateTime.now(),
              ),
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ DeleteReportsDataProvider: Data listener error: $error');
        emit(
          state.copyWith(
            error: 'Failed to update deleted reports: $error',
          ),
        );
      },
    );
  }
}
