import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart';

import 'package:mevolvesupport/constants/app_config.dart';
import 'package:mevolvesupport/constants/app_constant.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/utilities/logger.dart';

class FirebaseStorageRepository {
  FirebaseStorageRepository({
    FirebaseStorage? firebaseStorage,
  }) : _firebaseStorage = firebaseStorage ?? FirebaseStorage.instance;

  final FirebaseStorage _firebaseStorage;
  static String _secondaryBucketPath =
      'mevolve-app-uploads-${AppConfig.instance.environmentType.name}';

  Future<void> useEmulator() async {
    const host = FirebaseConstants.firebaseLocalHostWebIP;
    await _firebaseStorage.useStorageEmulator(
      host,
      FirebaseConstants.firebaseStoragePort,
    );
    _secondaryBucketPath = FirebaseStorage.instance.bucket;
  }

  Future<bool> uploadFile({
    required String filePath,
    required Uint8List fileBytes,
    required String userId,
    required String documentId,
    required FirebaseDocCollectionType documentType,
  }) async {
    final ref = FirebaseStorage.instanceFor(bucket: _secondaryBucketPath)
        .ref('temp/supportAppMedia/$userId/${documentType.name}/$documentId/')
        .child(basename(filePath));
    try {
      await ref.putData(fileBytes);
      return true;
    } on FirebaseException catch (e) {
      Log.e(e);
      return false;
    }
  }
}
