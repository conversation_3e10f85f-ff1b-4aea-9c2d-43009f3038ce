import 'dart:async';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolvesupport/constants/app_constant.dart';
import 'package:mevolvesupport/constants/extensions.dart';
import 'package:mevolvesupport/enums/allusers_filter_types.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/enums/delete_reason_type.dart';
import 'package:mevolvesupport/enums/feedback_filters.dart';
import 'package:mevolvesupport/enums/issue_report_status.dart';
import 'package:mevolvesupport/enums/purchase/user_entitlements.dart';
import 'package:mevolvesupport/enums/snippet_metadata_type.dart';
import 'package:mevolvesupport/enums/snippet_status.dart';
import 'package:mevolvesupport/models/chat_message.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/chats_count.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/models/issue_report.dart';
import 'package:mevolvesupport/models/issue_reports_count.dart';
import 'package:mevolvesupport/models/segment.dart';
import 'package:mevolvesupport/models/snippet.dart';
import 'package:mevolvesupport/models/snippets_metadata.dart';
import 'package:mevolvesupport/models/snippets_metadata_filter.dart';
import 'package:mevolvesupport/models/subscription_transaction.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/models/user/feature_usage_info.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/models/userslist_data.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:uuid/uuid.dart';

class FirebaseFirestoreRepository {
  FirebaseFirestoreRepository({
    FirebaseFirestore? firebaseFirestore,
  }) : _firebaseFirestore = firebaseFirestore ?? FirebaseFirestore.instance;

  final FirebaseFirestore _firebaseFirestore;
  static const String _fieldVer = 'docVer';

  Future<void> useEmulator() async {
    const host = FirebaseConstants.firebaseLocalHostWebIP;
    _firebaseFirestore.useFirestoreEmulator(
      host,
      FirebaseConstants.firebaseFirestorePort,
    );
  }

  Future<SupportUser> saveSupportUser({required User user}) async {
    Log.d('🗄️ Firestore: Saving support user for: ${user.email}');
    SupportUser supportUser;
    try {
      Log.d('🔍 Firestore: Checking if support user exists in database...');
      final doc = await _firebaseFirestore
          .collection('supportUsers')
          .doc(user.uid)
          .get();
      if (doc.exists) {
        Log.d('✅ Firestore: Existing support user found, updating last login');
        supportUser = SupportUser.fromFirestore(doc.data()!)
            .copyWith(lastLogin: DateTime.now());
        await _firebaseFirestore
            .collection('supportUsers')
            .doc(supportUser.sid)
            .update(supportUser.toMap());
        Log.d('✅ Firestore: Existing support user updated');
      } else {
        Log.d('➕ Firestore: Creating new support user');
        supportUser = SupportUser(
          sid: user.uid,
          sname: user.displayName ?? '',
          email: user.email!,
          lastLogin: DateTime.now(),
        );
        await _firebaseFirestore
            .collection('supportUsers')
            .doc(supportUser.sid)
            .set(supportUser.toMap());
        Log.d('✅ Firestore: New support user created');
      }

      Log.d('🔐 Firestore: Getting user token and permissions...');
      final token = await user.getIdTokenResult();
      List<String> permissions =
          List<String>.from(token.claims?['permissions'] ?? []);
      Log.d('✅ Firestore: User permissions retrieved: $permissions');
      supportUser = supportUser.copyWith(permissions: permissions);
      Log.d('✅ Firestore: Support user save completed successfully');
      return supportUser;
    } catch (e) {
      Log.e('❌ Firestore: Error saving support user: $e');
      rethrow;
    }
  }

  Future<void> updateSupportUser({required SupportUser supportUser}) async {
    return await _firebaseFirestore
        .collection('supportUsers')
        .doc(supportUser.sid)
        .update(supportUser.toMap());
  }

  Future<void> updateSuperSubscription({
    required String uid,
    required MeUserEntitlement subType,
  }) async {
    return await _firebaseFirestore.collection('usersMetadata').doc(uid).set(
      {
        'superSubscription': subType.toQueryValue(),
        'localUpdatedAt': FieldValue.serverTimestamp(),
        'cloudUpdatedAt': FieldValue.serverTimestamp(),
      },
      SetOptions(merge: true),
    );
  }

  Future<UserMetaData?> fetchUpdatedUserData({required String uid}) async {
    final userData = await _firebaseFirestore
        .collection('usersMetadata')
        .where('uid', isEqualTo: uid)
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
        .get();
    if (userData.docs.isEmpty) {
      return null;
    }
    final userDataList = userData.docs
        .map((doc) => UserMetaData.fromFirestore(doc.data()))
        .toList();
    return userDataList.isNotEmpty ? userDataList.first : null;
  }

  Stream<UserMetaData?> listenUser({required String uid}) {
    return _firebaseFirestore
        .collection('usersMetadata')
        .where('uid', isEqualTo: uid)
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
        .snapshots()
        .map((users) {
      List<UserMetaData> userList = users.docs
          .map((user) => UserMetaData.fromFirestore(user.data()))
          .toList();
      return userList.isNotEmpty ? userList.first : null;
    });
  }

  Future<List<UserslistData>> fetchUsers({
    DateTime? startAfter,
    Map<String, dynamic>? withFilters,
    int limit = 10,
  }) async {
    Query<Map<String, dynamic>> userQuery;
    userQuery = _firebaseFirestore.collection('usersMetadata');
    if (withFilters != null && withFilters.isNotEmpty) {
      Filter filterQuery = Filter.and(
        withFilters[AllUsersFilterType.subscriptionType.name] != null
            ? Filter(
                AllUsersFilterType.subscriptionType.toQueryString(),
                whereIn: withFilters[AllUsersFilterType.subscriptionType.name],
              )
            : Filter(
                AllUsersFilterType.subscriptionType.toQueryString(),
                whereIn: ['free', 'trial', 'monthly', 'yearly'],
              ),
        // Plan

        withFilters[AllUsersFilterType.appTheme.name] != null
            ? Filter(
                AllUsersFilterType.appTheme.toQueryString(),
                isEqualTo: withFilters[AllUsersFilterType.appTheme.name],
              )
            : Filter(
                AllUsersFilterType.appTheme.toQueryString(),
                whereIn: ['light', 'dark', 'systemDefault'],
              ),
        // App Theme

        withFilters[AllUsersFilterType.subscriptionState.name] != null
            ? Filter(
                AllUsersFilterType.subscriptionState.toQueryString(),
                whereIn: withFilters[AllUsersFilterType.subscriptionState.name],
              )
            : null,
        // Subscription status

        withFilters[AllUsersFilterType.deleted.name] != null
            ? Filter(
                AllUsersFilterType.deleted.toQueryString(),
                whereIn: withFilters[AllUsersFilterType.deleted.name],
              )
            : null,
        // Deleted status

        withFilters[AllUsersFilterType.subscriptionExpDate.name] != null
            ? Filter.and(
                Filter(
                  AllUsersFilterType.subscriptionExpDate.toQueryString(),
                  isGreaterThan: Timestamp.fromDate(DateTime.now()),
                ),
                Filter(
                  AllUsersFilterType.subscriptionExpDate.toQueryString(),
                  isLessThanOrEqualTo:
                      withFilters[AllUsersFilterType.subscriptionExpDate.name],
                ),
              ) // Expiring in
            : withFilters[AllUsersFilterType.createdAt.name] != null
                ? Filter(
                    AllUsersFilterType.createdAt.toQueryString(),
                    isLessThanOrEqualTo:
                        withFilters[AllUsersFilterType.createdAt.name],
                  )
                : Filter(
                    AllUsersFilterType.createdAt.toQueryString(),
                    isLessThanOrEqualTo: Timestamp.fromDate(
                      DateTime.now().onlyDateWithLastSecond(),
                    ),
                  ),
        // With us

        withFilters[AllUsersFilterType.chatStatus.name] != null
            ? Filter(
                AllUsersFilterType.subscriptionType.toQueryString(),
                whereIn: withFilters[AllUsersFilterType.chatStatus.name],
              )
            : null,
        // Chat status

        withFilters[AllUsersFilterType.reportStatus.name] != null
            ? Filter(
                AllUsersFilterType.subscriptionType.toQueryString(),
                whereIn: withFilters[AllUsersFilterType.reportStatus.name],
              )
            : null,
        // Report status

        withFilters[AllUsersFilterType.isPasscodeEnabled.name] != null
            ? Filter(
                AllUsersFilterType.isPasscodeEnabled.toQueryString(),
                isEqualTo:
                    withFilters[AllUsersFilterType.isPasscodeEnabled.name],
              )
            : Filter(
                AllUsersFilterType.isPasscodeEnabled.toQueryString(),
                isEqualTo: true,
              ),
        // Passcode

        withFilters[AllUsersFilterType.isBiometricEnabled.name] != null
            ? Filter(
                AllUsersFilterType.isBiometricEnabled.toQueryString(),
                isEqualTo:
                    withFilters[AllUsersFilterType.isBiometricEnabled.name],
              )
            : Filter(
                AllUsersFilterType.isBiometricEnabled.toQueryString(),
                isEqualTo: true,
              ), // Biometric
      );
      userQuery = userQuery.where(filterQuery);
    } else {
      userQuery = userQuery.orderBy('createdAt', descending: true).limit(limit);
    }
    if (startAfter != null) {
      userQuery = userQuery.startAfter([Timestamp.fromDate(startAfter)]);
    }
    final users = await userQuery.get();
    if (users.docs.isEmpty) {
      return [];
    }

    final userDataList = users.docs
        .map((doc) => UserslistData.fromFirestore(doc.data()))
        .toList();
    return userDataList.isNotEmpty ? userDataList : [];
  }

  Future<UserMetaData?> fetchUserData(String uid) async {
    final userData =
        await _firebaseFirestore.collection('usersMetadata').doc(uid).get();

    Map<String, dynamic>? userMap = userData.data();
    if (userMap != null) {
      userMap['uid'] = uid;
    }

    return userMap != null ? UserMetaData.fromFirestore(userMap) : null;
  }

  Future<ChatUser?> fetchChatUser(String uid) async {
    final userData = await _firebaseFirestore
        .collection('chatUsers')
        .doc(ChatUser.idFromUid(uid))
        .get();

    Map<String, dynamic>? userMap = userData.data();
    if (userMap != null) {
      userMap['uid'] = uid;
    }

    return userMap != null
        ? ChatUser.fromFirestore(userMap)
        : ChatUser(
            uid: uid,
            uname: '',
            updatedBy: uid,
            status: ChatStatus.replied,
            createdAt: DateTime.now(),
          );
  }

  Future<String?> fetchUidFromEmail(String email) async {
    // First try userKeys collection (faster lookup)
    final userData = await _firebaseFirestore
        .collection('userKeys')
        .where('email', isEqualTo: email)
        .limit(1)
        .get();
    if (userData.docs.isNotEmpty) {
      return userData.docs.first.data()['uid'];
    }

    // If not found in userKeys, search in usersMetadata (includes deleted users)
    final userMetadata = await _firebaseFirestore
        .collection('usersMetadata')
        .where('userInfo.email', isEqualTo: email)
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
        .limit(1)
        .get();
    if (userMetadata.docs.isNotEmpty) {
      return userMetadata.docs.first.data()['uid'];
    }

    return null;
  }

  Future<String?> fetchEmailFromUid(String uid) async {
    final userData = await _firebaseFirestore
        .collection('userKeys')
        .where('uid', isEqualTo: uid)
        .limit(1)
        .get();
    if (userData.docs.isNotEmpty) {
      return userData.docs.first.data()['email'];
    }
    return null;
  }

  Future<String?> fetchUserSupportLanguage(String uid) async {
    final userData = await _firebaseFirestore
        .collection('viewSettings')
        .where('uid', isEqualTo: uid)
        .limit(1)
        .get();
    String supportLanguage = 'english';
    if (userData.size > 0) {
      supportLanguage = userData.docs.first.data()['appSettings']
              ?['supportLanguage'] ??
          'english';
    }
    return supportLanguage;
  }

  Stream<String?> listenUserSupportLanguage(String uid) {
    return _firebaseFirestore
        .collection('viewSettings')
        .where('uid', isEqualTo: uid)
        .limit(1)
        .snapshots()
        .map((QuerySnapshot snapshot) {
      if (snapshot.docs.isNotEmpty) {
        final data = snapshot.docs.first.data() as Map<String, dynamic>?;
        return data?['appSettings']?['supportLanguage'] ?? 'english';
      }
      return 'english'; // Default fallback
    });
  }

  Future<List<IssueReport>> fetchUserIssueReports(String uid) async {
    final issueReports = await _firebaseFirestore
        .collection('issueReports')
        .orderBy('cloudUpdatedAt')
        .where('uid', isEqualTo: uid)
        .get();

    final issueReportsList = issueReports.docs
        .map((doc) => IssueReport.fromFirestore(doc.data()))
        .toList();
    return issueReportsList.isNotEmpty ? issueReportsList : [];
  }

  Future<List<FeedbackModel>> fetchUserFeedbacks(String uid) async {
    final feedbacks = await _firebaseFirestore
        .collection('usersFeedback')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
        .orderBy('cloudUpdatedAt')
        .where('uid', isEqualTo: uid)
        .get();

    final feedbacksList = feedbacks.docs
        .map((doc) => FeedbackModel.fromFirestore(doc.data()))
        .toList();
    return feedbacksList.isNotEmpty ? feedbacksList : [];
  }

  Future<List<TransactionModel>> fetchUserTransactions(
    String uid,
  ) async {
    final transactions = await _firebaseFirestore
        .collection('paymentTransactions')
        // .orderBy('planStartDate', descending: true)
        .where('uid', isEqualTo: uid)
        .get();

    final feedbacksList = transactions.docs
        .map((doc) => TransactionModel.fromFirestore(doc.data()))
        .toList();
    return feedbacksList.isNotEmpty ? feedbacksList : [];
  }

  /// Real-time listener for user-specific feedbacks
  Stream<List<FeedbackModel>> listenUserFeedbacks({required String uid}) {
    return _firebaseFirestore
        .collection('usersFeedback')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
        .where('uid', isEqualTo: uid)
        .orderBy('cloudUpdatedAt')
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => FeedbackModel.fromFirestore(doc.data()))
          .toList();
    });
  }

  /// Real-time listener for user-specific payment transactions
  Stream<List<TransactionModel>> listenUserTransactions({required String uid}) {
    return _firebaseFirestore
        .collection('paymentTransactions')
        .where('uid', isEqualTo: uid)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => TransactionModel.fromFirestore(doc.data()))
          .toList();
    });
  }

  Future<List<ChatUser>> fetchChats({
    required ChatStatus status,
    DateTime? lastLocalDateTime,
    DateTime? lastCloudDateTime, // Keep for backward compatibility but not used
    int limit = 10,
    String? byFilter,
  }) async {
    Query<Map<String, dynamic>> chatQuery;
    chatQuery = _firebaseFirestore
        .collection('chatUsers')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
        .limit(limit);

    // Use only localUpdatedAt for consistent ordering (descending = most recent first)
    chatQuery = chatQuery.orderBy('localUpdatedAt', descending: true);

    // Use only localUpdatedAt for pagination cursor
    if (lastLocalDateTime != null) {
      chatQuery = chatQuery.startAfter([Timestamp.fromDate(lastLocalDateTime)]);
    }

    if (status == ChatStatus.clarify) {
      // For clarify tab: show chats with clarify=true AND status!=resolved
      // Resolved chats should stay in resolved tab even if clarified
      final filters = <Filter>[
        Filter('clarify', isEqualTo: true),
        Filter('status', isNotEqualTo: ChatStatus.resolved.toQueryString()),
      ];

      if (byFilter != null) {
        if (byFilter == 'new') {
          filters.add(Filter('lastSid', isNull: true));
        } else {
          filters.add(Filter('lastSid', isEqualTo: byFilter));
        }
      }

      if (filters.length == 2) {
        chatQuery = chatQuery.where(Filter.and(filters[0], filters[1]));
      } else if (filters.length == 3) {
        chatQuery =
            chatQuery.where(Filter.and(filters[0], filters[1], filters[2]));
      }
    } else if (status == ChatStatus.resolved) {
      // For resolved tab: show chats with status=resolved (regardless of clarify flag)
      chatQuery = chatQuery.where('status', isEqualTo: status.toQueryString());

      if (byFilter != null) {
        if (byFilter == 'new') {
          chatQuery = chatQuery.where('lastSid', isNull: true);
        } else {
          chatQuery = chatQuery.where('lastSid', isEqualTo: byFilter);
        }
      }
    } else {
      // Build filters list, excluding null filters
      final filters = <Filter>[
        Filter('status', isEqualTo: status.toQueryString()),
        Filter.or(
          Filter('clarify', isNull: true),
          Filter('clarify', isEqualTo: false),
        ),
      ];

      // Add byFilter only if it's not null
      if (byFilter != null) {
        if (byFilter == 'new') {
          filters.add(Filter('lastSid', isNull: true));
        } else {
          filters.add(Filter('lastSid', isEqualTo: byFilter));
        }
      }

      if (filters.length == 1) {
        chatQuery = chatQuery.where(filters.first);
      } else if (filters.length == 2) {
        chatQuery = chatQuery.where(Filter.and(filters[0], filters[1]));
      } else if (filters.length == 3) {
        chatQuery =
            chatQuery.where(Filter.and(filters[0], filters[1], filters[2]));
      }
    }

    final chats = await chatQuery.get();
    if (chats.docs.isEmpty) {
      return [];
    }
    final chatsList =
        chats.docs.map((doc) => ChatUser.fromFirestore(doc.data())).toList();
    return chatsList.isNotEmpty ? chatsList : [];
  }

  Stream<List<ChatUser>> listenChatUpdates({
    required ChatStatus status,
    int count = 0,
    String? byFilter,
  }) {
    Query<Map<String, dynamic>> chatQuery;
    chatQuery = _firebaseFirestore
        .collection('chatUsers')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion);

    // Use only localUpdatedAt for consistent ordering (descending = most recent first)
    chatQuery = chatQuery.orderBy('localUpdatedAt', descending: true);

    if (count <= 10) {
      chatQuery = chatQuery.limit(10);
    } else {
      if (count % 10 != 0) {
        count = count + (10 - count % 10);
      }
      chatQuery = chatQuery.limit(count);
      // chatQuery = chatQuery.endAt([Timestamp.fromDate(lastChatDateTime)]);
    }

    if (status == ChatStatus.clarify) {
      chatQuery = chatQuery.where(
        'clarify',
        isEqualTo: true,
      );
      if (byFilter != null) {
        if (byFilter == 'new') {
          chatQuery = chatQuery.where('lastSid', isNull: true);
        } else {
          chatQuery = chatQuery.where('lastSid', isEqualTo: byFilter);
        }
      }
    } else {
      // Build filters list, excluding null filters
      final filters = <Filter>[
        Filter('status', isEqualTo: status.toQueryString()),
        Filter.or(
          Filter('clarify', isNull: true),
          Filter('clarify', isEqualTo: false),
        ),
      ];

      // Add byFilter only if it's not null
      if (byFilter != null) {
        if (byFilter == 'new') {
          filters.add(Filter('lastSid', isNull: true));
        } else {
          filters.add(Filter('lastSid', isEqualTo: byFilter));
        }
      }

      if (filters.length == 1) {
        chatQuery = chatQuery.where(filters.first);
      } else if (filters.length == 2) {
        chatQuery = chatQuery.where(Filter.and(filters[0], filters[1]));
      } else if (filters.length == 3) {
        chatQuery =
            chatQuery.where(Filter.and(filters[0], filters[1], filters[2]));
      }
    }

    return chatQuery
        .snapshots()
        // .map(
        //       (todos) => todos.docChanges
        //           .where(
        //         (change) =>
        //             change.type == DocumentChangeType.added ||
        //             change.type == DocumentChangeType.modified,
        //       )
        //           .map((change) {
        //         return ChatUser.fromFirestore(
        //           change.doc.data() as Map<String, dynamic>,
        //         );
        //       }).toList(),
        //     );

        .map(
          (chats) => chats.docs.map((chat) {
            return ChatUser.fromFirestore(chat.data());
          }).toList(),
        );
  }

  /// Listen for updates to specific chat UIDs
  Stream<List<ChatUser>> listenChatUpdatesByUIDs(List<String> uids) {
    if (uids.isEmpty) {
      return Stream.value([]); // Return empty stream if no UIDs provided
    }

    // Firestore has a limit of 10 items for whereIn queries
    // If we have more than 10 UIDs, we'll need to split them
    if (uids.length <= 10) {
      return _firebaseFirestore
          .collection('chatUsers')
          .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
          .where('uid', whereIn: uids)
          .orderBy('localUpdatedAt', descending: true)
          .snapshots()
          .map(
            (chats) => chats.docs.map((chat) {
              return ChatUser.fromFirestore(chat.data());
            }).toList(),
          );
    } else {
      // Split UIDs into chunks of 10 and combine the streams
      final chunks = <List<String>>[];
      for (int i = 0; i < uids.length; i += 10) {
        chunks.add(uids.sublist(i, (i + 10).clamp(0, uids.length)));
      }

      // Create streams for each chunk and combine them
      final streams = chunks
          .map(
            (chunk) => _firebaseFirestore
                .collection('chatUsers')
                .where(
                  _fieldVer,
                  isEqualTo: DatabaseRepository.currentDbVersion,
                )
                .where('uid', whereIn: chunk)
                .orderBy('localUpdatedAt', descending: true)
                .snapshots()
                .map(
                  (chats) => chats.docs.map((chat) {
                    return ChatUser.fromFirestore(chat.data());
                  }).toList(),
                ),
          )
          .toList();

      // Combine all streams using a simpler approach
      if (streams.length == 1) {
        return streams.first;
      } else {
        // For multiple streams, we'll use a simple merging approach
        // Listen to all streams and combine their latest values
        late StreamController<List<ChatUser>> controller;
        final Map<int, List<ChatUser>> streamData = {};
        final List<StreamSubscription> subscriptions = [];

        void updateCombinedResult() {
          final allChats = <ChatUser>[];
          final seenUIDs = <String>{};

          // Collect all chats from all streams, avoiding duplicates
          for (final chats in streamData.values) {
            for (final chat in chats) {
              if (!seenUIDs.contains(chat.uid)) {
                allChats.add(chat);
                seenUIDs.add(chat.uid);
              }
            }
          }

          // Sort combined results by localUpdatedAt
          allChats.sort((a, b) {
            final aTime = a.localUpdatedAt;
            final bTime = b.localUpdatedAt;
            if (aTime == null && bTime == null) return 0;
            if (aTime == null) return 1;
            if (bTime == null) return -1;
            return bTime.compareTo(aTime); // Most recent first (descending)
          });

          controller.add(allChats);
        }

        controller = StreamController<List<ChatUser>>(
          onListen: () {
            // Subscribe to all streams
            for (int i = 0; i < streams.length; i++) {
              final subscription = streams[i].listen(
                (chats) {
                  streamData[i] = chats;
                  updateCombinedResult();
                },
                onError: (error) {
                  controller.addError(error);
                },
              );
              subscriptions.add(subscription);
            }
          },
          onCancel: () {
            // Cancel all subscriptions
            for (final subscription in subscriptions) {
              subscription.cancel();
            }
            // Close the controller
            controller.close();
          },
        );

        return controller.stream;
      }
    }
  }

  // Add this method to FirebaseFirestoreRepository

  /// Listen for updates to specific user UIDs
  Stream<List<UserMetaData>> listenUserUpdatesByUIDs(List<String> uids) {
    if (uids.isEmpty) {
      return Stream.value([]); // Return empty stream if no UIDs provided
    }

    // Firestore has a limit of 10 items for whereIn queries
    // If we have more than 10 UIDs, we'll need to split them
    if (uids.length <= 10) {
      return _firebaseFirestore
          .collection('usersMetadata')
          .where('uid', whereIn: uids)
          .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
          .orderBy('userInfo.createdAt', descending: true)
          .snapshots()
          .map(
            (users) => users.docs.map((user) {
              return UserMetaData.fromFirestore(user.data());
            }).toList(),
          );
    } else {
      // Split UIDs into chunks of 10 and combine the streams
      final chunks = <List<String>>[];
      for (int i = 0; i < uids.length; i += 10) {
        chunks.add(uids.sublist(i, (i + 10).clamp(0, uids.length)));
      }

      // Create streams for each chunk and combine them
      final streams = chunks
          .map(
            (chunk) => _firebaseFirestore
                .collection('usersMetadata')
                .where('uid', whereIn: chunk)
                .where(
                  _fieldVer,
                  isEqualTo: DatabaseRepository.currentDbVersion,
                )
                .orderBy('userInfo.createdAt', descending: true)
                .snapshots()
                .map(
                  (users) => users.docs.map((user) {
                    return UserMetaData.fromFirestore(user.data());
                  }).toList(),
                ),
          )
          .toList();

      // Combine all streams using a simpler approach
      if (streams.length == 1) {
        return streams.first;
      } else {
        // For multiple streams, we'll use a simple merging approach
        // Listen to all streams and combine their latest values
        late StreamController<List<UserMetaData>> controller;
        final Map<int, List<UserMetaData>> streamData = {};
        final List<StreamSubscription> subscriptions = [];

        void updateCombinedResult() {
          final allUsers = <UserMetaData>[];
          final seenUIDs = <String>{};

          // Collect all users from all streams, avoiding duplicates
          for (final users in streamData.values) {
            for (final user in users) {
              if (!seenUIDs.contains(user.uid)) {
                allUsers.add(user);
                seenUIDs.add(user.uid);
              }
            }
          }

          // Sort combined results by createdAt
          allUsers.sort((a, b) {
            return b.userInfo.createdAt.compareTo(
              a.userInfo.createdAt,
            ); // Most recent first (descending)
          });

          controller.add(allUsers);
        }

        controller = StreamController<List<UserMetaData>>(
          onListen: () {
            // Subscribe to all streams
            for (int i = 0; i < streams.length; i++) {
              final subscription = streams[i].listen(
                (users) {
                  streamData[i] = users;
                  updateCombinedResult();
                },
                onError: (error) {
                  controller.addError(error);
                },
              );
              subscriptions.add(subscription);
            }
          },
          onCancel: () {
            // Cancel all subscriptions
            for (final subscription in subscriptions) {
              subscription.cancel();
            }
            // Close the controller
            controller.close();
          },
        );

        return controller.stream;
      }
    }
  }

  Future<List<ChatMessage>> fetchMessages({
    required String uid,
    DateTime? lastMessageCloudTime,
    DateTime? lastMessageLocalTime,
  }) async {
    Query<Map<String, dynamic>> msgQuery = _firebaseFirestore
        .collection('chatMessages')
        .orderBy('cloudUpdatedAt', descending: true)
        .orderBy('localUpdatedAt', descending: true)
        .limit(30)
        .where('uid', isEqualTo: uid);
    if (lastMessageCloudTime != null && lastMessageLocalTime != null) {
      msgQuery = msgQuery.startAfter([
        Timestamp.fromDate(lastMessageCloudTime),
        Timestamp.fromDate(lastMessageLocalTime),
      ]);
    }

    final messages = await msgQuery.get();
    if (messages.docs.isEmpty) {
      return [];
    }
    final msgList = messages.docs
        .map((doc) => ChatMessage.fromFirestore(doc.data()))
        .toList();
    return msgList.isNotEmpty ? msgList : [];
  }

  Stream<List<ChatMessage>> listenMessages({
    required String uid,
    DateTime? firstMessageCloudTime,
    DateTime? firstMessageLocalTime,
  }) {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('chatMessages')
        .orderBy('cloudUpdatedAt', descending: true)
        .orderBy('localUpdatedAt', descending: true)
        .where('uid', isEqualTo: uid);
    if (firstMessageCloudTime != null && firstMessageLocalTime != null) {
      query = query.endBefore([
        Timestamp.fromDate(firstMessageCloudTime),
        Timestamp.fromDate(firstMessageLocalTime),
      ]);
    }
    return query.snapshots().map(
          (message) => message.docChanges
              .where(
            (change) =>
                change.type == DocumentChangeType.added ||
                change.type == DocumentChangeType.modified,
          )
              .map((msg) {
            return ChatMessage.fromFirestore(msg.doc.data()!);
          }).toList(),
        );
  }

  Future<bool> sendMessage({
    required ChatUser chatUser,
    required ChatMessage chatMessage,
    bool updateCounts = true,
  }) async {
    WriteBatch writeBatch = _firebaseFirestore.batch();
    final chatUserMap = chatUser.toMap();
    chatUserMap.addAll({
      'localUpdatedAt': DateTime.now(),
      'cloudUpdatedAt': FieldValue.serverTimestamp(),
    });

    Map<String, dynamic> chatMessageMap = chatMessage.toMap();
    chatMessageMap.addAll({
      'localUpdatedAt': DateTime.now(),
      'cloudUpdatedAt': FieldValue.serverTimestamp(),
    });
    writeBatch.set(
      _firebaseFirestore.collection('chatUsers').doc(chatUser.id),
      chatUserMap,
      SetOptions(merge: true),
    );

    writeBatch.set(
      _firebaseFirestore
          .collection('chatMessages')
          .doc(chatMessageMap['id'].toString()),
      chatMessageMap,
    );
    if (updateCounts && !chatUser.clarify) {
      final Map<String, dynamic> chatsCountMap = {
        chatUser.status.name: FieldValue.increment(1),
        chatUser.prevStatus!.name: FieldValue.increment(-1),
      };

      writeBatch.update(
        _firebaseFirestore.collection('serverMetadata').doc('supportChatCount'),
        chatsCountMap,
      );
    }

    return await writeBatch
        .commit()
        .then((value) => true)
        .onError((error, stackTrace) => false);
  }

  Future<bool> updateSupportStatus({
    required ChatUser chatUser,
    bool isClarifySwitch = false,
    bool updateCounts = true,
  }) async {
    WriteBatch writeBatch = _firebaseFirestore.batch();
    final Map<String, dynamic> chatUserMap = {};
    chatUserMap.addAll({
      'localUpdatedAt': DateTime.now(),
      'cloudUpdatedAt': FieldValue.serverTimestamp(),
      'updatedBy': chatUser.updatedBy,
      'lastSid': chatUser.lastSid,
      'sname': chatUser.sname,
      'prevStatus': chatUser.prevStatus?.name,
    });
    if (chatUser.status == ChatStatus.clarify) {
      chatUserMap.addAll({
        'clarify': chatUser.clarify,
      });
    }
    if (chatUser.status != ChatStatus.clarify) {
      chatUserMap.addAll({
        'status': chatUser.status.name,
        'clarify': chatUser.clarify,
      });
    }

    writeBatch.update(
      _firebaseFirestore.collection('chatUsers').doc(chatUser.id),
      chatUserMap,
    );

    // Also update the user metadata chatStatus field to keep it in sync
    final Map<String, dynamic> userMetadataMap = {
      'chatStatus': chatUser.status.name,
      'localUpdatedAt': DateTime.now(),
      'cloudUpdatedAt': FieldValue.serverTimestamp(),
    };

    writeBatch.update(
      _firebaseFirestore.collection('usersMetadata').doc(chatUser.uid),
      userMetadataMap,
    );

    final Map<String, dynamic> chatsCountMap = {};
    if (updateCounts) {
      if (isClarifySwitch) {
        if (chatUser.clarify) {
          chatsCountMap.addAll({
            ChatStatus.clarify.name: FieldValue.increment(1),
            chatUser.status.name: FieldValue.increment(-1),
          });
        } else {
          chatsCountMap.addAll({
            chatUser.status.name: FieldValue.increment(1),
            ChatStatus.clarify.name: FieldValue.increment(-1),
          });
        }
      } else {
        chatsCountMap.addAll({
          chatUser.status.name: FieldValue.increment(1),
          chatUser.prevStatus!.name: FieldValue.increment(-1),
        });
      }
    }

    writeBatch.update(
      _firebaseFirestore.collection('serverMetadata').doc('supportChatCount'),
      chatsCountMap,
    );

    return await writeBatch
        .commit()
        .then((value) => true)
        .onError((error, stackTrace) => false);
  }

  Stream<ChatsCount> listenChatsCount() {
    DocumentReference query =
        _firebaseFirestore.collection('serverMetadata').doc('supportChatCount');
    return query.snapshots().map((event) {
      return ChatsCount.fromFirestore(
        event.data() as Map<String, dynamic>,
      );
    });
  }

  Stream<ChatUser?> listenChatBlock({
    required String uid,
  }) {
    DocumentReference query =
        _firebaseFirestore.collection('chatUsers').doc(ChatUser.idFromUid(uid));

    return query.snapshots().map(
          (event) => event.data() != null
              ? ChatUser.fromFirestore(
                  event.data() as Map<String, dynamic>,
                )
              : null,
        );
  }

  Future<ChatsCount> fetchChatsCount() async {
    final chatsCount = await _firebaseFirestore
        .collection('serverMetadata')
        .doc('supportChatCount')
        .get();
    return ChatsCount.fromFirestore(chatsCount.data()!);
  }

  /// Get filtered count using Firestore count aggregation query
  Future<int> fetchFilteredChatsCount({
    required ChatStatus status,
    String? byFilter,
  }) async {
    Query query = _firebaseFirestore
        .collection('chatUsers')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion);

    // Apply the same filters as the existing fetchChats method
    if (status == ChatStatus.clarify) {
      // For clarify tab: show chats with clarify=true AND status!=resolved
      final filters = <Filter>[
        Filter('clarify', isEqualTo: true),
        Filter('status', isNotEqualTo: ChatStatus.resolved.toQueryString()),
      ];

      if (byFilter != null) {
        if (byFilter == 'new') {
          filters.add(Filter('lastSid', isNull: true));
        } else {
          filters.add(Filter('lastSid', isEqualTo: byFilter));
        }
      }

      if (filters.length == 2) {
        query = query.where(Filter.and(filters[0], filters[1]));
      } else if (filters.length == 3) {
        query = query.where(Filter.and(filters[0], filters[1], filters[2]));
      }
    } else if (status == ChatStatus.resolved) {
      // For resolved tab: show chats with status=resolved (regardless of clarify flag)
      query = query.where('status', isEqualTo: status.toQueryString());

      if (byFilter != null) {
        if (byFilter == 'new') {
          query = query.where('lastSid', isNull: true);
        } else {
          query = query.where('lastSid', isEqualTo: byFilter);
        }
      }
    } else {
      // Build filters list, excluding null filters
      final filters = <Filter>[
        Filter('status', isEqualTo: status.toQueryString()),
        Filter.or(
          Filter('clarify', isNull: true),
          Filter('clarify', isEqualTo: false),
        ),
      ];

      // Add byFilter only if it's not null
      if (byFilter != null) {
        if (byFilter == 'new') {
          filters.add(Filter('lastSid', isNull: true));
        } else {
          filters.add(Filter('lastSid', isEqualTo: byFilter));
        }
      }

      if (filters.length == 1) {
        query = query.where(filters.first);
      } else if (filters.length == 2) {
        query = query.where(Filter.and(filters[0], filters[1]));
      } else if (filters.length == 3) {
        query = query.where(Filter.and(filters[0], filters[1], filters[2]));
      }
    }

    // Use Firestore count aggregation to get count without reading documents
    final countQuery = query.count();
    final snapshot = await countQuery.get();

    return snapshot.count ?? 0;
  }

  /// Listen to filtered count for specific chat status and support person filter
  Stream<int> listenFilteredChatsCount({
    required ChatStatus status,
    String? byFilter,
  }) {
    Query query = _firebaseFirestore
        .collection('chatUsers')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion);

    // Apply the same filters as the existing fetchChats method
    if (status == ChatStatus.clarify) {
      // For clarify tab: show chats with clarify=true AND status!=resolved
      final filters = <Filter>[
        Filter('clarify', isEqualTo: true),
        Filter('status', isNotEqualTo: ChatStatus.resolved.toQueryString()),
      ];

      if (byFilter != null) {
        if (byFilter == 'new') {
          query = query.where('lastSid', isNull: true);
        } else {
          query = query.where('lastSid', isEqualTo: byFilter);
        }
      }

      if (filters.length == 2) {
        query = query.where(Filter.and(filters[0], filters[1]));
      } else if (filters.length == 3) {
        query = query.where(Filter.and(filters[0], filters[1], filters[2]));
      }
    } else if (status == ChatStatus.resolved) {
      // For resolved tab: show chats with status=resolved (regardless of clarify flag)
      query = query.where('status', isEqualTo: status.toQueryString());

      if (byFilter != null) {
        if (byFilter == 'new') {
          query = query.where('lastSid', isNull: true);
        } else {
          query = query.where('lastSid', isEqualTo: byFilter);
        }
      }
    } else {
      // Build filters list, excluding null filters
      final filters = <Filter>[
        Filter('status', isEqualTo: status.toQueryString()),
        Filter.or(
          Filter('clarify', isNull: true),
          Filter('clarify', isEqualTo: false),
        ),
      ];

      // Add byFilter only if it's not null
      if (byFilter != null) {
        if (byFilter == 'new') {
          filters.add(Filter('lastSid', isNull: true));
        } else {
          filters.add(Filter('lastSid', isEqualTo: byFilter));
        }
      }

      if (filters.length == 1) {
        query = query.where(filters.first);
      } else if (filters.length == 2) {
        query = query.where(Filter.and(filters[0], filters[1]));
      } else if (filters.length == 3) {
        query = query.where(Filter.and(filters[0], filters[1], filters[2]));
      }
    }

    // Return real-time count stream
    return query.snapshots().map((snapshot) => snapshot.size);
  }

  Stream<ChatMessage?> listenMessageBlock({
    required String id,
  }) {
    DocumentReference query =
        _firebaseFirestore.collection('chatMessages').doc(id);

    return query.snapshots().map(
          (event) => event.data() != null
              ? ChatMessage.fromFirestore(
                  event.data() as Map<String, dynamic>,
                )
              : null,
        );
  }

  Future<int> updateSupportTabCount() async {
    final reportsCount = await _firebaseFirestore
        .collection('serverMetadata')
        .doc('supportChatCount')
        .get();
    return ChatsCount.fromFirestore(reportsCount.data()!).notReplied;
  }

  Stream<int> listenFeedbacksCount() {
    DocumentReference query =
        _firebaseFirestore.collection('serverMetadata').doc('appMetadata');
    return query.snapshots().map(
          (event) =>
              (event.data() as Map<String, dynamic>)['feedbacksCount'] ?? 0,
        );
  }

  Stream<IssueReportsCount> listenIssueReportsCount() {
    DocumentReference query = _firebaseFirestore
        .collection('serverMetadata')
        .doc('issueReportsCount');

    return query.snapshots().map(
          (event) => IssueReportsCount.fromFirestore(
            event.data() as Map<String, dynamic>,
          ),
        );
  }

  Future<IssueReportsCount> fetchIssueReportsCount() async {
    final reportsCount = await _firebaseFirestore
        .collection('serverMetadata')
        .doc('issueReportsCount')
        .get();
    return IssueReportsCount.fromFirestore(reportsCount.data()!);
  }

  Future<int> updateIssueReportsTabCount() async {
    final reportsCount = await _firebaseFirestore
        .collection('serverMetadata')
        .doc('issueReportsCount')
        .get();
    return IssueReportsCount.fromFirestore(reportsCount.data()!).toBeReviewed;
  }

  Future<List<IssueReport>> fetchIssueReports({
    DateTime? lastReportedDateTime,
    IssueReportStatus? reportStatus,
    String? byFilter,
    int limit = 10,
    DateTime? lastCloudDateTime,
    DateTime? lastLocalDateTime,
  }) async {
    Query<Map<String, dynamic>> query =
        _firebaseFirestore.collection('issueReports').limit(limit);
    if (reportStatus == IssueReportStatus.resolved) {
      query = query
          .orderBy('cloudUpdatedAt', descending: true)
          .orderBy('localUpdatedAt', descending: true);
      if (lastCloudDateTime != null && lastLocalDateTime != null) {
        query = query.startAfter([
          Timestamp.fromDate(lastCloudDateTime),
          Timestamp.fromDate(lastLocalDateTime),
        ]);
      }
    } else {
      query = query.orderBy('cloudUpdatedAt').orderBy('localUpdatedAt');
      if (lastCloudDateTime != null && lastLocalDateTime != null) {
        query = query.startAfter([
          Timestamp.fromDate(lastCloudDateTime),
          Timestamp.fromDate(lastLocalDateTime),
        ]);
      }
    }

    if (reportStatus != null && reportStatus != IssueReportStatus.none) {
      query = query.where('status', isEqualTo: reportStatus.name);
    }
    if (byFilter != null) {
      if (byFilter == 'new') {
        query = query.where('lastSid', isNull: true);
      } else {
        query = query.where('lastSid', isEqualTo: byFilter);
      }
    }

    final issueReports = await query.get();
    if (issueReports.docs.isEmpty) {
      return [];
    }
    final issueReportsList = issueReports.docs
        .map((doc) => IssueReport.fromFirestore(doc.data()))
        .toList();
    return issueReportsList.isNotEmpty ? issueReportsList : [];
  }

  Stream<List<IssueReport>> listenIssueReports({
    int count = 0,
    IssueReportStatus? reportStatus,
    String? byFilter,
  }) {
    Query<Map<String, dynamic>> query =
        _firebaseFirestore.collection('issueReports');
    if (reportStatus == IssueReportStatus.resolved) {
      query = query
          .orderBy('cloudUpdatedAt', descending: true)
          .orderBy('localUpdatedAt', descending: true);
    } else {
      query = query.orderBy('reportedAt');
    }
    if (count <= 10) {
      query = query.limit(10);
    } else {
      if (count % 10 != 0) {
        count = count + (10 - count % 10);
      }
      query = query.limit(count);
      // query = query.endAt([Timestamp.fromDate(lastReportDateTime)]);
    }
    if (reportStatus != null && reportStatus != IssueReportStatus.none) {
      query = query.where('status', isEqualTo: reportStatus.toQueryString());
    }
    if (byFilter != null) {
      if (byFilter == 'new') {
        query = query.where('lastSid', isNull: true);
      } else {
        query = query.where('lastSid', isEqualTo: byFilter);
      }
    }

    return query.snapshots().map(
          (reports) => reports.docs.map((msg) {
            return IssueReport.fromFirestore(msg.data());
          }).toList(),
        );
  }

  Future<bool> updateFeedbacktatus({
    required FeedbackModel feedback,
  }) async {
    WriteBatch writeBatch = _firebaseFirestore.batch();
    final Map<String, dynamic> feedbackMap = {};
    feedbackMap.addAll({
      'localUpdatedAt': DateTime.now(),
      'cloudUpdatedAt': FieldValue.serverTimestamp(),
      'note': feedback.note,
      'isArchived': feedback.isArchived,
      'updatedBy': feedback.updatedBy,
      'lastSid': feedback.lastSid,
      'sname': feedback.sname,
    });

    writeBatch.update(
      _firebaseFirestore.collection('usersFeedback').doc(feedback.id),
      feedbackMap,
    );

    return await writeBatch
        .commit()
        .then((value) => true)
        .onError((error, stackTrace) => false);
  }

  Future<bool> updateIssueReportStatus({
    required IssueReport issueReport,
  }) async {
    WriteBatch writeBatch = _firebaseFirestore.batch();
    final Map<String, dynamic> issueReportMap = {};
    issueReportMap.addAll({
      'localUpdatedAt': DateTime.now(),
      'cloudUpdatedAt': FieldValue.serverTimestamp(),
      'updatedBy': issueReport.updatedBy,
      'lastSid': issueReport.lastSid,
      'sname': issueReport.sname,
      'status': issueReport.status.name,
      'prevStatus': issueReport.prevStatus?.name,
      'resolvedReason': issueReport.resolvedReason,
    });

    writeBatch.update(
      _firebaseFirestore.collection('issueReports').doc(issueReport.id),
      issueReportMap,
    );

    final Map<String, dynamic> issueReportsCountMap = {
      issueReport.status.name: FieldValue.increment(1),
      issueReport.prevStatus!.name: FieldValue.increment(-1),
    };

    writeBatch.update(
      _firebaseFirestore.collection('serverMetadata').doc('issueReportsCount'),
      issueReportsCountMap,
    );

    return await writeBatch
        .commit()
        .then((value) => true)
        .onError((error, stackTrace) => false);
  }

  Stream<IssueReport?> listenIssueReportBlock({
    required String id,
  }) {
    DocumentReference query =
        _firebaseFirestore.collection('issueReports').doc(id);

    return query.snapshots().map(
          (event) => event.data() != null
              ? IssueReport.fromFirestore(
                  event.data() as Map<String, dynamic>,
                )
              : null,
        );
  }

  Stream<FeedbackModel?> listenFeedbackBlock({
    required String id,
  }) {
    DocumentReference query =
        _firebaseFirestore.collection('usersFeedback').doc(id);

    return query.snapshots().map(
          (event) => event.data() != null
              ? FeedbackModel.fromFirestore(
                  event.data() as Map<String, dynamic>,
                )
              : null,
        );
  }

  Stream<IssueReport?> listenReportNotReviewed({
    required String uid,
  }) {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('issueReports')
        .where('uid', isEqualTo: uid)
        .where('status', isEqualTo: IssueReportStatus.toBeReviewed.name)
        .limit(1);

    return query.snapshots().map(
          (reports) => reports.docs.isNotEmpty
              ? IssueReport.fromFirestore(reports.docs.first.data())
              : null,
        );
  }

  Future<int> fetchFeedbacksCount({
    List<String>? byFilter,
  }) async {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('usersFeedback')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion);

    // Apply filters based on the requirements table (same logic as fetchFeedbacks)
    if (byFilter != null && byFilter.isNotEmpty) {
      final hasArchived = byFilter.contains(FeedbackFilters.archived.name);
      final hasWithRatings =
          byFilter.contains(FeedbackFilters.withRatings.name);
      final hasWithoutRatings =
          byFilter.contains(FeedbackFilters.withoutRatings.name);

      // Handle filters
      if (hasArchived && hasWithRatings && hasWithoutRatings) {
        // All 3 filters selected: show both archived and non-archived with all emotions
        query = query.where('emotion', isGreaterThanOrEqualTo: -1);
        // Don't filter by isArchived - show both archived and non-archived
      } else if (hasArchived) {
        // archived only: isArchived == true
        query = query.where('isArchived', isEqualTo: true);
        // No emotion filter when only archived is selected
      } else if (hasWithRatings || hasWithoutRatings) {
        // Only emotion filters - exclude archived items
        if (hasWithRatings && hasWithoutRatings) {
          // Both withRatings + withoutRatings: emotion >= -1
          query = query.where('emotion', isGreaterThanOrEqualTo: -1);
        } else if (hasWithRatings) {
          // withRatings only: emotion >= 0
          query = query.where('emotion', isGreaterThanOrEqualTo: 0);
        } else if (hasWithoutRatings) {
          // withoutRatings only: emotion == -1
          query = query.where('emotion', isEqualTo: -1);
        }
      }
      // If no recognized filters → show all (no additional filters)
    }

    // For count queries, we need to handle client-side filtering differently
    // Since we can't apply client-side filtering to count queries,
    // we'll fetch the documents and count them manually when needed
    if (byFilter != null && byFilter.isNotEmpty) {
      final hasArchived = byFilter.contains(FeedbackFilters.archived.name);
      final hasWithRatings =
          byFilter.contains(FeedbackFilters.withRatings.name);
      final hasWithoutRatings =
          byFilter.contains(FeedbackFilters.withoutRatings.name);

      // If we need client-side filtering to exclude archived items, fetch and count manually
      if (!hasArchived && (hasWithRatings || hasWithoutRatings)) {
        final feedbacks = await query.get();
        final filteredFeedbacks = feedbacks.docs.where((doc) {
          final data = doc.data();
          final isArchived = data['isArchived'];
          return isArchived != true; // This handles both false and null cases
        }).toList();
        return filteredFeedbacks.length;
      }
    }

    // For other cases, use the standard count query
    final feedbackCount = query.count().get().then((value) => value.count!);
    return feedbackCount;
  }

  Future<FeatureUsageInfo> fetchFeaturesCount(String uid) async {
    Query<Map<String, dynamic>> query = _firebaseFirestore.collection('todos');
    query = query.where('uid', isEqualTo: uid);
    final todosCount = await query.count().get().then((value) => value.count!);
    final todosUpdatedAt = todosCount == 0
        ? null
        : await query
            .orderBy('cloudUpdatedAt')
            .limitToLast(1)
            .get()
            .then((value) => value.docs.first['cloudUpdatedAt']?.toDate());

    query = _firebaseFirestore.collection('notes');
    query = query.where('uid', isEqualTo: uid);
    final notesCount = await query.count().get().then((value) => value.count!);
    final notesUpdatedAt = notesCount == 0
        ? null
        : await query
            .orderBy('cloudUpdatedAt')
            .limitToLast(1)
            .get()
            .then((value) => value.docs.first['cloudUpdatedAt']?.toDate());

    query = _firebaseFirestore.collection('habitSetups');
    query = query.where('uid', isEqualTo: uid);
    final habitSetupsCount =
        await query.count().get().then((value) => value.count!);
    final habitSetupsUpdatedAt = habitSetupsCount == 0
        ? null
        : await query
            .orderBy('cloudUpdatedAt')
            .limitToLast(1)
            .get()
            .then((value) => value.docs.first['cloudUpdatedAt']?.toDate());

    query = _firebaseFirestore.collection('journalSetups');
    query = query.where('uid', isEqualTo: uid);
    final journalSetupsCount =
        await query.count().get().then((value) => value.count!);
    final journalSetupsUpdatedAt = journalSetupsCount == 0
        ? null
        : await query
            .orderBy('cloudUpdatedAt')
            .limitToLast(1)
            .get()
            .then((value) => value.docs.first['cloudUpdatedAt']?.toDate());

    query = _firebaseFirestore.collection('lists');
    query = query.where('uid', isEqualTo: uid);
    final listsCount = await query.count().get().then((value) => value.count!);
    final listsUpdatedAt = listsCount == 0
        ? null
        : await query
            .orderBy('cloudUpdatedAt')
            .limitToLast(1)
            .get()
            .then((value) => value.docs.first['cloudUpdatedAt']?.toDate());

    FeatureUsageInfo featureUsageInfo = FeatureUsageInfo(
      todosCount: todosCount,
      todoUpdatedAt: todosUpdatedAt,
      notesCount: notesCount,
      noteUpdatedAt: notesUpdatedAt,
      habitsSetupsCount: habitSetupsCount,
      habitSetupsUpdatedAt: habitSetupsUpdatedAt,
      journalSetupsCount: journalSetupsCount,
      journalSetupsUpdatedAt: journalSetupsUpdatedAt,
      listsCount: listsCount,
      listUpdatedAt: listsUpdatedAt,
      featuresUpdatedAt: DateTime.now(),
    );

    await _firebaseFirestore.collection('usersMetadata').doc(uid).set(
      {'featureUsageInfo': featureUsageInfo.toMap()},
      SetOptions(merge: true),
    );

    return featureUsageInfo;
  }

  Future<List<Snippet>> fetchAllSnippets() async {
    Query<Map<String, dynamic>> snippetsQuery;
    snippetsQuery =
        _firebaseFirestore.collection('snippets').orderBy('position');

    final snippets = await snippetsQuery.get();
    if (snippets.docs.isEmpty) {
      return [];
    }
    final snippetsList =
        snippets.docs.map((doc) => Snippet.fromFirestore(doc.data())).toList();
    return snippetsList.isNotEmpty ? snippetsList : [];
  }

  Stream<List<Snippet>> listenSnippets({
    required SnippetStatus status,
    DateTime? lastDateTime,
    String? byFilter,
  }) {
    Query<Map<String, dynamic>> snippetsQuery;
    snippetsQuery = _firebaseFirestore
        .collection('snippets')
        .orderBy('position')
        .where('status', isEqualTo: status.name);
    if (lastDateTime != null) {
      snippetsQuery.startAfter([
        [Timestamp.fromDate(lastDateTime)],
      ]);
    }
    return snippetsQuery.snapshots().map(
          (doc) => doc.docs.map((msg) {
            return Snippet.fromFirestore(msg.data());
          }).toList(),
        );
  }

  Future<List<SnippetsMetadata>> fetchAllSnippetsMetadata() async {
    Query<Map<String, dynamic>> snippetsMetadataQuery;
    snippetsMetadataQuery = _firebaseFirestore.collection('snippetsMetadata');

    final snippetsMetadata = await snippetsMetadataQuery.get();
    if (snippetsMetadata.docs.isEmpty) {
      return [];
    }
    final snippetsList = snippetsMetadata.docs
        .map((doc) => SnippetsMetadata.fromFirestore(doc.data()))
        .toList();
    return snippetsList.isNotEmpty ? snippetsList : [];
  }

  Stream<List<SnippetsMetadata>> listenSnippetsMetadata({
    required SnippetMetadataType type,
    DateTime? lastDateTime,
    SnippetsMetadataFilter? byFilter,
  }) {
    Query<Map<String, dynamic>> snippetsMetadataQuery;
    snippetsMetadataQuery = _firebaseFirestore
        .collection('snippetsMetadata')
        .where('type', isEqualTo: type.name);
    if (byFilter != null) {
      if (byFilter.status != null) {
        snippetsMetadataQuery = snippetsMetadataQuery.where(
          'status',
          isEqualTo: byFilter.status!.name,
        );
      }
      if (byFilter.sid != null) {
        snippetsMetadataQuery = snippetsMetadataQuery.where(
          'updatedBy',
          isEqualTo: byFilter.sid,
        );
      }
    }
    if (lastDateTime != null) {
      snippetsMetadataQuery.startAfter([
        [Timestamp.fromDate(lastDateTime)],
      ]);
    }
    return snippetsMetadataQuery.snapshots().map(
          (doc) => doc.docs.map((msg) {
            return SnippetsMetadata.fromFirestore(msg.data());
          }).toList(),
        );
  }

  Future<void> addSegmentToFirestore({
    required String name,
    required String reason,
  }) async {
    final FirebaseFirestore firestore = FirebaseFirestore.instance;
    final FirebaseAuth auth = FirebaseAuth.instance;

    final currentUser = auth.currentUser;
    if (currentUser == null) {
      throw Exception('User not logged in');
    }

    final String id = const Uuid().v4();
    final Timestamp now = Timestamp.now();

    final segmentData = {
      'id': id,
      'name': name,
      'purpose': reason,
      'usersCount': 0,
      'cloudUpdatedAt': now,
      'updatedBy': currentUser.uid,
      'createdBy': currentUser.uid,
      'createdAt': now,
      'deletedAt': null,
    };

    await firestore.collection('userSegments').doc(id).set(segmentData);
  }

  /// Direct Firestore users with real-time updates
  Stream<List<UserMetaData>> listenUsers({
    Map<String, dynamic> filters = const {},
    int limit = 50,
  }) {
    // Handle email search by converting to UID first
    if (filters['searchEmailOrUid'] != null) {
      final searchTerm = (filters['searchEmailOrUid'] as String).trim();

      if (searchTerm.contains('@')) {
        // Email search - convert to UID first, then create stream
        return Stream.fromFuture(fetchUidFromEmail(searchTerm))
            .asyncExpand((uid) {
          if (uid == null) {
            // Email not found, return empty stream
            return Stream.value(<UserMetaData>[]);
          }
          // Replace searchEmailOrUid with the found UID for the actual query
          final updatedFilters = Map<String, dynamic>.from(filters);
          updatedFilters['searchEmailOrUid'] = uid;
          return _createUsersStream(updatedFilters, limit);
        }).handleError((e) {
          // Error fetching UID from email, return empty stream
          return Stream.value(<UserMetaData>[]);
        });
      }
    }

    return _createUsersStream(filters, limit);
  }

  Future<List<UserMetaData>> fetchUsersFromFirestore({
    Map<String, dynamic> filters = const {},
    int limit = 50,
    DateTime? startAfter,
  }) async {
    if (filters['searchEmailOrUid'] != null) {
      final searchTerm = (filters['searchEmailOrUid'] as String).trim();

      if (searchTerm.contains('@')) {
        try {
          final uid = await fetchUidFromEmail(searchTerm);
          if (uid == null) {
            return [];
          }
          filters = Map.from(filters);
          filters['searchEmailOrUid'] = uid;
        } catch (e) {
          return [];
        }
      }
    }

    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('usersMetadata')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion);

    // Apply all filters using the centralized filter method
    query = _applyFiltersToQuery(query, filters);

    query = query.orderBy('userInfo.createdAt', descending: true);

    if (startAfter != null) {
      query = query.startAfter([Timestamp.fromDate(startAfter)]);
    }

    query = query.limit(limit);

    final snapshot = await query.get();
    return snapshot.docs
        .map((doc) => UserMetaData.fromFirestore(doc.data()))
        .toList();
  }

  Future<int> fetchUsersCount({
    Map<String, dynamic> filters = const {},
    DateTime? startAfter,
  }) async {
    if (filters['searchEmailOrUid'] != null) {
      final searchTerm = (filters['searchEmailOrUid'] as String).trim();

      if (searchTerm.contains('@')) {
        try {
          final uid = await fetchUidFromEmail(searchTerm);
          if (uid == null) {
            return 0;
          }
          filters = Map.from(filters);
          filters['searchEmailOrUid'] = uid;
        } catch (e) {
          return 0;
        }
      }
    }

    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('usersMetadata')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion);

    // Apply all filters using the centralized filter method
    query = _applyFiltersToQuery(query, filters);

    query = query.orderBy('userInfo.createdAt', descending: true);

    if (startAfter != null) {
      query = query.startAfter([Timestamp.fromDate(startAfter)]);
    }

    final snapshot = await query.count().get();
    return snapshot.count ?? 0;
  }

  Stream<List<UserMetaData>> _createUsersStream(
    Map<String, dynamic> filters,
    int limit,
  ) {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('usersMetadata')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion);

    if (filters['subscriptionType'] != null) {
      final subscriptions =
          (filters['subscriptionType'] as List<dynamic>).cast<String>();
      query =
          query.where('subscriptionInfo.entitlement', whereIn: subscriptions);
    }

    if (filters['userDeletedStatus'] != null) {
      final statuses =
          (filters['userDeletedStatus'] as List<dynamic>).cast<String>();
      query = query.where('userDeletedStatus', whereIn: statuses);
    }

    if (filters['searchEmailOrUid'] != null) {
      final searchTerm = (filters['searchEmailOrUid'] as String).trim();
      query = query.where('uid', isEqualTo: searchTerm);
    }

    query = query.orderBy('userInfo.createdAt', descending: true).limit(limit);

    return query.snapshots().map((snapshot) {
      return snapshot.docs
          .map((doc) => UserMetaData.fromFirestore(doc.data()))
          .toList();
    });
  }

  Stream<int> _createUsersCountStream(Map<String, dynamic> filters) {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('usersMetadata')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion);

    if (filters['subscriptionType'] != null) {
      final subscriptions =
          (filters['subscriptionType'] as List<dynamic>).cast<String>();
      query =
          query.where('subscriptionInfo.entitlement', whereIn: subscriptions);
    }

    if (filters['userDeletedStatus'] != null) {
      final statuses =
          (filters['userDeletedStatus'] as List<dynamic>).cast<String>();
      query = query.where('userDeletedStatus', whereIn: statuses);
    }

    if (filters['searchEmailOrUid'] != null) {
      final searchTerm = (filters['searchEmailOrUid'] as String).trim();
      query = query.where('uid', isEqualTo: searchTerm);
    }

    return query.snapshots().map((snapshot) => snapshot.size);
  }

  /// Real-time count listener for users
  Stream<int> listenUsersCount({
    Map<String, dynamic> filters = const {},
  }) {
    // Handle email search by converting to UID first
    if (filters['searchEmailOrUid'] != null) {
      final searchTerm = (filters['searchEmailOrUid'] as String).trim();

      if (searchTerm.contains('@')) {
        // Email search - convert to UID first, then create stream
        return Stream.fromFuture(fetchUidFromEmail(searchTerm))
            .asyncExpand((uid) {
          if (uid == null) {
            // Email not found, return count of 0
            return Stream.value(0);
          }
          // Replace searchEmailOrUid with the found UID for the actual query
          final updatedFilters = Map<String, dynamic>.from(filters);
          updatedFilters['searchEmailOrUid'] = uid;
          return _createUsersCountStream(updatedFilters);
        }).handleError((e) {
          // Error fetching UID from email, return count of 0
          return Stream.value(0);
        });
      }
    }

    return _createUsersCountStream(filters);
  }

  Future<List<FeedbackModel>> fetchFeedbacks({
    DateTime? lastLocalDateTime,
    List<String>? byFilter,
    int limit = 10,
    DateTime? lastReportedDateTime,
  }) async {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('usersFeedback')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
        .limit(limit);

    // Order by localUpdatedAt only (consistent with other providers)
    query = query.orderBy('localUpdatedAt', descending: true);

    // Pagination cursor - use only localUpdatedAt
    if (lastLocalDateTime != null) {
      query = query.startAfter([Timestamp.fromDate(lastLocalDateTime)]);
    }

    // Apply filters based on the requirements table
    if (byFilter != null && byFilter.isNotEmpty) {
      final hasArchived = byFilter.contains(FeedbackFilters.archived.name);
      final hasWithRatings =
          byFilter.contains(FeedbackFilters.withRatings.name);
      final hasWithoutRatings =
          byFilter.contains(FeedbackFilters.withoutRatings.name);

      // Handle filters
      if (hasArchived && hasWithRatings && hasWithoutRatings) {
        // All 3 filters selected: show both archived and non-archived with all emotions
        query = query.where('emotion', isGreaterThanOrEqualTo: -1);
        // Don't filter by isArchived - show both archived and non-archived
      } else if (hasArchived) {
        // archived only: isArchived == true
        query = query.where('isArchived', isEqualTo: true);
        // No emotion filter when only archived is selected
      } else if (hasWithRatings || hasWithoutRatings) {
        // Only emotion filters - exclude archived items
        if (hasWithRatings && hasWithoutRatings) {
          // Both withRatings + withoutRatings: emotion >= -1
          query = query.where('emotion', isGreaterThanOrEqualTo: -1);
        } else if (hasWithRatings) {
          // withRatings only: emotion >= 0
          query = query.where('emotion', isGreaterThanOrEqualTo: 0);
        } else if (hasWithoutRatings) {
          // withoutRatings only: emotion == -1
          query = query.where('emotion', isEqualTo: -1);
        }
      }
      // If no recognized filters → show all (no additional filters)
    }

    final feedbacks = await query.get();
    if (feedbacks.docs.isEmpty) {
      return [];
    }

    List<FeedbackModel> feedbacksList = feedbacks.docs
        .map((doc) => FeedbackModel.fromFirestore(doc.data()))
        .toList();

    // Apply client-side filtering for isArchived when needed
    if (byFilter != null && byFilter.isNotEmpty) {
      final hasArchived = byFilter.contains(FeedbackFilters.archived.name);
      final hasWithRatings =
          byFilter.contains(FeedbackFilters.withRatings.name);
      final hasWithoutRatings =
          byFilter.contains(FeedbackFilters.withoutRatings.name);

      // Client-side filter to exclude archived items when only emotion filters are applied
      if (!hasArchived && (hasWithRatings || hasWithoutRatings)) {
        feedbacksList = feedbacksList.where((feedback) {
          // Include items where isArchived is false OR null (missing)
          final isArchived = feedback.isArchived;
          return isArchived != true; // This handles both false and null cases
        }).toList();
      }
    }

    return feedbacksList;
  }

// Update listenFeedbacks to use localUpdatedAt for ordering
  Stream<List<FeedbackModel>> listenFeedbacks({
    int count = 0,
    List<String>? byFilter,
  }) {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('usersFeedback')
        .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion);

    // Order by localUpdatedAt only
    query = query.orderBy('localUpdatedAt', descending: true);

    if (count <= 10) {
      query = query.limit(10);
    } else {
      if (count % 10 != 0) {
        count = count + (10 - count % 10);
      }
      query = query.limit(count);
    }

    // Apply filters based on the requirements table (same logic as fetchFeedbacks)
    if (byFilter != null && byFilter.isNotEmpty) {
      final hasArchived = byFilter.contains(FeedbackFilters.archived.name);
      final hasWithRatings =
          byFilter.contains(FeedbackFilters.withRatings.name);
      final hasWithoutRatings =
          byFilter.contains(FeedbackFilters.withoutRatings.name);

      // Handle filters
      if (hasArchived && hasWithRatings && hasWithoutRatings) {
        // All 3 filters selected: show both archived and non-archived with all emotions
        query = query.where('emotion', isGreaterThanOrEqualTo: -1);
        // Don't filter by isArchived - show both archived and non-archived
      } else if (hasArchived) {
        // archived only: isArchived == true
        query = query.where('isArchived', isEqualTo: true);
        // No emotion filter when only archived is selected
      } else if (hasWithRatings || hasWithoutRatings) {
        // Only emotion filters - exclude archived items
        if (hasWithRatings && hasWithoutRatings) {
          // Both withRatings + withoutRatings: emotion >= -1
          query = query.where('emotion', isGreaterThanOrEqualTo: -1);
        } else if (hasWithRatings) {
          // withRatings only: emotion >= 0
          query = query.where('emotion', isGreaterThanOrEqualTo: 0);
        } else if (hasWithoutRatings) {
          // withoutRatings only: emotion == -1
          query = query.where('emotion', isEqualTo: -1);
        }
      }
      // If no recognized filters → show all (no additional filters)
    }

    return query.snapshots().map((feedbacks) {
      List<FeedbackModel> feedbacksList = feedbacks.docs
          .map((doc) => FeedbackModel.fromFirestore(doc.data()))
          .toList();

      // Apply client-side filtering for isArchived when needed
      if (byFilter != null && byFilter.isNotEmpty) {
        final hasArchived = byFilter.contains(FeedbackFilters.archived.name);
        final hasWithRatings =
            byFilter.contains(FeedbackFilters.withRatings.name);
        final hasWithoutRatings =
            byFilter.contains(FeedbackFilters.withoutRatings.name);

        // Client-side filter to exclude archived items when only emotion filters are applied
        if (!hasArchived && (hasWithRatings || hasWithoutRatings)) {
          feedbacksList = feedbacksList.where((feedback) {
            // Include items where isArchived is false OR null (missing)
            final isArchived = feedback.isArchived;
            return isArchived != true; // This handles both false and null cases
          }).toList();
        }
      }

      return feedbacksList;
    });
  }

// Add this new method for listening to specific feedback IDs (if not already present)
  Stream<List<FeedbackModel>> listenFeedbackUpdatesByIDs(List<String> ids) {
    if (ids.isEmpty) {
      return Stream.value([]);
    }

    // Firestore has a limit of 10 items for whereIn queries
    if (ids.length <= 10) {
      return _firebaseFirestore
          .collection('usersFeedback')
          .where(_fieldVer, isEqualTo: DatabaseRepository.currentDbVersion)
          .where('id', whereIn: ids)
          .orderBy('localUpdatedAt', descending: true)
          .snapshots()
          .map(
            (feedbacks) => feedbacks.docs.map((feedback) {
              return FeedbackModel.fromFirestore(feedback.data());
            }).toList(),
          );
    } else {
      // Split IDs into chunks of 10 and combine the streams
      final chunks = <List<String>>[];
      for (int i = 0; i < ids.length; i += 10) {
        chunks.add(ids.sublist(i, (i + 10).clamp(0, ids.length)));
      }

      // Create streams for each chunk and combine them
      final streams = chunks
          .map(
            (chunk) => _firebaseFirestore
                .collection('usersFeedback')
                .where(
                  _fieldVer,
                  isEqualTo: DatabaseRepository.currentDbVersion,
                )
                .where('id', whereIn: chunk)
                .orderBy('localUpdatedAt', descending: true)
                .snapshots()
                .map(
                  (feedbacks) => feedbacks.docs.map((feedback) {
                    return FeedbackModel.fromFirestore(feedback.data());
                  }).toList(),
                ),
          )
          .toList();

      // Combine all streams
      if (streams.length == 1) {
        return streams.first;
      } else {
        // For multiple streams, combine them
        late StreamController<List<FeedbackModel>> controller;
        final Map<int, List<FeedbackModel>> streamData = {};
        final List<StreamSubscription> subscriptions = [];

        void updateCombinedResult() {
          final allFeedbacks = <FeedbackModel>[];
          final seenIds = <String>{};

          // Collect all feedbacks from all streams, avoiding duplicates
          for (final feedbacks in streamData.values) {
            for (final feedback in feedbacks) {
              if (!seenIds.contains(feedback.id)) {
                allFeedbacks.add(feedback);
                seenIds.add(feedback.id);
              }
            }
          }

          // Sort combined results by localUpdatedAt
          allFeedbacks.sort((a, b) {
            final aTime = a.localUpdatedAt;
            final bTime = b.localUpdatedAt;
            if (aTime == null && bTime == null) return 0;
            if (aTime == null) return 1;
            if (bTime == null) return -1;
            return bTime.compareTo(aTime); // Most recent first (descending)
          });

          controller.add(allFeedbacks);
        }

        controller = StreamController<List<FeedbackModel>>(
          onListen: () {
            // Subscribe to all streams
            for (int i = 0; i < streams.length; i++) {
              final subscription = streams[i].listen(
                (feedbacks) {
                  streamData[i] = feedbacks;
                  updateCombinedResult();
                },
                onError: (error) {
                  controller.addError(error);
                },
              );
              subscriptions.add(subscription);
            }
          },
          onCancel: () {
            // Cancel all subscriptions
            for (final subscription in subscriptions) {
              subscription.cancel();
            }
            // Close the controller
            controller.close();
          },
        );

        return controller.stream;
      }
    }
  }

  Future<List<DeleteReport>> fetchDeleteReports({
    DateTime? lastReportDateTime,
    DeletedReasonType? deletedReasonType,
  }) async {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('deletedUsersReports')
        // .where(
        //   _fieldVer,
        //   isEqualTo: DatabaseRepository.currentDbVersion,
        // ) // Added this line
        .orderBy('deletedAt', descending: true)
        .limit(10);

    if (lastReportDateTime != null) {
      query = query.startAfter([Timestamp.fromDate(lastReportDateTime)]);
    }
    if (deletedReasonType != null &&
        deletedReasonType != DeletedReasonType.none) {
      query = query.where('reason', isEqualTo: deletedReasonType.name);
    }

    final deleteReports = await query.get();
    if (deleteReports.docs.isEmpty) {
      return [];
    }
    final deleteReportsList = deleteReports.docs
        .map((doc) => DeleteReport.fromFirestore(doc.data()))
        .toList();
    return deleteReportsList.isNotEmpty ? deleteReportsList : [];
  }

  Stream<List<DeleteReport>> listenDeleteReports({
    int count = 0,
    DateTime? lastReportDateTime,
    DeletedReasonType? deletedReasonType,
  }) {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('deletedUsersReports')
        // .where(
        //   _fieldVer,
        //   isEqualTo: DatabaseRepository.currentDbVersion,
        // ) // Added this line
        .orderBy('deletedAt', descending: true);

    if (count <= 10) {
      query = query.limit(10);
    } else {
      query = query.limit(count);
    }
    if (deletedReasonType != null &&
        deletedReasonType != DeletedReasonType.none) {
      query = query.where('reason', isEqualTo: deletedReasonType.name);
    }

    return query.snapshots().map(
          (reports) => reports.docs.map((msg) {
            return DeleteReport.fromFirestore(msg.data());
          }).toList(),
        );
  }

  Future<int> fetchDeleteReportsCount({
    DeletedReasonType? deletedReasonType,
  }) async {
    Query<Map<String, dynamic>> query =
        _firebaseFirestore.collection('deletedUsersReports');
    // .where(
    //   _fieldVer,
    //   isEqualTo: DatabaseRepository.currentDbVersion,
    // ); // Added this line

    if (deletedReasonType != null &&
        deletedReasonType != DeletedReasonType.none) {
      query = query.where('reason', isEqualTo: deletedReasonType.name);
    }
    final deleteReports = query.count().get().then((value) => value.count!);

    return deleteReports;
  }

  Stream<List<DeleteReport>> listenUserDeleteReports({required String uid}) {
    return _firebaseFirestore
        .collection('deletedUsersReports')
        // .where(
        //   _fieldVer,
        //   isEqualTo: DatabaseRepository.currentDbVersion,
        // ) // Added this line
        .where('uid', isEqualTo: uid)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => DeleteReport.fromFirestore(doc.data()))
          .toList();
    });
  }

  Future<List<DeleteReport>> fetchUserDeleteReports(String uid) async {
    final deleteReports = await _firebaseFirestore
        .collection('deletedUsersReports')
        // .where(
        //   _fieldVer,
        //   isEqualTo: DatabaseRepository.currentDbVersion,
        // ) // Added this line
        .where('uid', isEqualTo: uid)
        .get();

    final deleteReportsList = deleteReports.docs
        .map((doc) => DeleteReport.fromFirestore(doc.data()))
        .toList();
    return deleteReportsList.isNotEmpty ? deleteReportsList : [];
  }

// Update listenDeletedReportUpdatesByIDs method
  Stream<List<DeleteReport>> listenDeleteReportUpdatesByIDs(
    List<String> ids,
  ) {
    if (ids.isEmpty) {
      return Stream.value([]);
    }

    // Firestore has a limit of 10 items for whereIn queries
    if (ids.length <= 10) {
      return _firebaseFirestore
          .collection('deletedUsersReports')
          // .where(
          //   _fieldVer,
          //   isEqualTo: DatabaseRepository.currentDbVersion,
          // ) // Added this line
          .where('id', whereIn: ids)
          .orderBy('deletedAt', descending: true)
          .snapshots()
          .map(
            (reports) => reports.docs.map((report) {
              return DeleteReport.fromFirestore(report.data());
            }).toList(),
          );
    } else {
      // Split IDs into chunks of 10 and combine the streams
      final chunks = <List<String>>[];
      for (int i = 0; i < ids.length; i += 10) {
        chunks.add(ids.sublist(i, (i + 10).clamp(0, ids.length)));
      }

      // Create streams for each chunk and combine them
      final streams = chunks
          .map(
            (chunk) => _firebaseFirestore
                .collection('deletedUsersReports')
                // .where(
                //   _fieldVer,
                //   isEqualTo: DatabaseRepository.currentDbVersion,
                // ) // Added this line
                .where('id', whereIn: chunk)
                .orderBy('deletedAt', descending: true)
                .snapshots()
                .map(
                  (reports) => reports.docs.map((report) {
                    return DeleteReport.fromFirestore(report.data());
                  }).toList(),
                ),
          )
          .toList();

      // Rest of the implementation remains the same...
      if (streams.length == 1) {
        return streams.first;
      } else {
        // For multiple streams, combine them
        late StreamController<List<DeleteReport>> controller;
        final Map<int, List<DeleteReport>> streamData = {};
        final List<StreamSubscription> subscriptions = [];

        void updateCombinedResult() {
          final allReports = <DeleteReport>[];
          final seenIds = <String>{};

          // Collect all reports from all streams, avoiding duplicates
          for (final reports in streamData.values) {
            for (final report in reports) {
              if (!seenIds.contains(report.id)) {
                allReports.add(report);
                seenIds.add(report.id);
              }
            }
          }

          // Sort combined results by deletedAt
          allReports.sort((a, b) {
            return b.deletedAt.compareTo(a.deletedAt); // Most recent first
          });

          controller.add(allReports);
        }

        controller = StreamController<List<DeleteReport>>(
          onListen: () {
            // Subscribe to all streams
            for (int i = 0; i < streams.length; i++) {
              final subscription = streams[i].listen(
                (reports) {
                  streamData[i] = reports;
                  updateCombinedResult();
                },
                onError: (error) {
                  controller.addError(error);
                },
              );
              subscriptions.add(subscription);
            }
          },
          onCancel: () {
            // Cancel all subscriptions
            for (final subscription in subscriptions) {
              subscription.cancel();
            }
            // Close the controller
            controller.close();
          },
        );

        return controller.stream;
      }
    }
  }

  // Add these methods to your FirebaseFirestoreRepository class:

  /// Fetch segments with pagination and search
  Future<List<Segment>> fetchSegments({
    String? searchQuery,
    DateTime? lastCreatedDateTime,
    int limit = 20,
  }) async {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('userSegments')
        .where('deletedAt', isEqualTo: null)
        .orderBy('createdAt', descending: true)
        .limit(limit);

    // Apply search filter if provided
    if (searchQuery != null && searchQuery.isNotEmpty) {
      // Note: Firestore doesn't support full-text search, so we'll do client-side filtering
      // For better performance, consider using a search service like Algolia
      // For now, we'll fetch all and filter client-side
      query = _firebaseFirestore
          .collection('userSegments')
          .where('deletedAt', isEqualTo: null)
          .orderBy('createdAt', descending: true);
    }

    // Apply pagination cursor
    if (lastCreatedDateTime != null) {
      query = query.startAfter([Timestamp.fromDate(lastCreatedDateTime)]);
    }

    final segments = await query.get();
    if (segments.docs.isEmpty) {
      return [];
    }

    var segmentsList =
        segments.docs.map((doc) => Segment.fromFirestore(doc)).toList();

    // Client-side search filtering
    if (searchQuery != null && searchQuery.isNotEmpty) {
      segmentsList = segmentsList
          .where(
            (segment) =>
                segment.name.toLowerCase().contains(searchQuery.toLowerCase()),
          )
          .take(limit)
          .toList();
    }

    return segmentsList;
  }

  /// Fetch segments count
  Future<int> fetchSegmentsCount({
    String? searchQuery,
  }) async {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('userSegments')
        .where('deletedAt', isEqualTo: null);

    if (searchQuery != null && searchQuery.isNotEmpty) {
      // For search, we need to fetch all and count client-side
      final segments = await query.get();
      final filtered = segments.docs
          .map((doc) => Segment.fromFirestore(doc))
          .where(
            (segment) =>
                segment.name.toLowerCase().contains(searchQuery.toLowerCase()),
          )
          .toList();
      return filtered.length;
    } else {
      // Use Firestore count aggregation for better performance
      final countQuery = query.count();
      final snapshot = await countQuery.get();
      return snapshot.count ?? 0;
    }
  }

  /// Listen to segments for staleness detection
  Stream<List<Segment>> listenSegments({
    String? searchQuery,
    int count = 20,
  }) {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('userSegments')
        .where('deletedAt', isEqualTo: null)
        .orderBy('createdAt', descending: true);

    if (searchQuery == null || searchQuery.isEmpty) {
      query = query.limit(count);
    }

    return query.snapshots().map((snapshot) {
      var segments =
          snapshot.docs.map((doc) => Segment.fromFirestore(doc)).toList();

      // Client-side search filtering
      if (searchQuery != null && searchQuery.isNotEmpty) {
        segments = segments
            .where(
              (segment) => segment.name
                  .toLowerCase()
                  .contains(searchQuery.toLowerCase()),
            )
            .take(count)
            .toList();
      }

      return segments;
    });
  }

  /// Listen to specific segment IDs for real-time updates
  Stream<List<Segment>> listenSegmentUpdatesByIDs(List<String> ids) {
    if (ids.isEmpty) {
      return Stream.value([]);
    }

    // Firestore has a limit of 10 items for whereIn queries
    if (ids.length <= 10) {
      return _firebaseFirestore
          .collection('userSegments')
          .where('id', whereIn: ids)
          .orderBy('createdAt', descending: true)
          .snapshots()
          .map(
            (segments) => segments.docs.map((segment) {
              return Segment.fromFirestore(segment);
            }).toList(),
          );
    } else {
      // Split IDs into chunks of 10 and combine the streams
      final chunks = <List<String>>[];
      for (int i = 0; i < ids.length; i += 10) {
        chunks.add(ids.sublist(i, (i + 10).clamp(0, ids.length)));
      }

      // Create streams for each chunk and combine them
      final streams = chunks
          .map(
            (chunk) => _firebaseFirestore
                .collection('userSegments')
                .where('id', whereIn: chunk)
                .orderBy('createdAt', descending: true)
                .snapshots()
                .map(
                  (segments) => segments.docs.map((segment) {
                    return Segment.fromFirestore(segment);
                  }).toList(),
                ),
          )
          .toList();

      // Combine all streams
      if (streams.length == 1) {
        return streams.first;
      } else {
        // For multiple streams, combine them
        late StreamController<List<Segment>> controller;
        final Map<int, List<Segment>> streamData = {};
        final List<StreamSubscription> subscriptions = [];

        void updateCombinedResult() {
          final allSegments = <Segment>[];
          final seenIds = <String>{};

          // Collect all segments from all streams, avoiding duplicates
          for (final segments in streamData.values) {
            for (final segment in segments) {
              if (!seenIds.contains(segment.id)) {
                allSegments.add(segment);
                seenIds.add(segment.id);
              }
            }
          }

          // Sort combined results by createdAt
          allSegments.sort((a, b) {
            return b.createdAt.compareTo(a.createdAt); // Most recent first
          });

          controller.add(allSegments);
        }

        controller = StreamController<List<Segment>>(
          onListen: () {
            // Subscribe to all streams
            for (int i = 0; i < streams.length; i++) {
              final subscription = streams[i].listen(
                (segments) {
                  streamData[i] = segments;
                  updateCombinedResult();
                },
                onError: (error) {
                  controller.addError(error);
                },
              );
              subscriptions.add(subscription);
            }
          },
          onCancel: () {
            // Cancel all subscriptions
            for (final subscription in subscriptions) {
              subscription.cancel();
            }
            // Close the controller
            controller.close();
          },
        );

        return controller.stream;
      }
    }
  }

  /// Listen to a specific segment
  Stream<Segment?> listenSegment({required String id}) {
    return _firebaseFirestore
        .collection('userSegments')
        .doc(id)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        return Segment.fromFirestore(doc);
      }
      return null;
    });
  }

  /// Listen to segment activities
  Stream<List<SegmentActivity>> listenSegmentActivities({
    required String segmentId,
  }) {
    return _firebaseFirestore
        .collection('userSegmentActivities')
        .where('userSegmentId', isEqualTo: segmentId)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs
          .map((doc) => SegmentActivity.fromFirestore(doc))
          .toList();
    });
  }

  /// Refresh segment user count
  Future<int> refreshSegmentUserCount({
    required String segmentId,
    required String segmentName,
  }) async {
    try {
      // Count users who have this segment
      final usersSnapshot = await _firebaseFirestore
          .collection('usersMetadata')
          .where('userSegments', arrayContains: segmentName)
          .get();

      final userCount = usersSnapshot.docs.length;

      // Update segment with new count
      await _firebaseFirestore
          .collection('userSegments')
          .doc(segmentId)
          .update({
        'usersCount': userCount,
        'cloudUpdatedAt': FieldValue.serverTimestamp(),
      });

      return userCount;
    } catch (e) {
      Log.e('❌ Firestore: Error refreshing segment count: $e');
      rethrow;
    }
  }

  /// Create a new segment
  Future<void> createSegment({
    required String id,
    required String name,
    required String purpose,
    required String createdBy,
    int initialUserCount = 0,
  }) async {
    final now = Timestamp.now();

    await _firebaseFirestore.collection('userSegments').doc(id).set({
      'id': id,
      'name': name,
      'purpose': purpose,
      'usersCount': initialUserCount,
      'cloudUpdatedAt': now,
      'createdAt': now,
      'createdBy': createdBy,
      'updatedBy': createdBy,
      'deletedAt': null,
    });
  }

  /// Create segment activity
  Future<void> createSegmentActivity({
    required String id,
    required String userSegmentId,
    required String activityTitle,
    required String csvPath,
    required int csvUserCount,
    required int updatedUserCount,
    required String createdBy,
    required String activityType,
  }) async {
    final now = Timestamp.now();

    await _firebaseFirestore.collection('userSegmentActivities').doc(id).set({
      'id': id,
      'userSegmentId': userSegmentId,
      'activityTitle': activityTitle,
      'csvPath': csvPath,
      'csvUserCount': csvUserCount,
      'updatedUserCount': updatedUserCount,
      'createdAt': now,
      'cloudUpdatedAt': now,
      'createdBy': createdBy,
      'activityType': activityType,
    });
  }

  /// Update segment
  Future<void> updateSegment({
    required String id,
    required String name,
    required String purpose,
    required String updatedBy,
  }) async {
    await _firebaseFirestore.collection('userSegments').doc(id).update({
      'name': name,
      'purpose': purpose,
      'cloudUpdatedAt': FieldValue.serverTimestamp(),
      'updatedBy': updatedBy,
    });
  }

  /// Soft delete segment
  Future<void> deleteSegment({
    required String id,
    required String updatedBy,
  }) async {
    await _firebaseFirestore.collection('userSegments').doc(id).update({
      'deletedAt': FieldValue.serverTimestamp(),
      'cloudUpdatedAt': FieldValue.serverTimestamp(),
      'updatedBy': updatedBy,
    });
  }

  /// Add users to segment
  Future<int> addUsersToSegment({
    required String segmentName,
    required List<String> userIds,
  }) async {
    final batch = _firebaseFirestore.batch();
    final usersCollection = _firebaseFirestore.collection('usersMetadata');
    int addedCount = 0;

    for (final uid in userIds) {
      final userRef = usersCollection.doc(uid);
      final doc = await userRef.get();

      if (doc.exists) {
        final data = doc.data()!;
        final existingSegments =
            (data['userSegments'] as List?)?.cast<String>() ?? [];

        if (!existingSegments.contains(segmentName)) {
          batch.update(userRef, {
            'userSegments': FieldValue.arrayUnion([segmentName]),
          });
          addedCount++;
        }
      }
    }

    await batch.commit();
    return addedCount;
  }

  /// Remove users from segment
  Future<int> removeUsersFromSegment({
    required String segmentName,
    required List<String> userIds,
  }) async {
    final batch = _firebaseFirestore.batch();
    final usersCollection = _firebaseFirestore.collection('usersMetadata');
    int removedCount = 0;

    for (final uid in userIds) {
      final userRef = usersCollection.doc(uid);
      final doc = await userRef.get();

      if (doc.exists) {
        final data = doc.data()!;
        final segments = (data['userSegments'] as List?)?.cast<String>() ?? [];

        if (segments.contains(segmentName)) {
          batch.update(userRef, {
            'userSegments': FieldValue.arrayRemove([segmentName]),
          });
          removedCount++;
        }
      }
    }

    await batch.commit();
    return removedCount;
  }

  /// Update segment name in all user documents
  Future<void> updateSegmentNameInUsers({
    required String oldName,
    required String newName,
  }) async {
    // Get all users with the old segment name
    final usersQuery = await _firebaseFirestore
        .collection('usersMetadata')
        .where('userSegments', arrayContains: oldName)
        .get();

    final batch = _firebaseFirestore.batch();

    // Update each user
    for (var doc in usersQuery.docs) {
      final segments = List<String>.from(doc['userSegments']);
      final updatedSegments =
          segments.map((s) => s == oldName ? newName : s).toList();

      batch.update(doc.reference, {
        'userSegments': updatedSegments,
      });
    }

    await batch.commit();
  }

  /// Remove segment from all user documents
  Future<void> removeSegmentFromAllUsers({
    required String segmentName,
  }) async {
    // Get all users with this segment
    final usersQuery = await _firebaseFirestore
        .collection('usersMetadata')
        .where('userSegments', arrayContains: segmentName)
        .get();

    final batch = _firebaseFirestore.batch();

    // Remove segment from each user
    for (var doc in usersQuery.docs) {
      batch.update(doc.reference, {
        'userSegments': FieldValue.arrayRemove([segmentName]),
      });
    }

    await batch.commit();
  }

  Future<List<SupportUser>> fetchSupportUsers({
    String? searchQuery,
    DateTime? lastLoginDateTime,
    int limit = 10,
  }) async {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('supportUsers')
        .orderBy('lastLogin', descending: true)
        .limit(limit);

    if (lastLoginDateTime != null) {
      query = query.startAfter([Timestamp.fromDate(lastLoginDateTime)]);
    }

    final supportUsers = await query.get();
    if (supportUsers.docs.isEmpty) {
      return [];
    }

    var supportUsersList = supportUsers.docs
        .map((doc) => SupportUser.fromFirestore(doc.data()))
        .toList();

    // Apply search filter if provided
    if (searchQuery != null && searchQuery.isNotEmpty) {
      final query = searchQuery.toLowerCase();
      supportUsersList = supportUsersList.where((user) {
        return user.sname.toLowerCase().contains(query) ||
            user.email.toLowerCase().contains(query);
      }).toList();
    }

    return supportUsersList;
  }

  Future<int> fetchSupportUsersCount({
    String? searchQuery,
  }) async {
    Query<Map<String, dynamic>> query =
        _firebaseFirestore.collection('supportUsers');

    if (searchQuery != null && searchQuery.isNotEmpty) {
      // For search, we need to fetch all and count client-side
      final supportUsers = await query.get();
      final filtered = supportUsers.docs
          .map((doc) => SupportUser.fromFirestore(doc.data()))
          .where((user) {
        final query = searchQuery.toLowerCase();
        return user.sname.toLowerCase().contains(query) ||
            user.email.toLowerCase().contains(query);
      }).toList();
      return filtered.length;
    } else {
      // Use Firestore count aggregation for better performance
      final countQuery = query.count();
      final snapshot = await countQuery.get();
      return snapshot.count ?? 0;
    }
  }

  Stream<List<SupportUser>> listenSupportUsers({
    String? searchQuery,
    int count = 10,
  }) {
    Query<Map<String, dynamic>> query = _firebaseFirestore
        .collection('supportUsers')
        .orderBy('lastLogin', descending: true);

    if (searchQuery == null || searchQuery.isEmpty) {
      query = query.limit(count);
    }

    return query.snapshots().map((snapshot) {
      var supportUsers = snapshot.docs
          .map((doc) => SupportUser.fromFirestore(doc.data()))
          .toList();

      // Client-side search filtering
      if (searchQuery != null && searchQuery.isNotEmpty) {
        final query = searchQuery.toLowerCase();
        supportUsers = supportUsers
            .where((user) {
              return user.sname.toLowerCase().contains(query) ||
                  user.email.toLowerCase().contains(query);
            })
            .take(count)
            .toList();
      }

      return supportUsers;
    });
  }

  Stream<List<SupportUser>> listenSupportUserUpdatesByIDs(List<String> ids) {
    if (ids.isEmpty) {
      return Stream.value([]);
    }

    // Firestore has a limit of 10 items for whereIn queries
    if (ids.length <= 10) {
      return _firebaseFirestore
          .collection('supportUsers')
          .where('sid', whereIn: ids)
          .orderBy('lastLogin', descending: true)
          .snapshots()
          .map(
            (supportUsers) => supportUsers.docs.map((supportUser) {
              return SupportUser.fromFirestore(supportUser.data());
            }).toList(),
          );
    } else {
      // Split IDs into chunks of 10 and combine the streams
      final chunks = <List<String>>[];
      for (int i = 0; i < ids.length; i += 10) {
        chunks.add(ids.sublist(i, (i + 10).clamp(0, ids.length)));
      }

      // Create streams for each chunk and combine them
      final streams = chunks
          .map(
            (chunk) => _firebaseFirestore
                .collection('supportUsers')
                .where('sid', whereIn: chunk)
                .orderBy('lastLogin', descending: true)
                .snapshots()
                .map(
                  (supportUsers) => supportUsers.docs.map((supportUser) {
                    return SupportUser.fromFirestore(supportUser.data());
                  }).toList(),
                ),
          )
          .toList();

      // Combine all streams
      if (streams.length == 1) {
        return streams.first;
      } else {
        // For multiple streams, combine them
        late StreamController<List<SupportUser>> controller;
        final Map<int, List<SupportUser>> streamData = {};
        final List<StreamSubscription> subscriptions = [];

        void updateCombinedResult() {
          final allSupportUsers = <SupportUser>[];
          final seenIds = <String>{};

          // Collect all support users from all streams, avoiding duplicates
          for (final supportUsers in streamData.values) {
            for (final supportUser in supportUsers) {
              if (!seenIds.contains(supportUser.sid)) {
                allSupportUsers.add(supportUser);
                seenIds.add(supportUser.sid);
              }
            }
          }

          // Sort combined results by lastLogin
          allSupportUsers.sort((a, b) {
            return b.lastLogin.compareTo(a.lastLogin); // Most recent first
          });

          controller.add(allSupportUsers);
        }

        controller = StreamController<List<SupportUser>>(
          onListen: () {
            // Subscribe to all streams
            for (int i = 0; i < streams.length; i++) {
              final subscription = streams[i].listen(
                (supportUsers) {
                  streamData[i] = supportUsers;
                  updateCombinedResult();
                },
                onError: (error) {
                  controller.addError(error);
                },
              );
              subscriptions.add(subscription);
            }
          },
          onCancel: () {
            // Cancel all subscriptions
            for (final subscription in subscriptions) {
              subscription.cancel();
            }
            // Close the controller
            controller.close();
          },
        );

        return controller.stream;
      }
    }
  }

  /// Apply all filter types to a Firestore query
  /// Note: Each filter combination requires a composite index in Firebase Console
  Query<Map<String, dynamic>> _applyFiltersToQuery(
    Query<Map<String, dynamic>> query,
    Map<String, dynamic> filters,
  ) {
    // Priority order: Apply most selective filters first to optimize performance
    // and reduce the number of required composite indexes

    // Subscription Type filter (multi-select)
    if (filters['subscriptionType'] != null) {
      final subscriptions =
          (filters['subscriptionType'] as List<dynamic>).cast<String>();
      query =
          query.where('subscriptionInfo.entitlement', whereIn: subscriptions);
    }

    // Subscription State filter (multi-select)
    if (filters['subscriptionState'] != null) {
      final states =
          (filters['subscriptionState'] as List<dynamic>).cast<String>();
      query =
          query.where('subscriptionInfo.subscriptionState', whereIn: states);
    }

    // Super Subscription filter (multi-select)
    if (filters['superSubscription'] != null) {
      final superSubs =
          (filters['superSubscription'] as List<dynamic>).cast<String?>();

      // Handle mixed null and non-null values
      final hasNull = superSubs.contains(null);
      final nonNullValues =
          superSubs.where((s) => s != null).cast<String>().toList();

      if (hasNull && nonNullValues.isNotEmpty) {
        // Need to use Filter.or to combine null check with whereIn
        query = query.where(
          Filter.or(
            Filter('superSubscription', isNull: true),
            Filter('superSubscription', whereIn: nonNullValues),
          ),
        );
      } else if (hasNull) {
        // Only null values selected
        query = query.where('superSubscription', isNull: true);
      } else {
        // Only non-null values selected
        query = query.where('superSubscription', whereIn: nonNullValues);
      }
    }

    // User Deleted Status filter (multi-select) - using 'deleted' key from enum
    if (filters['deleted'] != null) {
      final statuses = (filters['deleted'] as List<dynamic>).cast<String>();
      query = query.where('userDeletedStatus', whereIn: statuses);
    }

    // Subscription Expiration Date filter (single-select)
    if (filters['subscriptionExpDate'] != null) {
      final expDate = filters['subscriptionExpDate'] as String?;
      if (expDate != null) {
        final expDateTime = DateTime.parse(expDate);
        query = query
            .where(
              'subscriptionInfo.subscriptionExpDate',
              isGreaterThan: Timestamp.fromDate(DateTime.now()),
            )
            .where(
              'subscriptionInfo.subscriptionExpDate',
              isLessThanOrEqualTo: Timestamp.fromDate(expDateTime),
            );
      }
    }

    // Created At filter (single-select)
    if (filters['createdAt'] != null) {
      final createdDate = filters['createdAt'] as String?;
      if (createdDate != null) {
        final createdDateTime = DateTime.parse(createdDate);
        query = query.where(
          'userInfo.createdAt',
          isGreaterThanOrEqualTo: Timestamp.fromDate(createdDateTime),
        );
      }
    }

    // Chat Status filter (multi-select)
    if (filters['chatStatus'] != null) {
      final statuses = (filters['chatStatus'] as List<dynamic>).cast<String>();
      query = query.where('chatStatus', whereIn: statuses);
    }

    // Report Status filter (multi-select)
    if (filters['reportStatus'] != null) {
      final statuses =
          (filters['reportStatus'] as List<dynamic>).cast<String>();
      query = query.where('reportStatus', whereIn: statuses);
    }

    // App Theme filter (single-select)
    if (filters['appTheme'] != null) {
      final theme = filters['appTheme'] as String?;
      if (theme != null) {
        query = query.where('appSettings.appTheme', isEqualTo: theme);
      } else {
        query = query.where('appSettings.appTheme', isNull: true);
      }
    }

    // Passcode Enabled filter (single-select)
    if (filters['isPasscodeEnabled'] != null) {
      final isEnabled = filters['isPasscodeEnabled'] as bool?;
      if (isEnabled != null) {
        query = query.where(
          'securitySettings.isPasscodeEnabled',
          isEqualTo: isEnabled,
        );
      } else {
        query = query.where('securitySettings.isPasscodeEnabled', isNull: true);
      }
    }

    // Biometric Enabled filter (single-select)
    if (filters['isBiometricEnabled'] != null) {
      final isEnabled = filters['isBiometricEnabled'] as bool?;
      if (isEnabled != null) {
        query = query.where(
          'securitySettings.isBiometricEnabled',
          isEqualTo: isEnabled,
        );
      } else {
        query =
            query.where('securitySettings.isBiometricEnabled', isNull: true);
      }
    }

    // Daily Agenda Email filter (single-select)
    if (filters['isDailyAgendaEmail'] != null) {
      final isEnabled = filters['isDailyAgendaEmail'] as bool?;
      if (isEnabled != null) {
        query = query.where(
          'notificationSettings.isDailyAgendaEmailNotificationEnabled',
          isEqualTo: isEnabled,
        );
      } else {
        query = query.where(
          'notificationSettings.isDailyAgendaEmailNotificationEnabled',
          isNull: true,
        );
      }
    }

    // Daily Agenda Mobile filter (single-select)
    if (filters['isDailyAgendaMobile'] != null) {
      final isEnabled = filters['isDailyAgendaMobile'] as bool?;
      if (isEnabled != null) {
        query = query.where(
          'notificationSettings.isDailyAgendaMobileNotificationEnabled',
          isEqualTo: isEnabled,
        );
      } else {
        query = query.where(
          'notificationSettings.isDailyAgendaMobileNotificationEnabled',
          isNull: true,
        );
      }
    }

    // Search by Email or UID filter (single-select)
    if (filters['searchEmailOrUid'] != null) {
      final searchTerm = (filters['searchEmailOrUid'] as String).trim();
      query = query.where('uid', isEqualTo: searchTerm);
    }

    return query;
  }
}
