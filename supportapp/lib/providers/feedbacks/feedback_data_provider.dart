import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/providers/feedbacks/feedback_data_state.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

/// **FeedbackDataProvider - Advanced Real-time Feedback List Manager**
///
/// This provider implements a sophisticated feedback list management system with:
/// - Real-time updates for displayed items only (performance optimized)
/// - Intelligent staleness detection to notify when list needs refresh
/// - Cursor-based pagination with smart totalCount adjustment
/// - Deleted user handling with status indicators
/// - Flexible filtering system for feedback attributes
///
/// ## 🏗️ ARCHITECTURE OVERVIEW
///
/// ### Data Flow Patterns
///
/// **1. Initial Load Flow**
/// ```
/// initialize() → _setupListeners() → _loadInitialData() → _setupDisplayedFeedbacksListener()
///                                 ↓
///                          Sets up staleness detection after initial load
/// ```
/// - Fetches first page of feedbacks + total count
/// - Sets up targeted real-time listeners
/// - Initializes staleness detection
///
/// **2. Real-time Update Flow**
/// ```
/// Firestore Change → listenFeedbackUpdatesByIDs() → Update in-place → Re-sort → Emit
///                                                ↓
///                                         Preserve totalCount
/// ```
/// - Only listens to currently displayed feedback IDs (efficient)
/// - Updates existing items without changing list structure
/// - Maintains sort order and pagination state
///
/// **3. Pagination Flow**
/// ```
/// loadMore() → Fetch from cursor → Filter duplicates → Append → Update hasMore
///            ↓
///         Smart totalCount adjustment if no more items available
/// ```
/// - Uses last displayed item's localUpdatedAt as cursor
/// - Handles database inconsistencies gracefully
///
/// **4. Staleness Detection Flow**
/// ```
/// Background Listener → Compare ID sets → Set hasNewUpdates flag
///                    ↓
///               Only triggers on actual content differences
/// ```
/// - Runs parallel staleness check with same count as displayed
/// - Ignores order changes, only detects content differences
///
/// ## 📊 STATE MANAGEMENT
///
/// ### Core State Properties
/// - **`feedbacks`**: Currently displayed feedbacks list (paginated)
/// - **`totalCount`**: Stable count from initial fetch (only updated on refresh)
/// - **`hasMore`**: Can load more items (displayed < total)
/// - **`hasNewUpdates`**: Content staleness flag
/// - **`filters`**: Active filters applied
///
/// ### Real-time Listeners
/// - **Displayed Items**: `_displayedFeedbacksListener` (ID-based)
/// - **Staleness Detection**: `_stalenessDetectionListener` (background)
///
/// ## 🎯 PUBLIC API
///
/// **Core Operations**
/// - `initialize(filters)` - Load initial data with filters
/// - `loadMore()` - Load next batch of feedbacks
/// - `refresh()` - Reload fresh data, clear staleness
/// - `changeFilters(filters)` - Change filters and reload
///
/// ## ⚡ PERFORMANCE OPTIMIZATIONS
///
/// - **Targeted Updates**: Only listen to displayed IDs
/// - **Cursor Pagination**: Efficient database queries
/// - **Smart Deduplication**: Prevents duplicate items
/// - **Stable Counts**: totalCount preserved during updates
/// - **Background Staleness**: Non-blocking freshness checks
class FeedbackDataProvider extends Cubit<FeedbackDataState> {
  FeedbackDataProvider({
    required DatabaseRepository databaseRepository,
  })  : _databaseRepository = databaseRepository,
        super(FeedbackDataState.initial());

  final DatabaseRepository _databaseRepository;

  // ============================================================================
  // PRIVATE STATE VARIABLES
  // ============================================================================

  // --- Real-time Stream Subscriptions ---

  /// Listener for displayed feedback items updates (efficient ID-based targeting)
  StreamSubscription? _displayedFeedbacksListener;

  /// Listener for staleness detection (background monitoring, non-blocking)
  StreamSubscription? _stalenessDetectionListener;

  // --- Pagination & Cursor State ---

  /// Pagination cursor using localUpdatedAt (consistent with Firestore ordering)
  DateTime? _lastLocalDateTime;

  /// Items per page for initial load (balanced performance vs UX)
  static const int _itemsPerPage = 10;

  /// Batch size for loadMore operations (optimized for smooth scrolling)
  static const int _loadMoreBatchSize = 10;

  /// Maximum attempts for shortfall handling (prevents infinite loops)
  static const int _maxShortfallAttempts = 5;

  // --- Utility State ---

  /// Counter for generating unique placeholder names for deleted users
  int _deletedUserIdCounter = 1;

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  @override
  Future<void> close() {
    _displayedFeedbacksListener?.cancel();
    _stalenessDetectionListener?.cancel();
    return super.close();
  }

  // ============================================================================
  // PUBLIC API METHODS
  // ============================================================================

  /// Initialize feedback list with filters and set up real-time listeners
  ///
  /// This is the main entry point that:
  /// - Cleans up existing listeners
  /// - Resets pagination state
  /// - Loads initial data batch
  /// - Sets up targeted real-time updates
  /// - Initializes staleness detection
  void initialize({List<String>? filters}) {
    Log.d(
      '🔄 FeedbackDataProvider: Initializing with filters: $filters',
    );
    Log.d('  🗑️ Cancelling old listeners...');
    _cancelListeners();
    Log.d('  🔄 Resetting pagination...');
    _resetPagination();

    Log.d('  🎨 Emitting loading state with new filters...');
    emit(
      FeedbackDataState.initial().copyWith(
        filters: filters,
      ),
    );

    Log.d('  👂 Setting up new listeners...');
    _setupListeners();
  }

  /// Refresh entire feedback list and clear staleness indicators
  ///
  /// This performs a full reset:
  /// - Clears hasNewUpdates flag
  /// - Re-initializes with current filters
  /// - Resets pagination to first page
  Future<void> refresh() async {
    Log.d('🔄 FeedbackDataProvider: Refreshing feedbacks data');

    // Clear new updates flag when refreshing
    emit(state.copyWith(hasNewUpdates: false));

    // Re-initialize with current filters
    initialize(filters: state.filters);
  }

  /// Change filters and reload data
  ///
  /// Efficiently handles filter changes by performing a clean re-initialization
  void changeFilters(List<String> filters) {
    Log.d('🎛️ FeedbackDataProvider: Changing filters');
    Log.d('  🔄 OLD filters: ${state.filters}');
    Log.d('  ✨ NEW filters: $filters');

    initialize(filters: filters);
  }

  /// Load more feedbacks with advanced duplicate handling and cursor advancement
  ///
  /// **Pagination Strategy:**
  /// - Uses cursor-based pagination with `_lastLocalDateTime`
  /// - Fetches batches of `_loadMoreBatchSize` items from database
  /// - Handles database inconsistencies and duplicate detection gracefully
  ///
  /// **Duplicate Handling Logic:**
  /// 1. **Initial Fetch**: Get next batch using current cursor position
  /// 2. **Process & Deduplicate**: Remove items already in displayed list
  /// 3. **Handle Shortfall**: If unique items < batch size AND we got full batch:
  ///    - Sort ALL processed items (including duplicates) by localUpdatedAt
  ///    - Advance cursor to last item's localUpdatedAt (ensures forward progress)
  ///    - Fetch additional batch and repeat until sufficient unique items
  /// 4. **End Detection**: Stop when partial batch received (< batch size)
  ///
  /// **Cursor Advancement Strategy:**
  /// - Critical insight: Sort ALL items (duplicates + unique) before advancing cursor
  /// - This ensures cursor always moves forward, even with 100% duplicate batches
  /// - Prevents infinite loops while maintaining database consistency
  ///
  /// **State Updates:**
  /// - Merge unique items with existing displayed list
  /// - Update `hasMore` based on partial batch detection
  /// - Adjust `totalCount` if database end reached
  /// - Refresh staleness detection to match new pagination state
  Future<void> loadMore() async {
    if (state.isLoadingMore || !state.hasMore) return;

    Log.d('📄 LoadMore: Loading next $_loadMoreBatchSize feedbacks...');
    emit(state.copyWith(isLoadingMore: true));

    try {
      // Initial fetch
      var newFeedbacks = await _databaseRepository.fetchFeedbacks(
        byFilter: state.filters.isEmpty ? null : state.filters,
        lastLocalDateTime: _lastLocalDateTime,
        limit: _loadMoreBatchSize,
      );

      if (newFeedbacks.isEmpty) {
        // Handle empty case
        emit(
          state.copyWith(
            isLoadingMore: false,
            hasMore: false,
            totalCount: state.feedbacks.length,
          ),
        );
        return;
      }

      // Deduplicate
      var processedFeedbacks = _processDeletedUsers(newFeedbacks);
      final existingIds = state.feedbacks.map((f) => f.id).toSet();
      var newUniqueFeedbacks =
          processedFeedbacks.where((f) => !existingIds.contains(f.id)).toList();

      // Handle duplicate shortfall with cursor advancement (max attempts for safety)
      int shortfallAttempts = 0;

      while (newUniqueFeedbacks.length < _loadMoreBatchSize &&
          newFeedbacks.length == _loadMoreBatchSize &&
          shortfallAttempts < _maxShortfallAttempts) {
        shortfallAttempts++;
        Log.d(
          '🔄 Shortfall attempt $shortfallAttempts/$_maxShortfallAttempts: ${newUniqueFeedbacks.length} < $_loadMoreBatchSize, fetching more...',
        );

        // Sort ALL processed items (including duplicates) to advance cursor
        _sortFeedbacksByTimestamp(processedFeedbacks);

        // Update cursor from sorted batch (advances even with duplicates)
        if (processedFeedbacks.isNotEmpty) {
          _lastLocalDateTime = processedFeedbacks.last.localUpdatedAt;
          Log.d('🎯 Cursor advanced to: $_lastLocalDateTime');
        }

        // Fetch next batch
        final additionalBatch = await _databaseRepository.fetchFeedbacks(
          byFilter: state.filters.isEmpty ? null : state.filters,
          lastLocalDateTime: _lastLocalDateTime,
          limit: _loadMoreBatchSize,
        );

        // Check for end conditions
        if (additionalBatch.isEmpty ||
            additionalBatch.length < _loadMoreBatchSize) {
          Log.d('🏁 Reached database end: ${additionalBatch.length} items');
          newFeedbacks = additionalBatch;
          break;
        }

        // Process additional batch
        final processedAdditional = _processDeletedUsers(additionalBatch);
        final allIds = [...existingIds, ...newUniqueFeedbacks.map((f) => f.id)];
        final uniqueFromAdditional =
            processedAdditional.where((f) => !allIds.contains(f.id)).toList();

        // Add to collections
        newUniqueFeedbacks.addAll(uniqueFromAdditional);
        processedFeedbacks = [...processedFeedbacks, ...processedAdditional];
        newFeedbacks = additionalBatch;

        Log.d(
          '📊 Additional: ${processedAdditional.length} processed, ${uniqueFromAdditional.length} unique',
        );
      }

      // Safety check: warn if we hit the attempt limit
      if (shortfallAttempts >= _maxShortfallAttempts) {
        Log.w(
            '⚠️ LoadMore: Reached max shortfall attempts ($_maxShortfallAttempts). '
            'Proceeding with ${newUniqueFeedbacks.length} unique items to prevent infinite loop.');
      }

      // Final merge and sort
      final allFeedbacks = [...state.feedbacks, ...newUniqueFeedbacks];
      _sortFeedbacksByTimestamp(allFeedbacks);

      // Update final cursor from displayed items
      if (allFeedbacks.isNotEmpty) {
        _lastLocalDateTime = allFeedbacks.last.localUpdatedAt;
      }

      // Determine hasMore
      final gotPartialBatch = newFeedbacks.length < _loadMoreBatchSize;
      final reachedExpectedTotal = allFeedbacks.length >= state.totalCount;
      final hasMoreData = !gotPartialBatch && !reachedExpectedTotal;
      final adjustedTotalCount =
          gotPartialBatch ? allFeedbacks.length : state.totalCount;

      Log.d(
        '✅ LoadMore complete: ${newUniqueFeedbacks.length} unique items added',
      );

      emit(
        state.copyWith(
          feedbacks: allFeedbacks,
          isLoadingMore: false,
          hasMore: hasMoreData,
          totalCount: adjustedTotalCount,
          lastUpdate: DateTime.now(),
        ),
      );

      // Update listeners
      _updateStalenessListenerCount();
      _displayedFeedbacksListener?.cancel();
      _setupDisplayedFeedbacksListener();
    } catch (error) {
      Log.e('❌ LoadMore error: $error');
      emit(
        state.copyWith(
          isLoadingMore: false,
          error: 'Failed to load more feedbacks: ${error.toString()}',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE SETUP & INITIALIZATION
  // ============================================================================

  /// Core setup method: loads initial data and configures real-time listeners
  ///
  /// Flow:
  /// 1. Load first page of feedbacks + total count
  /// 2. Set up targeted listener for displayed items
  /// 3. Start staleness detection in background
  Future<void> _setupListeners() async {
    await _loadInitialData();

    Log.d(
      '👂 FeedbackDataProvider: Setting up listeners for filters: ${state.filters}',
    );

    _setupDisplayedFeedbacksListener();
  }

  /// Cancel feedback-specific listeners
  ///
  /// Cleans up all active listeners when changing filters or closing
  void _cancelListeners() {
    _displayedFeedbacksListener?.cancel();
    _displayedFeedbacksListener = null;
    _stalenessDetectionListener?.cancel();
    _stalenessDetectionListener = null;
  }

  /// Reset pagination cursor and utility counters for clean state
  ///
  /// Called during initialization to ensure consistent starting state
  void _resetPagination() {
    _lastLocalDateTime = null;
    _deletedUserIdCounter = 1;
  }

  // ============================================================================
  // PRIVATE DATA LOADING - INITIAL & PAGINATION
  // ============================================================================

  /// Load initial page of feedbacks and total count (one-time fetch)
  ///
  /// This is a clean fetch operation that:
  /// - Gets first page of feedbacks matching current filters
  /// - Fetches accurate total count for pagination
  /// - Processes deleted users with placeholder names
  /// - Sets up pagination cursor for future loadMore calls
  ///
  /// Note: No real-time listeners set up here - that's done separately
  Future<void> _loadInitialData() async {
    Log.d(
      '📥 FeedbackDataProvider: Loading initial feedbacks data (no real-time updates)',
    );
    Log.d('  📋 Filters: ${state.filters}');

    try {
      // Parallel fetch for optimal performance: feedbacks + count
      final results = await Future.wait([
        _databaseRepository.fetchFeedbacks(
          byFilter: state.filters.isEmpty ? null : state.filters,
          limit: _itemsPerPage,
        ),
        _databaseRepository.fetchFeedbacksCount(
          byFilter: state.filters.isEmpty ? null : state.filters,
        ),
      ]);

      final feedbacks = results[0] as List<FeedbackModel>;
      final totalCount = results[1] as int;

      Log.d(
        '✅ FeedbackDataProvider: Loaded ${feedbacks.length} initial feedbacks, total count: $totalCount',
      );

      // Apply deleted user processing (placeholder names)
      final processedFeedbacks = _processDeletedUsers(feedbacks);

      // Sort by timestamp to match database ordering (newest first)
      _sortFeedbacksByTimestamp(processedFeedbacks);

      // Set pagination cursor to last item for future loadMore calls
      if (processedFeedbacks.isNotEmpty) {
        _lastLocalDateTime = processedFeedbacks.last.localUpdatedAt;
      }

      // Determine if more pages available by comparing counts
      final hasMoreData = processedFeedbacks.length < totalCount;

      emit(
        state.copyWith(
          feedbacks: processedFeedbacks,
          totalCount: totalCount,
          // Use real database count (only updated on initial load/refresh)
          isLoading: false,
          error: null,
          lastUpdate: DateTime.now(),
          hasMore: hasMoreData,
          // More pages available if we have fewer items than total
          hasNewUpdates:
              false, // Always false on initial load (no staleness yet)
        ),
      );

      // Set up staleness detection after initial data is loaded
      if (processedFeedbacks.isNotEmpty) {
        _setupStalenessDetectionListener();
      }
    } catch (error) {
      Log.e('❌ FeedbackDataProvider: Error loading initial feedbacks: $error');
      emit(
        state.copyWith(
          isLoading: false,
          error: 'Failed to load feedbacks: $error',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE UTILITY & HELPER METHODS
  // ============================================================================

  /// Process deleted users by replacing names with placeholders
  ///
  /// Handles users marked for deletion by giving them recognizable
  /// placeholder names with incrementing counter for uniqueness
  List<FeedbackModel> _processDeletedUsers(List<FeedbackModel> feedbacks) {
    final processedFeedbacks = <FeedbackModel>[];

    for (var feedback in feedbacks) {
      if (feedback.userDeletedStatus != null &&
          (feedback.userDeletedStatus == UserDeletedStatus.remove_a ||
              feedback.userDeletedStatus == UserDeletedStatus.remove_u)) {
        final newFeedback = feedback.copyWith(
          uname:
              'Deleted user ${feedback.uname?.isNotEmpty == true ? feedback.uname : _deletedUserIdCounter}',
        );
        _deletedUserIdCounter++;
        processedFeedbacks.add(newFeedback);
      } else {
        processedFeedbacks.add(feedback);
      }
    }

    return processedFeedbacks;
  }

  /// Sort feedbacks by localUpdatedAt timestamp (newest first)
  ///
  /// Maintains consistency with database ordering for predictable pagination
  void _sortFeedbacksByTimestamp(List<FeedbackModel> feedbacks) {
    feedbacks.sort((a, b) {
      final aTime = a.localUpdatedAt;
      final bTime = b.localUpdatedAt;
      if (aTime == null && bTime == null) return 0;
      if (aTime == null) return 1;
      if (bTime == null) return -1;
      return bTime.compareTo(aTime); // Most recent first (descending)
    });
  }

  /// Set up background staleness detection listener
  ///
  /// This runs a parallel query with the same count as displayed items
  /// to detect when new content becomes available. Only triggers when
  /// the actual content (IDs) differs, not just order changes.
  void _setupStalenessDetectionListener() {
    Log.d(
      '📡 Setting up real-time list listener for staleness detection - filters: ${state.filters}',
    );

    // Use current displayed count or default to initial page size
    final initialCount =
        state.feedbacks.isNotEmpty ? state.feedbacks.length : _itemsPerPage;

    _stalenessDetectionListener = _databaseRepository
        .listenFeedbacks(
      byFilter: state.filters.isEmpty ? null : state.filters,
      count: initialCount, // Start with initial count
    )
        .listen(
      (realtimeFeedbacks) {
        Log.d(
          '🔍 Real-time list: Received ${realtimeFeedbacks.length} feedbacks (expected: ${state.feedbacks.length})',
        );

        // Only run staleness check if we have items to compare against
        if (state.feedbacks.isNotEmpty) {
          // Apply same processing pipeline as displayed data for fair comparison
          final processedRealtimeFeedbacks =
              _processDeletedUsers(realtimeFeedbacks);
          _sortFeedbacksByTimestamp(processedRealtimeFeedbacks);

          // Compare content by ID sets (order differences don't matter)
          final currentIds = state.feedbacks.map((f) => f.id).toSet();
          final realtimeIds =
              processedRealtimeFeedbacks.map((f) => f.id).toSet();

          final hasChanges =
              !(const SetEquality().equals(currentIds, realtimeIds));

          Log.d(
            '🔍 ID Comparison - Current: ${currentIds.length} items, Realtime: ${realtimeIds.length} items, HasChanges: $hasChanges',
          );

          if (hasChanges) {
            Log.d('🚨 Staleness detected! Setting hasNewUpdates to true');
            emit(
              state.copyWith(
                hasNewUpdates: true,
                lastUpdate: DateTime.now(),
              ),
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ FeedbackDataProvider: Real-time list listener error: $error');
      },
    );
  }

  /// Update staleness listener to match current pagination state
  ///
  /// Called after loadMore to ensure staleness detection uses the
  /// same item count as currently displayed for accurate comparison
  void _updateStalenessListenerCount() {
    if (state.feedbacks.isNotEmpty) {
      Log.d(
        '🔄 Setting up real-time listener to match displayed items: ${state.feedbacks.length}',
      );

      // Restart listener with updated count to match displayed items
      _stalenessDetectionListener?.cancel();
      _setupStalenessDetectionListener();
    }
  }

  /// Set up targeted real-time listener for currently displayed feedbacks
  ///
  /// This is the core efficiency feature: instead of listening to ALL feedbacks,
  /// we only listen to the specific IDs currently displayed. This provides:
  /// - Real-time updates for visible items only
  /// - Minimal bandwidth and processing
  /// - In-place updates without list reconstruction
  void _setupDisplayedFeedbacksListener() {
    if (state.feedbacks.isEmpty) {
      Log.d('📡 No displayed items to listen for updates');
      return;
    }

    final displayedIds = state.feedbacks.map((f) => f.id).toList();
    Log.d(
      '📡 Setting up data listener for ${displayedIds.length} displayed IDs: ${displayedIds.take(3).join(", ")}...',
    );

    _displayedFeedbacksListener =
        _databaseRepository.listenFeedbackUpdatesByIDs(displayedIds).listen(
      (updatedFeedbacks) async {
        Log.d(
          '📡 Real-time data: Received ${updatedFeedbacks.length} updated feedbacks for displayed IDs',
        );

        if (updatedFeedbacks.isNotEmpty) {
          // Work on copy to avoid mutating state during processing
          final currentFeedbacks = List<FeedbackModel>.from(state.feedbacks);

          // Apply same deleted user processing as initial load
          final processedUpdatedFeedbacks =
              _processDeletedUsers(updatedFeedbacks);

          // Find and update matching items in displayed list
          bool hasChanges = false;
          for (final updatedFeedback in processedUpdatedFeedbacks) {
            final existingIndex =
                currentFeedbacks.indexWhere((f) => f.id == updatedFeedback.id);
            if (existingIndex != -1) {
              // Replace existing item with updated data
              currentFeedbacks[existingIndex] = updatedFeedback;
              hasChanges = true;
              Log.d('📡 Updated feedback item: ${updatedFeedback.id}');
            }
          }

          if (hasChanges) {
            // Re-sort to maintain timestamp ordering after updates
            _sortFeedbacksByTimestamp(currentFeedbacks);

            // Keep pagination cursor in sync with displayed items
            if (currentFeedbacks.isNotEmpty) {
              _lastLocalDateTime = currentFeedbacks.last.localUpdatedAt;
            }

            Log.d(
              '📡 Real-time data: Updated displayed items, keeping existing totalCount: ${state.totalCount}',
            );

            emit(
              state.copyWith(
                feedbacks: currentFeedbacks,
                // Preserve totalCount stability - only update on initial load/refresh
                lastUpdate: DateTime.now(),
                // Preserve hasMore state during real-time updates
              ),
            );
          } else {
            Log.d(
              '📡 Real-time data: No matching items found in displayed list',
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ FeedbackDataProvider: Data listener error: $error');
        emit(
          state.copyWith(
            error: 'Failed to update feedback data: $error',
          ),
        );
      },
    );
  }
}
