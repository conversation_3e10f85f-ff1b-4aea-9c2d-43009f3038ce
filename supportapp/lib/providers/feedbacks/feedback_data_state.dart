import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/feedback.dart';

/// State specifically for feedback data management
class FeedbackDataState extends Equatable {
  const FeedbackDataState({
    required this.feedbacks,
    required this.isLoading,
    required this.isLoadingMore,
    required this.hasMore,
    required this.totalCount,
    required this.lastUpdate,
    this.error,
    this.hasNewUpdates = false,
    this.filters = const [],
  });

  /// Current list of feedbacks being displayed
  final List<FeedbackModel> feedbacks;

  /// Loading states
  final bool isLoading; // Initial load
  final bool isLoadingMore; // Loading next batch
  final bool hasMore; // Can load more feedbacks

  /// Counts and metadata
  final int totalCount; // Total count from Firestore
  final DateTime lastUpdate; // Last time data was updated
  final String? error; // Current error if any
  final bool hasNewUpdates; // New feedbacks available

  /// Current filters applied
  final List<String> filters; // FeedbackFilters as string list

  /// Initial state
  static FeedbackDataState initial() {
    return FeedbackDataState(
      feedbacks: const [],
      isLoading: true,
      isLoadingMore: false,
      hasMore: true,
      totalCount: 0,
      lastUpdate: DateTime.now(),
      error: null,
      hasNewUpdates: false,
      filters: const [
        'withRatings',
        'withoutRatings',
      ], // Default: show active feedbacks (both with and without ratings)
    );
  }

  /// Create a copy with updated values
  FeedbackDataState copyWith({
    List<FeedbackModel>? feedbacks,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    int? totalCount,
    DateTime? lastUpdate,
    String? error,
    bool? hasNewUpdates,
    List<String>? filters,
  }) {
    return FeedbackDataState(
      feedbacks: feedbacks ?? this.feedbacks,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      totalCount: totalCount ?? this.totalCount,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error,
      hasNewUpdates: hasNewUpdates ?? this.hasNewUpdates,
      filters: filters ?? this.filters,
    );
  }

  @override
  List<Object?> get props => [
        feedbacks,
        isLoading,
        isLoadingMore,
        hasMore,
        totalCount,
        lastUpdate,
        error,
        hasNewUpdates,
        filters,
      ];
}
