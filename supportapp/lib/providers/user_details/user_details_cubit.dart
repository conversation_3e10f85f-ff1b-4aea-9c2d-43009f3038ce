import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:mevolvesupport/models/activity.dart';
import 'package:mevolvesupport/models/chat_message.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/models/subscription_transaction.dart';
import 'package:mevolvesupport/models/user/feature_usage_info.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/repositories/functions_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

/// UserDetailsCubit manages real-time user details data for all tabs in DetailsPage
/// Following the architecture pattern: only talks to repositories, never Firebase directly
class UserDetailsCubit extends Cubit<UserDetailsState> {
  UserDetailsCubit({
    required DatabaseRepository databaseRepository,
    required FunctionsRepository functionsRepository,
  })  : _databaseRepository = databaseRepository,
        _functionsRepository = functionsRepository,
        super(UserDetailsState.initial()) {
    Log.d(
      'BigQueryAnalytics: 🏁 CUBIT INITIALIZED - Analytics system ready (manual polling only)',
    );
    Log.d(
      'BigQueryAnalytics: ℹ️  Analytics polling will only start when user clicks Start button in analytics tab',
    );
  }

  final DatabaseRepository _databaseRepository;
  final FunctionsRepository _functionsRepository;

  // Real-time listeners
  StreamSubscription<UserMetaData?>? _userDataListener;
  StreamSubscription<List<DeleteReport>>? _deleteReportsListener;
  StreamSubscription<List<FeedbackModel>>? _feedbacksListener;
  StreamSubscription<List<ChatMessage>>? _chatMessagesListener;
  StreamSubscription<List<TransactionModel>>? _transactionsListener;
  StreamSubscription<String?>? _supportLanguageListener;

  // Activities polling
  Timer? _activitiesPollingTimer;
  static const Duration _pollingInterval = Duration(seconds: 5);

  /// Select a user and start real-time listening
  Future<void> selectUser(String userId) async {
    final sw = Stopwatch()..start();

    Log.d(
      'BigQueryAnalytics: 👤 USER SELECTED - User: $userId (NO activity data loaded yet)',
    );
    Log.d(
      'BigQueryAnalytics: ℹ️  Activity data will only load when user selects activity tab',
    );

    if (state.selectedUserId == userId && state.userDetails != null) {
      Log.d('🔄 selectUser: already loaded');
      Log.d(
        'BigQueryAnalytics: ℹ️  User already selected, analytics polling may already be active',
      );
      return;
    }

    await _cancelListener();
    Log.d('🔄 selectUser: cancel took ${sw.elapsedMilliseconds}ms');

    // CRITICAL: Stop any existing analytics polling when selecting new user
    _stopActivitiesPolling();
    Log.d(
      'BigQueryAnalytics: 🛑 FORCE STOP - Stopped any existing polling when selecting new user',
    );

    // Clear all previous user data when selecting a new user
    emit(
      UserDetailsState.initial().copyWith(
        selectedUserId: userId,
        isLoading: true,
        error: null,
        lastUpdate: DateTime.now(),
      ),
    );

    try {
      // Set up real-time user data listener
      _userDataListener = _databaseRepository.listenUser(uid: userId).listen(
        (userData) {
          Log.d(
            '🔄 selectUser: user data received after ${sw.elapsedMilliseconds}ms',
          );
          if (userData != null) {
            emit(
              state.copyWith(
                userDetails: userData,
                isLoading: false,
                lastUpdate: DateTime.now(),
                error: null,
              ),
            );

            // Load all data for the user
            _loadAllUserData(userId);
          } else {
            // User not found - clear all data and show error
            emit(
              UserDetailsState.initial().copyWith(
                selectedUserId: userId,
                userDetails: null,
                error: 'User not found',
                isLoading: false,
                lastUpdate: DateTime.now(),
              ),
            );
          }
        },
        onError: (error) {
          Log.e('UserDetailsCubit: Error listening to user data: $error');
          emit(
            UserDetailsState.initial().copyWith(
              selectedUserId: userId,
              userDetails: null,
              error: 'Failed to load user data: ${error.toString()}',
              isLoading: false,
              lastUpdate: DateTime.now(),
            ),
          );
        },
      );
    } catch (error) {
      Log.e('UserDetailsCubit: Error setting up user listener: $error');
      emit(
        UserDetailsState.initial().copyWith(
          selectedUserId: userId,
          userDetails: null,
          error: 'Failed to set up user listener: ${error.toString()}',
          isLoading: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Load all user data concurrently - each tab tracks its own loading state
  Future<void> _loadAllUserData(String userId) async {
    final sw = Stopwatch()..start();

    // Set all tabs to loading state initially
    emit(
      state.copyWith(
        isLoadingUserDetails: true,
        isLoadingMessages: true,
        isLoadingFeedbacks: true,
        isLoadingSubscriptions: true,
        isLoadingActivities: true,
        lastUpdate: DateTime.now(),
      ),
    );

    // Start all data loading concurrently - each will update its own loading state when done
    _loadUserDetailsData(userId);
    _loadMessagesData(userId);
    _loadFeedbacksData(userId);
    _loadSubscriptionData(userId);
    // DO NOT load activity data automatically - wait for user to select activity tab
    Log.d(
      'BigQueryAnalytics: ⏸️  SKIPPING - Activity data will only load when user selects activity tab',
    );

    Log.d(
      '🔄 _loadAllUserData: all concurrent loading started in ${sw.elapsedMilliseconds}ms',
    );
  }

  /// Load user details data - Real-time listeners
  Future<void> _loadUserDetailsData(String userId) async {
    final sw = Stopwatch()..start();
    try {
      // Set up real-time deleted reports listener
      await _deleteReportsListener?.cancel();
      _deleteReportsListener = _databaseRepository
          .listenUserDeleteReports(
        uid: userId,
      )
          .listen(
        (deleteReports) {
          emit(
            state.copyWith(
              userDeleteReports: deleteReports,
              lastUpdate: DateTime.now(),
            ),
          );
        },
        onError: (error) {
          Log.e('UserDetailsCubit: Error listening to deleted reports: $error');
        },
      );

      // Load feature usage info only once when user details are first loaded
      if (state.userDetails != null && !state.hasLoadedFeatureUsageInfo) {
        try {
          final FeatureUsageInfo featureUsageInfo =
              await _databaseRepository.fetchFeaturesCount(userId);
          emit(
            state.copyWith(
              featureUsageInfo: featureUsageInfo,
              hasLoadedFeatureUsageInfo: true,
              lastUpdate: DateTime.now(),
            ),
          );
          Log.d(
            '🔄 Feature usage info loaded: ${featureUsageInfo.todosCount} todos, ${featureUsageInfo.notesCount} notes',
          );
        } catch (error) {
          Log.e('UserDetailsCubit: Error loading feature usage info: $error');
        }
      }

      Log.d('🔄 _loadUserDetailsData: ${sw.elapsedMilliseconds}ms');

      // Mark user details loading as complete
      emit(
        state.copyWith(
          isLoadingUserDetails: false,
          lastUpdate: DateTime.now(),
        ),
      );
    } catch (error) {
      Log.e(
        'UserDetailsCubit: Error setting up deleted reports listener: $error',
      );

      // Mark user details loading as complete even on error
      emit(
        state.copyWith(
          isLoadingUserDetails: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Load messages data - Hybrid approach: fetch + lightweight listener
  Future<void> _loadMessagesData(String userId) async {
    final sw = Stopwatch()..start();
    try {
      // Fetch chat user and support language concurrently (one-time)
      final results = await Future.wait([
        _databaseRepository.fetchChatUser(userId),
        _databaseRepository.listenUserSupportLanguage(userId).first,
      ]);
      Log.d(
        '🔄 _loadMessagesData: fetch user+lang took ${sw.elapsedMilliseconds}ms',
      );

      final chatUser = results[0] as ChatUser?;
      final supportLanguage = results[1] as String?;

      // Fetch initial messages (30 most recent)
      final messages = await _databaseRepository.fetchMessages(uid: userId);
      Log.d(
        '🔄 _loadMessagesData: fetch messages took ${sw.elapsedMilliseconds}ms',
      );

      // Track pagination timestamps
      DateTime? firstMessageCloudTime;
      DateTime? firstMessageLocalTime;
      DateTime? lastMessageCloudTime;
      DateTime? lastMessageLocalTime;

      if (messages.isNotEmpty) {
        // Sort messages by timestamp (newest first)
        messages
            .sort((a, b) => (b.localUpdatedAt!).compareTo(a.localUpdatedAt!));

        firstMessageCloudTime = messages.first.cloudUpdatedAt;
        firstMessageLocalTime = messages.first.localUpdatedAt;
        lastMessageCloudTime = messages.last.cloudUpdatedAt;
        lastMessageLocalTime = messages.last.localUpdatedAt;
      }

      // Set up real-time support language listener
      await _supportLanguageListener?.cancel();
      _supportLanguageListener =
          _databaseRepository.listenUserSupportLanguage(userId).listen(
        (supportLanguage) {
          emit(
            state.copyWith(
              supportLanguage: supportLanguage ?? 'english',
              // Default fallback
              lastUpdate: DateTime.now(),
            ),
          );
        },
        onError: (error) {
          Log.e(
            'UserDetailsCubit: Error listening to support language: $error',
          );
          // On error, set default language
          emit(
            state.copyWith(
              supportLanguage: 'english',
              lastUpdate: DateTime.now(),
            ),
          );
        },
      );

      // Set up lightweight listener for NEW messages only (since first message)
      await _chatMessagesListener?.cancel();
      if (firstMessageCloudTime != null && firstMessageLocalTime != null) {
        _chatMessagesListener = _databaseRepository
            .listenMessages(
          uid: userId,
          firstMessageCloudTime: firstMessageCloudTime,
          firstMessageLocalTime: firstMessageLocalTime,
        )
            .listen(
          (newMessages) {
            if (newMessages.isNotEmpty) {
              Log.d(
                '📬 UserDetailsCubit: Received ${newMessages.length} new messages',
              );
              for (int i = 0; i < newMessages.length; i++) {
                final msg = newMessages[i];
                final msgPreview = msg.message.en.length > 20
                    ? '${msg.message.en.substring(0, 20)}...'
                    : msg.message.en;
                Log.d(
                  '  📬 New[$i]: "$msgPreview" - updatedBy: ${msg.updatedBy} - Time: ${msg.localUpdatedAt}',
                );
              }

              // Merge new messages with existing ones
              final updatedMessages =
                  _mergeNewMessages(state.chatMessages, newMessages);

              // Update first message timestamps for future new message detection
              final sortedMessages = List.from(updatedMessages)
                ..sort(
                  (a, b) => (b.localUpdatedAt!).compareTo(a.localUpdatedAt!),
                );

              emit(
                state.copyWith(
                  chatMessages: updatedMessages,
                  firstMessageCloudTime: sortedMessages.first.cloudUpdatedAt,
                  firstMessageLocalTime: sortedMessages.first.localUpdatedAt,
                  lastUpdate: DateTime.now(),
                ),
              );
            }
          },
          onError: (error) {
            Log.e('UserDetailsCubit: Error listening to new messages: $error');
          },
        );
      }

      emit(
        state.copyWith(
          chatUser: chatUser,
          chatMessages: messages,
          supportLanguage: supportLanguage ?? 'english',
          firstMessageCloudTime: firstMessageCloudTime,
          firstMessageLocalTime: firstMessageLocalTime,
          lastMessageCloudTime: lastMessageCloudTime,
          lastMessageLocalTime: lastMessageLocalTime,
          isLoadingMessages: false,
          lastUpdate: DateTime.now(),
        ),
      );
    } catch (error) {
      Log.e('UserDetailsCubit: Error setting up messages data: $error');
    }
  }

  /// Load feedbacks data - Real-time listeners
  Future<void> _loadFeedbacksData(String userId) async {
    final sw = Stopwatch()..start();
    try {
      // Set up real-time feedbacks listener
      await _feedbacksListener?.cancel();
      _feedbacksListener = _databaseRepository
          .listenUserFeedbacks(
        uid: userId,
      )
          .listen(
        (feedbacks) {
          emit(
            state.copyWith(
              userFeedbacks: feedbacks,
              lastUpdate: DateTime.now(),
            ),
          );
        },
        onError: (error) {
          Log.e('UserDetailsCubit: Error listening to feedbacks: $error');
        },
      );
      Log.d('🔄 _loadFeedbacksData: ${sw.elapsedMilliseconds}ms');

      // Mark feedbacks loading as complete
      emit(
        state.copyWith(
          isLoadingFeedbacks: false,
          lastUpdate: DateTime.now(),
        ),
      );
    } catch (error) {
      Log.e('UserDetailsCubit: Error setting up feedbacks listener: $error');

      // Mark feedbacks loading as complete even on error
      emit(
        state.copyWith(
          isLoadingFeedbacks: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Load subscription data - Real-time listeners
  Future<void> _loadSubscriptionData(String userId) async {
    final sw = Stopwatch()..start();
    try {
      // Set up real-time transactions listener
      await _transactionsListener?.cancel();
      _transactionsListener = _databaseRepository
          .listenUserTransactions(
        uid: userId,
      )
          .listen(
        (transactions) {
          emit(
            state.copyWith(
              userTransactions: transactions,
              lastUpdate: DateTime.now(),
            ),
          );
        },
        onError: (error) {
          Log.e('UserDetailsCubit: Error listening to transactions: $error');
        },
      );
      Log.d('🔄 _loadSubscriptionData: ${sw.elapsedMilliseconds}ms');

      // Mark subscriptions loading as complete
      emit(
        state.copyWith(
          isLoadingSubscriptions: false,
          lastUpdate: DateTime.now(),
        ),
      );
    } catch (error) {
      Log.e('UserDetailsCubit: Error setting up transactions listener: $error');

      // Mark subscriptions loading as complete even on error
      emit(
        state.copyWith(
          isLoadingSubscriptions: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Load activity data when user selects activity tab - NO automatic polling, only manual
  Future<void> loadActivityDataOnTabSelect() async {
    if (state.selectedUserId == null) {
      Log.d(
        'BigQueryAnalytics: ❌ No user selected - cannot load activity data',
      );
      return;
    }

    // Check if activity data is already loaded to avoid unnecessary calls
    if (state.userActivities.isNotEmpty && !state.isLoadingActivities) {
      Log.d(
        'BigQueryAnalytics: ✅ Activity data already loaded - skipping reload',
      );
      return;
    }

    final userId = state.selectedUserId!;
    Log.d(
      'BigQueryAnalytics: 📑 ACTIVITY TAB SELECTED - User navigated to activity tab, loading data for: $userId',
    );

    await _loadActivityData(userId);
  }

  /// Load activity data - NO automatic polling, only manual
  Future<void> _loadActivityData(String userId) async {
    final sw = Stopwatch()..start();
    try {
      // Stop any existing polling
      _stopActivitiesPolling();

      Log.d(
        'BigQueryAnalytics: 📋 Loading analytics data for user: $userId (triggered by activity tab selection)',
      );

      // Load initial activities without filters
      final activities = await _functionsRepository.fetchUserActivities(
        userId: userId,
      );
      Log.d('🔄 _loadActivityData: ${sw.elapsedMilliseconds}ms');
      Log.d(
        'BigQueryAnalytics: 📊 Initial analytics data loaded - ${activities.length} activities found',
      );

      emit(
        state.copyWith(
          userActivities: activities,
          isLoadingActivities: false,
          lastUpdate: DateTime.now(),
          activitiesPollingError: null,
        ),
      );

      // DO NOT start automatic polling - wait for user to click Start button
      Log.d(
        'BigQueryAnalytics: ⏸️  Activity data loaded - waiting for user to click Start button for polling',
      );
    } catch (error) {
      Log.e('UserDetailsCubit: Error loading activity data: $error');
      Log.e(
        'BigQueryAnalytics: ❌ Failed to load initial analytics data: $error',
      );
      emit(
        state.copyWith(
          activitiesPollingError:
              'Failed to load activities: ${error.toString()}',
          isLoadingActivities: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Load more activities with pagination
  Future<void> loadMoreActivities({
    String? eventName,
    String? eventType,
    DateTime? startFrom,
  }) async {
    if (state.selectedUserId == null || state.isLoadingActivities) {
      return;
    }

    final userId = state.selectedUserId!;

    emit(
      state.copyWith(
        isLoadingActivities: true,
        lastUpdate: DateTime.now(),
      ),
    );

    try {
      // Get pagination info from current activities
      String? startAfter;
      String? startAfterTime;
      int? skip;

      if (state.userActivities.isNotEmpty) {
        // Sort activities by timestamp to find the oldest
        final sortedActivities = List.from(state.userActivities)
          ..sort((a, b) => a.eventTime.compareTo(b.eventTime));

        final oldestActivity = sortedActivities.first;
        final rawData = oldestActivity.rawData;

        if (rawData.containsKey('event_timestamp')) {
          startAfter = rawData['event_timestamp'].toString();
        } else {
          startAfterTime = oldestActivity.eventTime.toUtc().toIso8601String();
        }

        skip = state.userActivities.length;
      }

      final newActivities = await _functionsRepository.fetchUserActivities(
        userId: userId,
        eventName: eventName,
        eventType: eventType,
        startFrom: startFrom,
        startAfter: startAfter,
        startAfterTime: startAfterTime,
        skip: skip,
      );

      // Append new activities to existing ones
      final allActivities = [...state.userActivities, ...newActivities];

      emit(
        state.copyWith(
          userActivities: allActivities,
          lastUpdate: DateTime.now(),
        ),
      );
    } catch (error) {
      Log.e('UserDetailsCubit: Error loading more activities: $error');
    } finally {
      emit(
        state.copyWith(
          isLoadingActivities: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Start live polling for activities (Firebase Analytics debug view style)
  void _startActivitiesPolling(String userId) {
    Log.d('UserDetailsCubit: Starting activities polling for user: $userId');
    Log.d(
      'BigQueryAnalytics: ▶️  MANUAL POLLING STARTED - User clicked Start button - Will query analytics every ${_pollingInterval.inSeconds} seconds for user: $userId',
    );
    Log.d(
      'BigQueryAnalytics: ⚠️  This may be making frequent BigQuery calls to analytics tables!',
    );
    _stopActivitiesPolling();

    emit(
      state.copyWith(
        isPollingActivities: true,
        activitiesPollingError: null,
        lastUpdate: DateTime.now(),
      ),
    );

    _activitiesPollingTimer = Timer.periodic(_pollingInterval, (timer) async {
      Log.d(
        'BigQueryAnalytics: 🔄 Timer tick - About to poll analytics data (${DateTime.now()}) for user: $userId',
      );
      Log.d(
        'BigQueryAnalytics: 🔄 Timer state check - selectedUserId: ${state.selectedUserId}, isPollingActivities: ${state.isPollingActivities}',
      );
      await _pollActivities(userId);
    });

    Log.d('BigQueryAnalytics: ✅ Timer created and started for user: $userId');
  }

  /// Stop activities polling
  void _stopActivitiesPolling() {
    Log.d(
      'BigQueryAnalytics: 🛑 _stopActivitiesPolling called - Timer active: ${_activitiesPollingTimer != null}, State polling: ${state.isPollingActivities}',
    );

    if (_activitiesPollingTimer != null) {
      Log.d('BigQueryAnalytics: 🛑 STOPPING automatic analytics polling timer');
      _activitiesPollingTimer?.cancel();
      _activitiesPollingTimer = null;
      Log.d('BigQueryAnalytics: ✅ Timer cancelled and set to null');
    }

    if (state.isPollingActivities) {
      Log.d(
        'BigQueryAnalytics: ✅ Analytics polling stopped - no more automatic BigQuery calls',
      );
      emit(
        state.copyWith(
          isPollingActivities: false,
          lastUpdate: DateTime.now(),
        ),
      );
      Log.d(
        'BigQueryAnalytics: ✅ State updated - isPollingActivities set to false',
      );
    }

    Log.d(
      'BigQueryAnalytics: ✅ _stopActivitiesPolling completed - Timer: ${_activitiesPollingTimer != null}, State: ${state.isPollingActivities}',
    );
  }

  /// Poll for new activities (newest first, like Firebase Analytics debug view)
  Future<void> _pollActivities(String userId) async {
    Log.d(
      'BigQueryAnalytics: 🔍 _pollActivities called - userId: $userId, selectedUserId: ${state.selectedUserId}, isPollingActivities: ${state.isPollingActivities}',
    );

    if (state.selectedUserId != userId || !state.isPollingActivities) {
      Log.d(
        'BigQueryAnalytics: 🛑 Stopping polling - User mismatch or polling disabled',
      );
      _stopActivitiesPolling();
      return;
    }

    try {
      Log.d(
        'BigQueryAnalytics: 📡 POLLING - Making analytics call for user: $userId (last 5 minutes)',
      );
      final pollStartTime = DateTime.now();

      // Fetch newest activities
      final newActivities = await _functionsRepository.fetchUserActivities(
        userId: userId,
        startFrom: DateTime.now()
            .subtract(const Duration(minutes: 5)), // Last 5 minutes
      );

      final pollDuration = DateTime.now().difference(pollStartTime);
      Log.d(
        'BigQueryAnalytics: ⏱️  Analytics call completed in ${pollDuration.inMilliseconds}ms - Found ${newActivities.length} new activities',
      );

      if (newActivities.isNotEmpty) {
        // Merge with existing activities, newest first
        final mergedActivities =
            _mergeActivities(state.userActivities, newActivities);

        emit(
          state.copyWith(
            userActivities: mergedActivities,
            lastPollingUpdate: DateTime.now(),
            lastUpdate: DateTime.now(),
            activitiesPollingError: null,
          ),
        );

        Log.d(
          'UserDetailsCubit: Polled ${newActivities.length} new activities',
        );
        Log.d(
          'BigQueryAnalytics: 📈 Updated UI with ${newActivities.length} new activities from analytics',
        );
      } else {
        // Update polling timestamp even if no new activities
        Log.d(
          'BigQueryAnalytics: 📊 No new activities found in this polling cycle',
        );
        emit(
          state.copyWith(
            lastPollingUpdate: DateTime.now(),
            lastUpdate: DateTime.now(),
          ),
        );
      }
    } catch (error) {
      Log.e('UserDetailsCubit: Error polling activities: $error');
      Log.e('BigQueryAnalytics: ❌ Analytics polling failed: $error');
      emit(
        state.copyWith(
          activitiesPollingError: 'Polling error: ${error.toString()}',
          lastPollingUpdate: DateTime.now(),
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Load more messages with pagination (for scroll-to-top loading)
  Future<void> loadMoreMessages() async {
    if (state.selectedUserId == null || state.isLoadingMessages) {
      return;
    }

    final userId = state.selectedUserId!;

    emit(
      state.copyWith(
        isLoadingMessages: true,
        lastUpdate: DateTime.now(),
      ),
    );

    try {
      // Fetch older messages using pagination
      final olderMessages = await _databaseRepository.fetchMessages(
        uid: userId,
        lastMessageCloudTime: state.lastMessageCloudTime,
        lastMessageLocalTime: state.lastMessageLocalTime,
      );

      if (olderMessages.isNotEmpty) {
        // Merge older messages with existing ones
        final allMessages = [...state.chatMessages, ...olderMessages];

        // Sort messages by timestamp (newest first)
        allMessages
            .sort((a, b) => (b.localUpdatedAt!).compareTo(a.localUpdatedAt!));

        // Update pagination tracking with the oldest message from new batch
        final sortedOlderMessages = List.from(olderMessages)
          ..sort((a, b) => (a.localUpdatedAt!).compareTo(b.localUpdatedAt!));

        final oldestMessage = sortedOlderMessages.first;

        emit(
          state.copyWith(
            chatMessages: allMessages,
            lastMessageCloudTime: oldestMessage.cloudUpdatedAt,
            lastMessageLocalTime: oldestMessage.localUpdatedAt,
            lastUpdate: DateTime.now(),
          ),
        );
      }
    } catch (error) {
      Log.e('UserDetailsCubit: Error loading more messages: $error');
    } finally {
      emit(
        state.copyWith(
          isLoadingMessages: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Merge new messages with existing ones intelligently (newest first, avoid duplicates)
  List<ChatMessage> _mergeNewMessages(
    List<ChatMessage> existingMessages,
    List<ChatMessage> newMessages,
  ) {
    Log.d('🔄 _mergeNewMessages: Starting merge...');
    Log.d('  📤 Existing messages: ${existingMessages.length}');
    for (int i = 0; i < existingMessages.length; i++) {
      final msg = existingMessages[i];
      final msgPreview = msg.message.en.length > 20
          ? '${msg.message.en.substring(0, 20)}...'
          : msg.message.en;
      Log.d('    Existing[$i]: "$msgPreview" - Time: ${msg.localUpdatedAt}');
    }

    // Create a set of existing message IDs for duplicate detection
    final existingIds = existingMessages.map((message) => message.id).toSet();

    // Filter out duplicates from new messages
    final uniqueNewMessages = newMessages.where((message) {
      return !existingIds.contains(message.id);
    }).toList();

    Log.d('  📥 Unique new messages: ${uniqueNewMessages.length}');

    // Combine and sort by timestamp (newest first)
    final allMessages = [...uniqueNewMessages, ...existingMessages];
    allMessages
        .sort((a, b) => (b.localUpdatedAt!).compareTo(a.localUpdatedAt!));

    Log.d('  📋 Final merged messages: ${allMessages.length}');
    for (int i = 0; i < allMessages.length; i++) {
      final msg = allMessages[i];
      final msgPreview = msg.message.en.length > 20
          ? '${msg.message.en.substring(0, 20)}...'
          : msg.message.en;
      Log.d('    Final[$i]: "$msgPreview" - Time: ${msg.localUpdatedAt}');
    }

    return allMessages;
  }

  /// Merge activities intelligently (newest first, avoid duplicates)
  List<ActivityItem> _mergeActivities(
    List<ActivityItem> existing,
    List<ActivityItem> newActivities,
  ) {
    // Create a set of existing activity timestamps for duplicate detection
    final existingTimestamps = existing
        .map((activity) => activity.rawData['event_timestamp'])
        .where((timestamp) => timestamp != null)
        .toSet();

    // Filter out duplicates from new activities
    final uniqueNewActivities = newActivities.where((activity) {
      final timestamp = activity.rawData['event_timestamp'];
      return timestamp != null && !existingTimestamps.contains(timestamp);
    }).toList();

    // Combine and sort by timestamp (newest first)
    final allActivities = [...uniqueNewActivities, ...existing];
    allActivities.sort((a, b) => b.eventTime.compareTo(a.eventTime));

    // Limit to reasonable number (like Firebase Analytics debug view)
    return allActivities.take(100).toList();
  }

  /// Manual refresh activities (for UI button)
  Future<void> refreshActivities() async {
    if (state.selectedUserId == null) return;

    final userId = state.selectedUserId!;
    Log.d('UserDetailsCubit: Manual refresh activities for user: $userId');
    Log.d(
      'BigQueryAnalytics: 🔄 MANUAL REFRESH - User triggered analytics refresh for user: $userId',
    );

    await _pollActivities(userId);
  }

  /// Toggle activities polling on/off (MANUAL ONLY)
  void toggleActivitiesPolling() {
    if (state.selectedUserId == null) return;

    if (state.isPollingActivities) {
      _stopActivitiesPolling();
      Log.d('UserDetailsCubit: Activities polling stopped by user');
      Log.d(
        'BigQueryAnalytics: 🛑 USER CLICKED STOP - Analytics polling manually disabled by user',
      );
    } else {
      _startActivitiesPolling(state.selectedUserId!);
      Log.d('UserDetailsCubit: Activities polling started by user');
      Log.d(
        'BigQueryAnalytics: ▶️  USER CLICKED START - Analytics polling manually enabled by user',
      );
    }
  }

  /// Clear selected user
  void clearUser() {
    Log.d(
      'BigQueryAnalytics: 🧹 USER CLEARED - Stopping all analytics tracking and polling',
    );
    _cancelListener();
    emit(state.clearUser());
  }

  /// Refresh user data - forces a fresh fetch
  Future<void> refreshUserData() async {
    if (state.selectedUserId == null) return;

    final userId = state.selectedUserId!;

    // Cancel current listener to force refresh
    await _cancelListener();

    // Re-select the same user to trigger fresh data load
    await selectUser(userId);
  }

  /// Refresh feature usage info - forces a fresh fetch
  Future<void> refreshFeatureUsageInfo() async {
    if (state.selectedUserId == null || state.userDetails == null) return;

    final userId = state.selectedUserId!;

    // Set loading state
    emit(
      state.copyWith(
        isRefreshingFeatureUsage: true,
        lastUpdate: DateTime.now(),
      ),
    );

    try {
      Log.d('🔄 Refreshing feature usage info for user: $userId');
      final FeatureUsageInfo featureUsageInfo =
          await _databaseRepository.fetchFeaturesCount(userId);
      emit(
        state.copyWith(
          featureUsageInfo: featureUsageInfo,
          isRefreshingFeatureUsage: false,
          lastUpdate: DateTime.now(),
        ),
      );
      Log.d(
        '✅ Feature usage info refreshed: ${featureUsageInfo.todosCount} todos, ${featureUsageInfo.notesCount} notes',
      );
    } catch (error) {
      Log.e('❌ UserDetailsCubit: Error refreshing feature usage info: $error');
      // Clear loading state on error
      emit(
        state.copyWith(
          isRefreshingFeatureUsage: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Cancel all active listeners
  Future<void> _cancelListener() async {
    await _userDataListener?.cancel();
    await _deleteReportsListener?.cancel();
    await _feedbacksListener?.cancel();
    await _chatMessagesListener?.cancel();
    await _transactionsListener?.cancel();
    await _supportLanguageListener?.cancel();

    // Stop activities polling
    _stopActivitiesPolling();

    _userDataListener = null;
    _deleteReportsListener = null;
    _feedbacksListener = null;
    _chatMessagesListener = null;
    _transactionsListener = null;
    _supportLanguageListener = null;
  }

  Future<String?> fetchEmailFromUid({
    required String uid,
  }) async {
    if (state.showEmail) {
      emit(
        state.copyWith(
          email: null,
          showEmail: false,
          isEmailLoading: false,
        ),
      );

      return null;
    }

    emit(state.copyWith(isEmailLoading: true));

    final email = await _databaseRepository.fetchEmailFromUid(
      uid,
    );

    emit(
      state.copyWith(
        email: email,
        showEmail: true,
        isEmailLoading: false,
      ),
    );

    return email;
  }

  @override
  Future<void> close() async {
    await _cancelListener();
    return super.close();
  }
}
