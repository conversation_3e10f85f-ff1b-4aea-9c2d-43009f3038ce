import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/activity.dart';
import 'package:mevolvesupport/models/chat_message.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/models/subscription_transaction.dart';
import 'package:mevolvesupport/models/user/feature_usage_info.dart';
import 'package:mevolvesupport/models/user_metadata.dart';

/// State specifically for user details real-time management
/// Manages data for all 5 tabs in the details page
class UserDetailsState extends Equatable {
  const UserDetailsState({
    required this.selectedUserId,
    required this.userDetails,
    required this.isLoading,
    required this.lastUpdate,
    this.error,
    this.userDeleteReports = const [],
    this.chatUser,
    this.chatMessages = const [],
    this.supportLanguage,
    this.lastMessageCloudTime,
    this.lastMessageLocalTime,
    this.firstMessageCloudTime,
    this.firstMessageLocalTime,
    this.userFeedbacks = const [],
    this.userTransactions = const [],
    this.userActivities = const [],
    this.featureUsageInfo,
    this.hasLoadedFeatureUsageInfo = false,
    this.isRefreshingFeatureUsage = false,
    this.isLoadingUserDetails = false,
    this.isLoadingMessages = false,
    this.isLoadingFeedbacks = false,
    this.isLoadingSubscriptions = false,
    this.isLoadingActivities = false,
    this.isPollingActivities = false,
    this.lastPollingUpdate,
    this.activitiesPollingError,
    this.isEmailLoading = false,
    this.showEmail = false,
    this.email,
  });

  /// Currently selected user ID
  final String? selectedUserId;

  /// Real-time user details data
  final UserMetaData? userDetails;

  /// Loading state for initial user load
  final bool isLoading;

  /// Individual loading states for each tab
  final bool isLoadingUserDetails;
  final bool isLoadingMessages;
  final bool isLoadingFeedbacks;
  final bool isLoadingSubscriptions;
  final bool isLoadingActivities;

  /// Last time data was updated
  final DateTime lastUpdate;

  /// Current error if any
  final String? error;

  /// User's deleted reports
  final List<DeleteReport> userDeleteReports;

  /// Chat user data
  final ChatUser? chatUser;

  /// Chat messages
  final List<ChatMessage> chatMessages;

  /// Support language for message translation (default: 'english')
  final String? supportLanguage;

  /// Pagination tracking for messages
  final DateTime? lastMessageCloudTime;
  final DateTime? lastMessageLocalTime;
  final DateTime? firstMessageCloudTime;
  final DateTime? firstMessageLocalTime;

  /// User feedbacks
  final List<FeedbackModel> userFeedbacks;

  /// User subscription transactions
  final List<TransactionModel> userTransactions;

  /// User activities
  final List<ActivityItem> userActivities;

  /// Feature usage information
  final FeatureUsageInfo? featureUsageInfo;

  /// Flag to track if feature usage info has been loaded initially
  final bool hasLoadedFeatureUsageInfo;

  /// Loading state for feature usage refresh
  final bool isRefreshingFeatureUsage;

  /// Live polling state for activities
  final bool isPollingActivities;

  /// Last time activities were polled
  final DateTime? lastPollingUpdate;

  /// Polling error if any
  final String? activitiesPollingError;

  /// User email loading state
  final bool isEmailLoading;
  final bool showEmail;
  final String? email;

  /// Initial state
  static UserDetailsState initial() {
    return UserDetailsState(
      selectedUserId: null,
      userDetails: null,
      isLoading: false,
      lastUpdate: DateTime.now(),
      error: null,
      userDeleteReports: const [],
      chatUser: null,
      chatMessages: const [],
      userFeedbacks: const [],
      userTransactions: const [],
      userActivities: const [],
      isLoadingUserDetails: false,
      isLoadingMessages: false,
      isLoadingFeedbacks: false,
      isLoadingSubscriptions: false,
      isLoadingActivities: false,
      isPollingActivities: false,
      // CRITICAL: Reset polling state
      lastPollingUpdate: null,
      activitiesPollingError: null,
      // Email state
      isEmailLoading: false,
      showEmail: false,
      email: null,
    );
  }

  /// Create a copy with updated values
  UserDetailsState copyWith({
    String? selectedUserId,
    UserMetaData? userDetails,
    bool? isLoading,
    bool? isLoadingUserDetails,
    bool? isLoadingMessages,
    bool? isLoadingFeedbacks,
    bool? isLoadingSubscriptions,
    bool? isLoadingActivities,
    DateTime? lastUpdate,
    String? error,
    List<DeleteReport>? userDeleteReports,
    ChatUser? chatUser,
    List<ChatMessage>? chatMessages,
    String? supportLanguage,
    DateTime? lastMessageCloudTime,
    DateTime? lastMessageLocalTime,
    DateTime? firstMessageCloudTime,
    DateTime? firstMessageLocalTime,
    List<FeedbackModel>? userFeedbacks,
    List<TransactionModel>? userTransactions,
    List<ActivityItem>? userActivities,
    FeatureUsageInfo? featureUsageInfo,
    bool? hasLoadedFeatureUsageInfo,
    bool? isRefreshingFeatureUsage,
    bool? isPollingActivities,
    DateTime? lastPollingUpdate,
    String? activitiesPollingError,
    bool? isEmailLoading,
    bool? showEmail,
    String? email,
  }) {
    return UserDetailsState(
      selectedUserId: selectedUserId ?? this.selectedUserId,
      userDetails: userDetails ?? this.userDetails,
      isLoading: isLoading ?? this.isLoading,
      isLoadingUserDetails: isLoadingUserDetails ?? this.isLoadingUserDetails,
      isLoadingMessages: isLoadingMessages ?? this.isLoadingMessages,
      isLoadingFeedbacks: isLoadingFeedbacks ?? this.isLoadingFeedbacks,
      isLoadingSubscriptions:
          isLoadingSubscriptions ?? this.isLoadingSubscriptions,
      isLoadingActivities: isLoadingActivities ?? this.isLoadingActivities,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error,
      userDeleteReports: userDeleteReports ?? this.userDeleteReports,
      chatUser: chatUser ?? this.chatUser,
      chatMessages: chatMessages ?? this.chatMessages,
      supportLanguage: supportLanguage ?? this.supportLanguage,
      lastMessageCloudTime: lastMessageCloudTime ?? this.lastMessageCloudTime,
      lastMessageLocalTime: lastMessageLocalTime ?? this.lastMessageLocalTime,
      firstMessageCloudTime:
          firstMessageCloudTime ?? this.firstMessageCloudTime,
      firstMessageLocalTime:
          firstMessageLocalTime ?? this.firstMessageLocalTime,
      userFeedbacks: userFeedbacks ?? this.userFeedbacks,
      userTransactions: userTransactions ?? this.userTransactions,
      userActivities: userActivities ?? this.userActivities,
      featureUsageInfo: featureUsageInfo ?? this.featureUsageInfo,
      hasLoadedFeatureUsageInfo:
          hasLoadedFeatureUsageInfo ?? this.hasLoadedFeatureUsageInfo,
      isRefreshingFeatureUsage:
          isRefreshingFeatureUsage ?? this.isRefreshingFeatureUsage,
      isPollingActivities: isPollingActivities ?? this.isPollingActivities,
      lastPollingUpdate: lastPollingUpdate ?? this.lastPollingUpdate,
      activitiesPollingError:
          activitiesPollingError ?? this.activitiesPollingError,
      isEmailLoading: isEmailLoading ?? this.isEmailLoading,
      showEmail: showEmail ?? this.showEmail,
      email: email ?? this.email,
    );
  }

  /// Clear user details (when no user is selected)
  UserDetailsState clearUser() {
    return UserDetailsState(
      selectedUserId: null,
      userDetails: null,
      isLoading: false,
      lastUpdate: DateTime.now(),
      error: null,
      userDeleteReports: const [],
      chatUser: null,
      chatMessages: const [],
      userFeedbacks: const [],
      userTransactions: const [],
      userActivities: const [],
      isLoadingUserDetails: false,
      isLoadingMessages: false,
      isLoadingFeedbacks: false,
      isLoadingSubscriptions: false,
      isLoadingActivities: false,
      isPollingActivities: false,
      // CRITICAL: Reset polling state
      lastPollingUpdate: null,
      activitiesPollingError: null,
      isEmailLoading: false,
      showEmail: false,
      email: null,
    );
  }

  @override
  List<Object?> get props => [
        selectedUserId,
        userDetails,
        isLoading,
        isLoadingUserDetails,
        isLoadingMessages,
        isLoadingFeedbacks,
        isLoadingSubscriptions,
        isLoadingActivities,
        lastUpdate,
        error,
        userDeleteReports,
        chatUser,
        chatMessages,
        supportLanguage,
        lastMessageCloudTime,
        lastMessageLocalTime,
        firstMessageCloudTime,
        firstMessageLocalTime,
        userFeedbacks,
        userTransactions,
        userActivities,
        featureUsageInfo,
        hasLoadedFeatureUsageInfo,
        isRefreshingFeatureUsage,
        isPollingActivities,
        lastPollingUpdate,
        activitiesPollingError,
        isEmailLoading,
        showEmail,
        email,
      ];
}
