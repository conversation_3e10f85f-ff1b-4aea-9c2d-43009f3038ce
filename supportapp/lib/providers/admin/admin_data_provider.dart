// providers/admin/admin_data_provider.dart
import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/providers/admin/admin_data_state.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

/// **AdminDataProvider - Advanced Real-time Support Users List Manager**
///
/// This provider implements a sophisticated support users list management system with:
/// - Real-time updates for displayed items only (performance optimized)
/// - Intelligent staleness detection to notify when list needs refresh
/// - Cursor-based pagination with smart totalCount adjustment
/// - Search functionality for name and email filtering
/// - Flexible filtering system for user attributes
///
/// ## 🏗️ ARCHITECTURE OVERVIEW
///
/// ### Data Flow Patterns
///
/// **1. Initial Load Flow**
/// ```
/// initialize() → _setupListeners() → _loadInitialData() → _setupDisplayedSupportUsersListener()
///                                 ↓
///                          Sets up staleness detection after initial load
/// ```
/// - Fetches first page of support users + total count
/// - Sets up targeted real-time listeners
/// - Initializes staleness detection
///
/// **2. Real-time Update Flow**
/// ```
/// Firestore Change → listenSupportUserUpdatesByIDs() → Update in-place → Re-sort → Emit
///                                                ↓
///                                         Preserve totalCount
/// ```
/// - Only listens to currently displayed support user IDs (efficient)
/// - Updates existing items without changing list structure
/// - Maintains sort order and pagination state
///
/// **3. Pagination Flow**
/// ```
/// loadMore() → Fetch from cursor → Filter duplicates → Append → Update hasMore
///            ↓
///         Smart totalCount adjustment if no more items available
/// ```
/// - Uses last displayed item's lastLogin as cursor
/// - Handles database inconsistencies gracefully
///
/// **4. Staleness Detection Flow**
/// ```
/// Background Listener → Compare ID sets → Set hasNewUpdates flag
///                    ↓
///               Only triggers on actual content differences
/// ```
/// - Runs parallel staleness check with same count as displayed
/// - Ignores order changes, only detects content differences
///
/// **5. Search Flow**
/// ```
/// search() → Reset pagination → Apply search filters → _loadInitialData()
///         ↓
///      Updates search query and reloads with filtered results
/// ```
/// - Client-side filtering by name and email
/// - Resets pagination state for new search
///
/// ## 📊 STATE MANAGEMENT
///
/// ### Core State Properties
/// - **`supportUsers`**: Currently displayed support users list (paginated)
/// - **`totalCount`**: Stable count from initial fetch (only updated on refresh)
/// - **`hasMore`**: Can load more items (displayed < total)
/// - **`hasNewUpdates`**: Content staleness flag
/// - **`searchQuery`**: Active search query applied
///
/// ### Real-time Listeners
/// - **Displayed Items**: `_displayedSupportUsersListener` (ID-based)
/// - **Staleness Detection**: `_stalenessDetectionListener` (background)
///
/// ## 🎯 PUBLIC API
///
/// **Core Operations**
/// - `initialize(searchQuery)` - Load initial data with search
/// - `loadMore()` - Load next batch of support users
/// - `refresh()` - Reload fresh data, clear staleness
/// - `changeFilter(searchQuery)` - Apply search filter and reload
///
/// ## ⚡ PERFORMANCE OPTIMIZATIONS
///
/// - **Targeted Updates**: Only listen to displayed IDs
/// - **Cursor Pagination**: Efficient database queries
/// - **Smart Deduplication**: Prevents duplicate items
/// - **Stable Counts**: totalCount preserved during updates
/// - **Background Staleness**: Non-blocking freshness checks
/// - **Client-side Search**: Fast filtering without additional queries
class AdminDataProvider extends Cubit<AdminDataState> {
  AdminDataProvider({
    required DatabaseRepository databaseRepository,
  })  : _databaseRepository = databaseRepository,
        super(AdminDataState.initial());

  final DatabaseRepository _databaseRepository;

  // ============================================================================
  // PRIVATE STATE VARIABLES
  // ============================================================================

  // --- Real-time Stream Subscriptions ---

  /// Listener for displayed support users updates (efficient ID-based targeting)
  StreamSubscription? _displayedSupportUsersListener;

  /// Listener for staleness detection (background monitoring, non-blocking)
  StreamSubscription? _stalenessDetectionListener;

  // --- Pagination & Cursor State ---

  /// Pagination cursor using lastLogin timestamp (consistent with Firestore ordering)
  DateTime? _lastLoginDateTime;

  /// Items per page for initial load (balanced performance vs UX)
  static const int _itemsPerPage = 50;

  /// Batch size for loadMore operations (optimized for smooth scrolling)
  static const int _loadMoreBatchSize = 10;

  /// Maximum attempts for shortfall handling (prevents infinite loops)
  static const int _maxShortfallAttempts = 3;

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  @override
  Future<void> close() {
    _displayedSupportUsersListener?.cancel();
    _stalenessDetectionListener?.cancel();
    return super.close();
  }

  // ============================================================================
  // PUBLIC API METHODS
  // ============================================================================

  /// Initialize support users list with search query and set up real-time listeners
  ///
  /// This is the main entry point that:
  /// - Cleans up existing listeners
  /// - Resets pagination state
  /// - Loads initial data batch
  /// - Sets up targeted real-time updates
  /// - Initializes staleness detection
  void initialize({String? searchQuery}) {
    Log.d(
      '🔄 AdminDataProvider: Initializing with search: "$searchQuery"',
    );
    Log.d('  🗑️ Cancelling old listeners...');
    _cancelListeners();
    Log.d('  🔄 Resetting pagination...');
    _resetPagination();

    Log.d('  🎨 Emitting loading state with new search...');
    emit(
      AdminDataState.initial().copyWith(
        searchQuery: searchQuery,
      ),
    );

    Log.d('  👂 Setting up new listeners...');
    _setupListeners();
  }

  /// Refresh entire support users list and clear staleness indicators
  ///
  /// This performs a full reset:
  /// - Clears hasNewUpdates flag
  /// - Re-initializes with current search query
  /// - Resets pagination to first page
  Future<void> refresh() async {
    Log.d('🔄 AdminDataProvider: Refreshing support users data');

    // Clear new updates flag when refreshing
    emit(state.copyWith(hasNewUpdates: false));

    // Re-initialize with current search query
    initialize(searchQuery: state.searchQuery);
  }

  /// Change search filter and reload data
  ///
  /// Efficiently handles filter changes by performing a clean re-initialization
  void changeFilter({String? searchQuery}) {
    final newSearchQuery = searchQuery;

    Log.d('🎛️ AdminDataProvider: Changing filter');
    Log.d('  🔄 Search query: $newSearchQuery');

    initialize(
      searchQuery: newSearchQuery?.isEmpty == true ? null : newSearchQuery,
    );
  }

  /// Load more support users with advanced duplicate handling and cursor advancement
  ///
  /// **Pagination Strategy:**
  /// - Uses cursor-based pagination with `_lastLoginDateTime`
  /// - Fetches batches of `_loadMoreBatchSize` items from database
  /// - Handles database inconsistencies and duplicate detection gracefully
  ///
  /// **Duplicate Handling Logic:**
  /// 1. **Initial Fetch**: Get next batch using current cursor position
  /// 2. **Process & Deduplicate**: Remove items already in displayed list
  /// 3. **Handle Shortfall**: If unique items < batch size AND we got full batch:
  ///    - Sort ALL processed items (including duplicates) by lastLogin
  ///    - Advance cursor to last item's lastLogin (ensures forward progress)
  ///    - Fetch additional batch and repeat until sufficient unique items
  /// 4. **End Detection**: Stop when partial batch received (< batch size)
  ///
  /// **Cursor Advancement Strategy:**
  /// - Critical insight: Sort ALL items (duplicates + unique) before advancing cursor
  /// - This ensures cursor always moves forward, even with 100% duplicate batches
  /// - Prevents infinite loops while maintaining database consistency
  ///
  /// **State Updates:**
  /// - Merge unique items with existing displayed list
  /// - Update `hasMore` based on partial batch detection
  /// - Adjust `totalCount` if database end reached
  /// - Refresh staleness detection to match new pagination state
  Future<void> loadMore() async {
    if (state.isLoadingMore || !state.hasMore) return;

    Log.d('📄 LoadMore: Loading next $_loadMoreBatchSize support users...');
    emit(state.copyWith(isLoadingMore: true));

    try {
      // Initial fetch - get support users (will be filtered by search in database layer)
      var newSupportUsers = await _databaseRepository.fetchSupportUsers(
        searchQuery: state.searchQuery,
        lastLoginDateTime: _lastLoginDateTime,
        limit: _loadMoreBatchSize,
      );

      if (newSupportUsers.isEmpty) {
        // Handle empty case
        emit(
          state.copyWith(
            isLoadingMore: false,
            hasMore: false,
            totalCount: state.supportUsers.length,
          ),
        );
        return;
      }

      // Deduplicate
      final existingIds = state.supportUsers.map((u) => u.sid).toSet();
      var newUniqueSupportUsers =
          newSupportUsers.where((u) => !existingIds.contains(u.sid)).toList();

      // Handle duplicate shortfall with cursor advancement (max attempts for safety)
      int shortfallAttempts = 0;

      while (newUniqueSupportUsers.length < _loadMoreBatchSize &&
          newSupportUsers.length == _loadMoreBatchSize &&
          shortfallAttempts < _maxShortfallAttempts) {
        shortfallAttempts++;
        Log.d(
          '🔄 Shortfall attempt $shortfallAttempts/$_maxShortfallAttempts: ${newUniqueSupportUsers.length} < $_loadMoreBatchSize, fetching more...',
        );

        // Sort ALL processed items (including duplicates) to advance cursor
        _sortSupportUsersByLogin(newSupportUsers);

        // Update cursor from sorted batch (advances even with duplicates)
        if (newSupportUsers.isNotEmpty) {
          _lastLoginDateTime = newSupportUsers.last.lastLogin;
          Log.d('🎯 Cursor advanced to: $_lastLoginDateTime');
        }

        // Fetch next batch
        final additionalBatch = await _databaseRepository.fetchSupportUsers(
          searchQuery: state.searchQuery,
          lastLoginDateTime: _lastLoginDateTime,
          limit: _loadMoreBatchSize,
        );

        // Check for end conditions
        if (additionalBatch.isEmpty ||
            additionalBatch.length < _loadMoreBatchSize) {
          Log.d('🏁 Reached database end: ${additionalBatch.length} items');
          newSupportUsers = additionalBatch;
          break;
        }

        // Process additional batch
        final allIds = [
          ...existingIds,
          ...newUniqueSupportUsers.map((u) => u.sid),
        ];
        final uniqueFromAdditional =
            additionalBatch.where((u) => !allIds.contains(u.sid)).toList();

        // Add to collections
        newUniqueSupportUsers.addAll(uniqueFromAdditional);
        newSupportUsers = [...newSupportUsers, ...additionalBatch];

        Log.d(
          '📊 Additional: ${additionalBatch.length} processed, ${uniqueFromAdditional.length} unique',
        );
      }

      // Safety check: warn if we hit the attempt limit
      if (shortfallAttempts >= _maxShortfallAttempts) {
        Log.w(
            '⚠️ LoadMore: Reached max shortfall attempts ($_maxShortfallAttempts). '
            'Proceeding with ${newUniqueSupportUsers.length} unique items to prevent infinite loop.');
      }

      // Final merge and sort
      final allSupportUsersDisplayed = [
        ...state.supportUsers,
        ...newUniqueSupportUsers,
      ];
      _sortSupportUsersByLogin(allSupportUsersDisplayed);

      // Update final cursor from displayed items
      if (allSupportUsersDisplayed.isNotEmpty) {
        _lastLoginDateTime = allSupportUsersDisplayed.last.lastLogin;
      }

      // Determine hasMore
      final gotPartialBatch = newSupportUsers.length < _loadMoreBatchSize;
      final reachedExpectedTotal =
          allSupportUsersDisplayed.length >= state.totalCount;
      final hasMoreData = !gotPartialBatch && !reachedExpectedTotal;
      final adjustedTotalCount =
          gotPartialBatch ? allSupportUsersDisplayed.length : state.totalCount;

      Log.d(
        '✅ LoadMore complete: ${newUniqueSupportUsers.length} unique items added',
      );

      emit(
        state.copyWith(
          supportUsers: allSupportUsersDisplayed,
          isLoadingMore: false,
          hasMore: hasMoreData,
          totalCount: adjustedTotalCount,
          lastUpdate: DateTime.now(),
        ),
      );

      // Update listeners
      _updateStalenessListenerCount();
      _displayedSupportUsersListener?.cancel();
      _setupDisplayedSupportUsersListener();
    } catch (error) {
      Log.e('❌ LoadMore error: $error');
      emit(
        state.copyWith(
          isLoadingMore: false,
          error: 'Failed to load more support users: ${error.toString()}',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE SETUP & INITIALIZATION
  // ============================================================================

  /// Core setup method: loads initial data and configures real-time listeners
  ///
  /// Flow:
  /// 1. Load first page of support users + total count
  /// 2. Set up targeted listener for displayed items
  /// 3. Start staleness detection in background
  Future<void> _setupListeners() async {
    await _loadInitialData();

    Log.d(
      '👂 AdminDataProvider: Setting up listeners for search: "${state.searchQuery}"',
    );

    _setupDisplayedSupportUsersListener();
  }

  /// Cancel admin-specific listeners
  ///
  /// Cleans up all active listeners when changing search or closing
  void _cancelListeners() {
    _displayedSupportUsersListener?.cancel();
    _displayedSupportUsersListener = null;
    _stalenessDetectionListener?.cancel();
    _stalenessDetectionListener = null;
  }

  /// Reset pagination cursor for clean state
  ///
  /// Called during initialization to ensure consistent starting state
  void _resetPagination() {
    _lastLoginDateTime = null;
  }

  // ============================================================================
  // PRIVATE DATA LOADING - INITIAL & PAGINATION
  // ============================================================================

  /// Load initial page of support users and total count (one-time fetch)
  ///
  /// This is a clean fetch operation that:
  /// - Gets first page of support users matching current search
  /// - Fetches accurate total count for pagination
  /// - Sets up pagination cursor for future loadMore calls
  ///
  /// Note: No real-time listeners set up here - that's done separately
  Future<void> _loadInitialData() async {
    Log.d(
      '📥 AdminDataProvider: Loading initial support users data (no real-time updates)',
    );
    Log.d('  🔍 Search: "${state.searchQuery}"');

    try {
      // Parallel fetch for optimal performance: support users + count
      final results = await Future.wait([
        _databaseRepository.fetchSupportUsers(
          searchQuery: state.searchQuery,
          limit: _itemsPerPage,
        ),
        _databaseRepository.fetchSupportUsersCount(
          searchQuery: state.searchQuery,
        ),
      ]);

      final supportUsers = results[0] as List<SupportUser>;
      final totalCount = results[1] as int;

      Log.d(
        '✅ AdminDataProvider: Loaded ${supportUsers.length} initial support users, total count: $totalCount',
      );

      // Sort by last login timestamp to match database ordering (newest first)
      _sortSupportUsersByLogin(supportUsers);

      // Set pagination cursor to last item for future loadMore calls
      if (supportUsers.isNotEmpty) {
        _lastLoginDateTime = supportUsers.last.lastLogin;
      }

      // Determine if more pages available by comparing counts
      final hasMoreData = supportUsers.length < totalCount;

      emit(
        state.copyWith(
          supportUsers: supportUsers,
          totalCount: totalCount,
          // Use real database count (only updated on initial load/refresh)
          isLoading: false,
          error: null,
          lastUpdate: DateTime.now(),
          hasMore: hasMoreData,
          // More pages available if we have fewer items than total
          hasNewUpdates:
              false, // Always false on initial load (no staleness yet)
        ),
      );

      // Set up staleness detection after initial data is loaded
      if (supportUsers.isNotEmpty) {
        _setupStalenessDetectionListener();
      }
    } catch (error) {
      Log.e('❌ AdminDataProvider: Error loading initial support users: $error');
      emit(
        state.copyWith(
          isLoading: false,
          error: 'Failed to load support users: $error',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE UTILITY & HELPER METHODS
  // ============================================================================

  /// Sort support users by lastLogin timestamp (newest first)
  ///
  /// Maintains consistency with database ordering for predictable pagination
  void _sortSupportUsersByLogin(List<SupportUser> supportUsers) {
    supportUsers.sort((a, b) {
      return b.lastLogin
          .compareTo(a.lastLogin); // Most recent first (descending)
    });
  }

  /// Set up background staleness detection listener
  ///
  /// This runs a parallel query with the same count as displayed items
  /// to detect when new content becomes available. Only triggers when
  /// the actual content (IDs) differs, not just order changes.
  void _setupStalenessDetectionListener() {
    Log.d(
      '📡 Setting up real-time list listener for staleness detection - search: "${state.searchQuery}"',
    );

    // Use current displayed count or default to initial page size
    final initialCount = state.supportUsers.isNotEmpty
        ? state.supportUsers.length
        : _itemsPerPage;

    _stalenessDetectionListener = _databaseRepository
        .listenSupportUsers(
      searchQuery: state.searchQuery,
      count: initialCount, // Start with initial count
    )
        .listen(
      (realtimeSupportUsers) {
        Log.d(
          '🔍 Real-time list: Received ${realtimeSupportUsers.length} support users (expected: ${state.supportUsers.length})',
        );

        // Only run staleness check if we have items to compare against
        if (state.supportUsers.isNotEmpty) {
          // Apply same processing pipeline as displayed data for fair comparison
          _sortSupportUsersByLogin(realtimeSupportUsers);

          // Compare content by ID sets (order differences don't matter)
          final currentIds = state.supportUsers.map((u) => u.sid).toSet();
          final realtimeIds = realtimeSupportUsers.map((u) => u.sid).toSet();

          final hasChanges =
              !(const SetEquality().equals(currentIds, realtimeIds));

          Log.d(
            '🔍 ID Comparison - Current: ${currentIds.length} items, Realtime: ${realtimeIds.length} items, HasChanges: $hasChanges',
          );

          if (hasChanges) {
            Log.d('🚨 Staleness detected! Setting hasNewUpdates to true');
            emit(
              state.copyWith(
                hasNewUpdates: true,
                lastUpdate: DateTime.now(),
              ),
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ AdminDataProvider: Real-time list listener error: $error');
      },
    );
  }

  /// Update staleness listener to match current pagination state
  ///
  /// Called after loadMore to ensure staleness detection uses the
  /// same item count as currently displayed for accurate comparison
  void _updateStalenessListenerCount() {
    if (state.supportUsers.isNotEmpty) {
      Log.d(
        '🔄 Setting up real-time listener to match displayed items: ${state.supportUsers.length}',
      );

      // Restart listener with updated count to match displayed items
      _stalenessDetectionListener?.cancel();
      _setupStalenessDetectionListener();
    }
  }

  /// Set up targeted real-time listener for currently displayed support users
  ///
  /// This is the core efficiency feature: instead of listening to ALL support users,
  /// we only listen to the specific IDs currently displayed. This provides:
  /// - Real-time updates for visible items only
  /// - Minimal bandwidth and processing
  /// - In-place updates without list reconstruction
  void _setupDisplayedSupportUsersListener() {
    if (state.supportUsers.isEmpty) {
      Log.d('📡 No displayed items to listen for updates');
      return;
    }

    final displayedIds = state.supportUsers.map((u) => u.sid).toList();
    Log.d(
      '📡 Setting up data listener for ${displayedIds.length} displayed IDs: ${displayedIds.take(3).join(", ")}...',
    );

    _displayedSupportUsersListener =
        _databaseRepository.listenSupportUserUpdatesByIDs(displayedIds).listen(
      (updatedSupportUsers) async {
        Log.d(
          '📡 Real-time data: Received ${updatedSupportUsers.length} updated support users for displayed IDs',
        );

        if (updatedSupportUsers.isNotEmpty) {
          // Work on copy to avoid mutating state during processing
          final currentSupportUsers =
              List<SupportUser>.from(state.supportUsers);

          // Find and update matching items in displayed list
          bool hasChanges = false;
          for (final updatedSupportUser in updatedSupportUsers) {
            final existingIndex = currentSupportUsers
                .indexWhere((u) => u.sid == updatedSupportUser.sid);
            if (existingIndex != -1) {
              // Replace existing item with updated data
              currentSupportUsers[existingIndex] = updatedSupportUser;
              hasChanges = true;
              Log.d('📡 Updated support user item: ${updatedSupportUser.sid}');
            }
          }

          if (hasChanges) {
            // Re-sort to maintain timestamp ordering after updates
            _sortSupportUsersByLogin(currentSupportUsers);

            // Keep pagination cursor in sync with displayed items
            if (currentSupportUsers.isNotEmpty) {
              _lastLoginDateTime = currentSupportUsers.last.lastLogin;
            }

            Log.d(
              '📡 Real-time data: Updated displayed items, keeping existing totalCount: ${state.totalCount}',
            );

            emit(
              state.copyWith(
                supportUsers: currentSupportUsers,
                // Preserve totalCount stability - only update on initial load/refresh
                lastUpdate: DateTime.now(),
                // Preserve hasMore state during real-time updates
              ),
            );
          } else {
            Log.d(
              '📡 Real-time data: No matching items found in displayed list',
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ AdminDataProvider: Data listener error: $error');

        // Check if this is a permission-denied error that might be due to expired token
        if (error.toString().contains('permission-denied') ||
            error.toString().contains('Missing or insufficient permissions')) {
          Log.w(
            '🔄 AdminDataProvider: Permission denied error detected, attempting token refresh...',
          );
          _handlePermissionDeniedError();
        } else {
          emit(
            state.copyWith(
              error: 'Failed to update support user data: $error',
            ),
          );
        }
      },
    );
  }

  /// Handle permission denied errors by attempting token refresh and retry
  Future<void> _handlePermissionDeniedError() async {
    try {
      // Get the current user from Firebase Auth
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        Log.e('❌ AdminDataProvider: No current user for token refresh');
        emit(
          state.copyWith(
            error: 'Authentication required. Please sign in again.',
          ),
        );
        return;
      }

      // Attempt to refresh the token
      await currentUser.getIdToken(true); // Force refresh
      Log.d('✅ AdminDataProvider: Token refreshed successfully');

      // Wait a moment for the token to propagate
      await Future.delayed(const Duration(milliseconds: 500));

      // Retry the data listener setup
      Log.d('🔄 AdminDataProvider: Retrying data listener setup...');
      _setupDisplayedSupportUsersListener();
    } catch (e) {
      Log.e('❌ AdminDataProvider: Token refresh failed: $e');
      emit(
        state.copyWith(
          error: 'Authentication failed. Please sign in again.',
        ),
      );
    }
  }
}
