import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/support_user.dart';

/// State specifically for admin panel support users data management
class AdminDataState extends Equatable {
  const AdminDataState({
    required this.supportUsers,
    required this.isLoading,
    required this.isLoadingMore,
    required this.hasMore,
    required this.totalCount,
    required this.lastUpdate,
    this.error,
    this.hasNewUpdates = false,
    this.searchQuery = '',
  });

  /// Current list of support users being displayed
  final List<SupportUser> supportUsers;

  /// Loading states
  final bool isLoading; // Initial load
  final bool isLoadingMore; // Loading next batch
  final bool hasMore; // Can load more support users

  /// Counts and metadata
  final int totalCount; // Total count from Firestore
  final DateTime lastUpdate; // Last time data was updated
  final String? error; // Current error if any
  final bool hasNewUpdates; // New support users available

  /// Current search query applied
  final String searchQuery;

  /// Initial state
  static AdminDataState initial() {
    return AdminDataState(
      supportUsers: const [],
      isLoading: true,
      isLoadingMore: false,
      hasMore: true,
      totalCount: 0,
      lastUpdate: DateTime.now(),
      error: null,
      hasNewUpdates: false,
      searchQuery: '',
    );
  }

  /// Create a copy with updated values
  AdminDataState copyWith({
    List<SupportUser>? supportUsers,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    int? totalCount,
    DateTime? lastUpdate,
    String? error,
    bool? hasNewUpdates,
    String? searchQuery,
  }) {
    return AdminDataState(
      supportUsers: supportUsers ?? this.supportUsers,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      totalCount: totalCount ?? this.totalCount,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error,
      hasNewUpdates: hasNewUpdates ?? this.hasNewUpdates,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [
        supportUsers,
        isLoading,
        isLoadingMore,
        hasMore,
        totalCount,
        lastUpdate,
        error,
        hasNewUpdates,
        searchQuery,
      ];
}
