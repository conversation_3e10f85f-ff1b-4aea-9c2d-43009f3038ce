import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:mevolvesupport/models/segment.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/providers/segment_details/segment_details_state.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

/// SegmentDetailsCubit manages real-time segment details data
/// Following the architecture pattern: only talks to repositories, never Firebase directly
class SegmentDetailsCubit extends Cubit<SegmentDetailsState> {
  SegmentDetailsCubit({
    required DatabaseRepository databaseRepository,
  })  : _databaseRepository = databaseRepository,
        super(SegmentDetailsState.initial());

  final DatabaseRepository _databaseRepository;

  // Real-time listeners
  StreamSubscription<Segment?>? _segmentDataListener;
  StreamSubscription<List<SegmentActivity>>? _segmentActivitiesListener;
  StreamSubscription<List<SupportUser>>? _supportUsersListener;

  /// Select a segment and start real-time listening
  Future<void> selectSegment(String segmentId) async {
    final sw = Stopwatch()..start();

    if (state.selectedSegmentId == segmentId && state.segmentDetails != null) {
      Log.d('🔄 selectSegment: already loaded');
      return;
    }

    await _cancelListeners();
    Log.d('🔄 selectSegment: cancel took ${sw.elapsedMilliseconds}ms');

    // Clear all previous segment data when selecting a new segment
    emit(
      SegmentDetailsState.initial().copyWith(
        selectedSegmentId: segmentId,
        isLoading: true,
        error: null,
        lastUpdate: DateTime.now(),
      ),
    );

    try {
      // Set up real-time segment data listener
      _segmentDataListener =
          _databaseRepository.listenSegment(id: segmentId).listen(
        (segmentData) {
          Log.d(
            '🔄 selectSegment: segment data received after ${sw.elapsedMilliseconds}ms',
          );
          if (segmentData != null) {
            emit(
              state.copyWith(
                segmentDetails: segmentData,
                isLoading: false,
                lastUpdate: DateTime.now(),
                error: null,
              ),
            );

            // Load all data for the segment
            _loadAllSegmentData(segmentId);
          } else {
            // Segment not found - clear all data and show error
            emit(
              SegmentDetailsState.initial().copyWith(
                selectedSegmentId: segmentId,
                segmentDetails: null,
                error: 'Segment not found',
                isLoading: false,
                lastUpdate: DateTime.now(),
              ),
            );
          }
        },
        onError: (error) {
          Log.e('SegmentDetailsCubit: Error listening to segment data: $error');
          emit(
            SegmentDetailsState.initial().copyWith(
              selectedSegmentId: segmentId,
              segmentDetails: null,
              error: 'Failed to load segment data: ${error.toString()}',
              isLoading: false,
              lastUpdate: DateTime.now(),
            ),
          );
        },
      );
    } catch (error) {
      Log.e('SegmentDetailsCubit: Error setting up segment listener: $error');
      emit(
        SegmentDetailsState.initial().copyWith(
          selectedSegmentId: segmentId,
          segmentDetails: null,
          error: 'Failed to set up segment listener: ${error.toString()}',
          isLoading: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Load all segment data concurrently
  Future<void> _loadAllSegmentData(String segmentId) async {
    final sw = Stopwatch()..start();

    // Set loading state
    emit(
      state.copyWith(
        isLoadingActivities: true,
        lastUpdate: DateTime.now(),
      ),
    );

    // Load activities and support users
    _loadSegmentActivities(segmentId);
    _loadSupportUsers();

    Log.d(
      '🔄 _loadAllSegmentData: all concurrent loading started in ${sw.elapsedMilliseconds}ms',
    );
  }

  /// Load segment activities - Real-time listeners
  Future<void> _loadSegmentActivities(String segmentId) async {
    final sw = Stopwatch()..start();
    try {
      // Set up real-time activities listener
      await _segmentActivitiesListener?.cancel();
      _segmentActivitiesListener = _databaseRepository
          .listenSegmentActivities(
        segmentId: segmentId,
      )
          .listen(
        (activities) {
          emit(
            state.copyWith(
              segmentActivities: activities,
              isLoadingActivities: false,
              lastUpdate: DateTime.now(),
            ),
          );
        },
        onError: (error) {
          Log.e('SegmentDetailsCubit: Error listening to activities: $error');
          emit(
            state.copyWith(
              isLoadingActivities: false,
              lastUpdate: DateTime.now(),
            ),
          );
        },
      );
      Log.d('🔄 _loadSegmentActivities: ${sw.elapsedMilliseconds}ms');
    } catch (error) {
      Log.e(
        'SegmentDetailsCubit: Error setting up activities listener: $error',
      );

      // Mark activities loading as complete even on error
      emit(
        state.copyWith(
          isLoadingActivities: false,
          lastUpdate: DateTime.now(),
        ),
      );
    }
  }

  /// Load support users - Real-time listener
  Future<void> _loadSupportUsers() async {
    final sw = Stopwatch()..start();
    try {
      // Set up real-time support users listener
      await _supportUsersListener?.cancel();
      _supportUsersListener = _databaseRepository.listenSupportUsers().listen(
        (supportUsers) {
          // Convert list to map for easy lookup
          final usersMap = <String, SupportUser>{};
          for (final user in supportUsers) {
            usersMap[user.sid] = user;
          }

          emit(
            state.copyWith(
              supportUsers: usersMap,
              lastUpdate: DateTime.now(),
            ),
          );
        },
        onError: (error) {
          Log.e(
            'SegmentDetailsCubit: Error listening to support users: $error',
          );
        },
      );
      Log.d('🔄 _loadSupportUsers: ${sw.elapsedMilliseconds}ms');
    } catch (error) {
      Log.e(
        'SegmentDetailsCubit: Error setting up support users listener: $error',
      );
    }
  }

  /// Refresh segment count
  Future<void> refreshSegmentCount() async {
    if (state.selectedSegmentId == null || state.segmentDetails == null) return;

    try {
      final updatedCount = await _databaseRepository.refreshSegmentUserCount(
        segmentId: state.selectedSegmentId!,
        segmentName: state.segmentDetails!.name,
      );

      // Update local state with new count
      if (state.segmentDetails != null) {
        final updatedSegment = Segment(
          id: state.segmentDetails!.id,
          name: state.segmentDetails!.name,
          purpose: state.segmentDetails!.purpose,
          usersCount: updatedCount,
          cloudUpdatedAt: state.segmentDetails!.cloudUpdatedAt,
          deletedAt: state.segmentDetails!.deletedAt,
          createdAt: state.segmentDetails!.createdAt,
          createdBy: state.segmentDetails!.createdBy,
          updatedBy: state.segmentDetails!.updatedBy,
        );

        emit(
          state.copyWith(
            segmentDetails: updatedSegment,
            lastUpdate: DateTime.now(),
          ),
        );
      }
    } catch (error) {
      Log.e('SegmentDetailsCubit: Error refreshing segment count: $error');
    }
  }

  /// Clear selected segment
  void clearSegment() {
    _cancelListeners();
    emit(state.clearSegment());
  }

  /// Refresh segment data - forces a fresh fetch
  Future<void> refreshSegmentData() async {
    if (state.selectedSegmentId == null) return;

    final segmentId = state.selectedSegmentId!;

    // Cancel current listener to force refresh
    await _cancelListeners();

    // Re-select the same segment to trigger fresh data load
    await selectSegment(segmentId);
  }

  /// Cancel all active listeners
  Future<void> _cancelListeners() async {
    await _segmentDataListener?.cancel();
    await _segmentActivitiesListener?.cancel();
    await _supportUsersListener?.cancel();

    _segmentDataListener = null;
    _segmentActivitiesListener = null;
    _supportUsersListener = null;
  }

  @override
  Future<void> close() async {
    await _cancelListeners();
    return super.close();
  }
}
