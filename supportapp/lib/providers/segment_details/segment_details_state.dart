import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/segment.dart';
import 'package:mevolvesupport/models/support_user.dart';

/// State specifically for segment details real-time management
/// Manages data for segment details page
class SegmentDetailsState extends Equatable {
  const SegmentDetailsState({
    required this.selectedSegmentId,
    required this.segmentDetails,
    required this.isLoading,
    required this.lastUpdate,
    this.error,
    this.segmentActivities = const [],
    this.isLoadingActivities = false,
    this.supportUsers = const {},
  });

  /// Currently selected segment ID
  final String? selectedSegmentId;

  /// Real-time segment details data
  final Segment? segmentDetails;

  /// Loading state for initial segment load
  final bool isLoading;

  /// Individual loading state for activities
  final bool isLoadingActivities;

  /// Last time data was updated
  final DateTime lastUpdate;

  /// Current error if any
  final String? error;

  /// Segment activities
  final List<SegmentActivity> segmentActivities;

  /// Support users map for activity creators
  final Map<String, SupportUser> supportUsers;

  /// Initial state
  static SegmentDetailsState initial() {
    return SegmentDetailsState(
      selectedSegmentId: null,
      segmentDetails: null,
      isLoading: false,
      lastUpdate: DateTime.now(),
      error: null,
      segmentActivities: const [],
      isLoadingActivities: false,
      supportUsers: const {},
    );
  }

  /// Create a copy with updated values
  SegmentDetailsState copyWith({
    String? selectedSegmentId,
    Segment? segmentDetails,
    bool? isLoading,
    bool? isLoadingActivities,
    DateTime? lastUpdate,
    String? error,
    List<SegmentActivity>? segmentActivities,
    Map<String, SupportUser>? supportUsers,
  }) {
    return SegmentDetailsState(
      selectedSegmentId: selectedSegmentId ?? this.selectedSegmentId,
      segmentDetails: segmentDetails ?? this.segmentDetails,
      isLoading: isLoading ?? this.isLoading,
      isLoadingActivities: isLoadingActivities ?? this.isLoadingActivities,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error,
      segmentActivities: segmentActivities ?? this.segmentActivities,
      supportUsers: supportUsers ?? this.supportUsers,
    );
  }

  /// Clear segment details (when no segment is selected)
  SegmentDetailsState clearSegment() {
    return SegmentDetailsState(
      selectedSegmentId: null,
      segmentDetails: null,
      isLoading: false,
      lastUpdate: DateTime.now(),
      error: null,
      segmentActivities: const [],
      isLoadingActivities: false,
      supportUsers: const {},
    );
  }

  @override
  List<Object?> get props => [
        selectedSegmentId,
        segmentDetails,
        isLoading,
        isLoadingActivities,
        lastUpdate,
        error,
        segmentActivities,
        supportUsers,
      ];
}
