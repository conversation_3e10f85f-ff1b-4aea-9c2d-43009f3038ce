import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/chats_count.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/utilities/nullable.dart';

/// State specifically for chat data management
class ChatDataState extends Equatable {
  const ChatDataState({
    required this.chatUsers,
    required this.isLoading,
    required this.isLoadingMore,
    required this.hasMore,
    required this.totalCount,
    required this.totalCounts,
    required this.lastUpdate,
    this.error,
    this.hasNewUpdates = false,
    this.currentStatus = ChatStatus.notReplied,
    this.supportPersonFilter,
  });

  /// Current list of chat users being displayed
  final List<ChatUser> chatUsers;

  /// Loading states
  final bool isLoading; // Initial load
  final bool isLoadingMore; // Loading next batch
  final bool hasMore; // Can load more chats

  /// Counts and metadata
  final int totalCount; // Filtered count for current view
  final ChatsCount totalCounts; // Total counts for all statuses (unfiltered)
  final DateTime lastUpdate; // Last time data was updated
  final String? error; // Current error if any
  final bool hasNewUpdates; // New chats available (count increased)

  /// Current filter state
  final ChatStatus currentStatus; // Current chat status filter
  final String? supportPersonFilter; // Filter by support person (uid or 'all')

  /// Initial state
  static ChatDataState initial() {
    return ChatDataState(
      chatUsers: const [],
      isLoading: true, // Set to true like we did for users
      isLoadingMore: false,
      hasMore: true,
      totalCount: 0,
      totalCounts:
          const ChatsCount(notReplied: 0, replied: 0, clarify: 0, resolved: 0),
      lastUpdate: DateTime.now(),
      error: null,
      hasNewUpdates: false,
      currentStatus: ChatStatus.notReplied, // Default status
      supportPersonFilter: null, // null means "All"
    );
  }

  /// Create a copy with updated values
  ChatDataState copyWith({
    List<ChatUser>? chatUsers,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    int? totalCount,
    ChatsCount? totalCounts,
    DateTime? lastUpdate,
    String? error,
    bool? hasNewUpdates,
    ChatStatus? currentStatus,
    Nullable<String?>? supportPersonFilter,
  }) {
    return ChatDataState(
      chatUsers: chatUsers ?? this.chatUsers,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      totalCount: totalCount ?? this.totalCount,
      totalCounts: totalCounts ?? this.totalCounts,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error,
      hasNewUpdates: hasNewUpdates ?? this.hasNewUpdates,
      currentStatus: currentStatus ?? this.currentStatus,
      supportPersonFilter: supportPersonFilter == null
          ? this.supportPersonFilter
          : supportPersonFilter.value,
    );
  }

  @override
  List<Object?> get props => [
        chatUsers,
        isLoading,
        isLoadingMore,
        hasMore,
        totalCount,
        totalCounts,
        lastUpdate,
        error,
        hasNewUpdates,
        currentStatus,
        supportPersonFilter,
      ];
}
