import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mevolvesupport/constants/app_constant.dart';
import 'package:mevolvesupport/utilities/logger.dart';

class FirebaseAuthenticationRepository {
  FirebaseAuthenticationRepository({
    FirebaseAuth? firebaseAuth,
    GoogleSignIn? googleSignIn,
  })  : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _googleSignIn = googleSignIn ?? _createGoogleSignIn();

  final FirebaseAuth _firebaseAuth;
  final GoogleSignIn _googleSignIn;
  bool isNewUser = false;

  static GoogleSignIn _createGoogleSignIn() {
    return GoogleSignIn(
      scopes: ['profile', 'email'],
    );
  }

  Future<void> useEmulator() async {
    const host = FirebaseConstants.firebaseLocalHostWebIP;
    await _firebaseAuth.useAuthEmulator(
      host,
      FirebaseConstants.firebaseAuthenticatorPort,
    );
  }

  Stream<User?> get user {
    return _firebaseAuth.authStateChanges();
  }

  User? get currentUser => _firebaseAuth.currentUser;

  FirebaseAuth get firebaseAuthInstance => _firebaseAuth;

  Future<User?> logInWithGoogle() async {
    try {
      Log.d('Starting google sign in');

      if (kIsWeb) {
        // Web implementation
        Log.d('Using web implementation');
        GoogleAuthProvider googleProvider = GoogleAuthProvider();
        final user =
            await FirebaseAuth.instance.signInWithPopup(googleProvider);
        Log.d('Web sign-in successful, user: ${user.user?.email}');
        return user.user;
      } else {
        // Mobile implementation
        Log.d('Using mobile implementation');
        final googleUser = await _googleSignIn.signIn();
        if (googleUser == null) {
          Log.d('User canceled Google sign-in');
          return null;
        }

        Log.d('Google user obtained: ${googleUser.email}');
        final googleAuth = await googleUser.authentication;
        Log.d('Google authentication obtained');

        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );
        Log.d('Firebase credential created');

        final userCredential =
            await _firebaseAuth.signInWithCredential(credential);
        Log.d(
          'Firebase sign-in successful, user: ${userCredential.user?.email}',
        );

        // Check if user has authorized email domain
        final email = userCredential.user?.email;
        if (email != null && !email.endsWith('@realtime-innovations.com')) {
          Log.w('Unauthorized email domain: $email. Signing out.');
          await logOut();
          throw const LogInWithGoogleFailure(
            'Only @realtime-innovations.com email addresses are allowed.',
          );
        }

        return userCredential.user;
      }
    } on FirebaseAuthException catch (e) {
      Log.e(
        'FirebaseAuthException during Google sign-in: ${e.code} - ${e.message}',
      );
      throw LogInWithGoogleFailure.fromCode(e.code);
    } catch (e) {
      Log.e('Unexpected error during Google sign-in: $e');
      throw const LogInWithGoogleFailure();
    }
  }

  Future<void> deleteFirebaseAccount() async {
    await _firebaseAuth.currentUser?.delete();
  }

  Future<void> refreshUserToken() async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) {
        throw const LogInWithGoogleFailure('No authenticated user found');
      }

      Log.d('🔄 FirebaseAuth: Refreshing user token...');
      await currentUser.getIdToken(true);
      Log.d('✅ FirebaseAuth: Token refreshed successfully');
    } catch (e) {
      Log.e('❌ FirebaseAuth: Error refreshing token: $e');
      rethrow;
    }
  }

  /// Check if current token is still valid (not expired)
  Future<bool> isTokenValid() async {
    try {
      final currentUser = _firebaseAuth.currentUser;
      if (currentUser == null) return false;

      final tokenResult = await currentUser.getIdTokenResult();
      final expirationTime = tokenResult.expirationTime;
      final now = DateTime.now();

      // Consider token invalid if it expires within the next 5 minutes
      final isValid = expirationTime != null &&
          expirationTime.isAfter(now.add(const Duration(minutes: 5)));

      Log.d(
        '🔍 FirebaseAuth: Token validity check - Valid: $isValid, Expires: $expirationTime',
      );
      return isValid;
    } catch (e) {
      Log.e('❌ FirebaseAuth: Error checking token validity: $e');
      return false;
    }
  }

  Future<void> reAuthenticateUserWithGoogle() async {
    if (kIsWeb) {
      // Web implementation
      GoogleAuthProvider googleProvider = GoogleAuthProvider();
      final login = await FirebaseAuth.instance.signInWithPopup(googleProvider);
      if (login.credential != null) {
        final user = _firebaseAuth.currentUser;
        await user?.reauthenticateWithCredential(login.credential!);
      }
    } else {
      // Mobile implementation
      final googleUser = await _googleSignIn.signIn();
      if (googleUser != null) {
        final googleAuth = await googleUser.authentication;
        final credential = GoogleAuthProvider.credential(
          accessToken: googleAuth.accessToken,
          idToken: googleAuth.idToken,
        );
        final user = _firebaseAuth.currentUser;
        await user?.reauthenticateWithCredential(credential);
      }
    }
  }

  Future<void> logOut() async {
    try {
      await Future.wait([
        _firebaseAuth.signOut(),
        _googleSignIn.signOut(),
      ]);
    } catch (_) {
      throw LogOutFailure();
    }
  }
}

class LogInWithGoogleFailure implements Exception {
  const LogInWithGoogleFailure([
    this.message = 'An unknown exception occurred.',
  ]);

  factory LogInWithGoogleFailure.fromCode(String code) {
    switch (code) {
      case 'account-exists-with-different-credential':
        return const LogInWithGoogleFailure(
          'Account exists with different credentials.',
        );
      case 'invalid-credential':
        return const LogInWithGoogleFailure(
          'The credential received is malformed or has expired.',
        );
      case 'operation-not-allowed':
        return const LogInWithGoogleFailure(
          'Operation is not allowed.  Please contact support.',
        );
      case 'user-disabled':
        return const LogInWithGoogleFailure(
          'This user has been disabled. Please contact support for help.',
        );
      case 'user-not-found':
        return const LogInWithGoogleFailure(
          'Email is not found, please create an account.',
        );
      case 'wrong-password':
        return const LogInWithGoogleFailure(
          'Incorrect password, please try again.',
        );
      case 'invalid-verification-code':
        return const LogInWithGoogleFailure(
          'The credential verification code received is invalid.',
        );
      case 'invalid-verification-id':
        return const LogInWithGoogleFailure(
          'The credential verification ID received is invalid.',
        );
      default:
        return const LogInWithGoogleFailure();
    }
  }

  final String message;
}

class LogOutFailure implements Exception {}
