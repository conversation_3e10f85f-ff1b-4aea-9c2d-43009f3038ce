import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/user_metadata.dart';

/// State specifically for users data management
class UsersDataState extends Equatable {
  const UsersDataState({
    required this.users,
    required this.isLoading,
    required this.isLoadingMore,
    required this.hasMore,
    required this.totalCount,
    required this.lastUpdate,
    this.error,
    this.hasNewUpdates = false,
    this.filters = const {},
  });

  /// Current list of users being displayed
  final List<UserMetaData> users;

  /// Loading states
  final bool isLoading; // Initial load
  final bool isLoadingMore; // Loading next batch
  final bool hasMore; // Can load more users

  /// Counts and metadata
  final int totalCount; // Real-time total count from Firestore
  final DateTime lastUpdate; // Last time data was updated
  final String? error; // Current error if any
  final bool hasNewUpdates; // New users available (count increased)

  /// Current filters applied (UI format with indices)
  final Map<String, dynamic> filters;

  /// Initial state with default subscription filter
  static UsersDataState initial() {
    return UsersDataState(
      users: const [],
      isLoading: true,
      isLoadingMore: false,
      hasMore: true,
      totalCount: 0,
      lastUpdate: DateTime.now(),
      error: null,
      hasNewUpdates: false,
      filters: const {
        'subscriptionState': [1], // Default filter: Subscribed users only
      },
    );
  }

  /// Create a copy with updated values
  UsersDataState copyWith({
    List<UserMetaData>? users,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    int? totalCount,
    DateTime? lastUpdate,
    String? error,
    bool? hasNewUpdates,
    Map<String, dynamic>? filters,
  }) {
    return UsersDataState(
      users: users ?? this.users,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      totalCount: totalCount ?? this.totalCount,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error ?? this.error,
      hasNewUpdates: hasNewUpdates ?? this.hasNewUpdates,
      filters: filters ?? this.filters,
    );
  }

  @override
  List<Object?> get props => [
        users,
        isLoading,
        isLoadingMore,
        hasMore,
        totalCount,
        lastUpdate,
        error,
        hasNewUpdates,
        filters,
      ];
}
