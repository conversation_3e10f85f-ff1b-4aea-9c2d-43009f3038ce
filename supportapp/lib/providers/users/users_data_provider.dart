import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolvesupport/enums/allusers_filter_types.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/providers/users/users_data_state.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

/// **UsersDataProvider - Advanced Real-time Users List Manager**
///
/// This provider implements a sophisticated users list management system with:
/// - Real-time updates for displayed items only (performance optimized)
/// - Intelligent staleness detection to notify when list needs refresh
/// - Cursor-based pagination with smart totalCount adjustment
/// - Deleted user handling with status indicators
/// - Flexible filtering system for user attributes
///
/// ## 🏗️ ARCHITECTURE OVERVIEW
///
/// ### Data Flow Patterns
///
/// **1. Initial Load Flow**
/// ```
/// initialize() → _setupListeners() → _loadInitialData() → _setupDisplayedUsersListener()
///                                 ↓
///                          Sets up staleness detection after initial load
/// ```
/// - Fetches first page of users + total count
/// - Sets up targeted real-time listeners
/// - Initializes staleness detection
///
/// **2. Real-time Update Flow**
/// ```
/// Firestore Change → listenUserUpdatesByUIDs() → Update in-place → Re-sort → Emit
///                                             ↓
///                                      Preserve totalCount
/// ```
/// - Only listens to currently displayed user UIDs (efficient)
/// - Updates existing items without changing list structure
/// - Maintains sort order and pagination state
///
/// **3. Pagination Flow**
/// ```
/// loadMore() → Fetch from cursor → Filter duplicates → Append → Update hasMore
///            ↓
///         Smart totalCount adjustment if no more items available
/// ```
/// - Uses last displayed item's createdAt as cursor
/// - Handles database inconsistencies gracefully
///
/// **4. Staleness Detection Flow**
/// ```
/// Background Listener → Compare UID sets → Set hasNewUpdates flag
///                    ↓
///               Only triggers on actual content differences
/// ```
/// - Runs parallel staleness check with same count as displayed
/// - Ignores order changes, only detects content differences
///
/// ## 📊 STATE MANAGEMENT
///
/// ### Core State Properties
/// - **`users`**: Currently displayed users list (paginated)
/// - **`totalCount`**: Stable count from initial fetch (only updated on refresh)
/// - **`hasMore`**: Can load more items (displayed < total)
/// - **`hasNewUpdates`**: Content staleness flag
/// - **`filters`**: Active filters applied (UI format with indices)
///
/// ### Real-time Listeners
/// - **Displayed Items**: `_displayedUsersListener` (UID-based)
/// - **Staleness Detection**: `_stalenessDetectionListener` (background)
///
/// ## 🎯 PUBLIC API
///
/// **Core Operations**
/// - `initialize(filters)` - Load initial data with filters
/// - `loadMore()` - Load next batch of users
/// - `refresh()` - Reload fresh data, clear staleness
/// - `changeFilters(filters)` - Change filters and reload
///
/// ## ⚡ PERFORMANCE OPTIMIZATIONS
///
/// - **Targeted Updates**: Only listen to displayed UIDs
/// - **Cursor Pagination**: Efficient database queries
/// - **Smart Deduplication**: Prevents duplicate items
/// - **Stable Counts**: totalCount preserved during updates
/// - **Background Staleness**: Non-blocking freshness checks
class UsersDataProvider extends Cubit<UsersDataState> {
  UsersDataProvider({
    required DatabaseRepository databaseRepository,
  })  : _databaseRepository = databaseRepository,
        super(UsersDataState.initial());

  final DatabaseRepository _databaseRepository;

  // ============================================================================
  // PRIVATE STATE VARIABLES
  // ============================================================================

  // --- Real-time Stream Subscriptions ---

  /// Listener for displayed user items updates (efficient UID-based targeting)
  StreamSubscription? _displayedUsersListener;

  /// Listener for staleness detection (background monitoring, non-blocking)
  StreamSubscription? _stalenessDetectionListener;

  // --- Pagination & Cursor State ---

  /// Pagination cursor using createdAt (consistent with Firestore ordering)
  DateTime? _lastCreatedAt;

  /// Items per page for initial load (balanced performance vs UX)
  static const int _itemsPerPage = 10;

  /// Batch size for loadMore operations (optimized for smooth scrolling)
  static const int _loadMoreBatchSize = 10;

  /// Maximum attempts for shortfall handling (prevents infinite loops)
  static const int _maxShortfallAttempts = 5;

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  @override
  Future<void> close() {
    _displayedUsersListener?.cancel();
    _stalenessDetectionListener?.cancel();
    return super.close();
  }

  // ============================================================================
  // PUBLIC API METHODS
  // ============================================================================

  /// Initialize users list with filters and set up real-time listeners
  ///
  /// This is the main entry point that:
  /// - Cleans up existing listeners
  /// - Resets pagination state
  /// - Loads initial data batch
  /// - Sets up targeted real-time updates
  /// - Initializes staleness detection
  void initialize({Map<String, dynamic>? filters}) {
    Log.d(
      '🔄 UsersDataProvider: Initializing with filters: $filters',
    );
    Log.d('  🗑️ Cancelling old listeners...');
    _cancelListeners();
    Log.d('  🔄 Resetting pagination...');
    _resetPagination();

    Log.d('  🎨 Emitting loading state with new filters...');
    emit(
      UsersDataState.initial().copyWith(
        filters: filters,
      ),
    );

    Log.d('  👂 Setting up new listeners...');
    _setupListeners();
  }

  /// Refresh entire users list and clear staleness indicators
  ///
  /// This performs a full reset:
  /// - Clears hasNewUpdates flag
  /// - Re-initializes with current filters
  /// - Resets pagination to first page
  Future<void> refresh() async {
    Log.d('🔄 UsersDataProvider: Refreshing users data');

    // Clear new updates flag when refreshing
    emit(state.copyWith(hasNewUpdates: false));

    // Re-initialize with current filters
    initialize(filters: state.filters);
  }

  /// Change filters and reload data
  ///
  /// Efficiently handles filter changes by performing a clean re-initialization
  void changeFilters(Map<String, dynamic> filters) {
    Log.d('🎛️ UsersDataProvider: Changing filters');
    Log.d('  🔄 OLD filters: ${state.filters}');
    Log.d('  ✨ NEW filters: $filters');

    initialize(filters: filters);
  }

  /// Load more users with advanced duplicate handling and cursor advancement
  ///
  /// **Pagination Strategy:**
  /// - Uses cursor-based pagination with `_lastCreatedAt`
  /// - Fetches batches of `_loadMoreBatchSize` items from database
  /// - Handles database inconsistencies and duplicate detection gracefully
  ///
  /// **Duplicate Handling Logic:**
  /// 1. **Initial Fetch**: Get next batch using current cursor position
  /// 2. **Process & Deduplicate**: Remove items already in displayed list
  /// 3. **Handle Shortfall**: If unique items < batch size AND we got full batch:
  ///    - Sort ALL processed items (including duplicates) by createdAt
  ///    - Advance cursor to last item's createdAt (ensures forward progress)
  ///    - Fetch additional batch and repeat until sufficient unique items
  /// 4. **End Detection**: Stop when partial batch received (< batch size)
  ///
  /// **Cursor Advancement Strategy:**
  /// - Critical insight: Sort ALL items (duplicates + unique) before advancing cursor
  /// - This ensures cursor always moves forward, even with 100% duplicate batches
  /// - Prevents infinite loops while maintaining database consistency
  ///
  /// **State Updates:**
  /// - Merge unique items with existing displayed list
  /// - Update `hasMore` based on partial batch detection
  /// - Adjust `totalCount` if database end reached
  /// - Refresh staleness detection to match new pagination state
  Future<void> loadMore() async {
    if (state.isLoadingMore || !state.hasMore) return;

    Log.d('📄 LoadMore: Loading next $_loadMoreBatchSize users...');
    emit(state.copyWith(isLoadingMore: true));

    try {
      // Convert filters for database use
      final databaseFilters = _convertFiltersForDatabase(state.filters);

      // Initial fetch
      var newUsers = await _databaseRepository.fetchUsersFromFirestore(
        filters: databaseFilters,
        startAfter: _lastCreatedAt,
        limit: _loadMoreBatchSize,
      );

      if (newUsers.isEmpty) {
        // Handle empty case
        emit(
          state.copyWith(
            isLoadingMore: false,
            hasMore: false,
            totalCount: state.users.length,
          ),
        );
        return;
      }

      // Deduplicate
      var processedUsers = List<UserMetaData>.from(newUsers);
      final existingUids = state.users.map((u) => u.uid).toSet();
      var newUniqueUsers =
          processedUsers.where((u) => !existingUids.contains(u.uid)).toList();

      // Handle duplicate shortfall with cursor advancement (max attempts for safety)
      int shortfallAttempts = 0;

      while (newUniqueUsers.length < _loadMoreBatchSize &&
          newUsers.length == _loadMoreBatchSize &&
          shortfallAttempts < _maxShortfallAttempts) {
        shortfallAttempts++;
        Log.d(
          '🔄 Shortfall attempt $shortfallAttempts/$_maxShortfallAttempts: ${newUniqueUsers.length} < $_loadMoreBatchSize, fetching more...',
        );

        // Sort ALL processed items (including duplicates) to advance cursor
        _sortUsersByCreatedAt(processedUsers);

        // Update cursor from sorted batch (advances even with duplicates)
        if (processedUsers.isNotEmpty) {
          _lastCreatedAt = processedUsers.last.userInfo.createdAt;
          Log.d('🎯 Cursor advanced to: $_lastCreatedAt');
        }

        // Fetch next batch
        final additionalBatch =
            await _databaseRepository.fetchUsersFromFirestore(
          filters: databaseFilters,
          startAfter: _lastCreatedAt,
          limit: _loadMoreBatchSize,
        );

        // Check for end conditions
        if (additionalBatch.isEmpty ||
            additionalBatch.length < _loadMoreBatchSize) {
          Log.d('🏁 Reached database end: ${additionalBatch.length} items');
          newUsers = additionalBatch;
          break;
        }

        // Process additional batch
        final allUids = [...existingUids, ...newUniqueUsers.map((u) => u.uid)];
        final uniqueFromAdditional =
            additionalBatch.where((u) => !allUids.contains(u.uid)).toList();

        // Add to collections
        newUniqueUsers.addAll(uniqueFromAdditional);
        processedUsers = [...processedUsers, ...additionalBatch];
        newUsers = additionalBatch;

        Log.d(
          '📊 Additional: ${additionalBatch.length} processed, ${uniqueFromAdditional.length} unique',
        );
      }

      // Safety check: warn if we hit the attempt limit
      if (shortfallAttempts >= _maxShortfallAttempts) {
        Log.w(
            '⚠️ LoadMore: Reached max shortfall attempts ($_maxShortfallAttempts). '
            'Proceeding with ${newUniqueUsers.length} unique items to prevent infinite loop.');
      }

      // Final merge and sort
      final allUsers = [...state.users, ...newUniqueUsers];
      _sortUsersByCreatedAt(allUsers);

      // Update final cursor from displayed items
      if (allUsers.isNotEmpty) {
        _lastCreatedAt = allUsers.last.userInfo.createdAt;
      }

      // Determine hasMore
      final gotPartialBatch = newUsers.length < _loadMoreBatchSize;
      final reachedExpectedTotal = allUsers.length >= state.totalCount;
      final hasMoreData = !gotPartialBatch && !reachedExpectedTotal;
      final adjustedTotalCount =
          gotPartialBatch ? allUsers.length : state.totalCount;

      Log.d('✅ LoadMore complete: ${newUniqueUsers.length} unique items added');

      emit(
        state.copyWith(
          users: allUsers,
          isLoadingMore: false,
          hasMore: hasMoreData,
          totalCount: adjustedTotalCount,
          lastUpdate: DateTime.now(),
        ),
      );

      // Update listeners
      _updateStalenessListenerCount();
      _displayedUsersListener?.cancel();
      _setupDisplayedUsersListener();
    } catch (error) {
      Log.e('❌ LoadMore error: $error');
      emit(
        state.copyWith(
          isLoadingMore: false,
          error: 'Failed to load more users: ${error.toString()}',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE SETUP & INITIALIZATION
  // ============================================================================

  /// Core setup method: loads initial data and configures real-time listeners
  ///
  /// Flow:
  /// 1. Load first page of users + total count
  /// 2. Set up targeted listener for displayed items
  /// 3. Start staleness detection in background
  Future<void> _setupListeners() async {
    await _loadInitialData();

    Log.d(
      '👂 UsersDataProvider: Setting up listeners for filters: ${state.filters}',
    );

    _setupDisplayedUsersListener();
  }

  /// Cancel user-specific listeners
  ///
  /// Cleans up all active listeners when changing filters or closing
  void _cancelListeners() {
    _displayedUsersListener?.cancel();
    _displayedUsersListener = null;
    _stalenessDetectionListener?.cancel();
    _stalenessDetectionListener = null;
  }

  /// Reset pagination cursor for clean state
  ///
  /// Called during initialization to ensure consistent starting state
  void _resetPagination() {
    _lastCreatedAt = null;
  }

  // ============================================================================
  // PRIVATE DATA LOADING - INITIAL & PAGINATION
  // ============================================================================

  /// Load initial page of users and total count (one-time fetch)
  ///
  /// This is a clean fetch operation that:
  /// - Gets first page of users matching current filters
  /// - Fetches accurate total count for pagination
  /// - Sets up pagination cursor for future loadMore calls
  ///
  /// Note: No real-time listeners set up here - that's done separately
  Future<void> _loadInitialData() async {
    Log.d(
      '📥 UsersDataProvider: Loading initial users data (no real-time updates)',
    );
    Log.d('  📋 Filters: ${state.filters}');

    try {
      // Convert filters for database use
      final databaseFilters = _convertFiltersForDatabase(state.filters);

      // Parallel fetch for optimal performance: users + count
      final results = await Future.wait([
        _databaseRepository.fetchUsersFromFirestore(
          filters: databaseFilters,
          limit: _itemsPerPage,
        ),
        _databaseRepository.fetchUsersCount(
          filters: databaseFilters,
        ),
      ]);

      final users = results[0] as List<UserMetaData>;
      final totalCount = results[1] as int;

      Log.d(
        '✅ UsersDataProvider: Loaded ${users.length} initial users, total count: $totalCount',
      );

      // Sort by createdAt to match database ordering (newest first)
      _sortUsersByCreatedAt(users);

      // Set pagination cursor to last item for future loadMore calls
      if (users.isNotEmpty) {
        _lastCreatedAt = users.last.userInfo.createdAt;
      }

      // Determine if more pages available by comparing counts
      final hasMoreData = users.length < totalCount;

      emit(
        state.copyWith(
          users: users,
          totalCount: totalCount,
          // Use real database count (only updated on initial load/refresh)
          isLoading: false,
          error: null,
          lastUpdate: DateTime.now(),
          hasMore: hasMoreData,
          // More pages available if we have fewer items than total
          hasNewUpdates:
              false, // Always false on initial load (no staleness yet)
        ),
      );

      // Set up staleness detection after initial data is loaded
      if (users.isNotEmpty) {
        _setupStalenessDetectionListener();
      }
    } catch (error) {
      Log.e('❌ UsersDataProvider: Error loading initial users: $error');
      emit(
        state.copyWith(
          isLoading: false,
          error: 'Failed to load users: $error',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE UTILITY & HELPER METHODS
  // ============================================================================

  /// Convert UI filter values to database query values
  Map<String, dynamic> _convertFiltersForDatabase(
    Map<String, dynamic> uiFilters,
  ) {
    Map<String, dynamic> databaseFilters = {};

    for (var item in uiFilters.entries) {
      final filterType = AllUsersFilterType.values.byName(item.key);

      if (item.value is int) {
        final optionValue = filterType.getTypeOptions()[item.value].value;
        if (optionValue != null) {
          databaseFilters[item.key] = optionValue;
        }
      } else if (item.value is List<int>) {
        var valueList = [];
        for (var element in item.value) {
          valueList.add(filterType.getTypeOptions()[element].value);
        }
        databaseFilters[item.key] = valueList;
      } else if (item.value is String) {
        databaseFilters[item.key] = item.value;
      }
    }

    return databaseFilters;
  }

  /// Sort users by createdAt timestamp (newest first)
  ///
  /// Maintains consistency with database ordering for predictable pagination
  void _sortUsersByCreatedAt(List<UserMetaData> users) {
    users.sort((a, b) {
      final aTime = a.userInfo.createdAt;
      final bTime = b.userInfo.createdAt;
      return bTime.compareTo(aTime); // Most recent first (descending)
    });
  }

  /// Set up background staleness detection listener
  ///
  /// This runs a parallel query with the same count as displayed items
  /// to detect when new content becomes available. Only triggers when
  /// the actual content (UIDs) differs, not just order changes.
  void _setupStalenessDetectionListener() {
    Log.d(
      '📡 Setting up real-time list listener for staleness detection - filters: ${state.filters}',
    );

    // Convert filters for database use
    final databaseFilters = _convertFiltersForDatabase(state.filters);

    // Use current displayed count or default to initial page size
    final initialCount =
        state.users.isNotEmpty ? state.users.length : _itemsPerPage;

    _stalenessDetectionListener = _databaseRepository
        .listenUsers(
      filters: databaseFilters,
      limit: initialCount, // Start with initial count
    )
        .listen(
      (realtimeUsers) {
        Log.d(
          '🔍 Real-time list: Received ${realtimeUsers.length} users (expected: ${state.users.length})',
        );

        // Only run staleness check if we have items to compare against
        if (state.users.isNotEmpty) {
          // Sort to match displayed data for fair comparison
          _sortUsersByCreatedAt(realtimeUsers);

          // Compare content by UID sets (order differences don't matter)
          final currentUIDs = state.users.map((u) => u.uid).toSet();
          final realtimeUIDs = realtimeUsers.map((u) => u.uid).toSet();

          final hasChanges =
              !(const SetEquality().equals(currentUIDs, realtimeUIDs));

          Log.d(
            '🔍 UID Comparison - Current: ${currentUIDs.length} items, Realtime: ${realtimeUIDs.length} items, HasChanges: $hasChanges',
          );

          if (hasChanges) {
            Log.d('🚨 Staleness detected! Setting hasNewUpdates to true');
            emit(
              state.copyWith(
                hasNewUpdates: true,
                lastUpdate: DateTime.now(),
              ),
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ UsersDataProvider: Real-time list listener error: $error');
      },
    );
  }

  /// Update staleness listener to match current pagination state
  ///
  /// Called after loadMore to ensure staleness detection uses the
  /// same item count as currently displayed for accurate comparison
  void _updateStalenessListenerCount() {
    if (state.users.isNotEmpty) {
      Log.d(
        '🔄 Setting up real-time listener to match displayed items: ${state.users.length}',
      );

      // Restart listener with updated count to match displayed items
      _stalenessDetectionListener?.cancel();
      _setupStalenessDetectionListener();
    }
  }

  /// Set up targeted real-time listener for currently displayed users
  ///
  /// This is the core efficiency feature: instead of listening to ALL users,
  /// we only listen to the specific UIDs currently displayed. This provides:
  /// - Real-time updates for visible items only
  /// - Minimal bandwidth and processing
  /// - In-place updates without list reconstruction
  void _setupDisplayedUsersListener() {
    if (state.users.isEmpty) {
      Log.d('📡 No displayed items to listen for updates');
      return;
    }

    final displayedUIDs = state.users.map((u) => u.uid).toList();
    Log.d(
      '📡 Setting up data listener for ${displayedUIDs.length} displayed UIDs: ${displayedUIDs.take(3).join(", ")}...',
    );

    _displayedUsersListener =
        _databaseRepository.listenUserUpdatesByUIDs(displayedUIDs).listen(
      (updatedUsers) async {
        Log.d(
          '📡 Real-time data: Received ${updatedUsers.length} updated users for displayed UIDs',
        );

        if (updatedUsers.isNotEmpty) {
          // Work on copy to avoid mutating state during processing
          final currentUsers = List<UserMetaData>.from(state.users);

          // Find and update matching items in displayed list
          bool hasChanges = false;
          for (final updatedUser in updatedUsers) {
            final existingIndex =
                currentUsers.indexWhere((u) => u.uid == updatedUser.uid);
            if (existingIndex != -1) {
              // Replace existing item with updated data
              currentUsers[existingIndex] = updatedUser;
              hasChanges = true;
              Log.d('📡 Updated user item: ${updatedUser.uid}');
            }
          }

          if (hasChanges) {
            // Re-sort to maintain createdAt ordering after updates
            _sortUsersByCreatedAt(currentUsers);

            // Keep pagination cursor in sync with displayed items
            if (currentUsers.isNotEmpty) {
              _lastCreatedAt = currentUsers.last.userInfo.createdAt;
            }

            Log.d(
              '📡 Real-time data: Updated displayed items, keeping existing totalCount: ${state.totalCount}',
            );

            emit(
              state.copyWith(
                users: currentUsers,
                // Preserve totalCount stability - only update on initial load/refresh
                lastUpdate: DateTime.now(),
                // Preserve hasMore state during real-time updates
              ),
            );
          } else {
            Log.d(
              '📡 Real-time data: No matching items found in displayed list',
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ UsersDataProvider: Data listener error: $error');

        // Check if this is a permission-denied error that might be due to expired token
        if (error.toString().contains('permission-denied') ||
            error.toString().contains('Missing or insufficient permissions')) {
          Log.w(
            '🔄 UsersDataProvider: Permission denied error detected, attempting token refresh...',
          );
          _handlePermissionDeniedError();
        } else {
          emit(
            state.copyWith(
              error: 'Failed to update user data: $error',
            ),
          );
        }
      },
    );
  }

  /// Handle permission denied errors by attempting token refresh and retry
  Future<void> _handlePermissionDeniedError() async {
    try {
      // Get the current user from Firebase Auth
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        Log.e('❌ UsersDataProvider: No current user for token refresh');
        emit(
          state.copyWith(
            error: 'Authentication required. Please sign in again.',
          ),
        );
        return;
      }

      // Attempt to refresh the token
      await currentUser.getIdToken(true); // Force refresh
      Log.d('✅ UsersDataProvider: Token refreshed successfully');

      // Wait a moment for the token to propagate
      await Future.delayed(const Duration(milliseconds: 500));

      // Retry the data listener setup
      Log.d('🔄 UsersDataProvider: Retrying data listener setup...');
      _setupDisplayedUsersListener();
    } catch (e) {
      Log.e('❌ UsersDataProvider: Token refresh failed: $e');
      emit(
        state.copyWith(
          error: 'Authentication failed. Please sign in again.',
        ),
      );
    }
  }
}
