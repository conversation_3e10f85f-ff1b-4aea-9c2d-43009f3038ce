import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:mevolvesupport/models/segment.dart';
import 'package:mevolvesupport/providers/segments/segments_data_state.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/logger.dart';

/// **SegmentsDataProvider - Advanced Real-time Segments List Manager**
///
/// This provider implements a sophisticated segments list management system with:
/// - Real-time updates for displayed items only (performance optimized)
/// - Intelligent staleness detection to notify when list needs refresh
/// - Cursor-based pagination with smart totalCount adjustment
/// - Search functionality
/// - Flexible filtering system
///
/// ## 🏗️ ARCHITECTURE OVERVIEW
///
/// ### Data Flow Patterns
///
/// **1. Initial Load Flow**
/// ```
/// initialize() → _setupListeners() → _loadInitialData() → _setupDisplayedSegmentsListener()
///                                 ↓
///                          Sets up staleness detection after initial load
/// ```
/// - Fetches first page of segments + total count
/// - Sets up targeted real-time listeners
/// - Initializes staleness detection
///
/// **2. Real-time Update Flow**
/// ```
/// Firestore Change → listenSegmentUpdatesByIDs() → Update in-place → Re-sort → Emit
///                                                ↓
///                                         Preserve totalCount
/// ```
/// - Only listens to currently displayed segment IDs (efficient)
/// - Updates existing items without changing list structure
/// - Maintains sort order and pagination state
///
/// **3. Pagination Flow**
/// ```
/// loadMore() → Fetch from cursor → Filter duplicates → Append → Update hasMore
///            ↓
///         Smart totalCount adjustment if no more items available
/// ```
/// - Uses last displayed item's createdAt as cursor
/// - Handles database inconsistencies gracefully
///
/// **4. Staleness Detection Flow**
/// ```
/// Background Listener → Compare ID sets → Set hasNewUpdates flag
///                    ↓
///               Only triggers on actual content differences
/// ```
/// - Runs parallel staleness check with same count as displayed
/// - Ignores order changes, only detects content differences
///
/// ## 📊 STATE MANAGEMENT
///
/// ### Core State Properties
/// - **`segments`**: Currently displayed segments list (paginated)
/// - **`totalCount`**: Stable count from initial fetch (only updated on refresh)
/// - **`hasMore`**: Can load more items (displayed < total)
/// - **`hasNewUpdates`**: Content staleness flag
/// - **`searchQuery`**: Active search query
///
/// ### Real-time Listeners
/// - **Displayed Items**: `_displayedSegmentsListener` (ID-based)
/// - **Staleness Detection**: `_stalenessDetectionListener` (background)
///
/// ## 🎯 PUBLIC API
///
/// **Core Operations**
/// - `initialize()` - Load initial data
/// - `loadMore()` - Load next batch of segments
/// - `refresh()` - Reload fresh data, clear staleness
/// - `search(query)` - Search segments by name
///
/// ## ⚡ PERFORMANCE OPTIMIZATIONS
///
/// - **Targeted Updates**: Only listen to displayed IDs
/// - **Cursor Pagination**: Efficient database queries
/// - **Smart Deduplication**: Prevents duplicate items
/// - **Stable Counts**: totalCount preserved during updates
/// - **Background Staleness**: Non-blocking freshness checks
class SegmentsDataProvider extends Cubit<SegmentsDataState> {
  SegmentsDataProvider({
    required DatabaseRepository databaseRepository,
  })  : _databaseRepository = databaseRepository,
        super(SegmentsDataState.initial());

  final DatabaseRepository _databaseRepository;

  // ============================================================================
  // PRIVATE STATE VARIABLES
  // ============================================================================

  // --- Real-time Stream Subscriptions ---

  /// Listener for displayed segment items updates (efficient ID-based targeting)
  StreamSubscription? _displayedSegmentsListener;

  /// Listener for staleness detection (background monitoring, non-blocking)
  StreamSubscription? _stalenessDetectionListener;

  // --- Pagination & Cursor State ---

  /// Pagination cursor using createdAt (consistent with Firestore ordering)
  DateTime? _lastCreatedDateTime;

  /// Items per page for initial load (balanced performance vs UX)
  static const int _itemsPerPage = 20;

  /// Batch size for loadMore operations (optimized for smooth scrolling)
  static const int _loadMoreBatchSize = 20;

  /// Maximum attempts for shortfall handling (prevents infinite loops)
  static const int _maxShortfallAttempts = 5;

  // ============================================================================
  // LIFECYCLE METHODS
  // ============================================================================

  @override
  Future<void> close() {
    _displayedSegmentsListener?.cancel();
    _stalenessDetectionListener?.cancel();
    return super.close();
  }

  // ============================================================================
  // PUBLIC API METHODS
  // ============================================================================

  /// Initialize segments list and set up real-time listeners
  ///
  /// This is the main entry point that:
  /// - Cleans up existing listeners
  /// - Resets pagination state
  /// - Loads initial data batch
  /// - Sets up targeted real-time updates
  /// - Initializes staleness detection
  void initialize({String? searchQuery}) {
    Log.d('🔄 SegmentsDataProvider: Initializing');
    Log.d('  📋 Search query: $searchQuery');
    Log.d('  🗑️ Cancelling old listeners...');
    _cancelListeners();
    Log.d('  🔄 Resetting pagination...');
    _resetPagination();

    Log.d('  🎨 Emitting loading state with filters...');
    emit(
      SegmentsDataState.initial().copyWith(
        searchQuery: searchQuery,
      ),
    );

    Log.d('  👂 Setting up new listeners...');
    _setupListeners();
  }

  /// Refresh entire segments list and clear staleness indicators
  ///
  /// This performs a full reset:
  /// - Clears hasNewUpdates flag
  /// - Re-initializes with current search query
  /// - Resets pagination to first page
  Future<void> refresh() async {
    Log.d('🔄 SegmentsDataProvider: Refreshing segments data');

    // Clear new updates flag when refreshing
    emit(state.copyWith(hasNewUpdates: false));

    // Re-initialize with current search query
    initialize(searchQuery: state.searchQuery);
  }

  /// Change search filter and reload segments
  ///
  /// Efficiently handles filter changes by performing a clean re-initialization
  void changeFilter({String? searchQuery}) {
    final newSearchQuery = searchQuery;

    Log.d('🎛️ SegmentsDataProvider: Changing filter');
    Log.d('  🔄 Search query: $newSearchQuery');

    initialize(
      searchQuery: newSearchQuery?.isEmpty == true ? null : newSearchQuery,
    );
  }

  /// Load more segments with advanced duplicate handling and cursor advancement
  ///
  /// **Pagination Strategy:**
  /// - Uses cursor-based pagination with `_lastCreatedDateTime`
  /// - Fetches batches of `_loadMoreBatchSize` items from database
  /// - Handles database inconsistencies and duplicate detection gracefully
  ///
  /// **Duplicate Handling Logic:**
  /// 1. **Initial Fetch**: Get next batch using current cursor position
  /// 2. **Process & Deduplicate**: Remove items already in displayed list
  /// 3. **Handle Shortfall**: If unique items < batch size AND we got full batch:
  ///    - Sort ALL processed items (including duplicates) by createdAt
  ///    - Advance cursor to last item's createdAt (ensures forward progress)
  ///    - Fetch additional batch and repeat until sufficient unique items
  /// 4. **End Detection**: Stop when partial batch received (< batch size)
  ///
  /// **Cursor Advancement Strategy:**
  /// - Critical insight: Sort ALL items (duplicates + unique) before advancing cursor
  /// - This ensures cursor always moves forward, even with 100% duplicate batches
  /// - Prevents infinite loops while maintaining database consistency
  ///
  /// **State Updates:**
  /// - Merge unique items with existing displayed list
  /// - Update `hasMore` based on partial batch detection
  /// - Adjust `totalCount` if database end reached
  /// - Refresh staleness detection to match new pagination state
  Future<void> loadMore() async {
    if (state.isLoadingMore || !state.hasMore) return;

    Log.d('📄 LoadMore: Loading next $_loadMoreBatchSize segments...');
    emit(state.copyWith(isLoadingMore: true));

    try {
      // Initial fetch
      var newSegments = await _databaseRepository.fetchSegments(
        searchQuery: state.searchQuery,
        lastCreatedDateTime: _lastCreatedDateTime,
        limit: _loadMoreBatchSize,
      );

      if (newSegments.isEmpty) {
        // Handle empty case
        emit(
          state.copyWith(
            isLoadingMore: false,
            hasMore: false,
            totalCount: state.segments.length,
          ),
        );
        return;
      }

      // Deduplicate
      final existingIds = state.segments.map((s) => s.id).toSet();
      var newUniqueSegments =
          newSegments.where((s) => !existingIds.contains(s.id)).toList();

      // Handle duplicate shortfall with cursor advancement (max attempts for safety)
      int shortfallAttempts = 0;

      while (newUniqueSegments.length < _loadMoreBatchSize &&
          newSegments.length == _loadMoreBatchSize &&
          shortfallAttempts < _maxShortfallAttempts) {
        shortfallAttempts++;
        Log.d(
          '🔄 Shortfall attempt $shortfallAttempts/$_maxShortfallAttempts: ${newUniqueSegments.length} < $_loadMoreBatchSize, fetching more...',
        );

        // Sort ALL processed items (including duplicates) to advance cursor
        _sortSegmentsByTimestamp(newSegments);

        // Update cursor from sorted batch (advances even with duplicates)
        if (newSegments.isNotEmpty) {
          _lastCreatedDateTime = newSegments.last.createdAt.toDate();
          Log.d('🎯 Cursor advanced to: $_lastCreatedDateTime');
        }

        // Fetch next batch
        final additionalBatch = await _databaseRepository.fetchSegments(
          searchQuery: state.searchQuery,
          lastCreatedDateTime: _lastCreatedDateTime,
          limit: _loadMoreBatchSize,
        );

        // Check for end conditions
        if (additionalBatch.isEmpty ||
            additionalBatch.length < _loadMoreBatchSize) {
          Log.d('🏁 Reached database end: ${additionalBatch.length} items');
          newSegments = additionalBatch;
          break;
        }

        // Process additional batch
        final allIds = [...existingIds, ...newUniqueSegments.map((s) => s.id)];
        final uniqueFromAdditional =
            additionalBatch.where((s) => !allIds.contains(s.id)).toList();

        // Add to collections
        newUniqueSegments.addAll(uniqueFromAdditional);
        newSegments = [...newSegments, ...additionalBatch];

        Log.d(
          '📊 Additional: ${additionalBatch.length} processed, ${uniqueFromAdditional.length} unique',
        );
      }

      // Safety check: warn if we hit the attempt limit
      if (shortfallAttempts >= _maxShortfallAttempts) {
        Log.w(
            '⚠️ LoadMore: Reached max shortfall attempts ($_maxShortfallAttempts). '
            'Proceeding with ${newUniqueSegments.length} unique items to prevent infinite loop.');
      }

      // Final merge and sort
      final allSegments = [...state.segments, ...newUniqueSegments];
      _sortSegmentsByTimestamp(allSegments);

      // Update final cursor from displayed items
      if (allSegments.isNotEmpty) {
        _lastCreatedDateTime = allSegments.last.createdAt.toDate();
      }

      // Determine hasMore
      final gotPartialBatch = newSegments.length < _loadMoreBatchSize;
      final reachedExpectedTotal = allSegments.length >= state.totalCount;
      final hasMoreData = !gotPartialBatch && !reachedExpectedTotal;
      final adjustedTotalCount =
          gotPartialBatch ? allSegments.length : state.totalCount;

      Log.d(
        '✅ LoadMore complete: ${newUniqueSegments.length} unique items added',
      );

      emit(
        state.copyWith(
          segments: allSegments,
          isLoadingMore: false,
          hasMore: hasMoreData,
          totalCount: adjustedTotalCount,
          lastUpdate: DateTime.now(),
        ),
      );

      // Update listeners
      _updateStalenessListenerCount();
      _displayedSegmentsListener?.cancel();
      _setupDisplayedSegmentsListener();
    } catch (error) {
      Log.e('❌ LoadMore error: $error');
      emit(
        state.copyWith(
          isLoadingMore: false,
          error: 'Failed to load more segments: ${error.toString()}',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE SETUP & INITIALIZATION
  // ============================================================================

  /// Core setup method: loads initial data and configures real-time listeners
  ///
  /// Flow:
  /// 1. Load first page of segments + total count
  /// 2. Set up targeted listener for displayed items
  /// 3. Start staleness detection in background
  Future<void> _setupListeners() async {
    await _loadInitialData();

    Log.d('👂 SegmentsDataProvider: Setting up listeners');

    _setupDisplayedSegmentsListener();
  }

  /// Cancel segment-specific listeners
  ///
  /// Cleans up all active listeners when changing filters or closing
  void _cancelListeners() {
    _displayedSegmentsListener?.cancel();
    _displayedSegmentsListener = null;
    _stalenessDetectionListener?.cancel();
    _stalenessDetectionListener = null;
  }

  /// Reset pagination cursor for clean state
  ///
  /// Called during initialization to ensure consistent starting state
  void _resetPagination() {
    _lastCreatedDateTime = null;
  }

  // ============================================================================
  // PRIVATE DATA LOADING - INITIAL & PAGINATION
  // ============================================================================

  /// Load initial page of segments and total count (one-time fetch)
  ///
  /// This is a clean fetch operation that:
  /// - Gets first page of segments matching current search query
  /// - Fetches accurate total count for pagination
  /// - Sets up pagination cursor for future loadMore calls
  ///
  /// Note: No real-time listeners set up here - that's done separately
  Future<void> _loadInitialData() async {
    Log.d(
      '📥 SegmentsDataProvider: Loading initial segments data (no real-time updates)',
    );
    Log.d('  📋 Search query: ${state.searchQuery}');

    try {
      // Parallel fetch for optimal performance: segments + count
      final results = await Future.wait([
        _databaseRepository.fetchSegments(
          searchQuery: state.searchQuery,
          limit: _itemsPerPage,
        ),
        _databaseRepository.fetchSegmentsCount(
          searchQuery: state.searchQuery,
        ),
      ]);

      final segments = results[0] as List<Segment>;
      final totalCount = results[1] as int;

      Log.d(
        '✅ SegmentsDataProvider: Loaded ${segments.length} initial segments, total count: $totalCount',
      );

      // Sort by timestamp to match database ordering (newest first)
      _sortSegmentsByTimestamp(segments);

      // Set pagination cursor to last item for future loadMore calls
      if (segments.isNotEmpty) {
        _lastCreatedDateTime = segments.last.createdAt.toDate();
      }

      // Determine if more pages available by comparing counts
      final hasMoreData = segments.length < totalCount;

      emit(
        state.copyWith(
          segments: segments,
          totalCount: totalCount,
          // Use real database count (only updated on initial load/refresh)
          isLoading: false,
          error: null,
          lastUpdate: DateTime.now(),
          hasMore: hasMoreData,
          // More pages available if we have fewer items than total
          hasNewUpdates:
              false, // Always false on initial load (no staleness yet)
        ),
      );

      // Set up staleness detection after initial data is loaded
      if (segments.isNotEmpty) {
        _setupStalenessDetectionListener();
      }
    } catch (error) {
      Log.e('❌ SegmentsDataProvider: Error loading initial segments: $error');
      emit(
        state.copyWith(
          isLoading: false,
          error: 'Failed to load segments: $error',
        ),
      );
    }
  }

  // ============================================================================
  // PRIVATE UTILITY & HELPER METHODS
  // ============================================================================

  /// Sort segments by createdAt timestamp (newest first)
  ///
  /// Maintains consistency with database ordering for predictable pagination
  void _sortSegmentsByTimestamp(List<Segment> segments) {
    segments.sort((a, b) {
      return b.createdAt
          .compareTo(a.createdAt); // Most recent first (descending)
    });
  }

  /// Set up background staleness detection listener
  ///
  /// This runs a parallel query with the same count as displayed items
  /// to detect when new content becomes available. Only triggers when
  /// the actual content (IDs) differs, not just order changes.
  void _setupStalenessDetectionListener() {
    Log.d(
      '📡 Setting up real-time list listener for staleness detection',
    );

    // Use current displayed count or default to initial page size
    final initialCount =
        state.segments.isNotEmpty ? state.segments.length : _itemsPerPage;

    _stalenessDetectionListener = _databaseRepository
        .listenSegments(
      searchQuery: state.searchQuery,
      count: initialCount, // Start with initial count
    )
        .listen(
      (realtimeSegments) {
        Log.d(
          '🔍 Real-time list: Received ${realtimeSegments.length} segments (expected: ${state.segments.length})',
        );

        // Only run staleness check if we have items to compare against
        if (state.segments.isNotEmpty) {
          // Apply same processing pipeline as displayed data for fair comparison
          _sortSegmentsByTimestamp(realtimeSegments);

          // Compare content by ID sets (order differences don't matter)
          final currentIds = state.segments.map((s) => s.id).toSet();
          final realtimeIds = realtimeSegments.map((s) => s.id).toSet();

          final hasChanges =
              !(const SetEquality().equals(currentIds, realtimeIds));

          Log.d(
            '🔍 ID Comparison - Current: ${currentIds.length} items, Realtime: ${realtimeIds.length} items, HasChanges: $hasChanges',
          );

          if (hasChanges) {
            Log.d('🚨 Staleness detected! Setting hasNewUpdates to true');
            emit(
              state.copyWith(
                hasNewUpdates: true,
                lastUpdate: DateTime.now(),
              ),
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ SegmentsDataProvider: Real-time list listener error: $error');
      },
    );
  }

  /// Update staleness listener to match current pagination state
  ///
  /// Called after loadMore to ensure staleness detection uses the
  /// same item count as currently displayed for accurate comparison
  void _updateStalenessListenerCount() {
    if (state.segments.isNotEmpty) {
      Log.d(
        '🔄 Setting up real-time listener to match displayed items: ${state.segments.length}',
      );

      // Restart listener with updated count to match displayed items
      _stalenessDetectionListener?.cancel();
      _setupStalenessDetectionListener();
    }
  }

  /// Set up targeted real-time listener for currently displayed segments
  ///
  /// This is the core efficiency feature: instead of listening to ALL segments,
  /// we only listen to the specific IDs currently displayed. This provides:
  /// - Real-time updates for visible items only
  /// - Minimal bandwidth and processing
  /// - In-place updates without list reconstruction
  void _setupDisplayedSegmentsListener() {
    if (state.segments.isEmpty) {
      Log.d('📡 No displayed items to listen for updates');
      return;
    }

    final displayedIds = state.segments.map((s) => s.id).toList();
    Log.d(
      '📡 Setting up data listener for ${displayedIds.length} displayed IDs: ${displayedIds.take(3).join(", ")}...',
    );

    _displayedSegmentsListener =
        _databaseRepository.listenSegmentUpdatesByIDs(displayedIds).listen(
      (updatedSegments) async {
        Log.d(
          '📡 Real-time data: Received ${updatedSegments.length} updated segments for displayed IDs',
        );

        if (updatedSegments.isNotEmpty) {
          // Work on copy to avoid mutating state during processing
          final currentSegments = List<Segment>.from(state.segments);

          // Find and update matching items in displayed list
          bool hasChanges = false;
          for (final updatedSegment in updatedSegments) {
            final existingIndex =
                currentSegments.indexWhere((s) => s.id == updatedSegment.id);
            if (existingIndex != -1) {
              // Replace existing item with updated data
              currentSegments[existingIndex] = updatedSegment;
              hasChanges = true;
              Log.d('📡 Updated segment item: ${updatedSegment.id}');
            }
          }

          if (hasChanges) {
            // Re-sort to maintain timestamp ordering after updates
            _sortSegmentsByTimestamp(currentSegments);

            // Keep pagination cursor in sync with displayed items
            if (currentSegments.isNotEmpty) {
              _lastCreatedDateTime = currentSegments.last.createdAt.toDate();
            }

            Log.d(
              '📡 Real-time data: Updated displayed items, keeping existing totalCount: ${state.totalCount}',
            );

            emit(
              state.copyWith(
                segments: currentSegments,
                // Preserve totalCount stability - only update on initial load/refresh
                lastUpdate: DateTime.now(),
                // Preserve hasMore state during real-time updates
              ),
            );
          } else {
            Log.d(
              '📡 Real-time data: No matching items found in displayed list',
            );
          }
        }
      },
      onError: (error) {
        Log.e('❌ SegmentsDataProvider: Data listener error: $error');
        emit(
          state.copyWith(
            error: 'Failed to update segment data: $error',
          ),
        );
      },
    );
  }
}
