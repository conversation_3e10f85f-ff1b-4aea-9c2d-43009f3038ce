import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/segment.dart';

/// State specifically for segments data management
class SegmentsDataState extends Equatable {
  const SegmentsDataState({
    required this.segments,
    required this.isLoading,
    required this.isLoadingMore,
    required this.hasMore,
    required this.totalCount,
    required this.lastUpdate,
    this.error,
    this.hasNewUpdates = false,
    this.searchQuery,
  });

  /// Current list of segments being displayed
  final List<Segment> segments;

  /// Loading states
  final bool isLoading; // Initial load
  final bool isLoadingMore; // Loading next batch
  final bool hasMore; // Can load more segments

  /// Counts and metadata
  final int totalCount; // Total count from Firestore
  final DateTime lastUpdate; // Last time data was updated
  final String? error; // Current error if any
  final bool hasNewUpdates; // New segments available

  /// Current search query
  final String? searchQuery;

  /// Initial state
  static SegmentsDataState initial() {
    return SegmentsDataState(
      segments: const [],
      isLoading: true,
      isLoadingMore: false,
      hasMore: true,
      totalCount: 0,
      lastUpdate: DateTime.now(),
      error: null,
      hasNewUpdates: false,
      searchQuery: null,
    );
  }

  /// Create a copy with updated values
  SegmentsDataState copyWith({
    List<Segment>? segments,
    bool? isLoading,
    bool? isLoadingMore,
    bool? hasMore,
    int? totalCount,
    DateTime? lastUpdate,
    String? error,
    bool? hasNewUpdates,
    String? searchQuery,
  }) {
    return SegmentsDataState(
      segments: segments ?? this.segments,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMore: hasMore ?? this.hasMore,
      totalCount: totalCount ?? this.totalCount,
      lastUpdate: lastUpdate ?? this.lastUpdate,
      error: error,
      hasNewUpdates: hasNewUpdates ?? this.hasNewUpdates,
      searchQuery: searchQuery ?? this.searchQuery,
    );
  }

  @override
  List<Object?> get props => [
        segments,
        isLoading,
        isLoadingMore,
        hasMore,
        totalCount,
        lastUpdate,
        error,
        hasNewUpdates,
        searchQuery,
      ];
}
