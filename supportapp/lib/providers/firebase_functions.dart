import 'dart:convert';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:mevolvesupport/constants/app_constant.dart';
import 'package:mevolvesupport/models/userslist_data.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

class FirebaseFunctionsRepository {
  FirebaseFunctionsRepository({
    FirebaseFunctions? firebaseFunctions,
  }) : _firebaseFunctions = firebaseFunctions ??
            FirebaseFunctions.instanceFor(region: functionsLocation);

  final FirebaseFunctions _firebaseFunctions;

  static const String functionsLocation = 'europe-west1';

  /// Generic method to call any Firebase Function
  Future<Map<String, dynamic>> callFunction({
    required String functionName,
    required Map<String, dynamic> parameters,
  }) async {
    final HttpsCallable callable =
        _firebaseFunctions.httpsCallable(functionName);
    final response = await callable.call(parameters);
    return response.data as Map<String, dynamic>;
  }

  Future<void> useEmulator() async {
    const host = FirebaseConstants.firebaseLocalHostWebIP;
    _firebaseFunctions.useFunctionsEmulator(
      host,
      FirebaseConstants.firebaseFunctionsPort,
    );
  }

  Future<int> updateSupportPerms({
    required Map<String, dynamic> supportUserMap,
  }) async {
    final HttpsCallable function = _firebaseFunctions.httpsCallable(
      'v${DatabaseRepository.currentDbVersion}-supportapp-updateSupportPerms',
    );
    final data = await function.call(supportUserMap).then((value) => value.data)
        as Map<String, dynamic>;
    return data['statusCode'];
    // if (data['statusCode'] == 200) {}
  }

  Future<Map<String, dynamic>?> restoreDatabaseToTimestamp({
    required DateTime timestamp,
    List<String>? uids, // Changed from String? uid to List<String>? uids
    List<String>? excludedCollections,
  }) async {
    Map<String, dynamic>? result;
    final functionName =
        '${DatabaseRepository.currentDbVersion}-supportapp-restoreDatabaseToTimestamp';

    // Format the timestamp to ISO 8601 string
    final formattedTimestamp = timestamp.toUtc().toIso8601String();

    Map<String, dynamic> requestData = {
      'timestamp': formattedTimestamp,
    };

    if (uids != null && uids.isNotEmpty) {
      requestData['uid'] = uids; // The backend expects 'uid' key
    }

    if (excludedCollections != null && excludedCollections.isNotEmpty) {
      requestData['excludedCollections'] = excludedCollections;
    }

    try {
      final HttpsCallable function =
          _firebaseFunctions.httpsCallable(functionName);
      dynamic response = await function.call(requestData);
      if (response.data != null && response.data.isNotEmpty) {
        result = response.data;
      }
    } catch (e) {
      //
    }

    return result;
  }

  Future<Map<String, dynamic>> fetchFilterUsers({
    Map<String, dynamic> filters = const {},
  }) async {
    final HttpsCallable function = _firebaseFunctions.httpsCallable(
      'v${DatabaseRepository.currentDbVersion}-supportapp-fetchFilterUsers',
    );
    final data = await function.call(filters).then((value) => value.data)
        as Map<String, dynamic>;
    if (data['statusCode'] == 200) {
      final list = List<Map<String, dynamic>>.from(jsonDecode(data['body']));
      // print("usrs: " + list.toString());
      List<UserslistData> userDataList =
          list.map((doc) => UserslistData.fromFirestore(doc)).toList();
      Map<String, dynamic> result = {
        'count': data['count'],
        'usersList': userDataList,
      };
      return result.isNotEmpty ? result : {};
    }
    return {};
  }

  Future<Map<String, dynamic>> getDbSchema() async {
    final HttpsCallable function = _firebaseFunctions.httpsCallable(
      'v${DatabaseRepository.currentDbVersion}-dbstatus-getSchema',
    );
    final data = await function.call().then((value) => value.data)
        as Map<String, dynamic>;
    if (data['statusCode'] == 200) {
      return data;
    }
    return {};
  }

  Future<Map<String, dynamic>> translateToolMsg({
    Map<String, dynamic> data = const {},
  }) async {
    final HttpsCallable function = _firebaseFunctions.httpsCallable(
      'v${DatabaseRepository.currentDbVersion}-translateapi-translateToolMsg',
    );
    final response = await function.call(data).then((value) => value.data)
        as Map<String, dynamic>;
    if (response['statusCode'] == 200) {
      return response;
    }
    return {};
  }
}
