import 'package:flutter/material.dart';
import 'package:mevolvesupport/constants/extensions.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';

abstract class AppStrings {
  static const String emptyDataSvgStr =
      '''<svg width="143" height="200" viewBox="0 0 143 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2_6)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M34.7187 47.8077C35.2784 28.358 44.5601 12.3444 71.4258 12.9663C98.2914 12.3444 107.573 28.358 108.133 47.8077C108.133 47.9631 108.137 48.1186 108.141 48.2741C108.145 48.4296 108.148 48.585 108.148 48.7405V49.6889C108.148 52.4408 107.993 55.3325 107.666 58.2865C107.013 64.1168 105.739 68.5322 104.044 74.0359C99.2398 87.0801 89.7249 97.901 73.0271 97.901C72.483 97.901 71.9544 97.8854 71.4258 97.8699C70.8972 97.8854 70.3685 97.901 69.8244 97.901C42.9743 97.901 34.7031 69.9781 34.7031 49.6889V48.7405C34.7031 48.4296 34.7031 48.1186 34.7187 47.8077ZM105.209 49.6889C105.209 51.3214 105.146 52.9694 105.038 54.6174C104.198 67.2418 97.9325 85.6808 86.4275 91.8065C82.6184 93.8276 78.1875 94.947 73.0413 94.947C72.5598 94.947 72.0629 94.9315 71.5658 94.916L71.5643 94.9159H71.4399H71.3156C70.8336 94.9315 70.3361 94.947 69.8386 94.947C53.2808 94.947 44.2011 83.2866 40.1278 68.47C38.3865 62.1423 37.578 55.3481 37.6713 48.7872C63.0133 54.0733 83.971 40.7182 88.6663 36.9868C90.9518 39.5677 95.9113 44.2318 105.193 47.901C105.193 48.039 105.197 48.1808 105.201 48.3246C105.205 48.4723 105.209 48.6219 105.209 48.7716V48.7872V49.6889Z" fill="#3E6155"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.3672 46.0821H31.3112V45.056C31.3112 22.9011 49.2683 4.94403 71.4232 4.94403C93.578 4.94403 111.535 22.9011 111.535 45.056V46.0821H116.479V45.056C116.464 20.1648 96.2988 0 71.4232 0C46.5475 0 26.3827 20.1648 26.3827 45.0404V46.0821H26.3672Z" fill="#3E6155"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M46.9994 100L46.9683 100.062L59.1884 106.95L71.4086 113.837L83.6443 106.965L95.8644 100.078L95.8333 100.016C125.917 100.016 136.769 111.692 142.833 127.938C139.723 124.596 135.541 122.761 130.877 122.761H11.9558C7.27612 122.761 3.09391 124.58 0 127.938C6.07898 111.66 16.9154 100 46.9994 100Z" fill="#3E6155"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M32.7425 32.6959H33.5821C35.3856 32.6959 36.847 34.1573 36.847 35.9608V60.7742C36.847 62.5777 35.3856 64.0392 33.5821 64.0392H32.7425C26.7879 64.0392 21.9216 59.1729 21.9216 53.2183V43.5168C21.9216 37.5622 26.7879 32.6959 32.7425 32.6959Z" fill="#3E6155"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M110.089 32.6958H109.249C107.446 32.6958 105.984 34.1572 105.984 35.9607V60.7742C105.984 62.5776 107.446 64.0391 109.249 64.0391H110.089C116.043 64.0391 120.91 59.1728 120.91 53.2182V43.5167C120.91 37.5621 116.043 32.6958 110.089 32.6958Z" fill="#3E6155"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M74.457 80.6593C87.7655 80.2085 100.561 73.1344 107.759 60.7744L108.272 59.8882L112.532 62.3757L112.019 63.2619C104.028 76.9746 89.8955 84.8882 75.1411 85.5723L74.457 80.6593Z" fill="#3E6155"/>
<path d="M142.71 63.0596C142.585 63.0596 142.492 63.0907 142.492 63.1373C142.492 63.1839 142.585 63.215 142.71 63.215C142.834 63.215 142.928 63.1839 142.928 63.1373C142.943 63.0907 142.834 63.0596 142.71 63.0596Z" stroke="#3E6155" stroke-width="0.5" stroke-miterlimit="2.6131"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M76.0123 78.8246L78.2356 78.5136C80.1013 78.2493 81.8425 79.5709 82.1069 81.4365L82.2312 82.3227C82.4955 84.1884 81.174 85.9297 79.3083 86.194L77.0851 86.5049C76.9763 86.5205 76.8519 86.536 76.7431 86.536C76.6342 86.5671 76.5254 86.5827 76.401 86.5982L74.1778 86.9092C72.3121 87.1735 70.5708 85.852 70.3065 83.9863L70.1821 83.1001C69.9178 81.2344 71.2393 79.4931 73.105 79.2288L75.3282 78.9179C75.4371 78.9023 75.5615 78.8868 75.6703 78.8868C75.7791 78.8557 75.888 78.8401 76.0123 78.8246Z" fill="#3E6155"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M81.9037 199.969H60.9304H49.2855H25.5915C20.2433 199.969 15.719 196.533 14.2886 191.371L6.59275 163.681L0.653698 142.288C-0.356874 138.635 0.327206 135.059 2.61265 132.043C4.91365 129.027 8.16303 127.41 11.9566 127.41H40.7656H102.053H130.862C134.656 127.41 137.92 129.027 140.206 132.043C142.507 135.059 143.191 138.635 142.165 142.288L136.226 163.681L128.53 191.371C127.1 196.533 122.575 199.969 117.227 199.969H93.5486H81.9037Z" fill="#5A9C7D" stroke="#3E6155" stroke-width="0.5" stroke-miterlimit="2.6131"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M81.9038 199.969H60.9305H49.2856H17.7869C17.0095 199.969 16.3565 199.906 15.5792 199.767L6.59286 198.056L1.36898 197.061C1.13577 197.015 0.964747 196.797 0.995842 196.564C1.01139 196.331 1.2135 196.144 1.46226 196.144H40.7813H102.069H141.388C141.636 196.144 141.823 196.331 141.854 196.564C141.87 196.797 141.714 197.015 141.481 197.061L136.257 198.056L127.271 199.767C126.509 199.906 125.84 199.969 125.079 199.969H93.5487H81.9038Z" fill="#3E6155"/>
</g>
<defs>
<clipPath id="clip0_2_6">
<rect width="142.973" height="200" fill="white"/>
</clipPath>
</defs>
</svg>''';

  static const String emptyDataMsgSvgStr =
      '''<svg width="150" height="135" viewBox="0 0 150 135" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M15.7986 0.0195312H109.314C113.652 0.0195312 117.586 1.78829 120.438 4.63982C123.289 7.49135 125.058 11.4257 125.058 15.7635V79.1159C125.058 83.4537 123.289 87.3881 120.438 90.2396C117.586 93.0911 113.645 94.8599 109.314 94.8599H57.9125L23.9833 119.031C22.0397 120.416 19.3429 119.959 17.9642 118.015C17.4194 117.255 17.1639 116.381 17.1639 115.513H17.1504V94.8599H15.7986C11.4608 94.8599 7.52651 93.0911 4.67498 90.2396C1.82345 87.3881 0.0546875 83.447 0.0546875 79.1159V15.7635C0.0546875 11.4257 1.82345 7.49135 4.67498 4.63982C7.53323 1.78829 11.4675 0.0195312 15.7986 0.0195312ZM109.314 8.67501H15.7986C13.855 8.67501 12.0863 9.47532 10.795 10.7599C9.50375 12.0444 8.71016 13.8199 8.71016 15.7635V79.1159C8.71016 81.0595 9.51048 82.835 10.795 84.1196C12.0795 85.4041 13.855 86.2044 15.7986 86.2044H21.4815C23.869 86.2044 25.8126 88.1413 25.8126 90.5355V107.134L53.7832 87.2065C54.5297 86.581 55.4982 86.2044 56.554 86.2044H109.314C111.258 86.2044 113.026 85.4041 114.318 84.1196C115.609 82.835 116.403 81.0595 116.403 79.1159V15.7635C116.403 13.8199 115.602 12.0511 114.318 10.7599C113.033 9.46859 111.258 8.67501 109.314 8.67501Z" fill="#3E6155"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M33.3383 53.9364C37.5148 53.9364 40.911 50.5334 40.911 46.3637C40.911 42.1873 37.508 38.791 33.3383 38.791C29.1686 38.791 25.7656 42.194 25.7656 46.3637C25.7589 50.5334 29.1619 53.9364 33.3383 53.9364Z" fill="#3E6155"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M62.561 53.9364C66.7374 53.9364 70.1337 50.5334 70.1337 46.3637C70.1337 42.1873 66.7307 38.791 62.561 38.791C58.3846 38.791 54.9883 42.194 54.9883 46.3637C54.9816 50.5334 58.3846 53.9364 62.561 53.9364Z" fill="#3E6155"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M91.7797 53.9364C95.9562 53.9364 99.3524 50.5334 99.3524 46.3637C99.3524 42.1873 95.9494 38.791 91.7797 38.791C87.6101 38.791 84.207 42.194 84.207 46.3637C84.2003 50.5334 87.6033 53.9364 91.7797 53.9364Z" fill="#3E6155"/>
<path d="M58.2295 110.019C55.842 110.019 53.8984 108.082 53.8984 105.688C53.8984 103.3 55.8353 101.357 58.2295 101.357H93.4567C94.5126 101.357 95.4743 101.733 96.2275 102.359L124.191 122.286V105.688C124.191 103.3 126.128 101.357 128.522 101.357H134.205C136.149 101.357 137.918 100.556 139.209 99.2719C140.494 97.9874 141.294 96.2186 141.294 94.2683V30.9158C141.294 28.9722 140.494 27.2035 139.209 25.9122C137.924 24.6277 136.149 23.8273 134.205 23.8273C131.818 23.8273 129.874 21.8905 129.874 19.4962C129.874 17.1088 131.811 15.1719 134.205 15.1719C138.543 15.1719 142.478 16.9406 145.329 19.7922C148.181 22.6437 149.949 26.578 149.949 30.9158V94.2683C149.949 98.6061 148.181 102.54 145.329 105.392C142.478 108.243 138.543 110.012 134.205 110.012H132.854V130.666H132.84C132.84 131.533 132.578 132.407 132.04 133.167C130.654 135.111 127.964 135.562 126.021 134.183L92.0915 110.012H58.2295V110.019Z" fill="#3E6155"/>
</svg>''';

  static const String noAccessSvgStr = '''
<svg width="170" height="201" viewBox="0 0 170 201" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_4170_120786)">
<path d="M82.8848 200.543C69.468 196.357 56.9509 190.333 45.4508 182.197C25.619 168.194 12.7499 149.222 6.06104 126.027C2.38414 113.353 0.584807 100.327 0.232763 87.1844C-0.119281 74.8238 0.271879 62.4631 0.0762988 50.1025C-0.0801651 38.0939 8.60358 28.2366 21.6292 27.5717C33.7552 26.9458 45.3726 24.1295 56.2468 18.5359C62.1142 15.5239 67.4731 11.7688 72.4409 7.42694C74.6705 5.47114 76.9001 3.35888 79.5209 1.98982C83.8236 -0.318024 88.2828 0.190484 92.1944 3.12418C95.2064 5.35379 97.9054 8.0528 100.839 10.3998C114.608 21.3131 130.489 26.4764 147.856 27.6108C155.054 28.0802 161.117 30.6227 165.459 36.5292C167.923 39.9323 169.37 43.7266 169.527 47.912C169.996 63.1281 170.348 78.3442 169.175 93.5603C167.649 112.571 163.933 130.994 154.741 147.971C146.566 163.03 135.183 175.117 120.906 184.505C111.322 190.842 100.996 195.692 90.1604 199.526C89.0652 199.917 87.9308 200.19 86.8355 200.543C85.4665 200.543 84.1757 200.543 82.8848 200.543ZM157.831 71.0686C157.831 63.7931 157.87 56.5175 157.831 49.2028C157.792 43.8439 154.663 40.3235 149.304 39.5412C143.28 38.6415 137.139 38.2503 131.154 37.0377C114.451 33.6738 99.8612 26.0461 87.4614 14.3505C84.8797 11.9253 84.8015 11.9253 82.1807 14.3896C69.7419 26.0853 55.1516 33.7129 38.4491 37.0377C32.4643 38.2503 26.3622 38.6415 20.2993 39.5412C14.9795 40.3235 11.9284 43.883 11.8111 49.281C11.6546 57.026 11.459 64.771 11.459 72.5159C11.459 87.8885 12.4369 103.183 16.1139 118.203C20.886 137.8 30.2738 154.542 45.7638 167.646C56.9901 177.151 69.8201 183.684 83.628 188.456C84.4886 188.769 85.7403 188.651 86.6008 188.299C91.7642 186.226 97.0057 184.231 102.013 181.806C131.819 167.294 149.226 143.394 155.132 110.928C157.518 97.7457 158.222 84.4463 157.831 71.0686Z" fill="#5A9C7D"/>
<g clip-path="url(#clip1_4170_120786)">
<path d="M133.721 100.389C133.752 127.256 111.647 149.361 84.9024 149.574C58.2186 149.758 36.328 128.111 36.0227 101.031C35.7174 74.0108 57.3942 52.0592 84.6277 51.7845C111.647 51.5097 133.416 73.0949 133.721 100.389ZM108.747 69.5228C93.6648 57.9517 70.9804 58.318 56.7531 73.3697C42.1289 88.7877 43.228 111.533 54.2801 124.478C72.4154 106.19 90.5506 87.8718 108.747 69.5228ZM61.2106 131.439C75.5295 143.255 98.4886 143.163 113.143 127.47C127.615 111.961 125.936 89.4289 115.677 77.0028C97.5116 95.1381 79.3764 113.273 61.2106 131.439Z" fill="#3E6155"/>
</g>
</g>
<defs>
<clipPath id="clip0_4170_120786">
<rect width="169.92" height="200" fill="white" transform="translate(0.0390625 0.54248)"/>
</clipPath>
<clipPath id="clip1_4170_120786">
<rect width="97.6984" height="97.7899" fill="white" transform="translate(36.0234 51.7844)"/>
</clipPath>
</defs>
</svg>
''';

  static String emojiMoodNone({
    required MeColorScheme colorScheme,
  }) {
    return '''
<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="30" height="30" rx="6" fill="${colorScheme.color22.toHex()}"/>
<circle cx="14.625" cy="14.625" r="7.625" stroke="${colorScheme.color7.toHex()}" stroke-width="2"/>
<line x1="20.1298" y1="8.61796" x2="8.43797" y2="20.5015" stroke="${colorScheme.color7.toHex()}" stroke-width="2"/>
</svg>
    ''';
  }

  static String emojiMoodVeryHappy({
    required MeColorScheme colorScheme,
    Color? color,
  }) {
    String? colorHex = color?.toHex();
    return '''
<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M24 0H6C2.68629 0 0 2.68629 0 6V24C0 27.3137 2.68629 30 6 30H24C27.3137 30 30 27.3137 30 24V6C30 2.68629 27.3137 0 24 0Z" fill="${colorHex ?? colorScheme.color22.toHex()}"/>
<path d="M6.02929 10.0454C5.97182 9.68631 6.00056 9.29845 6.10111 8.93931C6.21603 8.58018 6.40278 8.23541 6.66136 7.93374C6.91993 7.64644 7.25034 7.40223 7.60947 7.24421C7.9686 7.08619 8.37083 7 8.77306 7C9.17528 7 9.56315 7.08619 9.93664 7.24421C10.2958 7.40223 10.6262 7.64644 10.8848 7.93374C11.1433 8.22105 11.3444 8.56582 11.445 8.93931C11.5599 9.29845 11.5887 9.68631 11.5168 10.0454C11.4737 10.2609 11.2726 10.4046 11.0571 10.3758C10.8991 10.3471 10.7698 10.2322 10.7411 10.0742V10.0454C10.6118 9.52829 10.3532 9.1117 10.0085 8.82439C9.64934 8.53709 9.23275 8.37907 8.78742 8.37907C8.35646 8.37907 7.9255 8.53709 7.56637 8.82439C7.20724 9.1117 6.94867 9.52829 6.83374 10.0454V10.0742C6.77628 10.2897 6.5608 10.4189 6.34532 10.3758C6.17294 10.3184 6.05802 10.1891 6.02929 10.0454Z" fill="${colorScheme.color16.toHex()}"/>
<path d="M17.584 10.0454C17.5265 9.68631 17.5552 9.29845 17.6558 8.93931C17.7707 8.58018 17.9575 8.23541 18.216 7.93374C18.4746 7.64644 18.805 7.40223 19.1642 7.24421C19.5233 7.08619 19.9255 7 20.3277 7C20.73 7 21.1178 7.08619 21.4913 7.24421C21.8505 7.40223 22.1809 7.64644 22.4394 7.93374C22.698 8.22105 22.8991 8.56582 22.9997 8.93931C23.1146 9.29845 23.1433 9.68631 23.0715 10.0454C23.0284 10.2609 22.8273 10.4046 22.6118 10.3758C22.4538 10.3471 22.3245 10.2322 22.2958 10.0742V10.0454C22.1665 9.52829 21.9079 9.1117 21.5632 8.82439C21.204 8.53709 20.7874 8.37907 20.3421 8.37907C19.9112 8.37907 19.4802 8.53709 19.1211 8.82439C18.7619 9.1117 18.5034 9.52829 18.3884 10.0454V10.0742C18.331 10.2897 18.1155 10.4189 17.9 10.3758C17.7276 10.3184 17.6127 10.1891 17.584 10.0454Z" fill="${colorScheme.color16.toHex()}"/>
<path d="M9.06458 20.7198C10.2672 21.9795 11.9789 22.9105 14.1631 23.0185C16.4184 23.1299 18.3056 22.255 19.6742 20.7965C15.3536 18.6748 11.1268 19.8649 9.06458 20.7198Z" fill="${colorScheme.color16.toHex()}"/>
<path d="M7.00346 15.7188C6.94396 17.3098 7.65357 19.2413 9.0648 20.7196C11.127 19.8647 15.3538 18.6746 19.6744 20.7961C20.9089 19.4808 21.7215 17.6909 22 15.7188C17.8079 17.2457 11.82 16.873 7.00346 15.7188Z" fill="${colorScheme.color16.toHex()}"/>
</svg>
    ''';
  }

  static String emojiMoodHappy({
    required MeColorScheme colorScheme,
    Color? color,
  }) {
    String? colorHex = color?.toHex();
    return '''
<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="30" height="30" rx="6" fill="${colorHex ?? colorScheme.color22.toHex()}"/>
<path d="M22.6465 17.7522C19.9568 24.9865 10.4966 24.8937 7.71417 17.7522C11.6096 23.688 18.7511 23.7808 22.6465 17.7522Z" fill="${colorScheme.color16.toHex()}"/>
<ellipse cx="9.97119" cy="11.7618" rx="1.3999" ry="2.33317" fill="${colorScheme.color16.toHex()}"/>
<ellipse cx="20.2368" cy="11.7618" rx="1.3999" ry="2.33317" fill="${colorScheme.color16.toHex()}"/>
</svg>
    ''';
  }

  static String emojiMoodMeh({
    required MeColorScheme colorScheme,
    Color? color,
  }) {
    String? colorHex = color?.toHex();
    return '''
<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="30" height="30" rx="6" fill="${colorHex ?? colorScheme.color22.toHex()}"/>
<path d="M9.42188 21.7991H20.2219H9.42188Z" fill="${colorScheme.color16.toHex()}"/>
<path d="M9.42188 21.7991H20.2219" stroke="${colorScheme.color16.toHex()}" stroke-miterlimit="10" stroke-linecap="round"/>
<ellipse cx="9.97119" cy="12.7618" rx="1.3999" ry="2.33317" fill="${colorScheme.color16.toHex()}"/>
<ellipse cx="20.2368" cy="12.7618" rx="1.3999" ry="2.33317" fill="${colorScheme.color16.toHex()}"/>
</svg>
    ''';
  }

  static String emojiMoodSad({
    required MeColorScheme colorScheme,
    Color? color,
  }) {
    String? colorHex = color?.toHex();
    return '''
<svg width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="30" height="30" rx="6" fill="${colorHex ?? colorScheme.color22.toHex()}"/>
<path d="M7.71387 23.1425C10.4035 15.9082 19.8638 16.0009 22.6462 23.1425C18.7508 17.2066 11.6093 17.1139 7.71387 23.1425Z" fill="${colorScheme.color16.toHex()}"/>
<ellipse cx="9.97119" cy="11.7618" rx="1.3999" ry="2.33317" fill="${colorScheme.color16.toHex()}"/>
<ellipse cx="20.2368" cy="11.7618" rx="1.3999" ry="2.33317" fill="${colorScheme.color16.toHex()}"/>
</svg>
    ''';
  }

  static String emojiMoodVerySad({
    required MeColorScheme colorScheme,
    Color? color,
  }) {
    String? colorHex = color?.toHex();
    return '''
        <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 30 30" fill="none">
<path d="M24 0H6C2.68629 0 0 2.68629 0 6V24C0 27.3137 2.68629 30 6 30H24C27.3137 30 30 27.3137 30 24V6C30 2.68629 27.3137 0 24 0Z" fill="${colorHex ?? colorScheme.color22.toHex()}"/>
<path d="M9.0625 16.5875C10.2651 15.7211 11.9768 15.0807 14.161 15.0065C16.4164 14.9298 18.3035 15.5316 19.6721 16.5348C15.3515 17.9941 11.1247 17.1756 9.0625 16.5875Z" fill="#22252E"/>
<path d="M7.00346 20.0273C6.94396 18.933 7.65357 17.6044 9.0648 16.5876C11.127 17.1756 15.3538 17.9942 19.6744 16.535C20.9089 17.4397 21.7215 18.6709 22 20.0273C18 18.5 12 18.5 7.00346 20.0273Z" fill="#22252E"/>
<path d="M7.17204 10.7379C7.89974 10.7593 8.6028 10.6837 9.3535 10.3897C10.0796 9.99873 10.781 9.51087 11.167 9C11.2442 10.1154 10.7629 11.0632 9.843 11.5034C8.92311 11.9436 7.90302 11.5839 7.17204 10.7379Z" fill="#22252E"/>
<path d="M22.2166 10.7379C21.4889 10.7593 20.7859 10.6837 20.0352 10.3897C19.3091 9.99873 18.6077 9.51087 18.2217 9C18.1445 10.1154 18.6258 11.0632 19.5457 11.5034C20.4656 11.9436 21.4856 11.5839 22.2166 10.7379Z" fill="#22252E"/>
</svg>
    ''';
  }

  static String noMood({
    required MeColorScheme colorScheme,
    Color? color,
  }) {
    String? colorHex = color?.toHex();
    return '''
    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
<path d="M20 0H4C1.79086 0 0 1.79086 0 4V20C0 22.2091 1.79086 24 4 24H20C22.2091 24 24 22.2091 24 20V4C24 1.79086 22.2091 0 20 0Z" fill="${colorHex ?? colorScheme.color22.toHex()}"/>
<path d="M8 12H16" stroke="#22252E" stroke-width="2" stroke-linecap="round"/>
</svg>
    ''';
  }
}
