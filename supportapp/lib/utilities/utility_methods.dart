import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:get_time_ago/get_time_ago.dart';
import 'package:intl/intl.dart';
import 'package:mevolvesupport/constants/app_config.dart';
import 'package:mevolvesupport/enums/environment_type.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:mime/mime.dart';
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';

import 'package:mevolvesupport/blocs/app/app_bloc.dart';
import 'package:mevolvesupport/constants/extensions.dart';
import 'package:mevolvesupport/enums/document_type.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/enums/purchase/subscription_state.dart';
import 'package:mevolvesupport/enums/user_status.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/models/userslist_data.dart';
import 'package:mevolvesupport/providers/firebase_authentication.dart';
import 'package:mevolvesupport/firebase_options_dev.dart' as firebase_dev;
import 'package:mevolvesupport/firebase_options_hotfix.dart' as firebase_hotfix;
import 'package:mevolvesupport/firebase_options_prod.dart' as firebase_prod;
import 'package:mevolvesupport/firebase_options_qa.dart' as firebase_qa;
import 'package:mevolvesupport/firebase_options_staging.dart'
    as firebase_staging;

class UtilityMethods {}

/// FNV-1a 64bit hash algorithm optimized for Dart Strings
int fastHash(String string) {
  var hash = 0xcbf29ce484222000;

  var i = 0;
  while (i < string.length) {
    final codeUnit = string.codeUnitAt(i++);
    hash ^= codeUnit >> 8;
    hash *= 0x100000001b3;
    hash ^= codeUnit & 0xFF;
    hash *= 0x100000001b3;
  }

  return hash;
}

int stringTo32BitNumber(String s) {
  int hashCode = s.hashCode % 0x7FFFFFFF; // take the modulus with 0x7FFFFFFF
  int myInt = hashCode - 0x40000000; // adjust the range to [-2^31, 2^31 - 1]
  return myInt;
}

String getStorageImageUri(
  String fileName,
  String userId,
  String docId,
  DocumentType docType, {
  bool isThumbnail = false,
}) {
  if (isThumbnail) {
    fileName = 'thumb_$fileName'; // thumbnail photo
  } else {
    fileName = 'op_$fileName'; // optimized photo
  }
  return 'gs://${FirebaseStorage.instance.bucket}/media/$userId/${docType.name}/$docId/$fileName';
}

UserStatus getUserStatus(UserMetaData user) {
  UserStatus userStatus;
  if (user.subscriptionInfo.subscriptionExpDate != null &&
          DateTime.now()
              .onlyDate()
              .isAfter(user.subscriptionInfo.subscriptionExpDate!.onlyDate()) ||
      user.subscriptionInfo.subscriptionState ==
          SubscriptionState.subscriptionExpired ||
      user.subscriptionInfo.subscriptionState ==
          SubscriptionState.trialExpired) {
    userStatus = UserStatus.expired;
  } else if (user.subscriptionInfo.subscriptionState ==
      SubscriptionState.subscribed) {
    userStatus = UserStatus.subscribed;
  } else {
    userStatus = UserStatus.trial;
  }
  return userStatus;
}

UserStatus getUserlistStatus(UserslistData user) {
  UserStatus userStatus;
  if (user.subscriptionExpDate == null) {
    userStatus = UserStatus.free;
  } else if (DateTime.now()
          .onlyDate()
          .isAfter(user.subscriptionExpDate!.onlyDate()) ||
      user.subscriptionState == SubscriptionState.subscriptionExpired ||
      user.subscriptionState == SubscriptionState.trialExpired) {
    userStatus = UserStatus.expired;
  } else if (user.subscriptionState == SubscriptionState.subscribed) {
    userStatus = UserStatus.subscribed;
  } else {
    userStatus = UserStatus.trial;
  }
  return userStatus;
}

String getPlainText(String text) {
  if (text.contains('"insert":')) {
    final doc = Document.fromJson(jsonDecode(text));
    final List<Map<String, dynamic>> ops = doc.toDelta().toJson();
    final converter = QuillDeltaToHtmlConverter(
      ops,
      ConverterOptions(sanitizerOptions: OpAttributeSanitizerOptions()),
    );
    String desc =
        converter.convert().replaceAll(RegExp(r'<[^>]*>|&[^;]+;'), ' ');
    return desc.trim();
  } else {
    return text;
  }
}

String getPlainTextV2(String? text) {
  if (text == null) return '';
  if (text.contains('"insert":')) {
    final doc = Document.fromJson(jsonDecode(text));
    final List<Map<String, dynamic>> ops = doc.toDelta().toJson();
    String html = QuillDeltaToHtmlConverter(
      ops,
      ConverterOptions(
        sanitizerOptions: OpAttributeSanitizerOptions(),
        multiLineBlockquote: true,
        multiLineParagraph: true,
      ),
    ).convert();

    // Handle ordered lists
    RegExp olExp = RegExp(r'(?<=<ol>).*?(?=</ol>)', dotAll: true);
    Iterable<Match> olMatches = olExp.allMatches(html);
    for (final Match m in olMatches) {
      String match = m[0]!;
      RegExp liExp = RegExp(r'<li>');
      int num = 0;
      match = match.replaceAllMapped(liExp, (Match m) {
        num++;
        return '\n$num. ';
      });
      html = html.replaceFirst(m[0]!, match);
    }

    // Handle unordered lists
    RegExp ulExp = RegExp(r'(?<=<ul>).*?(?=</ul>)', dotAll: true);
    Iterable<Match> ulMatches = ulExp.allMatches(html);
    for (final Match m in ulMatches) {
      String match = m[0]!;
      RegExp liExp = RegExp(r'<li>');
      match = match.replaceAll(liExp, '\n• ');
      html = html.replaceFirst(m[0]!, match);
    }

    // Replace HTML tags while preserving newlines
    String desc = html
        .replaceAll(
          RegExp(r'<br\s*/?>', multiLine: true),
          '\n',
        ) // Replace <br> with newline
        .replaceAll(RegExp(r'<p>', multiLine: true), '') // Remove <p> tags
        .replaceAll(
          RegExp(r'</p>', multiLine: true),
          '\n',
        ) // Replace </p> with newline
        .replaceAll(RegExp(r'<[^>]*>|&[^;]+;'), ' ') // Remove other HTML tags
        .replaceAll(RegExp(r' +'), ' ') // Remove multiple spaces
        .replaceAll(
          RegExp(r'\n{3,}'),
          '\n\n',
        ); // Replace multiple newlines with double newline

    return desc.trim();
  } else {
    return text;
  }
}

String force12Hour(TimeOfDay time) {
  DateTime tempDate = DateFormat('HH:mm').parse('${time.hour}:${time.minute}');
  var dateFormat = DateFormat('h:mm a');
  return dateFormat.format(tempDate);
}

String getDaySuffix(int value) {
  if (value >= 11 && value <= 13) {
    return 'th';
  }
  switch (value % 10) {
    case 1:
      return 'st';
    case 2:
      return 'nd';
    case 3:
      return 'rd';
    default:
      return 'th';
  }
}

String getEmptyScreenText(int value) {
  switch (value) {
    case 0:
      return 'Tap any user to see the details';
    case 1:
      return 'Tap any chat to see the details';
    case 2:
      return 'Tap any item to see the details';
    case 3:
      return 'Tap any item to see the details';
    default:
      return 'Tap any item to see the details';
  }
}

PermissionType getPermissionFromTab(int index) {
  switch (index) {
    case 0:
      return PermissionType.allUsers;
    case 1:
      return PermissionType.supportChat;
    case 2:
      return PermissionType.feedbacks;
    case 3:
      return PermissionType.deletedReports;
    case 4:
      return PermissionType.nugget;
    case 5:
      return PermissionType.adminPanel;
    default:
      return PermissionType.allUsers;
  }
}

String getRepeatText(List<String> repeatData) {
  String repeatText = '';
  if (repeatData.isEmpty) {
    repeatText = 'No Repeat';
  } else if (repeatData[0].startsWith('d')) {
    repeatText = 'Repeat Daily';
  } else if (repeatData[0].startsWith('w')) {
    if (repeatData.length == 7) {
      repeatText = 'Everyday';
    } else {
      repeatText = 'Every ';
      for (var day in repeatData) {
        String dayName = day[7].toUpperCase() + day.substring(8);
        repeatText += dayName + (day == repeatData.last ? '' : ', ');
      }
    }
  } else if (repeatData[0].startsWith('y')) {
    String monthName = 'Jan';
    int yearlyDay = 1;
    yearlyDay = int.tryParse(
          repeatData[0].substring(11),
        ) ??
        1;
    monthName = repeatData[0][7].toUpperCase() + repeatData[0].substring(8, 10);
    repeatText = 'Every $monthName ${yearlyDay.toString()}';
  } else if (repeatData[0].startsWith('monthly:')) {
    int monthlyDay = int.tryParse(
          repeatData[0].substring(8),
        ) ??
        1;
    repeatText =
        '${monthlyDay.toString()}${getDaySuffix(monthlyDay)} of every month';
  } else if (repeatData[0].startsWith('monthly_')) {
    repeatText = 'Every month last day';
  }
  return repeatText;
}

String getDuration({
  required DateTime fromDateTime,
  DateTime? toDateTime,
}) {
  toDateTime ??= DateTime.now().onlyDate();
  int duration = fromDateTime.onlyDate().difference(toDateTime).inDays.abs();
  return '${duration}d';
}

String getTimeAgo(DateTime? dateTime, {bool isShort = false}) {
  if (dateTime == null) {
    return '';
  }
  int duration =
      dateTime.onlyDate().difference(DateTime.now().onlyDate()).inDays.abs();
  if (duration > 7 || isShort) {
    return '${duration}d${isShort ? '' : ' ago'}';
  }
  GetTimeAgo.setCustomLocaleMessages('en', CustomMessagesAgo());
  return GetTimeAgo.parse(dateTime);
}

String getTimeLeft(DateTime? dateTime) {
  if (dateTime == null) {
    return '';
  }
  int duration =
      dateTime.onlyDate().difference(DateTime.now().onlyDate()).inDays.abs();
  if (duration > 7) {
    return '${duration}d left';
  }
  GetTimeAgo.setCustomLocaleMessages('en', CustomMessagesLeft());
  return GetTimeAgo.parse(dateTime);
}

String getTimeStarts(DateTime? dateTime) {
  if (dateTime == null) {
    return '';
  }
  int duration =
      dateTime.onlyDate().difference(DateTime.now().onlyDate()).inDays.abs();
  if (duration > 7) {
    return 'Starts in $duration days';
  }
  GetTimeAgo.setCustomLocaleMessages('en', CustomMessagesLeft());
  return GetTimeAgo.parse(dateTime);
}

class CustomMessagesAgo implements Messages {
  @override
  String prefixAgo() => '';

  @override
  String suffixAgo() => 'ago';

  @override
  String secsAgo(int seconds) => 'a few secs';

  @override
  String minAgo(int minutes) => '1m';

  @override
  String minsAgo(int minutes) => '${minutes}m';

  @override
  String hourAgo(int minutes) => '1h';

  @override
  String hoursAgo(int hours) => '${hours}h';

  @override
  String dayAgo(int hours) => '1d';

  @override
  String daysAgo(int days) => '${days}d';

  @override
  String wordSeparator() => ' ';
}

class CustomMessagesLeft implements Messages {
  @override
  String prefixAgo() => '';

  @override
  String suffixAgo() => 'left';

  @override
  String secsAgo(int seconds) => 'a few secs';

  @override
  String minAgo(int minutes) => '1m';

  @override
  String minsAgo(int minutes) => '${minutes}m';

  @override
  String hourAgo(int minutes) => '1h';

  @override
  String hoursAgo(int hours) => '${hours}h';

  @override
  String dayAgo(int hours) => '1d';

  @override
  String daysAgo(int days) => '${days}d';

  @override
  String wordSeparator() => ' ';
}

DateTime? parseDate(dynamic date) {
  if (date == null) return null;
  if (date is Timestamp) return date.toDate();
  if (date is String) return DateTime.parse(date).toLocal();
  if (date is Map) {
    if (date.containsKey('value')) {
      return parseDate(date['value']);
    } else {
      return Timestamp(date['_seconds'], date['_nanoseconds']).toDate();
    }
  }
  return null;
}

bool? parseBool(dynamic value) {
  if (value == null) return null;
  if (value is bool) return value;
  if (value is int) return value == 1;
  if (value is String) return value == 'true';
  return null;
}

double getFileSize(int sizeInBytes) {
  double sizeInMb = sizeInBytes / (1024 * 1024);
  return sizeInMb;
}

String getFileExtension(Uint8List file) {
  return (lookupMimeType(
            '',
            headerBytes: file,
          ) ??
          'image/jpg')
      .substring(6);
  // return extensionFromMime(
  //   lookupMimeType(
  //         '',
  //         headerBytes: file,
  //       ) ??
  //       '',
  // );
}

Future<Uint8List> getCompressedImage(Uint8List image) async {
  if (getFileSize(image.lengthInBytes) < 20) {
    return image;
  }
  Uint8List compressed = image;

  while (getFileSize(compressed.lengthInBytes) > 20) {
    compressed = await FlutterImageCompress.compressWithList(
      image,
      minWidth: 4000,
      minHeight: 4000,
      quality: 80,
    );
    if (getFileSize(compressed.lengthInBytes) < 20) {
      return compressed;
    }
  }
  return compressed;
}

Future<String> getStorageDownloadUrl(String url) async {
  Log.d('getStorageDownloadUrl: Fetching download URL for path: $url');

  try {
    final ref = FirebaseStorage.instance.ref().child(url);

    // Check if file exists first
    final metadata = await ref.getMetadata();
    Log.d(
      'getStorageDownloadUrl: File exists - size: ${metadata.size}, contentType: ${metadata.contentType}',
    );

    // Get download URL
    final path = await ref.getDownloadURL();
    Log.d(
      'getStorageDownloadUrl: Successfully got download URL: ${path.substring(0, 50)}...',
    );

    return path;
  } on FirebaseException catch (e) {
    Log.d(
      'getStorageDownloadUrl: Firebase error for path $url: ${e.code} - ${e.message}',
    );

    // Handle specific Firebase errors
    switch (e.code) {
      case 'object-not-found':
        throw Exception('Video file not found');
      case 'unauthorized':
        throw Exception('Access denied to video file');
      case 'retry-limit-exceeded':
        throw Exception('Too many requests, please try again later');
      default:
        throw Exception('Failed to access video file: ${e.message}');
    }
  } catch (e) {
    Log.d('getStorageDownloadUrl: General error for path $url: $e');
    throw Exception('Failed to get video URL: $e');
  }
}

// Future<String> getCompressedImage(XFile image) async {
//   if (getFileSize(await image.length()) < 20) {
//     return image.path;
//   }
//   XFile compressed = image;
//   final imageUri = Uri.parse(image.path);
//   int loop = 1;
//   while (getFileSize(await compressed.length()) > 20) {
//     final String outputUri =
//         imageUri.resolve('./comp${loop.toString() + image.name}').toString();
//     compressed = await FlutterImageCompress.compressAndGetFile(
//           compressed.path,
//           outputUri,
//           minWidth: 4000,
//           minHeight: 4000,
//           quality: 80,
//         ) ??
//         image;
//     loop++;
//     // Todo delete temp files
//     if (getFileSize(await compressed.length()) < 20) {
//       return compressed.path;
//     }
//   }
//   return compressed.path;
// }

bool hasPermission({
  required BuildContext context,
  required PermissionType permissionType,
  bool isWrite = false,
}) {
  final firebaseAuth = context.read<FirebaseAuthenticationRepository>();
  if (firebaseAuth.currentUser?.email ==
      '<EMAIL> || <EMAIL>') {
    return true;
  }
  if (isWrite) {
    return context
        .read<AppBloc>()
        .state
        .supportUser!
        .permissions
        .contains('${permissionType.permissionString}_w');
  } else {
    return context
        .read<AppBloc>()
        .state
        .supportUser!
        .permissions
        .contains('${permissionType.permissionString}_r');
  }
}

String getAccessfromPermission({
  required PermissionType permissionType,
  required List<String> permissions,
}) {
  if (permissions.contains('${permissionType.name}_w')) {
    return 'Write';
  }
  if (permissions.contains('${permissionType.name}_r')) {
    if (permissionType == PermissionType.allUsers ||
        permissionType == PermissionType.viewEmail) {
      return 'View';
    }
    return 'Read';
  } else {
    return 'None';
  }
}

FirebaseOptions getFirebaseOptions() {
  switch (AppConfig.instance.environmentType) {
    case EnvironmentType.dev:
      return firebase_dev.DefaultFirebaseOptions.currentPlatform;
    case EnvironmentType.qa:
      return firebase_qa.DefaultFirebaseOptions.currentPlatform;
    case EnvironmentType.staging:
      return firebase_staging.DefaultFirebaseOptions.currentPlatform;
    case EnvironmentType.prod:
      return firebase_prod.DefaultFirebaseOptions.currentPlatform;
    case EnvironmentType.hotfix:
      return firebase_hotfix.DefaultFirebaseOptions.currentPlatform;
  }
}

BoxFit getBoxFit(SizedBox? iconSize) {
  return (iconSize != null && iconSize.width != null && iconSize.height != null)
      ? (iconSize.width! > iconSize.height!
          ? BoxFit.fitWidth
          : BoxFit.fitHeight)
      : (iconSize != null && iconSize.width != null && iconSize.height == null)
          ? BoxFit.fitWidth
          : (iconSize != null &&
                  iconSize.width == null &&
                  iconSize.height != null)
              ? BoxFit.fitHeight
              : BoxFit.scaleDown;
}
