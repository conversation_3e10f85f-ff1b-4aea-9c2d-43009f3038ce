import 'package:bloc/bloc.dart';
import 'package:mevolvesupport/utilities/logger.dart';

class MeBlocObserver extends BlocObserver {
  //  onEvent

  //onChange
  @override
  void onChange(BlocBase bloc, Change change) {
    super.onChange(bloc, change);
    // Log.d('onChange: ${bloc.runtimeType} nextState: ${change.nextState}');
  }

  //  onTransition

  //  onError

  //  onClose
  @override
  void onClose(BlocBase bloc) {
    super.onClose(bloc);
    Log.d('onClose: ${bloc.runtimeType}');
  }
}
