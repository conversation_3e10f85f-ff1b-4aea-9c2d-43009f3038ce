import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mevolvesupport/models/activity.dart';

/// Formats event names to be more user-friendly
// String formatEventName(String eventName) {
//   // Map of raw event names to user-friendly names
//   final Map<String, String> eventNameMap = {
//     'm_ad_impression': 'Ad Impression',
//     'p_screen_viewed': 'Screen Viewed',
//     'p_crud_action': 'Data Action',
//     'm_app_install': 'App Installed',
//     'm_app_update': 'App Updated',
//     'm_app_open': 'App Opened',
//     'm_app_remove': 'App Uninstalled',
//     'm_login': 'User Login',
//     'm_subscription': 'Subscription Event',
//     'm_promotion_click': 'Promotion Clicked',
//     'p_user_login': 'User Login',
//     'p_user_preference': 'User Preference Changed',
//     'p_feature_shared': 'Feature Shared',
//     'p_app_open': 'App Opened',
//     'p_sync_complete': 'Sync Completed',
//     'p_app_store_redirect': 'App Store Redirect',
//     'p_filter_applied': 'Filter Applied',
//     'm_app_store_redirect': 'App Store Redirect',
//   };
//
//   // Return the mapped name or the original with formatting if not found
//   return eventNameMap[eventName] ??
//       eventName.replaceAll('_', ' ').split(' ').map((word) {
//         if (word.isEmpty) return '';
//         if (word.length <= 2) return word.toUpperCase(); // Handle short words like 'p_' or 'm_'
//         return word[0].toUpperCase() + word.substring(1);
//       }).join(' ');
// }

class ActivityFilter {
  ActivityFilter({
    this.eventName,
    this.eventType,
    this.startFrom,
  });

  String? eventName;
  String? eventType;
  DateTime? startFrom;

  bool get isEmpty =>
      eventName == null && eventType == null && startFrom == null;

  ActivityFilter copyWith({
    String? eventName,
    String? eventType,
    DateTime? startFrom,
    bool clearEventName = false,
    bool clearEventType = false,
    bool clearStartFrom = false,
  }) {
    return ActivityFilter(
      eventName: clearEventName ? null : (eventName ?? this.eventName),
      eventType: clearEventType ? null : (eventType ?? this.eventType),
      startFrom: clearStartFrom ? null : (startFrom ?? this.startFrom),
    );
  }

  void clear() {
    eventName = null;
    eventType = null;
    startFrom = null;
  }
}

/// Returns an appropriate icon for the event type
IconData getEventIcon(String eventName) {
  // Map event names to appropriate icons
  final Map<String, IconData> eventIconMap = {
    'm_ad_impression': Icons.ads_click,
    'p_screen_viewed': Icons.visibility,
    'p_crud_action': Icons.edit,
    'm_app_install': Icons.download_done,
    'm_app_update': Icons.system_update,
    'm_app_open': Icons.open_in_new,
    'm_app_remove': Icons.delete,
    'm_login': Icons.login,
    'm_subscription': Icons.card_membership,
    'm_promotion_click': Icons.campaign,
    'p_user_login': Icons.login,
    'p_user_preference': Icons.settings,
    'p_feature_shared': Icons.share,
    'p_app_open': Icons.open_in_new,
    'p_sync_complete': Icons.sync,
    'p_app_store_redirect': Icons.shop,
    'p_filter_applied': Icons.filter_list,
    'm_app_store_redirect': Icons.shop,
  };

  // Return the mapped icon or a default one
  return eventIconMap[eventName] ?? Icons.event_note;
}

/// Format timestamp to a readable format
String formatTimestamp(DateTime timestamp) {
  return DateFormat('h:mm a').format(timestamp);
}

/// Group activities by date
Map<String, List<ActivityItem>> groupActivitiesByDate(
  List<ActivityItem> activities,
) {
  final Map<String, List<ActivityItem>> grouped = {};
  final now = DateTime.now();
  final yesterday = DateTime.now().subtract(const Duration(days: 1));

  for (var activity in activities) {
    final date = activity.eventTime;
    String key;

    if (isSameDay(date, now)) {
      key = 'Today';
    } else if (isSameDay(date, yesterday)) {
      key = 'Yesterday';
    } else {
      key = DateFormat('d MMM yyyy').format(date);
    }

    grouped.putIfAbsent(key, () => []).add(activity);
  }

  return grouped;
}

/// Check if two dates are the same day
bool isSameDay(DateTime a, DateTime b) {
  return a.year == b.year && a.month == b.month && a.day == b.day;
}

/// Get a user-friendly description of the event based on its parameters
// String getEventDescription(ActivityItem activity) {
//   final rawData = activity.rawData;
//   final eventName = activity.eventName;
//
//   // Handle specific event types
//   if (eventName == 'p_screen_viewed') {
//     final screenName = rawData['screen_name'] ?? rawData['page_title'];
//     if (screenName != null) {
//       return 'Viewed $screenName screen';
//     }
//   } else if (eventName == 'p_crud_action') {
//     final actionType = rawData['action_type'];
//     final itemType = rawData['item_type'];
//     if (actionType != null && itemType != null) {
//       return '${actionType.toString().toUpperCase()} action on $itemType';
//     }
//   } else if (eventName == 'p_user_preference') {
//     return 'Changed user preferences';
//   } else if (eventName == 'p_feature_shared') {
//     final featureType = rawData['feature_type'];
//     if (featureType != null) {
//       return 'Shared a $featureType';
//     }
//   }
//
//   // Default description
//   return eventName;
// }
