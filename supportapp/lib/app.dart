import 'package:firebase_app_check/firebase_app_check.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/blocs/app/app_bloc.dart';
import 'package:mevolvesupport/constants/app_config.dart';
import 'package:mevolvesupport/cubits/debug/debug_settings_cubit.dart';
import 'package:mevolvesupport/cubits/debug/debug_settings_state.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/environment_type.dart';
import 'package:mevolvesupport/providers/firebase_authentication.dart';
import 'package:mevolvesupport/providers/firebase_firestore.dart';
import 'package:mevolvesupport/providers/shared_prefs.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/repositories/functions_repository.dart';
import 'package:mevolvesupport/repositories/storage_repository.dart';
import 'package:mevolvesupport/utilities/bloc_observer.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/app_view.dart';
import 'package:mevolvesupport/widgets/screens/auth/splash_page.dart';
import 'package:mevolvesupport/widgets/shared/debug/debug_settings_tab.dart';

class App extends StatefulWidget {
  const App({
    super.key,
  });

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  late final Future initApp;
  late SharedPreferencesClient _sharedPreferencesClient;
  late FirebaseAuthenticationRepository _authenticationRepository;
  late DatabaseRepository _databaseRepository;
  late FirebaseFirestoreRepository _firebaseFirestoreRepository;
  late FunctionsRepository _functionsRepository;
  late StorageRepository _storageRepository;

  late final AppBloc _appBloc;

  Future<void> _initializeApp() async {
    try {
      Bloc.observer = MeBlocObserver();

      await AppConfig.instance.initAppConfig();
      await Firebase.initializeApp(options: getFirebaseOptions());

      if (AppConfig.instance.environmentType == EnvironmentType.prod ||
          AppConfig.instance.environmentType == EnvironmentType.staging ||
          AppConfig.instance.environmentType == EnvironmentType.hotfix) {
        await FirebaseAppCheck.instance.activate(
          webProvider: ReCaptchaEnterpriseProvider(_getWebRecaptchaSiteKey()),
          androidProvider: AndroidProvider.playIntegrity,
          appleProvider: AppleProvider.appAttest,
        );
      }

      _authenticationRepository = FirebaseAuthenticationRepository();
      _sharedPreferencesClient = SharedPreferencesClient();

      await Future.wait([
        _sharedPreferencesClient.initSharedPrefs(),
      ]);

      _functionsRepository = FunctionsRepository();

      _databaseRepository = DatabaseRepository();
      _firebaseFirestoreRepository = FirebaseFirestoreRepository();

      _storageRepository = StorageRepository(
        databaseRepository: _databaseRepository,
      );

      if (kDebugMode) {
        // await _authenticationRepository.useEmulator();
        // await _databaseRepository.useEmulator();
        // await _storageRepository.useEmulator();
        // await _firebaseFunctionsRepository.useEmulator();
      }

      _appBloc = AppBloc(
        authenticationRepository: _authenticationRepository,
        databaseRepository: _databaseRepository,
        sharedPreferencesClient: _sharedPreferencesClient,
      );
    } catch (e, stackTrace) {
      Log.e('Error during app initialization: $e');
      Log.e('Stack trace: $stackTrace');
      rethrow;
    }
  }

  @override
  void initState() {
    super.initState();
    Log.init();
    initApp = _initializeApp();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider<DebugSettingsCubit>(
      create: (BuildContext context) => DebugSettingsCubit(),
      lazy: false,
      child: BlocBuilder<DebugSettingsCubit, DebugSettingsState>(
        builder: (context, debugState) => Stack(
          textDirection: TextDirection.ltr,
          children: [
            FutureBuilder(
              future: initApp,
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.done) {
                  if (snapshot.hasError) {
                    return MaterialApp(
                      debugShowCheckedModeBanner: false,
                      home: Scaffold(
                        body: Center(
                          child: SelectableText(
                            'Error in loading app${snapshot.error}',
                          ),
                        ),
                      ),
                    );
                  } else {
                    return MultiRepositoryProvider(
                      providers: [
                        RepositoryProvider.value(
                          value: _authenticationRepository,
                        ),
                        RepositoryProvider.value(
                          value: _databaseRepository,
                        ),
                        RepositoryProvider.value(
                          value: _storageRepository,
                        ),
                        RepositoryProvider.value(
                          value: _functionsRepository,
                        ),
                        RepositoryProvider.value(
                          value: _sharedPreferencesClient,
                        ),
                        RepositoryProvider.value(
                          value: _firebaseFirestoreRepository,
                        ),
                      ],
                      child: MultiBlocProvider(
                        providers: [
                          BlocProvider(
                            create: (_) => _appBloc,
                          ),
                          BlocProvider(
                            create: (context) => UserSettingsCubit(
                              context.read<AppBloc>(),
                            ),
                          ),
                        ],
                        child: const AppView(),
                      ),
                    );
                  }
                } else {
                  return const SplashPage();
                }
              },
            ),
            if (debugState.envNameOverlay) const EnvNameOverlay(),
          ],
        ),
      ),
    );
  }

  String _getWebRecaptchaSiteKey() {
    switch (AppConfig.instance.environmentType) {
      case EnvironmentType.qa:
        return '6LeCZHQpAAAAAKpnlxTMTUQ_DlAJYhvbgSSj8ft6';
      case EnvironmentType.staging:
        return '6LeDZHQpAAAAAA6Cf9udUCyHxz4NUeouA4VwjGUl';
      case EnvironmentType.prod:
        return '6Lfa2nQpAAAAAGMTgNTDENwZO4hR0HZ9zLvIDLJ0';
      case EnvironmentType.hotfix:
        return '6LfSN3UpAAAAABQT6UPUwzfVwGgVrVNucrfV5b8-';
      case EnvironmentType.dev:
        return '6LeCZHQpAAAAAKpnlxTMTUQ_DlAJYhvbgSSj8ft6';
    }
  }
}
