import 'dart:convert';

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:csv/csv.dart';
import 'package:file_picker/file_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/segment.dart';
import 'package:mevolvesupport/providers/segment_details/segment_details_cubit.dart';
import 'package:mevolvesupport/providers/segment_details/segment_details_state.dart';
import 'package:mevolvesupport/providers/segments/segments_data_provider.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_icon_button.dart';
import 'package:universal_html/html.dart' as html;
import 'package:uuid/uuid.dart';

/// Segment details panel - displays segment information
class SegmentDetailsPanel extends StatefulWidget {
  const SegmentDetailsPanel({
    super.key,
    required this.segmentId,
  });

  final String segmentId;

  @override
  State<SegmentDetailsPanel> createState() => _SegmentDetailsPanelState();
}

class _SegmentDetailsPanelState extends State<SegmentDetailsPanel> {
  @override
  void initState() {
    super.initState();
    _loadSegmentData();
  }

  @override
  void didUpdateWidget(SegmentDetailsPanel oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Load new segment if changed
    if (oldWidget.segmentId != widget.segmentId) {
      _loadSegmentData();
    }
  }

  void _loadSegmentData() {
    // Ensure segment data is loaded
    final currentSegmentId =
        context.read<SegmentDetailsCubit>().state.selectedSegmentId;
    if (currentSegmentId != widget.segmentId) {
      context.read<SegmentDetailsCubit>().selectSegment(widget.segmentId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SegmentDetailsCubit, SegmentDetailsState>(
      builder: (context, state) {
        if (state.isLoading && state.segmentDetails == null) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.error != null && state.segmentDetails == null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error: ${state.error}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () => _loadSegmentData(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        if (state.segmentDetails == null) {
          return const Center(child: CircularProgressIndicator());
        }

        final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
        final segment = state.segmentDetails!;

        return Scaffold(
          backgroundColor: colorScheme.color5,
          appBar: _buildAppBar(context, segment),
          body: _SegmentDetailsContent(
            segment: segment,
            activities: state.segmentActivities,
            supportUsers: state.supportUsers,
            isLoadingActivities: state.isLoadingActivities,
            onRefresh: () =>
                context.read<SegmentDetailsCubit>().refreshSegmentCount(),
          ),
        );
      },
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, Segment segment) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return AppBar(
      backgroundColor: colorScheme.color9,
      titleSpacing: 0,
      toolbarHeight: 48,
      title: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16.0),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            const Expanded(
              child: MeText(
                text: 'Segment Details',
                meFontStyle: MeFontStyle.A12,
                maxLines: 1,
                textOverflow: TextOverflow.ellipsis,
              ),
            ),
            Row(
              children: [
                _SegmentActionsMenu(segment: segment),
                const SizedBox(width: 16),
                GestureDetector(
                  onTap: () {
                    context.read<SegmentDetailsCubit>().clearSegment();
                  },
                  child: Assets.svg.closeIcon.svg(),
                ),
              ],
            ),
          ],
        ),
      ),
      foregroundColor: Colors.black,
      elevation: 1,
    );
  }
}

// Segment actions menu widget
class _SegmentActionsMenu extends StatelessWidget {
  const _SegmentActionsMenu({required this.segment});

  final Segment segment;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final auth = FirebaseAuth.instance;
    final currentUser = auth.currentUser;
    final hasWritePermission = hasPermission(
      context: context,
      permissionType: PermissionType.segments,
      isWrite: true,
    );

    // Don't show menu if user doesn't have write permission
    if (!hasWritePermission) {
      return const SizedBox.shrink();
    }

    return PopupMenuButton<String>(
      onSelected: (value) {
        if (_isSegmentDeleted(segment)) {
          _showSegmentDeletedDialog(context);
          return;
        }
        if (value == 'edit') {
          _showEditSegmentDialog(context, segment, currentUser!.uid);
        } else if (value == 'delete') {
          _showDeleteConfirmationDialog(context, segment, currentUser!.uid);
        }
      },
      color: Theme.of(context).colorScheme.surface,
      itemBuilder: (BuildContext context) => [
        PopupMenuItem<String>(
          value: 'edit',
          child: Row(
            children: [
              Icon(
                Icons.edit,
                color: Theme.of(context).colorScheme.primary,
                size: 18,
              ),
              const SizedBox(width: 8),
              const Text('Edit'),
            ],
          ),
        ),
        const PopupMenuItem<String>(
          value: 'delete',
          child: Row(
            children: [
              Icon(
                Icons.delete_outline,
                color: Colors.red,
                size: 18,
              ),
              SizedBox(width: 8),
              Text(
                'Delete',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
      ],
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: colorScheme.color1,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 4,
        ),
        child: const Icon(Icons.more_vert),
      ),
    );
  }

  bool _isSegmentDeleted(Segment segment) {
    return segment.deletedAt != null;
  }

  void _showSegmentDeletedDialog(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        backgroundColor: colorScheme.color5,
        title: const MeText(
          text: 'Segment Deleted',
          meFontStyle: MeFontStyle.E8,
        ),
        content: const MeText(
          text:
              'This segment has been deleted and no actions can be performed.',
          meFontStyle: MeFontStyle.E8,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showEditSegmentDialog(
    BuildContext context,
    Segment segment,
    String updatedByUid,
  ) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final nameController = TextEditingController(text: segment.name);
    final purposeController = TextEditingController(text: segment.purpose);

    showDialog(
      context: context,
      builder: (context) {
        bool isLoading = false;

        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const MeText(
                text: 'Edit Segment',
                meFontStyle: MeFontStyle.E8,
              ),
              backgroundColor: colorScheme.color5,
              content: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: nameController,
                    style: TextStyle(color: colorScheme.color2),
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(r'[a-zA-Z0-9_]'),
                      ),
                    ],
                    decoration: InputDecoration(
                      hintText: 'Name',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Colors.grey.shade400,
                          width: 1.5,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 14,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: purposeController,
                    style: TextStyle(color: colorScheme.color2),
                    decoration: InputDecoration(
                      hintText: 'Purpose',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: Colors.grey.shade400),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(
                          color: Colors.grey.shade400,
                          width: 1.5,
                        ),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 14,
                      ),
                    ),
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('Cancel'),
                ),
                ElevatedButton(
                  onPressed: isLoading
                      ? null
                      : () async {
                          final newName = nameController.text.trim();
                          final newPurpose = purposeController.text.trim();

                          if (newName.isEmpty || newPurpose.isEmpty) {
                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content:
                                    Text('Both name and purpose are required'),
                              ),
                            );
                            return;
                          }

                          setState(() => isLoading = true);

                          try {
                            final databaseRepository =
                                context.read<DatabaseRepository>();

                            // Update segment and all user documents
                            if (segment.name != newName) {
                              await databaseRepository.updateSegmentNameInUsers(
                                oldName: segment.name,
                                newName: newName,
                              );
                            }

                            await databaseRepository.updateSegment(
                              id: segment.id,
                              name: newName,
                              purpose: newPurpose,
                              updatedBy: updatedByUid,
                            );

                            if (context.mounted) {
                              Navigator.of(context).pop();

                              // Refresh the segments list
                              context.read<SegmentsDataProvider>().refresh();

                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Segment updated'),
                                ),
                              );
                            }
                          } catch (e) {
                            if (context.mounted) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Update failed: $e'),
                                ),
                              );
                            }
                            setState(() => isLoading = false);
                          }
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.color1,
                    foregroundColor: Colors.white,
                  ),
                  child: isLoading
                      ? const SizedBox(
                          width: 18,
                          height: 18,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : const Text('Update'),
                ),
              ],
            );
          },
        );
      },
    );
  }

  void _showDeleteConfirmationDialog(
    BuildContext context,
    Segment segment,
    String updatedByUid,
  ) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const MeText(
          text: 'Confirm Delete',
          meFontStyle: MeFontStyle.E8,
        ),
        content: const MeText(
          text: 'Are you sure you want to delete this segment?',
          meFontStyle: MeFontStyle.E8,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              try {
                final databaseRepository = context.read<DatabaseRepository>();

                // Remove segment from all users
                await databaseRepository.removeSegmentFromAllUsers(
                  segmentName: segment.name,
                );

                // Soft delete the segment
                await databaseRepository.deleteSegment(
                  id: segment.id,
                  updatedBy: updatedByUid,
                );

                if (context.mounted) {
                  Navigator.of(context).pop();

                  // Clear selected segment
                  context.read<SegmentDetailsCubit>().clearSegment();

                  // Refresh the segments list
                  context.read<SegmentsDataProvider>().refresh();

                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Segment deleted')),
                  );
                }
              } catch (e) {
                if (context.mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Delete failed: $e')),
                  );
                }
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

// Segment details content widget
class _SegmentDetailsContent extends StatelessWidget {
  const _SegmentDetailsContent({
    required this.segment,
    required this.activities,
    required this.supportUsers,
    required this.isLoadingActivities,
    required this.onRefresh,
  });

  final Segment segment;
  final List<SegmentActivity> activities;
  final Map<String, dynamic> supportUsers;
  final bool isLoadingActivities;
  final VoidCallback onRefresh;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Container(
      color: colorScheme.color5,
      child: ListView(
        children: [
          const SizedBox(height: 12),
          _DetailRow(
            title: 'Name',
            content: segment.name,
          ),
          const Divider(),
          _DetailRow(
            title: 'Purpose',
            content: segment.purpose,
          ),
          const Divider(),
          _UsersSection(
            segment: segment,
            onRefresh: onRefresh,
          ),
          const Divider(),
          _sectionTitle('Activity'),
          _ActivitiesSection(
            activities: activities,
            supportUsers: supportUsers,
            isLoading: isLoadingActivities,
            segmentId: segment.id,
            isSegmentDeleted: segment.deletedAt != null,
          ),
        ],
      ),
    );
  }

  Widget _sectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: MeText(
        text: title,
        meFontStyle: MeFontStyle.F7,
      ),
    );
  }
}

// Detail row widget
class _DetailRow extends StatelessWidget {
  const _DetailRow({
    required this.title,
    required this.content,
  });

  final String title;
  final String content;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.all(20),
          child: MeText(
            text: title,
            meFontStyle: MeFontStyle.F7,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: MeText(
              text: content,
              meFontStyle: MeFontStyle.E8,
            ),
          ),
        ),
      ],
    );
  }
}

// Users section widget
class _UsersSection extends StatelessWidget {
  const _UsersSection({
    required this.segment,
    required this.onRefresh,
  });

  final Segment segment;
  final VoidCallback onRefresh;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Row(
          children: [
            _sectionTitle('Users'),
            const SizedBox(width: 12),
            Row(
              children: [
                const Icon(Icons.people_alt_outlined, size: 20),
                const SizedBox(width: 8),
                MeText(
                  text: '${segment.usersCount}',
                  meFontStyle: MeFontStyle.E8,
                ),
                const SizedBox(width: 16),
                const Icon(Icons.access_time, size: 18),
                const SizedBox(width: 6),
                MeText(
                  text: _timeAgo(segment.cloudUpdatedAt),
                  meFontStyle: MeFontStyle.E8,
                ),
              ],
            ),
          ],
        ),
        _ActionButtons(
          segmentId: segment.id,
          segmentName: segment.name,
          onRefresh: onRefresh,
          isDeleted: segment.deletedAt != null,
        ),
      ],
    );
  }

  Widget _sectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: MeText(
        text: title,
        meFontStyle: MeFontStyle.F7,
      ),
    );
  }

  String _timeAgo(Timestamp timestamp) {
    final diff = DateTime.now().difference(timestamp.toDate());
    if (diff.inDays > 0) return '${diff.inDays}d';
    if (diff.inHours > 0) return '${diff.inHours}h';
    return '${diff.inMinutes}m';
  }
}

// Action buttons widget
class _ActionButtons extends StatelessWidget {
  const _ActionButtons({
    required this.segmentId,
    required this.segmentName,
    required this.onRefresh,
    required this.isDeleted,
  });

  final String segmentId;
  final String segmentName;
  final VoidCallback onRefresh;
  final bool isDeleted;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final hasWritePermission = hasPermission(
      context: context,
      permissionType: PermissionType.segments,
      isWrite: true,
    );

    return Row(
      children: [
        RefreshButton(
          onRefresh: onRefresh,
          isDeleted: isDeleted,
        ),
        MeIconButton(
          key: const ValueKey('download'),
          iconPath: Assets.svg.downloadIcon.path,
          onPressed: () {
            if (isDeleted) {
              _showSegmentDeletedDialog(context);
              return;
            }
            _showDownloadConfirmationDialog(context, segmentName);
          },
          buttonColor: colorScheme.color5,
          iconColor: colorScheme.color1,
        ),
        const SizedBox(width: 10),
        if (hasWritePermission) ...[
          MeIconButton(
            key: const ValueKey('remove'),
            iconPath: Assets.svg.removeUser.path,
            onPressed: () {
              if (isDeleted) {
                _showSegmentDeletedDialog(context);
                return;
              }
              _showRemoveUsersDialog(context, segmentName, segmentId);
            },
            buttonColor: colorScheme.color5,
            iconColor: colorScheme.color1,
          ),
          const SizedBox(width: 10),
          MeIconButton(
            key: const ValueKey('add'),
            iconPath: Assets.svg.addUser.path,
            onPressed: () {
              if (isDeleted) {
                _showSegmentDeletedDialog(context);
                return;
              }
              _showAddUsersDialog(context, segmentName, segmentId);
            },
            buttonColor: colorScheme.color5,
            iconColor: colorScheme.color1,
          ),
          const SizedBox(width: 10),
        ],
      ],
    );
  }

  void _showSegmentDeletedDialog(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        backgroundColor: colorScheme.color5,
        title: const MeText(
          text: 'Segment Deleted',
          meFontStyle: MeFontStyle.E8,
        ),
        content: const MeText(
          text:
              'This segment has been deleted and no actions can be performed.',
          meFontStyle: MeFontStyle.E8,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _showAddUsersDialog(
    BuildContext context,
    String segmentName,
    String segmentId,
  ) async {
    PlatformFile? selectedFile;
    Uint8List? selectedFileBytes;
    String? selectedFileName;
    List<String> uuidList = [];
    List<String> allUuids = []; // Store all UIDs including duplicates
    int totalUuidCount = 0;
    final TextEditingController purposeController = TextEditingController();
    bool isLoading = false;

    // Capture the providers before showing the dialog
    final databaseRepository = context.read<DatabaseRepository>();
    final segmentDetailsCubit = context.read<SegmentDetailsCubit>();

    await showDialog(
      context: context,
      builder: (context) {
        final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
        return StatefulBuilder(
          builder: (context, setState) {
            Future<void> pickCSVFile() async {
              final result = await FilePicker.platform.pickFiles(
                type: FileType.custom,
                allowedExtensions: ['csv'],
                withData: true,
              );

              if (result != null && result.files.isNotEmpty) {
                final file = result.files.first;
                final bytes = file.bytes;
                if (bytes != null) {
                  final csvString = utf8.decode(bytes);
                  final rows =
                      const CsvToListConverter(eol: '\n', fieldDelimiter: ',')
                          .convert(csvString, shouldParseNumbers: false);

                  // Get all UIDs including duplicates for total count
                  allUuids = rows
                      .expand((row) => row)
                      .whereType<String>()
                      .map((uid) => uid.trim())
                      .where((uid) => uid.isNotEmpty)
                      .toList();

                  // Get unique UIDs for processing
                  uuidList = allUuids.toSet().toList();
                  totalUuidCount = allUuids.length;

                  setState(() {
                    selectedFile = file;
                    selectedFileBytes = bytes;
                    selectedFileName = file.name;
                  });
                }
              }
            }

            return Dialog(
              backgroundColor: colorScheme.color5,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: SizedBox(
                width: 400,
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Align(
                        alignment: Alignment.centerLeft,
                        child: MeText(
                          text: 'Add Users',
                          meFontStyle: MeFontStyle.E8,
                        ),
                      ),
                      const SizedBox(height: 20),
                      TextField(
                        controller: purposeController,
                        style: TextStyle(color: colorScheme.color2),
                        decoration: InputDecoration(
                          hintText: 'Reason',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade400),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade400),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: Colors.grey.shade400,
                              width: 1.5,
                            ),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 14,
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      GestureDetector(
                        onTap: pickCSVFile,
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                            vertical: 14,
                            horizontal: 12,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: MeText(
                                  text: selectedFileName ?? '.csv',
                                  meFontStyle: MeFontStyle.E8,
                                ),
                              ),
                              const Icon(Icons.people_alt_outlined),
                              const SizedBox(width: 6),
                              MeText(
                                text: '$totalUuidCount',
                                meFontStyle: MeFontStyle.E8,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('Cancel'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: colorScheme.color1,
                              foregroundColor: Colors.white,
                            ),
                            onPressed: isLoading
                                ? null
                                : () async {
                                    if (selectedFile == null ||
                                        selectedFileBytes == null ||
                                        purposeController.text.trim().isEmpty) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'Please select a CSV file and enter purpose',
                                          ),
                                        ),
                                      );
                                      return;
                                    }

                                    try {
                                      setState(() => isLoading = true);
                                      const uuid = Uuid();
                                      final segmentActivityId = uuid.v4();
                                      final auth = FirebaseAuth.instance;
                                      final currentUser = auth.currentUser;
                                      final createdBy = currentUser!.uid;

                                      final filePath =
                                          'supportApp/userSegmentActivities/$segmentActivityId/${selectedFile!.name}';
                                      final ref = FirebaseStorage.instance
                                          .ref()
                                          .child(filePath);
                                      await ref.putData(selectedFileBytes!);

                                      final Set<String> uniqueUids =
                                          uuidList.toSet();

                                      // Validate UIDs (Firebase Auth UIDs should be 28 characters)
                                      final validUids = <String>[];
                                      final invalidUids = <String>[];

                                      for (final uid in uniqueUids) {
                                        if (uid.length == 28 &&
                                            uid.isNotEmpty) {
                                          validUids.add(uid);
                                        } else {
                                          invalidUids.add(uid);
                                        }
                                      }

                                      // Calculate total invalid count from original CSV (including duplicates)
                                      final totalInvalidCount = allUuids
                                          .where(
                                            (uid) =>
                                                uid.length != 28 || uid.isEmpty,
                                          )
                                          .length;

                                      if (!context.mounted) {
                                        return;
                                      }

                                      // Add users to segment
                                      final addedCount =
                                          await databaseRepository
                                              .addUsersToSegment(
                                        segmentName: segmentName,
                                        userIds: validUids,
                                      );

                                      // Refresh segment count
                                      final finalCount =
                                          await databaseRepository
                                              .refreshSegmentUserCount(
                                        segmentId: segmentId,
                                        segmentName: segmentName,
                                      );

                                      // Create activity record
                                      await databaseRepository
                                          .createSegmentActivity(
                                        id: segmentActivityId,
                                        userSegmentId: segmentId,
                                        activityTitle:
                                            purposeController.text.trim(),
                                        csvPath: filePath,
                                        csvUserCount: addedCount,
                                        updatedUserCount: finalCount,
                                        createdBy: createdBy,
                                        activityType:
                                            SegmentActivityType.addition.value,
                                      );

                                      if (context.mounted) {
                                        Navigator.of(context).pop();

                                        // Refresh segment data
                                        segmentDetailsCubit
                                            .refreshSegmentCount();

                                        String message =
                                            'Added $addedCount users successfully';
                                        final totalFailedCount =
                                            totalUuidCount - addedCount;
                                        if (totalInvalidCount > 0) {
                                          message +=
                                              ' ($totalInvalidCount invalid UIDs skipped)';
                                        }
                                        if (totalFailedCount > 0) {
                                          message +=
                                              ' ($totalFailedCount users failed to add)';
                                        }

                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(message),
                                            duration:
                                                const Duration(seconds: 4),
                                          ),
                                        );
                                      }
                                    } catch (e) {
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content:
                                                Text('Failed to add users: $e'),
                                            duration:
                                                const Duration(seconds: 4),
                                          ),
                                        );
                                      }
                                    } finally {
                                      setState(() => isLoading = false);
                                    }
                                  },
                            child: isLoading
                                ? const SizedBox(
                                    width: 18,
                                    height: 18,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                : const Text('Add'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _showRemoveUsersDialog(
    BuildContext context,
    String segmentName,
    String segmentId,
  ) async {
    PlatformFile? selectedFile;
    Uint8List? selectedFileBytes;
    String? selectedFileName;
    List<String> uuidList = [];
    List<String> allUuids = []; // Store all UIDs including duplicates
    int totalUuidCount = 0;
    final TextEditingController purposeController = TextEditingController();
    bool isLoading = false;

    // Capture the providers before showing the dialog
    final databaseRepository = context.read<DatabaseRepository>();
    final segmentDetailsCubit = context.read<SegmentDetailsCubit>();

    await showDialog(
      context: context,
      builder: (context) {
        final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
        return StatefulBuilder(
          builder: (context, setState) {
            Future<void> pickCSVFile() async {
              final result = await FilePicker.platform.pickFiles(
                type: FileType.custom,
                allowedExtensions: ['csv'],
                withData: true,
              );

              if (result != null && result.files.isNotEmpty) {
                final file = result.files.first;
                final bytes = file.bytes;
                if (bytes != null) {
                  final csvString = utf8.decode(bytes);
                  final rows = const CsvToListConverter(
                    eol: '\n',
                    fieldDelimiter: ',',
                  ).convert(csvString, shouldParseNumbers: false);

                  // Get all UIDs including duplicates for total count
                  allUuids = rows
                      .expand((row) => row)
                      .whereType<String>()
                      .map((uid) => uid.trim())
                      .where((uid) => uid.isNotEmpty)
                      .toList();

                  // Get unique UIDs for processing
                  uuidList = allUuids.toSet().toList();
                  totalUuidCount = allUuids.length;

                  setState(() {
                    selectedFile = file;
                    selectedFileBytes = bytes;
                    selectedFileName = file.name;
                  });
                }
              }
            }

            return Dialog(
              backgroundColor: colorScheme.color5,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              child: SizedBox(
                width: 400,
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Align(
                        alignment: Alignment.centerLeft,
                        child: MeText(
                          text: 'Remove Users',
                          meFontStyle: MeFontStyle.E8,
                        ),
                      ),
                      const SizedBox(height: 20),
                      TextField(
                        controller: purposeController,
                        style: TextStyle(color: colorScheme.color2),
                        decoration: InputDecoration(
                          hintText: 'Reason',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade400),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade400),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(
                              color: Colors.grey.shade400,
                              width: 1.5,
                            ),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 14,
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),
                      GestureDetector(
                        onTap: pickCSVFile,
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(
                            vertical: 14,
                            horizontal: 12,
                          ),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colors.grey.shade400),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                child: MeText(
                                  text: selectedFileName ?? '.csv',
                                  meFontStyle: MeFontStyle.E8,
                                ),
                              ),
                              const Icon(Icons.people_alt_outlined),
                              const SizedBox(width: 6),
                              MeText(
                                text: '$totalUuidCount',
                                meFontStyle: MeFontStyle.E8,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(height: 24),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('Cancel'),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.red.shade600,
                              foregroundColor: Colors.white,
                            ),
                            onPressed: isLoading
                                ? null
                                : () async {
                                    if (selectedFile == null ||
                                        selectedFileBytes == null ||
                                        purposeController.text.trim().isEmpty) {
                                      ScaffoldMessenger.of(context)
                                          .showSnackBar(
                                        const SnackBar(
                                          content: Text(
                                            'Please select a CSV file and enter purpose',
                                          ),
                                        ),
                                      );
                                      return;
                                    }

                                    try {
                                      setState(() => isLoading = true);
                                      const uuid = Uuid();
                                      final segmentActivityId = uuid.v4();
                                      final auth = FirebaseAuth.instance;
                                      final currentUser = auth.currentUser;
                                      final createdBy = currentUser!.uid;

                                      final filePath =
                                          'supportApp/userSegmentActivities/$segmentActivityId/${selectedFile!.name}';
                                      final ref = FirebaseStorage.instance
                                          .ref()
                                          .child(filePath);
                                      await ref.putData(selectedFileBytes!);

                                      final Set<String> uniqueUids =
                                          uuidList.toSet();

                                      // Validate UIDs (Firebase Auth UIDs should be 28 characters)
                                      final validUids = <String>[];
                                      final invalidUids = <String>[];

                                      for (final uid in uniqueUids) {
                                        if (uid.length == 28 &&
                                            uid.isNotEmpty) {
                                          validUids.add(uid);
                                        } else {
                                          invalidUids.add(uid);
                                        }
                                      }

                                      // Calculate total invalid count from original CSV (including duplicates)
                                      final totalInvalidCount = allUuids
                                          .where(
                                            (uid) =>
                                                uid.length != 28 || uid.isEmpty,
                                          )
                                          .length;

                                      // Debug logging
                                      if (invalidUids.isNotEmpty) {}

                                      if (!context.mounted) {
                                        return;
                                      }

                                      // Remove users from segment
                                      final removedCount =
                                          await databaseRepository
                                              .removeUsersFromSegment(
                                        segmentName: segmentName,
                                        userIds: validUids,
                                      );

                                      // Refresh segment count
                                      final finalCount =
                                          await databaseRepository
                                              .refreshSegmentUserCount(
                                        segmentId: segmentId,
                                        segmentName: segmentName,
                                      );

                                      // Create activity record
                                      await databaseRepository
                                          .createSegmentActivity(
                                        id: segmentActivityId,
                                        userSegmentId: segmentId,
                                        activityTitle:
                                            purposeController.text.trim(),
                                        csvPath: filePath,
                                        csvUserCount: removedCount,
                                        updatedUserCount: finalCount,
                                        createdBy: createdBy,
                                        activityType:
                                            SegmentActivityType.removal.value,
                                      );

                                      if (context.mounted) {
                                        Navigator.of(context).pop();

                                        // Refresh segment data
                                        segmentDetailsCubit
                                            .refreshSegmentCount();

                                        String message =
                                            'Removed $removedCount users successfully';
                                        final totalFailedCount =
                                            totalUuidCount - removedCount;
                                        if (totalInvalidCount > 0) {
                                          message +=
                                              ' ($totalInvalidCount invalid UIDs skipped)';
                                        }
                                        if (totalFailedCount > 0) {
                                          message +=
                                              ' ($totalFailedCount users failed to remove)';
                                        }

                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(message),
                                            duration:
                                                const Duration(seconds: 4),
                                          ),
                                        );
                                      }
                                    } catch (e) {
                                      if (context.mounted) {
                                        ScaffoldMessenger.of(context)
                                            .showSnackBar(
                                          SnackBar(
                                            content: Text(
                                              'Failed to remove users: $e',
                                            ),
                                            duration:
                                                const Duration(seconds: 4),
                                          ),
                                        );
                                      }
                                    } finally {
                                      setState(() => isLoading = false);
                                    }
                                  },
                            child: isLoading
                                ? const SizedBox(
                                    width: 18,
                                    height: 18,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.white,
                                      ),
                                    ),
                                  )
                                : const Text('Remove'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            );
          },
        );
      },
    );
  }

  Future<void> _showDownloadConfirmationDialog(
    BuildContext context,
    String segmentName,
  ) async {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: colorScheme.color5,
        title: const MeText(
          text: 'Download CSV',
          meFontStyle: MeFontStyle.E8,
        ),
        content: const MeText(
          text: 'Download list of user UIDs for this segment?',
          meFontStyle: MeFontStyle.E8,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Download'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    final usersSnapshot = await FirebaseFirestore.instance
        .collection('usersMetadata')
        .where('userSegments', arrayContains: segmentName)
        .get();

    final uids = <String>{};
    for (var doc in usersSnapshot.docs) {
      uids.add(doc.id);
    }

    final csvContent = uids.join(',');

    // Web download logic using anchor element
    final blob = html.Blob([csvContent]);
    final url = html.Url.createObjectUrlFromBlob(blob);

    // Create anchor element and trigger download
    html.AnchorElement(href: url)
      ..setAttribute('download', '${segmentName}_users.csv')
      ..click();

    // Clean up the URL after download
    html.Url.revokeObjectUrl(url);

    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('CSV download started')),
      );
    }
  }
}

// Activities section widget
class _ActivitiesSection extends StatelessWidget {
  const _ActivitiesSection({
    required this.activities,
    required this.supportUsers,
    required this.isLoading,
    required this.segmentId,
    required this.isSegmentDeleted,
  });

  final List<SegmentActivity> activities;
  final Map<String, dynamic> supportUsers;
  final bool isLoading;
  final String segmentId;
  final bool isSegmentDeleted;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(left: 40, right: 18),
      child: isLoading
          ? const SizedBox(
              height: 40,
              child: Center(
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          : activities.isEmpty
              ? const Padding(
                  padding: EdgeInsets.symmetric(vertical: 16),
                  child: MeText(
                    text: 'No activity yet',
                    meFontStyle: MeFontStyle.E8,
                  ),
                )
              : Column(
                  children: activities.map((activity) {
                    final userName = supportUsers[activity.createdBy]?.sname ??
                        activity.createdBy;

                    return _ActivityItem(
                      activity: activity,
                      userName: userName,
                      isSegmentDeleted: isSegmentDeleted,
                    );
                  }).toList(),
                ),
    );
  }
}

// Activity item widget
class _ActivityItem extends StatelessWidget {
  const _ActivityItem({
    required this.activity,
    required this.userName,
    required this.isSegmentDeleted,
  });

  final SegmentActivity activity;
  final String userName;
  final bool isSegmentDeleted;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 10),
          child: Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    MeText(
                      text: activity.activityTitle,
                      meFontStyle: MeFontStyle.E8,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        activity.activityType == SegmentActivityType.addition
                            ? SvgPicture.asset(
                                Assets.svg.addUser.path,
                                colorFilter: ColorFilter.mode(
                                  colorScheme.color1,
                                  BlendMode.srcIn,
                                ),
                              )
                            : SvgPicture.asset(
                                Assets.svg.removeUser.path,
                                colorFilter: ColorFilter.mode(
                                  colorScheme.color1,
                                  BlendMode.srcIn,
                                ),
                              ),
                        const SizedBox(width: 6),
                        MeText(
                          text: '${activity.csvUserCount}',
                          meFontStyle: MeFontStyle.E8,
                        ),
                        const SizedBox(width: 16),
                        const Icon(Icons.access_time, size: 16),
                        const SizedBox(width: 4),
                        MeText(
                          text: _timeAgo(activity.createdAt),
                          meFontStyle: MeFontStyle.E8,
                        ),
                        const SizedBox(width: 16),
                        IconButton(
                          icon: const Icon(Icons.download_rounded, size: 18),
                          onPressed: () {
                            if (isSegmentDeleted) {
                              _showSegmentDeletedDialog(context);
                              return;
                            }
                            _downloadCsvFromPath(activity.csvPath);
                          },
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              MeText(
                text: userName,
                meFontStyle: MeFontStyle.F7,
              ),
            ],
          ),
        ),
        const Divider(),
      ],
    );
  }

  String _timeAgo(Timestamp timestamp) {
    final diff = DateTime.now().difference(timestamp.toDate());
    if (diff.inDays > 0) return '${diff.inDays}d';
    if (diff.inHours > 0) return '${diff.inHours}h';
    return '${diff.inMinutes}m';
  }

  void _showSegmentDeletedDialog(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        backgroundColor: colorScheme.color5,
        title: const MeText(
          text: 'Segment Deleted',
          meFontStyle: MeFontStyle.E8,
        ),
        content: const MeText(
          text:
              'This segment has been deleted and no actions can be performed.',
          meFontStyle: MeFontStyle.E8,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  Future<void> _downloadCsvFromPath(String path) async {
    try {
      final ref = FirebaseStorage.instance.ref().child(path);
      final downloadUrl = await ref.getDownloadURL();

      // Create anchor element and trigger download
      html.AnchorElement(href: downloadUrl)
        ..setAttribute('download', path.split('/').last)
        ..click();

      debugPrint('Download triggered for $downloadUrl');
    } catch (e) {
      debugPrint('Error downloading CSV from Firebase Storage: $e');
    }
  }
}

// Refresh button widget
class RefreshButton extends StatefulWidget {
  const RefreshButton({
    super.key,
    required this.onRefresh,
    this.isDeleted = false,
  });

  final VoidCallback onRefresh;
  final bool isDeleted;

  @override
  State<RefreshButton> createState() => _RefreshButtonState();
}

class _RefreshButtonState extends State<RefreshButton>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller;
  bool _isRefreshing = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 500),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  Future<void> _refreshSegment() async {
    if (_isRefreshing) return;

    // Check if segment is deleted and show dialog
    if (widget.isDeleted) {
      _showSegmentDeletedDialog(context);
      return;
    }

    setState(() {
      _isRefreshing = true;
      _controller.repeat();
    });

    try {
      widget.onRefresh();
      await Future.delayed(const Duration(seconds: 1));
    } catch (e) {
      debugPrint('Error refreshing: $e');
    }

    setState(() {
      _isRefreshing = false;
      _controller.reset();
    });
  }

  void _showSegmentDeletedDialog(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    showDialog(
      context: context,
      builder: (ctx) => AlertDialog(
        backgroundColor: colorScheme.color5,
        title: const MeText(
          text: 'Segment Deleted',
          meFontStyle: MeFontStyle.E8,
        ),
        content: const MeText(
          text:
              'This segment has been deleted and no actions can be performed.',
          meFontStyle: MeFontStyle.E8,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(ctx).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return IconButton(
      icon: RotationTransition(
        turns: _controller,
        child: SvgPicture.asset(
          Assets.svg.refreshIcon.path,
          colorFilter: ColorFilter.mode(
            colorScheme.color1,
            BlendMode.srcIn,
          ),
        ),
      ),
      onPressed: _refreshSegment,
    );
  }
}
