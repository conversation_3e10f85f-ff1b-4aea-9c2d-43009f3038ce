import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/blocs/app/app_bloc.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/providers/admin/admin_data_provider.dart';
import 'package:mevolvesupport/providers/admin/admin_data_state.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/ui/empty_list_screen.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_list_bottom_bar.dart';
import 'package:mevolvesupport/widgets/screens/admin_panel/dialogs/admin_panel_permissions_dialog.dart';

class AdminList extends StatefulWidget {
  const AdminList({super.key});

  @override
  State<AdminList> createState() => _AdminListState();
}

class _AdminListState extends State<AdminList>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  String _selectedSupportUserId = '';

  @override
  void initState() {
    super.initState();
    // Initialize
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Log.d('🎬 AdminList: Initializing AdminDataProvider');
      context.read<AdminDataProvider>().initialize();
    });
  }

  /// Determine which users should be disabled based on duplicate emails
  /// Returns a map of user ID -> isDisabled
  Map<String, bool> _getDisabledUsers(List<SupportUser> supportUsers) {
    final Map<String, bool> disabledUsers = {};
    final Map<String, List<SupportUser>> emailGroups = {};

    // Group users by email
    for (final user in supportUsers) {
      if (!emailGroups.containsKey(user.email)) {
        emailGroups[user.email] = [];
      }
      emailGroups[user.email]!.add(user);
    }

    // For each email group, disable all but the most recent user
    for (final email in emailGroups.keys) {
      final usersWithEmail = emailGroups[email]!;

      if (usersWithEmail.length > 1) {
        // Sort by lastLogin (most recent first)
        usersWithEmail.sort((a, b) => b.lastLogin.compareTo(a.lastLogin));

        // The first user (most recent) stays enabled, others are disabled
        for (int i = 0; i < usersWithEmail.length; i++) {
          disabledUsers[usersWithEmail[i].sid] =
              i > 0; // Disable all except first
        }
      } else {
        // Single user with this email - keep enabled
        disabledUsers[usersWithEmail.first.sid] = false;
      }
    }

    return disabledUsers;
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<AdminDataProvider, AdminDataState>(
      builder: (context, state) {
        Log.d(
          '🎨 AdminList: UI rebuild - ${state.supportUsers.length} support users, total: ${state.totalCount}, hasMore: ${state.hasMore}, loading: ${state.isLoading}',
        );

        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.error != null) {
          return _ErrorWidget(error: state.error!);
        }

        if (state.supportUsers.isEmpty) {
          return Container(
            color: colorScheme.color6,
            child: EmptyListScreen(
              title: 'No support users found',
              iconPath: Assets.svg.personIcon.path,
            ),
          );
        }

        // Calculate disabled users based on duplicate emails
        final disabledUsers = _getDisabledUsers(state.supportUsers);

        return Column(
          children: [
            Expanded(
              child: _AdminDataList(
                supportUsers: state.supportUsers,
                selectedSupportUserId: _selectedSupportUserId,
                disabledUsers: disabledUsers,
                onSupportUserSelected: (supportUser) {
                  setState(() {
                    _selectedSupportUserId = supportUser.sid;
                  });
                },
              ),
            ),
            if (state.supportUsers.isNotEmpty)
              MeListBottomBar(
                currentCount: state.supportUsers.length,
                totalCount: state.totalCount,
                hasMore: state.hasMore,
                isLoading: state.isLoadingMore,
                hasNewUpdates: state.hasNewUpdates,
                onLoadMore: () => context.read<AdminDataProvider>().loadMore(),
                onRefresh: () => context.read<AdminDataProvider>().refresh(),
                itemType: 'Support Users',
              ),
          ],
        );
      },
    );
  }
}

// Separated data list widget for better organization
class _AdminDataList extends StatefulWidget {
  const _AdminDataList({
    required this.supportUsers,
    required this.selectedSupportUserId,
    required this.disabledUsers,
    required this.onSupportUserSelected,
  });

  final List<SupportUser> supportUsers;
  final String selectedSupportUserId;
  final Map<String, bool> disabledUsers;
  final Function(SupportUser supportUser) onSupportUserSelected;

  @override
  State<_AdminDataList> createState() => _AdminDataListState();
}

class _AdminDataListState extends State<_AdminDataList> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final currentSupportUser =
        BlocProvider.of<AppBloc>(context).state.supportUser!;

    return Column(
      children: [
        // Header
        Container(
          color: colorScheme.color5,
          child: const Padding(
            padding: EdgeInsets.all(16.0),
            child: Row(
              children: [
                SizedBox(
                  width: 250,
                  child: MeText(text: 'Name', meFontStyle: MeFontStyle.B7),
                ),
                SizedBox(
                  width: 300,
                  child: MeText(text: 'Email', meFontStyle: MeFontStyle.B7),
                ),
                SizedBox(
                  width: 200,
                  child:
                      MeText(text: 'Display Name', meFontStyle: MeFontStyle.B7),
                ),
                Expanded(
                  child: MeText(
                    text: 'Accessible',
                    meFontStyle: MeFontStyle.B7,
                  ),
                ),
              ],
            ),
          ),
        ),
        // List
        Expanded(
          child: Container(
            color: colorScheme.color6,
            child: ListView.builder(
              controller: _scrollController,
              shrinkWrap: true,
              itemCount: widget.supportUsers.length,
              itemBuilder: (context, index) {
                final supportUser = widget.supportUsers[index];
                final isSelected =
                    widget.selectedSupportUserId == supportUser.sid;
                final isDisabled =
                    widget.disabledUsers[supportUser.sid] ?? false;

                return _SupportUserItem(
                  supportUser: supportUser,
                  currentSupportUser: currentSupportUser,
                  isSelected: isSelected,
                  isDisabled: isDisabled,
                  index: index,
                  onTap: () {
                    widget.onSupportUserSelected(supportUser);
                  },
                );
              },
            ),
          ),
        ),
      ],
    );
  }
}

// Support user item widget
class _SupportUserItem extends StatelessWidget {
  const _SupportUserItem({
    required this.supportUser,
    required this.currentSupportUser,
    required this.isSelected,
    required this.isDisabled,
    required this.index,
    required this.onTap,
  });

  final SupportUser supportUser;
  final SupportUser currentSupportUser;
  final bool isSelected;
  final bool isDisabled;
  final int index;
  final VoidCallback onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    // Determine colors based on state
    Color backgroundColor;
    if (isDisabled) {
      backgroundColor = colorScheme.color4; // Disabled background
    } else if (isSelected) {
      backgroundColor = colorScheme.color1; // Selected background
    } else {
      backgroundColor = colorScheme.color5; // Normal background
    }

    return Column(
      children: [
        if (index > 0)
          Divider(
            thickness: 1,
            height: 1,
            color: colorScheme.color6,
          ),
        InkWell(
          onTap: isDisabled
              ? null
              : () async {
                  // Select the item
                  onTap();

                  // Show permissions dialog
                  await showDialog<void>(
                    context: context,
                    barrierColor: Colors.black.withValues(alpha: 0.7),
                    builder: (_) {
                      return AdminPanelPermissionsDialog(
                        supportUser: supportUser,
                      );
                    },
                    useRootNavigator: false,
                  );
                },
          child: Container(
            color: backgroundColor,
            padding: const EdgeInsets.all(16.0),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Name column
                SizedBox(
                  width: 250,
                  child: Row(
                    children: [
                      Flexible(
                        child: MeText(
                          text: supportUser.sname,
                          meFontStyle: _getTextStyle(
                            isSelected,
                            isDisabled,
                            MeFontStyle.C8,
                            MeFontStyle.C12,
                          ),
                          maxLines: 2,
                          textOverflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (supportUser.sid == currentSupportUser.sid)
                        MeText(
                          text: ' (You)',
                          meFontStyle: _getTextStyle(
                            isSelected,
                            isDisabled,
                            MeFontStyle.C7,
                            MeFontStyle.C11,
                          ),
                        ),
                      if (isDisabled)
                        const Padding(
                          padding: EdgeInsets.only(left: 8.0),
                          child: MeText(
                            text: '(Inactive)',
                            meFontStyle: MeFontStyle.F11,
                          ),
                        ),
                    ],
                  ),
                ),
                // Email column
                SizedBox(
                  width: 300,
                  child: MeText(
                    text: supportUser.email,
                    meFontStyle: _getTextStyle(
                      isSelected,
                      isDisabled,
                      MeFontStyle.C8,
                      MeFontStyle.C12,
                    ),
                    maxLines: 2,
                    textOverflow: TextOverflow.ellipsis,
                  ),
                ),
                // Display Name column (sname)
                SizedBox(
                  width: 200,
                  child: MeText(
                    text: supportUser.sname,
                    meFontStyle: _getTextStyle(
                      isSelected,
                      isDisabled,
                      MeFontStyle.C8,
                      MeFontStyle.C12,
                    ),
                    maxLines: 1,
                    textOverflow: TextOverflow.ellipsis,
                  ),
                ),
                // Permissions column
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (supportUser.permissions.isEmpty)
                        MeText(
                          text: 'None',
                          meFontStyle: _getTextStyle(
                            isSelected,
                            isDisabled,
                            MeFontStyle.B7,
                            MeFontStyle.B12,
                          ),
                        )
                      else
                        ...List.generate(
                          PermissionType.values.length,
                          (permIndex) => getAccessfromPermission(
                                    permissionType:
                                        PermissionType.values[permIndex],
                                    permissions: supportUser.permissions,
                                  ) ==
                                  'None'
                              ? const SizedBox()
                              : Padding(
                                  padding: EdgeInsets.only(
                                    top: permIndex != 0 ? 8 : 0,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      SizedBox(
                                        width: 130,
                                        child: MeText(
                                          text: PermissionType.values[permIndex]
                                              .toReadableString(),
                                          meFontStyle: _getTextStyle(
                                            isSelected,
                                            isDisabled,
                                            MeFontStyle.B8,
                                            MeFontStyle.B12,
                                          ),
                                        ),
                                      ),
                                      const SizedBox(width: 16),
                                      MeText(
                                        text: '-',
                                        meFontStyle: _getTextStyle(
                                          isSelected,
                                          isDisabled,
                                          MeFontStyle.B8,
                                          MeFontStyle.B12,
                                        ),
                                      ),
                                      const SizedBox(width: 32),
                                      MeText(
                                        text: getAccessfromPermission(
                                          permissionType:
                                              PermissionType.values[permIndex],
                                          permissions: supportUser.permissions,
                                        ),
                                        meFontStyle: _getTextStyle(
                                          isSelected,
                                          isDisabled,
                                          MeFontStyle.B7,
                                          MeFontStyle.B11,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  /// Helper method to determine text style based on state
  MeFontStyle _getTextStyle(
    bool isSelected,
    bool isDisabled,
    MeFontStyle normalStyle,
    MeFontStyle selectedStyle,
  ) {
    if (isDisabled) {
      return MeFontStyle.F7; // Disabled style (grayed out)
    } else if (isSelected) {
      return selectedStyle; // Selected style
    } else {
      return normalStyle; // Normal style
    }
  }
}

// Error widget
class _ErrorWidget extends StatelessWidget {
  const _ErrorWidget({required this.error});

  final String error;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('Error: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<AdminDataProvider>().refresh();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
