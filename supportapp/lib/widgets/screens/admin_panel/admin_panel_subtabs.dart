import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/providers/admin/admin_data_state.dart';
import 'package:mevolvesupport/providers/admin/admin_data_provider.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class AdminListSubtabs extends StatefulWidget {
  const AdminListSubtabs({super.key});

  @override
  State<AdminListSubtabs> createState() => _AdminListSubtabsState();
}

class _AdminListSubtabsState extends State<AdminListSubtabs> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          BlocBuilder<AdminDataProvider, AdminDataState>(
            builder: (context, state) {
              return Row(
                children: [
                  const MeText(
                    text: 'Admin Panel',
                    meFontStyle: MeFontStyle.A1,
                  ),
                  const SizedBox(width: 12),
                  MeText(
                    text: '${state.totalCount} Users',
                    meFontStyle: MeFontStyle.B1,
                  ),
                ],
              );
            },
          ),
          // const SizedBox(width: 24),
          // Expanded(
          //   child: Container(
          //     height: 44,
          //     decoration: BoxDecoration(
          //       color: colorScheme.color6,
          //       borderRadius: BorderRadius.circular(8),
          //       border: Border.all(
          //         color: colorScheme.color7.withValues(alpha: 0.3),
          //       ),
          //     ),
          //     child: TextField(
          //       controller: _searchController,
          //       style: TextStyle(color: colorScheme.color2),
          //       decoration: InputDecoration(
          //         hintText: 'Search support person',
          //         hintStyle: TextStyle(color: colorScheme.color7),
          //         prefixIcon: Icon(
          //           Icons.search,
          //           color: colorScheme.color7,
          //           size: 20,
          //         ),
          //         border: InputBorder.none,
          //         contentPadding: const EdgeInsets.symmetric(
          //           horizontal: 16,
          //           vertical: 12,
          //         ),
          //       ),
          //       onChanged: (value) {
          //         // Update search in the provider
          //         context
          //             .read<AdminDataProvider>()
          //             .changeFilter(searchQuery: value);
          //       },
          //     ),
          //   ),
          // ),
        ],
      ),
    );
  }
}
