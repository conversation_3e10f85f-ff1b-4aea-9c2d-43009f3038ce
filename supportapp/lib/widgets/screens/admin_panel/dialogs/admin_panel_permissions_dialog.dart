import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/blocs/app/app_bloc.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/dialogs/show_me_discard_dialog.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_radio_button.dart';

class AdminPanelPermissionsDialog extends StatefulWidget {
  const AdminPanelPermissionsDialog({
    super.key,
    required this.supportUser,
  });

  final SupportUser supportUser;

  @override
  State<AdminPanelPermissionsDialog> createState() =>
      _AdminPanelPermissionsDialogState();
}

class _AdminPanelPermissionsDialogState
    extends State<AdminPanelPermissionsDialog> {
  late SupportUser supportUser;
  late List<String> permissions;
  bool isLoading = false;

  @override
  void initState() {
    supportUser = widget.supportUser;
    permissions = supportUser.permissions.toList();
    super.initState();
  }

  /// Check if current user has write permission for admin panel
  bool _hasWritePermission(BuildContext context) {
    return hasPermission(
      context: context,
      permissionType: PermissionType.adminPanel,
      isWrite: true,
    );
  }

  @override
  Widget build(BuildContext mainContext) {
    final colorScheme = Theme.of(mainContext).extension<MeColorScheme>()!;
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (value, result) async {
        Future<bool> resVal() async {
          if (!setEquals(
            permissions.toSet(),
            supportUser.permissions.toSet(),
          )) {
            final pop = await showMeDiscardDialog(
                  context,
                  'Are you sure to discard the changes?',
                ) ??
                false;
            return pop;
          }
          return true;
        }

        if (!value && await resVal() && mounted) {
          Navigator.pop(context);
        }
      },
      child: AlertDialog(
        insetPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 100),
        contentPadding: EdgeInsets.zero,
        content: SizedBox(
          width: 700,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: MeText(
                          text: 'Personal details',
                          meFontStyle: MeFontStyle.B1,
                        ),
                      ),
                      Divider(
                        thickness: 1,
                        height: 1,
                        color: colorScheme.color6,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            const SizedBox(
                              width: 118,
                              child: MeText(
                                text: 'Name',
                                meFontStyle: MeFontStyle.C7,
                              ),
                            ),
                            const SizedBox(
                              width: 46,
                            ),
                            MeText(
                              text: supportUser.sname,
                              meFontStyle: MeFontStyle.C8,
                            ),
                          ],
                        ),
                      ),
                      Divider(
                        thickness: 1,
                        height: 1,
                        color: colorScheme.color6,
                      ),
                      Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            const SizedBox(
                              width: 118,
                              child: MeText(
                                text: 'Email',
                                meFontStyle: MeFontStyle.C7,
                              ),
                            ),
                            const SizedBox(
                              width: 46,
                            ),
                            MeText(
                              text: supportUser.email,
                              meFontStyle: MeFontStyle.C8,
                            ),
                          ],
                        ),
                      ),
                      Divider(
                        thickness: 4,
                        height: 4,
                        color: colorScheme.color6,
                      ),
                      const Padding(
                        padding: EdgeInsets.all(16.0),
                        child: MeText(
                          text: 'Accessible',
                          meFontStyle: MeFontStyle.B1,
                        ),
                      ),
                      ...List.generate(
                        PermissionType.values.length,
                        (index) => Column(
                          children: [
                            Divider(
                              thickness: 1,
                              height: 1,
                              color: colorScheme.color6,
                            ),
                            Padding(
                              padding: const EdgeInsets.only(left: 16.0),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  MeText(
                                    text: PermissionType.values[index]
                                        .toReadableString(),
                                    meFontStyle: MeFontStyle.C8,
                                  ),
                                  PopupMenuButton(
                                    offset: const Offset(-20, 45),
                                    padding:
                                        const EdgeInsets.symmetric(vertical: 8),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    tooltip: '',
                                    color: colorScheme.color31,
                                    constraints:
                                        const BoxConstraints(minWidth: 142),
                                    onSelected: (value) {
                                      setState(() {
                                        if (value.endsWith('_n')) {
                                          permissions.removeWhere(
                                            (element) => element.startsWith(
                                              _getValueName(value),
                                            ),
                                          );
                                        } else if (value.endsWith('_r')) {
                                          permissions.removeWhere((element) {
                                            return value.startsWith(
                                              PermissionType.allUsers.name,
                                            )
                                                ? element.startsWith(
                                                      _getValueName(value),
                                                    ) ||
                                                    element.startsWith(
                                                      PermissionType
                                                          .userDetails.name,
                                                    )
                                                : element.startsWith(
                                                    _getValueName(value),
                                                  );
                                          });
                                          permissions.add(value);
                                          if (value.startsWith(
                                            PermissionType.allUsers.name,
                                          )) {
                                            permissions.addAll([
                                              value,
                                              '${PermissionType.userDetails.name}_r',
                                              '${PermissionType.userDetails.name}_w',
                                            ]);
                                          } else {
                                            permissions.addAll([
                                              '${_getValueName(value)}_r',
                                              value,
                                            ]);
                                          }
                                        } else if (value.endsWith('_w')) {
                                          permissions.removeWhere((element) {
                                            return value.startsWith(
                                              PermissionType.allUsers.name,
                                            )
                                                ? element.startsWith(
                                                      _getValueName(value),
                                                    ) ||
                                                    element.startsWith(
                                                      PermissionType
                                                          .userDetails.name,
                                                    )
                                                : element.startsWith(
                                                    _getValueName(value),
                                                  );
                                          });
                                          if (value.startsWith(
                                            PermissionType.allUsers.name,
                                          )) {
                                            permissions.addAll([
                                              '${_getValueName(value)}_r',
                                              value,
                                              '${PermissionType.userDetails.name}_r',
                                              '${PermissionType.userDetails.name}_w',
                                            ]);
                                          } else {
                                            permissions.addAll([
                                              '${_getValueName(value)}_r',
                                              value,
                                            ]);
                                          }
                                        }
                                      });
                                    },
                                    itemBuilder: (BuildContext context) {
                                      return [
                                        PopupMenuItem(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 8,
                                          ),
                                          height: 24,
                                          value:
                                              '${PermissionType.values[index].name}_n',
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              const MeText(
                                                text: 'None',
                                                meFontStyle: MeFontStyle.C8,
                                              ),
                                              MeRadioButton(
                                                value:
                                                    '${PermissionType.values[index].name}_n',
                                                groupValue: (permissions
                                                            .contains(
                                                          '${PermissionType.values[index].name}_r',
                                                        ) ||
                                                        permissions.contains(
                                                          '${PermissionType.values[index].name}_w',
                                                        ))
                                                    ? ''
                                                    : '${PermissionType.values[index].name}_n',
                                              ),
                                            ],
                                          ),
                                        ),
                                        PopupMenuItem(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 16,
                                            vertical: 8,
                                          ),
                                          height: 24,
                                          value:
                                              '${PermissionType.values[index].name}_r',
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              MeText(
                                                text: PermissionType.values[
                                                                index] ==
                                                            PermissionType
                                                                .allUsers ||
                                                        PermissionType.values[
                                                                index] ==
                                                            PermissionType
                                                                .viewEmail
                                                    ? 'View'
                                                    : 'Read',
                                                meFontStyle: MeFontStyle.C8,
                                              ),
                                              MeRadioButton(
                                                value:
                                                    '${PermissionType.values[index].name}_r',
                                                groupValue: (permissions
                                                            .contains(
                                                          '${PermissionType.values[index].name}_r',
                                                        ) &&
                                                        !permissions.contains(
                                                          '${PermissionType.values[index].name}_w',
                                                        ))
                                                    ? '${PermissionType.values[index].name}_r'
                                                    : '',
                                              ),
                                            ],
                                          ),
                                        ),
                                        if (PermissionType.values[index] !=
                                                PermissionType.deletedReports &&
                                            PermissionType.values[index] !=
                                                PermissionType.allUsers &&
                                            PermissionType.values[index] !=
                                                PermissionType.viewEmail)
                                          PopupMenuItem(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 16,
                                              vertical: 8,
                                            ),
                                            height: 24,
                                            value:
                                                '${PermissionType.values[index].name}_w',
                                            child: Row(
                                              mainAxisAlignment:
                                                  MainAxisAlignment
                                                      .spaceBetween,
                                              children: [
                                                const MeText(
                                                  text: 'Write',
                                                  meFontStyle: MeFontStyle.C8,
                                                ),
                                                MeRadioButton(
                                                  value:
                                                      '${PermissionType.values[index].name}_w',
                                                  groupValue: (permissions
                                                              .contains(
                                                            '${PermissionType.values[index].name}_r',
                                                          ) &&
                                                          permissions.contains(
                                                            '${PermissionType.values[index].name}_w',
                                                          ))
                                                      ? '${PermissionType.values[index].name}_w'
                                                      : '',
                                                ),
                                              ],
                                            ),
                                          ),
                                      ];
                                    },
                                    child: InkWell(
                                      onTap: null,
                                      child: Padding(
                                        padding: const EdgeInsets.all(16.0),
                                        child: Row(
                                          children: [
                                            MeText(
                                              text: permissions.contains(
                                                '${PermissionType.values[index].name}_w',
                                              )
                                                  ? PermissionType.values[
                                                                  index] ==
                                                              PermissionType
                                                                  .allUsers ||
                                                          PermissionType.values[
                                                                  index] ==
                                                              PermissionType
                                                                  .viewEmail
                                                      ? 'View'
                                                      : 'Write'
                                                  : permissions.contains(
                                                      '${PermissionType.values[index].name}_r',
                                                    )
                                                      ? PermissionType.values[
                                                                      index] ==
                                                                  PermissionType
                                                                      .allUsers ||
                                                              PermissionType
                                                                          .values[
                                                                      index] ==
                                                                  PermissionType
                                                                      .viewEmail
                                                          ? 'View'
                                                          : 'Read'
                                                      : 'None',
                                              meFontStyle: permissions.contains(
                                                        '${PermissionType.values[index].name}_r',
                                                      ) ||
                                                      permissions.contains(
                                                        '${PermissionType.values[index].name}_w',
                                                      )
                                                  ? MeFontStyle.C8
                                                  : MeFontStyle.C7,
                                            ),
                                            const SizedBox(
                                              width: 8,
                                            ),
                                            SizedBox(
                                              width: 18,
                                              height: 18,
                                              child:
                                                  Assets.svg.arrowDropDown.svg(
                                                colorFilter: ColorFilter.mode(
                                                  colorScheme.color1,
                                                  BlendMode.srcIn,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Divider(
                thickness: 4,
                height: 4,
                color: colorScheme.color6,
              ),
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisSize: MainAxisSize.max,
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    _buildActionButton(
                      text: 'Cancel',
                      onTap: () async {
                        if (setEquals(
                          permissions.toSet(),
                          supportUser.permissions.toSet(),
                        )) {
                          Navigator.of(context).pop();
                        } else {
                          final pop =
                              await showMeDiscardDialog(context) ?? false;
                          if (pop && mounted) {
                            Navigator.of(context).pop();
                          }
                        }
                      },
                      backgroundColor: colorScheme.color3,
                      textStyle: MeFontStyle.B2,
                    ),
                    const SizedBox(
                      width: 16,
                    ),
                    _buildActionButton(
                      text: _hasWritePermission(mainContext) ? 'Save' : 'Close',
                      onTap: (_hasWritePermission(mainContext) && _hasChanges)
                          ? () async {
                              setState(() {
                                isLoading = true;
                              });

                              final supportUserMap = supportUser.toMap();
                              supportUserMap['permissions'] = permissions;
                              final statusCode =
                                  await DatabaseRepository().updateSupportPerms(
                                supportUserMap: supportUserMap,
                              );
                              setState(() {
                                isLoading = false;
                              });
                              if (mounted && statusCode == 200) {
                                // Refresh permissions for the current user if they updated their own permissions
                                final currentUserEmail =
                                    context.read<AppBloc>().state.user?.email;
                                if (currentUserEmail == supportUser.email) {
                                  // User updated their own permissions, refresh immediately
                                  context
                                      .read<AppBloc>()
                                      .add(const RefreshUserPermissions());
                                }
                                Navigator.of(context).pop();
                              }
                            }
                          : null,
                      backgroundColor:
                          (_hasWritePermission(mainContext) && _hasChanges)
                              ? colorScheme.color1
                              : colorScheme.color4,
                      textStyle:
                          (_hasWritePermission(mainContext) && _hasChanges)
                              ? MeFontStyle.B12
                              : MeFontStyle.D12,
                      isLoading: isLoading,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getValueName(String value) {
    return value.substring(0, value.length - 2);
  }

  /// Check if there are any changes from the original permissions
  bool get _hasChanges {
    return !setEquals(
      permissions.toSet(),
      supportUser.permissions.toSet(),
    );
  }

  Widget _buildActionButton({
    required String text,
    required VoidCallback? onTap,
    required Color backgroundColor,
    required MeFontStyle textStyle,
    bool isLoading = false,
  }) {
    return Material(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(6),
      child: InkWell(
        borderRadius: BorderRadius.circular(6),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 54, vertical: 11),
          alignment: Alignment.center,
          child: isLoading
              ? SizedBox(
                  height: 16,
                  width: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    color:
                        Theme.of(context).extension<MeColorScheme>()!.color12,
                  ),
                )
              : MeText(
                  text: text,
                  meFontStyle: textStyle,
                ),
        ),
      ),
    );
  }
}
