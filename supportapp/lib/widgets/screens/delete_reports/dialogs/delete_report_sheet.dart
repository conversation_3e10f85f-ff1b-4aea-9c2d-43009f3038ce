import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/constants/extensions.dart';
import 'package:mevolvesupport/cubits/user/user_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/common/html_read_more.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_title_bar.dart';

class DeleteReportSheet extends StatelessWidget {
  const DeleteReportSheet({
    super.key,
    required this.deletedReport,
  });

  final DeleteReport deletedReport;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Scaffold(
      backgroundColor: Colors.black.withValues(alpha: 0.5),
      body: Container(
        decoration: BoxDecoration(
          color: colorScheme.color5,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(16),
            topRight: Radius.circular(16),
          ),
        ),
        margin: const EdgeInsets.only(top: 176),
        child: Column(
          children: [
            MeTitleBar(
              title: 'Deleted reason',
              onCloseClick: () {
                context.read<UserCubit>().updateUserCubit(
                      uid: deletedReport.uid,
                      tab: 0,
                      currentDeletedReport: null,
                    );
              },
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeText(
                    text: deletedReport.reason.toReadableString(),
                    meFontStyle: MeFontStyle.A8,
                  ),
                  const SizedBox(
                    height: 24,
                  ),
                  HtmlReadMore(
                    text: deletedReport.feedback ?? '',
                    readMore: false,
                  ),
                  const SizedBox(
                    height: 24,
                  ),
                  MeText(
                    text:
                        'Deleted on ${deletedReport.deletedAt.formattedDayDateTime()}',
                    meFontStyle: MeFontStyle.E7,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
