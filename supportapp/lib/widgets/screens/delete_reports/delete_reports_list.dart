import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/cubits/user/user_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/providers/delete_reports/delete_reports_data_provider.dart';
import 'package:mevolvesupport/providers/delete_reports/delete_reports_data_state.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/ui/empty_list_screen.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_list_bottom_bar.dart';

class DeleteReportsList extends StatefulWidget {
  const DeleteReportsList({super.key});

  @override
  State<DeleteReportsList> createState() => _DeleteReportsListState();
}

class _DeleteReportsListState extends State<DeleteReportsList>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  String _selectedReportId = '';

  @override
  void initState() {
    super.initState();
    // Initialize with default filters (none - show all)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Log.d('🎬 DeleteReportsList: Initializing DeleteReportsDataProvider');
      context.read<DeleteReportsDataProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<DeleteReportsDataProvider, DeleteReportsDataState>(
      builder: (context, state) {
        Log.d(
          '🎨 DeleteReportsList: UI rebuild - ${state.deleteReports.length} reports, total: ${state.totalCount}, hasMore: ${state.hasMore}, loading: ${state.isLoading}',
        );

        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.error != null) {
          return _ErrorWidget(error: state.error!);
        }

        if (state.deleteReports.isEmpty) {
          return Container(
            color: colorScheme.color6,
            child: EmptyListScreen(
              title: 'No items found',
              iconPath: Assets.svg.emptyScreenMessages.path,
            ),
          );
        }

        return Column(
          children: [
            Expanded(
              child: _DeleteReportsDataList(
                reports: state.deleteReports,
                selectedReportId: _selectedReportId,
                onReportSelected: (report) {
                  // Update user details and navigation
                  context.read<UserDetailsCubit>().selectUser(report.uid);
                  context.read<UserCubit>().updateUserCubit(
                        uid: report.uid,
                        tab: 3,
                        currentDeletedReport: report,
                      );

                  setState(() {
                    _selectedReportId = report.id;
                  });
                },
              ),
            ),
            if (state.deleteReports.isNotEmpty)
              MeListBottomBar(
                currentCount: state.deleteReports.length,
                totalCount: state.totalCount,
                hasMore: state.hasMore,
                isLoading: state.isLoadingMore,
                hasNewUpdates: state.hasNewUpdates,
                onLoadMore: () =>
                    context.read<DeleteReportsDataProvider>().loadMore(),
                onRefresh: () =>
                    context.read<DeleteReportsDataProvider>().refresh(),
                itemType: 'Reports',
              ),
          ],
        );
      },
    );
  }
}

// Separated data list widget for better organization
class _DeleteReportsDataList extends StatefulWidget {
  const _DeleteReportsDataList({
    required this.reports,
    required this.selectedReportId,
    required this.onReportSelected,
  });

  final List<DeleteReport> reports;
  final String selectedReportId;
  final Function(DeleteReport report) onReportSelected;

  @override
  State<_DeleteReportsDataList> createState() => _DeleteReportsDataListState();
}

class _DeleteReportsDataListState extends State<_DeleteReportsDataList> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Container(
      color: colorScheme.color6,
      child: ListView.builder(
        controller: _scrollController,
        shrinkWrap: true,
        itemCount: widget.reports.length,
        itemBuilder: (context, index) {
          final report = widget.reports[index];
          final isSelected = widget.selectedReportId == report.id;

          return _DeletedReportItem(
            report: report,
            isSelected: isSelected,
            onTap: () => widget.onReportSelected(report),
            isLastItem: index == widget.reports.length - 1,
          );
        },
      ),
    );
  }
}

// Individual report item widget
class _DeletedReportItem extends StatelessWidget {
  const _DeletedReportItem({
    required this.report,
    required this.isSelected,
    required this.onTap,
    required this.isLastItem,
  });

  final DeleteReport report;
  final bool isSelected;
  final VoidCallback onTap;
  final bool isLastItem;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final isDeletedUser = report.userDeletedStatus != null &&
        (report.userDeletedStatus == UserDeletedStatus.deleted ||
            report.userDeletedStatus == UserDeletedStatus.remove_u ||
            report.userDeletedStatus == UserDeletedStatus.remove_a);

    return Column(
      children: [
        InkWell(
          onTap: onTap,
          child: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
            color: isSelected ? colorScheme.color1 : colorScheme.color5,
            child: Column(
              children: [
                // Header row with reason and status badge
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: MeText(
                        text: report.reason.toReadableString(),
                        meFontStyle:
                            isSelected ? MeFontStyle.E12 : MeFontStyle.E8,
                      ),
                    ),
                    if (report.userDeletedStatus != null)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(3),
                          color: report.userDeletedStatus ==
                                  UserDeletedStatus.deleted
                              ? colorScheme.color32
                              : colorScheme.color11,
                        ),
                        child: MeText(
                          text: report.userDeletedStatus!.toReadableString(),
                          meFontStyle: report.userDeletedStatus ==
                                  UserDeletedStatus.deleted
                              ? MeFontStyle.G11
                              : MeFontStyle.G12,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 12),
                // Footer row with user info and time
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // User info
                    Expanded(
                      child: Row(
                        children: [
                          Assets.svg.personIcon.svg(
                            height: 14,
                            colorFilter: ColorFilter.mode(
                              isSelected
                                  ? colorScheme.color12
                                  : isDeletedUser
                                      ? colorScheme.color11
                                      : colorScheme.color1,
                              BlendMode.srcIn,
                            ),
                          ),
                          const SizedBox(width: 6),
                          Flexible(
                            child: MeText(
                              text: report.uname ?? 'Unknown User',
                              meFontStyle: isSelected
                                  ? MeFontStyle.I12
                                  : isDeletedUser
                                      ? MeFontStyle.I11
                                      : MeFontStyle.I1,
                              maxLines: 1,
                              textOverflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    // Time ago
                    MeText(
                      text: getTimeAgo(report.deletedAt),
                      meFontStyle: isSelected
                          ? MeFontStyle.F12
                          : isDeletedUser
                              ? MeFontStyle.F11
                              : MeFontStyle.F7,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
        if (!isLastItem)
          Divider(
            height: 1,
            thickness: 1,
            color: colorScheme.color6,
          ),
      ],
    );
  }
}

// Error widget
class _ErrorWidget extends StatelessWidget {
  const _ErrorWidget({required this.error});

  final String error;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('Error: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<DeleteReportsDataProvider>().refresh();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
