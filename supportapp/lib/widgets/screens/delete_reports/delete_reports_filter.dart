import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/delete_reason_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/providers/delete_reports/delete_reports_data_provider.dart';
import 'package:mevolvesupport/providers/delete_reports/delete_reports_data_state.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/filter_tab.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_icon_button.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_radio_button.dart';

class DeleteReportsFilter extends StatefulWidget {
  const DeleteReportsFilter({super.key});

  @override
  State<DeleteReportsFilter> createState() => _DeleteReportsFilterState();
}

class _DeleteReportsFilterState extends State<DeleteReportsFilter> {
  DeletedReasonType _currentDeletedReason = DeletedReasonType.none;
  DeletedReasonType _selectedFilter = DeletedReasonType.none;

  @override
  void initState() {
    super.initState();
    // Initialize from current provider state
    final currentFilters =
        context.read<DeleteReportsDataProvider>().state.filters;
    if (currentFilters.isNotEmpty && currentFilters.first != 'none') {
      _currentDeletedReason = DeletedReasonType.values.firstWhere(
        (type) => type.name == currentFilters.first,
        orElse: () => DeletedReasonType.none,
      );
    }
    _selectedFilter = _currentDeletedReason;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<DeleteReportsDataProvider, DeleteReportsDataState>(
      builder: (context, state) {
        final reportsCount = state.totalCount;

        return Container(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(30),
                child: Material(
                  color: colorScheme.color5,
                  child: PopupMenuButton(
                    offset: const Offset(0, 40),
                    color: colorScheme.color31,
                    padding: EdgeInsets.zero,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    constraints: const BoxConstraints(minWidth: 348),
                    onOpened: () {
                      // Reset selected filter to current when popup opens
                      setState(() {
                        _selectedFilter = _currentDeletedReason;
                      });
                    },
                    itemBuilder: (context) => [
                      PopupMenuItem(
                        enabled: false,
                        padding: EdgeInsets.zero,
                        child: BlocProvider.value(
                          // Provide the same DeleteReportsDataProvider instance
                          value: context.read<DeleteReportsDataProvider>(),
                          child: StatefulBuilder(
                            builder:
                                (BuildContext context, StateSetter setState) {
                              return Column(
                                children: [
                                  // Header
                                  Padding(
                                    padding: const EdgeInsets.fromLTRB(
                                      16,
                                      12,
                                      16,
                                      8,
                                    ),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        const MeText(
                                          text: 'Reason',
                                          meFontStyle: MeFontStyle.A1,
                                        ),
                                        MeIconButton(
                                          key: const ValueKey('close'),
                                          iconPath: Assets.svg.closeIcon.path,
                                          onPressed: () =>
                                              Navigator.pop(context),
                                          buttonColor: colorScheme.color5,
                                          iconColor: colorScheme.color7,
                                        ),
                                      ],
                                    ),
                                  ),
                                  // Filter options
                                  ...List.generate(
                                    DeletedReasonType.values.length,
                                    (index) => InkWell(
                                      onTap: () {
                                        setState(() {
                                          _selectedFilter =
                                              DeletedReasonType.values[index];
                                        });
                                      },
                                      child: Padding(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 16,
                                          vertical: 8.0,
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            MeText(
                                              text: DeletedReasonType
                                                  .values[index]
                                                  .toReadableString(),
                                              meFontStyle: MeFontStyle.C8,
                                            ),
                                            MeRadioButton(
                                              value: DeletedReasonType
                                                  .values[index],
                                              groupValue: _selectedFilter,
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                  ),
                                  Divider(
                                    color: colorScheme.color6,
                                    height: 2,
                                    thickness: 2,
                                  ),
                                  // Apply button
                                  Padding(
                                    padding: const EdgeInsets.only(
                                      top: 16.0,
                                      bottom: 12,
                                    ),
                                    child: Row(
                                      mainAxisSize: MainAxisSize.max,
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Material(
                                          borderRadius:
                                              BorderRadius.circular(6),
                                          color: _currentDeletedReason ==
                                                  _selectedFilter
                                              ? colorScheme.color4
                                              : colorScheme.color1,
                                          child: InkWell(
                                            borderRadius:
                                                BorderRadius.circular(6),
                                            splashFactory:
                                                InkSparkle.splashFactory,
                                            onTap: _currentDeletedReason ==
                                                    _selectedFilter
                                                ? null
                                                : () {
                                                    // Update current filter
                                                    this.setState(() {
                                                      _currentDeletedReason =
                                                          _selectedFilter;
                                                    });

                                                    // Apply filter to provider
                                                    final filters =
                                                        _selectedFilter ==
                                                                DeletedReasonType
                                                                    .none
                                                            ? <String>[]
                                                            : [
                                                                _selectedFilter
                                                                    .name,
                                                              ];

                                                    context
                                                        .read<
                                                            DeleteReportsDataProvider>()
                                                        .changeFilters(filters);

                                                    Navigator.pop(context);
                                                  },
                                            child: Container(
                                              alignment: Alignment.center,
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                horizontal: 14,
                                                vertical: 12,
                                              ),
                                              width: 97,
                                              decoration: BoxDecoration(
                                                borderRadius:
                                                    BorderRadius.circular(6),
                                              ),
                                              child: MeText(
                                                text: 'Apply',
                                                meFontStyle:
                                                    _currentDeletedReason ==
                                                            _selectedFilter
                                                        ? MeFontStyle.D12
                                                        : MeFontStyle.D12,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                    child: FilterTab(
                      title: _currentDeletedReason != DeletedReasonType.none
                          ? _currentDeletedReason.toReadableString()
                          : 'Reason',
                      titleUnselectedStyle: MeFontStyle.E7,
                      titleSelectedStyle: MeFontStyle.E12,
                      isSelected:
                          _currentDeletedReason != DeletedReasonType.none,
                      tabUnselectedColor: Colors.transparent,
                      tabSelectedColor: colorScheme.color1,
                      borderRadius: 20,
                      showDropdownIcon: true,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              MeText(
                text: '$reportsCount Reports',
                meFontStyle: MeFontStyle.B1,
              ),
            ],
          ),
        );
      },
    );
  }
}
