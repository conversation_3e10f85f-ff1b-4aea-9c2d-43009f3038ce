import 'dart:async';
import 'dart:typed_data';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:intl/intl.dart';
import 'package:mevolvesupport/blocs/app/app_bloc.dart';
import 'package:mevolvesupport/cubits/messages/messages_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/app_language_type.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/models/chat_message.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/image_metadata.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/app_strings.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/ui/empty_data_widget.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_dialog.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_primary_button.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_switch.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_text_input.dart';
import 'package:mevolvesupport/widgets/screens/support_chat/components/message_image_block.dart';
import 'package:uuid/uuid.dart';

// Constants
class _Constants {
  static const double attachmentThumbnailSize = 150.0;
  static const double attachmentPreviewHeight = 182.0; // 150 + padding
  static const Duration scrollAnimationDuration = Duration(milliseconds: 100);
  static const double messageBubbleHorizontalMargin = 50.0;
  static const double messageBubblePadding = 16.0;
  static const double chatActionSpacing = 16.0;
}

class MessagesTab extends StatefulWidget {
  const MessagesTab({super.key});

  @override
  State<MessagesTab> createState() => _MessagesTabState();
}

class _MessagesTabState extends State<MessagesTab> {
  late final ScrollController _scrollController;
  late final ValueNotifier<bool> _enableClarify;
  late final ValueNotifier<bool> _isResolved;
  late final ValueNotifier<bool> _isClarifyLoading;
  late final ValueNotifier<String> _currentTextInput;
  late final ValueNotifier<List<MeAttachmentInfo>> _imagesToSend;
  late final TextEditingController _textEditingController;

  List<Uint8List> _attachmentBytes = [];
  bool _isUserAtBottom = true;
  String? _currentUserId;
  int _previousMessageCount = 0;
  bool _isLoadingMore = false;
  bool _shouldScrollToBottom = true;
  Timer? _loadDebounce;

  // Cache the previous state to prevent unnecessary rebuilds
  UserDetailsState? _previousState;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController()..addListener(_onScroll);
    _enableClarify = ValueNotifier(false);
    _isResolved = ValueNotifier(false);
    _isClarifyLoading = ValueNotifier(false);
    _currentTextInput = ValueNotifier('');
    _imagesToSend = ValueNotifier([]);
    _textEditingController = TextEditingController();
  }

  @override
  void dispose() {
    _loadDebounce?.cancel();
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    _enableClarify.dispose();
    _isResolved.dispose();
    _isClarifyLoading.dispose();
    _currentTextInput.dispose();
    _imagesToSend.dispose();
    _textEditingController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (!_scrollController.hasClients) return;

    // Check if user is at bottom (normal list: maxExtent is bottom)
    final isAtBottom = _scrollController.position.pixels >=
        (_scrollController.position.maxScrollExtent - 10);

    if (_isUserAtBottom != isAtBottom) {
      setState(() {
        _isUserAtBottom = isAtBottom;
      });
    }

    // Load more when at top with debouncing
    if (_scrollController.position.pixels <= 10 &&
        !_isLoadingMore &&
        _scrollController.position.atEdge) {
      _loadDebounce?.cancel();
      _loadDebounce = Timer(const Duration(milliseconds: 100), () {
        _loadMoreMessages();
      });
    }
  }

  void _loadMoreMessages() {
    if (_isLoadingMore) return;

    setState(() {
      _isLoadingMore = true;
    });

    Log.d('🔄 Loading more messages');
    context.read<UserDetailsCubit>().loadMoreMessages().then((_) {
      if (mounted) {
        setState(() {
          _isLoadingMore = false;
        });
        // Don't adjust scroll position here - let the natural flow handle it
      }
    });
  }

  void _scrollToBottom() {
    if (_scrollController.hasClients) {
      _scrollController.animateTo(
        _scrollController.position.maxScrollExtent,
        duration: _Constants.scrollAnimationDuration,
        curve: Curves.easeOut,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<UserDetailsCubit, UserDetailsState>(
      // Only rebuild when specific fields change
      buildWhen: (previous, current) {
        // Don't rebuild if it's the same state object
        if (identical(previous, current)) return false;

        // Only rebuild if meaningful data changed
        return previous.isLoading != current.isLoading ||
            previous.isLoadingMessages != current.isLoadingMessages ||
            previous.selectedUserId != current.selectedUserId ||
            previous.chatMessages.length != current.chatMessages.length ||
            previous.chatUser != current.chatUser ||
            previous.userDetails != current.userDetails;
      },
      listener: (context, state) {
        // Handle state changes that don't require rebuild
        if (state.chatUser != null) {
          _syncLocalState(state.chatUser!);
        }
      },
      builder: (context, state) {
        // Only log when actually building
        if (_previousState?.chatMessages.length != state.chatMessages.length ||
            _previousState?.selectedUserId != state.selectedUserId) {
          Log.d('💬 MessagesTab: ${state.chatMessages.length} messages loaded');
          _previousState = state;
        }

        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.selectedUserId == null || state.userDetails == null) {
          return const _EmptyMessagesView();
        }

        if (state.chatUser == null) {
          return const _EmptyMessagesView();
        }

        final messages = state.chatMessages;
        final selectedChatUser = state.chatUser!;
        final selectedUserData = state.userDetails!;
        final supportLanguage = state.supportLanguage ?? 'english';

        // Check if user changed
        if (_currentUserId != state.selectedUserId) {
          _currentUserId = state.selectedUserId;
          _previousMessageCount = messages.length;
          _shouldScrollToBottom = true; // Scroll to bottom for new user
          _isUserAtBottom = true;
        }

        // Handle scroll position after build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            // Scroll to bottom on initial load or user change
            if (_shouldScrollToBottom) {
              _scrollController
                  .jumpTo(_scrollController.position.maxScrollExtent);
              _shouldScrollToBottom = false;
            }
            // Stay at bottom ONLY if user was there and new messages arrived (not more old messages)
            else if (_isUserAtBottom &&
                messages.length > _previousMessageCount &&
                !_isLoadingMore) {
              _scrollController
                  .jumpTo(_scrollController.position.maxScrollExtent);
            }

            _previousMessageCount = messages.length;
          }
        });

        return _MessagesContent(
          messages: messages,
          selectedChatUser: selectedChatUser,
          selectedUserData: selectedUserData,
          supportLanguage: supportLanguage,
          scrollController: _scrollController,
          enableClarify: _enableClarify,
          isResolved: _isResolved,
          isClarifyLoading: _isClarifyLoading,
          currentTextInput: _currentTextInput,
          imagesToSend: _imagesToSend,
          textEditingController: _textEditingController,
          attachmentBytes: _attachmentBytes,
          onAttachmentBytesChanged: (bytes) =>
              setState(() => _attachmentBytes = bytes),
          isLoadingMore: _isLoadingMore,
          isUserAtBottom: _isUserAtBottom,
          onScrollToBottom: _scrollToBottom,
        );
      },
    );
  }

  void _syncLocalState(ChatUser chatUser) {
    final isResolved = chatUser.status == ChatStatus.resolved;
    final isClarifyEnabled = chatUser.clarify;

    if (isResolved != _isResolved.value) {
      _isResolved.value = isResolved;
    }

    if (isClarifyEnabled != _enableClarify.value) {
      _enableClarify.value = isClarifyEnabled;
    }
  }
}

// Empty messages view
class _EmptyMessagesView extends StatelessWidget {
  const _EmptyMessagesView();

  @override
  Widget build(BuildContext context) {
    return const EmptyDataWidget(
      title: 'No messages',
      svgStr: AppStrings.emptyDataMsgSvgStr,
      height: 130,
    );
  }
}

// Main messages content
class _MessagesContent extends StatelessWidget {
  const _MessagesContent({
    required this.messages,
    required this.selectedChatUser,
    required this.selectedUserData,
    required this.supportLanguage,
    required this.scrollController,
    required this.enableClarify,
    required this.isResolved,
    required this.isClarifyLoading,
    required this.currentTextInput,
    required this.imagesToSend,
    required this.textEditingController,
    required this.attachmentBytes,
    required this.onAttachmentBytesChanged,
    required this.isLoadingMore,
    required this.isUserAtBottom,
    required this.onScrollToBottom,
  });

  final List<ChatMessage> messages;
  final ChatUser selectedChatUser;
  final UserMetaData selectedUserData;
  final String supportLanguage;
  final ScrollController scrollController;
  final ValueNotifier<bool> enableClarify;
  final ValueNotifier<bool> isResolved;
  final ValueNotifier<bool> isClarifyLoading;
  final ValueNotifier<String> currentTextInput;
  final ValueNotifier<List<MeAttachmentInfo>> imagesToSend;
  final TextEditingController textEditingController;
  final List<Uint8List> attachmentBytes;
  final Function(List<Uint8List>) onAttachmentBytesChanged;
  final bool isLoadingMore;
  final bool isUserAtBottom;
  final VoidCallback onScrollToBottom;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Container(
      color: colorScheme.color5,
      child: Column(
        children: [
          Expanded(
            child: Stack(
              children: [
                _MessagesList(
                  messages: messages,
                  selectedChatUser: selectedChatUser,
                  scrollController: scrollController,
                  isLoadingMore: isLoadingMore,
                ),
                // Scroll to bottom FAB (WhatsApp style)
                if (!isUserAtBottom)
                  Positioned(
                    bottom: 16,
                    right: 16,
                    child: FloatingActionButton(
                      mini: true,
                      backgroundColor: colorScheme.color1,
                      onPressed: onScrollToBottom,
                      child: Icon(
                        Icons.arrow_downward,
                        color: colorScheme.color3,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Divider(
            height: 1,
            thickness: 1,
            color: colorScheme.color6,
          ),
          // Chat input area - only show for active users and users with write permission
          if (selectedUserData.userDeletedStatus == null)
            _ChatInputArea(
              selectedChatUser: selectedChatUser,
              selectedUserData: selectedUserData,
              supportLanguage: supportLanguage,
              enableClarify: enableClarify,
              isResolved: isResolved,
              isClarifyLoading: isClarifyLoading,
              currentTextInput: currentTextInput,
              imagesToSend: imagesToSend,
              textEditingController: textEditingController,
              attachmentBytes: attachmentBytes,
              onAttachmentBytesChanged: onAttachmentBytesChanged,
            ),
        ],
      ),
    );
  }
}

// Messages list widget with overlay loading
class _MessagesList extends StatelessWidget {
  const _MessagesList({
    required this.messages,
    required this.selectedChatUser,
    required this.scrollController,
    required this.isLoadingMore,
  });

  final List<ChatMessage> messages;
  final ChatUser selectedChatUser;
  final ScrollController scrollController;
  final bool isLoadingMore;

  @override
  Widget build(BuildContext context) {
    final currentSupportUser = context.read<AppBloc>().state.user!;
    final reversedMessages = messages.reversed.toList();
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    // Show empty state when there are no messages
    if (messages.isEmpty) {
      return const _EmptyMessagesView();
    }

    return Stack(
      children: [
        // Main messages list
        ListView.builder(
          controller: scrollController,
          physics: const AlwaysScrollableScrollPhysics(),
          padding: const EdgeInsets.only(top: 8, bottom: 8),
          itemCount: reversedMessages.length +
              (selectedChatUser.status == ChatStatus.resolved ? 1 : 0),
          itemBuilder: (context, index) {
            // Show resolved banner at the bottom
            if (selectedChatUser.status == ChatStatus.resolved &&
                index == reversedMessages.length) {
              return _ResolvedBanner(selectedChatUser: selectedChatUser);
            }

            if (index < reversedMessages.length) {
              final message = reversedMessages[index];
              final nextMessage =
                  index > 0 ? reversedMessages[index - 1] : null;

              return _MessageItem(
                message: message,
                previousMessage: nextMessage,
                isLastMessage: index == 0,
                isFirstMessage: index == reversedMessages.length - 1,
                selectedChatUser: selectedChatUser,
                currentSupportUid: currentSupportUser.uid,
              );
            }

            return const SizedBox.shrink();
          },
        ),

        // Overlay loading indicator - doesn't affect layout!
        AnimatedPositioned(
          duration: const Duration(milliseconds: 200),
          top: isLoadingMore ? 0 : -60,
          left: 0,
          right: 0,
          child: Container(
            height: 60,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  colorScheme.color5,
                  colorScheme.color5.withValues(alpha: 0.95),
                  colorScheme.color5.withValues(alpha: 0.0),
                ],
              ),
            ),
            child: Center(
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                decoration: BoxDecoration(
                  color: colorScheme.color1,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      height: 16,
                      width: 16,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation(colorScheme.color3),
                      ),
                    ),
                    const SizedBox(width: 8),
                    const MeText(
                      text: 'Loading earlier messages',
                      meFontStyle: MeFontStyle.E12,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Individual message item
class _MessageItem extends StatelessWidget {
  const _MessageItem({
    required this.message,
    required this.previousMessage,
    required this.isLastMessage,
    required this.isFirstMessage,
    required this.selectedChatUser,
    required this.currentSupportUid,
  });

  final ChatMessage message;
  final ChatMessage? previousMessage;
  final bool isLastMessage;
  final bool isFirstMessage;
  final ChatUser selectedChatUser;
  final String currentSupportUid;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final isUserMessage = message.updatedBy == selectedChatUser.uid;
    final messageDate = message.localUpdatedAt!; // Always use localUpdatedAt
    final previousDate = previousMessage?.localUpdatedAt;
    final showDateHeader = isLastMessage ||
        (previousDate != null &&
            !DateUtils.isSameDay(messageDate, previousDate));
    final showSenderInfo = isFirstMessage ||
        (previousMessage != null &&
            previousMessage!.updatedBy != message.updatedBy);

    return Column(
      crossAxisAlignment:
          isUserMessage ? CrossAxisAlignment.start : CrossAxisAlignment.end,
      children: [
        if (showDateHeader) _DateHeader(date: messageDate),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: _Constants.messageBubblePadding,
          ),
          child: Column(
            crossAxisAlignment: isUserMessage
                ? CrossAxisAlignment.start
                : CrossAxisAlignment.end,
            children: [
              if (message.attachments != null &&
                  message.attachments!.isNotEmpty)
                MessageImageBlock(
                  message: message,
                  currentSupportUid: currentSupportUid,
                ),
              if (message.message.en.isNotEmpty)
                _MessageBubble(
                  message: message,
                  isUserMessage: isUserMessage,
                  colorScheme: colorScheme,
                ),
              if (showSenderInfo && message.updatedBy != currentSupportUid)
                _SenderInfo(
                  message: message,
                  messageDate: messageDate,
                  isUserMessage: isUserMessage,
                ),
            ],
          ),
        ),
      ],
    );
  }
}

// Date header widget
class _DateHeader extends StatelessWidget {
  const _DateHeader({required this.date});

  final DateTime date;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 16),
        child: MeText(
          text: DateUtils.isSameDay(date, DateTime.now())
              ? 'Today'
              : DateFormat('d MMM yyyy, EEE').format(date),
          meFontStyle: MeFontStyle.D7,
        ),
      ),
    );
  }
}

// Message bubble widget
class _MessageBubble extends StatelessWidget {
  const _MessageBubble({
    required this.message,
    required this.isUserMessage,
    required this.colorScheme,
  });

  final ChatMessage message;
  final bool isUserMessage;
  final MeColorScheme colorScheme;

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment:
          isUserMessage ? MainAxisAlignment.start : MainAxisAlignment.end,
      mainAxisSize: MainAxisSize.max,
      children: [
        Flexible(
          child: Container(
            margin: EdgeInsets.fromLTRB(
              isUserMessage ? 0 : _Constants.messageBubbleHorizontalMargin,
              4,
              isUserMessage ? _Constants.messageBubbleHorizontalMargin : 0,
              4,
            ),
            decoration: BoxDecoration(
              color: isUserMessage ? colorScheme.color3 : colorScheme.color1,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(isUserMessage ? 0 : 10),
                topRight: const Radius.circular(10),
                bottomLeft: const Radius.circular(10),
                bottomRight: Radius.circular(isUserMessage ? 10 : 0),
              ),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: MeText(
              text: message.message.en,
              meFontStyle: isUserMessage ? MeFontStyle.D8 : MeFontStyle.D12,
              textOverflow: TextOverflow.visible,
            ),
          ),
        ),
      ],
    );
  }
}

// Sender info widget
class _SenderInfo extends StatelessWidget {
  const _SenderInfo({
    required this.message,
    required this.messageDate,
    required this.isUserMessage,
  });

  final ChatMessage message;
  final DateTime messageDate;
  final bool isUserMessage;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(top: 4, bottom: 8),
      child: MeText(
        text: isUserMessage
            ? DateFormat('hh:mm aa')
                .format(messageDate) // Just time for user messages
            : '${message.sname?.split(' ')[0] ?? ''}, ${DateFormat('hh:mm aa').format(messageDate)}', // Name and time for support messages
        meFontStyle: MeFontStyle.E7,
      ),
    );
  }
}

// Resolved banner widget
class _ResolvedBanner extends StatelessWidget {
  const _ResolvedBanner({required this.selectedChatUser});

  final ChatUser selectedChatUser;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final resolvedDate = selectedChatUser.localUpdatedAt; // Use localUpdatedAt

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 8),
      margin: const EdgeInsets.only(top: 8),
      color: colorScheme.color10,
      child: MeText(
        text:
            "conversation resolved by ${selectedChatUser.sname?.split(' ')[0]} ${getTimeAgo(resolvedDate)}",
        meFontStyle: MeFontStyle.F8,
        textAlign: TextAlign.center,
      ),
    );
  }
}

// Chat input area widget
class _ChatInputArea extends StatelessWidget {
  const _ChatInputArea({
    required this.selectedChatUser,
    required this.selectedUserData,
    required this.supportLanguage,
    required this.enableClarify,
    required this.isResolved,
    required this.isClarifyLoading,
    required this.currentTextInput,
    required this.imagesToSend,
    required this.textEditingController,
    required this.attachmentBytes,
    required this.onAttachmentBytesChanged,
  });

  final ChatUser selectedChatUser;
  final UserMetaData selectedUserData;
  final String supportLanguage;
  final ValueNotifier<bool> enableClarify;
  final ValueNotifier<bool> isResolved;
  final ValueNotifier<bool> isClarifyLoading;
  final ValueNotifier<String> currentTextInput;
  final ValueNotifier<List<MeAttachmentInfo>> imagesToSend;
  final TextEditingController textEditingController;
  final List<Uint8List> attachmentBytes;
  final Function(List<Uint8List>) onAttachmentBytesChanged;

  @override
  Widget build(BuildContext context) {
    // Check if user has write permission for support chat
    final hasWritePermission = hasPermission(
      context: context,
      permissionType: PermissionType.supportChat,
      isWrite: true,
    );

    // If user doesn't have write permission, show read-only indicator
    if (!hasWritePermission) {
      return Container(
        padding: const EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.visibility,
              size: 16,
              color: Theme.of(context).extension<MeColorScheme>()!.color4,
            ),
            const SizedBox(width: 8),
            const MeText(
              text: 'READ ONLY - You can view messages but cannot send replies',
              meFontStyle: MeFontStyle.F7,
            ),
          ],
        ),
      );
    }

    // Create send handler that can be shared between text input and send button
    void handleSend() {
      final text = currentTextInput.value.trim();
      final hasImages = imagesToSend.value.isNotEmpty;

      if (text.isNotEmpty || hasImages) {
        final currentSupportUser = context.read<AppBloc>().state.user!;

        _sendMessage(
          context: context,
          selectedUserData: selectedUserData,
          value: text,
          currentSupportUser: currentSupportUser,
          prevStatus: selectedChatUser.status,
          clarify: selectedChatUser.clarify,
          updateCounts: selectedChatUser.status != ChatStatus.replied &&
              selectedChatUser.clarify != true,
          attachments: imagesToSend.value,
          attachmentBytes: attachmentBytes,
          supportLanguage: supportLanguage,
        );

        // Clear inputs
        imagesToSend.value = [];
        currentTextInput.value = '';
        textEditingController.text = '';
      }
    }

    return Column(
      children: [
        _AttachmentPreview(
          imagesToSend: imagesToSend,
          attachmentBytes: attachmentBytes,
          onAttachmentBytesChanged: onAttachmentBytesChanged,
        ),
        _MessageInput(
          currentTextInput: currentTextInput,
          textEditingController: textEditingController,
          onSubmitted: handleSend,
        ),
        _ChatActions(
          selectedChatUser: selectedChatUser,
          selectedUserData: selectedUserData,
          supportLanguage: supportLanguage,
          enableClarify: enableClarify,
          isResolved: isResolved,
          isClarifyLoading: isClarifyLoading,
          currentTextInput: currentTextInput,
          imagesToSend: imagesToSend,
          textEditingController: textEditingController,
          attachmentBytes: attachmentBytes,
          onAttachmentBytesChanged: onAttachmentBytesChanged,
          onSend: handleSend,
        ),
      ],
    );
  }
}

// Attachment preview widget
class _AttachmentPreview extends StatelessWidget {
  const _AttachmentPreview({
    required this.imagesToSend,
    required this.attachmentBytes,
    required this.onAttachmentBytesChanged,
  });

  final ValueNotifier<List<MeAttachmentInfo>> imagesToSend;
  final List<Uint8List> attachmentBytes;
  final Function(List<Uint8List>) onAttachmentBytesChanged;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return ValueListenableBuilder<List<MeAttachmentInfo>>(
      valueListenable: imagesToSend,
      builder: (context, images, child) {
        if (images.isEmpty) return const SizedBox.shrink();

        return SizedBox(
          height: _Constants.attachmentPreviewHeight,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: images.length,
            padding: const EdgeInsets.symmetric(vertical: 16),
            itemBuilder: (context, index) {
              return Padding(
                padding: EdgeInsets.only(
                  left: index == 0 ? 16 : 8,
                  right: index == images.length - 1 ? 16 : 8,
                ),
                child: _AttachmentThumbnail(
                  imageBytes: attachmentBytes[index],
                  onRemove: () => _removeAttachment(index),
                  colorScheme: colorScheme,
                ),
              );
            },
          ),
        );
      },
    );
  }

  void _removeAttachment(int index) {
    // Create new list without the removed item
    final updatedBytes = List<Uint8List>.from(attachmentBytes)..removeAt(index);
    onAttachmentBytesChanged(updatedBytes);

    // Update images list
    final updatedImages = List<MeAttachmentInfo>.from(imagesToSend.value)
      ..removeAt(index);
    imagesToSend.value = updatedImages;
  }
}

// Attachment thumbnail widget
class _AttachmentThumbnail extends StatelessWidget {
  const _AttachmentThumbnail({
    required this.imageBytes,
    required this.onRemove,
    required this.colorScheme,
  });

  final Uint8List imageBytes;
  final VoidCallback onRemove;
  final MeColorScheme colorScheme;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(8),
      child: Stack(
        alignment: AlignmentDirectional.topEnd,
        children: [
          SizedBox(
            height: _Constants.attachmentThumbnailSize,
            width: _Constants.attachmentThumbnailSize,
            child: Image.memory(
              imageBytes,
              fit: BoxFit.cover,
            ),
          ),
          GestureDetector(
            onTap: onRemove,
            child: Container(
              width: 30,
              height: 30,
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(100),
                color: colorScheme.color7.withValues(alpha: 0.7),
              ),
              padding: const EdgeInsets.all(5),
              child: Assets.svg.closeIcon.svg(
                width: 10,
                height: 10,
                fit: BoxFit.scaleDown,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

// Message input widget
class _MessageInput extends StatelessWidget {
  const _MessageInput({
    required this.currentTextInput,
    required this.textEditingController,
    required this.onSubmitted,
  });

  final ValueNotifier<String> currentTextInput;
  final TextEditingController textEditingController;
  final VoidCallback onSubmitted;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: ValueListenableBuilder(
        valueListenable: textEditingController,
        builder: (context, value, child) {
          return MeTextInput(
            hintText: 'Type your message...',
            textEditingController: textEditingController,
            initialValue: value.text,
            textInputAction: TextInputAction.send,
            enableShiftEnterNewLine: true,
            onChanged: (value) => currentTextInput.value = value,
            onSubmitted: (value) {
              if (value.trim().isNotEmpty) {
                onSubmitted();
              }
            },
          );
        },
      ),
    );
  }
}

// Chat actions widget
class _ChatActions extends StatelessWidget {
  const _ChatActions({
    required this.selectedChatUser,
    required this.selectedUserData,
    required this.supportLanguage,
    required this.enableClarify,
    required this.isResolved,
    required this.isClarifyLoading,
    required this.currentTextInput,
    required this.imagesToSend,
    required this.textEditingController,
    required this.attachmentBytes,
    required this.onAttachmentBytesChanged,
    required this.onSend,
  });

  final ChatUser selectedChatUser;
  final UserMetaData selectedUserData;
  final String supportLanguage;
  final ValueNotifier<bool> enableClarify;
  final ValueNotifier<bool> isResolved;
  final ValueNotifier<bool> isClarifyLoading;
  final ValueNotifier<String> currentTextInput;
  final ValueNotifier<List<MeAttachmentInfo>> imagesToSend;
  final TextEditingController textEditingController;
  final List<Uint8List> attachmentBytes;
  final Function(List<Uint8List>) onAttachmentBytesChanged;
  final VoidCallback onSend;

  @override
  Widget build(BuildContext context) {
    final currentSupportUser = context.read<AppBloc>().state.user!;

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Flexible(
            child: Row(
              children: [
                _ResolveButton(
                  isResolved: isResolved,
                  selectedChatUser: selectedChatUser,
                  selectedUserData: selectedUserData,
                  currentSupportUser: currentSupportUser,
                ),
                const SizedBox(width: _Constants.chatActionSpacing),
                _ClarifyToggle(
                  enableClarify: enableClarify,
                  isClarifyLoading: isClarifyLoading,
                  selectedChatUser: selectedChatUser,
                  selectedUserData: selectedUserData,
                  currentSupportUser: currentSupportUser,
                ),
              ],
            ),
          ),
          Row(
            children: [
              _AttachmentButton(
                imagesToSend: imagesToSend,
                onAttachmentBytesChanged: onAttachmentBytesChanged,
              ),
              const SizedBox(width: _Constants.chatActionSpacing),
              _SendButton(
                currentTextInput: currentTextInput,
                imagesToSend: imagesToSend,
                onSend: onSend,
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// Resolve button widget
class _ResolveButton extends StatelessWidget {
  const _ResolveButton({
    required this.isResolved,
    required this.selectedChatUser,
    required this.selectedUserData,
    required this.currentSupportUser,
  });

  final ValueNotifier<bool> isResolved;
  final ChatUser selectedChatUser;
  final UserMetaData selectedUserData;
  final User currentSupportUser;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return ValueListenableBuilder<bool>(
      valueListenable: isResolved,
      builder: (context, value, child) {
        return InkWell(
          onTap: () => _handleResolveToggle(context, value),
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: colorScheme.color21,
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: MeText(
              text: value ? 'Unresolve' : 'Resolve',
              meFontStyle: MeFontStyle.C1,
            ),
          ),
        );
      },
    );
  }

  Future<void> _handleResolveToggle(
    BuildContext context,
    bool currentValue,
  ) async {
    final shouldProceed = await showMeDialog(
          context,
          text: 'Are you sure to ${currentValue ? 'unresolve' : 'resolve'}?',
          primaryText: currentValue ? 'Unresolve' : 'Resolve',
        ) ??
        false;

    if (context.mounted && shouldProceed) {
      isResolved.value = !currentValue;
      _updateSupportStatus(
        context: context,
        selectedUserData: selectedUserData,
        currentSupportUser: currentSupportUser,
        status:
            currentValue ? selectedChatUser.prevStatus! : ChatStatus.resolved,
        clarifyStatus: selectedChatUser.clarify,
        prevStatus:
            currentValue ? ChatStatus.resolved : selectedChatUser.status,
        updateCounts: selectedChatUser.clarify != true,
      );
    }
  }
}

// Clarify toggle widget
class _ClarifyToggle extends StatelessWidget {
  const _ClarifyToggle({
    required this.enableClarify,
    required this.isClarifyLoading,
    required this.selectedChatUser,
    required this.selectedUserData,
    required this.currentSupportUser,
  });

  final ValueNotifier<bool> enableClarify;
  final ValueNotifier<bool> isClarifyLoading;
  final ChatUser selectedChatUser;
  final UserMetaData selectedUserData;
  final User currentSupportUser;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        color: colorScheme.color28,
      ),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          const MeText(
            text: 'Clarify',
            meFontStyle: MeFontStyle.C14,
          ),
          const SizedBox(width: 8),
          ValueListenableBuilder<bool>(
            valueListenable: isClarifyLoading,
            builder: (context, isLoading, child) {
              return ValueListenableBuilder<bool>(
                valueListenable: enableClarify,
                builder: (context, clarify, child) {
                  return Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      MeSwitch(
                        value: clarify,
                        switchColor: colorScheme.color14,
                        onChanged: isLoading
                            ? null
                            : (value) async {
                                // Set loading state
                                isClarifyLoading.value = true;

                                try {
                                  // Update on server
                                  await _updateSupportStatus(
                                    context: context,
                                    selectedUserData: selectedUserData,
                                    currentSupportUser: currentSupportUser,
                                    isClarifySwitch: true,
                                    status: selectedChatUser.status,
                                    clarifyStatus: value,
                                    prevStatus: selectedChatUser.prevStatus,
                                  );

                                  // Update local state immediately after successful server update
                                  enableClarify.value = value;
                                } catch (e) {
                                  if (context.mounted) {
                                    final colorScheme = Theme.of(context)
                                        .extension<MeColorScheme>()!;
                                    ScaffoldMessenger.of(context).showSnackBar(
                                      SnackBar(
                                        content: Text(
                                          'Failed to update clarify status: ${e.toString()}',
                                        ),
                                        backgroundColor: colorScheme.color11,
                                        duration: const Duration(seconds: 3),
                                      ),
                                    );
                                  }
                                } finally {
                                  // Clear loading state
                                  isClarifyLoading.value = false;
                                }
                              },
                      ),
                      if (isLoading) ...[
                        const SizedBox(width: 8),
                        SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            color: colorScheme.color1,
                          ),
                        ),
                      ],
                    ],
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }
}

// Attachment button widget
class _AttachmentButton extends StatelessWidget {
  const _AttachmentButton({
    required this.imagesToSend,
    required this.onAttachmentBytesChanged,
  });

  final ValueNotifier<List<MeAttachmentInfo>> imagesToSend;
  final Function(List<Uint8List>) onAttachmentBytesChanged;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return InkWell(
      onTap: () => _pickImages(context),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: colorScheme.color5,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 12),
        child: Assets.svg.attachIcon.svg(
          colorFilter: ColorFilter.mode(
            colorScheme.color7,
            BlendMode.srcIn,
          ),
        ),
      ),
    );
  }

  Future<void> _pickImages(BuildContext context) async {
    try {
      final picker = ImagePicker();
      final imagesPick = await picker.pickMultiImage();

      if (imagesPick.isEmpty) return;

      final imagesBytes = <Uint8List>[];
      for (final image in imagesPick) {
        final bytes = await image.readAsBytes();
        imagesBytes.add(bytes);
      }

      if (imagesBytes.isNotEmpty) {
        final attachmentPaths = <String>[];
        final attachmentBytes = <Uint8List>[];
        int timestamp = DateTime.now().millisecondsSinceEpoch;

        for (final imageBytes in imagesBytes) {
          final extension = getFileExtension(imageBytes);
          final compressedPath = '$timestamp.$extension';
          final compressedImage = await getCompressedImage(imageBytes);

          attachmentPaths.add(compressedPath);
          attachmentBytes.add(compressedImage);
          timestamp++;
        }

        onAttachmentBytesChanged(attachmentBytes);

        final attachmentsList = attachmentPaths
            .map(
              (path) => MeAttachmentInfo(
                id: int.parse(path.substring(0, 13)),
                createdAt: DateTime.now(),
                metadata: ImageMetadata(height: 0, width: 0),
                size: 0,
                status: AttachmentUploadStatus.temporary,
                localFilePath: path,
                deletedAt: null,
                fileType: FileType.image,
              ),
            )
            .toList();

        imagesToSend.value = attachmentsList;
      }
    } catch (e) {
      Log.e('Error picking images: $e');
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Failed to pick images. Please try again.'),
          ),
        );
      }
    }
  }
}

// Send button widget
class _SendButton extends StatelessWidget {
  const _SendButton({
    required this.currentTextInput,
    required this.imagesToSend,
    required this.onSend,
  });

  final ValueNotifier<String> currentTextInput;
  final ValueNotifier<List<MeAttachmentInfo>> imagesToSend;
  final VoidCallback onSend;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<MeAttachmentInfo>>(
      valueListenable: imagesToSend,
      builder: (context, images, child) {
        return ValueListenableBuilder<String>(
          valueListenable: currentTextInput,
          builder: (context, text, child) {
            final isEnabled = text.trim().isNotEmpty || images.isNotEmpty;

            return MePrimaryButton(
              iconPath: Assets.svg.sendIcon.path,
              isEnabled: isEnabled,
              padding: const EdgeInsets.all(12),
              onTap: isEnabled ? onSend : null,
            );
          },
        );
      },
    );
  }
}

// Helper functions moved outside of widgets
Future<void> _updateSupportStatus({
  required BuildContext context,
  required UserMetaData selectedUserData,
  required User currentSupportUser,
  required ChatStatus status,
  bool isClarifySwitch = false,
  bool clarifyStatus = false,
  ChatStatus? prevStatus,
  bool updateCounts = true,
}) async {
  final chatUser = ChatUser(
    uid: selectedUserData.uid,
    uname: selectedUserData.userInfo.pseudoName!,
    updatedBy: currentSupportUser.uid,
    clarify: clarifyStatus,
    status: status,
    prevStatus: prevStatus,
    lastSid: currentSupportUser.uid,
    sname: currentSupportUser.displayName,
    userDeletedStatus: null,
    createdAt: DateTime.now(),
  );

  await context.read<MessagesCubit>().updateSupportStatus(
        chatUser: chatUser,
        isClarifySwitch: isClarifySwitch,
        updateCounts: updateCounts,
      );
}

Future<void> _sendMessage({
  required BuildContext context,
  required UserMetaData selectedUserData,
  required String value,
  required User currentSupportUser,
  required ChatStatus prevStatus,
  required bool clarify,
  bool updateCounts = true,
  List<MeAttachmentInfo>? attachments,
  List<Uint8List> attachmentBytes = const [],
  String supportLanguage = 'english',
}) async {
  Log.d('📤 Sending message to ${selectedUserData.userInfo.pseudoName}');

  final chatUser = ChatUser(
    uid: selectedUserData.uid,
    uname: selectedUserData.userInfo.pseudoName!,
    message: value,
    updatedBy: currentSupportUser.uid,
    status: ChatStatus.replied,
    prevStatus: prevStatus,
    lastSid: currentSupportUser.uid,
    sname: currentSupportUser.displayName,
    attachmentCount: attachments?.length ?? 0,
    clarify: clarify,
    userDeletedStatus: null,
    createdAt: DateTime.now(),
  );

  final messageId = const Uuid().v4();
  final timestamp = DateTime.now();

  final chatMessage = ChatMessage(
    id: messageId,
    uid: selectedUserData.uid,
    updatedBy: currentSupportUser.uid,
    message: MessageData.fromText(LanguageType.english, value),
    sname: currentSupportUser.displayName,
    attachments: attachments,
    supportLanguage: LanguageType.english,
    localUpdatedAt: timestamp,
  );

  await context.read<MessagesCubit>().sendMessage(
        chatUser: chatUser,
        chatMessage: chatMessage,
        updateCounts: updateCounts,
        attachmentBytes: attachmentBytes,
        supportLanguage: supportLanguage,
      );

  Log.d('📤 Message sent successfully');
}
