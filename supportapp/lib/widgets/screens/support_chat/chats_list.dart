import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/cubits/app_navigation/app_navigation_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/providers/chats/chat_data_provider.dart';
import 'package:mevolvesupport/providers/chats/chat_data_state.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:mevolvesupport/widgets/shared/ui/empty_list_screen.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_list_bottom_bar.dart';
import 'package:mevolvesupport/widgets/screens/support_chat/components/chat_block.dart';

class ChatsList extends StatefulWidget {
  const ChatsList({super.key});

  @override
  State<ChatsList> createState() => _ChatsListState();
}

class _ChatsListState extends State<ChatsList>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  String _selectedChatUserId = '';

  @override
  void initState() {
    super.initState();
    // Initialize with default filter (NotReplied)
    WidgetsBinding.instance.addPostFrameCallback((_) {
      Log.d('🎬 ChatsList: Initializing ChatDataProvider');
      context.read<ChatDataProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<ChatDataProvider, ChatDataState>(
      builder: (context, state) {
        Log.d(
          '🎨 ChatsList: UI rebuild - ${state.chatUsers.length} chats, total: ${state.totalCount}, hasMore: ${state.hasMore}, loading: ${state.isLoading}',
        );

        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.error != null) {
          return _ErrorWidget(error: state.error!);
        }

        if (state.chatUsers.isEmpty) {
          return Container(
            color: colorScheme.color6,
            child: EmptyListScreen(
              title: 'No chats found',
              iconPath: Assets.svg.emptyScreenMessages.path,
            ),
          );
        }

        return Column(
          children: [
            Expanded(
              child: _ChatsDataTable(
                chats: state.chatUsers,
                selectedChatUserId: _selectedChatUserId,
                onChatSelected: (chatUser) {
                  // Update UserDetailsCubit - single source of truth for user selection
                  context.read<UserDetailsCubit>().selectUser(chatUser.uid);

                  // Navigate to messages tab when selecting a chat
                  context.read<AppNavigationCubit>().updateNavigationState(
                        selectedDetailsTab: 1, // Messages tab
                      );

                  setState(() {
                    _selectedChatUserId = chatUser.uid;
                  });
                },
              ),
            ),
            if (state.chatUsers.isNotEmpty)
              MeListBottomBar(
                currentCount: state.chatUsers.length,
                totalCount: state.totalCount,
                hasMore: state.hasMore,
                isLoading: state.isLoadingMore,
                hasNewUpdates: state.hasNewUpdates,
                onLoadMore: () => context.read<ChatDataProvider>().loadMore(),
                onRefresh: () => context.read<ChatDataProvider>().refresh(),
                itemType: 'Chats',
              ),
          ],
        );
      },
    );
  }
}

// Separated data table widget for better organization
class _ChatsDataTable extends StatefulWidget {
  const _ChatsDataTable({
    required this.chats,
    required this.selectedChatUserId,
    required this.onChatSelected,
  });

  final List<ChatUser> chats;
  final String selectedChatUserId;
  final Function(ChatUser chatUser) onChatSelected;

  @override
  State<_ChatsDataTable> createState() => _ChatsDataTableState();
}

class _ChatsDataTableState extends State<_ChatsDataTable> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Container(
      color: colorScheme.color6,
      child: ListView.builder(
        controller: _scrollController,
        itemCount: widget.chats.length,
        itemBuilder: (context, index) {
          final chatUser = widget.chats[index];
          final isSelected = widget.selectedChatUserId == chatUser.uid;

          return ChatBlock(
            chatUser: chatUser,
            isSelected: isSelected,
            index: index,
            chatsLength: widget.chats.length,
            onTap: (index) {
              widget.onChatSelected(chatUser);
            },
          );
        },
      ),
    );
  }
}

// Error widget
class _ErrorWidget extends StatelessWidget {
  const _ErrorWidget({required this.error});

  final String error;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('Error: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () {
              context.read<ChatDataProvider>().refresh();
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
