import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class ChatBlock extends StatelessWidget {
  const ChatBlock({
    super.key,
    required this.chatUser,
    required this.isSelected,
    required this.index,
    required this.chatsLength,
    required this.onTap,
    this.isEmpty = false,
  });

  final ChatUser chatUser;
  final bool isSelected;
  final int index;
  final int chatsLength;
  final bool isEmpty;
  final Function(int index) onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Column(
      children: [
        InkWell(
          onTap: () {
            onTap.call(index);
          },
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
                color: isSelected ? colorScheme.color1 : colorScheme.color5,
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: chatUser.attachmentCount != null &&
                                  chatUser.attachmentCount! > 0
                              ? Row(
                                  children: [
                                    Assets.svg.attachIcon.svg(
                                      height: 16,
                                      colorFilter: ColorFilter.mode(
                                        isSelected
                                            ? colorScheme.color12
                                            : colorScheme.color1,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                    const SizedBox(width: 6),
                                    Expanded(
                                      child: MeText(
                                        text:
                                            '${chatUser.attachmentCount} Attachment${chatUser.attachmentCount == 1 ? '' : 's'}',
                                        meFontStyle: isSelected
                                            ? MeFontStyle.E12
                                            : MeFontStyle.E8,
                                      ),
                                    ),
                                  ],
                                )
                              : MeText(
                                  text: chatUser.message!,
                                  meFontStyle: isSelected
                                      ? MeFontStyle.E12
                                      : MeFontStyle.E8,
                                ),
                        ),
                        Row(
                          children: [
                            if (chatUser.userDeletedStatus != null)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                margin: const EdgeInsets.only(
                                  right: 10,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(3),
                                  color: colorScheme.color11,
                                ),
                                child: MeText(
                                  text: chatUser.userDeletedStatus!
                                      .toReadableString(),
                                  meFontStyle: MeFontStyle.G12,
                                ),
                              ),
                            if (chatUser.clarify)
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 6,
                                  vertical: 2,
                                ),
                                margin: const EdgeInsets.only(
                                  right: 10,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(
                                    3,
                                  ),
                                  color: colorScheme.color28,
                                ),
                                child: MeText(
                                  text: ChatStatus.clarify.toReadableString(),
                                  meFontStyle: MeFontStyle.G14,
                                ),
                              ),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(
                                  3,
                                ),
                                border:
                                    chatUser.status == ChatStatus.resolved &&
                                            isSelected
                                        ? Border.all(
                                            color: colorScheme.color12,
                                            width: 0.5,
                                          )
                                        : null,
                                color: chatUser.status == ChatStatus.resolved
                                    ? colorScheme.color1
                                    : chatUser.status == ChatStatus.replied
                                        ? colorScheme.color21
                                        : colorScheme.color10,
                              ),
                              child: MeText(
                                text: chatUser.status.toReadableString(),
                                meFontStyle:
                                    chatUser.status == ChatStatus.resolved
                                        ? MeFontStyle.G12
                                        : chatUser.status == ChatStatus.replied
                                            ? MeFontStyle.G1
                                            : MeFontStyle.G8,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              Assets.svg.personIcon.svg(
                                height: 14,
                                colorFilter: ColorFilter.mode(
                                  isSelected
                                      ? colorScheme.color12
                                      : colorScheme.color1,
                                  BlendMode.srcIn,
                                ),
                              ),
                              const SizedBox(
                                width: 6,
                              ),
                              Expanded(
                                child: MeText(
                                  text: chatUser.uname,
                                  meFontStyle: isSelected
                                      ? MeFontStyle.F12
                                      : MeFontStyle.F1,
                                ),
                              ),
                            ],
                          ),
                        ),
                        MeText(
                          text:
                              "${chatUser.sname == null || (chatUser.status == ChatStatus.notReplied && !(chatUser.clarify)) ? "" : "${chatUser.sname?.split(' ')[0]}  |  "}${getTimeAgo(
                            chatUser.cloudUpdatedAt ?? chatUser.localUpdatedAt,
                          )}",
                          meFontStyle:
                              isSelected ? MeFontStyle.F12 : MeFontStyle.F7,
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Divider(
                height: 1,
                thickness: 1,
                color: colorScheme.color6,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
