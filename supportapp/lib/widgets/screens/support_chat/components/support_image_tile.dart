import 'package:mevolvesupport/widgets/shared/attachment_viewer/me_attachment_viewer.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_cached_storage_image.dart';
import 'package:mevolvesupport/widgets/shared/ui/show_me_snackbar.dart';
import 'package:universal_html/html.dart' as html;

import 'package:flutter/material.dart';

import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class SupportImageTile extends StatelessWidget {
  const SupportImageTile({
    super.key,
    required this.index,
    required this.attachments,
    required this.uid,
    required this.docId,
  });

  final int index;
  final List<MeAttachmentInfo> attachments;
  final String uid;
  final String docId;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return GestureDetector(
      onTap: () async {
        // Filter out text files for the attachment viewer (they should be downloaded directly)
        final mediaAttachments = attachments
            .where((attachment) => attachment.fileType != FileType.txt)
            .toList();

        if (attachments[index].fileType == FileType.txt) {
          // Handle text file download
          final logFile = attachments[index];
          var path = logFile.getAttachmentUrl(
            userId: uid,
            imageType: ImageType.original,
            fileType: attachments[index].fileType,
            isSupportMedia: true,
            docId: docId,
            docType: FirebaseDocCollectionType.chatMessages,
          );

          final url = await getStorageDownloadUrl(path);
          html.AnchorElement anchorElement = html.AnchorElement(href: url);
          anchorElement.download = url;
          anchorElement.click();
          if (context.mounted) {
            showMeSnackbar(context, 'Log downloaded');
          }
        } else {
          // Use comprehensive attachment viewer for all media types
          final currentMediaIndex =
              mediaAttachments.indexOf(attachments[index]);

          Navigator.of(context).push(
            MaterialPageRoute(
              builder: (_) => MeAttachmentViewer(
                attachments: mediaAttachments,
                currentIndex: currentMediaIndex >= 0 ? currentMediaIndex : 0,
                uid: uid,
                docId: docId,
                docType: FirebaseDocCollectionType.chatMessages,
                title: 'Chat Attachments',
              ),
            ),
          );
        }
      },
      child: SizedBox(
        height: 100,
        width: 100,
        child: Stack(
          children: [
            _buildAttachmentThumbnail(colorScheme),
            // Show "+X more" overlay on the last visible attachment when there are more
            if ((index == 5 && attachments.length > 6) ||
                (index == 2 &&
                    attachments.length > 3 &&
                    attachments.length <= 6))
              Container(
                width: 100,
                height: 100,
                color: Colors.black.withValues(alpha: 0.6),
                alignment: Alignment.center,
                child: MeText(
                  text: '+${attachments.length - (index + 1)}',
                  meFontStyle: MeFontStyle.R12,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentThumbnail(MeColorScheme colorScheme) {
    final attachment = attachments[index];

    switch (attachment.fileType) {
      case FileType.image:
        return MeCachedStorageImage(
          url: attachment.getAttachmentUrl(
            userId: uid,
            imageType: ImageType.thumbnail,
            fileType: FileType.image,
            isSupportMedia: true,
            docId: docId,
            docType: FirebaseDocCollectionType.chatMessages,
          ),
          storageType: attachment.status,
          width: 100,
          height: 100,
          fit: BoxFit.cover,
        );

      case FileType.video:
        return Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: Colors.black87,
            borderRadius: BorderRadius.circular(4),
          ),
          child: const Icon(
            Icons.play_circle_filled,
            color: Colors.white,
            size: 32,
          ),
        );

      case FileType.audio:
        return Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: colorScheme.color1,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: colorScheme.color7, width: 0.5),
          ),
          child: Icon(
            Icons.audiotrack,
            color: colorScheme.color3,
            size: 32,
          ),
        );

      case FileType.document:
        return Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: colorScheme.color1,
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: colorScheme.color7, width: 0.5),
          ),
          child: Icon(
            Icons.description,
            color: colorScheme.color3,
            size: 32,
          ),
        );

      case FileType.txt:
        return Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: colorScheme.color7, width: 0.5),
          ),
          child: Assets.svg.icLogThumbnail.svg(
            width: 36,
            fit: BoxFit.scaleDown,
            colorFilter: ColorFilter.mode(colorScheme.color35, BlendMode.srcIn),
          ),
        );
    }
  }
}
