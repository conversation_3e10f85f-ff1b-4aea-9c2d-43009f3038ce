import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/chat_message.dart';
import 'package:mevolvesupport/widgets/screens/support_chat/components/support_image_tile.dart';

class MessageImageBlock extends StatelessWidget {
  const MessageImageBlock({
    super.key,
    required this.message,
    required this.currentSupportUid,
  });

  final ChatMessage message;
  final String currentSupportUid;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    var currentMessage = message;
    var attachments = currentMessage.attachments ?? [];
    return Container(
      padding: const EdgeInsets.all(2),
      margin: const EdgeInsets.symmetric(
        vertical: 4,
      ),
      decoration: BoxDecoration(
        color: currentMessage.updatedBy == currentMessage.uid
            ? colorScheme.color3
            : colorScheme.color1,
        borderRadius: BorderRadius.circular(4),
      ),
      child: ClipRRect(
        clipBehavior: Clip.antiAliasWithSaveLayer,
        borderRadius: BorderRadius.circular(2),
        child: Column(
          children: [
            // First row - up to 3 attachments
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SupportImageTile(
                  index: 0,
                  attachments: attachments,
                  uid: currentMessage.uid,
                  docId: message.id,
                ),
                if (attachments.length > 1) ...[
                  const SizedBox(width: 2),
                  SupportImageTile(
                    index: 1,
                    attachments: attachments,
                    uid: currentMessage.uid,
                    docId: message.id,
                  ),
                ],
                if (attachments.length > 2) ...[
                  const SizedBox(width: 2),
                  SupportImageTile(
                    index: 2,
                    attachments: attachments,
                    uid: currentMessage.uid,
                    docId: message.id,
                  ),
                ],
              ],
            ),
            // Second row - for 4+ attachments
            if (attachments.length > 3) ...[
              const SizedBox(height: 2),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SupportImageTile(
                    index: 3,
                    attachments: attachments,
                    uid: currentMessage.uid,
                    docId: message.id,
                  ),
                  if (attachments.length > 4) ...[
                    const SizedBox(width: 2),
                    SupportImageTile(
                      index: 4,
                      attachments: attachments,
                      uid: currentMessage.uid,
                      docId: message.id,
                    ),
                  ],
                  if (attachments.length > 5) ...[
                    const SizedBox(width: 2),
                    SupportImageTile(
                      index: 5,
                      attachments: attachments,
                      uid: currentMessage.uid,
                      docId: message.id,
                    ),
                  ],
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }
}
