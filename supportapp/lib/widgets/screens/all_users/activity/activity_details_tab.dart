import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/activity_utils.dart';
import 'package:mevolvesupport/utilities/app_strings.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/screens/all_users/activity/components/activity_block.dart';
import 'package:mevolvesupport/widgets/screens/all_users/activity/dialogs/activity_filter_dialog.dart';
import 'package:mevolvesupport/widgets/shared/ui/empty_data_widget.dart';

class ActivityDetailsTab extends StatefulWidget {
  const ActivityDetailsTab({super.key});

  @override
  State<ActivityDetailsTab> createState() => _ActivityDetailsTabState();
}

class _ActivityDetailsTabState extends State<ActivityDetailsTab> {
  final ScrollController _scrollController = ScrollController();
  ActivityFilter filter = ActivityFilter();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScroll);
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent - 200) {
      final state = context.read<UserDetailsCubit>().state;
      if (!state.isLoadingActivities && state.selectedUserId != null) {
        context.read<UserDetailsCubit>().loadMoreActivities(
              eventName: filter.eventName,
              eventType: filter.eventType,
              startFrom: filter.startFrom,
            );
      }
    }
  }

  void _applyFilter(ActivityFilter newFilter) {
    setState(() {
      filter = newFilter;
    });

    // Load activities with new filter
    final userDetailsCubit = context.read<UserDetailsCubit>();
    if (userDetailsCubit.state.selectedUserId != null) {
      // Clear existing activities and reload with filter
      userDetailsCubit.loadMoreActivities(
        eventName: filter.eventName,
        eventType: filter.eventType,
        startFrom: filter.startFrom,
      );
    }
  }

  void _resetFilters() {
    setState(() {
      filter = ActivityFilter();
    });

    // Refresh activities without filters
    context.read<UserDetailsCubit>().refreshActivities();
  }

  void _clearSingleFilter({
    bool clearEventName = false,
    bool clearEventType = false,
    bool clearStartFrom = false,
  }) {
    setState(() {
      filter = filter.copyWith(
        clearEventName: clearEventName,
        clearEventType: clearEventType,
        clearStartFrom: clearStartFrom,
      );
    });

    // Reload activities with updated filter
    context.read<UserDetailsCubit>().refreshActivities();
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => ActivityFilterDialog(
        currentFilter: filter,
        onApply: _applyFilter,
      ),
    );
  }

  int _getAppliedFilterCount() {
    int count = 0;
    if (filter.eventName != null && filter.eventName!.isNotEmpty) count++;
    if (filter.eventType != null) count++;
    if (filter.startFrom != null) count++;
    return count;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<UserDetailsCubit, UserDetailsState>(
      builder: (context, state) {
        // Check if user is selected
        if (state.selectedUserId == null || state.userDetails == null) {
          return EmptyDataWidget(
            title: getEmptyScreenText(2),
            svgStr: AppStrings.emptyDataSvgStr,
          );
        }

        // Show loading state for initial load
        if (state.isLoadingActivities && state.userActivities.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                CircularProgressIndicator(),
                SizedBox(height: 16),
                MeText(
                  text: 'Loading user activities...',
                  meFontStyle: MeFontStyle.F7,
                ),
              ],
            ),
          );
        }

        // Show error if there is one
        if (state.activitiesPollingError != null &&
            state.userActivities.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.error_outline,
                  size: 48,
                  color: colorScheme.color3,
                ),
                const SizedBox(height: 16),
                const MeText(
                  text: 'Error loading activities',
                  meFontStyle: MeFontStyle.E5,
                ),
                const SizedBox(height: 8),
                MeText(
                  text: state.activitiesPollingError!,
                  meFontStyle: MeFontStyle.F7,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    context.read<UserDetailsCubit>().refreshActivities();
                  },
                  child: const MeText(
                    text: 'Retry',
                    meFontStyle: MeFontStyle.F7,
                  ),
                ),
              ],
            ),
          );
        }

        final groupedActivities = groupActivitiesByDate(state.userActivities);

        if (groupedActivities.isEmpty) {
          // Show empty data widget with reset filter button if filters are applied
          if (!filter.isEmpty) {
            return EmptyDataWidget(
              title: 'No Activity details',
              svgStr: AppStrings.emptyDataMsgSvgStr,
              height: 130,
              actionButton: ElevatedButton(
                onPressed: _resetFilters,
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.color3,
                ),
                child: const MeText(
                  text: 'Reset Filters',
                  meFontStyle: MeFontStyle.F7,
                ),
              ),
            );
          } else {
            return const EmptyDataWidget(
              title: 'No Activity details',
              svgStr: AppStrings.emptyDataMsgSvgStr,
              height: 130,
            );
          }
        }

        return Column(
          children: [
            // User info header with polling indicator
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              color: colorScheme.color5,
              child: Row(
                children: [
                  IconButton(
                    icon: Stack(
                      children: [
                        const Icon(Icons.filter_list),
                        if (!filter.isEmpty)
                          Positioned(
                            right: 0,
                            top: 0,
                            child: Container(
                              padding: const EdgeInsets.all(2),
                              decoration: const BoxDecoration(
                                color: Colors.green,
                                shape: BoxShape.circle,
                              ),
                              constraints: const BoxConstraints(
                                minWidth: 12,
                                minHeight: 12,
                              ),
                            ),
                          ),
                      ],
                    ),
                    onPressed: () => _showFilterDialog(context),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const MeText(
                          text: 'User Activity',
                          meFontStyle: MeFontStyle.E5,
                        ),
                        if (state.isPollingActivities)
                          Row(
                            children: [
                              const SizedBox(
                                width: 12,
                                height: 12,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                ),
                              ),
                              const SizedBox(width: 8),
                              MeText(
                                text:
                                    'Live - Last update: ${state.lastPollingUpdate != null ? DateFormat('h:mm:ss a').format(state.lastPollingUpdate!) : 'N/A'}',
                                meFontStyle: MeFontStyle.F7,
                              ),
                            ],
                          ),
                      ],
                    ),
                  ),
                  // Polling toggle button
                  IconButton(
                    icon: Icon(
                      state.isPollingActivities
                          ? Icons.pause
                          : Icons.play_arrow,
                      color: state.isPollingActivities
                          ? Colors.orange
                          : Colors.green,
                    ),
                    onPressed: () {
                      context
                          .read<UserDetailsCubit>()
                          .toggleActivitiesPolling();
                    },
                    tooltip: state.isPollingActivities
                        ? 'Pause live updates'
                        : 'Start live updates',
                  ),
                  // Manual refresh button
                  IconButton(
                    icon: const Icon(Icons.refresh),
                    onPressed: state.isLoadingActivities
                        ? null
                        : () {
                            context
                                .read<UserDetailsCubit>()
                                .refreshActivities();
                          },
                    tooltip: 'Refresh activities',
                  ),
                ],
              ),
            ),

            // Filter indicator
            if (!filter.isEmpty)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16.0),
                color: colorScheme.color5,
                child: Wrap(
                  spacing: 12,
                  runSpacing: 12,
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    // Filter chip showing count
                    Container(
                      padding: const EdgeInsets.fromLTRB(12, 6, 8, 6),
                      height: 30,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(24),
                        shape: BoxShape.rectangle,
                        color: colorScheme.color3,
                        border: Border.all(color: colorScheme.color3),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          MeText(
                            text: '${_getAppliedFilterCount()} filters applied',
                            meFontStyle: MeFontStyle.F2,
                          ),
                        ],
                      ),
                    ),

                    // Event Type filter chip
                    if (filter.eventType != null)
                      Container(
                        padding: const EdgeInsets.fromLTRB(12, 6, 8, 6),
                        height: 30,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          shape: BoxShape.rectangle,
                          color: colorScheme.color3,
                          border: Border.all(color: colorScheme.color3),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            MeText(
                              text: 'Event Type (${filter.eventType})',
                              meFontStyle: MeFontStyle.F7,
                            ),
                            const SizedBox(width: 4),
                            InkWell(
                              onTap: () {
                                _clearSingleFilter(clearEventType: true);
                              },
                              child: Icon(
                                Icons.close,
                                size: 14,
                                color: colorScheme.color2,
                              ),
                            ),
                          ],
                        ),
                      ),

                    // Event Name filter chip
                    if (filter.eventName != null &&
                        filter.eventName!.isNotEmpty)
                      Container(
                        padding: const EdgeInsets.fromLTRB(12, 6, 8, 6),
                        height: 30,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          shape: BoxShape.rectangle,
                          color: colorScheme.color3,
                          border: Border.all(color: colorScheme.color3),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            MeText(
                              text: 'Event Name (${filter.eventName})',
                              meFontStyle: MeFontStyle.F7,
                            ),
                            const SizedBox(width: 4),
                            InkWell(
                              onTap: () {
                                _clearSingleFilter(clearEventName: true);
                              },
                              child: Icon(
                                Icons.close,
                                size: 14,
                                color: colorScheme.color2,
                              ),
                            ),
                          ],
                        ),
                      ),

                    // Date filter chip
                    if (filter.startFrom != null)
                      Container(
                        padding: const EdgeInsets.fromLTRB(12, 6, 8, 6),
                        height: 30,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          shape: BoxShape.rectangle,
                          color: colorScheme.color3,
                          border: Border.all(color: colorScheme.color3),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            MeText(
                              text:
                                  'From (${DateFormat('d MMM yyyy').format(filter.startFrom!)})',
                              meFontStyle: MeFontStyle.F7,
                            ),
                            const SizedBox(width: 4),
                            InkWell(
                              onTap: () {
                                _clearSingleFilter(clearStartFrom: true);
                              },
                              child: Icon(
                                Icons.close,
                                size: 14,
                                color: colorScheme.color2,
                              ),
                            ),
                          ],
                        ),
                      ),

                    // Clear all button
                    InkWell(
                      onTap: _resetFilters,
                      child: Container(
                        padding: const EdgeInsets.fromLTRB(12, 6, 12, 6),
                        height: 30,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(24),
                          shape: BoxShape.rectangle,
                          color: colorScheme.color3,
                          border: Border.all(color: colorScheme.color3),
                        ),
                        child: const MeText(
                          text: 'Clear all',
                          meFontStyle: MeFontStyle.F2,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

            // Activity list
            Expanded(
              child: ListView.builder(
                controller: _scrollController,
                itemCount: groupedActivities.length +
                    (state.isLoadingActivities ? 1 : 0),
                itemBuilder: (context, index) {
                  if (index == groupedActivities.length) {
                    // Loading indicator at the bottom
                    return const Padding(
                      padding: EdgeInsets.all(16),
                      child: Center(
                        child: CircularProgressIndicator(strokeWidth: 2),
                      ),
                    );
                  }

                  final date = groupedActivities.keys.elementAt(index);
                  final activities = groupedActivities[date]!;

                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 10),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 16,
                          vertical: 12,
                        ),
                        color: colorScheme.color1,
                        child: MeText(
                          text: date,
                          meFontStyle: MeFontStyle.E5,
                        ),
                      ),
                      ...activities
                          .map((activity) => ActivityBlock(activity: activity)),
                    ],
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }
}
