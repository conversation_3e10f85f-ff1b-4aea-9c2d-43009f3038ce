import 'dart:convert';
import 'package:universal_html/html.dart' as html;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/activity.dart';

class ActivityJsonDialog extends StatefulWidget {
  const ActivityJsonDialog({
    super.key,
    required this.activity,
  });

  final ActivityItem activity;

  @override
  State<ActivityJsonDialog> createState() => _ActivityJsonDialogState();
}

class _ActivityJsonDialogState extends State<ActivityJsonDialog> {
  late ActivityItem activity;

  @override
  void initState() {
    activity = widget.activity;
    super.initState();
  }

  @override
  Widget build(BuildContext mainContext) {
    final colorScheme = Theme.of(mainContext).extension<MeColorScheme>()!;
    final jsonString =
        const JsonEncoder.withIndent('  ').convert(activity.rawData);

    return AlertDialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 100),
      contentPadding: EdgeInsets.zero,
      content: SizedBox(
        width: 800,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(
              height: 16,
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: SingleChildScrollView(
                  child: SelectableText(
                    jsonString,
                    style: TextStyle(
                      color: colorScheme.color8,
                      fontFamily: 'monospace',
                      fontSize: 14,
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Divider(
              thickness: 2,
              height: 2,
              color: colorScheme.color6,
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      // Download button
                      IconButton(
                        icon: Icon(
                          Icons.download,
                          color: colorScheme.color8,
                          size: 20,
                        ),
                        onPressed: () {
                          try {
                            // Create a Blob with the JSON data
                            final blob = html.Blob(
                              [jsonString],
                              'application/json',
                            );

                            // Create a URL for the Blob
                            final url = html.Url.createObjectUrlFromBlob(blob);

                            // Create an anchor element and set its properties
                            final anchor = html.AnchorElement(href: url)
                              ..setAttribute(
                                'download',
                                'activity_${activity.eventName.replaceAll(' ', '_')}_${activity.eventTime.millisecondsSinceEpoch}.json',
                              )
                              ..style.display = 'none';

                            // Add to the DOM
                            html.document.body?.append(anchor);

                            // Trigger the download
                            anchor.click();

                            // Clean up
                            anchor.remove();
                            html.Url.revokeObjectUrl(url);

                            ScaffoldMessenger.of(context).showSnackBar(
                              const SnackBar(
                                content: Text('JSON file downloaded'),
                              ),
                            );
                          } catch (e) {
                            debugPrint('Error downloading JSON: $e');
                            ScaffoldMessenger.of(context).showSnackBar(
                              SnackBar(
                                content: Text('Error downloading JSON: $e'),
                              ),
                            );
                          }
                        },
                      ),
                      // Copy button
                      IconButton(
                        icon: Icon(
                          Icons.copy_outlined,
                          color: colorScheme.color8,
                          size: 20,
                        ),
                        onPressed: () {
                          Clipboard.setData(ClipboardData(text: jsonString));
                          ScaffoldMessenger.of(context).showSnackBar(
                            const SnackBar(
                              content: Text('JSON copied to clipboard'),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                  // Close button
                  ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: colorScheme.color1,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(4),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 8,
                      ),
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('CLOSE'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
