import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/activity_filter_types.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/activity_utils.dart';
import 'package:mevolvesupport/widgets/screens/all_users/activity/components/activity_filter_widget.dart';

class ActivityFilterDialog extends StatefulWidget {
  const ActivityFilterDialog({
    super.key,
    required this.currentFilter,
    required this.onApply,
  });

  final ActivityFilter currentFilter;
  final Function(ActivityFilter) onApply;

  @override
  State<ActivityFilterDialog> createState() => _ActivityFilterDialogState();
}

class _ActivityFilterDialogState extends State<ActivityFilterDialog> {
  late ActivityFilter _filter;
  final TextEditingController _eventNameController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _filter = ActivityFilter(
      eventName: widget.currentFilter.eventName,
      eventType: widget.currentFilter.eventType,
      startFrom: widget.currentFilter.startFrom,
    );

    if (_filter.eventName != null) {
      _eventNameController.text = _filter.eventName!;
    }
  }

  @override
  void dispose() {
    _eventNameController.dispose();
    super.dispose();
  }

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _filter.startFrom ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );

    if (picked != null && picked != _filter.startFrom) {
      setState(() {
        _filter = _filter.copyWith(startFrom: picked);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Dialog(
      backgroundColor: colorScheme.color5,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(8),
      ),
      child: SizedBox(
        width: 715, // Match the width from AllUsersFilterWidget
        // padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
                color: colorScheme.color31,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const MeText(
                    text: 'Filter',
                    meFontStyle: MeFontStyle.A8, // Match user filter style
                  ),
                  InkWell(
                    onTap: () => Navigator.of(context).pop(),
                    child: SizedBox(
                      width: 24,
                      height: 24,
                      child: Assets.svg.closeIcon.svg(),
                    ),
                  ),
                ],
              ),
            ),

            Divider(
              thickness: 2,
              height: 2,
              color: colorScheme.color6,
            ),

            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 16),

                  // Activity section
                  const MeText(
                    text: 'Activity',
                    meFontStyle: MeFontStyle.A8, // Match user filter style
                  ),

                  const SizedBox(height: 16),

                  // Event Name
                  const MeText(
                    text: 'Event Name',
                    meFontStyle: MeFontStyle.B1, // Match user filter style
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _eventNameController,
                    decoration: InputDecoration(
                      filled: false,
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colorScheme.color7),
                      ),
                      enabledBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colorScheme.color7),
                      ),
                      focusedBorder: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide(color: colorScheme.color1),
                      ),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 16,
                      ),
                      hintText: 'Enter event name',
                      hintStyle: const TextStyle(
                        fontFamily: 'Inter',
                        fontSize: 16,
                        color: Color(0xFF8E8E93),
                      ),
                    ),
                    style: MeTextTheme.getMeFontStyle(
                      fontStyle: MeFontStyle.E8,
                      context: context,
                    ),
                    onChanged: (value) {
                      _filter = _filter.copyWith(
                        eventName: value.isEmpty ? null : value,
                      );
                    },
                  ),

                  const SizedBox(height: 24),

                  // Event Type
                  ActivityFilterWidget(
                    filterType: ActivityFilterType.eventType,
                    currentChoice: _filter.eventType == null
                        ? 0
                        : ActivityFilterType.eventType
                            .getTypeOptions()
                            .indexWhere(
                              (option) => option.value == _filter.eventType,
                            ),
                    onFilterChanged: (Map<String, dynamic> value) {
                      setState(() {
                        _filter = _filter.copyWith(
                          eventType: value[ActivityFilterType.eventType.name],
                        );
                      });
                    },
                  ),

                  const SizedBox(height: 24),

                  // Start From
                  ActivityFilterWidget(
                    filterType: ActivityFilterType.startFrom,
                    currentDate: _filter.startFrom,
                    onFilterChanged: (Map<String, dynamic> value) {
                      setState(() {
                        _filter = _filter.copyWith(
                          startFrom: value[ActivityFilterType.startFrom.name],
                        );
                      });
                    },
                    onCustomDateSelected: (context) => _selectDate(context),
                  ),

                  const SizedBox(height: 32),
                ],
              ),
            ),

            // Divider before action buttons
            Divider(
              thickness: 2,
              height: 2,
              color: colorScheme.color6,
            ),

            const SizedBox(height: 16),

            // Action buttons
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Reset button
                  InkWell(
                    onTap: () {
                      setState(() {
                        _filter.clear();
                        _eventNameController.clear();
                      });
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: colorScheme.color3,
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      child: Assets.svg.reloadIcon.svg(
                        colorFilter: ColorFilter.mode(
                          colorScheme.color2,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),

                  Row(
                    children: [
                      // Cancel button
                      Material(
                        color: colorScheme.color3,
                        borderRadius: BorderRadius.circular(6),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(6),
                          onTap: () => Navigator.of(context).pop(),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 54,
                              vertical: 11,
                            ),
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: const MeText(
                              text: 'Cancel',
                              meFontStyle: MeFontStyle.B2,
                            ),
                          ),
                        ),
                      ),

                      const SizedBox(width: 16),

                      // Apply button
                      Material(
                        color: colorScheme.color1,
                        borderRadius: BorderRadius.circular(6),
                        child: InkWell(
                          borderRadius: BorderRadius.circular(6),
                          onTap: () {
                            widget.onApply(_filter);
                            Navigator.of(context).pop();
                          },
                          child: Container(
                            alignment: Alignment.center,
                            padding: const EdgeInsets.symmetric(
                              horizontal: 54,
                              vertical: 11,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: const MeText(
                              text: 'Apply',
                              meFontStyle: MeFontStyle.B12,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }
}
