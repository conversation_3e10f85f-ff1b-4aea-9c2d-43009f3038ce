import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/activity.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/activity_utils.dart';
import 'package:mevolvesupport/widgets/screens/all_users/activity/dialogs/activity_json_dialog.dart';

class ActivityBlock extends StatelessWidget {
  const ActivityBlock({super.key, required this.activity});

  final ActivityItem activity;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return InkWell(
      onTap: () async {
        await showDialog<void>(
          context: context,
          barrierColor: const Color.fromRGBO(0, 0, 0, 0.7),
          builder: (_) {
            return ActivityJsonDialog(
              activity: activity,
            );
          },
          useRootNavigator: false,
        );
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: colorScheme.color5,
          border: Border(
            bottom: BorderSide(color: colorScheme.color6, width: 1),
          ),
        ),
        child: Row(
          children: [
            Expanded(
              child: MeText(
                text: activity.eventName,
                meFontStyle: MeFontStyle.F8,
              ),
            ),
            MeText(
              text: formatTimestamp(activity.eventTime),
              meFontStyle: MeFontStyle.F7,
            ),
          ],
        ),
      ),
    );
  }
}
