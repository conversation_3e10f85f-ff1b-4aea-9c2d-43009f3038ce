import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/activity_filter_types.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_radio_button.dart';

class ActivityFilterWidget extends StatelessWidget {
  const ActivityFilterWidget({
    super.key,
    required this.filterType,
    required this.onFilterChanged,
    this.currentChoice,
    this.currentDate,
    this.onCustomDateSelected,
  });

  final ActivityFilterType filterType;
  final ValueChanged<Map<String, dynamic>> onFilterChanged;
  final int? currentChoice;
  final DateTime? currentDate;
  final Function(BuildContext)? onCustomDateSelected;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final bool isMultipleChoice = filterType.isMultiChoice();
    ValueNotifier<int> selectedChoice = ValueNotifier<int>(currentChoice ?? 0);
    List<TypeOption> typeOptions = filterType.getTypeOptions();

    // Find the index of the current date in date options if it exists
    int dateIndex = 0;
    if (filterType == ActivityFilterType.startFrom && currentDate != null) {
      // Try to find a matching date range
      final now = DateTime.now();
      final diff = now.difference(currentDate!).inDays;

      if (diff <= 7) {
        dateIndex = 1; // Last 7 Days
      } else if (diff <= 30) {
        dateIndex = 2; // Last 30 Days
      } else if (diff <= 90) {
        dateIndex = 3; // Last 90 Days
      } else {
        dateIndex = 4; // Custom
      }

      selectedChoice.value = dateIndex;
    }

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      width: 715,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeText(
            text: filterType.toReadableString(),
            meFontStyle: MeFontStyle.B1,
          ),
          const SizedBox(
            height: 16,
          ),
          DropdownButtonHideUnderline(
            child: DropdownButton2<int>(
              isExpanded: true,
              isDense: true,
              hint: const MeText(
                text: 'Select',
                meFontStyle: MeFontStyle.C7,
              ),
              valueListenable: ValueNotifier<int>(0),
              items: List.generate(
                typeOptions.length,
                (index) {
                  return DropdownItem<int>(
                    value: index,
                    closeOnTap: !isMultipleChoice,
                    height: 56,
                    onTap: () {
                      if (filterType == ActivityFilterType.startFrom &&
                          index == typeOptions.length - 1 &&
                          onCustomDateSelected != null) {
                        // Handle custom date selection
                        onCustomDateSelected!(context);
                      } else {
                        selectedChoice.value = index;
                        if (typeOptions[selectedChoice.value].value == null) {
                          onFilterChanged({
                            filterType.name: null,
                          });
                        } else {
                          if (filterType == ActivityFilterType.startFrom) {
                            // For date filters, pass the actual date value
                            onFilterChanged({
                              filterType.name:
                                  typeOptions[selectedChoice.value].value,
                            });
                          } else {
                            // For other filters, pass the string value
                            onFilterChanged({
                              filterType.name:
                                  typeOptions[selectedChoice.value].value,
                            });
                          }
                        }
                      }
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        MeText(
                          text: typeOptions[index].name,
                          meFontStyle: MeFontStyle.C8,
                        ),
                        if (!isMultipleChoice)
                          ValueListenableBuilder(
                            valueListenable: selectedChoice,
                            builder: (context, value, child) {
                              return MeRadioButton(
                                value: index,
                                groupValue: value,
                                onChanged: (value) {
                                  if (filterType ==
                                          ActivityFilterType.startFrom &&
                                      index == typeOptions.length - 1 &&
                                      onCustomDateSelected != null) {
                                    // Handle custom date selection
                                    onCustomDateSelected!(context);
                                  } else {
                                    selectedChoice.value = index;
                                    if (typeOptions[selectedChoice.value]
                                            .value ==
                                        null) {
                                      onFilterChanged({
                                        filterType.name: null,
                                      });
                                    } else {
                                      if (filterType ==
                                          ActivityFilterType.startFrom) {
                                        // For date filters, pass the actual date value
                                        onFilterChanged({
                                          filterType.name:
                                              typeOptions[selectedChoice.value]
                                                  .value,
                                        });
                                      } else {
                                        // For other filters, pass the string value
                                        onFilterChanged({
                                          filterType.name:
                                              typeOptions[selectedChoice.value]
                                                  .value,
                                        });
                                      }
                                    }
                                  }
                                },
                              );
                            },
                          ),
                      ],
                    ),
                  );
                },
              ),
              //Use last selected item as the current value so if we've limited menu height, it scroll to last item.
              // value: 0,
              onChanged: (value) {},
              selectedItemBuilder: (context) {
                if (selectedChoice.value == 0) {
                  return [
                    const MeText(
                      text: 'Select',
                      meFontStyle: MeFontStyle.C7,
                    ),
                  ];
                }

                if (filterType == ActivityFilterType.startFrom &&
                    currentDate != null &&
                    selectedChoice.value == 4) {
                  // Show the custom date
                  return [
                    ValueListenableBuilder(
                      valueListenable: selectedChoice,
                      builder: (context, value, child) {
                        return MeText(
                          text: DateFormat('d MMM yyyy').format(currentDate!),
                          meFontStyle: MeFontStyle.C8,
                        );
                      },
                    ),
                  ];
                }

                return [
                  ValueListenableBuilder(
                    valueListenable: selectedChoice,
                    builder: (context, value, child) {
                      return MeText(
                        text: typeOptions[selectedChoice.value].name,
                        meFontStyle: MeFontStyle.C8,
                      );
                    },
                  ),
                ];
              },
              iconStyleData: IconStyleData(
                iconDisabledColor: colorScheme.color7,
                iconEnabledColor: colorScheme.color7,
              ),
              buttonStyleData: ButtonStyleData(
                height: 60,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: colorScheme.color7,
                  ),
                ),
              ),
              dropdownStyleData: DropdownStyleData(
                decoration: BoxDecoration(
                  color: colorScheme.color31,
                  borderRadius: BorderRadius.circular(8),
                ),
                padding: EdgeInsets.zero,
                scrollPadding: EdgeInsets.zero,
                maxHeight: 240,
              ),
              dropdownSeparator: DropdownSeparator(
                height: 1,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: Divider(
                    height: 1,
                    thickness: 1,
                    color: colorScheme.color6,
                  ),
                ),
              ),
              menuItemStyleData: MenuItemStyleData(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                overlayColor: WidgetStatePropertyAll(colorScheme.color31),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
