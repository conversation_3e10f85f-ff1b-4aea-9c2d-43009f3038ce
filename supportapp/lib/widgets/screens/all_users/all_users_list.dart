import 'package:data_table_2/data_table_2.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:mevolvesupport/constants/extensions.dart';
import 'package:mevolvesupport/cubits/app_navigation/app_navigation_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/providers/users/users_data_provider.dart';
import 'package:mevolvesupport/providers/users/users_data_state.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/ui/empty_list_screen.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_list_bottom_bar.dart';
import 'package:mevolvesupport/widgets/shared/components/user_status_badge.dart';

class AllUsersList extends StatefulWidget {
  const AllUsersList({super.key});

  @override
  State<AllUsersList> createState() => _AllUsersListState();
}

class _AllUsersListState extends State<AllUsersList>
    with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<UsersDataProvider>().initialize();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<UsersDataProvider, UsersDataState>(
      builder: (context, state) {
        if (state.isLoading) {
          return const Center(child: CircularProgressIndicator());
        }

        if (state.error != null) {
          return _ErrorWidget(error: state.error!);
        }

        if (state.users.isEmpty) {
          return Container(
            color: colorScheme.color6,
            child: const EmptyListScreen(title: 'No users found'),
          );
        }

        return Column(
          children: [
            Expanded(
              child: _UsersDataTable(
                users: state.users,
                onUserSelected: (user) {
                  // Only update UserDetailsCubit - this is the single source of truth for user selection
                  context.read<UserDetailsCubit>().selectUser(user.uid);

                  // Optionally reset to first tab when selecting a new user
                  context.read<AppNavigationCubit>().updateNavigationState(
                        selectedDetailsTab: 0,
                      );
                },
              ),
            ),
            if (state.users.isNotEmpty)
              MeListBottomBar(
                currentCount: state.users.length,
                totalCount: state.totalCount,
                hasMore: state.hasMore,
                isLoading: state.isLoadingMore,
                hasNewUpdates: state.hasNewUpdates,
                onLoadMore: () => context.read<UsersDataProvider>().loadMore(),
                onRefresh: () => context.read<UsersDataProvider>().refresh(),
                itemType: 'Users',
              ),
          ],
        );
      },
    );
  }
}

// Separated data table widget for better organization
class _UsersDataTable extends StatefulWidget {
  const _UsersDataTable({
    required this.users,
    required this.onUserSelected,
  });

  final List<UserMetaData> users;
  final Function(UserMetaData user) onUserSelected;

  @override
  State<_UsersDataTable> createState() => _UsersDataTableState();
}

class _UsersDataTableState extends State<_UsersDataTable> {
  late final ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<UserDetailsCubit, UserDetailsState>(
      buildWhen: (previous, current) =>
          previous.selectedUserId != current.selectedUserId,
      builder: (context, userDetailsState) {
        return Scrollbar(
          controller: _scrollController,
          child: DataTable2(
            showCheckboxColumn: false,
            headingRowHeight: 40,
            columnSpacing: 0,
            minWidth: 800,
            dividerThickness: 1,
            showBottomBorder: true,
            horizontalMargin: 16,
            headingRowColor: WidgetStateProperty.resolveWith<Color?>(
              (states) => colorScheme.color5,
            ),
            border: TableBorder(
              bottom: BorderSide(color: colorScheme.color6, width: 1.5),
              horizontalInside: BorderSide(color: colorScheme.color6, width: 1),
            ),
            isHorizontalScrollBarVisible: false,
            isVerticalScrollBarVisible: false,
            scrollController: _scrollController,
            columns: const [
              DataColumn2(
                label: MeText(text: 'Name', meFontStyle: MeFontStyle.D7),
              ),
              DataColumn2(
                size: ColumnSize.S,
                label: MeText(text: 'Status', meFontStyle: MeFontStyle.D7),
              ),
              DataColumn2(
                label:
                    MeText(text: 'Subscription', meFontStyle: MeFontStyle.D7),
              ),
              DataColumn2(
                size: ColumnSize.S,
                label: MeText(text: 'Created', meFontStyle: MeFontStyle.D7),
              ),
            ],
            rows: List<DataRow2>.generate(
              widget.users.length,
              (index) => _buildUserRow(
                context,
                widget.users[index],
                userDetailsState.selectedUserId == widget.users[index].uid,
              ),
            ),
          ),
        );
      },
    );
  }

  DataRow2 _buildUserRow(
    BuildContext context,
    UserMetaData user,
    bool isSelected,
  ) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final userStatus = getUserStatus(user);

    return DataRow2(
      specificRowHeight: 82,
      color: WidgetStateProperty.resolveWith<Color?>((states) {
        if (states.contains(WidgetState.selected)) {
          return colorScheme.color1;
        }
        return colorScheme.color5;
      }),
      selected: isSelected,
      onSelectChanged: (_) => widget.onUserSelected(user),
      cells: [
        // Name cell
        DataCell(_UserNameCell(user: user, isSelected: isSelected)),
        // Status cell
        DataCell(
          UserStatusBadge(
            userStatus: userStatus,
            userDeletedStatus: user.userDeletedStatus,
            wraptext: true,
            isSelected: isSelected,
            superSubscription: user.superSubscription,
          ),
        ),
        // Subscription cell
        DataCell(_SubscriptionCell(user: user, isSelected: isSelected)),
        // Created cell
        DataCell(
          MeText(
            text: getTimeAgo(user.userInfo.createdAt),
            meFontStyle: isSelected ? MeFontStyle.F12 : MeFontStyle.F8,
          ),
        ),
      ],
    );
  }
}

// Name cell widget
class _UserNameCell extends StatelessWidget {
  const _UserNameCell({
    required this.user,
    required this.isSelected,
  });

  final UserMetaData user;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        MeText(
          text: user.userInfo.pseudoName ?? 'Unknown User',
          meFontStyle: isSelected ? MeFontStyle.F12 : MeFontStyle.F8,
        ),
        const SizedBox(height: 8),
        Tooltip(
          message: 'With us',
          preferBelow: false,
          verticalOffset: 12,
          margin: const EdgeInsets.only(left: 32),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: colorScheme.color10,
          ),
          textStyle: MeTextTheme.getMeFontStyle(
            fontStyle: MeFontStyle.F8,
            context: context,
          ),
          child: MeText(
            text: getDuration(
              fromDateTime: user.userInfo.createdAt,
              toDateTime: user.userInfo.deletedAt,
            ),
            meFontStyle: isSelected ? MeFontStyle.F12 : MeFontStyle.F7,
          ),
        ),
        const SizedBox(height: 8),
        MeText(
          text: _getDateRangeText(user),
          meFontStyle: isSelected ? MeFontStyle.F12 : MeFontStyle.F7,
        ),
      ],
    );
  }

  String _getDateRangeText(UserMetaData user) {
    final startDate = DateFormat('d MMM yyyy').format(user.userInfo.createdAt);
    final endDate = user.userInfo.deletedAt != null
        ? DateFormat('d MMM yyyy').format(user.userInfo.deletedAt!.onlyDate())
        : 'Today';
    return '$startDate - $endDate';
  }
}

// Subscription cell widget
class _SubscriptionCell extends StatelessWidget {
  const _SubscriptionCell({
    required this.user,
    required this.isSelected,
  });

  final UserMetaData user;
  final bool isSelected;

  @override
  Widget build(BuildContext context) {
    final subscriptionInfo = user.subscriptionInfo;
    final expDate = subscriptionInfo.subscriptionExpDate;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        MeText(
          text: subscriptionInfo.entitlement.name,
          meFontStyle: isSelected ? MeFontStyle.F12 : MeFontStyle.F8,
        ),
        const SizedBox(height: 8),
        if (expDate != null) ...[
          MeText(
            text: _getExpiryStatusText(expDate),
            meFontStyle: isSelected ? MeFontStyle.F12 : MeFontStyle.F7,
          ),
          const SizedBox(height: 8),
          MeText(
            text: _getTimeRemainingText(expDate),
            meFontStyle: isSelected ? MeFontStyle.F12 : MeFontStyle.F7,
          ),
        ],
      ],
    );
  }

  String _getExpiryStatusText(DateTime expDate) {
    final isExpired = DateTime.now().onlyDate().isAfter(expDate.onlyDate());
    final prefix = isExpired ? 'Expired on' : 'Valid till';
    return '$prefix ${DateFormat('d MMM yyyy').format(expDate)}';
  }

  String _getTimeRemainingText(DateTime expDate) {
    final isExpired = DateTime.now().onlyDate().isAfter(expDate.onlyDate());
    return isExpired ? getTimeAgo(expDate) : getTimeLeft(expDate);
  }
}

// Error widget
class _ErrorWidget extends StatelessWidget {
  const _ErrorWidget({required this.error});

  final String error;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('Error: $error'),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: () => context.read<UsersDataProvider>().refresh(),
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }
}
