import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/allusers_filter_types.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_checkbox.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_radio_button.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_chip.dart';

class AllUsersFilterWidget extends StatefulWidget {
  const AllUsersFilterWidget({
    super.key,
    required this.filterType,
    required this.onFilterChanged,
    this.currentChoice,
    this.currentOptions,
  });

  final AllUsersFilterType filterType;
  final ValueChanged<Map<String, dynamic>> onFilterChanged;
  final int? currentChoice;
  final List<int>? currentOptions;

  @override
  State<AllUsersFilterWidget> createState() => _AllUsersFilterWidgetState();
}

class _AllUsersFilterWidgetState extends State<AllUsersFilterWidget> {
  late List<int> _selectedOptions;
  late int _selectedChoice;
  late final List<TypeOption> _typeOptions;
  late final bool _isMultipleChoice;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isMultipleChoice = widget.filterType.isMultiChoice();
    _typeOptions = widget.filterType.getTypeOptions();
    _selectedChoice = widget.currentChoice ?? 0;
    _selectedOptions = List<int>.from(widget.currentOptions ?? []);
  }

  @override
  void didUpdateWidget(AllUsersFilterWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.currentChoice != widget.currentChoice) {
      _selectedChoice = widget.currentChoice ?? 0;
    }
    if (oldWidget.currentOptions != widget.currentOptions) {
      _selectedOptions = List<int>.from(widget.currentOptions ?? []);
    }
  }

  void _handleSingleSelection(int index) {
    setState(() {
      _selectedChoice = index;
    });
    final value = index == 0 ? null : index;
    widget.onFilterChanged({widget.filterType.name: value});
  }

  void _handleMultipleSelection(int index) {
    setState(() {
      if (_selectedOptions.contains(index)) {
        _selectedOptions.remove(index);
      } else {
        _selectedOptions.add(index);
      }
      _selectedOptions.sort();
    });
    final value = _selectedOptions.isEmpty ? null : _selectedOptions;
    widget.onFilterChanged({widget.filterType.name: value});
  }

  String _getDisplayText() {
    if (_isMultipleChoice) {
      if (_selectedOptions.isEmpty) return 'Select';
      if (_selectedOptions.length == 1) {
        return _typeOptions[_selectedOptions.first].name;
      }
      return '${_selectedOptions.length} selected';
    } else {
      return _selectedChoice == 0
          ? 'Select'
          : _typeOptions[_selectedChoice].name;
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12.0),
      width: 715,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          MeText(
            text: widget.filterType.toReadableString(),
            meFontStyle: MeFontStyle.B1,
          ),
          const SizedBox(height: 16),
          _buildSelectionField(colorScheme),
        ],
      ),
    );
  }

  Widget _buildSelectionField(MeColorScheme colorScheme) {
    return Column(
      children: [
        // Clickable field
        InkWell(
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
          borderRadius: BorderRadius.circular(8),
          child: Container(
            height: 60,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: _isExpanded ? colorScheme.color1 : colorScheme.color7,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: _isMultipleChoice && _selectedOptions.isNotEmpty
                      ? _buildSelectedChips()
                      : MeText(
                          text: _getDisplayText(),
                          meFontStyle:
                              _selectedChoice == 0 && _selectedOptions.isEmpty
                                  ? MeFontStyle.C7
                                  : MeFontStyle.C8,
                        ),
                ),
                AnimatedRotation(
                  turns: _isExpanded ? 0.5 : 0,
                  duration: const Duration(milliseconds: 200),
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: colorScheme.color7,
                  ),
                ),
              ],
            ),
          ),
        ),

        // Expandable options
        AnimatedSize(
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
          child: _isExpanded
              ? Container(
                  margin: const EdgeInsets.only(top: 8),
                  constraints: const BoxConstraints(maxHeight: 240),
                  decoration: BoxDecoration(
                    color: colorScheme.color31,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: colorScheme.color7),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: ListView.separated(
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      itemCount: _typeOptions.length,
                      separatorBuilder: (context, index) => Divider(
                        height: 1,
                        thickness: 1,
                        color: colorScheme.color6,
                        indent: 16,
                        endIndent: 16,
                      ),
                      itemBuilder: (context, index) {
                        return _buildOptionItem(index, colorScheme);
                      },
                    ),
                  ),
                )
              : const SizedBox.shrink(),
        ),
      ],
    );
  }

  Widget _buildSelectedChips() {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: _selectedOptions.map((index) {
          return Padding(
            padding: const EdgeInsets.only(right: 12.0),
            child: MeChip(
              text: _typeOptions[index].name,
              onTap: () => _handleMultipleSelection(index),
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildOptionItem(int index, MeColorScheme colorScheme) {
    final isSelected = _isMultipleChoice
        ? _selectedOptions.contains(index)
        : _selectedChoice == index;

    return InkWell(
      onTap: () {
        if (_isMultipleChoice) {
          _handleMultipleSelection(index);
        } else {
          _handleSingleSelection(index);
          setState(() {
            _isExpanded = false;
          });
        }
      },
      child: Container(
        height: 56,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        child: Row(
          children: [
            Expanded(
              child: MeText(
                text: _typeOptions[index].name,
                meFontStyle: MeFontStyle.C8,
              ),
            ),
            if (_isMultipleChoice)
              MeCheckBox(
                value: isSelected,
                isDue: false,
                isCircular: false,
                size: 20,
                borderWidth: 1.5,
                checkboxIconColor: colorScheme.color12,
                checkboxUncheckedBorderColor: colorScheme.color7,
                checkboxCheckedBorderColor: colorScheme.color1,
                checkboxUncheckedColor: Colors.transparent,
                checkboxCheckedColor: colorScheme.color1,
              )
            else
              MeRadioButton(
                value: index,
                groupValue: _selectedChoice,
                onChanged: (_) {},
              ),
          ],
        ),
      ),
    );
  }
}
