import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/allusers_filter_types.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:mevolvesupport/widgets/dialogs/show_me_discard_dialog.dart';
import 'package:mevolvesupport/widgets/screens/all_users/filters/all_users_filter_widget.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_filter_chip.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_filter_text_input.dart';

class AllUsersFilterDialog extends StatefulWidget {
  const AllUsersFilterDialog({
    super.key,
    required this.currentFilters,
  });

  final Map<String, dynamic> currentFilters;

  @override
  State<AllUsersFilterDialog> createState() => _AllUsersFilterDialogState();
}

class _AllUsersFilterDialogState extends State<AllUsersFilterDialog> {
  static const _defaultFilters = {
    'subscriptionState': [1],
  };
  static const _deepEq = DeepCollectionEquality();

  late final ValueNotifier<Map<String, dynamic>> _selectedFilters;
  late final TextEditingController _searchController;
  late final ValueNotifier<int> _rebuildTrigger;
  late final Map<String, dynamic> _initialFilters;
  late final String _initialSearchText;

  // Filter sections for better organization
  final List<({String title, List<AllUsersFilterType> filters})>
      _filterSections = [
    (
      title: 'Account',
      filters: [
        AllUsersFilterType.subscriptionType,
        AllUsersFilterType.superSubscription,
        AllUsersFilterType.subscriptionState,
        AllUsersFilterType.deleted,
        AllUsersFilterType.subscriptionExpDate,
        AllUsersFilterType.createdAt,
      ],
    ),
    (
      title: 'Filter',
      filters: [
        AllUsersFilterType.chatStatus,
        AllUsersFilterType.reportStatus,
      ],
    ),
    (
      title: 'Experience',
      filters: [
        AllUsersFilterType.appTheme,
        AllUsersFilterType.isPasscodeEnabled,
        AllUsersFilterType.isBiometricEnabled,
        // AllUsersFilterType.isDailyAgendaEmail, // Removed
        AllUsersFilterType.isDailyAgendaMobile,
      ],
    ),
  ];

  @override
  void initState() {
    super.initState();
    Log.d(
      '🎯 AllUsersFilterDialog: Initializing with filters: ${widget.currentFilters}',
    );

    // Store initial values for comparison
    _initialFilters = Map<String, dynamic>.from(widget.currentFilters)
      ..remove(AllUsersFilterType.searchEmailOrUid.name);
    _initialSearchText =
        (widget.currentFilters[AllUsersFilterType.searchEmailOrUid.name] ?? '')
            .toString()
            .trim();

    _selectedFilters =
        ValueNotifier<Map<String, dynamic>>(Map.from(_initialFilters));
    _searchController = TextEditingController(text: _initialSearchText);
    _rebuildTrigger = ValueNotifier(0);

    // Listen to search changes
    _searchController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    // When search has text, clear other filters
    if (_searchController.text.trim().isNotEmpty &&
        _selectedFilters.value.isNotEmpty) {
      Log.d('🔍 AllUsersFilterDialog: Search active, clearing other filters');
      _selectedFilters.value = {};
    }
    _rebuildTrigger.value++; // Trigger rebuild
  }

  @override
  void dispose() {
    _searchController.removeListener(_onSearchChanged);
    _selectedFilters.dispose();
    _searchController.dispose();
    _rebuildTrigger.dispose();
    super.dispose();
  }

  bool get _hasSearchText => _searchController.text.trim().isNotEmpty;

  bool get _hasChangesFromInitial {
    // Compare current state with initial state (not default state)
    final currentSearch = _searchController.text.trim();
    final currentFilters = Map<String, dynamic>.from(_selectedFilters.value);

    // Check if changed from initial values
    final searchChanged = _initialSearchText != currentSearch;
    final filtersChanged = !_deepEq.equals(_initialFilters, currentFilters);

    final hasChanges = searchChanged || filtersChanged;

    Log.d('🔄 AllUsersFilterDialog: Checking changes from initial - '
        'Initial search: "$_initialSearchText", Current search: "$currentSearch", '
        'Initial filters: $_initialFilters, Current filters: $currentFilters, '
        'Search changed: $searchChanged, Filters changed: $filtersChanged, '
        'Has changes: $hasChanges');

    return hasChanges;
  }

  void _updateFilter(Map<String, dynamic> value) {
    // Don't allow filter updates when search is active
    if (_hasSearchText) {
      Log.d(
        '🚫 AllUsersFilterDialog: Cannot update filters when search is active',
      );
      return;
    }

    final key = value.keys.first;
    final filterValue = value.values.first;

    Log.d('🔧 AllUsersFilterDialog: Updating filter - $key: $filterValue');

    final updatedFilters = Map<String, dynamic>.from(_selectedFilters.value);

    if (filterValue == null ||
        (filterValue is String && filterValue.trim().isEmpty)) {
      updatedFilters.remove(key);
    } else {
      updatedFilters[key] = filterValue;
    }

    _selectedFilters.value = updatedFilters;
  }

  void _resetFilters() {
    Log.d('🔄 AllUsersFilterDialog: Resetting filters to default');
    Log.d('🔄 AllUsersFilterDialog: Default filters: $_defaultFilters');

    _selectedFilters.value = Map<String, dynamic>.from(_defaultFilters);
    _searchController.clear();

    // Trigger rebuild to update UI
    _rebuildTrigger.value++;

    Log.d(
      '🔄 AllUsersFilterDialog: Filters reset to: ${_selectedFilters.value}',
    );
  }

  void _resetToInitialState() {
    Log.d('🔄 AllUsersFilterDialog: Resetting to initial state');
    Log.d('🔄 AllUsersFilterDialog: Initial filters: $_initialFilters');
    Log.d('🔄 AllUsersFilterDialog: Initial search: $_initialSearchText');

    _selectedFilters.value = Map<String, dynamic>.from(_initialFilters);
    _searchController.text = _initialSearchText;

    // Trigger rebuild to update UI
    _rebuildTrigger.value++;

    Log.d('🔄 AllUsersFilterDialog: State reset to initial values');
  }

  void _removeFilter(String key) {
    Log.d('🗑️ AllUsersFilterDialog: Removing filter - $key');

    final updatedFilters = Map<String, dynamic>.from(_selectedFilters.value)
      ..remove(key);
    _selectedFilters.value = updatedFilters;

    // Trigger rebuild to update UI
    _rebuildTrigger.value++;

    Log.d(
      '🗑️ AllUsersFilterDialog: Filter removed, updated filters: ${_selectedFilters.value}',
    );
  }

  Future<void> _handleCancel() async {
    if (!_hasChangesFromInitial) {
      Log.d('👋 AllUsersFilterDialog: Cancel without changes');
      if (mounted) Navigator.pop(context);
      return;
    }

    Log.d('⚠️ AllUsersFilterDialog: Cancel with pending changes');
    final shouldDiscard = await showMeDiscardDialog(
          context,
          'Are you sure to discard the changes?',
        ) ??
        false;

    if (shouldDiscard && mounted) {
      Log.d(
        '🚮 AllUsersFilterDialog: User discarded changes - resetting to initial state',
      );
      // Reset internal state to initial values before closing
      _resetToInitialState();

      Navigator.pop(context);
    } else {
      Log.d('🔙 AllUsersFilterDialog: User cancelled discard');
    }
  }

  Future<void> _handlePopInvoked(bool didPop, dynamic result) async {
    if (!didPop && _hasChangesFromInitial) {
      Log.d('🚪 AllUsersFilterDialog: Back pressed with changes');
      final shouldDiscard = await showMeDiscardDialog(
            context,
            'Are you sure to discard the changes?',
          ) ??
          false;

      if (shouldDiscard && mounted) {
        Log.d(
          '🚮 AllUsersFilterDialog: Back pressed - discarding changes and resetting state',
        );
        // Reset internal state to initial values before closing
        _resetToInitialState();

        Navigator.pop(context);
      }
    } else if (!didPop && mounted) {
      Navigator.pop(context);
    }
  }

  void _applyFilters() {
    final finalFilters = Map<String, dynamic>.from(_selectedFilters.value);

    // Add search if it has value (trimmed)
    final searchText = _searchController.text.trim();
    if (searchText.isNotEmpty) {
      finalFilters[AllUsersFilterType.searchEmailOrUid.name] = searchText;
      // When applying with search, remove other filters
      finalFilters.removeWhere(
        (key, value) => key != AllUsersFilterType.searchEmailOrUid.name,
      );
    } else {
      finalFilters.remove(AllUsersFilterType.searchEmailOrUid.name);
    }

    Log.d('✅ AllUsersFilterDialog: Applying filters - $finalFilters');
    Navigator.pop(context, finalFilters);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    // Get the keyboard height
    final keyboardHeight = MediaQuery.of(context).viewInsets.bottom;
    // Calculate available height
    final screenHeight = MediaQuery.of(context).size.height;
    final dialogMaxHeight =
        screenHeight - 200; // 100 padding top + 100 padding bottom

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: _handlePopInvoked,
      child: AlertDialog(
        insetPadding: EdgeInsets.symmetric(
          horizontal: 0,
          vertical: keyboardHeight > 0
              ? 20
              : 100, // Reduce padding when keyboard is open
        ),
        contentPadding: EdgeInsets.zero,
        backgroundColor: colorScheme.color5,
        content: Container(
          width: 750,
          height: keyboardHeight > 0
              ? dialogMaxHeight -
                  keyboardHeight // Adjust height when keyboard is open
              : null, // Use intrinsic height when keyboard is closed
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: colorScheme.color5,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(colorScheme),
              Divider(thickness: 2, height: 2, color: colorScheme.color6),
              Flexible(
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      _buildFilterContent(colorScheme),
                      _buildSelectedFilters(),
                    ],
                  ),
                ),
              ),
              Divider(thickness: 2, height: 2, color: colorScheme.color6),
              _buildActions(colorScheme),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(MeColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
        color: colorScheme.color31,
      ),
      child: Row(
        children: [
          Expanded(
            child: MeFilterTextInput(
              textEditingController: _searchController,
              hintText: 'Search by email or UID',
              onChanged: (value) {
                Log.d(
                  '🔍 AllUsersFilterDialog: Search changed - "${value.values.first}"',
                );
                // Don't update _selectedFilters for search, just use controller
              },
            ),
          ),
          const SizedBox(width: 24),
          InkWell(
            onTap: () {
              Log.d('❌ AllUsersFilterDialog: Close button pressed');
              Navigator.pop(context);
            },
            child: SizedBox(
              width: 24,
              height: 24,
              child: Assets.svg.closeIcon.svg(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilterContent(MeColorScheme colorScheme) {
    return ValueListenableBuilder<int>(
      valueListenable: _rebuildTrigger,
      builder: (context, _, __) {
        final isSearchActive = _hasSearchText;

        return Opacity(
          opacity: isSearchActive ? 0.5 : 1.0,
          child: IgnorePointer(
            ignoring: isSearchActive,
            child: Container(
              padding: const EdgeInsets.all(16),
              color: Colors.transparent,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (isSearchActive)
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.only(bottom: 16),
                      decoration: BoxDecoration(
                        color: colorScheme.color10.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: colorScheme.color10),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 16,
                            color: colorScheme.color10,
                          ),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: MeText(
                              text:
                                  'Other filters are disabled when search is active',
                              meFontStyle: MeFontStyle.D7,
                            ),
                          ),
                        ],
                      ),
                    ),
                  for (final section in _filterSections) ...[
                    if (section != _filterSections.first)
                      const SizedBox(height: 12),
                    MeText(text: section.title, meFontStyle: MeFontStyle.A8),
                    const SizedBox(height: 10),
                    ...section.filters.map(
                      (filterType) =>
                          ValueListenableBuilder<Map<String, dynamic>>(
                        valueListenable: _selectedFilters,
                        builder: (context, filters, child) {
                          return _buildFilterWidget(filterType, filters);
                        },
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFilterWidget(
    AllUsersFilterType filterType,
    Map<String, dynamic> filters,
  ) {
    final currentValue = filters[filterType.name];

    // Determine if this is a multi-select (list) or single-select filter
    final isMultiSelect = const [
      AllUsersFilterType.subscriptionType,
      AllUsersFilterType.superSubscription,
      AllUsersFilterType.subscriptionState,
      AllUsersFilterType.deleted,
      AllUsersFilterType.chatStatus,
      AllUsersFilterType.reportStatus,
    ].contains(filterType);

    return AllUsersFilterWidget(
      key: ValueKey(filterType),
      filterType: filterType,
      currentOptions: isMultiSelect ? currentValue : null,
      currentChoice: !isMultiSelect ? currentValue : null,
      onFilterChanged: _updateFilter,
    );
  }

  Widget _buildSelectedFilters() {
    return ValueListenableBuilder<Map<String, dynamic>>(
      valueListenable: _selectedFilters,
      builder: (context, filters, child) {
        final displayFilters = filters.entries
            .where(
              (e) =>
                  e.value != null &&
                  e.key != AllUsersFilterType.searchEmailOrUid.name &&
                  !(e.value is List && (e.value as List).isEmpty),
            )
            .toList();

        if (displayFilters.isEmpty || _hasSearchText) {
          return const SizedBox.shrink();
        }

        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Divider(
              thickness: 1,
              height: 1,
              color: Theme.of(context).extension<MeColorScheme>()!.color6,
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16.0),
              child: Wrap(
                spacing: 12,
                runSpacing: 12,
                children: [
                  for (final filter in displayFilters)
                    MeFilterChip(
                      key: ValueKey(filter.key),
                      label: AllUsersFilterType.values
                          .byName(filter.key)
                          .toReadableString(),
                      value: _getFilterDisplayValue(filter),
                      onTap: () => _removeFilter(filter.key),
                    ),
                ],
              ),
            ),
          ],
        );
      },
    );
  }

  String _getFilterDisplayValue(MapEntry<String, dynamic> filter) {
    final filterType = AllUsersFilterType.values.byName(filter.key);
    final typeOptions = filterType.getTypeOptions();

    if (filterType.isMultiChoice() && filter.value is List) {
      final selectedOptions = filter.value as List<int>;
      return selectedOptions.map((index) => typeOptions[index].name).join(', ');
    } else if (filter.value is int) {
      return typeOptions[filter.value].name;
    } else {
      return filter.value.toString();
    }
  }

  Widget _buildActions(MeColorScheme colorScheme) {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: ValueListenableBuilder<Map<String, dynamic>>(
        valueListenable: _selectedFilters,
        builder: (context, filters, child) {
          return ValueListenableBuilder<int>(
            valueListenable: _rebuildTrigger,
            builder: (context, _, child) {
              // Check if current state is different from default
              final currentSearch = _searchController.text.trim();
              final isNotDefault = !_deepEq.equals(filters, _defaultFilters) ||
                  currentSearch.isNotEmpty;

              return Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Reset button - enabled when not in default state
                  InkWell(
                    onTap: isNotDefault ? _resetFilters : null,
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: isNotDefault
                            ? colorScheme.color3
                            : colorScheme.color6,
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      child: Assets.svg.reloadIcon.svg(
                        colorFilter: ColorFilter.mode(
                          isNotDefault
                              ? colorScheme.color2
                              : colorScheme.color7,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                  // Cancel and Apply buttons
                  Row(
                    children: [
                      _buildActionButton(
                        text: 'Cancel',
                        onTap: _handleCancel,
                        backgroundColor: colorScheme.color3,
                        textStyle: MeFontStyle.B2,
                      ),
                      const SizedBox(width: 16),
                      _buildActionButton(
                        text: 'Apply',
                        onTap: _hasChangesFromInitial ? _applyFilters : null,
                        backgroundColor: _hasChangesFromInitial
                            ? colorScheme.color1
                            : colorScheme.color4,
                        textStyle: _hasChangesFromInitial
                            ? MeFontStyle.B12
                            : MeFontStyle.D12,
                      ),
                    ],
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildActionButton({
    required String text,
    required VoidCallback? onTap,
    required Color backgroundColor,
    required MeFontStyle textStyle,
  }) {
    return Material(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(6),
      child: InkWell(
        borderRadius: BorderRadius.circular(6),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 54, vertical: 11),
          alignment: Alignment.center,
          child: MeText(
            text: text,
            meFontStyle: textStyle,
          ),
        ),
      ),
    );
  }
}
