import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:mevolvesupport/blocs/app/app_bloc.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/providers/firebase_functions.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_text_input.dart';

class PITRRecoveryScreen extends StatefulWidget {
  const PITRRecoveryScreen({super.key});

  @override
  State<PITRRecoveryScreen> createState() => _PITRRecoveryScreenState();
}

class _PITRRecoveryScreenState extends State<PITRRecoveryScreen> {
  final TextEditingController uidsController = TextEditingController();
  final TextEditingController dateTimeController = TextEditingController();
  final TextEditingController excludedCollectionsController =
      TextEditingController();
  DateTime? selectedDateTime;
  bool isLoading = false;
  String? errorMessage;
  String? successMessage;
  Map<String, dynamic>? lastResult;

  @override
  void initState() {
    super.initState();
    // Set default timestamp to 1 hour ago
    selectedDateTime = DateTime.now().subtract(const Duration(hours: 1));
    dateTimeController.text =
        DateFormat('yyyy-MM-dd HH:mm').format(selectedDateTime!);
  }

  @override
  void dispose() {
    uidsController.dispose();
    dateTimeController.dispose();
    excludedCollectionsController.dispose();
    super.dispose();
  }

  List<String> getUidList() {
    return uidsController.text
        .split(',')
        .map((uid) => uid.trim())
        .where((uid) => uid.isNotEmpty)
        .toList();
  }

  List<String>? getExcludedCollections() {
    final collections = excludedCollectionsController.text
        .split(',')
        .map((collection) => collection.trim())
        .where((collection) => collection.isNotEmpty)
        .toList();

    return collections.isNotEmpty ? collections : null;
  }

  Future<void> _showDateTimePicker() async {
    // Show date picker
    final DateTime? pickedDate = await showDatePicker(
      context: context,
      initialDate:
          selectedDateTime ?? DateTime.now().subtract(const Duration(hours: 1)),
      firstDate: DateTime.now().subtract(const Duration(days: 30)),
      lastDate: DateTime.now(),
    );

    if (pickedDate != null && mounted) {
      // Show time picker
      final TimeOfDay? pickedTime = await showTimePicker(
        context: context,
        initialTime: TimeOfDay.fromDateTime(
          selectedDateTime ?? DateTime.now().subtract(const Duration(hours: 1)),
        ),
      );

      if (pickedTime != null && context.mounted) {
        setState(() {
          selectedDateTime = DateTime(
            pickedDate.year,
            pickedDate.month,
            pickedDate.day,
            pickedTime.hour,
            pickedTime.minute,
          );
          dateTimeController.text =
              DateFormat('yyyy-MM-dd HH:mm').format(selectedDateTime!);
        });
      }
    }
  }

  Future<bool> showConfirmationDialog({required bool isEmptyUids}) async {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    String title =
        isEmptyUids ? 'Warning: No User IDs Provided' : 'Confirm Recovery';

    String content = isEmptyUids
        ? 'No User IDs were provided. This will affect ALL users in the database. Are you sure you want to proceed?'
        : 'This will restore data for the specified users to ${DateFormat('yyyy-MM-dd HH:mm').format(selectedDateTime!)}. This action cannot be undone. Continue?';

    return await showDialog<bool>(
          context: context,
          builder: (BuildContext context) {
            return AlertDialog(
              backgroundColor: colorScheme.color5,
              title: MeText(
                text: title,
                meFontStyle: MeFontStyle.B7,
              ),
              content: MeText(
                text: content,
                meFontStyle: MeFontStyle.B8,
              ),
              actions: <Widget>[
                TextButton(
                  child: const MeText(
                    text: 'Cancel',
                    meFontStyle: MeFontStyle.B8,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(false);
                  },
                ),
                TextButton(
                  child: const MeText(
                    text: 'Confirm',
                    meFontStyle: MeFontStyle.B8,
                  ),
                  onPressed: () {
                    Navigator.of(context).pop(true);
                  },
                ),
              ],
            );
          },
        ) ??
        false;
  }

  bool validateInputs() {
    // Check if date is valid
    if (selectedDateTime == null) {
      setState(() {
        errorMessage = 'Please select a valid date and time';
        successMessage = null;
      });
      return false;
    }

    // Check if date is in the future
    if (selectedDateTime!.isAfter(DateTime.now())) {
      setState(() {
        errorMessage = 'Recovery point cannot be in the future';
        successMessage = null;
      });
      return false;
    }

    // Check if date is too old (more than 30 days)
    if (selectedDateTime!
        .isBefore(DateTime.now().subtract(const Duration(days: 30)))) {
      setState(() {
        errorMessage = 'Recovery point cannot be older than 30 days';
        successMessage = null;
      });
      return false;
    }

    return true;
  }

  void performRecovery() async {
    final uidList = getUidList();

    // Validate inputs
    if (!validateInputs()) {
      return;
    }

    // Show confirmation dialog based on whether UIDs are provided
    final bool confirmed =
        await showConfirmationDialog(isEmptyUids: uidList.isEmpty);
    if (!confirmed) {
      return;
    }

    setState(() {
      isLoading = true;
      errorMessage = null;
      successMessage = null;
      lastResult = null;
    });

    try {
      // Call the Firebase function with the correct parameters
      final result =
          await FirebaseFunctionsRepository().restoreDatabaseToTimestamp(
        timestamp: selectedDateTime!,
        uids: uidList,
        excludedCollections: getExcludedCollections(),
      );

      setState(() {
        lastResult = result;
        successMessage = uidList.isEmpty
            ? 'Recovery initiated successfully for ALL users'
            : 'Recovery initiated successfully for ${uidList.length} user(s)';
        isLoading = false;
      });
    } catch (e) {
      setState(() {
        errorMessage = 'Error: ${e.toString()}';
        isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<AppBloc, AppState>(
      builder: (context, appState) {
        final hasWritePermission = hasPermission(
          context: context,
          permissionType: PermissionType.pitrRecovery,
          isWrite: true,
        );

        return SingleChildScrollView(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    const MeText(
                      text: 'Point-In-Time Recovery',
                      meFontStyle: MeFontStyle.B7,
                    ),
                    const SizedBox(width: 16),
                    if (!hasWritePermission)
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 4,
                        ),
                        decoration: BoxDecoration(
                          color: colorScheme.color10,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: const MeText(
                          text: 'READ ONLY',
                          meFontStyle: MeFontStyle.F7,
                        ),
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                const MeText(
                  text:
                      'This tool allows you to restore user data to a specific point in time.\nEnter comma-separated user IDs and select the target date and time for restoration.',
                  meFontStyle: MeFontStyle.B7,
                ),
                const SizedBox(height: 32),

                // User IDs input section
                const MeText(
                  text: 'User IDs (comma-separated)',
                  meFontStyle: MeFontStyle.B8,
                ),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(
                    color: colorScheme.color5,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: colorScheme.color6),
                  ),
                  child: MeTextInput(
                    hintText:
                        'Leave empty to affect ALL users (use with caution)',
                    textEditingController: uidsController,
                  ),
                ),
                const SizedBox(height: 24),
                // Date and time selection
                const MeText(
                  text: 'Recovery Point (Date and Time)',
                  meFontStyle: MeFontStyle.B8,
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: hasWritePermission ? _showDateTimePicker : null,
                        child: Container(
                          decoration: BoxDecoration(
                            color: colorScheme.color5,
                            borderRadius: BorderRadius.circular(4),
                            border: Border.all(color: colorScheme.color6),
                          ),
                          child: AbsorbPointer(
                            absorbing: true,
                            child: MeTextInput(
                              hintText: 'Select date and time',
                              textEditingController: dateTimeController,
                            ),
                          ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    IconButton(
                      icon: Icon(
                        Icons.calendar_today,
                        color: hasWritePermission
                            ? colorScheme.color35
                            : colorScheme.color10,
                      ),
                      onPressed:
                          hasWritePermission ? _showDateTimePicker : null,
                    ),
                  ],
                ),
                const SizedBox(height: 24),

                // Optional excluded collections
                const MeText(
                  text: 'Excluded Collections (Optional, comma-separated)',
                  meFontStyle: MeFontStyle.B8,
                ),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(
                    color: colorScheme.color5,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(color: colorScheme.color6),
                  ),
                  child: MeTextInput(
                    hintText: 'e.g. viewSettings, habitSetups',
                    textEditingController: excludedCollectionsController,
                  ),
                ),
                const SizedBox(height: 24),

                // Error and success messages
                if (errorMessage != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: Colors.red.withValues(alpha: 0.5),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.error_outline, color: Colors.red),
                          const SizedBox(width: 8),
                          Expanded(
                            child: MeText(
                              text: errorMessage!,
                              meFontStyle: MeFontStyle.B8,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                if (successMessage != null)
                  Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(4),
                        border: Border.all(
                          color: Colors.green.withValues(alpha: 0.5),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(
                            Icons.check_circle_outline,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: MeText(
                              text: successMessage!,
                              meFontStyle: MeFontStyle.B8,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),

                const SizedBox(height: 8),

                // Action buttons
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    _buildActionButton(
                      text: 'Cancel',
                      onTap: hasWritePermission
                          ? () => Navigator.of(context).pop()
                          : null,
                      backgroundColor: hasWritePermission
                          ? colorScheme.color3
                          : colorScheme.color10,
                      textStyle: MeFontStyle.B2,
                    ),
                    const SizedBox(width: 16),
                    _buildActionButton(
                      text: hasWritePermission
                          ? 'Start Recovery'
                          : 'Read Only Access',
                      onTap: hasWritePermission && !isLoading
                          ? performRecovery
                          : null,
                      backgroundColor: hasWritePermission
                          ? colorScheme.color1
                          : colorScheme.color10,
                      textStyle:
                          hasWritePermission ? MeFontStyle.B12 : MeFontStyle.B2,
                      isLoading: isLoading && hasWritePermission,
                    ),
                  ],
                ),
                const SizedBox(height: 24),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required String text,
    required VoidCallback? onTap,
    required Color backgroundColor,
    required MeFontStyle textStyle,
    bool isLoading = false,
  }) {
    return Material(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(6),
      child: InkWell(
        borderRadius: BorderRadius.circular(6),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 54, vertical: 11),
          alignment: Alignment.center,
          child: isLoading
              ? SizedBox(
                  height: 16,
                  width: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    color:
                        Theme.of(context).extension<MeColorScheme>()!.color12,
                  ),
                )
              : MeText(
                  text: text,
                  meFontStyle: textStyle,
                ),
        ),
      ),
    );
  }
}
