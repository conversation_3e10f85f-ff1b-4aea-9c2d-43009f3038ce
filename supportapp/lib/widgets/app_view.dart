import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/blocs/app/app_bloc.dart';
import 'package:mevolvesupport/blocs/chats/chats_bloc.dart';
import 'package:mevolvesupport/blocs/issueReports/issue_reports_bloc.dart';
import 'package:mevolvesupport/blocs/messages/messages_bloc.dart';
import 'package:mevolvesupport/blocs/usersFeedback/feedbacks_bloc.dart';
import 'package:mevolvesupport/cubits/app/app_cubit.dart';
import 'package:mevolvesupport/cubits/app_navigation/app_navigation_cubit.dart';
import 'package:mevolvesupport/cubits/chats/chats_cubit.dart';
import 'package:mevolvesupport/cubits/feedbacks/feedbacks_cubit.dart';
import 'package:mevolvesupport/cubits/issueReports/issue_reports_cubit.dart';
import 'package:mevolvesupport/cubits/login/login_cubit.dart';
import 'package:mevolvesupport/cubits/messages/messages_cubit.dart';
import 'package:mevolvesupport/cubits/subscription/subscription_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/cubits/userUpdates/user_updates_cubit.dart';
import 'package:mevolvesupport/enums/tab_type.dart';
import 'package:mevolvesupport/enums/theme_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/providers/admin/admin_data_provider.dart';
import 'package:mevolvesupport/providers/chats/chat_data_provider.dart';
import 'package:mevolvesupport/providers/delete_reports/delete_reports_data_provider.dart';
import 'package:mevolvesupport/providers/feedbacks/feedback_data_provider.dart';
import 'package:mevolvesupport/providers/firebase_authentication.dart';
import 'package:mevolvesupport/providers/segment_details/segment_details_cubit.dart';
import 'package:mevolvesupport/providers/segments/segments_data_provider.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/providers/users/users_data_provider.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/repositories/functions_repository.dart';
import 'package:mevolvesupport/repositories/storage_repository.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/me_theme.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/screens/admin_panel/admin_panel_list.dart';
import 'package:mevolvesupport/widgets/screens/admin_panel/admin_panel_subtabs.dart';
import 'package:mevolvesupport/widgets/layout/details_page.dart';
import 'package:mevolvesupport/widgets/screens/auth/login_page.dart';
import 'package:mevolvesupport/widgets/shared/debug/developer_overlay.dart';
import 'package:mevolvesupport/widgets/shared/ui/no_access_widget.dart';
import 'package:mevolvesupport/widgets/screens/pitr_recovery/pitr_recovery.dart';
import 'package:mevolvesupport/widgets/dialogs/profile_settings_dialog.dart';
import 'package:mevolvesupport/widgets/screens/schema/schema_list.dart';
import 'package:mevolvesupport/widgets/screens/all_users/all_users_list.dart';
import 'package:mevolvesupport/widgets/screens/all_users/filters/all_users_filter.dart';
import 'package:mevolvesupport/widgets/screens/delete_reports/delete_reports_filter.dart';
import 'package:mevolvesupport/widgets/screens/delete_reports/delete_reports_list.dart';
import 'package:mevolvesupport/widgets/screens/feedback/feedback_subtabs.dart';
import 'package:mevolvesupport/widgets/screens/feedback/feedbacks_list.dart';
import 'package:mevolvesupport/widgets/screens/support_chat/chats_list.dart';
import 'package:mevolvesupport/widgets/screens/support_chat/chats_subtabs.dart';
import 'package:mevolvesupport/widgets/screens/segments/segment_subtabs.dart';
import 'package:mevolvesupport/widgets/screens/segments/segments_list.dart';
import 'package:mevolvesupport/widgets/screens/auth/splash_page.dart';
import 'package:mevolvesupport/widgets/layout/tab_widget.dart';

final GlobalKey<ScaffoldMessengerState> scaffoldMessengerKey =
    GlobalKey<ScaffoldMessengerState>();

class AppView extends StatefulWidget {
  const AppView({super.key});

  @override
  State<AppView> createState() => _AppViewState();
}

class _AppViewState extends State<AppView> {
  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (_) => LoginCubit(
            context.read<FirebaseAuthenticationRepository>(),
            context.read<AppBloc>(),
          ),
        ),
        BlocProvider(
          create: (context) => AppNavigationCubit(),
        ),
      ],
      child: BlocBuilder<UserSettingsCubit, UserSettingsState>(
        buildWhen: (previous, current) =>
            previous.lightColorScheme != current.lightColorScheme ||
            previous.darkColorScheme != current.darkColorScheme ||
            previous.appThemeMode != current.appThemeMode,
        builder: (context, state) {
          return MaterialApp(
            debugShowCheckedModeBanner: false,
            theme: MeTheme.theme(state.lightColorScheme, Brightness.light),
            darkTheme: MeTheme.theme(state.darkColorScheme, Brightness.dark),
            themeMode: themeTypeToThemeMode(state.appThemeMode),
            home: BlocConsumer<AppBloc, AppState>(
              buildWhen: (previous, current) =>
                  previous.appAuthStatus != current.appAuthStatus,
              listenWhen: (previous, current) =>
                  previous.supportUser != current.supportUser,
              listener: (context, state) {
                if (state.supportUser != null) {
                  context
                      .read<UserSettingsCubit>()
                      .updateThemeOnUserChange(state.supportUser!);
                } else {
                  context.read<UserSettingsCubit>().setDefaultTheme();
                }
              },
              builder: (context, state) {
                return state.appAuthStatus == AppStatus.authenticated
                    ? const _AuthenticatedPage()
                    : state.appAuthStatus == AppStatus.unauthenticated
                        ? const LoginPage()
                        : const SplashPage();
              },
            ),
          );
        },
      ),
    );
  }
}

class _AuthenticatedPage extends StatelessWidget {
  const _AuthenticatedPage();

  @override
  Widget build(BuildContext context) {
    final databaseRepo = context.read<DatabaseRepository>();
    final storageRepo = context.read<StorageRepository>();
    final functionsRepo = context.read<FunctionsRepository>();

    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => AppCubit(databaseRepo)
            ..updateSupportTabCount(
              hasPerms: AppNavigationCubit.hasTabPermission(
                context,
                TabType.supportChat,
              ),
            )
            ..updatefeedbacksTabCount(
              hasPerms: AppNavigationCubit.hasTabPermission(
                context,
                TabType.feedback,
              ),
            )
            ..listenSupportUsers()
            ..listenInternetConnection(),
        ),
        BlocProvider(
          create: (_) =>
              AdminDataProvider(databaseRepository: databaseRepo)..initialize(),
        ),
        BlocProvider(create: (_) => UserCubit(databaseRepo)),
        BlocProvider(create: (_) => UserUpdatesCubit(databaseRepo)),
        BlocProvider(
          create: (_) =>
              ChatDataProvider(databaseRepository: databaseRepo)..initialize(),
        ),
        BlocProvider(
          create: (_) => UsersDataProvider(databaseRepository: databaseRepo),
        ),
        BlocProvider(
          create: (_) => FeedbackDataProvider(databaseRepository: databaseRepo)
            ..initialize(),
        ),
        BlocProvider(
          create: (_) =>
              DeleteReportsDataProvider(databaseRepository: databaseRepo),
        ),
        BlocProvider(
          create: (_) => SegmentsDataProvider(databaseRepository: databaseRepo),
        ),
        BlocProvider(
          create: (_) => UserDetailsCubit(
            databaseRepository: databaseRepo,
            functionsRepository: functionsRepo,
          ),
        ),
        BlocProvider(
          create: (_) => SegmentDetailsCubit(
            databaseRepository: databaseRepo,
          ),
        ),
        BlocProvider(create: (_) => ChatsCubit(databaseRepo)),
        BlocProvider(create: (_) => ChatsBloc(databaseRepo)),
        BlocProvider(create: (_) => MessagesCubit(databaseRepo, storageRepo)),
        BlocProvider(create: (_) => MessagesBloc(databaseRepo)),
        BlocProvider(create: (_) => IssueReportsCubit(databaseRepo)),
        BlocProvider(create: (_) => IssueReportsBloc(databaseRepo)),
        BlocProvider(
          create: (_) => FeedbacksCubit(databaseRepo)..listenFeedbacksCount(),
        ),
        BlocProvider(create: (_) => FeedbacksBloc(databaseRepo)),
        BlocProvider(create: (_) => SubscriptionCubit(databaseRepo)),
      ],
      child: const DeveloperOverlay(
        child: _MainScaffold(),
      ),
    );
  }
}

// Main scaffold with 4 widgets architecture
class _MainScaffold extends StatelessWidget {
  const _MainScaffold();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return SafeArea(
      child: Scaffold(
        backgroundColor: colorScheme.color5,
        body: Column(
          children: [
            const _InternetConnectionBar(),
            Expanded(
              child: Row(
                children: [
                  // Widget 1: Sidebar
                  const _SidebarWidget(),
                  VerticalDivider(
                    thickness: 2,
                    width: 2,
                    color: colorScheme.color6,
                  ),
                  // Main content area containing filter, content, and details
                  Expanded(
                    child: Column(
                      children: [
                        // Widget 2: Filter Bar
                        const _FilterBarWidget(),
                        Divider(
                          height: 2,
                          thickness: 2,
                          color: colorScheme.color6,
                        ),
                        // Content area with content screen and details panel
                        Expanded(
                          child: Row(
                            children: [
                              // Widget 3: Content Screen
                              const Expanded(
                                flex: 4,
                                child: _ContentScreenWidget(),
                              ),
                              VerticalDivider(
                                thickness: 2,
                                width: 2,
                                color: colorScheme.color6,
                              ),
                              // Widget 4: Details Panel
                              const _DetailsPanelWidget(),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Internet connection status bar
class _InternetConnectionBar extends StatelessWidget {
  const _InternetConnectionBar();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppCubit, AppCubitState>(
      builder: (context, state) {
        if (!state.isConnected) {
          final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
          return Container(
            color: colorScheme.color28,
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                MeText(
                  text: 'Check internet connection',
                  meFontStyle: MeFontStyle.F11,
                ),
              ],
            ),
          );
        }
        return const SizedBox.shrink();
      },
    );
  }
}

// Widget 1: Sidebar Widget
class _SidebarWidget extends StatelessWidget {
  const _SidebarWidget();

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          Assets.svg.mevolveIcon.svg(),
          const SizedBox(height: 48),
          Expanded(
            child: BlocBuilder<AppNavigationCubit, AppNavigationState>(
              buildWhen: (prev, curr) =>
                  prev.selectedScreen != curr.selectedScreen,
              builder: (context, navState) {
                return SingleChildScrollView(
                  padding: const EdgeInsets.only(right: 12),
                  child: Column(
                    children: TabType.values.map((tabType) {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 32),
                        child: TabWidget(
                          iconPath: tabType.getTabIcon(),
                          isSelected: tabType == navState.selectedScreen,
                          count: _getTabBadgeCount(context, tabType),
                          onTap: () => context
                              .read<AppNavigationCubit>()
                              .updateNavigationState(selectedScreen: tabType),
                        ),
                      );
                    }).toList(),
                  ),
                );
              },
            ),
          ),
          const _ProfileAvatar(),
        ],
      ),
    );
  }

  int? _getTabBadgeCount(BuildContext context, TabType tabType) {
    switch (tabType) {
      case TabType.supportChat:
        final chatDataProvider = context.watch<ChatDataProvider?>();
        // Only show count if data is loaded (not loading and has data)
        if (chatDataProvider?.state.isLoading == true ||
            chatDataProvider?.state.totalCounts.notReplied == 0) {
          return null; // Don't show badge while loading or if count is 0
        }
        return chatDataProvider?.state.totalCounts.notReplied;
      case TabType.feedback:
        final feedbackDataProvider = context.watch<FeedbackDataProvider?>();
        // Only show count if data is loaded (not loading and has data)
        if (feedbackDataProvider?.state.isLoading == true ||
            feedbackDataProvider?.state.totalCount == 0) {
          return null; // Don't show badge while loading or if count is 0
        }
        return feedbackDataProvider?.state.totalCount;
      default:
        return null;
    }
  }
}

// Widget 2: Filter Bar Widgets
class _FilterBarWidget extends StatelessWidget {
  const _FilterBarWidget();

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppNavigationCubit, AppNavigationState>(
      buildWhen: (prev, curr) => prev.selectedScreen != curr.selectedScreen,
      builder: (context, navState) {
        if (!AppNavigationCubit.hasTabPermission(
          context,
          navState.selectedScreen,
        )) {
          return const SizedBox();
        }

        return _getFilterWidget(navState.selectedScreen);
      },
    );
  }

  Widget _getFilterWidget(TabType tabType) {
    switch (tabType) {
      case TabType.allUsers:
        return const AllUsersFilter();
      case TabType.supportChat:
        return const ChatsSubtabs();
      case TabType.feedback:
        return const FeedbackSubtabs();
      case TabType.deleteReports:
        return const DeleteReportsFilter();
      case TabType.adminPanel:
        return const AdminListSubtabs();
      case TabType.schema:
        return const SizedBox();
      case TabType.segments:
        return const SegmentSubtabs();
      case TabType.pitrRecovery:
        return const SizedBox();
    }
  }
}

// Widget 3: Content Screen Widget
class _ContentScreenWidget extends StatefulWidget {
  const _ContentScreenWidget();

  @override
  State<_ContentScreenWidget> createState() => _ContentScreenWidgetState();
}

class _ContentScreenWidgetState extends State<_ContentScreenWidget> {
  late final PageController _pageController;

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AppNavigationCubit, AppNavigationState>(
      listenWhen: (prev, curr) => prev.selectedScreen != curr.selectedScreen,
      listener: (context, navState) {
        final targetIndex = TabType.values.indexOf(navState.selectedScreen);
        _pageController.jumpToPage(targetIndex);
      },
      child: PageView(
        controller: _pageController,
        physics: const NeverScrollableScrollPhysics(),
        children: TabType.values.map((tabType) {
          return AppNavigationCubit.hasTabPermission(context, tabType)
              ? _getContentWidget(tabType)
              : const NoAccessWidget();
        }).toList(),
      ),
    );
  }

  Widget _getContentWidget(TabType tabType) {
    switch (tabType) {
      case TabType.allUsers:
        return const AllUsersList();
      case TabType.supportChat:
        return const ChatsList();
      case TabType.feedback:
        return const FeedbacksList();
      case TabType.deleteReports:
        return const DeleteReportsList();
      case TabType.adminPanel:
        return const AdminList();
      case TabType.schema:
        return const SchemaList();
      case TabType.segments:
        return const SegmentsList();
      case TabType.pitrRecovery:
        return const PITRRecoveryScreen();
    }
  }
}

// Widget 4: Details Panel Widget
class _DetailsPanelWidget extends StatelessWidget {
  const _DetailsPanelWidget();

  @override
  Widget build(BuildContext context) {
    return BlocListener<AppNavigationCubit, AppNavigationState>(
      listenWhen: (prev, curr) => prev.selectedScreen != curr.selectedScreen,
      listener: (context, navState) {
        // Clear detail states when switching tabs to prevent overlapping detail screens
        context.read<UserDetailsCubit>().clearUser();
        context.read<SegmentDetailsCubit>().clearSegment();

        // Clear feedback and report selection states to prevent them from persisting across tabs
        context.read<AppNavigationCubit>().updateNavigationState(
              clearModals: true,
            );
      },
      child: BlocBuilder<AppNavigationCubit, AppNavigationState>(
        buildWhen: (prev, curr) => prev.selectedScreen != curr.selectedScreen,
        builder: (context, navState) {
          // Check if user has permission for this screen
          if (!AppNavigationCubit.hasTabPermission(
                context,
                navState.selectedScreen,
              ) ||
              !AppNavigationCubit.hasDetailsPanel(
                navState.selectedScreen,
              )) {
            return const SizedBox.shrink();
          }

          // Let the DetailsPage decide what to show based on the screen type
          return const Expanded(
            flex: 5,
            child: DetailsPage(),
          );
        },
      ),
    );
  }
}

// Profile avatar widget
class _ProfileAvatar extends StatelessWidget {
  const _ProfileAvatar();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return GestureDetector(
      onTap: () => _showProfileDialog(context),
      child: CircleAvatar(
        backgroundColor: colorScheme.color21,
        radius: 20,
        child: Center(
          child: BlocBuilder<AppBloc, AppState>(
            builder: (context, state) {
              final initial = state.supportUser?.sname[0].toUpperCase() ?? '?';
              return MeText(
                text: initial,
                meFontStyle: MeFontStyle.R1,
              );
            },
          ),
        ),
      ),
    );
  }

  Future<void> _showProfileDialog(BuildContext context) async {
    await showDialog<void>(
      context: context,
      barrierColor: Colors.black.withValues(alpha: 0.7),
      builder: (_) => const ProfileSettingsDialog(),
      useRootNavigator: false,
    );
  }
}
