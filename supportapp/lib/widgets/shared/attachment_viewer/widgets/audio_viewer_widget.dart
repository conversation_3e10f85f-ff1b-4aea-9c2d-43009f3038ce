import 'package:flutter/material.dart';
import 'package:just_audio/just_audio.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_icon_button.dart';

class AudioViewerWidget extends StatefulWidget {
  const AudioViewerWidget({
    super.key,
    required this.attachment,
    required this.uid,
    required this.docId,
    required this.docType,
    required this.showOverlay,
  });

  final MeAttachmentInfo attachment;
  final String uid;
  final String docId;
  final FirebaseDocCollectionType docType;
  final ValueNotifier<bool> showOverlay;

  @override
  State<AudioViewerWidget> createState() => _AudioViewerWidgetState();
}

class _AudioViewerWidgetState extends State<AudioViewerWidget> {
  late AudioPlayer _audioPlayer;
  bool _isLoading = true;
  String? _error;
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  bool _isDisposed = false;
  int _retryCount = 0;
  static const int _maxRetries = 3;

  @override
  void initState() {
    super.initState();
    _audioPlayer = AudioPlayer();
    _initializeAudio();
    _setupAudioListeners();
  }

  @override
  void dispose() {
    _isDisposed = true;
    _audioPlayer.dispose();
    super.dispose();
  }

  void _setupAudioListeners() {
    _audioPlayer.durationStream.listen((duration) {
      if (mounted && !_isDisposed && duration != null) {
        setState(() {
          _duration = duration;
        });
      }
    });

    _audioPlayer.positionStream.listen((position) {
      if (mounted && !_isDisposed) {
        setState(() {
          _position = position;
        });
      }
    });

    _audioPlayer.playerStateStream.listen((state) {
      if (mounted &&
          !_isDisposed &&
          state.processingState == ProcessingState.completed) {
        _audioPlayer.seek(Duration.zero);
        _audioPlayer.pause();
      }
    });
  }

  Future<void> _initializeAudio() async {
    if (_isDisposed) return;

    try {
      // Reset error state
      if (mounted && !_isDisposed) {
        setState(() {
          _error = null;
          _isLoading = true;
        });
      }

      // Stop any current playback
      await _audioPlayer.stop();

      if (_isDisposed) return;

      final audioUrl = widget.attachment.getAttachmentUrl(
        userId: widget.uid,
        imageType: ImageType.original,
        fileType: FileType.audio,
        isSupportMedia: true,
        docId: widget.docId,
        docType: widget.docType,
      );

      // Get fresh streaming URL (don't cache to avoid expiration issues)
      final streamingUrl = await getStorageDownloadUrl(audioUrl);

      if (_isDisposed) return; // Check if disposed after async operation

      if (streamingUrl.isEmpty) {
        throw Exception('Failed to get audio streaming URL');
      }

      await _audioPlayer.setUrl(streamingUrl);

      if (mounted && !_isDisposed) {
        setState(() {
          _isLoading = false;
          _retryCount = 0; // Reset retry count on success
        });
      }
    } catch (e) {
      if (_isDisposed) return;

      // Retry logic for network issues
      if (_retryCount < _maxRetries && !_isDisposed) {
        _retryCount++;
        await Future.delayed(Duration(seconds: _retryCount));
        if (!_isDisposed) {
          _initializeAudio();
          return;
        }
      }

      if (mounted && !_isDisposed) {
        setState(() {
          _isLoading = false;
          _error = 'Failed to load audio stream: ${e.toString()}';
        });
      }
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  Future<void> _togglePlayPause() async {
    if (_isDisposed) return;

    if (_audioPlayer.playing) {
      await _audioPlayer.pause();
    } else {
      await _audioPlayer.play();
    }
  }

  Future<void> _seekTo(Duration position) async {
    if (_isDisposed) return;

    await _audioPlayer.seek(position);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: colorScheme.color1,
        ),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              color: colorScheme.color11,
              size: 48,
            ),
            const SizedBox(height: 16),
            const MeText(
              text: 'Failed to stream audio',
              meFontStyle: MeFontStyle.B8,
            ),
            const SizedBox(height: 8),
            MeText(
              text: _error!,
              meFontStyle: MeFontStyle.F7,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: () {
                _retryCount = 0;
                _initializeAudio();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Retry'),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.color1,
                foregroundColor: colorScheme.color3,
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      color: colorScheme.color6,
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(32.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Audio icon
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: colorScheme.color1.withValues(alpha: 0.1),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.audiotrack,
                  color: colorScheme.color1,
                  size: 60,
                ),
              ),
              const SizedBox(height: 32),

              // File name or title
              const MeText(
                text: 'Audio File',
                meFontStyle: MeFontStyle.B7,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),

              // Progress bar
              Column(
                children: [
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: colorScheme.color1,
                      inactiveTrackColor: colorScheme.color7,
                      thumbColor: colorScheme.color1,
                      overlayColor: colorScheme.color1.withValues(alpha: 0.2),
                      trackHeight: 4.0,
                    ),
                    child: Slider(
                      value: _duration.inMilliseconds > 0
                          ? _position.inMilliseconds / _duration.inMilliseconds
                          : 0.0,
                      onChanged: (value) {
                        final newPosition = Duration(
                          milliseconds:
                              (value * _duration.inMilliseconds).round(),
                        );
                        _seekTo(newPosition);
                      },
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        MeText(
                          text: _formatDuration(_position),
                          meFontStyle: MeFontStyle.F7,
                        ),
                        MeText(
                          text: _formatDuration(_duration),
                          meFontStyle: MeFontStyle.F7,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Play controls
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Seek backward
                  MeIconButton(
                    icon: Icon(
                      Icons.replay_10,
                      color: colorScheme.color8,
                    ),
                    onPressed: () {
                      final newPosition =
                          _position - const Duration(seconds: 10);
                      _seekTo(
                        newPosition < Duration.zero
                            ? Duration.zero
                            : newPosition,
                      );
                    },
                  ),
                  const SizedBox(width: 24),

                  // Play/Pause button
                  StreamBuilder<PlayerState>(
                    stream: _audioPlayer.playerStateStream,
                    builder: (context, snapshot) {
                      final playerState = snapshot.data;
                      final isPlaying = playerState?.playing ?? false;
                      final processingState =
                          playerState?.processingState ?? ProcessingState.idle;

                      if (processingState == ProcessingState.loading ||
                          processingState == ProcessingState.buffering) {
                        return Container(
                          width: 64,
                          height: 64,
                          decoration: BoxDecoration(
                            color: colorScheme.color1,
                            shape: BoxShape.circle,
                          ),
                          child: Center(
                            child: SizedBox(
                              width: 24,
                              height: 24,
                              child: CircularProgressIndicator(
                                color: colorScheme.color12,
                                strokeWidth: 2,
                              ),
                            ),
                          ),
                        );
                      }

                      return GestureDetector(
                        onTap: _togglePlayPause,
                        child: Container(
                          width: 64,
                          height: 64,
                          decoration: BoxDecoration(
                            color: colorScheme.color1,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            isPlaying ? Icons.pause : Icons.play_arrow,
                            color: colorScheme.color12,
                            size: 32,
                          ),
                        ),
                      );
                    },
                  ),
                  const SizedBox(width: 24),

                  // Seek forward
                  MeIconButton(
                    icon: Icon(
                      Icons.forward_10,
                      color: colorScheme.color8,
                    ),
                    onPressed: () {
                      final newPosition =
                          _position + const Duration(seconds: 10);
                      _seekTo(
                        newPosition > _duration ? _duration : newPosition,
                      );
                    },
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
