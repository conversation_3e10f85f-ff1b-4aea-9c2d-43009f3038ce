import 'package:chewie/chewie.dart';
import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/logger.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:video_player/video_player.dart';

class VideoViewerWidget extends StatefulWidget {
  const VideoViewerWidget({
    super.key,
    required this.attachment,
    required this.uid,
    required this.docId,
    required this.docType,
    required this.showOverlay,
    required this.onTap,
  });

  final MeAttachmentInfo attachment;
  final String uid;
  final String docId;
  final FirebaseDocCollectionType docType;
  final ValueNotifier<bool> showOverlay;
  final VoidCallback onTap;

  @override
  State<VideoViewerWidget> createState() => _VideoViewerWidgetState();
}

class _VideoViewerWidgetState extends State<VideoViewerWidget> {
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  bool _isLoading = true;
  String? _error;
  bool _isDisposed = false;
  int _retryCount = 0;
  static const int _maxRetries = 3;
  late String _videoId;

  @override
  void initState() {
    super.initState();
    _videoId = '${widget.uid}_${widget.docId}_${widget.attachment.id}';
    Log.d('VideoPlayer: Initializing video player for $_videoId');
    _initializeVideo();
  }

  @override
  void dispose() {
    Log.d('VideoPlayer: Disposing video player for $_videoId');
    _isDisposed = true;
    _cleanupControllers();
    super.dispose();
  }

  void _cleanupControllers() {
    Log.d('VideoPlayer: Cleaning up controllers for $_videoId');

    _videoPlayerController?.removeListener(_videoPlayerListener);
    _chewieController?.dispose();
    _videoPlayerController?.dispose();

    _videoPlayerController = null;
    _chewieController = null;

    Log.d('VideoPlayer: Controllers cleaned up for $_videoId');
  }

  Future<void> _initializeVideo() async {
    if (_isDisposed) {
      Log.d(
        'VideoPlayer: Aborting initialization - widget disposed for $_videoId',
      );
      return;
    }

    Log.d(
      'VideoPlayer: Starting video initialization for $_videoId (attempt ${_retryCount + 1})',
    );

    try {
      // Reset state
      if (mounted && !_isDisposed) {
        setState(() {
          _error = null;
          _isLoading = true;
        });
      }

      // Clean up existing controllers
      _cleanupControllers();

      if (_isDisposed) {
        Log.d('VideoPlayer: Widget disposed during cleanup for $_videoId');
        return;
      }

      // Get video URL
      Log.d('VideoPlayer: Getting attachment URL for $_videoId');
      final videoUrl = widget.attachment.getAttachmentUrl(
        userId: widget.uid,
        imageType: ImageType.original,
        fileType: FileType.video,
        isSupportMedia: true,
        docId: widget.docId,
        docType: widget.docType,
      );

      Log.d(
        'VideoPlayer: Fetching streaming URL for $_videoId from: $videoUrl',
      );
      final streamingUrl = await getStorageDownloadUrl(videoUrl);

      if (_isDisposed) {
        Log.d('VideoPlayer: Widget disposed after URL fetch for $_videoId');
        return;
      }

      if (streamingUrl.isEmpty) {
        throw Exception('Empty streaming URL received');
      }

      Log.d(
        'VideoPlayer: Got streaming URL for $_videoId: ${streamingUrl.substring(0, 50)}...',
      );

      // Create video player controller
      _videoPlayerController = VideoPlayerController.networkUrl(
        Uri.parse(streamingUrl),
        videoPlayerOptions: VideoPlayerOptions(
          mixWithOthers: true,
          allowBackgroundPlayback: false,
        ),
      );

      if (_isDisposed) {
        Log.d(
          'VideoPlayer: Widget disposed before listener setup for $_videoId',
        );
        return;
      }

      // Add listener and initialize
      _videoPlayerController!.addListener(_videoPlayerListener);

      Log.d('VideoPlayer: Initializing video controller for $_videoId');
      await _videoPlayerController!.initialize();

      if (_isDisposed) {
        Log.d(
          'VideoPlayer: Widget disposed after initialization for $_videoId',
        );
        return;
      }

      // Set initial volume
      await _videoPlayerController!.setVolume(1.0);

      if (mounted && !_isDisposed) {
        Log.d('VideoPlayer: Video initialization successful for $_videoId');
        setState(() {
          _isLoading = false;
          _retryCount = 0;
        });

        // Setup Chewie controller
        _setupChewieController();
      }
    } catch (e) {
      Log.d('VideoPlayer: Error initializing video for $_videoId: $e');

      if (_isDisposed) {
        Log.d(
          'VideoPlayer: Widget disposed during error handling for $_videoId',
        );
        return;
      }

      // Retry logic
      if (_retryCount < _maxRetries) {
        _retryCount++;
        Log.d(
          'VideoPlayer: Retrying initialization for $_videoId (attempt $_retryCount/$_maxRetries)',
        );

        await Future.delayed(Duration(seconds: _retryCount));

        if (!_isDisposed) {
          _initializeVideo();
          return;
        }
      }

      Log.d('VideoPlayer: Max retries reached for $_videoId, showing error');
      if (mounted && !_isDisposed) {
        setState(() {
          _isLoading = false;
          _error = 'Failed to load video: ${e.toString()}';
        });
      }
    }
  }

  void _videoPlayerListener() {
    if (_isDisposed || _videoPlayerController == null) return;

    final value = _videoPlayerController!.value;

    if (value.hasError) {
      final error = value.errorDescription ?? 'Unknown video error';
      Log.d('VideoPlayer: Video player error for $_videoId: $error');

      if (mounted && !_isDisposed) {
        setState(() {
          _error = 'Video playback error: $error';
          _isLoading = false;
        });
      }
    }
  }

  void _setupChewieController() {
    if (_isDisposed ||
        _videoPlayerController == null ||
        !_videoPlayerController!.value.isInitialized ||
        _chewieController != null ||
        !mounted) {
      Log.d(
        'VideoPlayer: Skipping Chewie setup for $_videoId - conditions not met',
      );
      return;
    }

    Log.d('VideoPlayer: Setting up Chewie controller for $_videoId');

    try {
      final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: false,
        looping: false,
        allowFullScreen: true,
        allowMuting: true,
        showControls: true,
        showControlsOnInitialize: true,
        controlsSafeAreaMinimum: const EdgeInsets.all(12.0),
        allowPlaybackSpeedChanging: true,
        playbackSpeeds: const [0.5, 1.0, 1.25, 1.5, 2.0],
        hideControlsTimer: const Duration(seconds: 3),
        materialProgressColors: ChewieProgressColors(
          playedColor: colorScheme.color1,
          handleColor: colorScheme.color1,
          backgroundColor: colorScheme.color7,
          bufferedColor: colorScheme.color1.withValues(alpha: 0.5),
        ),
        placeholder: Container(
          color: colorScheme.color6,
          child: Center(
            child: CircularProgressIndicator(
              color: colorScheme.color1,
            ),
          ),
        ),
        errorBuilder: (context, errorMessage) {
          return _buildErrorWidget(errorMessage);
        },
      );

      if (mounted && !_isDisposed) {
        setState(() {});
        Log.d('VideoPlayer: Chewie controller setup complete for $_videoId');
      }
    } catch (e) {
      Log.d(
        'VideoPlayer: Error setting up Chewie controller for $_videoId: $e',
      );
      if (mounted && !_isDisposed) {
        setState(() {
          _error = 'Failed to setup video player: $e';
        });
      }
    }
  }

  Widget _buildErrorWidget(String errorMessage) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            color: colorScheme.color11,
            size: 48,
          ),
          const SizedBox(height: 16),
          const MeText(
            text: 'Failed to stream video',
            meFontStyle: MeFontStyle.B8,
          ),
          const SizedBox(height: 8),
          MeText(
            text: errorMessage,
            meFontStyle: MeFontStyle.F7,
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRetryButton() {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return ElevatedButton.icon(
      onPressed: () {
        Log.d('VideoPlayer: Retry button pressed for $_videoId');
        _retryCount = 0;
        _initializeVideo();
      },
      icon: const Icon(Icons.refresh),
      label: const Text('Retry'),
      style: ElevatedButton.styleFrom(
        backgroundColor: colorScheme.color1,
        foregroundColor: colorScheme.color3,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    // Loading state
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: colorScheme.color1,
        ),
      );
    }

    // Error state
    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildErrorWidget(_error!),
            const SizedBox(height: 16),
            _buildRetryButton(),
          ],
        ),
      );
    }

    // Video player ready
    if (_chewieController != null && _videoPlayerController != null) {
      return GestureDetector(
        onTap: widget.onTap,
        child: Center(
          child: AspectRatio(
            aspectRatio: _videoPlayerController!.value.aspectRatio,
            child: Chewie(
              controller: _chewieController!,
            ),
          ),
        ),
      );
    }

    // Fallback state
    return const Center(
      child: MeText(
        text: 'Video not available',
        meFontStyle: MeFontStyle.B8,
      ),
    );
  }
}
