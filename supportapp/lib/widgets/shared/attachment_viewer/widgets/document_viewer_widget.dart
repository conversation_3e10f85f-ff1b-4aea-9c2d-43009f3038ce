import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_icon_button.dart';
import 'package:universal_html/html.dart' as html;

class DocumentViewerWidget extends StatefulWidget {
  const DocumentViewerWidget({
    super.key,
    required this.attachment,
    required this.uid,
    required this.docId,
    required this.docType,
    required this.showOverlay,
  });

  final MeAttachmentInfo attachment;
  final String uid;
  final String docId;
  final FirebaseDocCollectionType docType;
  final ValueNotifier<bool> showOverlay;

  @override
  State<DocumentViewerWidget> createState() => _DocumentViewerWidgetState();
}

class _DocumentViewerWidgetState extends State<DocumentViewerWidget> {
  bool _isLoading = false;
  String? _content;
  String? _error;

  @override
  void initState() {
    super.initState();
    if (widget.attachment.fileType == FileType.txt) {
      _loadTextContent();
    }
  }

  Future<void> _loadTextContent() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final fileUrl = widget.attachment.getAttachmentUrl(
        userId: widget.uid,
        imageType: ImageType.original,
        fileType: FileType.txt,
        isSupportMedia: true,
        docId: widget.docId,
        docType: widget.docType,
      );

      // Get the actual download URL for Firebase Storage
      final downloadUrl = await getStorageDownloadUrl(fileUrl);

      // Fetch the text content
      final response = await http.get(Uri.parse(downloadUrl));

      if (response.statusCode == 200) {
        setState(() {
          _content = response.body;
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = 'Failed to load file: ${response.statusCode}';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Error loading file: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _downloadFile() async {
    try {
      final fileUrl = widget.attachment.getAttachmentUrl(
        userId: widget.uid,
        imageType: ImageType.original,
        fileType: widget.attachment.fileType,
        isSupportMedia: true,
        docId: widget.docId,
        docType: widget.docType,
      );

      final downloadUrl = await getStorageDownloadUrl(fileUrl);

      // Generate a proper filename
      final filename = widget.attachment.localFilePath?.split('/').last ??
          'document_${widget.attachment.id}.${widget.attachment.format}';

      // For certain file types that browsers typically open, give user choice
      final shouldForceDownload =
          _shouldForceDownload(widget.attachment.format);

      if (shouldForceDownload) {
        // Force download by creating blob and download link
        await _forceDownload(downloadUrl, filename);
      } else {
        // Open in new tab for viewable files
        html.window.open(downloadUrl, '_blank');
      }

      if (mounted) {
        final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
        final action = shouldForceDownload ? 'Downloading' : 'Opening';
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('$action $filename...'),
            backgroundColor: colorScheme.color1,
            duration: const Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to open file: $e'),
            backgroundColor: colorScheme.color11,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    }
  }

  bool _shouldForceDownload(String? format) {
    if (format == null) return true;

    // File types that should be downloaded rather than opened in browser
    const downloadFormats = {
      'doc',
      'docx',
      'xls',
      'xlsx',
      'ppt',
      'pptx',
      'zip',
      'rar',
      '7z',
      'tar',
      'gz',
      'exe',
      'msi',
      'dmg',
      'pkg',
      'csv',
      'json',
      'xml',
    };

    return downloadFormats.contains(format.toLowerCase());
  }

  Future<void> _forceDownload(String url, String filename) async {
    try {
      // Create anchor element with download attribute
      final anchorElement = html.AnchorElement(href: url);
      anchorElement.download = filename;
      anchorElement.target = '_blank';

      // Add to DOM, click, then remove
      html.document.body?.children.add(anchorElement);
      anchorElement.click();
      html.document.body?.children.remove(anchorElement);
    } catch (e) {
      // Fallback: open in new tab
      html.window.open(url, '_blank');
    }
  }

  String _getFileTypeDisplayName() {
    switch (widget.attachment.fileType) {
      case FileType.txt:
        return 'Text File';
      case FileType.document:
        return 'Document';
      default:
        return 'File';
    }
  }

  IconData _getFileIcon() {
    switch (widget.attachment.fileType) {
      case FileType.txt:
        return Icons.description;
      case FileType.document:
        return Icons.insert_drive_file;
      default:
        return Icons.attach_file;
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Container(
      color: colorScheme.color6,
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            children: [
              // Header with file info
              Container(
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: colorScheme.color5,
                  borderRadius: BorderRadius.circular(8.0),
                  border: Border.all(color: colorScheme.color7, width: 1),
                ),
                child: Row(
                  children: [
                    Icon(
                      _getFileIcon(),
                      color: colorScheme.color8,
                      size: 32,
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MeText(
                            text: _getFileTypeDisplayName(),
                            meFontStyle: MeFontStyle.B8,
                          ),
                          const SizedBox(height: 4),
                          MeText(
                            text: widget.attachment.localFilePath
                                    ?.split('/')
                                    .last ??
                                'file_${widget.attachment.id}.${widget.attachment.format}',
                            meFontStyle: MeFontStyle.F7,
                          ),
                          if (widget.attachment.size > 0) ...[
                            const SizedBox(height: 4),
                            MeText(
                              text: _formatFileSize(widget.attachment.size),
                              meFontStyle: MeFontStyle.F7,
                            ),
                          ],
                        ],
                      ),
                    ),
                    MeIconButton(
                      iconPath: Assets.svg.downloadIcon.path,
                      iconColor: colorScheme.color8,
                      onPressed: _downloadFile,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 16),

              // Content area
              Expanded(
                child: _buildContent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    if (widget.attachment.fileType == FileType.txt) {
      if (_isLoading) {
        return Center(
          child: CircularProgressIndicator(
            color: colorScheme.color1,
          ),
        );
      }

      if (_error != null) {
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                color: colorScheme.color11,
                size: 48,
              ),
              const SizedBox(height: 16),
              MeText(
                text: _error!,
                meFontStyle: MeFontStyle.B8,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _loadTextContent,
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.color1,
                  foregroundColor: colorScheme.color12,
                ),
                child: const Text('Retry'),
              ),
            ],
          ),
        );
      }

      if (_content != null) {
        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: colorScheme.color5,
            borderRadius: BorderRadius.circular(8.0),
            border: Border.all(color: colorScheme.color7, width: 1),
          ),
          child: SingleChildScrollView(
            child: SelectableText(
              _content!,
              style: TextStyle(
                color: colorScheme.color8,
                fontSize: 14,
                fontFamily: 'monospace',
                height: 1.4,
              ),
            ),
          ),
        );
      }
    }

    // For non-text files, show download option
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getFileIcon(),
            color: colorScheme.color8,
            size: 80,
          ),
          const SizedBox(height: 24),
          const MeText(
            text: 'Preview not available',
            meFontStyle: MeFontStyle.B7,
          ),
          const SizedBox(height: 8),
          const MeText(
            text: 'Click download to view this file',
            meFontStyle: MeFontStyle.F7,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _downloadFile,
            icon: const Icon(Icons.download),
            label: const Text('Download File'),
            style: ElevatedButton.styleFrom(
              backgroundColor: colorScheme.color1,
              foregroundColor: colorScheme.color12,
              padding: const EdgeInsets.symmetric(
                horizontal: 24,
                vertical: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }
}
