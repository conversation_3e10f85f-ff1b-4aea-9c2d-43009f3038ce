import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_cached_storage_image.dart';
import 'package:photo_view/photo_view.dart';

class ImageViewerWidget extends StatefulWidget {
  const ImageViewerWidget({
    super.key,
    required this.attachment,
    required this.uid,
    required this.docId,
    required this.docType,
    required this.showOverlay,
    required this.onTap,
  });

  final MeAttachmentInfo attachment;
  final String uid;
  final String docId;
  final FirebaseDocCollectionType docType;
  final ValueNotifier<bool> showOverlay;
  final VoidCallback onTap;

  @override
  State<ImageViewerWidget> createState() => _ImageViewerWidgetState();
}

class _ImageViewerWidgetState extends State<ImageViewerWidget> {
  late PhotoViewController _photoViewController;

  @override
  void initState() {
    super.initState();
    _photoViewController = PhotoViewController();
  }

  @override
  void dispose() {
    _photoViewController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return GestureDetector(
      onTap: widget.onTap,
      child: PhotoView.customChild(
        controller: _photoViewController,
        backgroundDecoration: BoxDecoration(
          color: colorScheme.color6,
        ),
        minScale: PhotoViewComputedScale.contained,
        maxScale: PhotoViewComputedScale.covered * 3.0,
        initialScale: PhotoViewComputedScale.contained,
        child: MeCachedStorageImage(
          url: widget.attachment.getAttachmentUrl(
            userId: widget.uid,
            imageType: ImageType.optimized,
            fileType: FileType.image,
            isSupportMedia: true,
            docId: widget.docId,
            docType: widget.docType,
          ),
          storageType: widget.attachment.status,
          fit: BoxFit.contain,
        ),
      ),
    );
  }
}
