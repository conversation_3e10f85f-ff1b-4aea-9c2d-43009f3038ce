import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/widgets/shared/attachment_viewer/widgets/audio_viewer_widget.dart';
import 'package:mevolvesupport/widgets/shared/attachment_viewer/widgets/document_viewer_widget.dart';
import 'package:mevolvesupport/widgets/shared/attachment_viewer/widgets/image_viewer_widget.dart';
import 'package:mevolvesupport/widgets/shared/attachment_viewer/widgets/video_viewer_widget.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_icon_button.dart';

class MeAttachmentViewer extends StatefulWidget {
  const MeAttachmentViewer({
    super.key,
    required this.attachments,
    this.currentIndex = 0,
    required this.uid,
    required this.docId,
    this.docType = FirebaseDocCollectionType.usersFeedback,
    this.title,
  });

  final List<MeAttachmentInfo> attachments;
  final int currentIndex;
  final String uid;
  final String docId;
  final FirebaseDocCollectionType docType;
  final String? title;

  @override
  State<MeAttachmentViewer> createState() => _MeAttachmentViewerState();
}

class _MeAttachmentViewerState extends State<MeAttachmentViewer> {
  late PageController _pageController;
  int _currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _currentIndex = widget.currentIndex;
    _pageController = PageController(initialPage: widget.currentIndex);
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  void _goToPrevious() {
    if (_currentIndex > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  void _goToNext() {
    if (_currentIndex < widget.attachments.length - 1) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  Widget _buildAttachmentViewer(MeAttachmentInfo attachment) {
    // Create a dummy ValueNotifier for compatibility
    final dummyOverlay = ValueNotifier<bool>(false);

    switch (attachment.fileType) {
      case FileType.image:
        return ImageViewerWidget(
          attachment: attachment,
          uid: widget.uid,
          docId: widget.docId,
          docType: widget.docType,
          showOverlay: dummyOverlay,
          onTap: () {}, // No-op for web
        );
      case FileType.video:
        return VideoViewerWidget(
          attachment: attachment,
          uid: widget.uid,
          docId: widget.docId,
          docType: widget.docType,
          showOverlay: dummyOverlay,
          onTap: () {}, // No-op for web
        );
      case FileType.audio:
        return AudioViewerWidget(
          attachment: attachment,
          uid: widget.uid,
          docId: widget.docId,
          docType: widget.docType,
          showOverlay: dummyOverlay,
        );
      case FileType.txt:
      case FileType.document:
        return DocumentViewerWidget(
          attachment: attachment,
          uid: widget.uid,
          docId: widget.docId,
          docType: widget.docType,
          showOverlay: dummyOverlay,
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Scaffold(
      backgroundColor: colorScheme.color6,
      appBar: AppBar(
        backgroundColor: colorScheme.color9,
        elevation: 0,
        automaticallyImplyLeading: false,
        titleSpacing: 0,
        toolbarHeight: 48,
        title: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Row(
            children: [
              Expanded(
                child: widget.title != null
                    ? MeText(
                        text: widget.title!,
                        meFontStyle: MeFontStyle.A12,
                        maxLines: 1,
                        textOverflow: TextOverflow.ellipsis,
                      )
                    : const MeText(
                        text: 'Attachment Viewer',
                        meFontStyle: MeFontStyle.A12,
                        maxLines: 1,
                        textOverflow: TextOverflow.ellipsis,
                      ),
              ),
              if (widget.attachments.length > 1) ...[
                const SizedBox(width: 16),
                // Previous button
                MeIconButton(
                  icon: Icon(
                    Icons.arrow_circle_left,
                    color: _currentIndex > 0
                        ? colorScheme.color12
                        : colorScheme.color7,
                  ),
                  onPressed: _currentIndex > 0 ? _goToPrevious : null,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
                const SizedBox(width: 8),
                // Page indicator
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: colorScheme.color5,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: colorScheme.color7),
                  ),
                  child: MeText(
                    text:
                        '${_currentIndex + 1} of ${widget.attachments.length}',
                    meFontStyle: MeFontStyle.F7,
                  ),
                ),
                const SizedBox(width: 8),
                // Next button
                MeIconButton(
                  icon: Icon(
                    Icons.arrow_circle_right,
                    color: _currentIndex < widget.attachments.length - 1
                        ? colorScheme.color12
                        : colorScheme.color7,
                  ),
                  onPressed: _currentIndex < widget.attachments.length - 1
                      ? _goToNext
                      : null,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                ),
                const SizedBox(width: 16),
              ],
              // Close button
              MeIconButton(
                iconPath: Assets.svg.closeIcon.path,
                onPressed: () => Navigator.of(context).pop(),
                padding: const EdgeInsets.symmetric(horizontal: 8),
              ),
            ],
          ),
        ),
      ),
      body: Container(
        color: colorScheme.color5,
        child: PageView.builder(
          controller: _pageController,
          onPageChanged: _onPageChanged,
          itemCount: widget.attachments.length,
          itemBuilder: (context, index) {
            return _buildAttachmentViewer(widget.attachments[index]);
          },
        ),
      ),
    );
  }
}
