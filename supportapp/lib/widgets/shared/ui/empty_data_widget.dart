import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/svg_color_correct.dart';

class EmptyDataWidget extends StatelessWidget {
  const EmptyDataWidget({
    super.key,
    required this.title,
    this.path,
    this.svgStr,
    this.height = 200,
    this.actionButton,
  });

  final String title;
  final String? path;
  final String? svgStr;
  final double height;
  final Widget? actionButton;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (path != null) ...[
            SvgPicture.asset(
              path!,
            ),
          ] else if (svgStr != null) ...[
            SVGColorCorrection(
              svgStr: svgStr!,
              newColor: colorScheme.color4,
              height: height,
            ),
          ],
          const SizedBox(
            height: 32,
          ),
          MeText(
            text: title,
            meFontStyle: MeFontStyle.B7,
          ),
          if (actionButton != null) ...[
            const SizedBox(
              height: 24,
            ),
            actionButton!,
          ],
        ],
      ),
    );
  }
}
