import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class MeTextInput extends StatelessWidget {
  const MeTextInput({
    super.key,
    this.hintText,
    this.maxLength,
    this.onChanged,
    this.onSubmitted,
    this.initialValue,
    this.textInputAction,
    this.enableShiftEnterNewLine = false,
    this.focusNode,
    required this.textEditingController,
  });

  final String? hintText;
  final String? initialValue;
  final int? maxLength;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final TextInputAction? textInputAction;
  final bool enableShiftEnterNewLine;
  final FocusNode? focusNode;
  final TextEditingController textEditingController;

  @override
  Widget build(BuildContext context) {
    return CallbackShortcuts(
      bindings: enableShiftEnterNewLine
          ? {
              const SingleActivator(LogicalKeyboardKey.enter, shift: true): () {
                // Insert new line at cursor position
                final controller = textEditingController;
                final selection = controller.selection;
                final text = controller.text;

                final newText = text.replaceRange(
                  selection.start,
                  selection.end,
                  '\n',
                );

                controller.value = TextEditingValue(
                  text: newText,
                  selection: TextSelection.collapsed(
                    offset: selection.start + 1,
                  ),
                );

                // Update the onChanged callback
                onChanged?.call(newText);
              },
            }
          : {},
      child: TextField(
        maxLines: null,
        maxLength: maxLength,
        controller: textEditingController,
        focusNode: focusNode,
        style: MeTextTheme.getMeFontStyle(
          fontStyle: MeFontStyle.C8,
          context: context,
        ),
        buildCounter: maxLength == null
            ? null
            : (
                context, {
                required currentLength,
                required isFocused,
                required maxLength,
              }) {
                return Column(
                  children: [
                    const SizedBox(
                      height: 20,
                    ),
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4.0),
                      child: Row(
                        children: [
                          MeText(
                            text: '$currentLength / $maxLength',
                            meFontStyle: MeFontStyle.F7,
                          ),
                        ],
                      ),
                    ),
                  ],
                );
              },
        decoration: InputDecoration(
          hintText: hintText ?? 'Type your message...',
          border: InputBorder.none,
          isDense: true,
          contentPadding: const EdgeInsets.only(
            bottom: 4,
          ),
        ),
        textCapitalization: TextCapitalization.sentences,
        keyboardType: TextInputType.multiline,
        textInputAction: textInputAction ?? TextInputAction.newline,
        onChanged: onChanged,
        onSubmitted: (value) {
          if (value.trim().isEmpty) return;
          onSubmitted!(value);

          textEditingController.text = '';
        },
      ),
    );
  }
}
