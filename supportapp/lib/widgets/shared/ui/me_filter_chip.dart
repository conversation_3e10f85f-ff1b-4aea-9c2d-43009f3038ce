import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class MeFilterChip extends StatelessWidget {
  const MeFilterChip({
    super.key,
    required this.label,
    required this.value,
    this.onTap,
  });

  final String label;
  final String value;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: const EdgeInsets.fromLTRB(12, 6, 8, 6),
          height: 30,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(24),
            shape: BoxShape.rectangle,
            color: colorScheme.color3,
            border: Border.all(
              color: colorScheme.color3,
            ),
          ),
          child: Row(
            children: [
              MeText(
                text: label,
                meFontStyle: MeFontStyle.F2,
              ),
              MeText(
                text: ' ($value)',
                meFontStyle: MeFontStyle.F7,
              ),
              if (onTap != null) ...[
                const SizedBox(width: 4),
                GestureDetector(
                  onTap: onTap,
                  child: Assets.svg.closeIconFilled.svg(
                    width: 14,
                    height: 14,
                    colorFilter: ColorFilter.mode(
                      colorScheme.color2,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
