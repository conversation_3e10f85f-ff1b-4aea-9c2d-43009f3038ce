import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class MeChip extends StatelessWidget {
  const MeChip({
    super.key,
    required this.text,
    this.onTap,
  });

  final String text;

  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.fromLTRB(
            12,
            4,
            onTap != null ? 8 : 12,
            4,
          ),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(
              24,
            ),
            shape: BoxShape.rectangle,
            border: Border.all(
              color: colorScheme.color7,
            ),
          ),
          child: Row(
            children: [
              MeText(
                text: text,
                meFontStyle: MeFontStyle.F8,
              ),
              if (onTap != null) ...[
                const SizedBox(
                  width: 4,
                ),
                GestureDetector(
                  onTap: onTap,
                  child: Padding(
                    padding: const EdgeInsets.all(
                      4,
                    ),
                    child: Assets.svg.closeIconFilled.svg(
                      width: 14,
                      height: 14,
                      colorFilter: ColorFilter.mode(
                        colorScheme.color7,
                        BlendMode.srcIn,
                      ),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }
}
