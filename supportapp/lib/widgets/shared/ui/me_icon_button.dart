import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mevolvesupport/constants/size_constants.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';

class MeIconButton extends StatelessWidget {
  const MeIconButton({
    super.key,
    required this.onPressed,
    this.iconPath,
    this.icon,
    this.buttonColor,
    this.iconColor,
    this.splashSize,
    this.iconContainerSize = const SizedBox(
      height: SizeConstants.iconButtonIconContainerSize,
      width: SizeConstants.iconButtonIconContainerSize,
    ),
    this.iconSize,
    this.padding = const EdgeInsets.all(0),
    this.isCircularIconButton = true,
  }) : assert(
          iconPath != null || icon != null,
          'iconPath or icon must not be null',
        );

  /// The callback that is called when the button is tapped.
  final VoidCallback? onPressed;

  /// The iconPath of the button
  final String? iconPath;
  final Widget? icon;

  final Color? buttonColor;

  final Color? iconColor;

  final SizedBox iconContainerSize;

  final double? splashSize;

  final EdgeInsets padding;

  final SizedBox? iconSize;

  final bool isCircularIconButton;

  BoxFit _getBoxFit() {
    return (iconSize != null &&
            iconSize!.width != null &&
            iconSize!.height != null)
        ? (iconSize!.width! > iconSize!.height!
            ? BoxFit.fitWidth
            : BoxFit.fitHeight)
        : (iconSize != null &&
                iconSize!.width != null &&
                iconSize!.height == null)
            ? BoxFit.fitWidth
            : (iconSize != null &&
                    iconSize!.width == null &&
                    iconSize!.height != null)
                ? BoxFit.fitHeight
                : BoxFit.scaleDown;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return isCircularIconButton
        ? IconButton(
            onPressed: onPressed,
            padding: padding,
            splashRadius: splashSize ??
                max(
                      iconContainerSize.height! + padding.top + padding.bottom,
                      iconContainerSize.width! + padding.left + padding.right,
                    ) /
                    2,
            constraints: BoxConstraints(
              maxHeight:
                  iconContainerSize.height! + padding.top + padding.bottom,
              maxWidth: iconContainerSize.width! + padding.left + padding.right,
              minHeight:
                  iconContainerSize.height! + padding.top + padding.bottom,
              minWidth: iconContainerSize.width! + padding.left + padding.right,
            ),
            style: IconButton.styleFrom(
              foregroundColor: buttonColor ?? colorScheme.color3,
              disabledForegroundColor: iconColor ?? colorScheme.color9,
              splashFactory: InkSparkle.splashFactory,
            ),
            icon: SizedBox(
              width: iconContainerSize.width!,
              height: iconContainerSize.height!,
              child: iconPath != null
                  ? SvgPicture.asset(
                      iconPath!,
                      colorFilter: ColorFilter.mode(
                        onPressed == null
                            ? colorScheme.color4
                            : iconColor ?? colorScheme.color12,
                        BlendMode.srcIn,
                      ),
                      fit: _getBoxFit(),
                      width: iconSize?.width,
                      height: iconSize?.height,
                    )
                  : Center(
                      child: SizedBox(
                        width: iconSize?.width,
                        height: iconSize?.height,
                        child: icon!,
                      ),
                    ),
            ),
          )
        : Material(
            color: Colors.transparent,
            child: SizedBox(
              height: iconContainerSize.height! + padding.top + padding.bottom,
              width: iconContainerSize.width! + padding.left + padding.right,
              child: InkWell(
                splashFactory: InkSparkle.splashFactory,
                onTap: onPressed,
                borderRadius: BorderRadius.circular(0),
                child: Center(
                  child: SizedBox(
                    width: iconContainerSize.width!,
                    height: iconContainerSize.height!,
                    child: iconPath != null
                        ? Center(
                            child: SvgPicture.asset(
                              iconPath!,
                              colorFilter: ColorFilter.mode(
                                onPressed == null
                                    ? colorScheme.color4
                                    : iconColor ?? colorScheme.color12,
                                BlendMode.srcIn,
                              ),
                              fit: _getBoxFit(),
                              width: iconSize?.width,
                              height: iconSize?.height,
                            ),
                          )
                        : Center(
                            child: SizedBox(
                              width: iconSize?.width,
                              height: iconSize?.height,
                              child: icon!,
                            ),
                          ),
                  ),
                ),
              ),
            ),
          );
  }
}
