import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mevolvesupport/constants/size_constants.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

enum ButtonType {
  filled,
  text,
  outlinedIcon,
}

class MeButton extends StatelessWidget {
  const MeButton({
    super.key,
    this.onPressed,
    this.title,
    this.iconPath,
    required this.color,
    required this.disabledColor,
    this.height = SizeConstants.buttonMaxHeight,
    this.minWidth = SizeConstants.buttonMinWidth,
    this.buttonRadius = SizeConstants.buttonRadius,
    required this.fontStyle,
    this.buttonType = ButtonType.text,
    this.canExpandHorizontally = false,
    this.child,
    this.textScaler,
    this.canNotDisable = false,
  }) : assert(
          title != null || child != null,
          'Either title or child must be provided',
        );

  final VoidCallback? onPressed;
  final String? title;
  final String? iconPath;
  final Widget? child;
  final Color? color;
  final Color? disabledColor;
  final double height;
  final double minWidth;
  final MeFontStyle fontStyle;
  final ButtonType buttonType;
  final bool canExpandHorizontally;
  final double buttonRadius;
  final TextScaler? textScaler;
  final bool canNotDisable;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return buttonType == ButtonType.outlinedIcon
        ? SizedBox(
            height: height,
            child: TextButton(
              onPressed: onPressed,
              style: TextButton.styleFrom(
                padding: const EdgeInsets.symmetric(horizontal: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(
                    buttonRadius,
                  ),
                  side: BorderSide(
                    color: colorScheme.color10,
                    width: 1,
                  ),
                ),
                maximumSize: Size(double.infinity, height),
                minimumSize: Size(
                  minWidth,
                  height,
                ),
                disabledBackgroundColor: disabledColor,
                alignment: Alignment.centerLeft,
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SvgPicture.asset(
                    iconPath!,
                    colorFilter: ColorFilter.mode(color!, BlendMode.srcIn),
                    width: 18,
                    height: 18,
                  ),
                  const SizedBox(width: 8),
                  canExpandHorizontally
                      ? Center(
                          child: child ??
                              MeText(
                                textScaler: textScaler,
                                text: title!,
                                meFontStyle: fontStyle,
                                disabled: onPressed == null,
                              ),
                        )
                      : child ??
                          MeText(
                            textScaler: textScaler,
                            text: title!,
                            meFontStyle: fontStyle,
                            disabled: onPressed == null,
                          ),
                ],
              ),
            ),
          )
        : buttonType == ButtonType.filled
            ? SizedBox(
                height: height,
                child: FilledButton(
                  onPressed: onPressed,
                  style: FilledButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        buttonRadius,
                      ),
                    ),
                    maximumSize: Size(double.infinity, height),
                    minimumSize: Size(
                      minWidth,
                      height,
                    ),
                    backgroundColor: color,
                    disabledBackgroundColor:
                        canNotDisable ? color : disabledColor,
                  ),
                  child: canExpandHorizontally
                      ? Center(
                          child: child ??
                              MeText(
                                textScaler: textScaler,
                                text: title!,
                                meFontStyle: fontStyle,
                                disabled:
                                    canNotDisable ? false : onPressed == null,
                              ),
                        )
                      : child ??
                          MeText(
                            textScaler: textScaler,
                            text: title!,
                            meFontStyle: fontStyle,
                            disabled: canNotDisable ? false : onPressed == null,
                          ),
                ),
              )
            : SizedBox(
                height: height,
                child: TextButton(
                  onPressed: onPressed,
                  style: TextButton.styleFrom(
                    padding: const EdgeInsets.symmetric(horizontal: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        SizeConstants.buttonRadius,
                      ),
                    ),
                    maximumSize: Size(double.infinity, height),
                    minimumSize: Size(
                      minWidth,
                      height,
                    ),
                    foregroundColor: color,
                    disabledForegroundColor: disabledColor,
                  ),
                  child: canExpandHorizontally
                      ? Center(
                          child: child ??
                              MeText(
                                textScaler: textScaler,
                                text: title!,
                                meFontStyle: fontStyle,
                                disabled: onPressed == null,
                              ),
                        )
                      : child ??
                          MeText(
                            textScaler: textScaler,
                            text: title!,
                            meFontStyle: fontStyle,
                            disabled: onPressed == null,
                          ),
                ),
              );
  }
}
