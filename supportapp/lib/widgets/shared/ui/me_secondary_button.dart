import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class MeSecondaryButton extends StatelessWidget {
  const MeSecondaryButton({
    super.key,
    this.iconPath,
    this.text,
    required this.isEnabled,
    this.padding,
    this.onTap,
  });

  final String? iconPath;
  final String? text;
  final bool isEnabled;
  final EdgeInsets? padding;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Material(
      color: colorScheme.color21,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: isEnabled ? onTap : null,
        child: Container(
          padding: padding ??
              const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 10,
              ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (iconPath != null) ...[
                SvgPicture.asset(
                  iconPath!,
                  colorFilter: ColorFilter.mode(
                    colorScheme.color5,
                    BlendMode.srcIn,
                  ),
                ),
              ],
              if (iconPath != null && text != null)
                const SizedBox(
                  width: 8,
                ),
              if (text != null) ...[
                MeText(
                  text: text!,
                  meFontStyle: isEnabled ? MeFontStyle.E1 : MeFontStyle.E7,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
