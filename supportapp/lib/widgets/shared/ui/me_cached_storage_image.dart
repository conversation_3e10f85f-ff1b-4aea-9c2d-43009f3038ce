import 'dart:developer' as developer;

import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class MeCachedStorageImage extends StatefulWidget {
  const MeCachedStorageImage({
    required this.url,
    required this.storageType,
    this.width,
    this.height,
    this.fit,
    super.key,
  });

  final String url;
  final AttachmentUploadStatus storageType;
  final double? width;
  final double? height;
  final BoxFit? fit;

  @override
  State<MeCachedStorageImage> createState() => _MeCachedStorageImageState();
}

class _MeCachedStorageImageState extends State<MeCachedStorageImage> {
  // Static cache to store download URLs across widget instances
  static final Map<String, Future<String>> _urlCache = {};
  late Future<String>? _downloadUrlFuture;

  // // Static method to clear cache when needed (for memory management)
  // static void clearCache() {
  //   _urlCache.clear();
  // }

  // // Static method to remove specific URL from cache
  // static void removeCachedUrl(String url) {
  //   _urlCache.remove(url);
  // }

  @override
  void initState() {
    super.initState();
    _initializeUrl();
  }

  void _initializeUrl() {
    if (widget.storageType == AttachmentUploadStatus.local ||
        widget.storageType == AttachmentUploadStatus.temporary) {
      _downloadUrlFuture = null;
      return;
    }

    // Use cached future if available, otherwise create new one
    if (_urlCache.containsKey(widget.url)) {
      _downloadUrlFuture = _urlCache[widget.url];
      developer.log(
        'MeCachedStorageImage: Using cached URL for ${widget.url.substring(widget.url.length - 20)}',
        name: 'ImageCache',
      );
    } else {
      developer.log(
        'MeCachedStorageImage: Creating new URL request for ${widget.url.substring(widget.url.length - 20)} (cache size: ${_urlCache.length})',
        name: 'ImageCache',
      );
      _downloadUrlFuture = getStorageDownloadUrl(widget.url);
      _urlCache[widget.url] = _downloadUrlFuture!;
    }
  }

  @override
  void didUpdateWidget(MeCachedStorageImage oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reinitialize if URL or storage type changed
    if (oldWidget.url != widget.url ||
        oldWidget.storageType != widget.storageType) {
      _initializeUrl();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.storageType == AttachmentUploadStatus.local ||
        widget.storageType == AttachmentUploadStatus.temporary) {
      return _ImageRenderer(
        isPlaceholder: true,
        key: ValueKey(widget.url),
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
      );
    }

    return FutureBuilder<String>(
      future: _downloadUrlFuture,
      builder: (_, snapshot) {
        if (snapshot.data == null) {
          return _ImageRenderer(
            isPlaceholder: true,
            key: ValueKey(widget.url),
            width: widget.width,
            height: widget.height,
            fit: widget.fit,
          );
        }
        return _ImageRenderer(
          path: snapshot.data,
          key: ValueKey(widget.url),
          width: widget.width,
          height: widget.height,
          fit: widget.fit,
        );
      },
    );
  }
}

class _ImageRenderer extends StatelessWidget {
  const _ImageRenderer({
    this.isPlaceholder = false,
    this.path,
    super.key,
    this.width,
    this.height,
    this.fit,
  });

  final bool isPlaceholder;
  final double? width;
  final double? height;
  final BoxFit? fit;
  final String? path;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    if (isPlaceholder || path == null) {
      return Image.asset(
        Assets.png.placeholder.path,
        width: width,
        height: height,
        fit: fit,
      );
    }
    return Image.network(
      path!,
      width: width,
      height: height,
      fit: fit,
      errorBuilder: (context, error, stackTrace) {
        return Container(
          width: width,
          height: height,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4),
            border: Border.all(color: colorScheme.color7, width: 0.5),
          ),
          child: Assets.svg.warningTriangle.svg(
            width: 20,
            height: 18,
            fit: BoxFit.scaleDown,
            colorFilter: ColorFilter.mode(colorScheme.color7, BlendMode.srcIn),
          ),
        );
      },
    );
  }
}
