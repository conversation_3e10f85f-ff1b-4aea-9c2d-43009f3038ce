import 'package:flutter/material.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class FilterTab extends StatelessWidget {
  const FilterTab({
    super.key,
    required this.title,
    required this.isSelected,
    this.titleUnselectedStyle = MeFontStyle.E2,
    this.titleSelectedStyle = MeFontStyle.E12,
    this.tabUnselectedColor,
    this.tabSelectedColor,
    this.borderUnselectedColor,
    this.showIcon = false,
    this.showDropdownIcon = false,
    this.count,
    this.borderRadius = 8,
    this.isCompact = false,
    this.onTap,
    this.isAlwaysSelectable = false,
  });

  final bool showIcon;
  final bool showDropdownIcon;
  final String title;
  final MeFontStyle titleUnselectedStyle;
  final MeFontStyle titleSelectedStyle;
  final Color? tabUnselectedColor;
  final Color? tabSelectedColor;
  final Color? borderUnselectedColor;
  final int? count;
  final bool isSelected;
  final bool isAlwaysSelectable;
  final double borderRadius;
  final bool isCompact;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Material(
      color: isSelected
          ? tabSelectedColor ?? colorScheme.color1
          : tabUnselectedColor ?? colorScheme.color5,
      shape: RoundedRectangleBorder(
        side: isSelected
            ? BorderSide.none
            : BorderSide(
                color: borderUnselectedColor ?? colorScheme.color30,
              ),
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(borderRadius),
        onTap: isAlwaysSelectable
            ? onTap
            : isSelected
                ? null
                : onTap,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: isCompact ? 12 : 16,
            vertical: isCompact ? 6 : 8,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (showIcon) ...[
                SizedBox(
                  width: isCompact ? 18 : 20,
                  height: isCompact ? 18 : 20,
                  child: Assets.svg.filterIcon.svg(
                    colorFilter: ColorFilter.mode(
                      isSelected ? colorScheme.color12 : colorScheme.color1,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
              ],
              MeText(
                text: title,
                meFontStyle:
                    isSelected ? titleSelectedStyle : titleUnselectedStyle,
              ),
              if (count != null) ...[
                const SizedBox(
                  width: 8,
                ),
                Container(
                  height: 18,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(4),
                    color:
                        isSelected ? colorScheme.color21 : colorScheme.color30,
                  ),
                  child: MeText(
                    text: count.toString(),
                    meFontStyle: isSelected ? MeFontStyle.F1 : MeFontStyle.F12,
                  ),
                ),
              ] else ...[
                const SizedBox(
                  height: 18,
                ),
              ],
              if (showDropdownIcon) ...[
                const SizedBox(
                  width: 4,
                ),
                SizedBox(
                  width: 18,
                  height: 18,
                  child: Assets.svg.arrowDropDown.svg(
                    colorFilter: ColorFilter.mode(
                      isSelected ? colorScheme.color12 : colorScheme.color1,
                      BlendMode.srcIn,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
