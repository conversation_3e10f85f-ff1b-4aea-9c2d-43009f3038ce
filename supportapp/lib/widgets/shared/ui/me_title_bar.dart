import 'package:flutter/material.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_icon_button.dart';

class MeTitleBar extends StatelessWidget {
  const MeTitleBar({
    super.key,
    required this.title,
    this.subtitle,
    this.onCloseClick,
    this.titleButton,
    this.statusText,
  });

  final String title;
  final String? subtitle;
  final String? statusText;
  final Function()? onCloseClick;
  final Widget? titleButton;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Container(
      width: MediaQuery.of(context).size.width,
      decoration: BoxDecoration(
        color: colorScheme.color9,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      height: 53,
      padding: const EdgeInsets.only(left: 16, right: 16),
      child: Row(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              MeText(
                text: title,
                meFontStyle: MeFontStyle.A12,
              ),
              if (subtitle != null)
                Padding(
                  padding: const EdgeInsets.only(left: 8, bottom: 2),
                  child: MeText(
                    text: subtitle ?? '',
                    meFontStyle: MeFontStyle.I12,
                  ),
                ),
            ],
          ),
          if (statusText != null)
            Container(
              margin: const EdgeInsets.only(left: 16),
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
              decoration: BoxDecoration(
                color: colorScheme.color3,
                borderRadius: BorderRadius.circular(50),
              ),
              child: MeText(text: statusText!, meFontStyle: MeFontStyle.F1),
            ),
          const Spacer(),
          if (titleButton != null) titleButton!,
          const SizedBox(
            width: 24,
          ),
          onCloseClick != null
              ? MeIconButton(
                  key: const ValueKey('close'),
                  iconPath: Assets.svg.closeIcon.path,
                  onPressed: onCloseClick,
                  buttonColor: colorScheme.color9,
                  iconColor: colorScheme.color12,
                )
              : const SizedBox.shrink(),
        ],
      ),
    );
  }
}
