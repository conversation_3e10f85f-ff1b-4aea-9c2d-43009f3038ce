import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/allusers_filter_types.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/logger.dart';

class MeFilterTextInput extends StatefulWidget {
  const MeFilterTextInput({
    super.key,
    this.hintText,
    this.maxLength,
    this.onChanged,
    this.onSubmitted,
    this.initialValue,
    this.textEditingController,
  });

  final String? hintText;
  final String? initialValue;
  final int? maxLength;
  final ValueChanged<Map<String, dynamic>>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final TextEditingController? textEditingController;

  @override
  State<MeFilterTextInput> createState() => _MeFilterTextInputState();
}

class _MeFilterTextInputState extends State<MeFilterTextInput> {
  late TextEditingController _controller;
  late final ValueNotifier<bool> _hasText;
  bool _isInternalController = false;

  @override
  void initState() {
    super.initState();

    // Track if we created the controller internally
    _isInternalController = widget.textEditingController == null;

    // Use provided controller or create new one
    _controller = widget.textEditingController ??
        TextEditingController(text: widget.initialValue);

    // Initialize hasText notifier
    _hasText = ValueNotifier(_controller.text.isNotEmpty);

    // Listen to text changes for clear button visibility
    _controller.addListener(_onTextChanged);

    Log.d('🔤 MeFilterTextInput: Initialized with text: "${_controller.text}"');
  }

  @override
  void didUpdateWidget(MeFilterTextInput oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle controller changes
    if (oldWidget.textEditingController != widget.textEditingController) {
      // Remove listener from old controller
      if (_isInternalController) {
        _controller.removeListener(_onTextChanged);
        _controller.dispose();
      }

      // Update controller reference
      _isInternalController = widget.textEditingController == null;
      _controller = widget.textEditingController ??
          TextEditingController(text: widget.initialValue);

      // Add listener to new controller
      _controller.addListener(_onTextChanged);
      _hasText.value = _controller.text.isNotEmpty;
    }

    // Update initial value if changed and using internal controller
    if (_isInternalController &&
        oldWidget.initialValue != widget.initialValue &&
        widget.initialValue != null) {
      _controller.text = widget.initialValue!;
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);

    // Only dispose if we created it
    if (_isInternalController) {
      _controller.dispose();
    }

    _hasText.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    _hasText.value = _controller.text.isNotEmpty;
  }

  void _handleTextChange(String value) {
    final trimmedValue = value.trim();

    Log.d('🔍 MeFilterTextInput: Text changed to: "$trimmedValue"');

    widget.onChanged?.call({
      AllUsersFilterType.searchEmailOrUid.name:
          trimmedValue.isEmpty ? null : trimmedValue,
    });
  }

  void _clearText() {
    Log.d('🧹 MeFilterTextInput: Clearing text');

    _controller.clear();
    widget.onChanged?.call({AllUsersFilterType.searchEmailOrUid.name: null});

    // Unfocus to hide keyboard
    FocusScope.of(context).unfocus();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return TextField(
      controller: _controller,
      maxLines: 1,
      maxLength: widget.maxLength,
      style: MeTextTheme.getMeFontStyle(
        fontStyle: MeFontStyle.E8,
        context: context,
      ),
      textAlignVertical: TextAlignVertical.center,
      decoration: InputDecoration(
        hintText: widget.hintText ?? 'Search...',
        hintStyle: MeTextTheme.getMeFontStyle(
          fontStyle: MeFontStyle.E7,
          context: context,
        ),
        isDense: true,
        filled: true,
        fillColor: colorScheme.color6,
        counterText: '',
        // Hide character counter
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: BorderSide(
            color: colorScheme.color1,
            width: 1,
          ),
        ),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 14,
        ),
        prefixIcon: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 12),
          child: Assets.svg.searchIcon.svg(
            colorFilter: ColorFilter.mode(
              colorScheme.color7,
              BlendMode.srcIn,
            ),
          ),
        ),
        prefixIconConstraints: const BoxConstraints(
          minWidth: 48,
          minHeight: 48,
        ),
        suffixIcon: ValueListenableBuilder<bool>(
          valueListenable: _hasText,
          builder: (context, hasText, child) {
            if (!hasText) return const SizedBox.shrink();

            return Material(
              type: MaterialType.transparency,
              child: InkWell(
                onTap: _clearText,
                borderRadius: BorderRadius.circular(20),
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Assets.svg.closeIconFilled.svg(
                    width: 18,
                    height: 18,
                  ),
                ),
              ),
            );
          },
        ),
        suffixIconConstraints: const BoxConstraints(
          minWidth: 42,
          minHeight: 42,
        ),
      ),
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.search,
      autocorrect: false,
      enableSuggestions: false,
      onChanged: _handleTextChange,
      onSubmitted: (value) {
        final trimmedValue = value.trim();
        if (trimmedValue.isNotEmpty) {
          Log.d('🎯 MeFilterTextInput: Submitted: "$trimmedValue"');
          widget.onSubmitted?.call(trimmedValue);
        }
      },
    );
  }
}
