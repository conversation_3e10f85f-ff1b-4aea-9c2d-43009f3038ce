import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class MeTextButton extends StatelessWidget {
  const MeTextButton({
    super.key,
    this.iconPath,
    this.isLoading = false,
    this.text,
    required this.isEnabled,
    this.padding,
    this.onTap,
    this.buttonColor,
    this.meFontStyle = MeFontStyle.L12,
  });

  final String? iconPath;
  final bool isLoading;
  final Color? buttonColor;
  final MeFontStyle meFontStyle;
  final String? text;
  final bool isEnabled;
  final EdgeInsets? padding;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Material(
      color: isEnabled ? buttonColor ?? colorScheme.color1 : colorScheme.color7,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: isEnabled ? onTap : null,
        child: Container(
          width: 87,
          alignment: Alignment.center,
          padding: padding ??
              const EdgeInsets.symmetric(
                vertical: 12,
              ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isLoading) ...[
                SizedBox(
                  height: 16,
                  width: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 3,
                    color: colorScheme.color12,
                  ),
                ),
              ] else ...[
                if (iconPath != null)
                  SvgPicture.asset(
                    iconPath!,
                    colorFilter:
                        ColorFilter.mode(colorScheme.color12, BlendMode.srcIn),
                    width: 18,
                    height: 18,
                  ),
                if (text != null)
                  MeText(
                    text: text!,
                    meFontStyle: meFontStyle,
                  ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
