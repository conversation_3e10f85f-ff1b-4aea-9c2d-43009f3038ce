import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_text_button.dart';

Future<bool?> showMeDialog(
  BuildContext context, {
  required String text,
  required String primaryText,
}) {
  return showDialog<bool>(
    useRootNavigator: false,
    context: context,
    barrierColor: Colors.black.withValues(alpha: 0.7),
    builder: (BuildContext childContext) {
      return MeDialog(
        text: text,
        primaryText: primaryText,
      );
    },
  );
}

class MeDialog extends StatelessWidget {
  const MeDialog({
    super.key,
    required this.primaryText,
    required this.text,
  });

  final String primaryText;
  final String text;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return AlertDialog(
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 24),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Container(
            width: 332,
            alignment: Alignment.center,
            child: MeText(
              text: text,
              meFontStyle: MeFontStyle.B8,
            ),
          ),
          const SizedBox(
            height: 16,
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(
                width: 87,
                child: MeTextButton(
                  onTap: () {
                    Navigator.of(context).pop(false);
                  },
                  isEnabled: true,
                  text: 'Cancel',
                  buttonColor: colorScheme.color3,
                  meFontStyle: MeFontStyle.L2,
                ),
              ),
              const SizedBox(
                width: 16,
              ),
              SizedBox(
                width: 87,
                child: MeTextButton(
                  onTap: () {
                    Navigator.of(context).pop(true);
                  },
                  isEnabled: true,
                  text: primaryText,
                  buttonColor: colorScheme.color14,
                  meFontStyle: MeFontStyle.L12,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
