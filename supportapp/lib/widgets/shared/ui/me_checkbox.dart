import 'package:flutter/material.dart';
import 'package:mevolvesupport/constants/size_constants.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_icon_button.dart';

class MeCheckBox extends StatelessWidget {
  const MeCheckBox({
    super.key,
    required this.value,
    required this.isDue,
    this.onTap,
    this.size = SizeConstants.iconButtonIconContainerSize,
    this.isCircular = true,
    this.borderWidth = 0.5,
    this.padding = const EdgeInsets.all(0),
    this.iconSize,
    this.checkboxCheckedBorderColor,
    this.checkboxUncheckedBorderColor,
    this.checkboxCheckedColor,
    this.checkboxUncheckedColor,
    this.checkboxIconColor,
  });
  final bool value;
  final bool isDue;
  final double size;
  final VoidCallback? onTap;
  final bool isCircular;
  final double borderWidth;
  final EdgeInsets padding;
  final SizedBox? iconSize;
  final Color? checkboxCheckedBorderColor;
  final Color? checkboxUncheckedBorderColor;
  final Color? checkboxCheckedColor;
  final Color? checkboxUncheckedColor;
  final Color? checkboxIconColor;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return MeIconButton(
      padding: padding,
      onPressed: onTap,
      splashSize: (size / 2) + 2,
      iconContainerSize: SizedBox(width: size, height: size),
      isCircularIconButton: isCircular,
      iconSize: iconSize,
      icon: Container(
        decoration: BoxDecoration(
          shape: isCircular ? BoxShape.circle : BoxShape.rectangle,
          borderRadius: isCircular ? null : BorderRadius.circular(2),
          border: Border.all(
            color: value
                ? checkboxCheckedBorderColor ?? colorScheme.color7
                : isDue
                    ? colorScheme.color11
                    : checkboxUncheckedBorderColor ?? colorScheme.color7,
            width: borderWidth,
          ),
          color: value
              ? checkboxCheckedColor ?? colorScheme.color7
              : checkboxUncheckedColor ?? colorScheme.color6,
        ),
        child: value ? Center(child: Assets.svg.checkmark.svg()) : null,
      ),
    );
  }
}
