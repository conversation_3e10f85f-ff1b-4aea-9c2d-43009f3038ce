import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/app_strings.dart';
import 'package:mevolvesupport/widgets/shared/ui/svg_color_correct.dart';

class NoAccessWidget extends StatelessWidget {
  const NoAccessWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SVGColorCorrection(
          svgStr: AppStrings.noAccessSvgStr,
          newColor: colorScheme.color4,
          height: 200,
        ),
        const SizedBox(
          height: 32,
        ),
        const MeText(
          text: "You don't have access for this",
          meFontStyle: MeFontStyle.B7,
        ),
      ],
    );
  }
}
