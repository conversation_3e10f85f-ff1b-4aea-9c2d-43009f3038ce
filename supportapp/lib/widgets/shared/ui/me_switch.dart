import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';

class MeSwitch extends StatelessWidget {
  const MeSwitch({
    required this.value,
    this.onChanged,
    super.key,
    this.switchColor,
  });
  final bool value;
  final ValueChanged<bool>? onChanged;
  final Color? switchColor;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return GestureDetector(
      onTap: () => onChanged?.call(!value),
      child: SizedBox(
        width: 28,
        height: 16,
        child: Stack(
          children: [
            Align(
              alignment: Alignment.center,
              child: FractionallySizedBox(
                heightFactor: 0.8,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 150),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(24.0),
                    color: value
                        ? switchColor ?? colorScheme.color1
                        : colorScheme.color7,
                  ),
                ),
              ),
            ),
            Positioned.fill(
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 150),
                alignment: value ? Alignment.centerRight : Alignment.centerLeft,
                child: AspectRatio(
                  aspectRatio: 1,
                  child: Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: value
                            ? switchColor ?? colorScheme.color1
                            : colorScheme.color7,
                      ),
                      shape: BoxShape.circle,
                      color: colorScheme.color12,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
