import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_button.dart';

void showMeSnackbar(
  BuildContext context,
  String snackbarText, [
  String? actionText,
  void Function()? actionFunction,
  Duration duration = const Duration(seconds: 4),
]) {
  ScaffoldMessenger.of(context)
    ..removeCurrentSnackBar()
    ..clearSnackBars();

  if (context.mounted) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        duration: duration,
        behavior: SnackBarBehavior.floating,
        backgroundColor: Colors.transparent,
        dismissDirection: DismissDirection.none,
        elevation: 0,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
        content: MeSnackbar(
          snackbarText: snackbarText,
          actionText: actionText,
          actionFunction: actionFunction,
        ),
        padding: EdgeInsets.zero,
        margin: EdgeInsets.fromLTRB(
          120 + (MediaQuery.of(context).size.width - 104) * 0.446,
          8,
          16,
          80,
        ),
      ),
    );
  }
}

class MeSnackbar extends StatelessWidget {
  const MeSnackbar({
    super.key,
    required this.snackbarText,
    this.actionText,
    this.actionFunction,
  });

  final String snackbarText;
  final String? actionText;
  final void Function()? actionFunction;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(4),
      ),
      color: colorScheme.color26,
      margin: const EdgeInsets.all(0),
      child: Padding(
        padding: const EdgeInsets.only(
          left: 16,
          top: 16,
          right: 4,
          bottom: 6,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            ConstrainedBox(
              constraints: BoxConstraints(
                maxHeight: MediaQuery.of(context).size.height * 0.55,
              ),
              child: SingleChildScrollView(
                child: MeText(
                  text: snackbarText,
                  meFontStyle: MeFontStyle.D12,
                  textOverflow: null,
                ),
              ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                _MeSnackBarActionButton(
                  label: 'DISMISS',
                  onPressed: () =>
                      ScaffoldMessenger.of(context).clearSnackBars(),
                ),
                if (actionText != null)
                  _MeSnackBarActionButton(
                    label: actionText!.toUpperCase(),
                    onPressed: () {
                      if (actionFunction != null) {
                        actionFunction!();
                        ScaffoldMessenger.of(context).clearSnackBars();
                      }
                    },
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class _MeSnackBarActionButton extends StatelessWidget {
  const _MeSnackBarActionButton({
    required this.label,
    required this.onPressed,
  });

  final String label;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return MeButton(
      height: 28,
      minWidth: 0,
      onPressed: onPressed,
      title: label.toUpperCase(),
      color: colorScheme.color12,
      disabledColor: colorScheme.color12.withValues(alpha: 0.5),
      fontStyle: MeFontStyle.C12,
    );
  }
}
