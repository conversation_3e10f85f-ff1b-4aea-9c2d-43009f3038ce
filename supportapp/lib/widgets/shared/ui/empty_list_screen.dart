import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class EmptyListScreen extends StatelessWidget {
  const EmptyListScreen({
    super.key,
    required this.title,
    this.iconPath,
  });

  final String title;
  final String? iconPath;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(
            iconPath ?? Assets.svg.personIcon.path,
            height: 133,
            colorFilter: ColorFilter.mode(
              colorScheme.color4,
              BlendMode.srcIn,
            ),
          ),
          const SizedBox(
            height: 32,
          ),
          MeText(
            text: title,
            meFontStyle: MeFontStyle.C7,
          ),
        ],
      ),
    );
  }
}
