import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mevolvesupport/constants/extensions.dart';

class SVGColorCorrection extends StatelessWidget {
  const SVGColorCorrection({
    super.key,
    required this.svgStr,
    required this.newColor,
    this.oldColor = const Color(0xFF3E6155),
    this.height = 24,
  });

  final String svgStr;
  final Color newColor;

  /// Defaults to the [MeAppColors.greenDarkColorScheme.color4] color
  final Color oldColor;
  final double height;

  @override
  Widget build(BuildContext context) {
    // Get Old color String value excluding the first 2 characters
    final oldColorString = oldColor.toHex();
    final newColorString = newColor.toHex();
    String replacedSvgStar = svgStr
        .replaceAll(
          oldColorString.toLowerCase(),
          newColorString,
        )
        .replaceAll(oldColorString.toUpperCase(), newColorString);
    return SvgPicture.string(
      replacedSvgStar,
      height: height,
    );
  }
}
