import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class MeListBottomBar extends StatelessWidget {
  const MeListBottomBar({
    super.key,
    required this.currentCount,
    required this.totalCount,
    required this.hasMore,
    required this.isLoading,
    required this.hasNewUpdates,
    required this.onLoadMore,
    required this.onRefresh,
    required this.itemType, // 'users' or 'chats'
  });

  final int currentCount;
  final int totalCount;
  final bool hasMore;
  final bool isLoading;
  final bool hasNewUpdates;
  final VoidCallback onLoadMore;
  final VoidCallback onRefresh;
  final String itemType;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final remainingCount = totalCount - currentCount;
    final loadCount = hasMore ? (remainingCount < 10 ? remainingCount : 10) : 0;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 14),
      decoration: BoxDecoration(
        color: colorScheme.color5,
        border: Border(
          top: BorderSide(color: colorScheme.color6, width: 1),
        ),
      ),
      child: Row(
        children: [
          // Status and count info
          Expanded(
            child: MeText(
              text: 'Showing $currentCount of $totalCount $itemType',
              meFontStyle: MeFontStyle.F8,
            ),
          ),

          // Action buttons
          Row(
            children: [
              // Refresh button
              _buildRefreshButton(colorScheme),

              if (hasMore) ...[
                const SizedBox(width: 12),
                // Load more button with text
                _buildLoadMoreButton(colorScheme, loadCount),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRefreshButton(MeColorScheme colorScheme) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onRefresh,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: hasNewUpdates
                ? colorScheme.color1.withValues(alpha: 0.1)
                : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
            border: Border.all(
              color: hasNewUpdates ? colorScheme.color1 : colorScheme.color7,
              width: 1,
            ),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.refresh,
                size: 16,
                color: hasNewUpdates ? colorScheme.color1 : colorScheme.color7,
              ),
              const SizedBox(width: 6),
              MeText(
                text: hasNewUpdates ? 'New updates' : 'Refresh',
                meFontStyle: hasNewUpdates ? MeFontStyle.F7 : MeFontStyle.F8,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadMoreButton(MeColorScheme colorScheme, int loadCount) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: isLoading ? null : onLoadMore,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isLoading ? colorScheme.color6 : colorScheme.color1,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (isLoading) ...[
                SizedBox(
                  width: 14,
                  height: 14,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      colorScheme.color1,
                    ),
                  ),
                ),
                const SizedBox(width: 6),
                const MeText(
                  text: 'Loading...',
                  meFontStyle: MeFontStyle.F7,
                ),
              ] else ...[
                Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                  color: colorScheme.color12,
                ),
                const SizedBox(width: 4),
                MeText(
                  text: 'Load $loadCount more',
                  meFontStyle: MeFontStyle.G12,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
