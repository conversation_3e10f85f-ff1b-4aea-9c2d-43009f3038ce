import 'package:flutter/material.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class MeLoadMoreButton extends StatelessWidget {
  const MeLoadMoreButton({
    super.key,
    required this.onPressed,
    this.buttonColor,
    this.count = 10,
  });

  /// The callback that is called when the button is tapped.
  /// If this is null, the button will be disabled.
  /// the disabled color is color4 of the colorTheme.
  final VoidCallback? onPressed;

  /// The color of the button
  /// If this is null, the button will use the color 6 of the theme
  final Color? buttonColor;

  final int? count;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Container(
      color: colorScheme.color6,
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Material(
            color: buttonColor ?? colorScheme.color6,
            borderRadius: BorderRadius.circular(8),
            child: InkWell(
              onTap: onPressed,
              splashFactory: InkSparkle.splashFactory,
              borderRadius: BorderRadius.circular(8),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: colorScheme.color1,
                    width: 1,
                  ),
                ),
                alignment: Alignment.center,
                child: MeText(
                  text: 'Load next ${count.toString()}',
                  meFontStyle: MeFontStyle.F1,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
