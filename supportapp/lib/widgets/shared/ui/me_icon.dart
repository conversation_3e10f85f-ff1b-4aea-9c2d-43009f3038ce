import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:mevolvesupport/utilities/utility_methods.dart';

class MeIcon extends StatelessWidget {
  const MeIcon({
    super.key,
    this.iconPath,
    this.iconString,
    this.icon,
    this.iconColor,
    this.iconSize,
    this.iconContainerSize,
    this.alignment = Alignment.center,
    this.padding,
    this.changeColorWithThemeTag = false,
    this.fit,
  });

  /// The iconPath of the button
  final String? iconPath;
  final String? iconString;
  final Widget? icon;

  final Color? iconColor;
  final SizedBox? iconSize;
  final SizedBox? iconContainerSize;
  final Alignment alignment;
  final EdgeInsets? padding;
  final bool changeColorWithThemeTag;

  /// If null, then a dynamic box fit will be chosen based on provided iconSize.
  final BoxFit? fit;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.all(0),
      child: SizedBox(
        key: const Value<PERSON>ey('Icon'),
        width: iconContainerSize?.width,
        height: iconContainerSize?.height,
        child: iconPath != null
            ? Center(
                child: changeColorWithThemeTag
                    ? _SvgWithThemeColorFromPath(
                        svgPath: iconPath!,
                        iconColor: iconColor,
                        iconSize: iconSize,
                        fit: fit,
                      )
                    : SvgPicture.asset(
                        iconPath!,
                        colorFilter: iconColor != null
                            ? ColorFilter.mode(
                                iconColor!,
                                BlendMode.srcIn,
                              )
                            : null,
                        fit: fit ??
                            getBoxFit(
                              SizedBox(
                                width: iconSize?.width,
                                height: iconSize?.height,
                              ),
                            ),
                        width: iconSize?.width,
                        height: iconSize?.height,
                      ),
              )
            : iconString != null
                ? Center(
                    child: SvgPicture.string(
                      iconString!,
                      fit: fit ??
                          getBoxFit(
                            SizedBox(
                              width: iconSize?.width,
                              height: iconSize?.height,
                            ),
                          ),
                      width: iconSize?.width,
                      height: iconSize?.height,
                    ),
                  )
                : Center(
                    child: SizedBox(
                      width: iconSize?.width,
                      height: iconSize?.height,
                      child: icon!,
                    ),
                  ),
      ),
    );
  }
}

//Change svg color with Theme
// Future<String> changeSvgPathColor(String svgPath, Color newColor) async {
//   String svgString = await rootBundle.loadString(svgPath);
//   final document = XmlDocument.parse(svgString);
//   final elements = document.findAllElements('*').where((element) {
//     return element.getAttribute('class') == 'themeColor';
//   });

//   for (var element in elements) {
//     element.setAttribute(
//       'fill',
//       '#${newColor.value.toRadixString(16).substring(2)}',
//     );
//   }

//   return document.toXmlString();
// }

class _SvgWithThemeColorFromPath extends StatefulWidget {
  const _SvgWithThemeColorFromPath({
    required this.svgPath,
    this.iconColor,
    this.iconSize,
    this.fit,
  });

  final String svgPath;
  final Color? iconColor;
  final SizedBox? iconSize;

  /// If null, then a dynamic box fit will be chosen based on provided iconSize.
  final BoxFit? fit;

  @override
  State<_SvgWithThemeColorFromPath> createState() =>
      _SvgWithThemeColorFromPathState();
}

class _SvgWithThemeColorFromPathState
    extends State<_SvgWithThemeColorFromPath> {
  late Future<String> imageLoader;

  @override
  void initState() {
    super.initState();
    // imageLoader = changeSvgPathColor(
    //   widget.svgPath,
    //   widget.iconColor ?? Theme.of(context).extension<MeColorScheme>()!.color1,
    // );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder(
      future: imageLoader,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.done &&
            snapshot.hasData) {
          return SvgPicture.string(
            snapshot.data as String,
            fit: widget.fit ??
                getBoxFit(
                  SizedBox(
                    width: widget.iconSize?.width,
                    height: widget.iconSize?.height,
                  ),
                ),
            width: widget.iconSize?.width,
            height: widget.iconSize?.height,
          );
        } else {
          return const SizedBox();
        }
      },
    );
  }
}
