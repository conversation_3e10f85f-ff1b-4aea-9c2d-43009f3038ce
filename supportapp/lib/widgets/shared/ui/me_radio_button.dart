import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';

class MeRadioButton extends StatelessWidget {
  const MeRadioButton({
    super.key,
    this.groupValue,
    required this.value,
    this.disabled = false,
    this.selectable = false,
    this.paddingValue = 4,
    this.onChanged,
  });

  final dynamic groupValue;
  final dynamic value;
  final bool selectable;
  final bool disabled;
  final double paddingValue;
  final void Function(dynamic)? onChanged;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Padding(
      padding: EdgeInsets.all(paddingValue),
      child: InkWell(
        onTap: !selectable ? null : () => onChanged?.call(value),
        borderRadius: BorderRadius.circular(30),
        child: Stack(
          alignment: Alignment.center,
          children: [
            if (value == groupValue)
              Container(
                height: 8,
                width: 8,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8),
                  color: colorScheme.color1,
                ),
              ),
            Container(
              height: 16,
              width: 16,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  width: 1.5,
                  color: disabled
                      ? colorScheme.color10
                      : value == groupValue
                          ? colorScheme.color1
                          : colorScheme.color7,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
