import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_cached_storage_image.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_icon_button.dart';
import 'package:photo_view/photo_view.dart';
import 'package:photo_view/photo_view_gallery.dart';
import 'dart:math' as math;

class ViewSupportImage extends StatefulWidget {
  const ViewSupportImage({
    super.key,
    required this.attachments,
    this.currentIndex = 0,
    this.isViewing = false,
    required this.uid,
    required this.docId,
    this.docType = FirebaseDocCollectionType.chatMessages,
  });

  final String uid;
  final bool isViewing;
  final List<MeAttachmentInfo> attachments;
  final int currentIndex;
  final String docId;
  final FirebaseDocCollectionType docType;

  @override
  State<ViewSupportImage> createState() => _ViewSupportImageState();
}

class _ViewSupportImageState extends State<ViewSupportImage> {
  late List<MeAttachmentInfo> attachments;
  late List<int> rotation;
  late PageController _controller;
  ValueNotifier<int> currentPage = ValueNotifier<int>(0);

  final double iconHeight = 18;
  final double iconWidth = 18;
  late double iconVerticalPadding = 12;
  late double iconHorizontalPadding = 16;

  @override
  void initState() {
    attachments = widget.attachments;
    rotation = List<int>.filled(attachments.length, 0);
    _controller = PageController(initialPage: widget.currentIndex);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Scaffold(
      appBar: AppBar(
        automaticallyImplyLeading: false,
        title: MeText(
          text: '${currentPage.value + 1} of ${attachments.length}',
          meFontStyle: MeFontStyle.A12,
        ),
        actions: [
          MeIconButton(
            onPressed: () {
              setState(() {
                rotation[currentPage.value] =
                    (rotation[currentPage.value] + 1) % 4;
              });
            },
            iconPath: Assets.svg.reloadIcon.path,
            padding: const EdgeInsets.symmetric(horizontal: 12),
          ),
          MeIconButton(
            onPressed: () => Navigator.pop(context),
            iconPath: Assets.svg.closeIcon.path,
            padding: const EdgeInsets.symmetric(horizontal: 12),
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: Center(
              child: attachments.isNotEmpty
                  ? Stack(
                      alignment: Alignment.center,
                      children: [
                        PhotoViewGallery.builder(
                          itemCount: attachments.length,
                          allowImplicitScrolling: true,
                          builder: (BuildContext context, int index) {
                            PhotoViewController controller =
                                PhotoViewController();
                            controller.rotation =
                                rotation[index] * -math.pi / 2;
                            final imageInfo = attachments[index];
                            return PhotoViewGalleryPageOptions.customChild(
                              child: MeCachedStorageImage(
                                url: attachments[index].getAttachmentUrl(
                                  userId: widget.uid,
                                  imageType: ImageType.optimized,
                                  fileType: FileType.image,
                                  isSupportMedia: true,
                                  docId: widget.docId,
                                  docType: widget.docType,
                                ),
                                storageType: attachments[index].status,
                                fit: BoxFit.contain,
                              ),
                              controller: controller,
                              heroAttributes: PhotoViewHeroAttributes(
                                tag: imageInfo.id!,
                              ),
                            );
                          },
                          onPageChanged: (value) {
                            setState(() {
                              currentPage.value = value;
                            });
                          },
                          pageController: _controller,
                        ),
                        ValueListenableBuilder(
                          valueListenable: currentPage,
                          builder: (context, value, child) {
                            if (attachments.length > 1) {
                              return Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  value > 0
                                      ? GestureDetector(
                                          onTap: () {
                                            _controller.animateToPage(
                                              value - 1,
                                              duration: Durations.long2,
                                              curve: Curves.easeInOutCirc,
                                            );
                                          },
                                          child: Icon(
                                            Icons.arrow_circle_left,
                                            color: colorScheme.color12,
                                            size: 50,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                  value < attachments.length - 1
                                      ? GestureDetector(
                                          onTap: () {
                                            _controller.animateToPage(
                                              value + 1,
                                              duration: Durations.long2,
                                              curve: Curves.easeInOutCirc,
                                            );
                                          },
                                          child: Icon(
                                            Icons.arrow_circle_right,
                                            color: colorScheme.color12,
                                            size: 50,
                                          ),
                                        )
                                      : const SizedBox.shrink(),
                                ],
                              );
                            } else {
                              return const SizedBox.shrink();
                            }
                          },
                        ),
                      ],
                    )
                  : const SizedBox(),
            ),
          ),
          // if (!widget.isViewing)
          //   MeMsgInput(
          //     maxLength: 640,
          //     lengthToShowCounter: 640,
          //     hintText: 'Optional message',
          //     suffixIconPath: Assets.svg.icSend.path,
          //     allowEmptyTextSubmit: true,
          //     onFieldSubmitted: (value) {
          //       if (value.isEmpty) value = '';
          //       Navigator.pop(context, value);
          //     },
          //   ),
        ],
      ),
    );
  }
}
