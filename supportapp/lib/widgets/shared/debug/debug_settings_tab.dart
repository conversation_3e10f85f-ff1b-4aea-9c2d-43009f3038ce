import 'package:flutter/material.dart';
import 'package:mevolvesupport/constants/app_config.dart';

class EnvNameOverlay extends StatelessWidget {
  const EnvNameOverlay({super.key});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: IgnorePointer(
        ignoring: true,
        child: Align(
          alignment: Alignment.topLeft,
          child: Padding(
            padding: const EdgeInsets.only(bottom: 55, left: 0),
            child: RepaintBoundary(
              child: Banner(
                message: AppConfig.instance.environmentType.name.toUpperCase(),
                location: BannerLocation.topStart,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
