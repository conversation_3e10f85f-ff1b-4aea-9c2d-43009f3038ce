import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/purchase/user_entitlements.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/enums/user_status.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class UserStatusBadge extends StatelessWidget {
  const UserStatusBadge({
    super.key,
    required this.userStatus,
    this.userDeletedStatus,
    this.wraptext = false,
    this.isSelected = false,
    this.fromAppBar = false,
    this.superSubscription,
  });

  final UserStatus userStatus;
  final UserDeletedStatus? userDeletedStatus;
  final bool wraptext;
  final bool isSelected;
  final MeUserEntitlement? superSubscription;
  final bool fromAppBar;

  bool get _showDeletedStatus {
    return userDeletedStatus != null &&
        (userStatus == UserStatus.free ||
            userStatus == UserStatus.expired ||
            userStatus == UserStatus.trial);
  }

  bool get _hasSuperSubscription {
    return superSubscription != null && superSubscription!.getSubInteger() > 0;
  }

  bool get _shouldShowStarIcon {
    return _hasSuperSubscription || userStatus == UserStatus.subscribed;
  }

  Color _getBackgroundColor(MeColorScheme colorScheme) {
    if (_showDeletedStatus) return colorScheme.color11;

    if (_hasSuperSubscription) {
      return colorScheme.color1;
    }

    switch (userStatus) {
      case UserStatus.subscribed:
        return colorScheme.color1;
      case UserStatus.expired:
        return colorScheme.color28;
      case UserStatus.trial:
        return colorScheme.color21;
      default:
        return colorScheme.color11;
    }
  }

  MeFontStyle _getFontStyle() {
    if (_showDeletedStatus) return MeFontStyle.F12;

    if (_hasSuperSubscription) {
      return MeFontStyle.F12;
    }

    switch (userStatus) {
      case UserStatus.expired:
        return MeFontStyle.F14;
      case UserStatus.subscribed:
        return MeFontStyle.F12;
      case UserStatus.trial:
        return MeFontStyle.F1;
      default:
        return MeFontStyle.F12;
    }
  }

  String _getText() {
    if (_showDeletedStatus) return userDeletedStatus!.toReadableString();

    if (_hasSuperSubscription) {
      return superSubscription!.getSuperString();
    }

    return userStatus.toReadableString();
  }

  BoxBorder? _getBorder(MeColorScheme colorScheme) {
    final shouldShowBorder = _hasSuperSubscription ||
        (isSelected && userStatus == UserStatus.subscribed);

    return shouldShowBorder ? Border.all(color: colorScheme.color12) : null;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    double? width = wraptext ? 84 : null;

    if (fromAppBar && wraptext && _showDeletedStatus) {
      width = 120;
    }

    return Container(
      height: wraptext ? (_showDeletedStatus ? 36 : 22) : null,
      width: width,
      constraints: const BoxConstraints(minWidth: 84),
      padding: EdgeInsets.symmetric(
        horizontal: wraptext ? 8 : 12,
        vertical: 4,
      ),
      alignment: Alignment.center,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(50),
        border: _getBorder(colorScheme),
        color: _getBackgroundColor(colorScheme),
      ),
      child: _shouldShowStarIcon
          ? Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: MeText(
                    textOverflow: TextOverflow.visible,
                    maxLines: 2,
                    textAlign: TextAlign.center,
                    text: _getText(),
                    meFontStyle: _getFontStyle(),
                  ),
                ),
                const SizedBox(width: 4),
                Icon(
                  Icons.star,
                  size: 12,
                  color: colorScheme.color31,
                ),
                const SizedBox(width: 4),
              ],
            )
          : MeText(
              textOverflow: TextOverflow.visible,
              maxLines: 2,
              textAlign: TextAlign.center,
              text: _getText(),
              meFontStyle: _getFontStyle(),
            ),
    );
  }
}
