import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:flutter_quill/flutter_quill.dart' as quill;
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';

class HtmlReadMore extends StatefulWidget {
  const HtmlReadMore({
    super.key,
    required this.text,
    this.ops,
    this.readMore = true,
  });

  final String text;
  final List<Map<String, dynamic>>? ops;
  final bool readMore;

  @override
  State<HtmlReadMore> createState() => _HtmlReadMoreState();
}

class _HtmlReadMoreState extends State<HtmlReadMore> {
  bool _expanded = false;
  String _htmlText = '';
  static const _readMoreHtml =
      "<span></span><span style='text-decoration: none;color:black;font-weight: normal;'> ...</span><a href='readmore' style='text-decoration:none underline;font-weight: normal;'>Read more</a>";

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    if (widget.text.contains('"insert":')) {
      _htmlText = convertToHtml(widget.text);
    } else {
      _htmlText = widget.text;
    }
    return Html(
      data: _expanded || !widget.readMore ? _htmlText : _getHtmlShortText(120),
      onAnchorTap: (url, attributes, element) {
        if (url == 'readmore') {
          setState(() {
            _expanded = true;
          });
        }
      },
      style: {
        '#': Style(
          fontSize: FontSize(14),
          color: colorScheme.color8,
          margin: Margins.zero,
        ),
        'a': Style(color: colorScheme.color1),
        'ul': Style(lineHeight: LineHeight.number(1.5)),
      },
    );
  }

  String convertToHtml(String doc) {
    final doc = quill.Document.fromJson(jsonDecode(widget.text));
    final List<Map<String, dynamic>> ops = doc.toDelta().toJson();
    final converter = QuillDeltaToHtmlConverter(
      ops,
      ConverterOptions(sanitizerOptions: OpAttributeSanitizerOptions()),
    );
    return converter.convert();
  }

  String _getHtmlShortText(int max) {
    String text = _htmlText;
    String stripped = _stripHtml(text);

    // Clip text if above max chars
    String clippedText = stripped.length > max
        ? text.substring(
            0,
            _getLastIndex(
              stripped.substring(
                0,
                max,
              ),
            ),
          )
        : checkNewLine();

    // Add 'Read more' clickable text after clipping
    String readMoreText = stripped.length > max ? _readMoreHtml : '';

    // Return final shortText
    String shortText = '$clippedText$readMoreText';
    return shortText;
  }

  String _stripHtml(String text) {
    return text.replaceAll(RegExp(r'<[^>]*>|&[^;]+;'), ' ');
  }

  String checkNewLine() {
    final regex = RegExp('((<br/>)|(</p>)|(</li>)|(</ol>))|(</ul>)');
    final match = regex.allMatches(_htmlText);
    if (match.length > 2) {
      final index = match.elementAt(2);
      final result = _htmlText.replaceRange(index.start, null, _readMoreHtml);
      return result;
    }
    return _htmlText;
  }

  int _getLastIndex(String text) {
    // Regex to find index of last character to clip at.
    int index = 0;
    List<String> split = text.split(' ');
    if (split.length >= 2) {
      final regex = RegExp(
        '(?<=(${split[split.length - 2]}))( *?|<[^>]*>|&[^;]+;)+(?=(${split[split.length - 1]}))',
      );
      index = regex.firstMatch(_htmlText)!.end + split[split.length - 1].length;
    } else {
      index = RegExp('(?=(${split[split.length - 1]}))').firstMatch(text)!.end +
          split[split.length - 1].length;
    }
    return index;
  }
}
