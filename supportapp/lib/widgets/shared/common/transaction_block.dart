import 'package:flutter/material.dart';
import 'package:flutter_helper_utils/flutter_helper_utils.dart';
import 'package:mevolvesupport/constants/extensions.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/subscription_transaction.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class TransactionBlock extends StatelessWidget {
  const TransactionBlock({
    super.key,
    required this.transaction,
  });

  final TransactionModel transaction;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Column(
      children: [
        Column(
          children: [
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              color: colorScheme.color5,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      MeText(
                        text:
                            '${(transaction.entitlement ?? '').capitalizeFirstLetter} ${(transaction.planType ?? '').capitalizeFirstLetter}',
                        meFontStyle: MeFontStyle.E8,
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      MeText(
                        text: transaction.planStartDate != null
                            ? transaction.planStartDate!.formattedDate() +
                                (transaction.planEndDate != null
                                    ? ' - ${transaction.planEndDate!.formattedDate()}'
                                    : ' - Forever')
                            : '-',
                        meFontStyle: MeFontStyle.F7,
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      MeText(
                        text: transaction.planStartDate != null
                            ? transaction.planStartDate!.isAfter(DateTime.now())
                                ? getTimeStarts(
                                    transaction.planStartDate,
                                  )
                                : transaction.planEndDate!
                                        .isBefore(DateTime.now())
                                    ? 'Expired ${getTimeAgo(
                                        transaction.planEndDate,
                                      )}'
                                    : transaction.planStartDate!
                                                .isBefore(DateTime.now()) &&
                                            transaction.planEndDate!
                                                .isAfter(DateTime.now())
                                        ? getTimeLeft(transaction.planEndDate)
                                        : '-'
                            : '-',
                        meFontStyle: MeFontStyle.F7,
                      ),
                    ],
                  ),
                  Container(
                    height: 22,
                    width: 134,
                    constraints: const BoxConstraints(minWidth: 134),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(50),
                      border: Border.all(
                        color: transaction.planEndDate != null &&
                                transaction.planEndDate!
                                    .isBefore(DateTime.now())
                            ? colorScheme.color7
                            : colorScheme.color4,
                      ),
                      color: Colors.transparent,
                    ),
                    child: MeText(
                      textOverflow: TextOverflow.visible,
                      maxLines: 2,
                      textAlign: TextAlign.center,
                      text: transaction.planStartDate != null
                          ? transaction.planStartDate!.isAfter(DateTime.now())
                              ? 'Upcoming'
                              : transaction.planEndDate!
                                      .isBefore(DateTime.now())
                                  ? 'Ended'
                                  : transaction.planStartDate!
                                              .isBefore(DateTime.now()) &&
                                          transaction.planEndDate!
                                              .isAfter(DateTime.now())
                                      ? 'Active'
                                      : ''
                          : '',
                      meFontStyle: transaction.planEndDate != null &&
                              transaction.planEndDate!.isBefore(DateTime.now())
                          ? MeFontStyle.F7
                          : MeFontStyle.F4,
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: colorScheme.color6,
            ),
          ],
        ),
      ],
    );
  }
}
