import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/cubits/app_navigation/app_navigation_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/tab_type.dart';
import 'package:mevolvesupport/providers/segment_details/segment_details_cubit.dart';
import 'package:mevolvesupport/providers/segment_details/segment_details_state.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/utilities/app_strings.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/user_details_panel.dart';
import 'package:mevolvesupport/widgets/shared/ui/empty_data_widget.dart';
import 'package:mevolvesupport/widgets/screens/segments/details_panels/segment_details_panel.dart';

/// Generic details page that displays different panels based on the selected screen
class DetailsPage extends StatelessWidget {
  const DetailsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppNavigationCubit, AppNavigationState>(
      builder: (context, navState) {
        switch (navState.selectedScreen) {
          // User-related screens
          case TabType.allUsers:
          case TabType.supportChat:
          case TabType.feedback:
          case TabType.deleteReports:
            return _buildUserDetailsPanel(context, navState);

          // Screens that have their own details
          case TabType.segments:
            return _buildSegmentDetailsPanel(context, navState);

          // Screens without details panels
          case TabType.adminPanel:
          case TabType.schema:
          case TabType.pitrRecovery:
            // These screens don't have detail panels
            return const SizedBox.shrink();
        }
      },
    );
  }

  Widget _buildUserDetailsPanel(
    BuildContext context,
    AppNavigationState navState,
  ) {
    return BlocBuilder<UserDetailsCubit, UserDetailsState>(
      builder: (context, userState) {
        if (userState.selectedUserId == null) {
          return const _EmptyDetailsView(
            message: 'Select a user from the list to view details',
          );
        }

        return UserDetailsPanel(
          userId: userState.selectedUserId!,
          initialTab: navState.selectedDetailsTab,
        );
      },
    );
  }

  Widget _buildSegmentDetailsPanel(
    BuildContext context,
    AppNavigationState navState,
  ) {
    return BlocBuilder<SegmentDetailsCubit, SegmentDetailsState>(
      builder: (context, segmentState) {
        if (segmentState.selectedSegmentId == null) {
          return const _EmptyDetailsView(
            message: 'Select a segment from the list to view details',
          );
        }

        return SegmentDetailsPanel(
          segmentId: segmentState.selectedSegmentId!,
        );
      },
    );
  }
}

// Empty details view widget
class _EmptyDetailsView extends StatelessWidget {
  const _EmptyDetailsView({required this.message});

  final String message;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Theme.of(context).extension<MeColorScheme>()!.color5,
      child: EmptyDataWidget(
        title: message,
        svgStr: AppStrings.emptyDataSvgStr,
      ),
    );
  }
}
