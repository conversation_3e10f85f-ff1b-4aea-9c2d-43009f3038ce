import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';

class DetailsTabWidget extends StatelessWidget {
  const DetailsTabWidget({
    super.key,
    this.iconPath,
    required this.isSelected,
    this.customChild,
    this.showDivider = true,
  });

  final String? iconPath;

  final bool isSelected;
  final Widget? customChild;
  final bool showDivider;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Container(
      height: 56,
      width: double.infinity,
      decoration: BoxDecoration(
        color: isSelected ? colorScheme.color1 : colorScheme.color3,
        border: showDivider
            ? Border(
                right: BorderSide(
                  color: colorScheme.color6,
                  width: 2,
                  style: BorderStyle.solid,
                ),
              )
            : null,
      ),
      child: customChild ??
          SizedBox(
            height: 24,
            width: 24,
            child: SvgPicture.asset(
              iconPath!,
              fit: BoxFit.scaleDown,
              colorFilter: ColorFilter.mode(
                isSelected ? colorScheme.color12 : colorScheme.color2,
                BlendMode.srcIn,
              ),
            ),
          ),
    );
  }
}
