import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class TabWidget extends StatelessWidget {
  const TabWidget({
    super.key,
    required this.iconPath,
    this.count,
    required this.isSelected,
    this.onTap,
  });

  final String iconPath;
  final int? count;
  final bool isSelected;
  final void Function()? onTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Material(
      color: isSelected ? colorScheme.color1 : colorScheme.color5,
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        borderRadius: BorderRadius.circular(8),
        onTap: isSelected ? null : onTap,
        child: Container(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              SizedBox(
                width: 24,
                height: 24,
                child: SvgPicture.asset(
                  iconPath,
                  colorFilter: ColorFilter.mode(
                    isSelected ? colorScheme.color12 : colorScheme.color7,
                    BlendMode.srcIn,
                  ),
                ),
              ),
              if (count != null) ...[
                MeText(
                  text: count.toString(),
                  meFontStyle: isSelected ? MeFontStyle.F12 : MeFontStyle.F7,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
