import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/components/feature_usage_tab.dart';

class FeatureUsageRow extends StatelessWidget {
  const FeatureUsageRow({
    super.key,
    required this.selectedUserData,
  });

  final UserMetaData selectedUserData;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Column(
      children: [
        Divider(
          height: 1,
          thickness: 1,
          color: colorScheme.color6,
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: Row(
                  children: [
                    const MeText(
                      text: 'Feature usage',
                      meFontStyle: MeFontStyle.D7,
                    ),
                    const SizedBox(width: 8),
                    BlocBuilder<UserDetailsCubit, UserDetailsState>(
                      buildWhen: (previous, current) =>
                          previous.isRefreshingFeatureUsage !=
                          current.isRefreshingFeatureUsage,
                      builder: (context, state) {
                        final isRefreshing = state.isRefreshingFeatureUsage;

                        return GestureDetector(
                          onTap: isRefreshing
                              ? null
                              : () {
                                  context
                                      .read<UserDetailsCubit>()
                                      .refreshFeatureUsageInfo();
                                },
                          child: isRefreshing
                              ? SizedBox(
                                  width: 16,
                                  height: 16,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      colorScheme.color8,
                                    ),
                                  ),
                                )
                              : Icon(
                                  Icons.refresh,
                                  size: 16,
                                  color: colorScheme.color8,
                                ),
                        );
                      },
                    ),
                  ],
                ),
              ),
              BlocBuilder<UserDetailsCubit, UserDetailsState>(
                buildWhen: (previous, current) =>
                    previous.featureUsageInfo != current.featureUsageInfo ||
                    previous.isRefreshingFeatureUsage !=
                        current.isRefreshingFeatureUsage,
                builder: (context, state) {
                  final featureUsageInfo = state.featureUsageInfo;

                  if (featureUsageInfo != null) {
                    return Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          FeatureUsageTab(
                            featureName: 'Todos',
                            count: featureUsageInfo.todosCount,
                            updatedAt: featureUsageInfo.todoUpdatedAt,
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          FeatureUsageTab(
                            featureName: 'Habit',
                            count: featureUsageInfo.habitsSetupsCount,
                            updatedAt: featureUsageInfo.habitSetupsUpdatedAt,
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          FeatureUsageTab(
                            featureName: 'Journal',
                            count: featureUsageInfo.journalSetupsCount,
                            updatedAt: featureUsageInfo.journalSetupsUpdatedAt,
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          FeatureUsageTab(
                            featureName: 'Note',
                            count: featureUsageInfo.notesCount,
                            updatedAt: featureUsageInfo.noteUpdatedAt,
                          ),
                          const SizedBox(
                            height: 16,
                          ),
                          FeatureUsageTab(
                            featureName: 'List',
                            count: featureUsageInfo.listsCount,
                            updatedAt: featureUsageInfo.listUpdatedAt,
                          ),
                        ],
                      ),
                    );
                  } else {
                    return const Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          FeatureUsageTab(
                            featureName: 'Todos',
                            count: 0,
                          ),
                          SizedBox(
                            height: 16,
                          ),
                          FeatureUsageTab(
                            featureName: 'Habit',
                            count: 0,
                          ),
                          SizedBox(
                            height: 16,
                          ),
                          FeatureUsageTab(
                            featureName: 'Journal',
                            count: 0,
                          ),
                          SizedBox(
                            height: 16,
                          ),
                          FeatureUsageTab(
                            featureName: 'Note',
                            count: 0,
                          ),
                          SizedBox(
                            height: 16,
                          ),
                          FeatureUsageTab(
                            featureName: 'List',
                            count: 0,
                          ),
                        ],
                      ),
                    );
                  }
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}
