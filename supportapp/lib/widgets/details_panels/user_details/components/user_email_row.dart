import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class UserEmailRow extends StatelessWidget {
  const UserEmailRow({
    super.key,
    required this.uid,
  });

  final String uid;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<UserDetailsCubit, UserDetailsState>(
      buildWhen: (previous, current) {
        return previous.isEmailLoading != current.isEmailLoading ||
            previous.showEmail != current.showEmail ||
            previous.email != current.email;
      },
      builder: (context, state) {
        final email = state.email;
        final showEmail = state.showEmail;

        return Row(
          children: [
            MeText(
              text:
                  showEmail && email != null ? email : '***********@gmail.com',
              meFontStyle: MeFontStyle.F7,
            ),
            const SizedBox(
              width: 12,
            ),
            hasPermission(
              context: context,
              permissionType: PermissionType.viewEmail,
            )
                ? GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: state.isEmailLoading
                        ? null
                        : () async {
                            await context
                                .read<UserDetailsCubit>()
                                .fetchEmailFromUid(uid: uid);
                          },
                    child: SizedBox(
                      height: 18,
                      width: 18,
                      child: state.isEmailLoading
                          ? SizedBox(
                              height: 14,
                              width: 14,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  colorScheme.color8,
                                ),
                              ),
                            )
                          : SvgPicture.asset(
                              state.showEmail
                                  ? Assets.svg.passwordEyeOpen.path
                                  : Assets.svg.passwordEye.path,
                              colorFilter: ColorFilter.mode(
                                colorScheme.color8,
                                BlendMode.srcIn,
                              ),
                            ),
                    ),
                  )
                : const SizedBox.shrink(),
          ],
        );
      },
    );
  }
}
