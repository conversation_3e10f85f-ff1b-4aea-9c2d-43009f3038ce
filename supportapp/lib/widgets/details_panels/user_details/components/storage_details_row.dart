import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class StorageDetailsRow extends StatelessWidget {
  const StorageDetailsRow({
    super.key,
    required this.selectedUserData,
  });

  final UserMetaData? selectedUserData;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final storageText = selectedUserData!.superSubscription!.getMaxStorage() >
            selectedUserData!.subscriptionInfo.entitlement.getMaxStorage()
        ? selectedUserData!.superSubscription!.getMaxStorage()
        : selectedUserData!.subscriptionInfo.entitlement.getMaxStorage();
    return Column(
      children: [
        Divider(
          height: 1,
          thickness: 1,
          color: colorScheme.color6,
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Expanded(
                child: MeText(
                  text: 'Storage details',
                  meFontStyle: MeFontStyle.D7,
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            MeText(
                              text:
                                  '${selectedUserData!.userInfo.storageUsedInGb} GB',
                              meFontStyle: MeFontStyle.D1,
                            ),
                            MeText(
                              text: '/$storageText GB',
                              meFontStyle: MeFontStyle.D7,
                            ),
                          ],
                        ),
                        MeText(
                          text: '${double.parse(
                                selectedUserData!.userInfo.storageUsedInGb,
                              ) / 10 * 100}%',
                          meFontStyle: MeFontStyle.D7,
                        ),
                      ],
                    ),
                    const SizedBox(
                      height: 8,
                    ),
                    Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(2),
                        color: colorScheme.color10,
                      ),
                      height: 4,
                      child: Row(
                        children: [
                          Expanded(
                            child: FractionallySizedBox(
                              widthFactor: double.parse(
                                    selectedUserData!.userInfo.storageUsedInGb,
                                  ) /
                                  10,
                              alignment: Alignment.topLeft,
                              child: Container(
                                height: 4,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(2),
                                  color: colorScheme.color1,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
