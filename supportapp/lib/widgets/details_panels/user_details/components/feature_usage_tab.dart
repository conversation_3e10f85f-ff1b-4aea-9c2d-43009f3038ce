import 'package:flutter/material.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

class FeatureUsageTab extends StatelessWidget {
  const FeatureUsageTab({
    super.key,
    required this.featureName,
    required this.count,
    this.updatedAt,
  });

  final String featureName;
  final int count;
  final DateTime? updatedAt;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Row(
      children: [
        SizedBox(
          width: 50,
          child: MeText(
            text: featureName,
            meFontStyle: count > 0 ? MeFontStyle.E8 : MeFontStyle.E7,
          ),
        ),
        const SizedBox(
          width: 24,
        ),
        Container(
          width: 52,
          height: 22,
          alignment: Alignment.center,
          decoration: BoxDecoration(
            color: count > 0 ? colorScheme.color9 : colorScheme.color7,
            borderRadius: BorderRadius.circular(4),
          ),
          child: MeText(
            text: count.toString(),
            meFontStyle: MeFontStyle.F12,
          ),
        ),
        if (count > 0 && updatedAt != null) ...[
          const SizedBox(
            width: 24,
          ),
          MeText(
            text: updatedAt == null
                ? ''
                : getTimeAgo(
                    updatedAt,
                  ),
            meFontStyle: MeFontStyle.F7,
          ),
        ],
      ],
    );
  }
}
