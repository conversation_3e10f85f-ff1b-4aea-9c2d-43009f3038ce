import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/constants/extensions.dart';
import 'package:mevolvesupport/cubits/user/user_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class UserDeleteReportsRow extends StatelessWidget {
  const UserDeleteReportsRow({
    super.key,
    required this.deleteReports,
    required this.userData,
  });

  final UserMetaData userData;
  final List<DeleteReport> deleteReports;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Column(
      children: [
        Divider(
          height: 1,
          thickness: 1,
          color: colorScheme.color6,
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Expanded(
                child: MeText(
                  text: 'Deleted reason',
                  meFontStyle: MeFontStyle.C7,
                ),
              ),
              Expanded(
                child: ListView.builder(
                  shrinkWrap: true,
                  itemCount: deleteReports.length,
                  itemBuilder: (context, index) {
                    return Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        if (index > 0)
                          const SizedBox(
                            height: 16,
                          ),
                        MeText(
                          text: deleteReports[index].reason.toReadableString(),
                          meFontStyle: MeFontStyle.E11,
                        ),
                        const SizedBox(
                          height: 8,
                        ),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            MeText(
                              text:
                                  'On ${deleteReports[index].deletedAt.formattedDayDateTime()}',
                              meFontStyle: MeFontStyle.E7,
                            ),
                            GestureDetector(
                              onTap: () {
                                context.read<UserCubit>().updateUserCubit(
                                      userData: userData,
                                      currentDeletedReport:
                                          deleteReports[index],
                                    );
                              },
                              child: const MeText(
                                text: 'See details',
                                meFontStyle: MeFontStyle.E1,
                              ),
                            ),
                          ],
                        ),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
