import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/cubits/user/user_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/enums/purchase/user_entitlements.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_radio_button.dart';

class SuperSubscriptionRow extends StatelessWidget {
  const SuperSubscriptionRow({
    super.key,
    required this.selectedUserData,
  });

  final UserMetaData selectedUserData;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    ValueNotifier<MeUserEntitlement> superSub =
        ValueNotifier<MeUserEntitlement>(selectedUserData.superSubscription!);
    return Column(
      children: [
        Divider(
          height: 1,
          thickness: 1,
          color: colorScheme.color6,
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Expanded(
                child: MeText(
                  text: 'Super subscription',
                  meFontStyle: MeFontStyle.D7,
                ),
              ),
              Expanded(
                child: Row(
                  children: [
                    ValueListenableBuilder(
                      valueListenable: superSub,
                      builder: (context, value, child) {
                        final hasWritePermission = hasPermission(
                          context: context,
                          permissionType: PermissionType.userDetails,
                          isWrite: true,
                        );

                        // If no write permission, show read-only view
                        if (!hasWritePermission) {
                          return Padding(
                            padding: const EdgeInsets.only(
                              right: 16.0,
                              top: 4,
                              bottom: 4,
                            ),
                            child: MeText(
                              text: superSub.value.getSuperString(),
                              meFontStyle: MeFontStyle.D8,
                            ),
                          );
                        }

                        return PopupMenuButton<MeUserEntitlement>(
                          offset: const Offset(60, 24),
                          padding: EdgeInsets.zero,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          tooltip: '',
                          color: colorScheme.color31,
                          constraints: const BoxConstraints(minWidth: 160),
                          onSelected: (value) async {
                            superSub.value = value;
                            await context
                                .read<UserCubit>()
                                .updateSuperSubscription(
                                  uid: selectedUserData.uid,
                                  subType: value,
                                );
                          },
                          itemBuilder: (BuildContext context) {
                            return List.generate(
                              MeUserEntitlement.values.length,
                              (index) => PopupMenuItem(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 2,
                                ),
                                height: 32,
                                value: MeUserEntitlement.values[index],
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    MeText(
                                      text: MeUserEntitlement.values[index]
                                          .getSuperString(),
                                      meFontStyle: MeFontStyle.D8,
                                    ),
                                    MeRadioButton(
                                      value: MeUserEntitlement.values[index],
                                      groupValue: superSub.value,
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                          child: InkWell(
                            onTap: null,
                            splashFactory: NoSplash.splashFactory,
                            child: Padding(
                              padding: const EdgeInsets.only(
                                right: 16.0,
                                top: 4,
                                bottom: 4,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  MeText(
                                    text: superSub.value.getSuperString(),
                                    meFontStyle: MeFontStyle.D8,
                                  ),
                                  const SizedBox(
                                    width: 8,
                                  ),
                                  SizedBox(
                                    width: 18,
                                    height: 18,
                                    child: Assets.svg.arrowDropDown.svg(
                                      colorFilter: ColorFilter.mode(
                                        colorScheme.color1,
                                        BlendMode.srcIn,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
