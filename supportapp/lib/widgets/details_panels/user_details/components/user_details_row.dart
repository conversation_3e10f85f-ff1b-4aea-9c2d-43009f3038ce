import 'package:flutter/material.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';

class UserDetailsRow extends StatelessWidget {
  const UserDetailsRow({
    super.key,
    required this.title,
    required this.primaryText,
    this.secondaryText,
    this.tertiaryText,
    this.secondaryTextStyle = MeFontStyle.E7,
    this.clickableText,
    this.onClickableTap,
  });

  final String title;
  final String primaryText;
  final String? secondaryText;
  final MeFontStyle? secondaryTextStyle;
  final String? tertiaryText;
  final String? clickableText;
  final void Function()? onClickableTap;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Column(
      children: [
        Divider(
          height: 1,
          thickness: 1,
          color: colorScheme.color6,
        ),
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                child: MeText(
                  text: title,
                  meFontStyle: MeFontStyle.D7,
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        MeText(
                          text: primaryText,
                          meFontStyle: MeFontStyle.E8,
                        ),
                        if (clickableText != null)
                          GestureDetector(
                            onTap: onClickableTap,
                            child: MeText(
                              text: clickableText!,
                              meFontStyle: MeFontStyle.E1,
                            ),
                          ),
                      ],
                    ),
                    if (secondaryText != null) ...[
                      const SizedBox(
                        height: 8,
                      ),
                      MeText(
                        text: secondaryText!,
                        meFontStyle: secondaryTextStyle!,
                      ),
                    ],
                    if (tertiaryText != null) ...[
                      const SizedBox(
                        height: 8,
                      ),
                      MeText(
                        text: tertiaryText!,
                        meFontStyle: MeFontStyle.E7,
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }
}
