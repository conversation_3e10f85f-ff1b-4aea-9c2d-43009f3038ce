import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/cubits/app_navigation/app_navigation_cubit.dart';
import 'package:mevolvesupport/cubits/feedbackBlock/feedback_block_cubit.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/cubits/userUpdates/user_updates_cubit.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/enums/user_details_tab_type.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/dialogs/user_json_dialog.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/tabs/subscription_details_tab.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/tabs/user_details_tab.dart';
import 'package:mevolvesupport/widgets/layout/details_tab_widget.dart';
import 'package:mevolvesupport/widgets/shared/ui/no_access_widget.dart';
import 'package:mevolvesupport/widgets/screens/all_users/activity/activity_details_tab.dart';
import 'package:mevolvesupport/widgets/screens/delete_reports/dialogs/delete_report_sheet.dart';
import 'package:mevolvesupport/widgets/screens/feedback/dialogs/feedback_sheet.dart';
import 'package:mevolvesupport/widgets/screens/feedback/feedback_details_tab.dart';
import 'package:mevolvesupport/widgets/screens/support_chat/messages_tab.dart';
import 'package:mevolvesupport/widgets/shared/components/user_status_badge.dart';

/// User details panel - displays user information with tabs
class UserDetailsPanel extends StatefulWidget {
  const UserDetailsPanel({
    super.key,
    required this.userId,
    this.initialTab = 0,
    this.hiddenTabs = const <UserDetailsTabType>{},
  });

  final String userId;
  final int initialTab;
  final Set<UserDetailsTabType> hiddenTabs;

  @override
  State<UserDetailsPanel> createState() => _UserDetailsPanelState();
}

class _UserDetailsPanelState extends State<UserDetailsPanel>
    with TickerProviderStateMixin {
  late final TabController _tabController;
  late final List<UserDetailsTabType> _visibleTabs;

  @override
  void initState() {
    super.initState();
    _visibleTabs = _getVisibleTabs();
    _tabController = TabController(
      length: _visibleTabs.length,
      vsync: this,
      initialIndex: _getValidInitialTab(),
    );
    _setupTabListener();
    _loadUserData();

    // Check if initial tab is activity tab - load analytics data if so
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final initialTabType = _visibleTabs[_getValidInitialTab()];
      if (initialTabType == UserDetailsTabType.activity) {
        context.read<UserDetailsCubit>().loadActivityDataOnTabSelect();
      }
    });
  }

  /// Get all tabs that should be visible (not hidden)
  List<UserDetailsTabType> _getVisibleTabs() {
    return UserDetailsTabType.values
        .where((tab) => !widget.hiddenTabs.contains(tab))
        .toList();
  }

  /// Get a valid initial tab index based on visible tabs
  int _getValidInitialTab() {
    if (widget.initialTab < _visibleTabs.length) {
      return widget.initialTab;
    }
    return 0; // Default to first visible tab
  }

  @override
  void didUpdateWidget(UserDetailsPanel oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Update tab if navigation state changed
    if (oldWidget.initialTab != widget.initialTab) {
      _tabController.animateTo(widget.initialTab);

      // Check if new tab is activity tab - load analytics data if so
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final newTabType = _visibleTabs[widget.initialTab];
        if (newTabType == UserDetailsTabType.activity) {
          context.read<UserDetailsCubit>().loadActivityDataOnTabSelect();
        }
      });
    }

    // Load new user if changed
    if (oldWidget.userId != widget.userId) {
      _loadUserData();
    }
  }

  void _setupTabListener() {
    _tabController.addListener(() {
      if (_tabController.indexIsChanging) {
        // Update navigation state when tab changes
        context.read<AppNavigationCubit>().updateNavigationState(
              selectedDetailsTab: _tabController.index,
            );

        // Check if user selected the activity tab - load analytics data lazily
        final selectedTabType = _visibleTabs[_tabController.index];
        if (selectedTabType == UserDetailsTabType.activity) {
          // User selected activity tab - trigger analytics data loading
          context.read<UserDetailsCubit>().loadActivityDataOnTabSelect();
        }
      }
    });
  }

  void _loadUserData() {
    // Ensure user data is loaded
    final currentUserId = context.read<UserDetailsCubit>().state.selectedUserId;
    if (currentUserId != widget.userId) {
      context.read<UserDetailsCubit>().selectUser(widget.userId);
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<AppNavigationCubit, AppNavigationState>(
      listenWhen: (previous, current) =>
          previous.selectedDetailsTab != current.selectedDetailsTab,
      listener: (context, navState) {
        // Sync tab controller with navigation state
        if (_tabController.index != navState.selectedDetailsTab) {
          _tabController.animateTo(navState.selectedDetailsTab);
        }
      },
      child: BlocBuilder<UserDetailsCubit, UserDetailsState>(
        builder: (context, state) {
          if (state.isLoading && state.userDetails == null) {
            return const Center(child: CircularProgressIndicator());
          }

          if (state.error != null && state.userDetails == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Error: ${state.error}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _loadUserData(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            );
          }

          if (state.userDetails == null) {
            return const Center(child: CircularProgressIndicator());
          }

          return Stack(
            children: [
              Scaffold(
                backgroundColor:
                    Theme.of(context).extension<MeColorScheme>()!.color5,
                appBar: _buildAppBar(context, state.userDetails!),
                body: _buildTabContent(state),
              ),
              // Modal sheets
              _ModalSheets(userDetails: state.userDetails),
            ],
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildAppBar(BuildContext context, dynamic userDetails) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return AppBar(
      backgroundColor: colorScheme.color9,
      titleSpacing: 0,
      toolbarHeight: 48,
      title: _AppBarContent(userDetails: userDetails),
      bottom: PreferredSize(
        preferredSize: const Size.fromHeight(56),
        child: _TabBar(
          tabController: _tabController,
          colorScheme: colorScheme,
          visibleTabs: _visibleTabs,
        ),
      ),
    );
  }

  Widget _buildTabContent(UserDetailsState state) {
    return TabBarView(
      controller: _tabController,
      children: _visibleTabs.map((tabType) => _getTabContent(tabType)).toList(),
    );
  }

  /// Get the content widget for a specific tab type
  Widget _getTabContent(UserDetailsTabType tabType) {
    switch (tabType) {
      case UserDetailsTabType.userDetails:
        return hasPermission(
          context: context,
          permissionType: PermissionType.userDetails,
        )
            ? const UserDetailsTab()
            : const NoAccessWidget();
      case UserDetailsTabType.messages:
        return hasPermission(
          context: context,
          permissionType: PermissionType.supportChat,
        )
            ? const MessagesTab()
            : const NoAccessWidget();
      case UserDetailsTabType.feedback:
        return hasPermission(
          context: context,
          permissionType: PermissionType.feedbacks,
        )
            ? const FeedbackDetailsTab()
            : const NoAccessWidget();
      case UserDetailsTabType.subscription:
        return hasPermission(
          context: context,
          permissionType: PermissionType.userDetails,
        )
            ? const SubscriptionDetailsTab()
            : const NoAccessWidget();
      case UserDetailsTabType.activity:
        return hasPermission(
          context: context,
          permissionType: PermissionType.userDetails,
        )
            ? const ActivityDetailsTab()
            : const NoAccessWidget();
    }
  }
}

// App bar content
class _AppBarContent extends StatelessWidget {
  const _AppBarContent({required this.userDetails});

  final dynamic userDetails;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Row(
              children: [
                Flexible(
                  child: MeText(
                    text: userDetails.userInfo.pseudoName ?? '',
                    meFontStyle: MeFontStyle.A12,
                    maxLines: 1,
                    textOverflow: TextOverflow.ellipsis,
                  ),
                ),
                const SizedBox(width: 12),
                UserStatusBadge(
                  userStatus: getUserStatus(userDetails),
                  userDeletedStatus: userDetails.userDeletedStatus,
                  superSubscription: userDetails.superSubscription,
                  wraptext: true,
                  fromAppBar: true,
                ),
              ],
            ),
          ),
          Row(
            children: [
              _JsonButton(userDetails: userDetails),
              const SizedBox(width: 16),
              GestureDetector(
                onTap: () {
                  // Clear user selection
                  context.read<UserDetailsCubit>().clearUser();
                },
                child: Assets.svg.closeIcon.svg(),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

// JSON button widget
class _JsonButton extends StatelessWidget {
  const _JsonButton({required this.userDetails});

  final dynamic userDetails;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return GestureDetector(
      onTap: () async {
        await showDialog<void>(
          context: context,
          barrierColor: Colors.black.withValues(alpha: 0.7),
          builder: (_) => UserJsonDialog(userMetaData: userDetails),
          useRootNavigator: false,
        );
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          color: colorScheme.color1,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
        child: const MeText(
          text: 'JSON',
          meFontStyle: MeFontStyle.G12,
        ),
      ),
    );
  }
}

// Tab bar widget
class _TabBar extends StatelessWidget {
  const _TabBar({
    required this.tabController,
    required this.colorScheme,
    required this.visibleTabs,
  });

  final TabController tabController;
  final MeColorScheme colorScheme;
  final List<UserDetailsTabType> visibleTabs;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.only(top: 1),
      color: colorScheme.color6,
      child: Material(
        color: colorScheme.color3,
        child: TabBar(
          dividerColor: colorScheme.color1,
          controller: tabController,
          padding: EdgeInsets.zero,
          labelPadding: EdgeInsets.zero,
          unselectedLabelColor: colorScheme.color1,
          labelColor: colorScheme.color12,
          indicatorWeight: 0.1,
          tabs: visibleTabs.asMap().entries.map((entry) {
            final index = entry.key;
            final tabType = entry.value;
            final isLastTab = index == visibleTabs.length - 1;

            return _buildTabWidget(
              tabType: tabType,
              index: index,
              isLastTab: isLastTab,
              tabController: tabController,
              colorScheme: colorScheme,
            );
          }).toList(),
        ),
      ),
    );
  }

  /// Build the appropriate tab widget for each tab type
  Widget _buildTabWidget({
    required UserDetailsTabType tabType,
    required int index,
    required bool isLastTab,
    required TabController tabController,
    required MeColorScheme colorScheme,
  }) {
    switch (tabType) {
      case UserDetailsTabType.userDetails:
        return _TabIcon(
          index: index,
          iconPath: tabType.iconPath,
          tabController: tabController,
          showDivider: !isLastTab,
        );
      case UserDetailsTabType.messages:
        return _MessagesTab(
          index: index,
          tabController: tabController,
          colorScheme: colorScheme,
        );
      case UserDetailsTabType.feedback:
        return _FeedbackTab(
          index: index,
          tabController: tabController,
          colorScheme: colorScheme,
        );
      case UserDetailsTabType.subscription:
      case UserDetailsTabType.activity:
        return _TabIcon(
          index: index,
          iconPath: tabType.iconPath,
          tabController: tabController,
          showDivider: !isLastTab,
        );
    }
  }
}

// Generic tab icon
class _TabIcon extends StatelessWidget {
  const _TabIcon({
    required this.index,
    required this.iconPath,
    required this.tabController,
    this.showDivider = true,
  });

  final int index;
  final String iconPath;
  final TabController tabController;
  final bool showDivider;

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: tabController,
      builder: (context, child) {
        return DetailsTabWidget(
          iconPath: iconPath,
          isSelected: tabController.index == index,
          showDivider: showDivider,
        );
      },
    );
  }
}

// Messages tab with notification
class _MessagesTab extends StatelessWidget {
  const _MessagesTab({
    required this.index,
    required this.tabController,
    required this.colorScheme,
  });

  final int index;
  final TabController tabController;
  final MeColorScheme colorScheme;

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: tabController,
      builder: (context, child) {
        return DetailsTabWidget(
          isSelected: tabController.index == index,
          customChild: BlocBuilder<UserUpdatesCubit, UserUpdatesCubitState>(
            builder: (context, state) {
              return Center(
                child: Stack(
                  alignment: AlignmentDirectional.topEnd,
                  children: [
                    Assets.svg.messagesIcon.svg(
                      fit: BoxFit.scaleDown,
                      colorFilter: ColorFilter.mode(
                        tabController.index == index
                            ? colorScheme.color12
                            : colorScheme.color2,
                        BlendMode.srcIn,
                      ),
                    ),
                    if (state.isChatNotReplied)
                      _NotificationDot(
                        isSelected: tabController.index == index,
                        colorScheme: colorScheme,
                      ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}

// Feedback tab
class _FeedbackTab extends StatelessWidget {
  const _FeedbackTab({
    required this.index,
    required this.tabController,
    required this.colorScheme,
  });

  final int index;
  final TabController tabController;
  final MeColorScheme colorScheme;

  @override
  Widget build(BuildContext context) {
    return ListenableBuilder(
      listenable: tabController,
      builder: (context, child) {
        return DetailsTabWidget(
          isSelected: tabController.index == index,
          customChild: Center(
            child: Assets.svg.feedbackIcon.svg(
              colorFilter: ColorFilter.mode(
                tabController.index == index
                    ? colorScheme.color12
                    : colorScheme.color2,
                BlendMode.srcIn,
              ),
            ),
          ),
        );
      },
    );
  }
}

// Notification dot widget
class _NotificationDot extends StatelessWidget {
  const _NotificationDot({
    required this.isSelected,
    required this.colorScheme,
  });

  final bool isSelected;
  final MeColorScheme colorScheme;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 8,
      height: 8,
      decoration: BoxDecoration(
        color: colorScheme.color11,
        borderRadius: BorderRadius.circular(30),
        border: Border.all(
          strokeAlign: BorderSide.strokeAlignOutside,
          color: isSelected ? colorScheme.color1 : colorScheme.color3,
        ),
      ),
    );
  }
}

// Modal sheets
class _ModalSheets extends StatelessWidget {
  const _ModalSheets({this.userDetails});

  final dynamic userDetails;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<AppNavigationCubit, AppNavigationState>(
      buildWhen: (previous, current) =>
          previous.showFeedbackSheet != current.showFeedbackSheet ||
          previous.showDeleteReportSheet != current.showDeleteReportSheet ||
          previous.selectedFeedback != current.selectedFeedback ||
          previous.selectedReport != current.selectedReport,
      builder: (context, navState) {
        if (navState.showDeleteReportSheet &&
            navState.selectedReport != null &&
            userDetails != null) {
          return DeleteReportSheet(
            deletedReport: navState.selectedReport!,
          );
        }

        if (navState.showFeedbackSheet &&
            navState.selectedFeedback != null &&
            userDetails != null) {
          return BlocProvider(
            create: (context) => FeedbackBlockCubit(
              context.read<DatabaseRepository>(),
            )..listenFeedbackBlockUpdates(id: navState.selectedFeedback!.id),
            child: FeedbackSheet(
              feedback: navState.selectedFeedback!,
            ),
          );
        }

        return const SizedBox.shrink();
      },
    );
  }
}
