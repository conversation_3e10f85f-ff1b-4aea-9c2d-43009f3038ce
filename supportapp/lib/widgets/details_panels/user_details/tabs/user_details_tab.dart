import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:mevolvesupport/constants/extensions.dart';
import 'package:mevolvesupport/enums/purchase/subscription_state.dart';
import 'package:mevolvesupport/enums/purchase/user_entitlements.dart';
import 'package:mevolvesupport/enums/user_status.dart';
import 'package:mevolvesupport/generated/assets.gen.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/components/feature_usage_row.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/components/storage_details_row.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/components/super_subscription_row.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/components/user_delete_reports_row.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/components/user_details_row.dart';
import 'package:mevolvesupport/widgets/details_panels/user_details/components/user_email_row.dart';
import 'package:mevolvesupport/widgets/shared/components/user_status_badge.dart';

class UserDetailsTab extends StatelessWidget {
  const UserDetailsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<UserDetailsCubit, UserDetailsState>(
      builder: (context, state) {
        // Show loading state
        if (state.isLoading || state.isLoadingUserDetails) {
          return const Center(child: CircularProgressIndicator());
        }

        // Show error state
        if (state.error != null) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text('Error: ${state.error}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () =>
                      context.read<UserDetailsCubit>().refreshUserData(),
                  child: const Text('Retry'),
                ),
              ],
            ),
          );
        }

        // Show empty state
        if (state.userDetails == null) {
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Assets.svg.emptyScreen.svg(),
              const SizedBox(height: 32),
              const MeText(
                text: 'Tap any user to see the full details',
                meFontStyle: MeFontStyle.B7,
              ),
            ],
          );
        }

        final selectedUserData = state.userDetails!;
        final deleteReports = state.userDeleteReports;

        return SingleChildScrollView(
          child: Column(
            children: [
              _ProfileSection(selectedUserData: selectedUserData),
              SuperSubscriptionRow(selectedUserData: selectedUserData),
              if (deleteReports.isNotEmpty)
                UserDeleteReportsRow(
                  userData: selectedUserData,
                  deleteReports: deleteReports,
                ),
              FeatureUsageRow(selectedUserData: selectedUserData),
              _DetailsSection(selectedUserData: selectedUserData),
            ],
          ),
        );
      },
    );
  }
}

// Profile section widget
class _ProfileSection extends StatelessWidget {
  const _ProfileSection({required this.selectedUserData});

  final UserMetaData selectedUserData;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Expanded(
            child: MeText(
              text: 'Profile',
              meFontStyle: MeFontStyle.D7,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                MeText(
                  text: selectedUserData.userInfo.pseudoName!,
                  meFontStyle: MeFontStyle.A8,
                ),
                const SizedBox(height: 8),
                UserEmailRow(uid: selectedUserData.uid),
                if (selectedUserData.userDeletedStatus != null) ...[
                  const SizedBox(height: 4),
                  MeText(
                    text:
                        selectedUserData.userDeletedStatus!.toReadableString(),
                    meFontStyle: MeFontStyle.F7,
                  ),
                ],
                const SizedBox(height: 8),
                Row(
                  children: [
                    UserStatusBadge(
                      userStatus: getUserStatus(selectedUserData),
                      userDeletedStatus: selectedUserData.userDeletedStatus,
                      superSubscription: selectedUserData.superSubscription,
                      wraptext: true,
                    ),
                    const SizedBox(width: 12),
                    MeText(
                      text: _getStatusTimeAgo(),
                      meFontStyle: MeFontStyle.E7,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                MeText(
                  text: 'v${selectedUserData.docVer}',
                  meFontStyle: MeFontStyle.E7,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusTimeAgo() {
    if (selectedUserData.userDeletedStatus != null) {
      return getTimeAgo(selectedUserData.userInfo.deletedAt);
    }

    if (getUserStatus(selectedUserData) == UserStatus.expired) {
      return getTimeAgo(selectedUserData.subscriptionInfo.subscriptionExpDate);
    }

    return getTimeAgo(selectedUserData.subscriptionInfo.subscriptionStartDate);
  }
}

// Details section widget
class _DetailsSection extends StatelessWidget {
  const _DetailsSection({required this.selectedUserData});

  final UserMetaData selectedUserData;

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          _buildWithUsRow(),
          _buildPlanDetailsRow(),
          _buildAutoRenewalRow(),
          // _buildDevicesRow(context),
          StorageDetailsRow(selectedUserData: selectedUserData),
          UserDetailsRow(
            title: 'Language',
            primaryText:
                selectedUserData.appSettings.language.name.toUpperCase(),
          ),
          UserDetailsRow(
            title: 'Theme',
            primaryText:
                selectedUserData.appSettings.appTheme.toReadableString(),
          ),
          UserDetailsRow(
            title: 'Color',
            primaryText: selectedUserData.appSettings.themeColor,
          ),
          UserDetailsRow(
            title: 'Pin',
            primaryText: selectedUserData.securitySettings.isPasscodeEnabled
                ? 'Enabled'
                : 'Disabled',
          ),
          UserDetailsRow(
            title: 'Biometrics',
            primaryText: selectedUserData.securitySettings.isBiometricEnabled
                ? 'Enabled'
                : 'Disabled',
          ),
          _buildNotificationTimeRow(),
        ],
      ),
    );
  }

  Widget _buildWithUsRow() {
    return UserDetailsRow(
      title: 'With us',
      primaryText: getDuration(
        fromDateTime: selectedUserData.userInfo.createdAt,
        toDateTime: selectedUserData.userInfo.deletedAt,
      ),
      secondaryText: _getDateRangeText(),
    );
  }

  Widget _buildPlanDetailsRow() {
    final hasExpDate =
        selectedUserData.subscriptionInfo.subscriptionExpDate != null;
    final isExpired = hasExpDate &&
        DateTime.now().onlyDate().isAfter(
              selectedUserData.subscriptionInfo.subscriptionExpDate!.onlyDate(),
            );

    return UserDetailsRow(
      title: 'Plan details',
      primaryText: _getPlanText(),
      secondaryText: hasExpDate ? _getExpiryText(isExpired) : '',
      secondaryTextStyle: isExpired ? MeFontStyle.E11 : MeFontStyle.E7,
      tertiaryText: hasExpDate ? _getTimeRemainingText(isExpired) : '',
    );
  }

  Widget _buildAutoRenewalRow() {
    return UserDetailsRow(
      title: 'Auto renewal',
      primaryText: selectedUserData.subscriptionInfo.subscriptionState ==
              SubscriptionState.subscribed
          ? 'Enabled'
          : 'Disabled',
    );
  }

  // Widget _buildDevicesRow(BuildContext context) {
  //   final deviceCount = selectedUserData.userInfo.devicesList.length;
  //
  //   return UserDetailsRow(
  //     title: 'No. of Device using',
  //     primaryText: '$deviceCount ',
  //     clickableText: 'See details',
  //     onClickableTap: deviceCount == 0
  //         ? null
  //         : () async {
  //             await showDialog<void>(
  //               context: context,
  //               barrierColor: Colors.black.withValues(alpha: 0.7),
  //               builder: (_) =>
  //                   DeviceDetailsDialog(userMetaData: selectedUserData),
  //               useRootNavigator: false,
  //             );
  //           },
  //   );
  // }

  Widget _buildNotificationTimeRow() {
    final notificationTime =
        selectedUserData.notificationSettings.mobileDailyAgendaNotificationTime;

    return UserDetailsRow(
      title: 'Notification time for daily agenda',
      primaryText: notificationTime != null
          ? DateFormat('hh:mm a').format(notificationTime.dateTime)
          : '08:00 AM',
    );
  }

  String _getDateRangeText() {
    final startDate =
        DateFormat('d MMM yyyy').format(selectedUserData.userInfo.createdAt);
    final endDate = selectedUserData.userInfo.deletedAt != null
        ? DateFormat('d MMM yyyy')
            .format(selectedUserData.userInfo.deletedAt!.onlyDate())
        : 'Today';
    return '$startDate - $endDate';
  }

  String _getPlanText() {
    if (selectedUserData.subscriptionInfo.entitlement ==
        MeUserEntitlement.free) {
      return selectedUserData.subscriptionInfo.entitlement.toReadableString();
    }

    return '${selectedUserData.subscriptionInfo.entitlement.toReadableString()} '
        '${selectedUserData.subscriptionInfo.subscriptionType.toReadableString()}';
  }

  String _getExpiryText(bool isExpired) {
    final prefix = isExpired ? 'Expired on' : 'Valid till';
    final date = DateFormat('d MMM yyyy').format(
      selectedUserData.subscriptionInfo.subscriptionExpDate!,
    );
    return '$prefix $date';
  }

  String _getTimeRemainingText(bool isExpired) {
    final expDate = selectedUserData.subscriptionInfo.subscriptionExpDate!;
    return isExpired ? getTimeAgo(expDate) : getTimeLeft(expDate);
  }
}
