import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/subscription_transaction.dart';
import 'package:mevolvesupport/providers/user_details/user_details_state.dart';
import 'package:mevolvesupport/providers/user_details/user_details_cubit.dart';
import 'package:mevolvesupport/utilities/app_strings.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';
import 'package:mevolvesupport/widgets/shared/common/transaction_block.dart';
import 'package:mevolvesupport/widgets/shared/ui/empty_data_widget.dart';

class SubscriptionDetailsTab extends StatelessWidget {
  const SubscriptionDetailsTab({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return BlocBuilder<UserDetailsCubit, UserDetailsState>(
      builder: (context, state) {
        // Check if user is selected
        if (state.selectedUserId == null || state.userDetails == null) {
          return EmptyDataWidget(
            title: getEmptyScreenText(2),
            svgStr: AppStrings.emptyDataSvgStr,
          );
        }

        // Show loading state while subscriptions are being loaded
        if (state.isLoadingSubscriptions) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        // Sort transactions by plan start date (newest first)
        final userTransactions =
            List<TransactionModel>.from(state.userTransactions)
              ..sort((b, a) {
                if (a.planStartDate == null) return -1;
                if (b.planStartDate == null) return 1;
                return a.planStartDate!.compareTo(b.planStartDate!);
              });

        if (userTransactions.isEmpty) {
          return const EmptyDataWidget(
            title: 'No subscription details',
            svgStr: AppStrings.emptyDataMsgSvgStr,
            height: 130,
          );
        }

        return ListView.separated(
          shrinkWrap: true,
          itemCount: userTransactions.length,
          itemBuilder: (context, index) {
            final transaction = userTransactions[index];
            return TransactionBlock(
              transaction: transaction,
            );
          },
          separatorBuilder: (BuildContext context, int index) {
            return Divider(
              color: colorScheme.color6,
              height: 1,
              thickness: 1,
            );
          },
        );
      },
    );
  }
}
