import 'package:flutter/material.dart';
import 'package:json_view/json_view.dart';
// import 'package:flutter_json_view/flutter_json_view.dart';
import 'package:mevolvesupport/cubits/user/user_settings_cubit.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/styles/me_text.dart';
import 'package:mevolvesupport/styles/themes/text_themes.dart';
import 'package:mevolvesupport/widgets/shared/ui/me_text_button.dart';

class UserJsonDialog extends StatefulWidget {
  const UserJsonDialog({
    super.key,
    required this.userMetaData,
  });

  final UserMetaData userMetaData;

  @override
  State<UserJsonDialog> createState() => _UserJsonDialogState();
}

class _UserJsonDialogState extends State<UserJsonDialog> {
  late UserMetaData userMetaData;
  bool isLoading = false;

  @override
  void initState() {
    userMetaData = widget.userMetaData;

    super.initState();
  }

  @override
  Widget build(BuildContext mainContext) {
    final colorScheme = Theme.of(mainContext).extension<MeColorScheme>()!;
    return AlertDialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 0, vertical: 100),
      contentPadding: EdgeInsets.zero,
      content: SizedBox(
        // height: 200,
        width: 800,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: MeText(
                text: 'User Metadata JSON v${userMetaData.docVer}',
                meFontStyle: MeFontStyle.A1,
              ),
            ),
            Expanded(
              child: //
                  SingleChildScrollView(
                child:
                    //    MeText(
                    //     text: userMetaData.getJsonString.toString(),
                    //     meFontStyle: MeFontStyle.C7,
                    //   ),
                    JsonView(
                  gap: 200,
                  json: userMetaData.getJsonMap,
                  shrinkWrap: true,
                  itemPadding: const EdgeInsets.all(8),
                  colorScheme: JsonColorScheme(
                    normalColor: colorScheme.color8,
                    markColor: colorScheme.color8,
                    nullColor: colorScheme.color7,
                  ),
                  styleScheme: JsonStyleScheme(
                    keysStyle: MeTextTheme.getMeFontStyle(
                      fontStyle: MeFontStyle.B8,
                      context: context,
                      height: 28,
                    ),
                    quotation: JsonQuotation.doubleQuote,
                    // set this to true to open all nodes at start
                    // use with caution, it will cause performance issue when json items is too large
                    openAtStart: false,
                    arrow: const Icon(
                      Icons.arrow_right,
                      size: 24,
                    ),
                    // too large depth will cause performance issue
                    depth: 0,
                  ),
                ),
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Divider(
              thickness: 2,
              height: 2,
              color: colorScheme.color6,
            ),
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Row(
                mainAxisSize: MainAxisSize.max,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SizedBox(
                    width: 97,
                    child: MeTextButton(
                      onTap: () => Navigator.of(context).pop(),
                      isEnabled: true,
                      text: 'Ok',
                      buttonColor: colorScheme.color1,
                      meFontStyle: MeFontStyle.D12,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
