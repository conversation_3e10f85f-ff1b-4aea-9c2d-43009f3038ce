import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/subscription_transaction.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'subscription_cubit_state.dart';

class SubscriptionCubit extends Cubit<SubscriptionCubitState> {
  SubscriptionCubit(this._databaseRepository)
      : super(const SubscriptionCubitState());

  final DatabaseRepository _databaseRepository;
  Future<void> fetchCurrentTransactions({required String uid}) async {
    final transactions = await _databaseRepository.fetchUserTransactions(uid);
    transactions.sort((b, a) {
      if (a.planStartDate == null) return -1;
      if (b.planStartDate == null) return 1;
      return a.planStartDate!.compareTo(b.planStartDate!);
    });
    emit(state.copyWith(transactions: transactions));
  }
}
