part of 'subscription_cubit.dart';

class SubscriptionCubitState extends Equatable {
  const SubscriptionCubitState({
    this.transactions = const [],
  });

  final List<TransactionModel> transactions;

  SubscriptionCubitState copyWith({
    List<TransactionModel>? transactions,
  }) {
    return SubscriptionCubitState(
      transactions: transactions ?? this.transactions,
    );
  }

  @override
  List<Object> get props => [];
}
