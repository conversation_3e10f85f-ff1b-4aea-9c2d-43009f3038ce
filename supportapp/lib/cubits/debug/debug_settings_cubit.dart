import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter/scheduler.dart' show timeDilation;
import 'package:flutter/rendering.dart';
import 'package:mevolvesupport/constants/app_config.dart';
import 'package:mevolvesupport/cubits/debug/debug_settings_state.dart';

class DebugSettingsCubit extends Cubit<DebugSettingsState> {
  DebugSettingsCubit()
      : super(
          DebugSettingsState(
            timeDilationValue: timeDilation,
            uriDebugOverlayStatus: false,
            envNameOverlay: AppConfig.instance.isTestEnv,
            debugPaintSizeEnabled: false,
            debugPaintBaselinesEnabled: false,
            debugRepaintRainbowEnabled: false,
            debugInvertOversizedImages: false,
            debugFakeIconOverflow: false,
            showAnalyticsEventSnackbar: false,
            debug29MinTimerJumpOnStart: false,
            debugSpinnerOnAppBarEnabled: kDebugMode ? true : false,
          ),
        );

  Future<void> onUpdateUriDebugOverlay(bool value) async {
    updateState(state.copyWith(uriDebugOverlayStatus: value));
  }

  Future<void> onUpdateEnvNameOverlay(bool value) async {
    updateState(state.copyWith(envNameOverlay: value));
  }

  Future<void> onUpdateDebugSpinnerOnAppBarEnabled(bool value) async {
    updateState(state.copyWith(debugSpinnerOnAppBarEnabled: value));
  }

  Future<void> onUpdateDebugPaintSizeEnabled(bool value) async {
    debugPaintSizeEnabled = value;
    updateState(state.copyWith(debugPaintSizeEnabled: value));
  }

  Future<void> onUpdateDebugPaintBaselinesEnabled(bool value) async {
    debugPaintBaselinesEnabled = value;
    updateState(state.copyWith(debugPaintBaselinesEnabled: value));
  }

  Future<void> onUpdateDebugRepaintRainbowEnabled(bool value) async {
    debugRepaintRainbowEnabled = value;
    updateState(state.copyWith(debugRepaintRainbowEnabled: value));
  }

  Future<void> onUpdateDebugInvertOversizedImages(bool value) async {
    debugInvertOversizedImages = value;
    updateState(state.copyWith(debugInvertOversizedImages: value));
  }

  Future<void> onUpdateDebugFakeIconOverflow(bool value) async {
    updateState(state.copyWith(debugFakeIconOverflow: value));
  }

  Future<void> onUpdateShowAnalyticsEventSnackbar(bool value) async {
    updateState(state.copyWith(showAnalyticsEventSnackbar: value));
  }

  Future<void> onUpdateDebug29MinTimerJumpOnStart(bool value) async {
    updateState(state.copyWith(debug29MinTimerJumpOnStart: value));
  }

  void onUpdateTimeDilationValue(double value) {
    timeDilation = value;
    updateState(state.copyWith(timeDilationStatus: value));
  }

  void updateState(DebugSettingsState newState) {
    if (isClosed) return;
    emit(newState);
  }
}
