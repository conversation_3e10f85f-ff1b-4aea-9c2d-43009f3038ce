import 'package:equatable/equatable.dart';

class DebugSettingsState extends Equatable {
  const DebugSettingsState({
    required this.timeDilationValue,
    required this.uriDebugOverlayStatus,
    required this.envNameOverlay,
    required this.debugPaintSizeEnabled,
    required this.debugPaintBaselinesEnabled,
    required this.debugRepaintRainbowEnabled,
    required this.debugInvertOversizedImages,
    required this.debugFakeIconOverflow,
    required this.showAnalyticsEventSnackbar,
    required this.debug29MinTimerJumpOnStart,
    required this.debugSpinnerOnAppBarEnabled,
  });

  final double timeDilationValue;
  final bool uriDebugOverlayStatus;
  final bool envNameOverlay;
  final bool debugPaintSizeEnabled;
  final bool debugPaintBaselinesEnabled;
  final bool debugRepaintRainbowEnabled;
  final bool debugInvertOversizedImages;
  final bool debugFakeIconOverflow;
  final bool showAnalyticsEventSnackbar;
  final bool debug29MinTimerJumpOnStart;
  final bool debugSpinnerOnAppBarEnabled;

  DebugSettingsState copyWith({
    double? timeDilationStatus,
    bool? uriDebugOverlayStatus,
    bool? envNameOverlay,
    bool? debugPaintSizeEnabled,
    bool? debugPaintBaselinesEnabled,
    bool? debugRepaintRainbowEnabled,
    bool? debugInvertOversizedImages,
    bool? debugFakeIconOverflow,
    bool? showAnalyticsEventSnackbar,
    bool? debug29MinTimerJumpOnStart,
    bool? debugSpinnerOnAppBarEnabled,
  }) {
    return DebugSettingsState(
      timeDilationValue: timeDilationStatus ?? timeDilationValue,
      uriDebugOverlayStatus:
          uriDebugOverlayStatus ?? this.uriDebugOverlayStatus,
      envNameOverlay: envNameOverlay ?? this.envNameOverlay,
      debugPaintSizeEnabled:
          debugPaintSizeEnabled ?? this.debugPaintSizeEnabled,
      debugPaintBaselinesEnabled:
          debugPaintBaselinesEnabled ?? this.debugPaintBaselinesEnabled,
      debugRepaintRainbowEnabled:
          debugRepaintRainbowEnabled ?? this.debugRepaintRainbowEnabled,
      debugInvertOversizedImages:
          debugInvertOversizedImages ?? this.debugInvertOversizedImages,
      debugFakeIconOverflow:
          debugFakeIconOverflow ?? this.debugFakeIconOverflow,
      showAnalyticsEventSnackbar:
          showAnalyticsEventSnackbar ?? this.showAnalyticsEventSnackbar,
      debug29MinTimerJumpOnStart:
          debug29MinTimerJumpOnStart ?? this.debug29MinTimerJumpOnStart,
      debugSpinnerOnAppBarEnabled:
          debugSpinnerOnAppBarEnabled ?? this.debugSpinnerOnAppBarEnabled,
    );
  }

  @override
  List<Object?> get props => [
        timeDilationValue,
        uriDebugOverlayStatus,
        envNameOverlay,
        debugPaintSizeEnabled,
        debugPaintBaselinesEnabled,
        debugRepaintRainbowEnabled,
        debugInvertOversizedImages,
        debugFakeIconOverflow,
        showAnalyticsEventSnackbar,
        debug29MinTimerJumpOnStart,
        debugSpinnerOnAppBarEnabled,
      ];

  @override
  String toString() {
    return 'DebugSettingsState{timeDilationValue: $timeDilationValue, uriDebugOverlayStatus: $uriDebugOverlayStatus, envNameOverlay: $envNameOverlay, debugPaintSizeEnabled: $debugPaintSizeEnabled, debugPaintBaselinesEnabled: $debugPaintBaselinesEnabled, debugRepaintRainbowEnabled: $debugRepaintRainbowEnabled, debugInvertOversizedImages: $debugInvertOversizedImages, debugFakeIconOverflow: $debugFakeIconOverflow, showAnalyticsEventSnackbar: $showAnalyticsEventSnackbar, debug20MinTimerJumpOnStart: $debug29MinTimerJumpOnStart, debugSpinnerOnAppBarEnabled: $debugSpinnerOnAppBarEnabled}';
  }

  @override
  bool get stringify => true;
}
