import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';

part 'nuggets_metadata_state.dart';

class NuggetsMetadataCubit extends Cubit<NuggetsMetadataCubitState> {
  NuggetsMetadataCubit()
      : super(const NuggetsMetadataCubitState(currentNuggetId: null));

  Future<void> updateNuggetsCubit({String? currentNuggetId}) async {
    emit(state.copyWith(currentNuggetId: currentNuggetId));
  }
}
