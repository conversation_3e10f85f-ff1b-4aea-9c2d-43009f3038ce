part of 'nuggets_metadata_cubit.dart';

class NuggetsMetadataCubitState extends Equatable {
  const NuggetsMetadataCubitState({required this.currentNuggetId});

  final String? currentNuggetId;

  @override
  List<Object?> get props => [currentNuggetId];

  NuggetsMetadataCubitState copyWith({String? currentNuggetId}) {
    return NuggetsMetadataCubitState(
      currentNuggetId: currentNuggetId,
    );
  }
}
