import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'chat_block_state.dart';

class ChatBlockCubit extends Cubit<ChatBlockState> {
  ChatBlockCubit(this._databaseRepository) : super(const ChatBlockState());

  final DatabaseRepository _databaseRepository;
  StreamSubscription<ChatUser?>? _listenChatBlock;

  Future<void> listenChatBlock({
    required String uid,
    required ChatUser chatUser,
  }) async {
    emit(state.copyWith(chatUser: chatUser));
    await _listenChatBlock?.cancel();
    _listenChatBlock =
        _databaseRepository.listenChatBlock(uid: uid).listen((chatUser) {
      if (chatUser != null) {
        emit(state.copyWith(chatUser: chatUser));
      }
    });
  }

  @override
  Future<void> close() async {
    await _listenChatBlock?.cancel();
    return super.close();
  }
}
