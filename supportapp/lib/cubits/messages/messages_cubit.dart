import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:mevolvesupport/enums/app_language_type.dart';
import 'package:mevolvesupport/enums/firebase_document_type.dart';
import 'package:mevolvesupport/models/chat_message.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/repositories/storage_repository.dart';

part 'messages_cubit_state.dart';

class MessagesCubit extends Cubit<MessagesCubitState> {
  MessagesCubit(this._databaseRepository, this._storageRepository)
      : super(const MessagesCubitState());

  final DatabaseRepository _databaseRepository;
  final StorageRepository _storageRepository;

  Future<void> updateSupportStatus({
    required ChatUser chatUser,
    bool isClarifySwitch = false,
    bool updateCounts = true,
  }) async {
    await _databaseRepository.updateSupportStatus(
      chatUser: chatUser,
      isClarifySwitch: isClarifySwitch,
      updateCounts: updateCounts,
    );
  }

  Future<void> sendMessage({
    required ChatUser chatUser,
    required ChatMessage chatMessage,
    List<Uint8List> attachmentBytes = const [],
    bool updateCounts = true,
    String? supportLanguage,
  }) async {
    if (chatMessage.attachments != null &&
        chatMessage.attachments!.isNotEmpty &&
        chatMessage.attachments!.any(
          (element) =>
              element.status == AttachmentUploadStatus.temporary &&
              attachmentBytes.isNotEmpty,
        )) {
      int i = 0;
      for (var image in chatMessage.attachments!) {
        if (image.status == AttachmentUploadStatus.temporary) {
          await _storageRepository.uploadImage(
            id: chatMessage.id,
            imagePath: image.localFilePath!,
            userId: chatMessage.uid,
            docType: FirebaseDocCollectionType.chatMessages,
            imageBytes: attachmentBytes[i],
          );
        }
        i++;
      }
    }

    await _databaseRepository.sendMessage(
      chatUser: chatUser,
      chatMessage: chatMessage,
      updateCounts: updateCounts,
    );
    final supportLanguage = await _databaseRepository.fetchUserSupportLanguage(
      chatUser.uid,
    );
    if (supportLanguage != null &&
        supportLanguage != LanguageType.english.name) {
      Map<String, dynamic> dataMap = {};
      dataMap['text'] = chatMessage.message.en;
      dataMap['chatMsg'] = chatMessage.toJson();
      dataMap['target'] = supportLanguage;
      _databaseRepository.translateToolMsg(data: dataMap);
    }
  }
}
