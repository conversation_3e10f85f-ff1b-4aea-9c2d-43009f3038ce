import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/enums/permission_type.dart';
import 'package:mevolvesupport/enums/tab_type.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/utilities/utility_methods.dart';

part 'app_navigation_state.dart';

/// Global navigation state management for the 4-widget architecture
/// Controls: Sidebar selection, Details panel tabs, Modal states
class AppNavigationCubit extends Cubit<AppNavigationState> {
  AppNavigationCubit() : super(AppNavigationState.initial());

  /// Single method to update navigation state
  void updateNavigationState({
    TabType? selectedScreen,
    int? selectedDetailsTab,
    bool? showFeedbackSheet,
    bool? showDeleteReportSheet,
    FeedbackModel? selectedFeedback,
    DeleteReport? selectedReport,
    bool clearModals = false,
  }) {
    emit(
      state.copyWith(
        selectedScreen: selectedScreen,
        selectedDetailsTab: selectedDetailsTab,
        showFeedbackSheet: clearModals ? false : showFeedbackSheet,
        showDeleteReportSheet: clearModals ? false : showDeleteReportSheet,
        selectedFeedback: clearModals ? null : selectedFeedback,
        selectedReport: clearModals ? null : selectedReport,
      ),
    );
  }

  /// Get permission type for a tab
  static PermissionType? getTabPermissionType(TabType tabType) {
    switch (tabType) {
      case TabType.allUsers:
        return PermissionType.allUsers;
      case TabType.supportChat:
        return PermissionType.supportChat;
      case TabType.feedback:
        return PermissionType.feedbacks;
      case TabType.deleteReports:
        return PermissionType.deletedReports;
      case TabType.adminPanel:
        return PermissionType.adminPanel;
      case TabType.schema:
        return null;
      case TabType.segments:
        return PermissionType.segments;
      case TabType.pitrRecovery:
        return PermissionType.pitrRecovery;
    }
  }

  /// Check if user has permission for a tab
  static bool hasTabPermission(BuildContext context, TabType tabType) {
    final permissionType = getTabPermissionType(tabType);
    if (permissionType == null) return true;

    return hasPermission(
      context: context,
      permissionType: permissionType,
    );
  }

  /// Check if tab has a details panel
  static bool hasDetailsPanel(TabType tabType) {
    switch (tabType) {
      case TabType.allUsers:
      case TabType.supportChat:
      case TabType.feedback:
      case TabType.deleteReports:
      case TabType.segments:
        return true; // These tabs have details panels
      case TabType.adminPanel:
      case TabType.schema:
      case TabType.pitrRecovery:
        return false; // No details panels for these tabs
    }
  }
}
