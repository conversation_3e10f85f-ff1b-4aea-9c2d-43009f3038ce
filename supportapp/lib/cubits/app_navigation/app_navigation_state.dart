part of 'app_navigation_cubit.dart';

class AppNavigationState extends Equatable {
  const AppNavigationState({
    required this.selectedScreen,
    this.selectedUserId,
    required this.selectedDetailsTab,
    required this.showFeedbackSheet,
    required this.showDeleteReportSheet,
    this.selectedFeedback,
    this.selectedReport,
  });

  factory AppNavigationState.initial() => const AppNavigationState(
        selectedScreen: TabType.allUsers,
        selectedUserId: null,
        selectedDetailsTab: 0,
        showFeedbackSheet: false,
        showDeleteReportSheet: false,
      );
  final TabType selectedScreen;
  final String? selectedUserId;
  final int selectedDetailsTab;
  final bool showFeedbackSheet;
  final bool showDeleteReportSheet;
  final FeedbackModel? selectedFeedback;
  final DeleteReport? selectedReport;

  AppNavigationState copyWith({
    TabType? selectedScreen,
    String? selectedUserId,
    int? selectedDetailsTab,
    bool? showFeedbackSheet,
    bool? showDeleteReportSheet,
    FeedbackModel? selectedFeedback,
    DeleteReport? selectedReport,
    bool clearDetails = false,
    bool clearOtherSheets = false,
    bool nullifyUserId = false, // Add this to explicitly clear userId
  }) {
    return AppNavigationState(
      selectedScreen: selectedScreen ?? this.selectedScreen,
      // Fixed: properly handle selectedUserId updates
      selectedUserId: clearDetails || nullifyUserId
          ? null
          : (selectedUserId ?? this.selectedUserId),
      selectedDetailsTab:
          clearDetails ? 0 : (selectedDetailsTab ?? this.selectedDetailsTab),
      showFeedbackSheet: clearOtherSheets
          ? (showFeedbackSheet ?? false)
          : (showFeedbackSheet ?? this.showFeedbackSheet),
      showDeleteReportSheet: clearOtherSheets
          ? (showDeleteReportSheet ?? false)
          : (showDeleteReportSheet ?? this.showDeleteReportSheet),
      selectedFeedback: clearOtherSheets
          ? (showFeedbackSheet == true ? selectedFeedback : null)
          : (selectedFeedback ?? this.selectedFeedback),
      selectedReport: clearOtherSheets
          ? (showDeleteReportSheet == true ? selectedReport : null)
          : (selectedReport ?? this.selectedReport),
    );
  }

  @override
  List<Object?> get props => [
        selectedScreen,
        selectedUserId,
        selectedDetailsTab,
        showFeedbackSheet,
        showDeleteReportSheet,
        selectedFeedback,
        selectedReport,
      ];
}
