import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/chats_count.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'chats_cubit_state.dart';

class ChatsCubit extends Cubit<ChatsCubitState> {
  ChatsCubit(this._databaseRepository) : super(const ChatsCubitState());

  final DatabaseRepository _databaseRepository;
  StreamSubscription<ChatsCount>? _listenChatsCount;

  Future<void> listenChatsCount() async {
    await _listenChatsCount?.cancel();
    _listenChatsCount = _databaseRepository.listenChatsCount().listen((count) {
      emit(state.copyWith(chatsCount: count));
    });
  }

  @override
  Future<void> close() async {
    await _listenChatsCount?.cancel();
    return super.close();
  }
}
