import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'chats_updates_cubit_state.dart';

// class ChatsUpdatesCubit extends Cubit<ChatsUpdatesCubitState> {
//   ChatsUpdatesCubit(this._databaseRepository)
//       : super(const ChatsUpdatesCubitState());

//   final DatabaseRepository _databaseRepository;

//   StreamSubscription<List<ChatUser>>? _listenChats;

//   Future<void> listenChats({
//     required ChatStatus currentChatStatus,
//     required List<ChatUser> currentChats,
//     required List<String?> currentByFilter,
//   }) async {
//     emit(
//       state.copyWith(
//         isUpdate: false,
//       ),
//     );
//     await _listenChats?.cancel();
//     _listenChats = _databaseRepository
//         .listenChatUpdates(
//       status: currentChatStatus,
//       count: currentChats.length,
//       byFilter: currentByFilter[0],
//     )
//         .listen((List<ChatUser> listenChats) async {
//       if (!listEquals(currentChats, listenChats)) {
//         final newChats = await _databaseRepository.fetchChats(
//           status: currentChatStatus,
//           limit: currentChats.length,
//           byFilter: currentByFilter[0],
//         );
//         if (!listEquals(currentChats, newChats)) {
//           emit(
//             state.copyWith(
//               isUpdate: true,
//             ),
//           );
//         } else {
//           emit(
//             state.copyWith(
//               isUpdate: false,
//             ),
//           );
//         }
//       } else {
//         emit(
//           state.copyWith(
//             isUpdate: false,
//           ),
//         );
//       }
//     });
//   }

//   @override
//   Future<void> close() async {
//     await _listenChats?.cancel();
//     return super.close();
//   }
// }

class ChatsUpdatesCubit extends Cubit<ChatsUpdatesCubitState> {
  ChatsUpdatesCubit(this._databaseRepository)
      : super(const ChatsUpdatesCubitState());

  final DatabaseRepository _databaseRepository;

  StreamSubscription<List<ChatUser>>? _listenChats;

  Future<void> listenChats({
    required ChatStatus currentChatStatus,
    required List<ChatUser> currentChats,
    required List<String?> currentByFilter,
  }) async {
    emit(
      state.copyWith(
        isUpdate: false,
      ),
    );
    await _listenChats?.cancel();
    _listenChats = _databaseRepository
        .listenChatUpdates(
      status: currentChatStatus,
      count: currentChats.length,
      byFilter: currentByFilter[0],
    )
        .listen((List<ChatUser> listenChats) async {
      // Use more precise comparison instead of listEquals
      if (_hasSignificantChanges(currentChats, listenChats)) {
        final newChats = await _databaseRepository.fetchChats(
          status: currentChatStatus,
          limit: currentChats.length,
          byFilter: currentByFilter[0],
        );
        // Use more precise comparison for the second check as well
        if (_hasSignificantChanges(currentChats, newChats)) {
          emit(
            state.copyWith(
              isUpdate: true,
            ),
          );
        } else {
          emit(
            state.copyWith(
              isUpdate: false,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            isUpdate: false,
          ),
        );
      }
    });
  }

  bool _hasSignificantChanges(
    List<ChatUser> oldChats,
    List<ChatUser> newChats,
  ) {
    if (oldChats.length != newChats.length) {
      return true;
    }

    final oldChatsMap = {for (var chat in oldChats) chat.uid: chat};
    final newChatsMap = {for (var chat in newChats) chat.uid: chat};

    for (final uid in newChatsMap.keys) {
      if (!oldChatsMap.containsKey(uid)) {
        return true;
      }
    }

    for (final uid in oldChatsMap.keys) {
      final oldChat = oldChatsMap[uid];
      final newChat = newChatsMap[uid];

      if (newChat == null) {
        return true;
      }

      if (_isChatUpdated(oldChat!, newChat)) {
        return true;
      }
    }

    return false;
  }

  bool _isChatUpdated(ChatUser oldChat, ChatUser newChat) {
    if (oldChat.cloudUpdatedAt != null &&
        newChat.cloudUpdatedAt != null &&
        newChat.cloudUpdatedAt!.isAfter(oldChat.cloudUpdatedAt!)) {
      return true;
    }

    if (oldChat.localUpdatedAt != null &&
        newChat.localUpdatedAt != null &&
        newChat.localUpdatedAt!.isAfter(oldChat.localUpdatedAt!)) {
      return true;
    }
    return false;
  }

  @override
  Future<void> close() async {
    await _listenChats?.cancel();
    return super.close();
  }
}
