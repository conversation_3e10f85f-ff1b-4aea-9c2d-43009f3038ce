part of 'chats_cubit.dart';

class ChatsCubitState extends Equatable {
  const ChatsCubitState({
    this.chatsCount = const ChatsCount(
      notReplied: 0,
      replied: 0,
      clarify: 0,
      resolved: 0,
    ),
  });

  final ChatsCount chatsCount;

  @override
  List<Object> get props => [
        chatsCount,
      ];

  ChatsCubitState copyWith({
    ChatsCount? chatsCount,
  }) {
    return ChatsCubitState(
      chatsCount: chatsCount ?? this.chatsCount,
    );
  }
}
