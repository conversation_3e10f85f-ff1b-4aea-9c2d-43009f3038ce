import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:mevolvesupport/enums/issue_report_status.dart';
import 'package:mevolvesupport/models/issue_report.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'issue_reports_updates_cubit_state.dart';

class IssueReportsUpdatesCubit extends Cubit<IssueReportsUpdatesCubitState> {
  IssueReportsUpdatesCubit(this._databaseRepository)
      : super(const IssueReportsUpdatesCubitState());

  final DatabaseRepository _databaseRepository;

  StreamSubscription<List<IssueReport>>? _listenIssueReports;

  Future<void> listenChats({
    required IssueReportStatus currentIssueReportStatus,
    required List<IssueReport> currentReports,
    required List<String?> currentByFilter,
  }) async {
    emit(
      state.copyWith(
        isUpdate: false,
      ),
    );
    await _listenIssueReports?.cancel();
    _listenIssueReports = _databaseRepository
        .listenIssueReports(
      reportStatus: currentIssueReportStatus,
      byFilter: currentByFilter[0],
      count: currentReports.length,
    )
        .listen((List<IssueReport> listenReports) async {
      if (!listEquals(currentReports, listenReports)) {
        final newReports = await _databaseRepository.fetchIssueReports(
          reportStatus: currentIssueReportStatus,
          limit: currentReports.length,
          byFilter: currentByFilter[0],
        );
        if (!listEquals(currentReports, newReports)) {
          emit(
            state.copyWith(
              isUpdate: true,
            ),
          );
        } else {
          emit(
            state.copyWith(
              isUpdate: false,
            ),
          );
        }
      } else {
        emit(
          state.copyWith(
            isUpdate: false,
          ),
        );
      }
    });
  }

  @override
  Future<void> close() async {
    await _listenIssueReports?.cancel();
    return super.close();
  }
}
