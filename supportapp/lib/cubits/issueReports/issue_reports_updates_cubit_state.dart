part of 'issue_reports_updates_cubit.dart';

class IssueReportsUpdatesCubitState extends Equatable {
  const IssueReportsUpdatesCubitState({
    this.isUpdate = false,
  });

  final bool isUpdate;

  @override
  List<Object> get props => [
        isUpdate,
      ];

  IssueReportsUpdatesCubitState copyWith({
    bool? isUpdate,
  }) {
    return IssueReportsUpdatesCubitState(
      isUpdate: isUpdate ?? this.isUpdate,
    );
  }
}
