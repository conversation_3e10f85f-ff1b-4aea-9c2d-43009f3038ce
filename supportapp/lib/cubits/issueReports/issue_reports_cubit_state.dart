part of 'issue_reports_cubit.dart';

class IssueReportsCubitState extends Equatable {
  const IssueReportsCubitState({
    this.userIssueReports = const [],
    this.issueReportsCount = const IssueReportsCount(
      toBeReviewed: 0,
      inReview: 0,
      inProcess: 0,
      resolved: 0,
    ),
  });

  final List<IssueReport> userIssueReports;
  final IssueReportsCount issueReportsCount;

  @override
  List<Object> get props => [
        userIssueReports,
        issueReportsCount,
      ];

  IssueReportsCubitState copyWith({
    List<IssueReport>? userIssueReports,
    IssueReportsCount? issueReportsCount,
  }) {
    return IssueReportsCubitState(
      userIssueReports: userIssueReports ?? this.userIssueReports,
      issueReportsCount: issueReportsCount ?? this.issueReportsCount,
    );
  }
}
