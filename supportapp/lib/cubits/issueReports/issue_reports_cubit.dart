import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/issue_report.dart';
import 'package:mevolvesupport/models/issue_reports_count.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'issue_reports_cubit_state.dart';

class IssueReportsCubit extends Cubit<IssueReportsCubitState> {
  IssueReportsCubit(this._databaseRepository)
      : super(const IssueReportsCubitState());

  final DatabaseRepository _databaseRepository;
  StreamSubscription<IssueReportsCount>? _listenReportsCount;

  Future<void> fetchCurrentIssueReports({required String uid}) async {
    final userIssueReports =
        await _databaseRepository.fetchUserIssueReports(uid);
    emit(state.copyWith(userIssueReports: userIssueReports));
  }

  Future<void> listenIssueReportsCount() async {
    await _listenReportsCount?.cancel();
    _listenReportsCount =
        _databaseRepository.listenIssueReportsCount().listen((count) {
      emit(state.copyWith(issueReportsCount: count));
    });
  }

  Future<void> updateIssueReportStatus({
    required IssueReport issueReport,
  }) async {
    await _databaseRepository.updateIssueReportStatus(
      issueReport: issueReport,
    );
  }

  @override
  Future<void> close() async {
    await _listenReportsCount?.cancel();

    return super.close();
  }
}
