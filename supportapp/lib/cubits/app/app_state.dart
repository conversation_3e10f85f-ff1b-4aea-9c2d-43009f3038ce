part of 'app_cubit.dart';

class AppCubitState extends Equatable {
  const AppCubitState({
    this.supportTabCount = 0,
    this.feedbacksTabCount = 0,
    this.supportUsers = const [],
    this.isConnected = true,
    this.schemaMap = const {},
  });
  final int supportTabCount;
  final int feedbacksTabCount;
  final List<SupportUser>? supportUsers;
  final bool isConnected;
  final Map<String, dynamic> schemaMap;

  @override
  List<Object?> get props => [
        supportTabCount,
        feedbacksTabCount,
        supportUsers,
        isConnected,
        schemaMap,
      ];

  AppCubitState copyWith({
    int? supportTabCount,
    int? feedbacksTabCount,
    List<SupportUser>? supportUsers,
    bool? isConnected,
    Map<String, dynamic>? schemaMap,
  }) {
    return AppCubitState(
      supportTabCount: supportTabCount ?? this.supportTabCount,
      feedbacksTabCount: feedbacksTabCount ?? this.feedbacksTabCount,
      supportUsers: supportUsers ?? this.supportUsers,
      isConnected: isConnected ?? this.isConnected,
      schemaMap: schemaMap ?? this.schemaMap,
    );
  }
}
