import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:internet_connection_checker_plus/internet_connection_checker_plus.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
part 'app_state.dart';

class AppCubit extends Cubit<AppCubitState> {
  AppCubit(this._databaseRepository) : super(const AppCubitState());

  final DatabaseRepository _databaseRepository;
  StreamSubscription<List<SupportUser>>? _listenSupportUsers;
  StreamSubscription<InternetStatus>? _internetConnectionSubscription;

  Future<void> updateSupportTabCount({
    int? count,
    bool hasPerms = true,
  }) async {
    if (hasPerms) {
      if (count != null) {
        emit(state.copyWith(supportTabCount: count));
      } else {
        final supportTabCount =
            await _databaseRepository.updateSupportTabCount();
        emit(state.copyWith(supportTabCount: supportTabCount));
      }
    }
  }

  Future<void> updatefeedbacksTabCount({
    int? count,
    bool hasPerms = true,
  }) async {
    if (hasPerms) {
      if (count != null) {
        emit(state.copyWith(feedbacksTabCount: count));
      } else {
        final feedbacksTabCount =
            await _databaseRepository.fetchFeedbacksCount();
        emit(state.copyWith(feedbacksTabCount: feedbacksTabCount));
      }
    }
  }

  Future<void> listenSupportUsers() async {
    await _listenSupportUsers?.cancel();
    _listenSupportUsers = _databaseRepository.listenSupportUsers().listen(
      (supportUsers) {
        supportUsers.sort((a, b) => a.sname.compareTo(b.sname));
        emit(state.copyWith(supportUsers: supportUsers));
      },
    );
  }

  Future<void> listenInternetConnection() async {
    await _internetConnectionSubscription?.cancel();
    _internetConnectionSubscription =
        InternetConnection().onStatusChange.listen((status) {
      emit(state.copyWith(isConnected: status == InternetStatus.connected));
    });
  }

  Future<void> getSchemaMap() async {
    if (state.schemaMap.isEmpty) {
      final schemaMap = await _databaseRepository.getDbSchema();
      if (schemaMap.isNotEmpty) {
        emit(state.copyWith(schemaMap: schemaMap));
      }
    }
  }

  @override
  Future<void> close() async {
    await _listenSupportUsers?.cancel();
    return super.close();
  }
}
