import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/attachment_info.dart';
import 'package:mevolvesupport/models/chat_message.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'message_block_state.dart';

class MessageBlockCubit extends Cubit<MessageBlockState> {
  MessageBlockCubit(this._databaseRepository)
      : super(const MessageBlockState());

  final DatabaseRepository _databaseRepository;
  StreamSubscription<ChatMessage?>? _listenMessageBlock;

  Future<void> listenMessageBlock({required ChatMessage message}) async {
    final attachments = message.attachments ?? [];
    if (!checkAttachmentsCloud(attachments)) {
      await _listenMessageBlock?.cancel();

      _listenMessageBlock = _databaseRepository
          .listenMessageBlock(id: message.id)
          .listen((chatMessage) async {
        if (chatMessage != null) {
          if (checkAttachmentsCloud(chatMessage.attachments)) {
            emit(state.copyWith(chatMessage: chatMessage));
            await _listenMessageBlock?.cancel();
          }
        }
      });
    }
  }

  @override
  Future<void> close() async {
    await _listenMessageBlock?.cancel();
    return super.close();
  }

  bool checkAttachmentsCloud(List<MeAttachmentInfo>? attachments) {
    if (attachments != null && attachments.isNotEmpty) {
      for (var item in attachments) {
        if (item.status != AttachmentUploadStatus.cloud) {
          return false;
        }
      }
    }
    return true;
  }
}
