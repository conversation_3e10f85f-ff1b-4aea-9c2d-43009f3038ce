import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'feedbacks_cubit_state.dart';

class FeedbacksCubit extends Cubit<FeedbacksCubitState> {
  FeedbacksCubit(this._databaseRepository) : super(const FeedbacksCubitState());

  final DatabaseRepository _databaseRepository;
  StreamSubscription<int>? _listenReportsCount;

  Future<void> fetchCurrentFeedbacks({required String uid}) async {
    final userFeedbacks = await _databaseRepository.fetchUserFeedbacks(uid);
    emit(state.copyWith(userFeedbacks: userFeedbacks));
  }

  Future<void> updateFeedbacktatus({
    required FeedbackModel feedback,
  }) async {
    await _databaseRepository.updateFeedbacktatus(
      feedback: feedback,
    );
  }

  Future<void> listenFeedbacksCount() async {
    await _listenReportsCount?.cancel();
    _listenReportsCount =
        _databaseRepository.listenFeedbacksCount().listen((count) {
      emit(state.copyWith(feedbacksCount: count));
    });
  }

  @override
  Future<void> close() async {
    await _listenReportsCount?.cancel();

    return super.close();
  }
}
