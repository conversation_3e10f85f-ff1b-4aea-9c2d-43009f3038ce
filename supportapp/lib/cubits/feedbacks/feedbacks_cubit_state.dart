part of 'feedbacks_cubit.dart';

class FeedbacksCubitState extends Equatable {
  const FeedbacksCubitState({
    this.userFeedbacks = const [],
    this.feedbacksCount = 0,
  });

  final List<FeedbackModel> userFeedbacks;
  final int feedbacksCount;

  @override
  List<Object> get props => [
        userFeedbacks,
        feedbacksCount,
      ];

  FeedbacksCubitState copyWith({
    List<FeedbackModel>? userFeedbacks,
    int? feedbacksCount,
  }) {
    return FeedbacksCubitState(
      userFeedbacks: userFeedbacks ?? this.userFeedbacks,
      feedbacksCount: feedbacksCount ?? this.feedbacksCount,
    );
  }
}
