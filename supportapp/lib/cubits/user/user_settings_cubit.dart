import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolvesupport/blocs/app/app_bloc.dart';
import 'package:mevolvesupport/enums/theme_custom_color.dart';
import 'package:mevolvesupport/enums/theme_type.dart';
import 'package:mevolvesupport/models/support_user.dart';
import 'package:mevolvesupport/styles/themes/me_app_colors.dart';

part 'user_settings_state.dart';

class UserSettingsCubit extends Cubit<UserSettingsState> {
  UserSettingsCubit(this.appBloc) : super(const UserSettingsState()) {
    if (appBloc.state.supportUser != null) {
      updateThemeOnUserChange(appBloc.state.supportUser!);
    } else {
      setDefaultTheme();
    }
  }

  final AppBloc appBloc;

  bool isDarkMode(SupportUser supportUser) {
    if (supportUser.appTheme == ThemeType.dark) {
      return true;
    } else if (supportUser.appTheme == ThemeType.systemDefault) {
      var brightness =
          SchedulerBinding.instance.platformDispatcher.platformBrightness;

      return brightness == Brightness.dark;
    }
    return false;
  }

  void updateThemeOnUserChange(SupportUser supportUser) {
    _setThemeType(supportUser.appTheme);
  }

  void setDefaultTheme() {
    _setTheme(
      CustomTheme.green,
    );
  }

  void setTheme(ThemeType themeType) {
    appBloc.add(
      SupportUserChanged(
        supportUser: appBloc.state.supportUser!.copyWith(appTheme: themeType),
      ),
    );
  }

  void _setTheme(
    CustomTheme customColor,
  ) {
    late final UserSettingsState newTodayState;
    switch (customColor) {
      case CustomTheme.blue:
        newTodayState = state.copyWith(
          lightColorScheme: MeAppColors.blueLightColorScheme,
          darkColorScheme: MeAppColors.blueDarkColorScheme,
        );
        break;
      case CustomTheme.green:
        newTodayState = state.copyWith(
          lightColorScheme: MeAppColors.greenLightColorScheme,
          darkColorScheme: MeAppColors.greenDarkColorScheme,
        );
        break;
      case CustomTheme.purple:
        newTodayState = state.copyWith(
          lightColorScheme: MeAppColors.purpleLightColorScheme,
          darkColorScheme: MeAppColors.purpleDarkColorScheme,
        );
        break;
      case CustomTheme.red:
        newTodayState = state.copyWith(
          lightColorScheme: MeAppColors.redLightColorScheme,
          darkColorScheme: MeAppColors.redDarkColorScheme,
        );
        break;
      case CustomTheme.pink:
        newTodayState = state.copyWith(
          lightColorScheme: MeAppColors.pinkLightColorScheme,
          darkColorScheme: MeAppColors.pinkDarkColorScheme,
        );
        break;
    }
    emit(
      newTodayState,
    );
  }

  void _setThemeType(ThemeType themeType) {
    emit(
      state.copyWith(
        appThemeMode: themeType,
      ),
    );
  }

  static String whichColorSchemeString(MeColorScheme colorScheme) {
    if (MeAppColors.blueLightColorScheme == colorScheme) {
      return 'Blue';
    } else if (MeAppColors.greenLightColorScheme == colorScheme) {
      return 'Green';
    } else if (MeAppColors.purpleLightColorScheme == colorScheme) {
      return 'Purple';
    } else if (MeAppColors.redLightColorScheme == colorScheme) {
      return 'Red';
    } else if (MeAppColors.pinkLightColorScheme == colorScheme) {
      return 'Pink';
    } else {
      return 'Blue';
    }
  }

  static CustomTheme whichColor(String themeColor) {
    switch (themeColor) {
      case 'Blue':
        return CustomTheme.blue;
      case 'Green':
        return CustomTheme.green;
      case 'Purple':
        return CustomTheme.purple;
      case 'Red':
        return CustomTheme.red;
      case 'Pink':
        return CustomTheme.pink;
      default:
        return CustomTheme.blue;
    }
  }
}
