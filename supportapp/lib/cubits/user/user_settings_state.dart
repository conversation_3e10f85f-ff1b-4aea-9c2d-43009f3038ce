part of 'user_settings_cubit.dart';

class UserSettingsState extends Equatable {
  const UserSettingsState({
    this.lightColorScheme = MeAppColors.greenLightColorScheme,
    this.darkColorScheme = MeAppColors.greenDarkColorScheme,
    this.appThemeMode = ThemeType.light,
  });

  final ThemeType appThemeMode;
  final MeColorScheme lightColorScheme;
  final MeColorScheme darkColorScheme;

  @override
  List<Object?> get props => [
        lightColorScheme,
        darkColorScheme,
        appThemeMode,
      ];

  UserSettingsState copyWith({
    ThemeType? appThemeMode,
    MeColorScheme? lightColorScheme,
    MeColorScheme? darkColorScheme,
  }) {
    return UserSettingsState(
      appThemeMode: appThemeMode ?? this.appThemeMode,
      lightColorScheme: lightColorScheme ?? this.lightColorScheme,
      darkColorScheme: darkColorScheme ?? this.darkColorScheme,
    );
  }
}

class MeColorScheme extends ThemeExtension<MeColorScheme> with EquatableMixin {
  const MeColorScheme({
    this.color1 = MeAppColors.greenLight1,
    this.color2 = MeAppColors.greenLight2,
    this.color3 = MeAppColors.greenLight3,
    this.color4 = MeAppColors.greenLight4,
    this.color5 = MeAppColors.greenLight5,
    this.color6 = MeAppColors.greenLight6,
    this.color7 = MeAppColors.greenLight7,
    this.color8 = MeAppColors.greenLight8,
    this.color9 = MeAppColors.greenLight9,
    this.color10 = MeAppColors.greenLight10,
    this.color11 = MeAppColors.greenLight11,
    this.color12 = MeAppColors.greenLight12,
    this.color13 = MeAppColors.greenLight13,
    this.color14 = MeAppColors.greenLight14,
    this.color15 = MeAppColors.greenLight15,
    this.color16 = MeAppColors.greenLight16,
    this.color17 = MeAppColors.greenLight17,
    this.color18 = MeAppColors.greenLight18,
    this.color19 = MeAppColors.greenLight19,
    this.color20 = MeAppColors.greenLight20,
    this.color21 = MeAppColors.greenLight21,
    this.color22 = MeAppColors.greenLight22,
    this.color23 = MeAppColors.greenLight23,
    this.color24 = MeAppColors.greenLight24,
    this.color25 = MeAppColors.greenLight25,
    this.color26 = MeAppColors.greenLight26,
    this.color27 = MeAppColors.greenLight27,
    this.color28 = MeAppColors.greenLight28,
    this.color29 = MeAppColors.greenLight29,
    this.color30 = MeAppColors.greenLight30,
    this.color31 = MeAppColors.greenLight31,
    this.color32 = MeAppColors.greenLight32,
    this.color35 = MeAppColors.greenLight35,
  });

  final Color color1;
  final Color color2;
  final Color color3;
  final Color color5;
  final Color color6;
  final Color color9;
  final Color color7;
  final Color color8;
  final Color color4;
  final Color color10;
  final Color color11;
  final Color color12;
  final Color color13;
  final Color color14;
  final Color color15;
  final Color color16;
  final Color color17;
  final Color color18;
  final Color color19;
  final Color color20;
  final Color color21;
  final Color color22;
  final Color color23;
  final Color color24;
  final Color color25;
  final Color color26;
  final Color color27;
  final Color color28;
  final Color color29;
  final Color color30;
  final Color color31;
  final Color color32;
  final Color color35;

  @override
  List<Object> get props {
    return [
      color1,
      color2,
      color3,
      color5,
      color6,
      color7,
      color8,
      color4,
      color9,
      color10,
      color11,
      color12,
      color13,
      color14,
      color15,
      color16,
      color17,
      color18,
      color19,
      color20,
      color21,
      color22,
      color23,
      color24,
      color25,
      color26,
      color27,
      color28,
      color29,
      color30,
      color31,
      color32,
      color35,
    ];
  }

  @override
  MeColorScheme copyWith({
    Color? color1,
    Color? color2,
    Color? color3,
    Color? color5,
    Color? color6,
    Color? color9,
    Color? color7,
    Color? color8,
    Color? color4,
    Color? color10,
    Color? color11,
    Color? color12,
    Color? color13,
    Color? color14,
    Color? color15,
    Color? color16,
    Color? color17,
    Color? color18,
    Color? color19,
    Color? color20,
    Color? color21,
    Color? color22,
    Color? color23,
    Color? color24,
    Color? color25,
    Color? color26,
    Color? color27,
    Color? color28,
    Color? color29,
    Color? color30,
    Color? color31,
    Color? color32,
    Color? color35,
  }) {
    return MeColorScheme(
      color1: color1 ?? this.color1,
      color2: color2 ?? this.color2,
      color3: color3 ?? this.color3,
      color5: color5 ?? this.color5,
      color6: color6 ?? this.color6,
      color9: color9 ?? this.color9,
      color7: color7 ?? this.color7,
      color8: color8 ?? this.color8,
      color4: color4 ?? this.color4,
      color10: color10 ?? this.color10,
      color11: color11 ?? this.color11,
      color12: color12 ?? this.color12,
      color13: color13 ?? this.color13,
      color14: color14 ?? this.color14,
      color15: color15 ?? this.color15,
      color16: color16 ?? this.color16,
      color17: color17 ?? this.color17,
      color18: color18 ?? this.color18,
      color19: color19 ?? this.color19,
      color20: color20 ?? this.color20,
      color21: color21 ?? this.color21,
      color22: color22 ?? this.color22,
      color23: color23 ?? this.color23,
      color24: color24 ?? this.color24,
      color25: color25 ?? this.color25,
      color26: color26 ?? this.color26,
      color27: color27 ?? this.color27,
      color28: color28 ?? this.color28,
      color29: color29 ?? this.color29,
      color30: color30 ?? this.color30,
      color31: color31 ?? this.color31,
      color32: color32 ?? this.color32,
      color35: color35 ?? this.color35,
    );
  }

  @override
  ThemeExtension<MeColorScheme> lerp(
    covariant ThemeExtension<MeColorScheme>? other,
    double t,
  ) {
    if (other == null) {
      return this;
    }
    if (other is! MeColorScheme) {
      return this;
    }
    return MeColorScheme(
      color1: Color.lerp(color1, other.color1, t)!,
      color2: Color.lerp(color2, other.color2, t)!,
      color3: Color.lerp(color3, other.color3, t)!,
      color5: Color.lerp(color5, other.color5, t)!,
      color6: Color.lerp(color6, other.color6, t)!,
      color9: Color.lerp(color9, other.color9, t)!,
      color7: Color.lerp(color7, other.color7, t)!,
      color8: Color.lerp(color8, other.color8, t)!,
      color4: Color.lerp(color4, other.color4, t)!,
      color10: Color.lerp(color10, other.color10, t)!,
      color11: Color.lerp(color11, other.color11, t)!,
      color12: Color.lerp(color12, other.color12, t)!,
      color13: Color.lerp(color13, other.color13, t)!,
      color14: Color.lerp(color14, other.color14, t)!,
      color15: Color.lerp(color15, other.color15, t)!,
      color16: Color.lerp(color16, other.color16, t)!,
      color17: Color.lerp(color17, other.color17, t)!,
      color18: Color.lerp(color18, other.color18, t)!,
      color19: Color.lerp(color19, other.color19, t)!,
      color20: Color.lerp(color20, other.color20, t)!,
      color21: Color.lerp(color21, other.color21, t)!,
      color22: Color.lerp(color22, other.color22, t)!,
      color23: Color.lerp(color23, other.color23, t)!,
      color24: Color.lerp(color24, other.color24, t)!,
      color25: Color.lerp(color25, other.color25, t)!,
      color26: Color.lerp(color26, other.color26, t)!,
      color27: Color.lerp(color27, other.color27, t)!,
      color28: Color.lerp(color28, other.color28, t)!,
      color29: Color.lerp(color29, other.color29, t)!,
      color30: Color.lerp(color30, other.color30, t)!,
      color31: Color.lerp(color31, other.color31, t)!,
      color32: Color.lerp(color32, other.color32, t)!,
      color35: Color.lerp(color35, other.color35, t)!,
    );
  }
}
