part of 'user_cubit.dart';

class UserState extends Equatable {
  const UserState({
    this.currentUserId,
    this.currentUser,
    this.currentChatUser,
    this.currentTab = 0,
    this.currentIssueReport,
    this.currentFeedback,
    this.currentDeletedReport,
    this.userDeleteReports = const [],
    this.userIssueReports = const [],
    this.subtabsState,
  });

  final String? currentUserId;
  final UserMetaData? currentUser;
  final ChatUser? currentChatUser;

  final int currentTab;
  final IssueReport? currentIssueReport;
  final FeedbackModel? currentFeedback;

  final DeleteReport? currentDeletedReport;
  final List<DeleteReport> userDeleteReports;
  final List<IssueReport> userIssueReports;
  final Map<SubtabType, String?>? subtabsState;

  @override
  List<Object?> get props => [
        currentUserId,
        currentUser,
        currentTab,
        currentChatUser,
        currentIssueReport,
        currentFeedback,
        currentDeletedReport,
        userDeleteReports,
        userIssueReports,
        subtabsState,
      ];

  UserState copyWith({
    String? currentUserId,
    Nullable<UserMetaData>? currentUser,
    ChatUser? currentChatUser,
    int? currentTab,
    IssueReport? currentIssueReport,
    FeedbackModel? currentFeedback,
    DeleteReport? currentDeletedReport,
    List<DeleteReport>? userDeleteReports,
    List<IssueReport>? userIssueReports,
    Map<SubtabType, String?>? subtabsState,
  }) {
    return UserState(
      currentUserId: currentUserId ?? this.currentUserId,
      currentUser: currentUser == null ? this.currentUser : currentUser.value,
      currentChatUser: currentChatUser ?? this.currentChatUser,
      currentTab: currentTab ?? this.currentTab,
      currentIssueReport: currentIssueReport,
      currentFeedback: currentFeedback,
      currentDeletedReport: currentDeletedReport,
      userDeleteReports: userDeleteReports ?? this.userDeleteReports,
      userIssueReports: userIssueReports ?? this.userIssueReports,
      subtabsState: subtabsState ?? this.subtabsState,
    );
  }
}
