import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter_helper_utils/flutter_helper_utils.dart';
import 'package:mevolvesupport/enums/purchase/user_entitlements.dart';
import 'package:mevolvesupport/enums/subtab_type.dart';
import 'package:mevolvesupport/enums/user_deleted_status.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/delete_report.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/models/issue_report.dart';
import 'package:mevolvesupport/models/user/feature_usage_info.dart';
import 'package:mevolvesupport/models/user_metadata.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';
import 'package:mevolvesupport/utilities/nullable.dart';

part 'user_state.dart';

class UserCubit extends Cubit<UserState> {
  UserCubit(this._databaseRepository) : super(const UserState());

  final DatabaseRepository _databaseRepository;

  Future<void> updateUserCubit({
    String? uid,
    UserMetaData? userData,
    int? tab,
    ChatUser? chatUser,
    IssueReport? currentIssueReport,
    DeleteReport? currentDeletedReport,
    FeedbackModel? currentFeedback,
    List<DeleteReport>? userDeleteReports,
    List<IssueReport>? userIssueReports,
  }) async {
    if (state.currentUserId == uid && state.currentUser != null) {
      userData = state.currentUser;
    } else {
      userData ??= await _databaseRepository.fetchUserData(uid!);
    }
    // chatUser ??= await _databaseRepository.fetchChatUser(uid!);
    if (tab == 0) {
      userDeleteReports = await _databaseRepository
          .fetchUserDeleteReports(userData?.uid ?? uid!);
    }

    if ((state.currentDeletedReport != null &&
            state.currentDeletedReport!.uid == (userData?.uid ?? uid!)) ||
        (currentDeletedReport != null &&
                currentDeletedReport.uid == (userData?.uid ?? uid!)) &&
            (userData != null &&
                userData.userDeletedStatus != null &&
                (userData.userDeletedStatus == UserDeletedStatus.remove_a ||
                    userData.userDeletedStatus ==
                        UserDeletedStatus.remove_u))) {
      userData = userData!.copyWith(
        userInfo: userData.userInfo.copyWith(
          name:
              currentDeletedReport?.uname ?? state.currentDeletedReport?.uname,
        ),
      );
    }
    emit(
      state.copyWith(
        currentUserId: uid,
        currentUser: userData == null ? null : Nullable(userData),
        currentTab: tab,
        currentChatUser: chatUser,
        currentIssueReport: currentIssueReport,
        currentFeedback: currentFeedback,
        currentDeletedReport: currentDeletedReport,
        userDeleteReports: userDeleteReports,
        userIssueReports: userIssueReports,
      ),
    );
  }

  Future<void> updateSubtabState({
    required SubtabType subtabType,
    required String? uid,
    int? tab,
    ChatUser? chatUser,
    IssueReport? currentIssueReport,
    FeedbackModel? currentFeedback,
  }) async {
    Map<SubtabType, String?>? subtabsState = state.subtabsState ?? {};
    UserMetaData? userData;
    List<DeleteReport>? userDeleteReports;
    DeleteReport? currentDeletedReport;
    if (tab == 1 && uid != null) {
      subtabsState[subtabType] = uid;
    } else if (tab == 2 && currentFeedback != null) {
      subtabsState[subtabType] = currentFeedback.id;
    } else {
      uid = state.subtabsState![subtabType];
    }

    if (state.currentUserId == uid && state.currentUser != null) {
      userData = state.currentUser!;
      userDeleteReports = state.userDeleteReports;
      currentDeletedReport = state.currentDeletedReport;
    } else {
      userData ??= await _databaseRepository.fetchUserData(uid!);
      userDeleteReports = await _databaseRepository
          .fetchUserDeleteReports(userData?.uid ?? uid!);
    }
    if (tab == 0 && state.currentUserId == uid) {
      userDeleteReports = await _databaseRepository
          .fetchUserDeleteReports(userData?.uid ?? uid!);
    }

    if ((state.currentDeletedReport != null &&
            state.currentDeletedReport!.uid == userData!.uid) ||
        (currentDeletedReport != null &&
                currentDeletedReport.uid == userData!.uid) &&
            userData.userDeletedStatus != null &&
            (userData.userDeletedStatus == UserDeletedStatus.remove_a ||
                userData.userDeletedStatus == UserDeletedStatus.remove_u)) {
      userData = userData.copyWith(
        userInfo: userData.userInfo.copyWith(
          name:
              currentDeletedReport?.uname ?? state.currentDeletedReport?.uname,
        ),
      );
    }
    emit(
      state.copyWith(
        currentUserId: uid,
        currentUser: userData == null ? null : Nullable(userData),
        currentTab: tab,
        currentChatUser: chatUser,
        currentIssueReport: currentIssueReport,
        currentDeletedReport: currentDeletedReport,
        userDeleteReports: userDeleteReports,
        // userIssueReports: userIssueReports,
        currentFeedback: currentFeedback,
        subtabsState: subtabsState,
      ),
    );
  }

  void resetUserCubit() {
    // emit(const UserState());
    emit(
      state.copyWith(
        currentUser: const Nullable(null),
      ),
    );
  }

  void updateCurrentTab(int tab) async {
    emit(state.copyWith(currentTab: tab));
  }

  Future<FeatureUsageInfo> getFeatureCount(
    String uid,
    UserMetaData userData,
  ) async {
    FeatureUsageInfo feature;
    if (userData.featureUsageInfo.featuresUpdatedAt == null ||
        !userData.featureUsageInfo.featuresUpdatedAt!.isToday) {
      feature = await _databaseRepository.fetchFeaturesCount(uid);
    } else {
      feature = userData.featureUsageInfo;
    }
    return feature;
  }

  Future<void> updateSuperSubscription({
    required String uid,
    required MeUserEntitlement subType,
  }) async {
    await _databaseRepository.updateSuperSubscription(
      uid: uid,
      subType: subType,
    );

    emit(
      state.copyWith(
        currentUser:
            Nullable(state.currentUser!.copyWith(superSubscription: subType)),
      ),
    );
  }
}
