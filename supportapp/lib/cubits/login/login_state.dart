part of 'login_cubit.dart';

enum LoginType {
  none,
  googleSignIn,
}

class LoginState extends Equatable {
  const LoginState({
    this.status = FormzSubmissionStatus.initial,
    this.errorMessage,
    this.loginType = LoginType.none,
    this.timeLeft = 0,
  });

  final FormzSubmissionStatus status;

  final String? errorMessage;
  final LoginType loginType;
  final int timeLeft;

  @override
  List<Object> get props => [
        status,
        loginType,
        timeLeft,
      ];

  LoginState copyWith({
    FormzSubmissionStatus? status,
    String? errorMessage,
    LoginType? loginType,
    int? timeLeft,
  }) {
    return LoginState(
      status: status ?? this.status,
      errorMessage: errorMessage ?? this.errorMessage,
      loginType: loginType ?? this.loginType,
      timeLeft: timeLeft ?? this.timeLeft,
    );
  }
}
