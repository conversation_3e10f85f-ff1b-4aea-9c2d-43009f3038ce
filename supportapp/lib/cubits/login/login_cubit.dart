import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:formz/formz.dart';
import 'package:mevolvesupport/providers/firebase_authentication.dart';
import 'package:mevolvesupport/blocs/app/app_bloc.dart';
import 'package:mevolvesupport/utilities/logger.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit(
    this._authenticationRepository,
    this._appBloc,
  ) : super(const LoginState());

  final FirebaseAuthenticationRepository _authenticationRepository;
  final AppBloc _appBloc;

  Future<void> logInWithGoogle() async {
    Log.d('LoginCubit: Starting Google login process');
    changeLoginInProgressState(LoginProcessStatus.loginInProgress);
    emit(
      state.copyWith(
        status: FormzSubmissionStatus.inProgress,
        loginType: LoginType.googleSignIn,
      ),
    );
    try {
      Log.d('LoginCubit: Calling authentication repository');
      final user = await _authenticationRepository.logInWithGoogle();
      if (user != null) {
        Log.d('LoginCubit: Received google user: ${user.email}');
        changeLoginInProgressState(
          LoginProcessStatus.freshLoginCompleted,
          user: user,
        );
        emit(state.copyWith(status: FormzSubmissionStatus.success));
      } else {
        Log.d('LoginCubit: User is null (canceled)');
        emit(state.copyWith(status: FormzSubmissionStatus.canceled));
      }
    } on LogInWithGoogleFailure catch (e) {
      Log.e('LoginCubit: LogInWithGoogleFailure: ${e.message}');
      emit(
        state.copyWith(
          errorMessage: e.message,
          status: FormzSubmissionStatus.failure,
        ),
      );
    } catch (error) {
      Log.e('LoginCubit: Unexpected error: $error');
      emit(state.copyWith(status: FormzSubmissionStatus.failure));
    }
  }

  void changeLoginInProgressState(LoginProcessStatus status, {User? user}) {
    _appBloc.add(
      AppLoginProcessStatusChanged(appLoginProcessStatus: status, user: user),
    );
  }
}
