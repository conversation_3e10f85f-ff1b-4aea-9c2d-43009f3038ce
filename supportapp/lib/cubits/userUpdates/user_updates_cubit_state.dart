part of 'user_updates_cubit.dart';

class UserUpdatesCubitState extends Equatable {
  const UserUpdatesCubitState({
    this.isChatNotReplied = false,
    this.isReportNotReviewed = false,
  });

  final bool isChatNotReplied;
  final bool isReportNotReviewed;

  @override
  List<Object> get props => [
        isChatNotReplied,
        isReportNotReviewed,
      ];

  UserUpdatesCubitState copyWith({
    bool? isChatNotReplied,
    bool? isReportNotReviewed,
  }) {
    return UserUpdatesCubitState(
      isChatNotReplied: isChatNotReplied ?? this.isChatNotReplied,
      isReportNotReviewed: isReportNotReviewed ?? this.isReportNotReviewed,
    );
  }
}
