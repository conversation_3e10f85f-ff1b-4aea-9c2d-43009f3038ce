import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/enums/chat_status.dart';
import 'package:mevolvesupport/models/chat_user.dart';
import 'package:mevolvesupport/models/issue_report.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'user_updates_cubit_state.dart';

class UserUpdatesCubit extends Cubit<UserUpdatesCubitState> {
  UserUpdatesCubit(this._databaseRepository)
      : super(const UserUpdatesCubitState());

  final DatabaseRepository _databaseRepository;

  StreamSubscription<ChatUser?>? _listenChatUser;
  StreamSubscription<IssueReport?>? _listenIssueReport;

  Future<void> updateUserUpdatesCubit({
    required String? uid,
  }) async {
    listenChatUser(uid: uid);
  }

  Future<void> listenChatUser({required String? uid}) async {
    await _listenChatUser?.cancel();
    if (uid != null) {
      _listenChatUser =
          _databaseRepository.listenChatBlock(uid: uid).listen((chatUser) {
        if (chatUser != null) {
          emit(
            state.copyWith(
              isChatNotReplied: chatUser.status == ChatStatus.notReplied,
            ),
          );
        } else {
          emit(state.copyWith(isChatNotReplied: false));
        }
      });
    }
  }

  // Future<void> listenIssueReport({required String? uid}) async {
  //   await _listenIssueReport?.cancel();
  //   if (uid != null) {
  //     _listenIssueReport = _databaseRepository
  //         .listenReportNotReviewed(uid: uid)
  //         .listen((report) {
  //       if (report != null) {
  //         emit(
  //           state.copyWith(
  //             isReportNotReviewed:
  //                 report.status == IssueReportStatus.toBeReviewed,
  //           ),
  //         );
  //       } else {
  //         emit(state.copyWith(isReportNotReviewed: false));
  //       }
  //     });
  //   }
  // }

  @override
  Future<void> close() async {
    await _listenChatUser?.cancel();
    await _listenIssueReport?.cancel();
    return super.close();
  }
}
