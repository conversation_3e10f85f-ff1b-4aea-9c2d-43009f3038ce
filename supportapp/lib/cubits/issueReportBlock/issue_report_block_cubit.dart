import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/issue_report.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'issue_report_block_state.dart';

class IssueReportBlockCubit extends Cubit<IssueReportBlockState> {
  IssueReportBlockCubit(this._databaseRepository)
      : super(const IssueReportBlockState());

  final DatabaseRepository _databaseRepository;
  StreamSubscription<IssueReport?>? _listenReportBlock;

  Future<void> listenReportBlockUpdates({
    required String id,
  }) async {
    await _listenReportBlock?.cancel();
    _listenReportBlock = _databaseRepository
        .listenIssueReportBlock(id: id)
        .listen((issueReport) {
      if (issueReport != null) {
        emit(state.copyWith(issueReport: issueReport));
      }
    });
  }

  @override
  Future<void> close() async {
    await _listenReportBlock?.cancel();
    return super.close();
  }
}
