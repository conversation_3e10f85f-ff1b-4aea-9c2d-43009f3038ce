part of 'issue_report_block_cubit.dart';

class IssueReportBlockState extends Equatable {
  const IssueReportBlockState({this.issueReport});

  final IssueReport? issueReport;

  @override
  List<Object?> get props => [
        issueReport,
      ];

  IssueReportBlockState copyWith({
    IssueReport? issueReport,
  }) {
    return IssueReportBlockState(
      issueReport: issueReport ?? this.issueReport,
    );
  }
}
