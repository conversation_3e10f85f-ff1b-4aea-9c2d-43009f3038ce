import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:mevolvesupport/models/feedback.dart';
import 'package:mevolvesupport/repositories/database_repository.dart';

part 'feedback_block_state.dart';

class FeedbackBlockCubit extends Cubit<FeedbackBlockState> {
  FeedbackBlockCubit(this._databaseRepository)
      : super(const FeedbackBlockState());

  final DatabaseRepository _databaseRepository;
  StreamSubscription<FeedbackModel?>? _listenReportBlock;

  Future<void> listenFeedbackBlockUpdates({
    required String id,
  }) async {
    await _listenReportBlock?.cancel();
    _listenReportBlock =
        _databaseRepository.listenFeedbackBlock(id: id).listen((issueReport) {
      if (issueReport != null) {
        emit(state.copyWith(feedback: issueReport));
      }
    });
  }

  @override
  Future<void> close() async {
    await _listenReportBlock?.cancel();
    return super.close();
  }
}
