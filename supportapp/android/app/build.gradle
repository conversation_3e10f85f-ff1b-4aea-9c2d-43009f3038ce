plugins {
   id "com.android.application"
    // START: FlutterFire Configuration
    id 'com.google.gms.google-services'
    // END: FlutterFire Configuration
   id "kotlin-android"
   id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

def debugKeystoreProperties = new Properties()
def debugKeystorePropertiesFile = rootProject.file('key-debug.properties')
if (debugKeystorePropertiesFile.exists()) {
    debugKeystoreProperties.load(new FileInputStream(debugKeystorePropertiesFile))
}

android {
    namespace "app.mevolve.support"
    compileSdkVersion flutter.compileSdkVersion
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        applicationId "app.mevolve.support"
        minSdkVersion 23
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        multiDexEnabled true
    }

    signingConfigs {
        debug {
            if (debugKeystoreProperties['keyAlias']) {
                keyAlias debugKeystoreProperties['keyAlias']
                keyPassword debugKeystoreProperties['keyPassword']
                storeFile debugKeystoreProperties['storeFile'] ? rootProject.file(debugKeystoreProperties['storeFile']) : null
                storePassword debugKeystoreProperties['storePassword']
            }
        }
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile keystoreProperties['storeFile'] ? file(keystoreProperties['storeFile']) : null
            storePassword keystoreProperties['storePassword']
        }
    }

    buildTypes {
        release {
            signingConfig signingConfigs.release
            minifyEnabled true
            shrinkResources true
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        profile {
            signingConfig signingConfigs.release['keyAlias'] != null ? signingConfigs.release : signingConfigs.debug
        }
        debug {
            signingConfig signingConfigs.debug
        }
    }

    buildFeatures {
        flavorDimensions = ["environment"]
    }
    
    productFlavors {
        dev {
            dimension "environment"
            applicationIdSuffix ".dev"
            manifestPlaceholders.envName = "dev"
            resValue "string", "app_name", "Mevolve Support Dev"
        }
        qa {
            dimension "environment"
            applicationIdSuffix ".qa"
            manifestPlaceholders.envName = "qa"
            resValue "string", "app_name", "Mevolve Support QA"
        }
        staging {
            dimension "environment"
            applicationIdSuffix ".staging"
            manifestPlaceholders.envName = "staging"
            resValue "string", "app_name", "Mevolve Support Staging"
        }
        prod {
            dimension "environment"
            applicationIdSuffix ".prod"
            manifestPlaceholders.envName = "prod"
            resValue "string", "app_name", "Mevolve Support"
        }
        hotfix {
            dimension "environment"
            applicationIdSuffix ".hotfix"
            manifestPlaceholders.envName = "hotfix"
            resValue "string", "app_name", "Mevolve Support Hotfix"
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation platform('com.google.firebase:firebase-bom:30.0.2')
    implementation 'com.android.support:multidex:1.0.3'
}