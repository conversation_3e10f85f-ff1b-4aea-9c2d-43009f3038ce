name: mevolvesupport
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 4.0.131+1

environment:
  sdk: ">=3.0.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  bloc: ^8.1.4
  cloud_firestore: ^5.4.4
  cloud_functions: ^5.1.3
  collection: ^1.17.2
  data_table_2: ^2.6.0
  dropdown_button2: ^3.0.0-beta.5
  equatable: ^2.0.5
  firebase_auth: ^5.3.1
  firebase_core: ^3.6.0
  firebase_storage: ^12.3.4
  firebase_app_check: ^0.3.1+4
  flutter:
    sdk: flutter
  flutter_bloc: ^8.1.5
  flutter_helper_utils: ^4.0.1
  flutter_image_compress: ^2.3.0
  flutter_gen: ^5.2.0
  flutter_html: ^3.0.0-beta.2
  flutter_quill: ^9.3.11
  flutter_svg: ^2.0.1
  formz: ^0.7.0
  get_time_ago: ^1.1.5
  google_fonts: ^6.1.0
  google_sign_in: ^6.1.0
  http: ^1.2.1
  image_picker: ^1.0.4
  internet_connection_checker_plus: ^2.1.0
  intl: ^0.19.0
  json_view: ^0.4.2
  loading_indicator: ^3.1.0
  logger: ^2.0.1
  mime: ^1.0.4
  package_info_plus: ^4.0.0
  path: ^1.8.2
  photo_view: ^0.14.0
  shared_preferences: ^2.2.0
  timezone: ^0.9.2
  uuid: ^4.2.1
  vsc_quill_delta_to_html: ^1.0.4
  csv: ^6.0.0
  file_picker: ^10.0.0
  universal_html: ^2.2.4
  just_audio: ^0.9.40
  video_player: ^2.9.2
  chewie: ^1.7.5

dev_dependencies:
  bloc_test: ^9.1.7
  build_runner: ^2.3.3
  flutter_gen_runner: ^5.1.0+1
  flutter_launcher_icons: ^0.13.1
  flutter_lints: ^3.0.1
  flutter_test:
    sdk: flutter
  dependency_validator: ^3.2.2

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/svg/
    - assets/png/

# Flutter Gen Configuration required to generate the file refrences of assets
flutter_gen:
  output: lib/generated/ # Optional (default: lib/gen/)
  line_length: 80 # Optional (default: 80)

  # Optional
  integrations:
    flutter_svg: true
    # can be set true if needed in the future (default: false)
  #    flare_flutter: true
  #    rive: true
  #    lottie: true
