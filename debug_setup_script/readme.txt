# Used for local firebase emulator setup and other things for local debugging

## Cautions:
1. First stash your changes before running command as the script might be old and might overwrite your changes
2. After it runs see the changes it made as those specific changes need to be reverted before pushing to prod.

## Setup:
1. Create a file `firebase_local.json` for firebase local emulator env variables with the following content and fill it:
   ```
   {
   "usingFirebaseEmulator": "true",
   "firebaseLocalHostAndroidIP": "Your-device-local-ip"
   }
   ```
   In run command use to pass the env vars `--dart-define-from-file=debug_setup_script/firebase_local.json.json`
2. Create a file `cloudflare_test_keys.txt` and store your cloudflare test keys to copy them in 'functions/src/services/cloudfare/cloudflare.data.ts' file.
3. Run below command to setup the firebase local debug environment. CAUTION: It will overwrite the BELOW mentioned above.
   DOES:
      1. Asks the user for 'firebaseLocalHostAndroidIP' and replaces it in 'app_constant.dart'.
      2. Comment content of file 'functions/src/services/cloudfare/cloudflare.data.ts' and replaces it with the content of 'debug_setup_script/cloudflare_test_keys.txt'.
      3. Replaces the storage rules in 'storage.primary.rules' with 'debug_setup_script/storage.primary.rules.txt'.
   Command:
   ```
   dart debug_setup_script/setup_debug.dart
   ```

# Setup staging build debugging:
1. Create a file `appcheck_debug_tokens_staging.json` in this location with the following content and fill it:
   ```
   {
   "androidAppCheckDebugToken": "ask-this-from-devs",
   "appleAppCheckDebugToken": "ask-this-from-devs"
   }
   ```
   In run command use to pass the env vars `--dart-define-from-file=debug_setup_script/appcheck_debug_tokens_staging.json`