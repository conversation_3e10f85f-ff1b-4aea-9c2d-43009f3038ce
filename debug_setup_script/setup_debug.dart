// ignore_for_file: avoid_print

import 'dart:io';

void main() {
  print('Running Debug Setup Script...\n');

  String backupDir = 'debug_setup_script/backup';
  Directory(backupDir).createSync(recursive: true);

  updateFirebaseLocalHostIP(backupDir);
  commentCloudflareData(
    directory: 'functions/src/services/cloudfare',
    filename: 'cloudflare.data.ts',
    backupDir: backupDir,
  );
  replaceFileWithContentFromFile(
    targetFilename: 'storage.primary.rules',
    sourceFilename: 'debug_setup_script/storage.primary.rules.txt',
    backupDir: backupDir,
  );

  print('\n✅ Debug setup completed!');
}

void updateFirebaseLocalHostIP(String backupDir) {
  if (getValidYesNoInput(
        'Do you want to update Firebase Local Host Android IP? (Y/N): ',
      ) ==
      'y') {
    stdout.write('Enter Firebase Local Host Android IP: ');
    String? userIp = stdin.readLineSync()?.trim();
    if (userIp != null && userIp.isNotEmpty) {
      print('Updating IP to: $userIp');
      updateFileContent(
        directory: 'lib',
        filename: 'app_constant.dart',
        searchPattern:
            r"static const String firebaseLocalHostAndroidIP\s*=\s*'.*?';",
        replaceWith:
            "static const String firebaseLocalHostAndroidIP = '$userIp';",
        backupDir: backupDir,
      );
    } else {
      print('No IP provided. Skipping update...');
    }
  } else {
    print('Skipping Firebase Local Host IP update...');
  }
}

String getValidYesNoInput(String question) {
  while (true) {
    stdout.write(question);
    String? input = stdin.readLineSync()?.trim().toLowerCase();
    if (input == 'y' || input == 'n') return input!;
    print('❌ Invalid input. Please enter "Y" or "N".\n');
  }
}

void updateFileContent({
  required String directory,
  required String filename,
  required String searchPattern,
  required String replaceWith,
  required String backupDir,
}) {
  File? targetFile = findFile(directory, filename);
  if (targetFile == null) {
    print('⚠️ ERROR: $filename not found in $directory');
    return;
  }

  backupFile(targetFile, backupDir);

  String content = targetFile.readAsStringSync();
  content = content.replaceAll(RegExp(searchPattern), replaceWith);

  targetFile.writeAsStringSync(content);
  print('✅ Updated $filename');
}

void commentCloudflareData({
  required String directory,
  required String filename,
  required String backupDir,
}) {
  File? targetFile = findFile(directory, filename);
  if (targetFile == null) {
    print('⚠️ $filename not found in $directory');
    return;
  }

  backupFile(targetFile, backupDir);

  List<String> lines =
      targetFile.readAsLinesSync().map((line) => '// $line').toList();
  File testKeysFile = File('debug_setup_script/cloudflare_test_keys.txt');
  if (testKeysFile.existsSync()) {
    lines.add('\n// Appended Test Keys:\n');
    lines.addAll(testKeysFile.readAsLinesSync());
  }

  targetFile.writeAsStringSync(lines.join('\n'));
  print('✅ Updated and appended test keys to $filename');
}

void replaceFileWithContentFromFile({
  required String targetFilename,
  required String sourceFilename,
  required String backupDir,
}) {
  File targetFile = File(targetFilename);
  File sourceFile = File(sourceFilename);

  if (!targetFile.existsSync() || !sourceFile.existsSync()) {
    print('⚠️ One of the files not found. Skipping replacement.');
    return;
  }

  backupFile(targetFile, backupDir);
  targetFile.writeAsStringSync(sourceFile.readAsStringSync());
  print('✅ Replaced content in $targetFilename');
}

File? findFile(String directory, String filename) {
  return Directory(directory)
      .listSync(recursive: true)
      .whereType<File>()
      .firstWhereOrNull(
        (file) => file.path.split(Platform.pathSeparator).last == filename,
      );
}

void backupFile(File file, String backupDir) {
  String backupPath = '$backupDir/${file.path.replaceAll('\\', '/')}';
  Directory(backupPath.substring(0, backupPath.lastIndexOf('/')))
      .createSync(recursive: true);
  file.copySync(backupPath);
  print('🛑 Backup created for ${file.path}');
}

extension FirstWhereOrNull<T> on Iterable<T> {
  T? firstWhereOrNull(bool Function(T) test) {
    for (var element in this) {
      if (test(element)) return element;
    }
    return null;
  }
}
