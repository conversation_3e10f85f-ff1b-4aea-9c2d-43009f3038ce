{"name": "functions", "version": "131.0.0", "scripts": {"lint": "eslint --ext .js,.ts .", "build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "debug": "tsc -w | firebase emulators:start --inspect-functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "20"}, "main": "lib/index.js", "dependencies": {"@aws-sdk/client-s3": "^3.712.0", "@aws-sdk/client-ses": "^3.712.0", "@aws-sdk/s3-request-presigner": "^3.712.0", "@google-cloud/bigquery": "^7.3.0", "@google-cloud/logging": "^11.2.0", "@google-cloud/pubsub": "^4.5.0", "@google-cloud/storage": "^6.11.0", "@google-cloud/translate": "^8.3.0", "@jrmdayn/googleapis-batcher": "^0.8.0", "@microsoft/microsoft-graph-client": "^3.0.7", "@sendgrid/mail": "^8.1.0", "@supercharge/promise-pool": "^3.2.0", "@types/fast-json-patch": "^1.1.7", "@types/node-fetch": "^2.6.4", "adm-zip": "^0.5.14", "archiver": "^7.0.1", "eciesjs": "^0.4.7", "eslint-config-prettier": "^9.1.0", "fast-json-patch": "^3.1.1", "firebase-admin": "^12.2.0", "firebase-functions": "^5.0.1", "get-audio-duration": "^4.0.1", "get-video-duration": "^4.1.0", "googleapis": "^144.0.0", "jszip": "^3.10.1", "mime-types": "^2.1.35", "mkdirp": "^2.1.3", "moment": "^2.30.1", "node-fetch": "^2.6.12", "rrule": "^2.8.1", "sharp": "^0.32.6", "stripe": "^14.8.0", "unzipper": "^0.12.3", "uuid": "^9.0.1", "zod": "^3.19.1"}, "devDependencies": {"@microsoft/microsoft-graph-types-beta": "^0.42.0-preview", "@types/adm-zip": "^0.5.5", "@types/archiver": "^6.0.2", "@types/mime-types": "^2.1.4", "@types/qrcode": "^1.5.5", "@types/sharp": "^0.31.0", "@types/unzipper": "^0.10.10", "@types/uuid": "^9.0.6", "@typescript-eslint/eslint-plugin": "^5.54.1", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.55.0", "eslint-config-google": "^0.14.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.25.4", "firebase-functions-test": "^3.3.0", "firebase-tools": "^13.13.0", "prettier": "^3.4.2", "typescript": "^4.9.5"}, "private": true}