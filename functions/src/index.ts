import * as admin from "firebase-admin";
import { getMeEnvironment } from "./me_config";
import { setGlobalOptions } from "firebase-functions/v2";
import { currentDbVersion } from "./functions/migration/versions/base/model_mappings";

const env = getMeEnvironment();

export const appCheckConfig = env == "prod" || env == "hotfix" ? true : false;

export const functionsRegion = "europe-west1";

setGlobalOptions({
  region: functionsRegion,
  enforceAppCheck: appCheckConfig,
  memory: "256MiB",
});

admin.initializeApp();

exports[`v${currentDbVersion}`] = require("./api_version");
exports.me = require("./api_non_version");

// const versionedFunctions: { [key: string]: string } = {
//   clientDataßð: "./functions/save_data/save_client_data",
//   user: "./functions/user/user",
//   triggers: "./functions/user_uploads/upload_events_handler",
//   login: "./functions/login/sign_in",
//   collaboration: "./functions/collabration/collaboration",
//   migration: "./functions/migration/migration_service",
//   supportapp: "./functions/support_app/support_app",
//   backup: "./functions/account_backup/data_backup",
//   dbstatus: "./utils/db_status/db_status_report",
//   translateapi: "./functions/translation/translation_methods",
//   calendar: "./functions/calendar/calendar_api",
// };

// const nonVersionedFunctions: { [key: string]: string } = {
//   encryption: "./utils/encryption/encryption",
//   appconfig: "./functions/appconfig/app_config_functions",
//   lifecycle: "./functions/user/lifecycle/lifecycle_events",
//   automations: "./functions/automation/automation_apis",
//   uploads: "./functions/user_uploads/file_upload_trigger",
//   calendar: "./functions/calendar/calendar_webhook",
//   invite: "./functions/collabration/invite",
//   purchase: "./functions/user/purchase/purchase_api",
//   login: "./functions/login/apple_sign_in",
//   storage: "./functions/account_backup/storage",
//   admin: "./functions/translation/app_translation_status",
// };

// for (const [baseName, path] of Object.entries(versionedFunctions)) {
//   const functionName = `v${currentDbVersion}-${baseName}`;
//   exports[functionName] = require(path);
// }

// for (const [baseName, path] of Object.entries(nonVersionedFunctions)) {
//   const functionName = `me-${baseName}`;
//   exports[functionName] = require(path);
// }
