import { projectID } from "firebase-functions/params";
// export const firebaseProjectId : string = projectID.value();

export default {
  secondaryBucket: process.env.FUNCTIONS_EMULATOR
    ? "mevolve-dev.appspot.com"
    : "mevolve-app-uploads-" + getMeEnvironment(),
};

export function getMeEnvironment() {
  const env = projectID.value();
  if (
    process.env.FUNCTIONS_EMULATOR ||
    env == undefined ||
    env == "mevolve-dev"
  ) {
    return "dev";
  } else if (env == "mevolve-qa") {
    return "qa";
  } else if (env == "mevolve-staging") {
    return "staging";
  } else if (env == "mevolve-prod") {
    return "prod";
  } else if (env == "mevolve-hotfix") {
    return "hotfix";
  } else {
    return "dev";
  }
}
