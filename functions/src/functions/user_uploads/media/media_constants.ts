export type FileType = "image" | "video" | "audio" | "document";

export class AttachmentConstants {
  static readonly supportedImageTypes: string[] = [
    "jpg",
    "jpeg",
    "png",
    "webp",
  ];

  static readonly supportedVideoTypes: string[] = [
    "mp4",
    "mov",
    "avi",
    "flv",
    "wmv",
    "mkv",
  ];

  static readonly supportedAudioTypes: string[] = ["mp3", "wav", "m4a"];

  static readonly supportedDocumentTypes: string[] = [
    "pdf",
    "doc",
    "docx",
    "xls",
    "xlsx",
    "ppt",
    "pptx",
    "txt",
    "gif",
  ];

  static readonly maxImageSize: number = 10 * 1024 * 1024;
  static readonly maxVideoSize: number = 100 * 1024 * 1024;
  static readonly maxAudioSize: number = 10 * 1024 * 1024;
  static readonly maxDocumentSize: number = 10 * 1024 * 1024;

  static readonly maxImageSizeMb: number = 10;
  static readonly maxVideoSizeMb: number = 100;
  static readonly maxAudioSizeMb: number = 10;
  static readonly maxDocumentSizeMb: number = 10;

  static getMaxFileSize(fileType: FileType): number {
    switch (fileType) {
      case "image":
        return this.maxImageSize;
      case "video":
        return this.maxVideoSize;
      case "audio":
        return this.maxAudioSize;
      case "document":
        return this.maxDocumentSize;
      default:
        return 0;
    }
  }
  static readonly userDataPath: string = "userData/attachments";
  static readonly supportedUserDataDocType: string[] = [
    "todos",
    "habitActions",
    "journalActions",
    "notes",
    "moneyTrackerTransactions",
  ];
}
