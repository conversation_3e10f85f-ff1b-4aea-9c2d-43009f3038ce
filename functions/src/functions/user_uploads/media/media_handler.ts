import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v2";
import { mkdirp } from "mkdirp";
import sharp = require("sharp");
import * as path from "path";
import * as os from "os";
import * as fs from "fs";
import { AttachmentConstants, FileType } from "./media_constants";
import getVideoDurationInSeconds from "get-video-duration";
import getAudioDurationInSeconds from "get-audio-duration";
import { INTERNAL_SERVER_ERROR } from "../../../utils/constants";
import {
  IDocCollectionName,
  IBaseSchema,
} from "../../migration/versions/base/base_schema";
import {
  decryptDocKey,
  encryptFileByChunk,
  encryptFile,
} from "../../../utils/encryption/encryption";
import { getFileSize, getImageMetaData } from "../../../utils/utility_methods";
import {
  ImageMetadata,
  AudioVideoMetadata,
} from "../../migration/versions/base/attachments_model";
import { DbRepository } from "../../save_data/db.repository";

sharp.cache(false);

export async function processMediaFile(
  filePath: string,
  bucketId: string,
  size: number,
  docCollectionName: IDocCollectionName
) {
  functions.logger.log("Checking file for conversion: " + filePath);

  const parsedPath = path.parse(filePath);
  const filePathArray = filePath.split("/");

  const bucketName = filePathArray[1];
  const userId = filePathArray[2];
  const docType = filePathArray[3];
  const docId = filePathArray[4];
  const basePath =
    bucketName == "supportAppMedia"
      ? "supportApp/media"
      : AttachmentConstants.userDataPath;

  const bucket = admin.storage().bucket(bucketId);
  const uploadFileTye = parsedPath.ext.replace(".", "");
  const isVideoAudioDocument: boolean =
    AttachmentConstants.supportedAudioTypes.includes(uploadFileTye) ||
    AttachmentConstants.supportedVideoTypes.includes(uploadFileTye) ||
    AttachmentConstants.supportedDocumentTypes.includes(uploadFileTye);
  if (parsedPath.ext == ".txt" && bucketName == "supportAppMedia") {
    try {
      const fileId = parsedPath.name;
      // Move file from bucket.
      const primaryBucket = admin
        .storage()
        .bucket()
        .file(`${basePath}/originalFiles/${userId}/${docId}/${fileId}.txt`);
      functions.logger.log("Moving file from secondary to primary storage");
      const remoteOriginalFile = bucket.file(filePath);
      await remoteOriginalFile.move(primaryBucket);
      const originalFileSize = Math.round(size / 1024);
      await updateFirestore(
        docId,
        docType,
        Number(fileId),
        originalFileSize,
        null,
        null,
        "txt"
      );
      functions.logger.log("Deleting original uploaded file from storage");
      await remoteOriginalFile.delete();
      functions.logger.log("Deleted original uploaded file from storage");
    } catch (error) {
      console.log(error);
    }
  } else if (isVideoAudioDocument) {
    console.log("inside audio, video, document");
    functions.logger.log("function triggered. fileName " + parsedPath.name);
    let localOriginalFile;
    let remoteOriginalFile;
    try {
      localOriginalFile = path.join(os.tmpdir(), "temp", parsedPath.name);
      functions.logger.log("temp local original file " + localOriginalFile);
      const tempLocalDir = path.dirname(localOriginalFile);
      functions.logger.log("temp dir created " + tempLocalDir);

      // Create the temp directory where the
      // storage file will be downloaded.
      await mkdirp(tempLocalDir);

      // Download file from bucket.
      functions.logger.log("Downloading original file: " + filePath);
      remoteOriginalFile = bucket.file(filePath);
      await remoteOriginalFile.download({ destination: localOriginalFile });
      functions.logger.log("Downloaded original file: " + filePath);

      const { name: fileId } = parsedPath;
      // upload files to storage
      functions.logger.log("Uploading file to storage");
      // Remove temp from file path
      filePathArray.splice(0, 1);
      // Remove original file name to replace it with new names
      filePathArray.pop();
      // encrypt files before uploading
      const db = admin.firestore();
      const doc = await db.collection(docType).doc(docId).get();
      const docData = doc.data() as IBaseSchema;
      const docSecret = await decryptDocKey(docData.encData.dek, userId);
      if (docSecret == null) {
        throw new functions.https.HttpsError(
          INTERNAL_SERVER_ERROR,
          "Error in decrypting doc key for user: " + userId
        );
      }
      const fileExtension: string = parsedPath.ext;
      const fileType: FileType =
        AttachmentConstants.supportedAudioTypes.includes(uploadFileTye)
          ? "audio"
          : AttachmentConstants.supportedVideoTypes.includes(uploadFileTye)
            ? "video"
            : "document";

      // encrypt only user files
      if (basePath == AttachmentConstants.userDataPath) {
        const originalEncrypted = path.join(tempLocalDir, "originalEnc");
        await encryptFileByChunk(
          localOriginalFile,
          originalEncrypted,
          docSecret
        );
        await Promise.all([
          uploadFile(
            originalEncrypted,
            `${basePath}/${userId}/${docCollectionName}/originalFiles/${fileId}${fileExtension}`,
            fileType == "audio"
              ? "audio/mpeg"
              : fileType == "video"
                ? "video/mp4"
                : "multipart/form-data"
          ),
        ]);
      } else {
        await Promise.all([
          uploadFile(
            localOriginalFile,
            `${basePath}/${userId}/${docCollectionName}/originalFiles/${fileId}${fileExtension}`,
            fileType == "audio"
              ? "audio/mpeg"
              : fileType == "video"
                ? "video/mp4"
                : "multipart/form-data"
          ),
        ]);
      }

      const originalFileSize = getFileSize(localOriginalFile);
      const duration =
        fileType == "video"
          ? await getVideoDurationInSeconds(localOriginalFile)
          : fileType == "audio"
            ? await getAudioDurationInSeconds(localOriginalFile)
            : 0;

      // Update firestore
      await updateFirestore(
        docId,
        docType,
        Number(fileId),
        originalFileSize,
        fileType == "audio" || fileType == "video"
          ? { duration: duration }
          : null,
        uploadFileTye,
        fileType == "audio"
          ? "audio"
          : fileType == "video"
            ? "video"
            : "document"
      );
    } catch (error) {
      console.log(error);
    } finally {
      try {
        if (remoteOriginalFile) {
          functions.logger.log("Deleting original uploaded file from storage");
          await remoteOriginalFile.delete();
          functions.logger.log("Deleted original uploaded file from storage");
        }
      } catch (error) {
        console.error("Error in deleting original uploaded file from storage");
      }
      if (localOriginalFile && fs.existsSync(localOriginalFile)) {
        try {
          fs.unlinkSync(localOriginalFile);
        } catch (err) {
          functions.logger.error(
            "Error in deleting local original file: " + localOriginalFile
          );
        }
      }
    }
  } else {
    functions.logger.log("function triggered. fileName " + parsedPath.name);

    let localOriginalFile;
    let remoteOriginalFile;
    let localOriginalJpegFile;
    let optimizedImageFile;
    let thumbnailImageFile;
    try {
      localOriginalFile = path.join(os.tmpdir(), "temp", parsedPath.name);
      functions.logger.log("temp local original file " + localOriginalFile);
      const tempLocalDir = path.dirname(localOriginalFile);
      functions.logger.log("temp dir created " + tempLocalDir);

      // Create the temp directory where the
      // storage file will be downloaded.
      await mkdirp(tempLocalDir);

      // Download file from bucket.
      functions.logger.log("Downloading original image: " + filePath);
      remoteOriginalFile = bucket.file(filePath);
      await remoteOriginalFile.download({ destination: localOriginalFile });
      functions.logger.log("Downloaded original image: " + filePath);

      //  convert to jpeg
      const { name: fileId } = parsedPath;
      const localOriginalFileName = `${fileId}.jpeg`;
      localOriginalJpegFile = path.join(tempLocalDir, localOriginalFileName);
      await convertToJpeg(localOriginalFile, localOriginalJpegFile);

      //  create optimized Image
      optimizedImageFile = await createOptimizedFile(
        fileId,
        tempLocalDir,
        localOriginalJpegFile
      );

      //  create thumbnail Image
      thumbnailImageFile = await createThumbnailFile(
        fileId,
        tempLocalDir,
        localOriginalJpegFile
      );

      // upload images to storage
      functions.logger.log("Uploading images to storage");

      // Remove temp from file path
      filePathArray.splice(0, 1);
      // Remove original image name to replace it with new names
      filePathArray.pop();



      // encrypt only user files
      if (basePath == AttachmentConstants.userDataPath) {
        // encrypt files before uploading
        const db = admin.firestore();
        const doc = await db.collection(docType).doc(docId).get();
        const docData = doc.data() as IBaseSchema;
        const docSecret = await decryptDocKey(docData.encData.dek, userId);
        if (docSecret == null) {
          throw new functions.https.HttpsError(
            INTERNAL_SERVER_ERROR,
            "Error in decrypting doc key for user: " + userId
          );
        }

        const originalEncrypted = path.join(tempLocalDir, "originalEnc");
        const optimizedEncrypted = path.join(tempLocalDir, "optimizedEnc");
        const thumbnailEncrypted = path.join(tempLocalDir, "thumbnailEnc");
        await encryptFile(localOriginalJpegFile, originalEncrypted, docSecret);
        await encryptFile(optimizedImageFile, optimizedEncrypted, docSecret);
        await encryptFile(thumbnailImageFile, thumbnailEncrypted, docSecret);
        await Promise.all([
          uploadFile(
            originalEncrypted,
            `${basePath}/${userId}/${docCollectionName}/originalFiles/${fileId}.jpeg`
          ),
          uploadFile(
            optimizedEncrypted,
            `${basePath}/${userId}/${docCollectionName}/optimizedFiles/${fileId}.jpeg`
          ),
          uploadFile(
            thumbnailEncrypted,
            `${basePath}/${userId}/${docCollectionName}/thumbnails/${fileId}.jpeg`
          ),
        ]);
      } else {
        await Promise.all([
          uploadFile(
            localOriginalJpegFile,
            `${basePath}/${userId}/${docCollectionName}/${docId}/originalFiles/${fileId}.jpeg`
          ),
          uploadFile(
            optimizedImageFile,
            `${basePath}/${userId}/${docCollectionName}/${docId}/optimizedFiles/${fileId}.jpeg`
          ),
          uploadFile(
            thumbnailImageFile,
            `${basePath}/${userId}/${docCollectionName}/${docId}/thumbnails/${fileId}.jpeg`
          ),
        ]);
      }

      // Get image meta data
      const originalImageMetaData = await getImageMetaData(
        localOriginalJpegFile
      );
      const originalImageFileSize = await getFileSize(localOriginalJpegFile);
      const optimizedImageFileSize = await getFileSize(optimizedImageFile);
      const thumbnailImageFileSize = await getFileSize(thumbnailImageFile);
      const totalImageSize =
        originalImageFileSize + optimizedImageFileSize + thumbnailImageFileSize;
      const imageMetadata: ImageMetadata = {
        height: originalImageMetaData.height ?? 0,
        width: originalImageMetaData.width ?? 0,
      };

      // Update firestore
      await updateFirestore(
        docId,
        docType,
        Number(fileId),
        totalImageSize,
        imageMetadata,
        "jpeg",
        "image"
      );
    } catch (error) {
      console.log(error);
    } finally {
      try {
        if (remoteOriginalFile) {
          functions.logger.log("Deleting original uploaded file from storage");
          await remoteOriginalFile.delete();
          functions.logger.log("Deleted original uploaded file from storage");
        }
      } catch (error) {
        console.error("Error in deleting original uploaded file from storage");
      }
      if (localOriginalFile && fs.existsSync(localOriginalFile)) {
        try {
          fs.unlinkSync(localOriginalFile);
        } catch (err) {
          functions.logger.error(
            "Error in deleting local original file: " + localOriginalFile
          );
        }
      }
      if (localOriginalJpegFile) {
        try {
          fs.unlinkSync(localOriginalJpegFile);
        } catch (err) {
          functions.logger.error(
            "Error in deleting local original jpeg: " + localOriginalJpegFile
          );
        }
      }
      if (optimizedImageFile) {
        try {
          fs.unlinkSync(optimizedImageFile);
        } catch (err) {
          functions.logger.error(
            "Error in deleting local opetimized file: " + optimizedImageFile
          );
        }
      }
      if (thumbnailImageFile) {
        try {
          fs.unlinkSync(thumbnailImageFile);
        } catch (err) {
          functions.logger.error(
            "Error in deleting local thumbnail: " + thumbnailImageFile
          );
        }
      }
    }
  }
}

async function uploadFile(
  sourceFilePath: string,
  destinationFilePath: string,
  contentType?: string
) {
  console.log(`Uploading - ${destinationFilePath}`);
  const bucket = admin.storage().bucket();
  const metaData = { contentType: contentType ?? "image/jpeg" };
  console.log("metaData is ", metaData);
  await bucket.upload(sourceFilePath, {
    destination: destinationFilePath,
    metadata: metaData,
  });
}

async function updateFirestore(
  docId: string,
  docCollectionName: string,
  fileId: number,
  fileSize: number,
  metadata: ImageMetadata | AudioVideoMetadata | null,
  format: string | null,
  fileType: string
) {
  console.log(`Updating firestore with new image path 
    ${docCollectionName}, ${docId}`);
  await new DbRepository().updateAttachmentData(
    docId,
    docCollectionName,
    fileId,
    fileSize,
    metadata,
    format,
    fileType
  );
  console.log("Updated firestore with new attachment path");
}

async function convertToJpeg(
  localOriginalFile: string,
  localOriginalJpegFile: string
) {
  functions.logger.log("Converting original image to jpeg");
  await sharp(localOriginalFile)
    .toFormat("jpeg")
    .jpeg({
      force: false,
    })
    .toFile(localOriginalJpegFile);
  functions.logger.log("converted original image to jpeg");
}

async function createOptimizedFile(
  fileId: string,
  tempLocalDir: string,
  localOriginalJpegFile: string
): Promise<string> {
  const optimizedImageFileName = `op_${fileId}.jpeg`;
  const optimizedImageFile = path.join(tempLocalDir, optimizedImageFileName);
  functions.logger.log("Converting original image to optimized image");
  await sharp(localOriginalJpegFile)
    .resize(1644, 3840, { fit: "inside" })
    .toFile(optimizedImageFile);
  functions.logger.log("Converted original image to optimized image");
  return optimizedImageFile;
}

async function createThumbnailFile(
  fileId: string,
  tempLocalDir: string,
  localOriginalJpegFile: string
): Promise<string> {
  const thumbnailFileName = `thumb_${fileId}.jpeg`;
  functions.logger.log("Converting original image to thumbnail image");
  const thumbnailImageFile = path.join(tempLocalDir, thumbnailFileName);
  await sharp(localOriginalJpegFile)
    .resize(128, 128)
    .toFile(thumbnailImageFile);
  functions.logger.log("Converted original image to thumbnail image");
  return thumbnailImageFile;
}
