import * as functions from "firebase-functions/v2";
import meConfig, { getMeEnvironment } from "../../me_config";
import { INTERNAL_SERVER_ERROR } from "../../utils/constants";
import {
  getUserDbVersion,
  publishMessageToPubSub,
} from "../../utils/utility_methods";

import * as admin from "firebase-admin";
import { functionsRegion } from "../..";
import {
  deleteFileFromR2,
  uploadFileToR2,
} from "../../services/cloudfare/r2/r2_upload_helper";

export const userUploadsHandler = functions.storage.onObjectFinalized(
  {
    bucket: meConfig.secondaryBucket,
  },
  async (event) => {
    const data = event.data;
    functions.logger.log("Checking file for conversion: " + data.name);
    const filePath = data.name;
    if (!filePath) {
      throw new functions.https.HttpsError(
        INTERNAL_SERVER_ERROR,
        "Filename can't be undefined."
      );
    }
    const filePathArray = filePath.split("/");
    if (filePathArray[0] != "temp") {
      console.log("file already converted");
      return;
    }
    let mediaType = filePathArray[1];
    if (mediaType == "supportAppMedia") {
      mediaType = "media";
    }
    const userId = filePathArray[2];
    const dbVersion = await getUserDbVersion(userId);
    console.log(`${filePathArray[1]}_v${dbVersion}`);
    if (dbVersion > 0) {
      return publishMessageToPubSub(`${mediaType}_v${dbVersion}`, {
        filePath: filePath,
        bucket: data.bucket,
        size: data.size,
      });
    }
    return;
  }
);

export const publicAttachmentCloudflareDeleteHandler =
  functions.storage.onObjectDeleted(
    {
      region: getMeEnvironment() === "dev" ? "us-central1" : functionsRegion,
      memory: "1GiB",
      timeoutSeconds: 540,
    },
    async (event) => {
      console.log("Trigger for public attachment delete got called");

      const data = event.data;
      const filePath = data.name;
      console.log("File path:", filePath);

      const isUserDataAttachment = filePath.startsWith("userData/attachments/");
      if (!isUserDataAttachment) {
        console.log("Not an attachment for user tasks, skipping.");
        return;
      }

      console.log("Checking if file still exists...");
      const bucket = admin.storage().bucket();
      const file = bucket.file(filePath);
      const [exists] = await file.exists();

      if (exists) {
        // File exists, meaning it was overwritten. No need to delete from R2.
        return;
      }

      // If file does not exist, proceed with R2 deletion assuming the document was public.
      console.log(
        "File does not exist and document was public. Deleting from R2."
      );
      deleteFileFromR2(filePath);
    }
  );

export const publicAttachmentCloudflareHandler =
  functions.storage.onObjectFinalized(
    {
      region: getMeEnvironment() === "dev" ? "us-central1" : functionsRegion,
      memory: "1GiB",
      timeoutSeconds: 540,
    },
    async (event) => {
      const data = event.data;
      const filePath = data.name;

      const isUserDataAttachment = filePath.startsWith("userData/attachments/");
      if (!isUserDataAttachment) {
        console.log("Not an attachment for user tasks, skipping", filePath);
        return;
      }
      console.log("Data for attachment", data);

      const filePathArray = filePath.split("/");
      const uid = filePathArray[2];
      const collection = filePathArray[3];
      const docId = filePathArray[4];

      // Get the user db version
      const dbVersion = await getUserDbVersion(uid);

      if (dbVersion > 128) {
        // Skipping as on higher DB version R2 uploads happen from Client side.
        console.log(
          "Skipping publicAttachmentCloudflareHandler for user with db version > 128 as not needed"
        );
        return;
      }

      // Todo: ideally in future we should have a robust way to tell if the attachment is for a public doc or not.
      // Rightnow, we add the delay because sometimes the doc may take few seconds to sync from user device to cloud firestore after made public
      // but the attachment might already be reach here so without the delay they will get missed.
      console.log("Waiting 5 seconds to verify if the document is public...");
      await new Promise((resolve) => setTimeout(resolve, 5000));

      let isPublic = false;
      const doc = await admin
        .firestore()
        .collection(collection)
        .doc(docId)
        .get();

      if (doc.exists) {
        const data = doc.data();
        if (data && data["isPublic"]) {
          isPublic = true;
        }
      }

      if (!isPublic) {
        console.log(
          "Not a Public Attachment, removing from R2 if exists at path",
          filePath
        );
        deleteFileFromR2(filePath);
        return;
      } else {
        console.log("Started Uploading public file to R2:");
        // upload the file to cloudflare
        const bucket = admin.storage().bucket(data.bucket);
        const file = bucket.file(data.name);
        const [fileContent] = await file.download();
        const fileBuffer = fileContent as Buffer;
        console.log("UPLOADING PUBLIC FILES TO R2:", data.name);

        await uploadFileToR2(filePath, fileBuffer);
        return;
      }
    }
  );
