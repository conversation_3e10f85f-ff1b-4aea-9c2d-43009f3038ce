/* eslint-disable require-jsdoc */
import * as functions from "firebase-functions/v2";
import { processMediaFile } from "./media/media_handler";
import { onMessagePublished } from "firebase-functions/v2/pubsub";
import { restoreDataFromExportedFile } from "../account_backup/restore_data/import_user_data_from_zip";
import { currentDbVersion } from "../migration/versions/base/model_mappings";

exports.mediaEventHandler = onMessagePublished(
  {
    topic: `media_v${currentDbVersion}`,
    memory: "1GiB",
    concurrency: 1,
  },
  async (event) => {
    functions.logger.info("Starting image conversion", event);
    const data = event.data.message.json;
    return processMediaFile(
      data.filePath,
      data.bucket,
      data.size,
      data.filePath.split("/")[3]
    );
  }
);

exports.backupFileEventHandler = onMessagePublished(
  {
    topic: `backupFile_v${currentDbVersion}`,
    memory: "4GiB",
    concurrency: 1,
    timeoutSeconds: 540,
  },
  async (event) => {
    functions.logger.info("Starting restoring user data", event);
    const data = event.data.message.json;
    return restoreDataFromExportedFile(data.filePath, data.bucket);
  }
);
