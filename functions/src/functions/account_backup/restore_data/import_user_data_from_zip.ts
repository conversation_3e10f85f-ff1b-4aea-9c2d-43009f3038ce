import * as admin from "firebase-admin";
import * as path from "path";
import * as os from "os";
import { mkdirp } from "mkdirp";
import * as fs from "fs";
import PromisePool from "@supercharge/promise-pool";
import AdmZip from "adm-zip";
import { v4 as uuidv4 } from "uuid";
import * as crypto from "crypto";
import {
  AttachmentFileInfo,
  getAttachmentDetails,
} from "../user_data_backup/export_user_data";
import { DataMigrationService } from "../../migration/migration_service";
import { IBaseSchema } from "../../migration/versions/base/base_schema";
import {
  getMeParsedData,
  IUser,
  IUserResources,
  IViewSettings,
} from "../../migration/versions/base/model_mappings";
import {
  AttachmentArrayType,
  DbRepository,
  isDocHasAttachments,
} from "../../save_data/db.repository";
import {
  encryptFile,
  getNewSecret,
  encryptDocKey,
  iterateMap,
  getPublicKeyOfUserById,
  decryptTextData,
  getSecretFromKmsForUserByEmail,
  decryptDocKey,
} from "../../../utils/encryption/encryption";
import {
  convertDatesToTimestamps,
  getCurrentUTCDate,
  getEmailNameByIdFromAuthService,
  getOptimizedFilePath,
  getOriginalFilePath,
  getSupportLink,
  getThumbnailFilePath,
  getViewSettingsByUserId,
} from "../../../utils/utility_methods";
import { calculateStorageForUserById } from "../storage";
import { sendSingleEmailViaSes } from "../../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../../utils/emails/email_helper";
import { logger } from "firebase-functions/v2";

export async function restoreDataFromExportedFile(
  filePath: string,
  bucketId: string
) {
  const TIMEOUT_MS = 535000; // 8 minutes 55 seconds

  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(
        new Error(
          `Execution nearing ${TIMEOUT_MS}ms so safely aborting process`
        )
      );
    }, TIMEOUT_MS);
  });

  const bucket = admin.storage().bucket(bucketId);
  const remoteFile = bucket.file(filePath);
  const filePathArray = filePath.split("/");
  const currentUserId = filePathArray[2];

  try {
    // Wrap the main logic in a promise
    const mainTask = (async () => {
      // const userSnapshot = await admin
      //   .firestore()
      //   .collection("users")
      //   .doc(currentUserId)
      //   .get();
      // const user = userSnapshot.data() as IUser;

      await updateProgress(currentUserId, "inProgress");

      const userDetails = await getEmailNameByIdFromAuthService(currentUserId);
      if (userDetails?.email == null) {
        throw new Error("User email not found");
      }

      // download file from firebase storage using filepath
      const parsedPath = path.parse(filePath);
      const backupZipFile = path.join(
        os.tmpdir(),
        currentUserId,
        parsedPath.name
      );
      const tempLocalDir = path.dirname(backupZipFile);
      await mkdirp(tempLocalDir);

      console.log("Downloading file from firebase storage");
      await remoteFile.download({ destination: backupZipFile });
      const [metadata] = await remoteFile.getMetadata();
      const userEncPvtKey = metadata.metadata?.userEncPvtKey;

      if (userEncPvtKey == null) {
        console.log("User private key not found");
      }

      const userSecret = (
        await getSecretFromKmsForUserByEmail(userDetails.email)
      ).key;
      const userPvtKey = decryptTextData(userEncPvtKey ?? "", userSecret);

      console.log("Downloading file completed");

      await extractZipFile(
        backupZipFile,
        path.join(os.tmpdir(), currentUserId)
      );
      console.log("Extraction of file completed");

      // delete zip file
      fs.unlinkSync(backupZipFile);

      const userPublicKey = await getPublicKeyOfUserById(currentUserId);
      const jsonFile = await fs.readFileSync(
        path.join(os.tmpdir(), currentUserId, "data.json"),
        "utf8"
      );
      let allUserData = JSON.parse(jsonFile) as IBaseSchema[];
      const importedUserId = allUserData[0].uid;

      const results = await changeDocIdsIfRequired(
        allUserData,
        importedUserId,
        currentUserId !== importedUserId,
        userDetails
      );
      allUserData = results.allUserData;

      console.log("Saving media files");
      await uploadAllMediaFilesToFirebaseStorage(
        allUserData,
        currentUserId,
        importedUserId,
        results.changedIdsMap
      );
      console.log("Saving media files completed");

      console.log("Saving firestore data");
      await saveDataToFirestore(
        allUserData,
        currentUserId,
        userPvtKey,
        userPublicKey
      );
      console.log("Firestore data saved successfully");

      await calculateStorageForUserById(currentUserId);

      if (userDetails?.email != null) {
        const viewSettings = await getViewSettingsByUserId(currentUserId);
        const language = viewSettings?.appSettings.language;
        await sendSingleEmailViaSes({
          templateName: MevolveEmails.dataImportSuccessfull,
          recipient: userDetails.email,
          templateData: {
            name: userDetails.name ?? "",
            linkToSupportChat: getSupportLink(),
          },
          language: language,
        });
      }
    })();

    // Wait for either the main task or the timeout
    await Promise.race([mainTask, timeoutPromise]);
  } catch (e) {
    console.log("Error in restoring data: " + e);

    const userDetails = await getEmailNameByIdFromAuthService(currentUserId);
    if (userDetails?.email != null) {
      const viewSettings = await getViewSettingsByUserId(currentUserId);
      const language = viewSettings?.appSettings.language;
      await sendSingleEmailViaSes({
        templateName: MevolveEmails.dataImportFailed,
        recipient: userDetails.email,
        templateData: {
          name: userDetails.name ?? "",
          linkToSupportChat: getSupportLink(),
        },
        language: language,
      });
    }
  } finally {
    console.log("Resetting flag");
    await updateProgress(currentUserId, "success");

    try {
      const tempDirPath = path.join(os.tmpdir(), currentUserId);
      if (fs.existsSync(tempDirPath)) {
        fs.rmSync(tempDirPath, { recursive: true });
      }
    } catch (error) {
      console.error("Error in deleting temp files: " + error);
    }

    await remoteFile.delete();
  }

  return;
}

function extractZipFile(filePath: string, folderPath: string) {
  const zip = new AdmZip(filePath);
  zip.extractAllTo(folderPath, true);
}

async function changeDocIdsIfRequired(
  allUserData: IBaseSchema[],
  importedUserId: string,
  changeOfIdsRequired: boolean,
  userInWhichDataIsImported: {
    email: string | undefined;
    name: string | undefined;
  }
) {
  const docs = allUserData.filter(
    (item) =>
      item.docCollection != "users" &&
      item.docCollection != "usersMetadata" &&
      item.docCollection != "viewSettings" &&
      item.docCollection != "userResources"
  );

  for (const doc of docs) {
    doc.encData.dek = await getNewSecret();
  }

  // Remove docs in allUserData which are not of the imported user.
  allUserData = allUserData.filter((item) => item.uid == importedUserId);

  // Remove some collection docs from the data as we are not importing them.
  allUserData = allUserData.filter(
    (item) =>
      item.docCollection != "chatMessages" &&
      item.docCollection != "inAppNotifications" &&
      item.docCollection != "chatUsers" &&
      item.docCollection != "issueReports" &&
      item.docCollection != "publicUsers"
  );

  // Remove members field and update IDs if needed
  const docIdsWhoseMembersFieldNeedsToBeRemoved: Set<string> = new Set();

  // Remove inviteLink, isPublic, and publicId fields from docs.
  // This will remove the public sharing info from tasks.
  // Sharing data in imported data can cause conflicts so all shareing info is removed from such data and these tasks are imported with new ids
  allUserData.forEach((doc) => {
    let docIdShouldBeChanged = false;

    if ("inviteLink" in doc && doc.inviteLink != null) {
      docIdShouldBeChanged = true;
      delete doc.inviteLink;
    }
    if ("isPublic" in doc && doc.isPublic) {
      docIdShouldBeChanged = true;
      delete doc.isPublic;
    }
    if ("publicId" in doc && doc.publicId != null) {
      docIdShouldBeChanged = true;
      delete doc.publicId;
    }

    if (
      "members" in doc &&
      doc.members != null &&
      typeof doc.members === "object"
    ) {
      // Ensure members contains memberHashedEmails and it's not empty
      if (
        "memberHashedEmails" in doc.members &&
        Array.isArray(doc.members.memberHashedEmails) &&
        doc.members.memberHashedEmails.length > 0
      ) {
        delete doc.members;
        docIdShouldBeChanged = true;
      }
    }

    // If field ownerName and ownerEmail is present then update it with the userInWhichDataIsImported name and email
    if ("ownerEmail" in doc) {
      doc.ownerEmail = userInWhichDataIsImported.email;
    }
    if ("ownerName" in doc) {
      doc.ownerName = userInWhichDataIsImported.name;
    }

    if (docIdShouldBeChanged) {
      const parsedDoc = getMeParsedData(doc);
      // Replace the doc in allUserData with parsedDoc
      allUserData = allUserData.map((item) => {
        if (item.id == doc.id) {
          return parsedDoc;
        }
        return item;
      });
      docIdsWhoseMembersFieldNeedsToBeRemoved.add(doc.id);
    }
  });

  let changedIdsMap: Map<string, string> = new Map();

  if (changeOfIdsRequired || docIdsWhoseMembersFieldNeedsToBeRemoved.size > 0) {
    if (changeOfIdsRequired) {
      console.log(
        "Changing IDs of all documents as imported data is not of user"
      );
      const result = changIdsOfAllDocs(allUserData);
      allUserData = result.changedDocs;
      changedIdsMap = result.changedIdsMap;
    } else {
      console.log(
        "Imported data owner is same as the user. Changing ids of docs which had sharing info."
      );
      // Collect all the docs in docIdsWhoseMembersFieldNeedsToBeRemoved. Iterate docIdsWhoseMembersFieldNeedsToBeRemoved
      // and get from allUserData.
      const docsToChangeIds = allUserData.filter((item) =>
        docIdsWhoseMembersFieldNeedsToBeRemoved.has(item.id)
      );
      const result = changIdsOfAllDocs(docsToChangeIds);
      const changedIdDocs = result.changedDocs;
      changedIdsMap = result.changedIdsMap;
      // Replace the docs in allUserData with changedIdDocs using changedIdsMap
      allUserData = allUserData.map((item) => {
        if (docIdsWhoseMembersFieldNeedsToBeRemoved.has(item.id)) {
          // Iterate over changedIdsMap and find if the item.id is a value
          for (const [key, value] of changedIdsMap.entries()) {
            if (value == item.id) {
              const newDoc = changedIdDocs.find((doc) => doc.id == key);
              return newDoc || item;
            }
          }
        }
        return item;
      });
    }
  }

  console.log("Total documents to save: " + allUserData.length);
  return { allUserData, changedIdsMap };
}

async function saveDataToFirestore(
  allUserData: IBaseSchema[],
  userId: string,
  userPrivateKey: string,
  userPublicKey: string
) {
  // encrypt data
  await encryptData(
    allUserData.filter(
      (item) =>
        item.docCollection != "users" &&
        item.docCollection != "usersMetadata" &&
        item.docCollection != "viewSettings" &&
        item.docCollection != "userResources"
    ),
    userId,
    userPublicKey
  );

  await Promise.all([
    saveUserDocument(
      allUserData.filter((item) => item.docCollection == "users")[0],
      userId
    ),
    saveViewSettings(
      allUserData.filter((item) => item.docCollection == "viewSettings")[0],
      userId,
      userPrivateKey,
      userPublicKey
    ),
    saveUserResource(
      allUserData.filter((item) => item.docCollection == "userResources")[0],
      userId,
      userPrivateKey,
      userPublicKey
    ),
  ]);

  logger.info("User, ViewSettings and UserResource saved successfully");

  const helperData: Map<string, string> = new Map();
  const result = await PromisePool.withConcurrency(200)
    .for(
      allUserData.filter(
        (item) =>
          item.docCollection != "users" &&
          item.docCollection != "usersMetadata" &&
          item.docCollection != "viewSettings" &&
          item.docCollection != "userResources"
      )
    )
    .process(async (document, _index, _pool) => {
      console.log(
        "saving document - " + document.docCollection + "/" + document.id
      );
      try {
        // If the document is having attachments, then remove the local attachments.
        if (isDocHasAttachments(document)) {
          const item = document as AttachmentArrayType;

          // Filter out "local" attachments
          item.attachments = item.attachments.filter(
            (att: any) => att.status !== "local"
          );
        }

        const finalData = await new DataMigrationService().migrateSchema(
          document,
          helperData
        );
        finalData.cloudUpdatedAt = null;
        finalData.localUpdatedAt = getCurrentUTCDate();
        const parsedDoc = convertDatesToTimestamps(getMeParsedData(finalData));

        parsedDoc.uid = userId;
        if (parsedDoc.docCollection == "users") {
          parsedDoc.id = userId;
        }

        await new DbRepository().updateData(parsedDoc);
        console.log(
          "Document saved: " + document.docCollection + " - " + document.id
        );
      } catch (e) {
        console.log(
          "Error in saving document: " +
            `${document.docCollection}/${document.id}` +
            e
        );
      }
    })
    .catch((e) => {
      console.log("Error in saving documents: " + e);
    });

  console.log(result);
  console.log("All documents saved successfully");
}

async function uploadAllMediaFilesToFirebaseStorage(
  allUserData: IBaseSchema[],
  currentUser: string,
  importedUserId: string,
  changedIdsMap: Map<string, string>
) {
  // const docData = doc.data() as AttachmentArrayType;
  const filesToUpload: AttachmentFileInfo[] = [];
  filesToUpload.push(
    ...getAttachmentDetails(
      allUserData.filter(
        (item) =>
          item.docCollection == "todos" ||
          item.docCollection == "habitActions" ||
          item.docCollection == "journalActions" ||
          item.docCollection == "notes" ||
          item.docCollection == "moneyTrackerTransactions"
      ),
      true,
      changedIdsMap
    )
  );

  const result = await PromisePool.withConcurrency(10)
    .for(filesToUpload)
    .process(async (file, _index, _pool) => {
      console.log("uploading file: " + file.id);
      const extension = file.format ?? "jpeg";
      const destinationOriginalFilePath = getOriginalFilePath(
        currentUser,
        file.docCollectionName,
        file.docId,
        file.id,
        extension
      );
      const destinationOptimizedFilePath = getOptimizedFilePath(
        currentUser,
        file.docCollectionName,
        file.docId,
        file.id,
        extension
      );
      const destinationThumbnailFilePath = getThumbnailFilePath(
        currentUser,
        file.docCollectionName,
        file.docId,
        file.id,
        extension
      );

      const sourceOriginalFilePath = path.join(
        os.tmpdir(),
        currentUser,
        getOriginalFilePath(
          importedUserId,
          file.docCollectionName,
          file.oldDocId ?? file.docId,
          file.id,
          extension
        )
      );
      const sourceOptimizedFilePath = path.join(
        os.tmpdir(),
        currentUser,
        getOptimizedFilePath(
          importedUserId,
          file.docCollectionName,
          file.oldDocId ?? file.docId,
          file.id,
          extension
        )
      );
      const sourceThumbnailFilePath = path.join(
        os.tmpdir(),
        currentUser,
        getThumbnailFilePath(
          importedUserId,
          file.docCollectionName,
          file.oldDocId ?? file.docId,
          file.id,
          extension
        )
      );

      const docSecret = file.encKey;

      if (fs.existsSync(sourceOriginalFilePath)) {
        await uploadFile(
          sourceOriginalFilePath,
          destinationOriginalFilePath,
          docSecret,
          extension,
          file.fileType
        );
        if (fs.existsSync(sourceOptimizedFilePath)) {
          await uploadFile(
            sourceOptimizedFilePath,
            destinationOptimizedFilePath,
            docSecret,
            extension,
            file.fileType
          );
        }
        if (fs.existsSync(sourceThumbnailFilePath)) {
          await uploadFile(
            sourceThumbnailFilePath,
            destinationThumbnailFilePath,
            docSecret,
            extension,
            file.fileType
          );
        }
        console.log("File uploaded: " + file.id);
      } else {
        console.log("File not found: " + file.id);
      }
    });

  console.log("All files uploaded successfully");
  console.log(result.errors);
}

async function uploadFile(
  localFilePath: string,
  bucketFilePath: string,
  encKey: string,
  format?: string,
  fileType?: string
) {
  const extension = format ?? "jpeg";
  const contentType =
    fileType == "image"
      ? "image/jpeg"
      : fileType == "video"
        ? "video/mp4"
        : fileType == "audio"
          ? "audio/mpeg"
          : "multipart/form-data";
  const id = crypto.randomBytes(20).toString("hex");
  const tempPath = path.join(os.tmpdir(), id, `main.${extension}`);
  await mkdirp(path.dirname(tempPath));
  try {
    await encryptFile(localFilePath, tempPath, encKey);

    // upload file
    const bucket = admin.storage().bucket();
    const metaData = { contentType: contentType };

    await bucket.upload(tempPath, {
      destination: bucketFilePath,
      metadata: metaData,
    });

    console.log("File encrypted and uploaded successfully " + bucketFilePath);
  } catch (e) {
    console.log("error in encrypting and uploading file: " + bucketFilePath);
    console.log(e);
  } finally {
    try {
      fs.rmSync(path.dirname(tempPath), { recursive: true });
    } catch (error) {
      console.error("Error in deleting temp files: " + error);
    }
    try {
      fs.unlinkSync(localFilePath);
    } catch (error) {
      console.error("Error in deleting local source file: " + error);
    }
    // console.log("Temp directory deleted: " + tempPath);
  }
}

async function updateProgress(uid: string, progress: "inProgress" | "success") {
  const db = admin.firestore();
  const userDocRef = db.collection("users").doc(uid);
  db.runTransaction(async (transaction) => {
    const userDoc = await transaction.get(userDocRef);
    const user = userDoc.data() as IUser;
    user.dataBackupInfo.import.status = progress;
    if (progress == "inProgress") {
      user.dataBackupInfo.import.lastUpdatedAt = new Date();
    }
    user.localUpdatedAt = getCurrentUTCDate();
    user.cloudUpdatedAt = null;
    transaction.update(
      userDocRef,
      convertDatesToTimestamps(getMeParsedData(user))
    );
  });
}

async function saveUserDocument(userData: IBaseSchema, userId: string) {
  const db = admin.firestore();
  const userDocRef = db.collection("users").doc(userId);
  const userSnapshot = await userDocRef.get();
  if (userSnapshot) {
    const originalUser = userSnapshot.data() as IUser;
    const helperData: Map<string, string> = new Map();
    helperData.set("userId", userData.uid);
    const finalData = await new DataMigrationService().migrateSchema(
      userData,
      helperData
    );
    const parsedDoc = getMeParsedData(finalData) as IUser;
    originalUser.securitySettings = parsedDoc.securitySettings;
    originalUser.cloudUpdatedAt = null;

    // const secret = await getNewSecret();
    // originalUser.encData.dek = await encryptDocKey(secret, userId, userPublicKey);
    originalUser.cloudUpdatedAt = null;
    originalUser.localUpdatedAt = getCurrentUTCDate();
    await new DbRepository().updateData(
      convertDatesToTimestamps(getMeParsedData(originalUser))
    );
    console.log("user saved");
  }
}

async function saveViewSettings(
  viewSettings: IBaseSchema,
  userId: string,
  userPrivateKey: string,
  userPublicKey: string
) {
  const db = admin.firestore();
  const viewSettingsDocRef = db
    .collection("viewSettings")
    .where("uid", "==", userId)
    .limit(1);
  const viewSettingsSnapshot = await viewSettingsDocRef.get();
  if (viewSettingsSnapshot && viewSettingsSnapshot.size > 0) {
    const originalViewSettings =
      viewSettingsSnapshot.docs[0].data() as IViewSettings;

    // decrypt document
    const decryptedDek = await decryptDocKey(
      originalViewSettings.encData.dek,
      userId,
      userPrivateKey
    );
    await decryptDocument(originalViewSettings, decryptedDek);
    // viewSettings.uid = userId;
    // viewSettings.id = originalViewSettings.id;
    const helperData: Map<string, string> = new Map();
    helperData.set("userId", userId);
    const finalData = await new DataMigrationService().migrateSchema(
      viewSettings,
      helperData
    );
    const parsedDoc = getMeParsedData(finalData) as IViewSettings;

    originalViewSettings.featureSettings.userGoal =
      parsedDoc.featureSettings.userGoal;

    // const secret = await getNewSecret();
    // originalUser.encData.dek = await encryptDocKey(secret, userId, userPublicKey);
    originalViewSettings.cloudUpdatedAt = null;
    originalViewSettings.localUpdatedAt = getCurrentUTCDate();
    // encrypt data
    const secret = await getNewSecret();
    originalViewSettings.encData.dek = await encryptDocKey(
      secret,
      userId,
      userPublicKey
    );
    await encryptDocument(originalViewSettings, secret);
    await new DbRepository().updateData(
      convertDatesToTimestamps(getMeParsedData(originalViewSettings))
    );
    console.log("View setting saved");
  }
}

async function saveUserResource(
  userResource: IBaseSchema,
  userId: string,
  userPrivateKey: string,
  userPublicKey: string
) {
  const db = admin.firestore();
  const userResourceDocRef = db
    .collection("userResources")
    .where("uid", "==", userId)
    .limit(1);
  const userResourceSnapshot = await userResourceDocRef.get();
  if (userResourceSnapshot && userResourceSnapshot.size > 0) {
    const originalUserResourceDoc =
      userResourceSnapshot.docs[0].data() as IUserResources;
    // decrypt document
    const decryptedDek = await decryptDocKey(
      originalUserResourceDoc.encData.dek,
      userId,
      userPrivateKey
    );
    await decryptDocument(originalUserResourceDoc, decryptedDek);

    const helperData: Map<string, string> = new Map();
    helperData.set("userId", userId);
    const migratedOldUserResource =
      await new DataMigrationService().migrateSchema(userResource, helperData);
    const finalOldResourceDoc = getMeParsedData(
      migratedOldUserResource
    ) as IUserResources;
    finalOldResourceDoc.tags.forEach((tag) => {
      if (
        originalUserResourceDoc.tags.find((item) => item.id == tag.id) ==
        undefined
      ) {
        originalUserResourceDoc.tags.push(tag);
      }
    });

    // const secret = await getNewSecret();
    // originalUser.encData.dek = await encryptDocKey(secret, userId, userPublicKey);
    originalUserResourceDoc.cloudUpdatedAt = null;
    originalUserResourceDoc.localUpdatedAt = getCurrentUTCDate();
    const secret = await getNewSecret();
    originalUserResourceDoc.encData.dek = await encryptDocKey(
      secret,
      userId,
      userPublicKey
    );
    await encryptDocument(originalUserResourceDoc, secret);
    await new DbRepository().updateData(
      convertDatesToTimestamps(getMeParsedData(originalUserResourceDoc))
    );
    console.log("User resource saved");
  }
}

async function encryptDocument(doc: IBaseSchema, docSecret: string) {
  if (doc.encData != null) {
    for (const field of doc.encData.encFields) {
      const parts = field.split(".");
      iterateMap(doc, parts, 0, docSecret, true, doc.id, doc.docCollection);
    }
  }
}

async function decryptDocument(doc: IBaseSchema, docSecret: string) {
  if (doc.encData != null) {
    for (const field of doc.encData.encFields) {
      const parts = field.split(".");
      iterateMap(doc, parts, 0, docSecret, false, doc.id, doc.docCollection);
    }
  }
}

function changIdsOfAllDocs(allDocuments: IBaseSchema[]) {
  let jsonString = JSON.stringify(allDocuments);
  const changedIdsMap: Map<string, string> = new Map();
  allDocuments.forEach((doc) => {
    if (doc.docCollection != "users" && doc.docCollection != "usersMetadata") {
      const newId = uuidv4();
      changedIdsMap.set(newId, doc.id);
      jsonString = jsonString.replaceAll(doc.id, newId);
    }
  });
  const changedDocs = JSON.parse(jsonString) as IBaseSchema[];
  return { changedDocs, changedIdsMap };
}

async function encryptData(
  docs: IBaseSchema[],
  userId: string,
  userPublicKey: string
) {
  console.log("length " + docs.length);
  for (const doc of docs) {
    const secret = doc.encData.dek;
    doc.encData.dek = await encryptDocKey(secret, userId, userPublicKey);
    for (const field of doc.encData.encFields) {
      const parts = field.split(".");
      iterateMap(doc, parts, 0, secret, true, doc.id, doc.docCollection);
    }
  }
}
