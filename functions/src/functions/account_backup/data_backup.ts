import * as functions from "firebase-functions/v2";
import * as admin from "firebase-admin";
import {
  createWeaklyBackuOfUser,
  refreshBackupForUserById,
} from "./user_data_backup/weekly_account_backups";
import { createAccountBackupOfUser } from "./user_data_backup/export_user_data";
import { onMessagePublished } from "firebase-functions/v2/pubsub";
import { publishMessageToPubSub } from "../../utils/utility_methods";
import { IBaseSchema } from "../migration/versions/base/base_schema";
import { currentDbVersion } from "../migration/versions/base/model_mappings";

const simultaneousUserLimit = 500;

export const createUserSnapshot = functions.scheduler.onSchedule(
  { schedule: "0 0 * * 0", memory: "2GiB", timeoutSeconds: 540 },
  async (_event) => {
    return createAllUserBackup();
  }
);

exports.refreshUserBackup = onMessagePublished(
  {
    topic: "refresh.user.backup",
    concurrency: 1,
    timeoutSeconds: 540,
  },
  async (event) => {
    const data = event.data.message.json;
    functions.logger.info("Refreshing backup for: ", data.uid);
    await refreshBackupForUserById(data.uid);
  }
);

export const createAccountBackupFile = functions.https.onCall(
  async (request) => {
    const uid = request.data.uid;
    const localTime = request.data.localTime ?? new Date().toISOString();
    const localTimeZoneName = request.data.tz;
    const userSecret = request.data.userSecret;
    if (!uid) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "The function must be called with " +
          "one arguments 'uid' containing the user id to backup."
      );
    }

    await publishMessageToPubSub(
      `user_account_backup_event_v${currentDbVersion}`,
      {
        uid: uid,
        localTime: localTime,
        localTimeZoneName: localTimeZoneName ?? "",
        userSecret: userSecret,
      }
    );
    return;
  }
);

exports.accountBackupEventHandler = onMessagePublished(
  {
    topic: `user_account_backup_event_v${currentDbVersion}`,
    memory: "4GiB",
    concurrency: 1,
    timeoutSeconds: 540, // 9 minutes
  },
  async (event) => {
    functions.logger.info("Starting account backup", event);
    const data = event.data.message.json;
    const userSnapshot = await admin
      .firestore()
      .collection("users")
      .doc(data.uid)
      .get();
    if (userSnapshot.exists) {
      await createAccountBackupOfUser(
        userSnapshot.data() as IBaseSchema,
        new Date(data.localTime),
        data.localTimeZoneName,
        data.userSecret
      );
    }
  }
);

async function createAllUserBackup() {
  functions.logger.info("User based migration started");
  const getUserDataQuery = admin
    .firestore()
    .collection("users")
    .limit(simultaneousUserLimit);
  let dataSnapshot = await getUserDataQuery.get();
  functions.logger.info(`length = ${dataSnapshot.docs.length}`);
  while (dataSnapshot.docs.length > 0) {
    const tasks: Promise<void>[] = [];
    for (const document of dataSnapshot.docs) {
      tasks.push(
        createWeaklyBackuOfUser(document.data() as IBaseSchema, false)
      );
    }
    const result = await Promise.all(tasks);
    functions.logger.info("result length = " + result.length);
    const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
    dataSnapshot = await getUserDataQuery.startAfter(last).get();
    functions.logger.info(
      "next length of snapshot = " + dataSnapshot.docs.length
    );
  }
  functions.logger.info("User based migration completed");
}

// exports.testFunctionBackup = functions.https.onRequest(
//   { enforceAppCheck: false },
//   async (req, res) => {
//     console.log("sending push notifications to users");
//     const userSnapshot = await admin
//       .firestore()
//       .collection("users")
//       .doc("NxoIt3KPhm58Fd8jQgqPYGaxiwC7")
//       .get();
//     console.log("userSnapshot.exists = " + userSnapshot.exists);
//     await createAccountBackupOfUser(
//       userSnapshot.data() as IBaseSchema,
//       new Date(new Date().toISOString())
//     );
//     res.send("success");
//   }
// );

// exports.user = functions.https.onRequest(
//   { enforceAppCheck: false },
//   async (req, res) => {
//     console.log("testing migration");
//     const userId: string = req.query.userId as string;
//     if (userId === undefined) {
//       res.send("userId is required");
//       return;
//     }
//     const userSnapshot = await admin
//       .firestore()
//       .collection("users")
//       .doc(userId)
//       .get();
//     if (userSnapshot.exists) {
//       await createBackupOfUser(userSnapshot.data() as IUser);
//     }
//     res.send("success");
//   }
// );

// exports.testFunction = functions.https.onRequest(
//   { enforceAppCheck: false },
//   async (req, res) => {
//     console.log("sending push notifications to users");
//     await createAllUserBackup();
//     res.send("success");
//   }
// );
