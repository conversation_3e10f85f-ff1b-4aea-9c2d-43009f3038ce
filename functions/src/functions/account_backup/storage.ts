import * as functions from "firebase-functions/v2";
import * as admin from "firebase-admin";
import { Bucket, File } from "@google-cloud/storage";
import {
  IUser,
  getMeParsedData,
} from "../migration/versions/base/model_mappings";
import { AttachmentConstants } from "../user_uploads/media/media_constants";
import {
  convertDatesToTimestamps,
  getCurrentUTCDate,
} from "../../utils/utility_methods";

const limit = 1000;

export const calculate = functions.https.onRequest(
  // Check Function: No use found. Chec the trigger freuqency of this function to see if it is used. Otherwise remove.
  { enforceAppCheck: false },
  async (_request, response) => {
    functions.logger.info("starting data migration");
    const db = admin.firestore();
    let collectionQuery = db.collection("users").limit(limit);
    let dataSnapshot = await collectionQuery.get();
    console.log("initial length of snapshot = " + dataSnapshot.docs.length);
    while (dataSnapshot.docs.length > 0) {
      const tasks: Promise<void>[] = [];
      for (const document of dataSnapshot.docs) {
        try {
          const userObj = document.data() as IUser;
          tasks.push(calculateStorageForUserById(userObj.uid));
        } catch (error) {
          console.log(error);
          console.log("error in updating doc: " + document.id);
        }
      }
      await Promise.all(tasks);
      const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
      collectionQuery = db.collection("users").limit(limit).startAfter(last);
      dataSnapshot = await collectionQuery.get();
      console.log("next length of snapshot = " + dataSnapshot.docs.length);
    }
    response.send("success");
  }
);

export async function calculateStorageForUserById(uid: string): Promise<void> {
  const db = admin.firestore();
  console.log(`calculating storage for user ${uid}`);
  const userDocRef = db.collection("users").doc(uid);
  db.runTransaction(async (transaction) => {
    const userDoc = await transaction.get(userDocRef);
    const user = userDoc.data() as IUser;

    const bucket = admin.storage().bucket();
    const fileList = await getAllFileData(
      bucket as unknown as Bucket,
      uid
    ).catch((error) => {
      console.error(error);
      return [];
    });

    console.log(`file length = ${fileList.length}`);
    let storageValue = 0;
    if (fileList.length > 0) {
      for (let i = 0; i < fileList.length; i++) {
        storageValue =
          storageValue + (fileList[i].metadata.size as number) / 1000;
      }
      // storage = storage / 1024;
    }
    console.log(
      `storage used by user ${user.uid} = ${Math.floor(storageValue)}`
    );
    user.userInfo.storageUsed = Math.floor(storageValue);
    user.localUpdatedAt = getCurrentUTCDate();
    user.cloudUpdatedAt = null;
    transaction.update(
      userDocRef,
      convertDatesToTimestamps(getMeParsedData(user))
    );
  });
}

const getFileData = async (bucket: Bucket, prefix: string): Promise<File[]> => {
  return (await bucket.getFiles({ prefix }))[0];
};

async function getAllFileData(bucket: Bucket, userId: string): Promise<File[]> {
  const result: File[] = [];

  const tasks: Promise<File[]>[] = [];
  const basePrefix = `${AttachmentConstants.userDataPath}/${userId}`;
  tasks.push(getFileData(bucket, basePrefix));
  const taskResults = await Promise.all(tasks);
  result.push(...taskResults.flat());
  return result;
}
