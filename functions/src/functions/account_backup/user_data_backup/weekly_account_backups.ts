import * as admin from "firebase-admin";
import * as path from "path";
import * as fs from "fs";
import * as os from "os";
import { mkdirp } from "mkdirp";
import {
  IBaseSchema,
  meCollectionNames,
} from "../../migration/versions/base/base_schema";
import { currentDbVersion } from "../../migration/versions/base/model_mappings";
import { publishMessageToPubSub } from "../../../utils/utility_methods";

export async function createWeaklyBackuOfUser(user: IBaseSchema, reSync: boolean) {
  const allUserData = await getAllFirestoreDataOfUser(user, reSync);
  await saveUserDataSnapshot(allUserData);
}

export async function refreshUserBackup(userId: string) {
  return publishMessageToPubSub("refresh.user.backup", {
    uid: userId,
  });
}

export async function refreshBackupForUserById(userId: string) {
  const userSnapshot = await admin
    .firestore()
    .collection("users")
    .doc(userId)
    .get();
  if (userSnapshot.exists) {
    await createWeaklyBackuOfUser(
      userSnapshot.data() as IBaseSchema,
      true
    );
  }
}

export async function getAllFirestoreDataOfUser(user: IBaseSchema, reSync: boolean) {
  const allDataFromBackupFile = await getAllDataFromBackupFile(
    user.uid,
    user.docVer
  );
  return await getAllDocumentOfUser(user, allDataFromBackupFile, reSync);
}

async function getAllDocumentOfUser(
  user: IBaseSchema,
  allDataFromBackupFile: IBaseSchema[] | null | undefined,
  reSync: boolean
) {
  const userId: string = user.uid;
  const allUserData: IBaseSchema[] = [];
  allUserData.push(user);
  for (const collection of meCollectionNames.filter(
    (item) =>
      item != "users" &&
      item != "calendarEventSetups" &&
      item != "calendarEventActions" &&
      item != "calendarIntegrations"
  )) {
    const docs = await getUserCollectionDocuments(
      collection,
      userId,
      allDataFromBackupFile,
      reSync
    );
    allUserData.push(...docs);
  }
  return allUserData;
}

async function getUserCollectionDocuments(
  collectionName: string,
  userId: string,
  oldDataList: IBaseSchema[] | undefined | null,
  reSync: boolean
) {
  let cloudUpdatedDate = undefined;
  let oldData: IBaseSchema[] = [];
  if (!reSync && oldDataList && oldDataList != null && oldDataList.length > 0) {
    oldData = oldDataList.filter(
      (item) => item.docCollection == collectionName
    );
    if (oldData.length > 0) {
      cloudUpdatedDate = oldData.reduce((prev, current) =>
        (prev.cloudUpdatedAt ?? new Date(0)) >
          (current.cloudUpdatedAt ?? new Date(0))
          ? prev
          : current
      ).cloudUpdatedAt;
    }
  }
  const query = admin
    .firestore()
    .collection(collectionName)
    .where("uid", "==", userId)
    .where("docVer", "==", currentDbVersion);
  if (cloudUpdatedDate) {
    query.where("cloudUpdatedAt", ">", cloudUpdatedDate);
  }
  const documentsSnapshot = await query.get();
  const newList = documentsSnapshot.docs.map(
    (doc) => doc.data() as IBaseSchema
  );
  // replace old data with new data if it exists
  newList.forEach((doc) => {
    const index = oldData.findIndex((oldData) => oldData.id === doc.id);
    if (index !== -1) {
      oldData[index] = doc;
    } else {
      oldData.push(doc);
    }
  });
  return oldData;
}

export async function saveUserDataSnapshot(allUserData: IBaseSchema[]) {
  const jsonData = JSON.stringify(allUserData);
  const bucket = admin.storage().bucket();
  const dbVersion = allUserData[0].docVer;
  const uid = allUserData[0].uid;

  const bucketFile = bucket.file(`userBackup/${dbVersion}/${uid}/backup.json`);

  await bucketFile.save(jsonData);
  console.log(`User data snapshot saved successfully for user ${uid} and docVer ${dbVersion}`);
}

async function getAllDataFromBackupFile(
  uid: string,
  docVer: number
): Promise<IBaseSchema[] | null | undefined> {
  const fileRef = admin
    .storage()
    .bucket()
    .file(`userBackup/${docVer}/${uid}/backup.json`);
  const localOriginalFile = path.join(os.tmpdir(), "temp", "tbackup.json");
  try {
    const tempLocalDir = path.dirname(localOriginalFile);
    await mkdirp(tempLocalDir);
    await fileRef.download({ destination: localOriginalFile });
    const fileContent = fs.readFileSync(localOriginalFile, "utf8");
    const jsonObj = JSON.parse(fileContent);
    console.log("parsed data to object");
    return jsonObj;
  } catch (e) {
    console.log("error in getting backup file: " + e);
    return null;
  } finally {
    if (localOriginalFile && fs.existsSync(localOriginalFile)) {
      try {
        fs.unlinkSync(localOriginalFile);
      } catch (err) {
        console.log(
          "Error in deleting local original file: " + localOriginalFile
        );
      }
    }
  }
}
