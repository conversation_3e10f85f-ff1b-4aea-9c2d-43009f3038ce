import * as admin from "firebase-admin";
import * as crypto from "crypto";
import { getAllFirestoreDataOfUser } from "./weekly_account_backups";
import * as path from "path";
import moment from "moment";
import { PassThrough } from "stream";
import {
  decryptDoc<PERSON>ey,
  getAESDecryptionCypher,
  getDecryptedPrivateKeyOfUserById,
  iterateMap,
} from "../../../utils/encryption/encryption";
import {
  convertDatesToTimestamps,
  getCurrentUTCDate,
  getEmailNameByIdFromAuthService,
  getOptimizedFilePath,
  getOriginalFilePath,
  getSupportLink,
  getThumbnailFilePath,
  getViewSettingsByUserId,
} from "../../../utils/utility_methods";
import { IBaseSchema } from "../../migration/versions/base/base_schema";
import {
  getMeParsedData,
  IUser,
} from "../../migration/versions/base/model_mappings";
import {
  AttachmentArrayType,
  isDocHasAttachments,
} from "../../save_data/db.repository";
import <PERSON><PERSON><PERSON> from "@supercharge/promise-pool";
import { sendSingleEmailViaSes } from "../../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../../utils/emails/email_helper";
import archiver from "archiver";
import { getMeEnvironment } from "../../../me_config";

const linkExpireDate = new Date(Date.now() + 1000 * 60 * 60 * 24 * 7);

export async function createAccountBackupOfUser(
  user: IBaseSchema,
  localTime: Date,
  localTimeZoneName: string,
  userSecret: string
) {
  const TIMEOUT_MS = 535000; // 8 minutes 55 seconds

  const timeoutPromise = new Promise<never>((_, reject) => {
    setTimeout(() => {
      reject(
        new Error(
          `Execution nearing ${TIMEOUT_MS}ms so safely aborting process`
        )
      );
    }, TIMEOUT_MS);
  });

  try {
    console.log("Creating backup for user: " + user.uid);

    // Wrap the main task in a promise
    const mainTask = (async () => {
      await updateProgress(user.uid, "inProgress");
      const userDetails = await getEmailNameByIdFromAuthService(user.uid);

      if (userDetails?.email == null) {
        // const userSecret = (
        //   await getSecretFromKmsForUserByEmail(userDetails.email)
        // ).key;
        throw new Error("User email not found for user: " + user.uid);
      }

      const userPrivateKey = await getDecryptedPrivateKeyOfUserById(
        user.uid,
        userSecret
      );

      if (userPrivateKey == null) {
        throw new Error("User private key not found for user: " + user.uid);
      }

      let allUserData = await getAllFirestoreDataOfUser(user, false);
      console.log(
        "All user data fetched successfully for backup for user: " + user.uid
      );

      // Remove chat messages from the data as we are not exporting chat messages.
      allUserData = allUserData.filter(
        (item) => item.docCollection != "chatMessages"
      );
      console.log("Chat messages removed from export data if found");

      await decryptCollectionData(allUserData, userPrivateKey);

      const zipFilePath = path.join(
        "accountBackupFiles",
        user.uid,
        `mevolve_${moment(localTime).format("YYYYMMDD")}.zip`
      );

      await downloadAllUserStorageData(
        allUserData,
        userPrivateKey,
        zipFilePath
      );

      const signedUrl = await getSignedUrlOfFile(zipFilePath);
      if (getMeEnvironment() === "dev" || getMeEnvironment() === "qa") {
        console.log("Backup created successfully. Signed URL: " + signedUrl);
      } else {
        console.log("Backup created successfully for user: " + user.uid);
      }

      const viewSettings = await getViewSettingsByUserId(user.id);
      const language = viewSettings?.appSettings.language;
      const timeZone =
        viewSettings?.notificationSettings.emailNotificationTimezone;

      await sendSingleEmailViaSes({
        templateName: MevolveEmails.downloadBackupData,
        recipient: userDetails.email,
        templateData: {
          name: userDetails.name ?? "",
          dateOfBackup: `${moment(localTime)
            .utcOffset(timeZone ?? "")
            .format("lll")} ${localTimeZoneName}`,
          linkExpireDate: moment(linkExpireDate)
            .utcOffset(timeZone ?? "")
            .format("ll"),
          downloadDataLink: signedUrl,
          linkToSupportChat: getSupportLink(),
        },
        language,
      });

      console.log(
        "Email sent successfully of data backup for user: " + user.uid
      );
    })();

    // Wait for either the main task or the timeout
    await Promise.race([mainTask, timeoutPromise]);

    await updateProgress(user.uid, "success");
  } catch (error) {
    console.log("Error in creating backup for user: " + user.uid, error);
    await updateProgress(user.uid, "failed");
  }
}

export type AttachmentFileInfo = {
  id: number;
  encKey: string;
  isEncrypted: boolean;
  docCollectionName: string;
  docId: string;
  oldDocId: string | undefined;
  format?: string;
  fileType?: string;
  size?: number;
};

async function downloadAllUserStorageData(
  allUserData: IBaseSchema[],
  userSecret: string,
  zipFilePath: string
) {
  const uid = allUserData[0].uid;

  // Iterate all user data and if the document is having attachments with status == "local", then remove the local attachments from the document
  for (const doc of allUserData) {
    if (isDocHasAttachments(doc)) {
      const item = doc as AttachmentArrayType;

      // Filter out "local" attachments
      item.attachments = item.attachments.filter(
        (att: any) => att.status !== "local"
      );
    }
  }

  const filesToDownload: AttachmentFileInfo[] = [];
  filesToDownload.push(
    ...getAttachmentDetails(
      allUserData.filter(
        (item) =>
          item.docCollection == "todos" ||
          item.docCollection == "habitActions" ||
          item.docCollection == "journalActions" ||
          item.docCollection == "notes" ||
          item.docCollection == "moneyTrackerTransactions"
      ),
      true
    )
  );

  // process all files
  await processFilesAndCreateZip(
    filesToDownload,
    userSecret,
    uid,
    zipFilePath,
    allUserData
  );
  console.log("All files processed successfully");
}

async function processFilesAndCreateZip(
  attachmentsList: AttachmentFileInfo[],
  userSecret: string,
  uid: string,
  zipFilePath: string,
  allUserData: IBaseSchema[]
) {
  console.time("zip-creation");
  console.log("Processing files and creating zip - " + attachmentsList.length);

  const filesToProcess = [];
  for (const attachment of attachmentsList) {
    try {
      const extension = attachment.format ?? "jpeg";
      const docSecret = await decryptDocKey(attachment.encKey, uid, userSecret);

      const originalFilePath = getOriginalFilePath(
        uid,
        attachment.docCollectionName,
        attachment.docId,
        attachment.id,
        extension
      );
      filesToProcess.push({ filePath: originalFilePath, docSecret: docSecret });

      const optimizedFilePath = getOptimizedFilePath(
        uid,
        attachment.docCollectionName,
        attachment.docId,
        attachment.id,
        extension
      );
      filesToProcess.push({
        filePath: optimizedFilePath,
        docSecret: docSecret,
      });

      const thumbnailFilePath = getThumbnailFilePath(
        uid,
        attachment.docCollectionName,
        attachment.docId,
        attachment.id,
        extension
      );
      filesToProcess.push({
        filePath: thumbnailFilePath,
        docSecret: docSecret,
      });
    } catch (error) {
      console.error(
        `Error preparing file info for attachment ${attachment.id}:`,
        error
      );
      // Continue with other attachments
    }
  }

  const bucket = admin.storage().bucket();
  const remoteZipFile = bucket.file(zipFilePath);
  const archiveUploadStream = remoteZipFile.createWriteStream({
    timeout: 1200000,
  });

  const archive = archiver("zip", {
    zlib: { level: 9 },
  });

  archive.on("warning", (err) => {
    if (err.code === "ENOENT") {
      // Log warning but don't fail
      console.warn("Archive warning:", err);
    } else {
      console.error("Archive error:", err);
    }
  });

  archive.on("finish", () => {
    console.log(`Zip Archive created successfully to ${zipFilePath}`);
  });

  archive.on("error", (err) => {
    console.error("Error creating the zip archive:", err);
    archiveUploadStream.destroy(err);
    throw err;
  });

  archive.pipe(archiveUploadStream);

  archiveUploadStream.on("finish", () => {
    console.log(`Archive uploaded successfully to ${zipFilePath}`);
  });

  archiveUploadStream.on("error", async (err) => {
    console.error("Error uploading the archive:", err);
    try {
      if ((await remoteZipFile.exists())[0]) {
        await remoteZipFile.delete();
      }
    } catch (deleteErr) {
      console.error("Error deleting partial zip file:", deleteErr);
    }
    throw err;
  });

  // Add user data as JSON
  try {
    archive.append(JSON.stringify(allUserData), { name: "data.json" });
  } catch (error) {
    console.error("Error adding data.json to archive:", error);
    // Continue with file processing even if data.json fails
  }

  // Process files with error handling for individual files
  await PromisePool.withConcurrency(30)
    .for(filesToProcess)
    .handleError(async (error, file) => {
      // Log the error but continue processing other files
      console.error(`Error processing file: ${file.filePath}`, error);
      // Don't stop the pool, just continue with next file
      return; // Continue with next file
    })
    .process(async ({ filePath, docSecret }) => {
      console.log(`Processing file: ${filePath}`);
      try {
        await processFile(filePath, archive, docSecret);
        console.log(`Successfully processed file: ${filePath}`);
      } catch (error) {
        // Individual file processing errors are caught here
        console.error(`Failed to process file: ${filePath}`, error);
        // Continue with next file
      }
    });

  console.log("File processing completed. Finalizing archive...");

  // Finalize the zip archive after all files are processed
  try {
    await archive.finalize();
    console.log("Archive finalization started.");

    // Wait for upload stream to finish
    await new Promise<void>((resolve, reject) => {
      archiveUploadStream.on("close", resolve);
      archiveUploadStream.on("error", (err) => {
        console.error("Error during archive upload stream close:", err);
        reject(err);
      });

      // Set a timeout to prevent hanging
      const timeout = setTimeout(
        () => {
          console.warn("Archive upload stream timeout after 10 minutes");
          resolve();
        },
        8 * 60 * 1000
      ); // 8 minute timeout

      // Clear timeout if stream closes normally
      archiveUploadStream.on("close", () => {
        clearTimeout(timeout);
      });
    });
  } catch (error) {
    console.error("Error finalizing archive:", error);
    // Still return successfully since we processed as many files as possible
  }

  console.timeEnd("zip-creation");
}

async function processFile(
  filePath: string,
  archive: archiver.Archiver,
  encryptionKey: string
) {
  const bucket = admin.storage().bucket();
  const remoteFile = bucket.file(filePath);

  try {
    // Check if file exists
    if ((await remoteFile.exists())[0] == false) {
      console.log(`File does not exist: ${filePath}`);
      return;
    }
  } catch (error) {
    console.error(`Error checking if file exists: ${filePath}`, error);
    // Rethrow to be caught by the caller
    throw error;
  }

  return new Promise<void>((resolve, _reject) => {
    // Set a timeout to prevent hanging on this file
    const processTimeout = setTimeout(
      () => {
        console.warn(`File processing timeout for ${filePath}`);
        resolve(); // Resolve anyway to continue with other files
      },
      5 * 60 * 1000
    ); // 5 minute timeout per file

    const fileStream = remoteFile.createReadStream();
    let iv: Buffer | undefined;
    let remainderData: Buffer | undefined;
    let decryptStream: crypto.Decipher | undefined;
    const passThrough = new PassThrough();

    // Add to archive only if we successfully start processing
    let addedToArchive = false;

    const cleanupAndResolve = () => {
      clearTimeout(processTimeout);

      if (decryptStream) {
        try {
          decryptStream.end();
        } catch (e) {
          console.warn(`Error ending decrypt stream for ${filePath}:`, e);
        }
      }

      try {
        passThrough.end();
      } catch (e) {
        console.warn(`Error ending passThrough stream for ${filePath}:`, e);
      }
      resolve();
    };

    const handleError = (error: Error) => {
      console.error(`Error processing file ${filePath}:`, error);

      // Cleanup streams
      try {
        fileStream.destroy();
      } catch (e) {
        console.warn(`Error destroying file stream for ${filePath}:`, e);
      }

      if (decryptStream) {
        try {
          decryptStream.destroy();
        } catch (e) {
          console.warn(`Error destroying decrypt stream for ${filePath}:`, e);
        }
      }

      try {
        passThrough.destroy();
      } catch (e) {
        console.warn(`Error destroying passThrough stream for ${filePath}:`, e);
      }

      clearTimeout(processTimeout);
      resolve(); // Resolve instead of reject to continue with other files
    };

    // File stream error handler
    fileStream.on("error", handleError);

    // Timeout handler for file stream
    const fileStreamTimeout = setTimeout(
      () => {
        console.warn(`File stream timeout for ${filePath}`);
        handleError(new Error(`File stream timeout for ${filePath}`));
      },
      2 * 60 * 1000
    ); // 2 minute timeout for stream

    // Clear timeout when stream ends properly
    fileStream.on("end", () => {
      clearTimeout(fileStreamTimeout);
      if (!iv) {
        console.error(`File is too short to extract IV: ${filePath}`);
        cleanupAndResolve();
      }
    });

    // Extract IV and start decryption
    fileStream.once("data", (chunk) => {
      clearTimeout(fileStreamTimeout); // Got data, clear timeout

      if (!iv && chunk.length >= 16) {
        iv = chunk.slice(0, 16) as Buffer;
        remainderData = chunk.slice(16) as Buffer;

        try {
          // Only add to archive if we have valid IV
          addedToArchive = true;
          archive.append(passThrough, { name: filePath });
          startDecryption(iv, remainderData);
        } catch (error) {
          handleError(error as Error);
        }
      } else {
        console.error(
          `Invalid chunk size for IV extraction: ${filePath}, size: ${chunk.length}`
        );
        handleError(new Error(`Invalid chunk size for IV: ${filePath}`));
      }
    });

    // Start decryption once IV is extracted
    const startDecryption = (iv: Buffer, remainingData: Buffer) => {
      try {
        decryptStream = getAESDecryptionCypher(encryptionKey, iv);

        // Error handling for decrypt stream
        decryptStream.on("error", (err) => {
          console.error(`Decryption error for ${filePath}:`, err);
          // Don't call handleError to avoid double cleanup
          cleanupAndResolve();
        });

        decryptStream.on("end", () => {
          cleanupAndResolve();
        });

        // Write the remaining encrypted data
        decryptStream.write(remainingData);

        // Pipe the remaining fileStream data to decryptStream, then to passThrough
        fileStream.pipe(decryptStream).pipe(passThrough);
      } catch (error) {
        console.error(`Error starting decryption for ${filePath}:`, error);
        handleError(error as Error);
      }
    };

    // If file stream doesn't emit any data or ends unexpectedly
    fileStream.on("close", () => {
      if (!addedToArchive) {
        console.warn(`File stream closed without processing: ${filePath}`);
        cleanupAndResolve();
      }
    });
  });
}

async function getSignedUrlOfFile(filePath: string) {
  const bucket = admin.storage().bucket();
  const zipFileInStorageRef = bucket.file(filePath);
  // await zipFileInStorageRef.save(fs.readFileSync("data.zip"));

  const signedUrlResponse = await zipFileInStorageRef.getSignedUrl({
    action: "read",
    expires: linkExpireDate,
  });

  return signedUrlResponse[0];
}

export function getAttachmentDetails(
  dataArray: IBaseSchema[],
  isEncrypted: boolean,
  changedIdsMap?: Map<string, string>
): AttachmentFileInfo[] {
  const files: AttachmentFileInfo[] = [];
  for (const data of dataArray) {
    const item = data as AttachmentArrayType;
    for (const attachment of item.attachments) {
      if (attachment.status == "cloud") {
        files.push({
          id: attachment.id,
          encKey: data.encData.dek,
          isEncrypted: isEncrypted,
          docCollectionName: item.docCollection,
          docId: data.id,
          oldDocId: changedIdsMap?.get(data.id),
          format: attachment.format,
          fileType: attachment.fileType,
          size: attachment.size,
        });
      }
    }
  }
  return files;
}

async function decryptCollectionData(
  data: IBaseSchema[],
  userPrivateKey: string
) {
  for (const doc of data) {
    if (doc.encData != null) {
      // console.log(
      //   "Decrypting data for doc: " + doc.docCollection + "/" + doc.id
      // );
      try {
        const docSecret = await decryptDocKey(
          doc.encData.dek,
          data[0].uid,
          userPrivateKey
        );
        for (let i = 0; i < doc.encData.encFields.length; i++) {
          const field = doc.encData.encFields[i];
          const parts = field.split(".");
          iterateMap(
            doc,
            parts,
            0,
            docSecret,
            false,
            doc.id,
            doc.docCollection
          );
        }
      } catch (error) {
        console.log(
          `error in decrypting data for ${doc.docCollection}/${doc.id}: ${error}`
        );
      }
    }
  }
  console.log("Data decrypted successfully");
}

async function updateProgress(
  uid: string,
  progress: "inProgress" | "success" | "failed"
) {
  const snapshot = await admin.firestore().collection("users").doc(uid).get();
  const user = snapshot.data() as IUser;
  user.dataBackupInfo.export.status = progress;
  if (progress == "inProgress") {
    user.dataBackupInfo.export.lastUpdatedAt = new Date();
  }
  user.localUpdatedAt = getCurrentUTCDate();
  user.cloudUpdatedAt = null;
  await admin
    .firestore()
    .collection("users")
    .doc(user.uid)
    .update(convertDatesToTimestamps(getMeParsedData(user)));
}
