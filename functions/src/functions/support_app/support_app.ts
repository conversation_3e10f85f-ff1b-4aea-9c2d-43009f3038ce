/* eslint-disable max-len */
import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v2";

import { BigQuery } from "@google-cloud/bigquery";
import { getMeEnvironment } from "../../me_config";
import { Timestamp } from "firebase-admin/firestore";

function isEmailAllowed(email: string | undefined): boolean {
  return email == undefined
    ? false
    : email == "<EMAIL>" ||
        email == "<EMAIL>";
}

export const updateSupportPerms = functions.https.onCall(async (request) => {
  if (
    isEmailAllowed(request.auth?.token.email) ||
    (request.auth?.token.isAdmin ?? false)
  ) {
    console.log("Updating permissions for user - " + request.data.sid);
    const db = admin.firestore();
    const docRef = db.collection("supportUsers").doc(request.data.sid);
    try {
      await db.runTransaction(async (t) => {
        const doc = await t.get(docRef);
        if (doc.exists) {
          // Get the requested permissions or initialize if not provided
          let requestedPerms: string[] =
            (request.data.permissions as string[]) || [];

          // Remove duplicates by converting to a Set and back to array
          requestedPerms = [...new Set(requestedPerms)];

          // For all users in isEmailAllowed list, ensure they have admin permissions
          if (isEmailAllowed(doc.data()?.email)) {
            // Ensure adminPanel permissions are always present
            if (!requestedPerms.includes("adminPanel_w")) {
              requestedPerms.push("adminPanel_w");
            }
            if (!requestedPerms.includes("adminPanel_r")) {
              requestedPerms.push("adminPanel_r");
            }

            // Set user claims with admin rights for all allowed emails
            const permsMap = {
              isSupportUser: true,
              permissions: requestedPerms,
              isAdmin: true,
            };

            console.log(
              `Updating permissions for ${doc.data()?.email} with admin rights preserved`
            );
            await admin.auth().setCustomUserClaims(request.data.sid, permsMap);
            t.update(docRef, { permissions: requestedPerms });
          } else {
            // Handle regular support users
            const permsMap = {
              isSupportUser: true,
              permissions: requestedPerms,
              isAdmin: false,
            };

            // Set admin status based on permissions
            if (requestedPerms.includes("adminPanel_w")) {
              permsMap.isAdmin = true;
            }

            await admin.auth().setCustomUserClaims(request.data.sid, permsMap);
            t.update(docRef, { permissions: requestedPerms });
          }
        } else {
          console.log("Support user not found");
        }
      });
      console.log("Permissions updated for user: " + request.data.sid);
      return {
        statusCode: 200,
      };
    } catch (e) {
      console.log("Failed to update permissions:", e);
      return {
        statusCode: 500,
        error: "Failed to update permissions",
      };
    }
  } else {
    console.log("Insufficient permissions");
    return {
      statusCode: 401,
      error: "Unauthorized",
    };
  }
});

const projectId = `mevolve-${getMeEnvironment()}`;
const datasetId = "users_metadata_export";
const tableId = "usersMetadata_schema_userslist_filter_latest";

export const fetchFilterUsers = functions.https.onCall(async (request) => {
  const perms: string[] = request.auth?.token.permissions as string[];
  if (
    (request.auth?.token.isAdmin ||
      (perms != undefined && perms.includes("allUsers_r"))) &&
    request.data != null
  ) {
    const dataMap: Map<string, unknown> = new Map(Object.entries(request.data));

    const bigquery = new BigQuery({ projectId });
    let query = "";
    let isFirstQuery = true;

    if (dataMap.has("subscriptionType")) {
      const typesList = dataMap.get("subscriptionType") as string[];
      query += getFilterCondition(isFirstQuery);

      // for (let i = 0; i < typesList.length; i++) {
      //   const planTime = typesList[i].split("-");
      //   if (i == 0) {
      //     query +=
      //       "((subscriptionInfo_entitlement = '" + planTime[0] + "'";
      //     if (planTime.length > 1) {
      //       query += " AND subscriptionInfo_subscriptionType = '" + planTime[1] + "'";
      //     }
      //     query += ")";
      //   } else {
      //     query +=
      //       " OR (subscriptionInfo_entitlement = '" + planTime[0] + "'";
      //     if (planTime.length > 1) {
      //       query += " AND subscriptionInfo_subscriptionType = '" + planTime[1] + "'";
      //     }
      //     query += ")";
      //   }
      // }

      for (let i = 0; i < typesList.length; i++) {
        if (i == 0) {
          if (typesList[i] == "basic") {
            query += "(subscriptionInfo_entitlement = '" + typesList[i] + "'";
          } else {
            query += "(subscriptionInfo_productId = '" + typesList[i] + "'";
          }
        } else {
          if (typesList[i] == "basic") {
            query +=
              " OR subscriptionInfo_entitlement = '" + typesList[i] + "'";
          } else {
            query += " OR subscriptionInfo_productId = '" + typesList[i] + "'";
          }
        }
      }

      query += ")";
      isFirstQuery = false;
    }
    if (dataMap.has("subscriptionState")) {
      const typesList = dataMap.get("subscriptionState") as string[];
      query += getFilterCondition(isFirstQuery);

      for (let i = 0; i < typesList.length; i++) {
        if (i == 0) {
          if (typesList[i] == "subscribed") {
            query +=
              "((subscriptionInfo_subscriptionState = '" +
              typesList[i] +
              "'" +
              " AND subscriptionInfo_subscriptionExpDate > TIMESTAMP(CURRENT_DATE(), 'UTC'))";
          } else {
            query +=
              "(subscriptionInfo_subscriptionState = '" + typesList[i] + "'";
          }
        } else {
          if (typesList[i] == "subscribed") {
            query +=
              " OR (subscriptionInfo_subscriptionState = '" +
              typesList[i] +
              "'" +
              " AND subscriptionInfo_subscriptionExpDate > TIMESTAMP(CURRENT_DATE(), 'UTC'))";
          } else {
            query +=
              " OR subscriptionInfo_subscriptionState = '" + typesList[i] + "'";
          }
        }
      }
      query += ")";
      isFirstQuery = false;
    }
    if (dataMap.has("deleted")) {
      const typesList = dataMap.get("deleted") as string[];
      query += getFilterCondition(isFirstQuery);

      for (let i = 0; i < typesList.length; i++) {
        if (i == 0) {
          query += "(userDeletedStatus = '" + typesList[i] + "'";
        } else {
          query += " OR userDeletedStatus = '" + typesList[i] + "'";
        }
      }
      query += ")";
      isFirstQuery = false;
    }
    if (dataMap.has("subscriptionExpDate")) {
      const dtString = dataMap.get("subscriptionExpDate") as string;
      query += getFilterCondition(isFirstQuery);
      query +=
        "(subscriptionInfo_subscriptionExpDate > TIMESTAMP(CURRENT_DATE(), 'UTC') " +
        "AND subscriptionInfo_subscriptionExpDate < TIMESTAMP '" +
        dtString +
        "')";
      isFirstQuery = false;
    }
    if (dataMap.has("createdAt")) {
      const dtString = dataMap.get("createdAt") as string;
      query += getFilterCondition(isFirstQuery);
      query += "userInfo_createdAt <= TIMESTAMP '" + dtString + "'";
      isFirstQuery = false;
    }
    if (dataMap.has("chatStatus")) {
      const status = dataMap.get("chatStatus") as string;
      query += getFilterCondition(isFirstQuery);
      query += "chatStatus = '" + status + "'";
      isFirstQuery = false;
    }
    if (dataMap.has("reportStatus")) {
      const status = dataMap.get("reportStatus") as string[];
      query += getFilterCondition(isFirstQuery);
      query += "reportStatus = '" + status + "'";
      isFirstQuery = false;
    }
    if (dataMap.has("disabledFeaturesOr")) {
      const featuresList = dataMap.get("disabledFeaturesOr") as string[];
      query += getFilterCondition(isFirstQuery);

      for (let i = 0; i < featuresList.length; i++) {
        if (i == 0) {
          query += "(" + featuresList[i] + " = false";
        } else {
          query += " OR " + featuresList[i] + " = false";
        }
      }
      query += ")";
      isFirstQuery = false;
    }
    if (dataMap.has("disabledFeaturesAnd")) {
      const featuresList = dataMap.get("disabledFeaturesAnd") as string[];
      query += getFilterCondition(isFirstQuery);

      for (let i = 0; i < featuresList.length; i++) {
        if (i == 0) {
          query += "(" + featuresList[i] + " = false";
        } else {
          query += " AND " + featuresList[i] + " = false";
        }
      }
      query += ")";
      isFirstQuery = false;
    }
    if (dataMap.has("themeColor")) {
      const color = dataMap.get("themeColor") as string[];
      query += getFilterCondition(isFirstQuery);
      query += "appSettings_themeColor = '" + color + "'";
      isFirstQuery = false;
    }
    if (dataMap.has("appTheme")) {
      const theme = dataMap.get("appTheme") as string[];
      query += getFilterCondition(isFirstQuery);
      query += "appSettings_appTheme = '" + theme + "'";
      isFirstQuery = false;
    }
    if (dataMap.has("isPasscodeEnabled")) {
      const value = dataMap.get("isPasscodeEnabled") as boolean;
      query += getFilterCondition(isFirstQuery);
      query += "securitySettings_isPasscodeEnabled = " + value;
      isFirstQuery = false;
    }
    if (dataMap.has("isBiometricEnabled")) {
      const value = dataMap.get("isBiometricEnabled") as boolean;
      query += getFilterCondition(isFirstQuery);
      query += "securitySettings_isBiometricEnabled = " + value;
      isFirstQuery = false;
    }
    if (dataMap.has("isDailyAgendaEmail")) {
      const value = dataMap.get("isDailyAgendaEmail") as boolean;
      query += getFilterCondition(isFirstQuery);
      query +=
        "notificationSettings_isDailyAgendaEmailNotificationEnabled = " + value;
      isFirstQuery = false;
    }
    if (dataMap.has("isDailyAgendaMobile")) {
      const value = dataMap.get("isDailyAgendaMobile") as boolean;
      query += getFilterCondition(isFirstQuery);
      query +=
        "notificationSettings_isDailyAgendaMobileNotificationEnabled = " +
        value;
      isFirstQuery = false;
    }
    if (dataMap.has("searchName")) {
      const nameEmail = dataMap.get("searchName") as string;
      query += getFilterCondition(isFirstQuery);
      query += "(CONTAINS_SUBSTR(userInfo_name, '" + nameEmail + "')";
      query += "OR CONTAINS_SUBSTR(uid, '" + nameEmail + "')";
      query += "OR CONTAINS_SUBSTR(userInfo_email, '" + nameEmail + "'))";
      isFirstQuery = false;
    }
    if (dataMap.has("startAfter")) {
      const dtString = dataMap.get("startAfter") as string;
      " AND userInfo_createdAt <= TIMESTAMP '" + dtString + "'";
    }

    const countQuery =
      "(SELECT COUNT(DISTINCT(uid)) as count FROM `" +
      projectId +
      "." +
      datasetId +
      "." +
      tableId +
      "`" +
      query +
      ") as count";

    let dataQuery =
      "SELECT *, " +
      countQuery +
      " FROM `" +
      projectId +
      "." +
      datasetId +
      "." +
      tableId +
      "`" +
      query;
    dataQuery += " ORDER BY userInfo_createdAt DESC LIMIT 50";
    try {
      const [dataRows] = await bigquery.query(dataQuery);

      let count = 0;
      if (dataRows.length > 0) {
        count = dataRows[0].count as number;
      }

      // Return the results in json
      return {
        statusCode: 200,
        body: JSON.stringify(dataRows),
        count: count,
      };
    } catch (error) {
      console.error("Error running BigQuery query:", error);

      // Return an error response
      return {
        statusCode: 500,
        error: "Internal Server Error",
      };
    }
  }
  return {
    statusCode: 401,
    error: "Unauthorized",
  };
});

function getFilterCondition(isFirst: boolean): string {
  return isFirst ? " WHERE " : " AND ";
}

export const fetchUserActivities = functions.https.onCall(async (request) => {
  const perms: string[] = request.auth?.token.permissions as string[];
  const isAdmin = request.auth?.token.isAdmin;
  const hasPermission = perms && perms.includes("segments_r");
  const userId = request.data?.userId;

  // Database
  const analyticsProjectId = `mevolve_${getMeEnvironment()}`;
  const database = `ri-common-analytics.${analyticsProjectId}.events`;

  // Authorization check
  if (!(isAdmin || hasPermission) || !userId) {
    console.warn("Unauthorized access attempt or missing userId:", {
      isAdmin,
      hasPermission,
      userId,
    });
    return {
      statusCode: 401,
      error: "Unauthorized",
    };
  }

  // Extract and validate parameters
  const {
    startAfter,
    startAfterTime,
    skip = 0,
    eventName,
    eventType,
    startFrom,
  } = request.data;

  const skipNum = parseInt(skip as string, 10) || 0;

  console.log("Filter parameters:", { eventName, eventType, startFrom });
  console.log("Pagination parameters:", {
    startAfter,
    startAfterTime,
    skip: skipNum,
  });

  const bigquery = new BigQuery({ projectId });

  // Build base query with partition filtering for cost optimization
  let query = `
    SELECT event_date, event_timestamp, event_name, event_params
    FROM \`${database}\`
    WHERE uid = @userId
      AND DATE(event_timestamp) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
  `;

  // Apply filters
  if (eventName?.trim()) {
    query += ` AND LOWER(event_name) LIKE LOWER('%${eventName}%')`;
  }

  if (eventType) {
    const eventTypeFilters = {
      Marketing: "STARTS_WITH(event_name, 'm_')",
      Product: "STARTS_WITH(event_name, 'p_')",
      User: "(event_name LIKE '%user%' OR event_name LIKE '%login%' OR event_name LIKE '%subscription%')",
      System:
        "(event_name LIKE '%sync%' OR STARTS_WITH(event_name, 'app_') OR event_name LIKE '%screen%')",
    };

    const filter = eventTypeFilters[eventType as keyof typeof eventTypeFilters];
    if (filter) {
      query += ` AND ${filter}`;
    }
  }

  // Helper function to convert timestamp to microseconds
  const convertToMicroseconds = (
    timestamp: string,
    label: string
  ): string | null => {
    try {
      // If it's already a microsecond timestamp, use it directly
      if (/^\d+$/.test(timestamp)) {
        return BigInt(timestamp).toString();
      }
      // Otherwise convert from ISO timestamp
      const date = new Date(timestamp);
      return (BigInt(date.getTime()) * BigInt(1000)).toString();
    } catch (error) {
      console.error(`Error converting ${label} to microseconds:`, error);
      return null;
    }
  };

  // Apply date filters
  if (startFrom) {
    const microseconds = convertToMicroseconds(startFrom, "startFrom");
    if (microseconds) {
      query += ` AND event_timestamp >= TIMESTAMP_MICROS(${microseconds})`;
    }
  }

  // Apply pagination filters
  if (startAfter) {
    console.log("Using original event_timestamp for pagination:", startAfter);
    const microseconds = convertToMicroseconds(startAfter, "startAfter");
    if (microseconds) {
      query += ` AND event_timestamp < TIMESTAMP_MICROS(${microseconds})`;
    }
  } else if (startAfterTime) {
    console.log("Converting ISO timestamp to microseconds:", startAfterTime);
    const microseconds = convertToMicroseconds(
      startAfterTime,
      "startAfterTime"
    );
    if (microseconds) {
      query += ` AND event_timestamp < TIMESTAMP_MICROS(${microseconds})`;
    }
  }

  // Add ordering and pagination
  query += " ORDER BY event_timestamp DESC";
  query += skipNum > 0 ? ` LIMIT 50 OFFSET ${skipNum}` : " LIMIT 50";

  // 100 MB limit for maximumBytesBilled (should be much lower now with partition filtering)
  const maximumBytesBilled = 100 * 1024 * 1024;

  const options = {
    location: "europe-west1",
    query: query,
    params: { userId },
    // Add performance optimizations
    useLegacySql: false, // Ensure we're using Standard SQL
    maximumBytesBilled: maximumBytesBilled.toString(),
    jobTimeoutMs: 30000, // 30 second timeout
    // Enable query cache for repeated queries
    useQueryCache: true,
    // Set job priority to reduce costs
    priority: "INTERACTIVE",
  };

  console.log("Query:", options.query);
  console.log("Parameters:", options.params);

  try {
    console.log("Running BigQuery with options:", options);
    const [rows] = await bigquery.query(options);
    console.log(`Fetched ${rows.length} rows for user_id ${userId}`);

    return {
      statusCode: 200,
      body: JSON.stringify(rows),
      hasMore: rows.length === 50,
    };
  } catch (error: any) {
    console.error("BigQuery fetch error:", error);
    return {
      statusCode: 500,
      error: error.message || "Internal Server Error",
    };
  }
});

// PITR (Point-In-Time Recovery) restoration function to restore data to a specific timestamp.
// Can restore specific user data across collections or the entire database.
// Restricted to admin users only.
export const restoreDatabaseToTimestamp = functions.https.onCall(
  async (request) => {
    // Verify admin permissions
    const isAdmin = request.auth?.token.isAdmin;

    if (!isAdmin) {
      console.warn("Unauthorized database restore attempt");
      return {
        statusCode: 401,
        error: "Unauthorized. Only admin users can perform database restores.",
      };
    }

    try {
      // Validate and parse timestamp
      const timestamp = request.data?.timestamp;
      if (!timestamp) {
        return {
          statusCode: 400,
          error: "Missing required parameter: timestamp",
        };
      }

      const readTimestamp = Timestamp.fromMillis(Date.parse(timestamp));

      // Prevent restoring to a future timestamp
      const currentTime = Timestamp.now();
      if (readTimestamp > currentTime) {
        return {
          statusCode: 400,
          error: "Cannot restore to a future timestamp",
        };
      }

      // Get the Firestore client
      const db = admin.firestore();
      const uidParam = request.data?.uid; // Can be either a single UID or an array of UIDs
      const excludedCollections = request.data?.excludedCollections || []; // Collections to exclude

      // Convert uidParam to an array of UIDs (or empty array if no UID specified)
      const uids = uidParam
        ? Array.isArray(uidParam)
          ? uidParam
          : [uidParam]
        : [];

      console.log(
        `PITR READ FOR {${readTimestamp.toMillis()} | ${readTimestamp.toDate().toISOString()}}` +
          `${uids.length > 0 ? ` for UID(s): ${uids.join(", ")}` : " for entire database"}`
      );

      // Dynamically get all collections instead of hardcoding them
      const collectionsRef = await db.listCollections();
      const allCollections = collectionsRef
        .map((collection) => collection.id)
        .filter((collectionId) => !excludedCollections.includes(collectionId));

      console.log(
        `Found ${allCollections.length} collections to process (after exclusions)`
      );

      const restoredStats: Record<string, number> = {};

      if (uids.length > 0) {
        // User-specific restore across all collections for each provided UID
        for (const uid of uids) {
          await restoreUserData(
            db,
            uid,
            allCollections,
            readTimestamp,
            restoredStats
          );
        }
      } else {
        // Throw for full database restore for test
        throw new Error("Full database restore is not implemented yet.");

        // Full database restore
        await restoreFullDatabase(
          db,
          allCollections,
          readTimestamp,
          restoredStats
        );
      }

      return {
        statusCode: 200,
        message:
          uids.length > 0
            ? `User data restoration complete for UID(s): ${uids.join(", ")}.`
            : "Full database restoration complete (excluding specified collections).",
        details: {
          targetTimestamp: readTimestamp.toDate().toISOString(),
          restoreOperation:
            uids.length > 0 ? "userDataRestore" : "databaseFullRestore",
          restoredUids: uids.length > 0 ? uids : null,
          restoredCollections: Object.entries(restoredStats).map(
            ([collection, count]) => ({
              name: collection,
              documentCount: count,
            })
          ),
        },
      };
    } catch (error: any) {
      console.error("Error initiating database restore:", error);
      return {
        statusCode: 500,
        error: error.message || "Internal Server Error",
      };
    }
  }
);

// Restore user-specific data across multiple collections
async function restoreUserData(
  db: FirebaseFirestore.Firestore,
  uid: string,
  collections: string[],
  readTimestamp: FirebaseFirestore.Timestamp,
  restoredStats: Record<string, number>
): Promise<void> {
  for (const collectionName of collections) {
    try {
      restoredStats[collectionName] = 0;

      const query = db.collection(collectionName).where("uid", "==", uid);

      // First, read all documents at the specified point in time
      const querySnapshot = await db.runTransaction(
        async (transaction) => {
          return transaction.get(query);
        },
        {
          readOnly: true,
          readTime: readTimestamp,
        }
      );

      if (querySnapshot.empty) {
        console.log(`No documents found in ${collectionName} for UID: ${uid}`);
        continue;
      }

      // Batch writes for better atomicity and performance
      const MAX_BATCH_SIZE = 500; // Firestore limit
      let batch = db.batch();
      let batchCount = 0;

      for (const doc of querySnapshot.docs) {
        const docData = doc.data();

        // Update timestamp fields if they exist in the document
        if ("cloudUpdatedAt" in docData) {
          docData.cloudUpdatedAt = admin.firestore.FieldValue.serverTimestamp();
        }
        if ("localUpdatedAt" in docData) {
          docData.localUpdatedAt = admin.firestore.FieldValue.serverTimestamp();
        }

        batch.set(doc.ref, docData);
        batchCount++;

        // Commit when batch size reaches limit
        if (batchCount >= MAX_BATCH_SIZE) {
          await batch.commit();
          batch = db.batch();
          batchCount = 0;
        }
      }

      // Commit any remaining operations
      if (batchCount > 0) {
        await batch.commit();
      }

      restoredStats[collectionName] = querySnapshot.size;
      console.log(
        `Restored ${querySnapshot.size} documents from ${collectionName} for UID: ${uid}`
      );
    } catch (collectionError) {
      console.error(
        `Error processing collection ${collectionName}:`,
        collectionError
      );
      // Continue with other collections despite error in one
    }
  }
}

// Restore the entire database to a specific timestamp
async function restoreFullDatabase(
  db: FirebaseFirestore.Firestore,
  collections: string[],
  readTimestamp: FirebaseFirestore.Timestamp,
  restoredStats: Record<string, number>
): Promise<void> {
  // Process each collection
  for (const collectionName of collections) {
    try {
      restoredStats[collectionName] = 0;
      let processedCount = 0;
      const batchSize = 1000; // Maximum docs to process in one batch
      let lastDocRef: FirebaseFirestore.QueryDocumentSnapshot | null = null;
      let hasMoreDocs = true;

      // Process collection in batches to handle large collections
      while (hasMoreDocs) {
        // Create a query with pagination if needed
        let query: FirebaseFirestore.Query<FirebaseFirestore.DocumentData>;
        const collectionRef = db.collection(collectionName);

        if (lastDocRef) {
          query = collectionRef.startAfter(lastDocRef).limit(batchSize);
        } else {
          query = collectionRef.limit(batchSize);
        }

        // Get documents at point in time
        const querySnapshot = await db.runTransaction(
          async (transaction) => {
            return transaction.get(query);
          },
          {
            readOnly: true,
            readTime: readTimestamp,
          }
        );

        if (querySnapshot.empty) {
          hasMoreDocs = false;
          break; // No more documents
        }

        // Create write batch for atomic updates
        let writeBatch = db.batch();
        let writeBatchCount = 0;
        const MAX_WRITE_BATCH = 500; // Firestore limit

        for (const doc of querySnapshot.docs) {
          const docData = doc.data();

          // Update timestamp fields if they exist in the document
          if ("cloudUpdatedAt" in docData) {
            docData.cloudUpdatedAt =
              admin.firestore.FieldValue.serverTimestamp();
          }
          if ("localUpdatedAt" in docData) {
            docData.localUpdatedAt =
              admin.firestore.FieldValue.serverTimestamp();
          }

          writeBatch.set(doc.ref, docData);
          writeBatchCount++;

          // Commit batch when it reaches limit
          if (writeBatchCount >= MAX_WRITE_BATCH) {
            await writeBatch.commit();
            writeBatch = db.batch();
            writeBatchCount = 0;
          }

          // Track last document for pagination
          lastDocRef = doc;
        }

        // Commit any remaining operations
        if (writeBatchCount > 0) {
          await writeBatch.commit();
        }

        processedCount += querySnapshot.size;

        // Check if we got fewer docs than requested (end of collection)
        if (querySnapshot.size < batchSize) {
          hasMoreDocs = false;
        }
      }

      restoredStats[collectionName] = processedCount;
      console.log(
        `Restored ${processedCount} documents from ${collectionName}`
      );
    } catch (collectionError) {
      console.error(
        `Error processing collection ${collectionName}:`,
        collectionError
      );
    }
  }
}
