import * as functions from "firebase-functions/v2";
import * as admin from "firebase-admin";
import { Timestamp } from "firebase-admin/firestore";
import { getMeEnvironment } from "../../me_config";
import { getApiSecret } from "../../utils/utility_methods";

export const updateReleaseConfig = functions.https.onRequest(
  // Check Function: Used for proect tool app to update release config.
  { enforceAppCheck: false },
  async (request, response) => {
    if (
      request.body.secret != undefined &&
      getApiSecret() != undefined &&
      (request.body.secret as string) == getApiSecret()
    ) {
      console.log("Updating release config for " + getMeEnvironment());
      const db = admin.firestore();
      const docRef = db.collection("releaseConfigs").doc(request.body.data.id);
      try {
        await db.runTransaction(async (t) => {
          const doc = await t.get(docRef);
          if (doc.exists) {
            request.body.data.cloudUpdatedAt = Timestamp.now();
            if (request.body.data.deletedAt != null) {
              t.delete(docRef);
            } else {
              t.update(docRef, request.body.data);
            }
            console.log("Release config updated for " + getMeEnvironment());
          } else {
            request.body.data.cloudUpdatedAt = Timestamp.now();
            t.set(docRef, request.body.data);
            console.log("Release config added for " + getMeEnvironment());
          }
        });
        response.status(200).send("Success");
      } catch (e) {
        console.log("Failed to update release config:", e);
        response.status(500).send("Failed");
      }
    } else {
      console.log("Insufficient permissions");
      response.status(401).send("Unauthorized");
    }
  }
);
