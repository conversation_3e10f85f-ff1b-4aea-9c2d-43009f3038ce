import { Timestamp } from "firebase-admin/firestore";
import * as admin from "firebase-admin";
import {
  getAccessToken,
  getGoogleCalendarInstance,
  getGoogleOAuth2Client,
  getMicrosoftClient,
  parseGoogleCalendarEvents,
  parseMicrosoftCalendarEvents,
  stopWebhookForCalGroup,
} from "./calendar_api_handler";
import {
  decryptTextData,
  getSecretFromKmsForUserByEmail,
} from "../../../utils/encryption/encryption";
import { ICalendarIntegrations } from "../../migration/versions/base/model_mappings";
import { Calendar, Event } from "@microsoft/microsoft-graph-types-beta";
import { google } from "googleapis";
import { MeLogger } from "../../../utils/logger/models/me_logger";

export async function handleWebhook(
  logger: MeLogger,
  chanId: string,
  token: string,
  resourceId: string | undefined,
  eventId: string | undefined
) {
  try {
    const calId = token.match("(?<=calId=).+")?.[0] ?? "";
    const type = token.match("(?<=type=).+.+?(?=\\?)")?.[0] ?? "";
    const uid = token.match("(?<=uid=).+.+?(?=\\?type)")?.[0] ?? "";
    const db = admin.firestore();
    const calData = await db
      .collection("calendarIntegrations")
      .doc(calId)
      .get();
    const batch = db.batch();

    logger.log(
      `Calendar Webhook handle for user: ${uid}, calId: ${calId}, chanId: ${chanId} and resId: ${resourceId}`
    );
    if (calData.exists) {
      // logger.log(`Calendar Webhook handle for user : ${uid} and chanId: ${chanId}`);
      let calendarIntegrations =
        calData.data() as unknown as ICalendarIntegrations;
      if (
        calendarIntegrations.deletedAt == null &&
        calendarIntegrations.status != "deleted" &&
        calendarIntegrations.refreshToken != null
      ) {
        const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
        if (type == "google") {
          logger.log(
            `Calendar Webhook process start for user : ${uid} and google calId: ${calId}`
          );
          // const googleCalendars = calendarIntegrations.googleCalendars;
          // const calDetIndex = googleCalendars.findIndex((item) => item.id == calId);
          const calDetails = calendarIntegrations;

          // if (calDetIndex > -1) {
          const index = calDetails.calendarGroups.findIndex((item) =>
            item.channelId?.startsWith(chanId)
          );
          if (index > -1) {
            const oauth2Client = getGoogleOAuth2Client();
            const calendar = getGoogleCalendarInstance();

            const calGroup = calDetails.calendarGroups[index];
            if (calDetails.refreshToken != null) {
              const refreshToken = decryptTextData(
                calDetails.refreshToken,
                userSecret
              );
              if (calGroup.syncStatus != "disabled") {
                oauth2Client.setCredentials({
                  refresh_token: refreshToken,
                });
                const accessToken = (await oauth2Client.getAccessToken()).token;
                oauth2Client.setCredentials({
                  access_token: accessToken,
                });
                let accountName: string | null = null;
                const oauth2 = google.oauth2({
                  auth: oauth2Client,
                  version: "v2",
                });
                const res = await oauth2.userinfo.get();
                accountName = res?.data.name ?? null;

                const calInfo = await calendar.calendarList.get({
                  calendarId: calGroup.id,
                  auth: oauth2Client,
                  quotaUser: uid,
                });
                let defaultMinutes: number | null = null;
                if (calInfo.data.defaultReminders != null) {
                  const reminder = calInfo.data.defaultReminders.find(
                    (item) => item.method == "popup"
                  );
                  defaultMinutes = reminder?.minutes ?? null;
                }
                let calEvents = await calendar.events.list({
                  calendarId: calGroup.id,
                  auth: oauth2Client,
                  maxResults: 2500,
                  singleEvents: false,
                  syncToken: calGroup.nextSyncToken ?? undefined,
                  showDeleted: true,
                  quotaUser: uid,
                });
                
                // Fallback for stale sync tokens: if we have a sync token but got 0 events,
                // the token is likely invalid. Retry with timeMin to get last 90 days of events.
                // This fixes the issue where webhooks trigger but don't sync events for old calendar groups.
                if (calGroup.nextSyncToken && (!calEvents.data.items || calEvents.data.items.length === 0)) {
                  logger.log(`Webhook triggered but no events returned for calendar ${calGroup.id}, sync token expired, doing full sync`);
                  
                  // First try to get a fresh sync token by doing a full sync
                  try {
                    const fullSyncResponse = await calendar.events.list({
                      calendarId: calGroup.id,
                      auth: oauth2Client,
                      maxResults: 2500,
                      singleEvents: false,
                      showDeleted: true,
                      quotaUser: uid,
                    });
                    
                    calEvents = fullSyncResponse;
                    // Clear the old invalid sync token since we're starting fresh
                    calGroup.nextSyncToken = null;
                    logger.log(`Full sync successful for calendar ${calGroup.id}, got ${calEvents.data.items?.length || 0} events`);
                  } catch (fullSyncError) {
                    // If full sync fails, fall back to time-based query
                    logger.log(`Full sync failed for calendar ${calGroup.id}, falling back to time-based query: ${fullSyncError}`);
                    calEvents = await calendar.events.list({
                      calendarId: calGroup.id,
                      auth: oauth2Client,
                      maxResults: 2500,
                      singleEvents: false,
                      timeMin: new Date(new Date().setDate(new Date().getDate() - 90)).toISOString(),
                      showDeleted: true,
                      quotaUser: uid,
                    });
                    // Explicitly set to null since time-based queries don't return sync tokens
                    calGroup.nextSyncToken = null;
                  }
                }
                
                if (
                  calEvents.data.items != null &&
                  calEvents.data.items.length > 0
                ) {
                  console.log(
                    "Calendar Webhook Event List for group: " +
                      calGroup.id +
                      " length: " +
                      calEvents.data.items.length.toString()
                  );
                  const calAllEventsMap = await parseGoogleCalendarEvents(
                    uid,
                    calendarIntegrations.id,
                    calendarIntegrations.email,
                    calGroup.id,
                    calendarIntegrations.email == calGroup.id
                      ? (accountName ?? calInfo.data.summary ?? "")
                      : (calInfo.data.summary ?? ""),
                    calEvents.data.items,
                    calGroup.syncStatus,
                    defaultMinutes
                  );

                  const calAllEventSetupsMap = calAllEventsMap[0];
                  const calAllEventActionsMap = calAllEventsMap[1];

                  calAllEventSetupsMap.forEach((value: object, key: string) => {
                    const docRef = db
                      .collection("calendarEventSetups")
                      .doc(key);
                    batch.set(docRef, value, { merge: true });
                  });
                  calAllEventActionsMap.forEach(
                    (value: object, key: string) => {
                      const docRef = db
                        .collection("calendarEventActions")
                        .doc(key);
                      batch.set(docRef, value, { merge: true });
                    }
                  );

                  // Only update nextSyncToken if we got one from the response
                  // If we did a time-based query (no syncToken), this will be null
                  // The next webhook call will do a full sync to get a fresh token
                  calGroup.nextSyncToken = calEvents.data.nextSyncToken ?? null;
                  calGroup.nextPageToken = calEvents.data.nextPageToken ?? null;
                  calGroup.resourceId =
                    typeof resourceId === "string" ? resourceId : null;

                  calDetails.calendarGroups[index] = calGroup;
                  calendarIntegrations = calDetails;
                  // calendarIntegrations.googleCalendars = googleCalendars;
                  batch.update(
                    db.collection("calendarIntegrations").doc(calId),
                    {
                      calendarGroups: calendarIntegrations.calendarGroups,
                      localUpdatedAt: Timestamp.now(),
                      cloudUpdatedAt: Timestamp.now(),
                    }
                  );
                  await batch.commit();
                }
              } else {
                const accessToken = await getAccessToken(refreshToken, type);
                if (
                  resourceId != null &&
                  typeof resourceId === "string" &&
                  accessToken != null
                ) {
                  stopWebhookForCalGroup(
                    logger,
                    accessToken,
                    chanId,
                    resourceId,
                    type
                  );
                  logger.log(
                    `Calendar Webhook stopped for user: ${uid}, calId: ${calId}, chanId: ${chanId} and resId: ${resourceId}`
                  );
                }
              }
            }
          }
          // }
        } else if (type == "microsoft") {
          logger.log(
            `Calendar Webhook process start for user : ${uid} and ms calId: ${calId}`
          );
          // const microsoftCalendars = calendarIntegrations.microsoftCalendars;
          // const calDetIndex = microsoftCalendars.findIndex((item) => item.id == calId);
          const calDetails = calendarIntegrations;
          // if (calDetIndex > -1) {
          const index = calDetails.calendarGroups.findIndex(
            (item) => item.id == chanId
          );
          if (index > -1) {
            const calGroup = calDetails.calendarGroups[index];
            if (calDetails.refreshToken != null) {
              const refreshToken = decryptTextData(
                calDetails.refreshToken,
                userSecret
              );
              const accessToken = await getAccessToken(refreshToken, type);
              if (accessToken != null) {
                if (calGroup.syncStatus != "disabled") {
                  const client = getMicrosoftClient(accessToken, true);
                  // const startTime = new Date(
                  //   new Date().setDate(new Date().getDate() - 90)
                  // ).toISOString();
                  // const endTime = new Date(
                  //   new Date().setDate(new Date().getFullYear() + 100)
                  // ).toISOString();

                  const apiUrl = `me/Events/${eventId}`;
                  // + "?$select=subject,body,bodyPreview,seriesMasterId,type,recurrence,start,end," +
                  // "reminderMinutesBeforeStart,isAllDay,isCancelled,createdDateTime,location,sensitivity," +
                  // "importance,responseStatus,onlineMeeting,cancelledOccurrences&$expand=exceptionOccurrences";

                  // calGroup.nextSyncToken != null ?
                  //   `/me/calendars/${calGroup.id}/events/delta?$deltatoken=${calGroup.nextSyncToken}` :
                  //   `/me/calendars/${calGroup.id}/events/delta?startDateTime=${startTime}&endDateTime=${endTime}`;

                  try {
                    const calEvents: Event[] = [];
                    let event: Event | null = null;
                    event = (await client.api(apiUrl).get()) as Event;
                    calEvents.push(event);

                    if (event.recurrence != null) {
                      const startTime = new Date(
                        new Date().setDate(new Date().getDate() - 90)
                      ).toISOString();
                      const endTime = new Date(
                        new Date().setDate(new Date().getDate() + 365)
                      ).toISOString();
                      const apiUrl = `me/Events/${eventId}/instances?startDateTime=${startTime}&endDateTime=${endTime}&$filter=type eq 'exception'`;
                      const eventOccurences = (await client.api(apiUrl).get())
                        .value as Event[];
                      calEvents.push(...eventOccurences);
                    }

                    if (calEvents != null && calEvents.length > 0) {
                      logger.log(
                        "Calendar Webhook Event List for group: " +
                          calGroup.id +
                          " length: " +
                          calEvents.length.toString()
                      );
                      const calAllEventsMap =
                        await parseMicrosoftCalendarEvents(
                          uid,
                          calendarIntegrations.id,
                          calendarIntegrations.email,
                          calGroup.id,
                          calGroup.title ?? "",
                          calEvents,
                          calGroup.syncStatus,
                          accessToken
                        );

                      const calAllEventSetupsMap = calAllEventsMap[0];
                      const calAllEventActionsMap = calAllEventsMap[1];

                      calAllEventSetupsMap.forEach(
                        (value: object, key: string) => {
                          const docRef = db
                            .collection("calendarEventSetups")
                            .doc(key);
                          batch.set(docRef, value, { merge: true });
                        }
                      );
                      calAllEventActionsMap.forEach(
                        (value: object, key: string) => {
                          const docRef = db
                            .collection("calendarEventActions")
                            .doc(key);
                          batch.set(docRef, value, { merge: true });
                        }
                      );
                      await batch.commit();
                    }
                  } catch (error) {
                    if (`${error}`.includes("specified object was not found")) {
                      const id = `${calendarIntegrations.id.substring(0, 7)}_${eventId}`;
                      const docRef = db
                        .collection("calendarEventSetups")
                        .doc(id);
                      batch.set(
                        docRef,
                        {
                          deletedAt: Timestamp.now(),
                          permaDeletedAt: Timestamp.now(),
                          localUpdatedAt: Timestamp.now(),
                          cloudUpdatedAt: Timestamp.now(),
                        },
                        { merge: true }
                      );
                      await batch.commit();
                    }
                  }
                  // const url = new URL(
                  //   JSON.parse(userResponse)["@odata.deltaLink"]
                  // );
                  // const nextSyncToken = url.searchParams.get("$deltatoken");
                } else {
                  if (
                    resourceId != null &&
                    typeof resourceId === "string" &&
                    accessToken != null
                  ) {
                    stopWebhookForCalGroup(
                      logger,
                      accessToken,
                      resourceId,
                      null,
                      type
                    );
                    logger.log(
                      `Calendar Webhook stopped for user: ${uid}, calId: ${calId}, chanId: ${chanId} and resId: ${resourceId}`
                    );
                  }
                }
              }
            }
          }
        }
      } else {
        logger.log(
          `Calendar Webhook discard for user: ${uid} and calId: ${calId}`
        );
      }
    }
    logger.log(
      `Calendar Webhook processed for user: ${uid}, calId: ${calId}, chanId: ${chanId} and resId: ${resourceId}`
    );
    return { status: 200 };
  } catch (e) {
    logger.log(
      `Calendar Webhook processing failed for: chanId: ${chanId} and resId: ${resourceId} with error: ${e}`
    );
    return { status: 500 };
  }
}

export async function handleCalListWebhook(
  logger: MeLogger,
  chanId: string,
  token: string,
  resourceId: string | string[] | undefined
) {
  try {
    const calId = token.match("(?<=calId=).+")?.[0] ?? "";
    const type = token.match("(?<=type=).+.+?(?=\\?)")?.[0] ?? "";
    const uid = token.match("(?<=uid=).+.+?(?=\\?type)")?.[0] ?? "";
    const db = admin.firestore();
    const calData = await db
      .collection("calendarIntegrations")
      .doc(calId)
      .get();
    const batch = db.batch();
    logger.log(
      `Calendar CalList Webhook handle init for user: ${uid}, calId: ${calId}, chanId: ${chanId} and resId: ${resourceId}`
    );
    if (calData.exists) {
      // logger.log(`Calendar CalList Webhook handle for user : ${uid} and google calId: ${calId}`);
      const calendarIntegrations =
        calData.data() as unknown as ICalendarIntegrations;
      if (
        calendarIntegrations.deletedAt == null &&
        calendarIntegrations.status != "deleted" &&
        calendarIntegrations.refreshToken != null
      ) {
        const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
        if (type == "googleCalList") {
          logger.log(
            `Calendar CalList Webhook process start for user : ${uid} and google calId: ${calId}`
          );

          const calDetails = calendarIntegrations;

          const index = calDetails.calendarGroups.findIndex(
            (item) => item.title == "Mevolve"
          );
          if (index > -1) {
            const oauth2Client = getGoogleOAuth2Client();
            const calendar = getGoogleCalendarInstance();

            const mevolveGroup = calDetails.calendarGroups[index];
            if (calDetails.refreshToken != null) {
              const refreshToken = decryptTextData(
                calDetails.refreshToken,
                userSecret
              );
              oauth2Client.setCredentials({
                refresh_token: refreshToken,
              });
              const accessToken = (await oauth2Client.getAccessToken()).token;
              oauth2Client.setCredentials({
                access_token: accessToken,
              });

              const calGroupsList = await calendar.calendarList.list({
                auth: oauth2Client,
                quotaUser: uid,
              });

              if (
                calGroupsList.data.items == null ||
                calGroupsList.data.items.length == 0 ||
                calGroupsList.data.items.findIndex(
                  (item) =>
                    item.summary == "Mevolve" &&
                    item.id == mevolveGroup.id &&
                    item.deleted != true
                ) == -1
              ) {
                batch.update(db.collection("calendarIntegrations").doc(calId), {
                  localUpdatedAt: Timestamp.now(),
                  cloudUpdatedAt: Timestamp.now(),
                  mevolveStatus: "mevolveError",
                  resourceId:
                    typeof resourceId === "string" ? resourceId : null,
                });
                await batch.commit();
              }
            }
          }
        } else if (type == "microsoftCalList") {
          logger.log(
            `Calendar CalList Webhook process start for user : ${uid} and ms calId: ${calId}`
          );
          const calDetails = calendarIntegrations;
          const index = calDetails.calendarGroups.findIndex(
            (item) => item.title == "Mevolve"
          );
          if (index > -1) {
            const mevolveGroup = calDetails.calendarGroups[index];
            if (calDetails.refreshToken != null) {
              const refreshToken = decryptTextData(
                calDetails.refreshToken,
                userSecret
              );
              const accessToken = await getAccessToken(refreshToken, type);
              if (accessToken != null) {
                const client = getMicrosoftClient(accessToken);

                const calendarsListResponse = await client
                  .api("/me/calendars")
                  .get();
                const calendarsList = calendarsListResponse.value as Calendar[];

                if (
                  calendarsList == null ||
                  calendarsList.length == 0 ||
                  calendarsList.findIndex(
                    (item) =>
                      item.name == "Mevolve" && item.id != mevolveGroup.id
                  ) > -1
                ) {
                  batch.update(
                    db.collection("calendarIntegrations").doc(calId),
                    {
                      localUpdatedAt: Timestamp.now(),
                      cloudUpdatedAt: Timestamp.now(),
                      mevolveStatus: "mevolveError",
                      resourceId:
                        typeof resourceId === "string" ? resourceId : null,
                    }
                  );
                  await batch.commit();
                }
              }
            }
          }
        }
      } else {
        logger.log(
          `Calendar CalList Webhook discard for user: ${uid} and calId: ${calId}`
        );
      }
    }
    logger.log(
      `Calendar CalList Webhook processed for user: ${uid}, calId: ${calId}, chanId: ${chanId} and resId: ${resourceId}`
    );
    return { status: 200 };
  } catch (e) {
    logger.log(
      `Calendar CalList Webhook processing failed for: chanId: ${chanId} and resId: ${resourceId} with error: ${e}`
    );
    return { status: 500 };
  }
}
