/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable camelcase */
import { google } from "googleapis";
import * as admin from "firebase-admin";
import { FieldValue, Timestamp } from "firebase-admin/firestore";
import axios from "axios";
import { ByWeekday, Frequency, RRule, Weekday, WeekdayStr } from "rrule";
import { calendar_v3 } from "googleapis/build/src/apis/calendar";
import qs = require("qs");
import { z } from "zod";
import {
  Calendar,
  DayOfWeek,
  Event,
  RecurrencePatternType,
  User,
  WeekIndex,
} from "@microsoft/microsoft-graph-types-beta";
import { Client } from "@microsoft/microsoft-graph-client";
import { v4 as uuidv4 } from "uuid";
import { batchFetchImplementation } from "@jrmdayn/googleapis-batcher";
import {
  decryptTextData,
  encryptTextData,
  getNewSecret,
  encryptDocKey,
  getSecretFromKmsForUserByEmail,
  getUserKeyDocById,
  hashData,
} from "../../../utils/encryption/encryption";
import {
  convertDatesToTimestamps,
  getMeApiBaseDomain,
  getUserDbVersion,
  publishMessageToPubSub,
  // getUserDbVersion,
  // publishMessageToPubSub,
} from "../../../utils/utility_methods";
import {
  ZSyncRequestModel,
  IBaseSchema,
  dateSchema,
  ZDocCollectionName,
} from "../../migration/versions/base/base_schema";
import {
  IUser,
  ZCalDetails,
  ICalendarEventAction,
  ICalendarEventSetup,
  currentDbVersion,
  ICalDetailsType,
  ICalendarIntegrations,
  ZCalendarIntegrations,
} from "../../migration/versions/base/model_mappings";
import { meDateTimeObj } from "../../migration/versions/base/me_date_model";
import { MeLogger } from "../../../utils/logger/models/me_logger";
import { createSpecialActivitiesDoc } from "../../user/user_api/get_user";

export function getGoogleOAuth2Client(redirectUri: string | null = null) {
  const clientId = process.env.GOOGLE_CALENDAR_CLIENT_ID;
  const clientSecret = process.env.GOOGLE_CALENDAR_CLIENT_SECRET;
  const finalRedirectUri =
    redirectUri ?? `https://${getMeApiBaseDomain()}/me-calendar-auth`;
  return new google.auth.OAuth2(clientId, clientSecret, finalRedirectUri);
}

export function getMicrosoftClient(
  accessToken: string,
  isBeta?: boolean
): Client {
  return Client.init({
    authProvider: (done) => {
      done(null, accessToken);
    },
    defaultVersion: isBeta == true ? "beta" : "v1.0",
  });
}

const fetchImpl = batchFetchImplementation({ maxBatchSize: 50 });
export function getGoogleCalendarInstance() {
  return google.calendar({
    version: "v3",
    fetchImplementation: fetchImpl,
  });
}

export function getSingleGoogleCalendarInstance() {
  return google.calendar({
    version: "v3",
    // fetchImplementation: fetchImpl,
  });
}

async function startWebhookForCalGroup(
  logger: MeLogger,
  groupId: string | null,
  uid: string,
  type: string,
  calId: string,
  accessToken: string
) {
  let channelId = uuidv4();
  if (channelId.length > 64) {
    channelId = channelId.substring(0, 64);
  }
  channelId = channelId.replaceAll("@", "+");
  channelId = channelId.replaceAll(".", "_");

  if (type == "google") {
    const googleCalendarWebhookUrl = `https://www.googleapis.com/calendar/v3/calendars/${groupId}/events/watch`;
    const expiry = new Date(new Date().setDate(new Date().getDate() + 30));
    const data = {
      id: channelId, // Your channel ID.
      type: "web_hook",
      address: `https://${getMeApiBaseDomain()}/me-calendar-calendarwebhook`, // Your receiving URL.
      expiration: expiry.valueOf(), // (Optional) Your requested channel expiration time.
      token: `uid=${uid}?type=${type}?calId=${calId}`,
    };
    logger.log("post: " + JSON.stringify(data));
    try {
      // Perform the POST request to the Google Calendar Webhook
      const response = await axios.post(googleCalendarWebhookUrl, data, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });
      logger.log("response: " + JSON.stringify(response.data));
      return {
        status: 200,
        resourceId: response.data.resourceId,
        channelId: channelId,
      };
    } catch (e) {
      logger.error(
        "Calendar google webhook failed for " + channelId + " with error: " + e
      );
      return { status: 500 };
    }
  } else if (type == "microsoft") {
    const microsoftCalendarWebhookUrl =
      "https://graph.microsoft.com/v1.0/subscriptions";

    const expiry = new Date(
      new Date().setTime(new Date().getTime() + 167 * 60 * 60 * 1000)
    );

    const data = {
      changeType: "created,updated,deleted",
      notificationUrl: `https://${getMeApiBaseDomain()}/me-calendar-calendarwebhookms?cgId=${groupId}`,
      lifecycleNotificationUrl: `https://${getMeApiBaseDomain()}/me-calendar-calendarwebhookms?cgId=${groupId}`,
      resource: `/me/calendars/${groupId}/events`,
      expirationDateTime: expiry.toISOString(),
      clientState: `uid=${uid}?type=${type}?calId=${calId}`,
    };
    logger.log("post: " + JSON.stringify(data));
    try {
      // Perform the POST request to the Google Calendar Webhook
      const response = await axios.post(microsoftCalendarWebhookUrl, data, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });
      // console.log("MS WH Res: " + JSON.stringify(response.data));
      logger.log("response: " + JSON.stringify(response.data));
      return {
        status: 200,
        resourceId: response.data.applicationId,
        channelId: response.data.id,
      };
    } catch (e) {
      logger.error(
        "Calendar ms webhook failed for " + channelId + " with error: " + e
      );
      return { status: 500 };
    }
  } else if (type == "googleCalList") {
    const googleCalendarWebhookUrl =
      "https://www.googleapis.com/calendar/v3/users/me/calendarList/watch";
    // const expiry = new Date(new Date().setDate(new Date().getDate() + 30));
    const data = {
      id: channelId, // Your channel ID.
      type: "web_hook",
      address: `https://${getMeApiBaseDomain()}/me-calendar-calendarwebhook`, // Your receiving URL.
      // expiration: expiry.valueOf(), // (Optional) Your requested channel expiration time.
      token: `uid=${uid}?type=${type}?calId=${calId}`,
    };
    logger.log("post: " + JSON.stringify(data));
    try {
      // Perform the POST request to the Google Calendar Webhook
      const response = await axios.post(googleCalendarWebhookUrl, data, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });
      logger.log("response: " + JSON.stringify(response.data));
      return {
        status: 200,
        resourceId: response.data.resourceId,
        channelId: channelId,
      };
    } catch (e) {
      logger.error(
        "Calendar google webhook failed for " + channelId + " with error: " + e
      );
      return { status: 500 };
    }
  } else if (type == "microsoftCalList") {
    // const microsoftCalendarWebhookUrl = "https://graph.microsoft.com/v1.0/subscriptions";
    // const expiry = new Date(new Date().setDate(new Date().getDate() + 6));
    // // expiry = new Date(expiry.setDate(expiry.getMinutes() - 10));
    // const data = {
    //   changeType: "created,updated,deleted",
    //   notificationUrl: `https://${getMeApiBaseDomain()}/me-calendar-calendarwebhookms?cgId=${groupId}`,
    //   lifecycleNotificationUrl: `https://${getMeApiBaseDomain()}/me-calendar-calendarwebhookms`,
    //   resource: `/me/calendars/${groupId}/events`,
    //   expirationDateTime: expiry.toISOString(),
    //   clientState: `uid=${uid}?type=${type}?calId=${calId}`,
    // };
    // logger.log(JSON.stringify(data));
    // try {
    //   // Perform the POST request to the Google Calendar Webhook
    //   const response = await axios.post(microsoftCalendarWebhookUrl, data, {
    //     headers: {
    //       "Content-Type": "application/json",
    //       Authorization: `Bearer ${accessToken}`,
    //     },
    //   });
    //   // console.log("MS WH Res: " + JSON.stringify(response.data));
    //   return {
    //     status: 200,
    //     resourceId: response.data.applicationId,
    //     channelId: response.data.id,
    //   };
    // } catch (e) {
    //   logger.error(
    //     "Calendar ms webhook failed for " + channelId + " with error: " + e
    //   );
    //   return { status: 500 };
    // }
    return { status: 500 };
  }
  return { status: 500 };
}

export async function stopWebhookForCalGroup(
  logger: MeLogger,
  accessToken: string,
  channelId: string,
  resourceId: string | null,
  type: string
) {
  if (type == "google" || type == "googleCalList") {
    const googleCalendarWebhookUrl =
      "https://www.googleapis.com/calendar/v3/channels/stop";
    const webhookData = {
      id: channelId, // Your channel ID.
      resourceId: resourceId, // Your resource ID.
    };

    try {
      // Perform the POST request to the Google Calendar Webhook
      await axios.post(googleCalendarWebhookUrl, webhookData, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });
      logger.error(
        "Calendar webhook stopped successful for " + channelId + " with error: "
      );
    } catch (e) {
      logger.error(
        "Calendar webhook stop failed for " + channelId + " with error: " + e
      );
    }
  }
  if (type == "microsoft" || type == "microsoftCalList") {
    const microsoftCalendarWebhookUrl = `https://graph.microsoft.com/v1.0/subscriptions/${channelId}`;

    try {
      // Perform the POST request to the Google Calendar Webhook
      await axios.delete(microsoftCalendarWebhookUrl, {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
      });
    } catch (e) {
      logger.error(
        "Calendar MS webhook stop failed for " + channelId + " with error: " + e
      );
    }
  }
}

export async function getAccessToken(
  refreshToken: string,
  type: string,
  redirectUri?: string | null
): Promise<string | null> {
  try {
    if (type == "google") {
      const googleTokenUrl = "https://accounts.google.com/o/oauth2/token";
      const clientId = process.env.GOOGLE_CALENDAR_CLIENT_ID;
      const clientSecret = process.env.GOOGLE_CALENDAR_CLIENT_SECRET;
      const authLink = `https://${getMeApiBaseDomain()}/me-calendar-auth`;

      const getTokenData = {
        client_id: clientId,
        client_secret: clientSecret,
        redirect_uri: redirectUri ?? authLink,
        grant_type: "refresh_token",
        refresh_token: refreshToken,
      };

      const response = await axios.post(googleTokenUrl, getTokenData, {
        headers: {
          "Content-Type": "application/json",
        },
      });
      return response.data.access_token ?? null;
    } else {
      const microsoftTokenUrl =
        "https://login.microsoftonline.com/common/oauth2/v2.0/token";
      const data = qs.stringify({
        client_id:
          process.env.MICROSOFT_CLIENT_ID ??
          "6db47327-78e5-4aad-98c7-edd2e23b80a2",
        scope: "openid email offline_access Calendars.ReadWrite",
        refresh_token: refreshToken,
        grant_type: "refresh_token",
        client_secret:
          process.env.MICROSOFT_CLIENT_SECRET ??
          "****************************************",
      });
      const config = {
        method: "post",
        maxBodyLength: Infinity,
        url: microsoftTokenUrl,
        headers: { "content-type": "application/x-www-form-urlencoded" },
        data: data,
      };
      // Perform the POST request to get token
      let accessToken: string | null = null;
      await axios
        .request(config)
        .then((value) => {
          accessToken = value.data.access_token;
        })
        .catch((e) => {
          if (JSON.parse(JSON.stringify(e)).status == 400) {
            accessToken = "locked";
          }
          return null;
        });
      return accessToken ?? null;
    }
  } catch (error) {
    return null;
  }
}

// Function to fetch calendar events
export async function getCalendarDetailsHandler(
  logger: MeLogger,
  uid: string,
  id: string,
  email: string,
  refreshToken: string | null | undefined,
  serverAuthCode: string | null | undefined,
  accessToken: string | null | undefined,
  name: string | null | undefined,
  type: string,
  redirectUri: string | null | undefined
) {
  try {
    // ====== INITIALIZATION ======
    logger.log(
      "Calendar Details fetching calendars for user: " +
        uid +
        " and email: " +
        email
    );

    if (serverAuthCode != null) {
      const db = admin.firestore();

      // ====== GOOGLE CALENDAR INTEGRATION ======
      if (type == "google") {
        const oauth2Client = getGoogleOAuth2Client(redirectUri ?? null);
        const calendar = getGoogleCalendarInstance();

        // Get the access toke and refresh token if not provided
        if (refreshToken == null || accessToken == null) {
          try {
            const { tokens } = await oauth2Client.getToken(serverAuthCode);
            accessToken = accessToken ?? tokens.access_token ?? "";
            refreshToken = tokens.refresh_token ?? null;
            logger.log("Got the access and refresh token");
            logger.log(
              `Calendar Details: Token exchange complete, refresh token available: ${!!refreshToken} and access token available: ${!!accessToken}`
            );
          } catch (error) {
            const tokenError = error as Error;
            logger.log(`Calendar Details: Token exchange error: ${tokenError}`);
            return {
              statusCode: 500,
              error: "" + tokenError,
            };
          }
        }

        // Get email and name from Google if not provided (web OAuth flow)
        if (email == null || name == null) {
          try {
            oauth2Client.setCredentials({
              access_token: accessToken,
              refresh_token: refreshToken,
            });
            const oauth2 = google.oauth2({
              auth: oauth2Client,
              version: "v2",
            });
            const userInfoRes = await oauth2.userinfo.get();
            email = email ?? userInfoRes?.data.email ?? "";
            name = name ?? userInfoRes?.data.name ?? "";
            logger.log("Got email and name from Google userinfo API");
            logger.log(`Email: ${!!email}, Name: ${!!name}`);
          } catch (error) {
            const userInfoError = error as Error;
            logger.log(`Calendar Details: User info error: ${userInfoError}`);
            return {
              statusCode: 500,
              error: "" + userInfoError,
            };
          }
        }

        // Check account limits
        const count = (
          await db
            .collection("calendarIntegrations")
            .where("emailGroupHash", "==", hashData(type + email))
            .limit(10)
            .count()
            .get()
        ).data().count;

        if (count >= 10) {
          logger.log(
            `Calendar Details: Too many accounts (${count}) with same email`
          );
          return {
            statusCode: 429,
            body: "Too many accounts with same email",
          };
        }

        // Set up OAuth client
        try {
          oauth2Client.setCredentials({
            access_token: accessToken,
            // Note: Adding refresh_token is essential when re-integrating a previously deleted calendar
            // Without this, if a user:
            // 1. Deletes the calendar integration in the app
            // 2. Revokes access in Google Account settings
            // 3. Tries to re-integrate
            // Then the access_token will be accepted initially but will fail with "Invalid Credentials"
            // when making API calls because Google has invalidated previously issued tokens.
            // The refresh_token allows OAuth to automatically get a new valid token when needed.
            refresh_token: refreshToken,
          });
          logger.log("Access token set in oauth2Client credentials");
        } catch (error) {
          const credError = error as Error;
          logger.log(
            `Calendar Details: Error setting credentials: ${credError}`
          );
          return {
            statusCode: 500,
            error: "" + credError,
          };
        }

        // Fetch calendar list
        let calGroupsList;
        try {
          logger.log("Calendar Details: Requesting calendar list");
          calGroupsList = await calendar.calendarList.list({
            auth: oauth2Client,
            quotaUser: uid,
          });
          logger.log("Calendar Details fetch success");
        } catch (error) {
          const calError = error as Error;
          logger.log(`Calendar Details Error: ${calError}`);

          // Add specific error detection for debugging
          if (calError instanceof Error) {
            if (calError.message?.includes("invalid_grant")) {
              logger.log(
                "Calendar Details: Invalid grant error - token likely revoked or expired"
              );
            }

            if (calError.message?.includes("invalid_token")) {
              logger.log(
                "Calendar Details: Invalid token error - access token is invalid"
              );
            }
          }

          return {
            statusCode: 500,
            error: "" + calError,
          };
        }

        // Process calendar list
        if (
          calGroupsList?.data?.items != null &&
          calGroupsList.data.items.length > 0
        ) {
          logger.log(
            "Calendar Details List length: " +
              calGroupsList.data.items.length.toString()
          );

          const calGroups = [];
          let mevolveCalIndex = -1;
          const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;

          // Process each calendar
          for (let i = 0; i < calGroupsList.data.items.length; i++) {
            const calGroup = calGroupsList.data.items[i];
            const summary =
              calGroup.primary && calGroup.summary == email
                ? (name ?? "Primary")
                : calGroup.summary;

            if (summary == "Mevolve") {
              mevolveCalIndex = i;
              logger.log("Calendar Details: Found existing Mevolve calendar");
            }

            if (
              calGroup.id != null &&
              !summary?.includes("Holiday") &&
              !summary?.includes("Mevolve")
            ) {
              const calMap = {
                id: calGroup.id,
                title: summary,
                description: calGroup.description ?? null,
                nextSyncToken: null,
                nextPageToken: null,
                syncStatus: "syncNotifications",
                resourceId: null,
                channelId: null,
              };
              calGroups.push(calMap);
            }
          }

          // Create or use existing Mevolve calendar
          if (mevolveCalIndex == -1) {
            try {
              logger.log("Calendar Details: Creating new Mevolve calendar");
              const calGroup = await calendar.calendars.insert({
                auth: oauth2Client,
                requestBody: {
                  summary: "Mevolve",
                  timeZone: calGroupsList.data.items[0].timeZone,
                },
                quotaUser: uid,
              });

              const calMap = {
                id: calGroup.data.id,
                title: "Mevolve",
                description: calGroup.data.description ?? null,
                nextSyncToken: null,
                nextPageToken: null,
                syncStatus: "disabled",
                resourceId: null,
                channelId: null,
              };
              calGroups.unshift(calMap);
              logger.log(
                `Calendar Details: Created new Mevolve calendar with ID: ${calGroup.data.id}`
              );
            } catch (error) {
              const createError = error as Error;
              logger.log(
                `Calendar Details: Error creating Mevolve calendar: ${createError}`
              );
              // Continue even if Mevolve calendar creation fails
            }
          } else {
            const mevolveMap = {
              id: calGroupsList.data.items[mevolveCalIndex].id,
              title: calGroupsList.data.items[mevolveCalIndex].summary,
              description:
                calGroupsList.data.items[mevolveCalIndex].description ?? null,
              nextSyncToken: null,
              nextPageToken: null,
              syncStatus: "disabled",
              resourceId: null,
              channelId: null,
            };
            calGroups.unshift(mevolveMap);
            logger.log("Calendar Details: Using existing Mevolve calendar");
          }

          // Prepare data for storage
          const userPublicKey = (await getUserKeyDocById(uid))?.publicKey;
          const secret = getNewSecret();
          const encData = {
            dek: await encryptDocKey(secret, uid, userPublicKey),
            encFields: ["name"],
          };

          // Encrypt sensitive data
          const accessTokenEnc = encryptTextData(accessToken, userSecret);
          const refreshTokenEnc =
            refreshToken != null
              ? encryptTextData(refreshToken, userSecret)
              : null;

          // Create calendar integration object
          const calDetMap = {
            calendarType: type,
            accessToken: accessTokenEnc,
            refreshToken: refreshTokenEnc,
            localUpdatedAt: Timestamp.now(),
            cloudUpdatedAt: Timestamp.now(),
            deletedAt: null,
            permaDeletedAt: null,
            id: id,
            uid: uid,
            email: email,
            emailHash: hashData(uid + type + email),
            emailGroupHash: hashData(type + email),
            name: name,
            status: "active",
            webhookExpiry: null,
            lastSync: Timestamp.now(),
            calendarGroups: calGroups,
            syncTodos: "syncNotifications",
            syncHabits: "syncNotifications",
            syncJournals: "syncNotifications",
            encData: encData,
            resyncLimit: 0,
            resourceId: null,
            channelId: null,
            mevolveStatus: "active",
          };

          logger.log(
            "Calendar Details fetch success for " +
              (calGroups.length ?? 0).toString() +
              " groups for user: " +
              uid +
              " and email: " +
              email
          );

          return {
            statusCode: 200,
            body: "Success",
            data: JSON.stringify(calDetMap),
          };
        } else {
          logger.log("Calendar Details: No calendars found");
          return {
            statusCode: 200,
            body: "Success",
          };
        }
      }
      // ====== MICROSOFT CALENDAR INTEGRATION ======
      else if (type == "microsoft") {
        const microsoftTokenUrl =
          "https://login.microsoftonline.com/common/oauth2/v2.0/token";

        // Prepare token request data
        const data = qs.stringify({
          client_id:
            process.env.MICROSOFT_CLIENT_ID ??
            "6db47327-78e5-4aad-98c7-edd2e23b80a2",
          scope: "openid email User.Read offline_access Calendars.ReadWrite",
          code: serverAuthCode,
          redirect_uri:
            redirectUri ?? `https://${getMeApiBaseDomain()}/me-calendar-auth`,
          grant_type: "authorization_code",
          client_secret:
            process.env.MICROSOFT_CLIENT_SECRET ??
            "****************************************",
        });

        const config = {
          method: "post",
          maxBodyLength: Infinity,
          url: microsoftTokenUrl,
          headers: { "content-type": "application/x-www-form-urlencoded" },
          data: data,
        };

        try {
          // Get Microsoft tokens
          logger.log("Calendar Details: Microsoft - Requesting tokens");
          const tokenResponse = await axios.request(config);
          accessToken = tokenResponse.data.access_token ?? "";
          refreshToken = tokenResponse.data.refresh_token;
          logger.log("Calendar Details: Microsoft - Received tokens");
        } catch (error) {
          const tokenError = error as Error;
          logger.log(`Calendar Details: Microsoft token error: ${tokenError}`);
          return {
            statusCode: 500,
            error: "" + tokenError,
          };
        }

        // Use Microsoft Graph API
        if (accessToken != null) {
          try {
            logger.log(
              "Calendar Details: Microsoft - Initializing Graph client"
            );
            const client = getMicrosoftClient(accessToken);

            // Get user information
            const userResponse = await client.api("/me").get();
            name = (userResponse as User).displayName ?? "";
            email = (userResponse as User).mail ?? "";
            logger.log(
              `Calendar Details: Microsoft - User info: ${name}, ${email}`
            );

            // Check account limits
            const count = (
              await db
                .collection("calendarIntegrations")
                .where("emailGroupHash", "==", hashData(type + email))
                .limit(10)
                .count()
                .get()
            ).data().count;

            if (count >= 10) {
              logger.log(
                `Calendar Details: Microsoft - Too many accounts (${count}) with same email`
              );
              return {
                statusCode: 429,
                body: "Too many accounts with same email",
              };
            }

            // Get calendars list
            logger.log("Calendar Details: Microsoft - Requesting calendars");
            const calendarsListResponse = await client
              .api(
                "/me/calendars?$select=id,name,color,changeKey,canEdit,owner"
              )
              .get();
            const calendarsList = calendarsListResponse.value as Calendar[];

            if (calendarsList != null && calendarsList.length > 0) {
              logger.log(
                "Calendar Details Group List length: " +
                  calendarsList.length.toString()
              );

              const calGroups = [];
              let mevolveCalIndex = -1;
              const userSecret = (await getSecretFromKmsForUserByEmail(uid))
                .key;

              // Process each calendar
              for (let i = 0; i < calendarsList.length; i++) {
                const calGroup = calendarsList[i];
                const title =
                  calGroup.name != null
                    ? calGroup.name == email
                      ? "Calendar"
                      : calGroup.name
                    : "";

                if (title == "Mevolve") {
                  mevolveCalIndex = i;
                  logger.log(
                    "Calendar Details: Microsoft - Found existing Mevolve calendar"
                  );
                }

                if (
                  calGroup.id != null &&
                  !title?.includes("holidays") &&
                  !title?.includes("Mevolve")
                ) {
                  const calMap = {
                    id: calGroup.id,
                    title: title,
                    description: null,
                    nextSyncToken: null,
                    nextPageToken: null,
                    syncStatus: "syncNotifications",
                    resourceId: null,
                    channelId: null,
                  };
                  calGroups.push(calMap);
                }
              }

              // Create or use existing Mevolve calendar
              if (mevolveCalIndex == -1) {
                try {
                  logger.log(
                    "Calendar Details: Microsoft - Creating new Mevolve calendar"
                  );
                  const calGroup = (await client
                    .api("/me/calendars")
                    .post({ name: "Mevolve" })) as Calendar;

                  const calMap = {
                    id: calGroup.id,
                    title: "Mevolve",
                    description: null,
                    nextSyncToken: null,
                    nextPageToken: null,
                    syncStatus: "disabled",
                    resourceId: null,
                    channelId: null,
                  };
                  calGroups.unshift(calMap);
                  logger.log(
                    "Calendar Details: Microsoft - Created new Mevolve calendar"
                  );
                } catch (error) {
                  const createError = error as Error;
                  logger.log(
                    `Calendar Details: Microsoft - Error creating Mevolve calendar: ${createError}`
                  );
                  // Continue even if Mevolve calendar creation fails
                }
              } else {
                const mevolveMap = {
                  id: calendarsList[mevolveCalIndex].id,
                  title: "Mevolve",
                  description: null,
                  nextSyncToken: null,
                  nextPageToken: null,
                  syncStatus: "disabled",
                  resourceId: null,
                  channelId: null,
                };
                calGroups.unshift(mevolveMap);
                logger.log(
                  "Calendar Details: Microsoft - Using existing Mevolve calendar"
                );
              }

              // Prepare data for storage
              const userPublicKey = (await getUserKeyDocById(uid))?.publicKey;
              const secret = getNewSecret();
              const encData = {
                dek: await encryptDocKey(secret, uid, userPublicKey),
                encFields: ["name"],
              };

              // Encrypt sensitive data
              const accessTokenEnc = encryptTextData(accessToken, userSecret);
              const refreshTokenEnc =
                refreshToken != null
                  ? encryptTextData(refreshToken, userSecret)
                  : null;

              // Create calendar integration object
              const calDetMap = {
                calendarType: type,
                accessToken: accessTokenEnc,
                refreshToken: refreshTokenEnc,
                localUpdatedAt: Timestamp.now(),
                cloudUpdatedAt: Timestamp.now(),
                deletedAt: null,
                permaDeletedAt: null,
                id: id,
                uid: uid,
                email: email,
                emailHash: hashData(uid + type + email),
                emailGroupHash: hashData(type + email),
                name: name,
                status: "active",
                webhookExpiry: null,
                lastSync: Timestamp.now(),
                calendarGroups: calGroups,
                syncTodos: "syncNotifications",
                syncHabits: "syncNotifications",
                syncJournals: "syncNotifications",
                encData: encData,
                resyncLimit: 0,
                resourceId: null,
                channelId: null,
                mevolveStatus: "active",
              };

              logger.log(
                "Calendar Details fetch success for " +
                  (calGroups.length ?? 0).toString() +
                  " groups for user: " +
                  uid +
                  " and email: " +
                  email
              );

              return {
                statusCode: 200,
                body: "Success",
                data: JSON.stringify(calDetMap),
              };
            }
          } catch (error) {
            const graphError = error as Error;
            logger.log(
              `Calendar Details: Microsoft Graph error: ${graphError}`
            );
            return {
              statusCode: 500,
              error: "" + graphError,
            };
          }
        }
      }
    }

    // Default return if no integration performed
    return {
      statusCode: 200,
      body: "Success",
    };
  } catch (error) {
    const finalError = error as Error;
    logger.log(`Calendar Details Error ${finalError}`);

    return {
      statusCode: 500,
      error: "" + finalError,
    };
  }
}

export async function resubCalendarWebhooksHandler(
  logger: MeLogger,
  uid: string,
  calId: string
) {
  try {
    logger.log(
      `Resub Calendar Webhooks start for user: ${uid} and cal: ${calId}`
    );
    const db = admin.firestore();
    const calData = await db
      .collection("calendarIntegrations")
      .doc(calId)
      .get();
    if (calData.exists) {
      const calendarIntegrations = ZCalDetails.parse(calData.data() as unknown);
      const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
      const type = calendarIntegrations.calendarType;
      const tasksInsert: unknown[] = [];
      if (
        calendarIntegrations != null &&
        calendarIntegrations.refreshToken != null
      ) {
        const refreshToken = decryptTextData(
          calendarIntegrations.refreshToken,
          userSecret
        );
        const accessToken = await getAccessToken(refreshToken, type);
        if (accessToken != null) {
          logger.log(
            "Resub Calendar Webhooks resubbing for user: " +
              uid +
              " and calId: " +
              calendarIntegrations.id
          );
          if (type == "google") {
            for (
              let i = 0;
              i < calendarIntegrations.calendarGroups.length;
              i++
            ) {
              const calGroup = calendarIntegrations.calendarGroups[i];

              if (calGroup.channelId != null && calGroup.resourceId != null) {
                tasksInsert.push(
                  stopWebhookForCalGroup(
                    logger,
                    accessToken,
                    calGroup.channelId,
                    calGroup.resourceId,
                    type
                  ).catch((_) => {
                    null;
                  })
                );
              }
              if (calGroup.syncStatus != "disabled") {
                const response = await startWebhookForCalGroup(
                  logger,
                  calGroup.id,
                  uid,
                  type,
                  calendarIntegrations.id,
                  accessToken
                );
                calendarIntegrations.calendarGroups[i].resourceId =
                  response.status == 200 ? (response.resourceId ?? null) : null;
                calendarIntegrations.calendarGroups[i].channelId =
                  response.status == 200 ? (response.channelId ?? null) : null;
              }
            }
          } else {
            for (
              let i = 0;
              i < calendarIntegrations.calendarGroups.length;
              i++
            ) {
              const calGroup = calendarIntegrations.calendarGroups[i];
              if (calGroup.channelId != null) {
                tasksInsert.push(
                  stopWebhookForCalGroup(
                    logger,
                    accessToken,
                    calGroup.channelId,
                    calGroup.resourceId,
                    type
                  ).catch((_) => {
                    null;
                  })
                );
              }
              if (calGroup.syncStatus != "disabled") {
                const response = await startWebhookForCalGroup(
                  logger,
                  calGroup.id,
                  uid,
                  type,
                  calendarIntegrations.id,
                  accessToken
                );
                calendarIntegrations.calendarGroups[i].resourceId =
                  response.status == 200 ? (response.resourceId ?? null) : null;
                calendarIntegrations.calendarGroups[i].channelId =
                  response.status == 200 ? (response.channelId ?? null) : null;
              }
            }
          }
          const expiry = new Date();
          expiry.setDate(expiry.getDate() + (type == "google" ? 28 : 6));
          await db
            .collection("calendarIntegrations")
            .doc(calendarIntegrations.id)
            .set(
              {
                calendarGroups: calendarIntegrations.calendarGroups,
                localUpdatedAt: Timestamp.now(),
                cloudUpdatedAt: Timestamp.now(),
                webhookExpiry: Timestamp.fromDate(expiry),
              },
              { merge: true }
            );
        } else {
          return {
            statusCode: 400,
            error:
              "Resub Calendar Webhooks failed for user: " +
              uid +
              " and calId: " +
              calendarIntegrations.id +
              " with wrong credentials",
          };
        }
        if (tasksInsert.length > 0) {
          await Promise.allSettled(tasksInsert);
        }
      }

      logger.log(
        "Resub Calendar Webhooks successful for user: " +
          uid +
          " and calId: " +
          calendarIntegrations.id
      );
      return { statusCode: 200 };
    }
    logger.log("Delete All Calendars success for user: " + uid);
    return {
      statusCode: 400,
      error: "Delete All Calendars failed: user not found",
    };
  } catch (e) {
    logger.log("Delete All Calendars failed with error: " + e);
    return { statusCode: 500 };
  }
}

export async function deleteAllCalendarsHandler(
  logger: MeLogger,
  uid: string,
  redirectUri: string | null
) {
  try {
    logger.log(`Delete All Calendars start for ${uid}`);
    const db = admin.firestore();
    const data = await db.collection("users").doc(uid).get();
    const calData = await db
      .collection("calendarIntegrations")
      .where("uid", "==", uid)
      .where("deletedAt", "==", null)
      .get();
    if (!calData.empty) {
      const calList = calData.docs.map((doc) =>
        ZCalDetails.parse(doc.data() as unknown)
      );
      const userData = data.exists ? (data.data() as unknown as IUser) : null;
      for (let i = 0; i < calList.length; i++) {
        const batch = db.batch();
        const calendarAccountRemoveIds: Set<string> = new Set();

        const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;

        const calendarIntegrations = calList[i];
        const type = calendarIntegrations.calendarType;
        const tasksInsert: unknown[] = [];
        logger.log(
          "Delete All Calendars deleting calendar " +
            calendarIntegrations.id +
            " for user: " +
            uid
        );

        await db
          .collection("calendarIntegrations")
          .doc(calendarIntegrations.id)
          .set(
            {
              status: "deletingInProgress",
              localUpdatedAt: Timestamp.now(),
              cloudUpdatedAt: Timestamp.now(),
            },
            { merge: true }
          );

        if (
          calendarIntegrations != null &&
          calendarIntegrations.refreshToken != null
        ) {
          const delCalendarIntegrations = {
            ...calendarIntegrations,
            calendarType: calendarIntegrations.calendarType,
            accessToken: "",
            refreshToken: null,
            id: calendarIntegrations.id,
            name: "",
            email: "",
            emailHash: "",
            emailGroupHash: "",
            expiry: null,
            lastSync: null,
            calendarGroups: [],
            syncTodos: "disabled",
            syncHabits: "disabled",
            syncJournals: "disabled",
            status: "deleted",
          };

          batch.set(
            db.collection("calendarIntegrations").doc(calendarIntegrations.id),
            {
              ...delCalendarIntegrations,
              localUpdatedAt: Timestamp.now(),
              cloudUpdatedAt: Timestamp.now(),
              deletedAt: Timestamp.now(),
              permaDeletedAt: Timestamp.now(),
            },
            { merge: true }
          );
          if (userData != null) {
            const removeId =
              type == "google"
                ? `g_${calendarIntegrations.email}`
                : `m_${calendarIntegrations.email}`;

            calendarAccountRemoveIds.add(removeId);
          }

          await createSpecialActivitiesDoc(
            uid,
            undefined,
            true,
            delCalendarIntegrations.docVer,
            "calendarAccountIds",
            Array.from(calendarAccountRemoveIds),
            batch
          );

          tasksInsert.push(batch.commit());

          const refreshToken = decryptTextData(
            calendarIntegrations.refreshToken,
            userSecret
          );
          const accessToken = await getAccessToken(
            refreshToken,
            type,
            redirectUri
          );
          if (accessToken != null) {
            if (type == "google") {
              if (
                calendarIntegrations.channelId != null &&
                calendarIntegrations.resourceId != null
              ) {
                tasksInsert.push(
                  stopWebhookForCalGroup(
                    logger,
                    accessToken,
                    calendarIntegrations.channelId,
                    calendarIntegrations.resourceId,
                    "googleCalList"
                  ).catch((_) => {
                    null;
                  })
                );
              }
              for (
                let i = 0;
                i < calendarIntegrations.calendarGroups.length;
                i++
              ) {
                const calGroup = calendarIntegrations.calendarGroups[i];

                if (calGroup.channelId != null && calGroup.resourceId != null) {
                  tasksInsert.push(
                    stopWebhookForCalGroup(
                      logger,
                      accessToken,
                      calGroup.channelId,
                      calGroup.resourceId,
                      type
                    ).catch((_) => {
                      null;
                    })
                  );
                }
              }
            } else {
              for (
                let i = 0;
                i < calendarIntegrations.calendarGroups.length;
                i++
              ) {
                const calGroup = calendarIntegrations.calendarGroups[i];
                if (calGroup.channelId != null) {
                  tasksInsert.push(
                    stopWebhookForCalGroup(
                      logger,
                      accessToken,
                      calGroup.channelId,
                      calGroup.resourceId,
                      type
                    ).catch((_) => {
                      null;
                    })
                  );
                }
              }
            }

            // Old

            if (type == "google") {
              const oauth2Client = getGoogleOAuth2Client(redirectUri ?? null);
              const calendar = getGoogleCalendarInstance();

              oauth2Client.setCredentials({
                access_token: accessToken,
              });
              const mevolveIndex =
                calendarIntegrations.calendarGroups.findIndex(
                  (group) => group.title == "Mevolve"
                );
              const mevolveGroup =
                mevolveIndex == -1
                  ? null
                  : calendarIntegrations.calendarGroups[mevolveIndex];

              if (mevolveGroup != null) {
                tasksInsert.push(
                  calendar.calendarList
                    .delete({
                      calendarId: mevolveGroup.id,
                      auth: oauth2Client,
                    })
                    .catch((_) => {
                      null;
                    })
                );
              }
            } else {
              const client = getMicrosoftClient(accessToken);
              const mevolveIndex =
                calendarIntegrations.calendarGroups.findIndex(
                  (group) => group.title == "Mevolve"
                );
              const mevolveGroup =
                mevolveIndex == -1
                  ? null
                  : calendarIntegrations.calendarGroups[mevolveIndex];
              if (mevolveGroup != null) {
                tasksInsert.push(
                  client
                    .api(`/me/calendars/${mevolveGroup.id}`)
                    .delete()
                    .catch((_) => {
                      null;
                    })
                );
              }
            }
          } else {
            return {
              statusCode: 400,
              error:
                "Delete All Calendars: wrong credentails, forcefully deleting for: " +
                calendarIntegrations.id +
                " for user: " +
                uid +
                "wrong credentials",
            };
          }
          if (tasksInsert.length > 0) {
            await Promise.allSettled(tasksInsert);
          }
          logger.log(
            "Delete All Calendars deleted calendar " +
              calendarIntegrations.id +
              " for user: " +
              uid
          );
        }
      }
      logger.log("Delete All Calendars success for user: " + uid);
      return { statusCode: 200 };
    }
    logger.log("Delete All Calendars success for user: " + uid);
    return {
      statusCode: 400,
      error: "Delete All Calendars failed: user not found",
    };
  } catch (e) {
    logger.log("Delete All Calendars failed with error: " + e);
    return { statusCode: 500 };
  }
}

export async function deleteCalendarHandler(
  logger: MeLogger,
  uid: string,
  calId: string,
  dataList: string,
  type: string,
  isResync?: boolean,
  eventList?: string,
  redirectUri?: string | null
) {
  const db = admin.firestore();
  try {
    const batch = db.batch();
    const data = await db.collection("users").doc(uid).get();
    const calData = await db
      .collection("calendarIntegrations")
      .doc(calId)
      .get();
    if (data.exists) {
      if (!isResync) {
        await db.collection("calendarIntegrations").doc(calId).set(
          {
            status: "deletingInProgress",
            localUpdatedAt: Timestamp.now(),
            cloudUpdatedAt: Timestamp.now(),
          },
          { merge: true }
        );
      }
      const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
      const calendarIntegrations = ZCalDetails.parse(calData.data() as unknown);
      logger.log(
        isResync
          ? "Resync calendar: clearing " + calId + " for user: " + uid
          : "Deleting calendar " + calId + " for user: " + uid
      );
      if (
        calendarIntegrations != null &&
        calendarIntegrations.refreshToken != null
      ) {
        const refreshToken = decryptTextData(
          calendarIntegrations.refreshToken,
          userSecret
        );
        const accessToken = await getAccessToken(
          refreshToken,
          type,
          redirectUri
        );
        if (accessToken != null) {
          if (type == "google") {
            if (
              calendarIntegrations.channelId != null &&
              calendarIntegrations.resourceId != null
            ) {
              await stopWebhookForCalGroup(
                logger,
                accessToken,
                calendarIntegrations.channelId,
                calendarIntegrations.resourceId,
                "googleCalList"
              ).catch((_) => {
                null;
              });
            }
            for (
              let i = 0;
              i < calendarIntegrations.calendarGroups.length;
              i++
            ) {
              const calGroup = calendarIntegrations.calendarGroups[i];

              if (calGroup.channelId != null && calGroup.resourceId != null) {
                await stopWebhookForCalGroup(
                  logger,
                  accessToken,
                  calGroup.channelId,
                  calGroup.resourceId,
                  type
                ).catch((_) => {
                  null;
                });
              }
            }
          } else {
            for (
              let i = 0;
              i < calendarIntegrations.calendarGroups.length;
              i++
            ) {
              const calGroup = calendarIntegrations.calendarGroups[i];
              if (calGroup.channelId != null) {
                await stopWebhookForCalGroup(
                  logger,
                  accessToken,
                  calGroup.channelId,
                  calGroup.resourceId,
                  type
                ).catch((_) => {
                  null;
                });
              }
            }
          }
          if (isResync != true) {
            const delCalendarIntegrations = {
              ...calendarIntegrations,
              calendarType: calendarIntegrations.calendarType,
              accessToken: "",
              refreshToken: null,
              id: calendarIntegrations.id,
              name: "",
              email: "",
              emailHash: "",
              emailGroupHash: "",
              expiry: null,
              lastSync: null,
              calendarGroups: [],
              syncTodos: "disabled",
              syncHabits: "disabled",
              syncJournals: "disabled",
              status: "deleted",
            };

            const removeId =
              type == "google"
                ? `g_${calendarIntegrations.email}`
                : `m_${calendarIntegrations.email}`;

            await createSpecialActivitiesDoc(
              uid,
              undefined,
              true,
              delCalendarIntegrations.docVer,
              "calendarAccountIds",
              [removeId],
              batch
            );

            batch.set(
              db
                .collection("calendarIntegrations")
                .doc(calendarIntegrations.id),
              {
                ...delCalendarIntegrations,
                localUpdatedAt: Timestamp.now(),
                cloudUpdatedAt: Timestamp.now(),
                deletedAt: Timestamp.now(),
                permaDeletedAt: Timestamp.now(),
              },
              { merge: true }
            );
          }
          const tasksInsert: unknown[] = [];
          const tasksUpdate: unknown[] = [];
          if (type == "google") {
            const oauth2Client = getGoogleOAuth2Client(redirectUri ?? null);
            const calendar = getGoogleCalendarInstance();

            oauth2Client.setCredentials({
              access_token: accessToken,
              refresh_token: refreshToken,
            });
            const mevolveIndex = calendarIntegrations.calendarGroups.findIndex(
              (group) => group.title == "Mevolve"
            );
            const mevolveGroup =
              mevolveIndex == -1
                ? null
                : calendarIntegrations.calendarGroups[mevolveIndex];

            if (dataList != null && dataList.length > 0) {
              for (const syncData of dataList) {
                let dataReceived;
                if (isResync == true) {
                  const parsedSyncData = ZSyncRequestModel.parse(syncData);
                  dataReceived = ZDeleteSchema.parse(
                    JSON.parse(parsedSyncData.data)
                  );
                } else {
                  dataReceived = ZDeleteSchema.parse(syncData);
                }
                if (
                  dataReceived.docCollection == "calendarEventActions" ||
                  dataReceived.docCollection == "calendarEventSetups"
                ) {
                  const docRef = db
                    .collection(dataReceived.docCollection)
                    .doc(dataReceived.id);
                  batch.delete(docRef);
                } else if (mevolveGroup != null) {
                  const event = {
                    id: dataReceived.id.replace(/-/g, ""),
                  };
                  tasksUpdate.push(
                    calendar.events
                      .delete({
                        eventId: event.id ?? "",
                        calendarId: mevolveGroup.id,
                        auth: oauth2Client,
                      })
                      .catch((_) => {
                        null;
                      })
                  );
                }
              }
            }
          } else {
            const client = getMicrosoftClient(accessToken);

            const mevolveIndex = calendarIntegrations.calendarGroups.findIndex(
              (group) => group.title == "Mevolve"
            );

            const mevolveGroup =
              mevolveIndex == -1
                ? null
                : calendarIntegrations.calendarGroups[mevolveIndex];

            for (const syncData of dataList) {
              let dataReceived;
              if (isResync == true) {
                const parsedSyncData = ZSyncRequestModel.parse(syncData);
                dataReceived = ZDeleteSchema.parse(
                  JSON.parse(parsedSyncData.data)
                );
              } else {
                dataReceived = ZDeleteSchema.parse(syncData);
              }
              if (
                dataReceived.docCollection == "calendarEventActions" ||
                dataReceived.docCollection == "calendarEventSetups"
              ) {
                const docRef = db
                  .collection(dataReceived.docCollection)
                  .doc(dataReceived.id);
                batch.delete(docRef);
              } else if (mevolveGroup != null) {
                const event = {
                  transactionId: dataReceived.id.replace(/-/g, ""),
                };
                tasksInsert.push(
                  client
                    .api(`/me/calendars/${mevolveGroup.id}/events`)
                    .post(event)
                    .then((res) => {
                      tasksUpdate.push(
                        client
                          .api(
                            `/me/calendars/${mevolveGroup.id}/events/${res.id}`
                          )
                          .delete()
                          .catch((_) => {
                            null;
                          })
                      );
                    })
                    .catch((e) => {
                      if (e.code == "ErrorDuplicateTransactionId") {
                        const id = JSON.parse(e.body)["@event.existingEventId"];
                        tasksUpdate.push(
                          client
                            .api(
                              `/me/calendars/${mevolveGroup.id}/events/${id}`
                            )
                            .delete()
                            .catch((_) => {
                              null;
                            })
                        );
                      } else {
                        logger.log("MS event error: " + JSON.stringify(e));
                      }
                    })
                );
              }
            }
          }
          if (isResync == true && eventList != null) {
            for (const syncData of eventList) {
              let dataReceived;
              if (isResync == true) {
                const parsedSyncData = ZSyncRequestModel.parse(syncData);
                dataReceived = ZDeleteSchema.parse(
                  JSON.parse(parsedSyncData.data)
                );
              } else {
                dataReceived = ZDeleteSchema.parse(syncData);
              }
              const docRef = db
                .collection(dataReceived.docCollection)
                .doc(dataReceived.id);
              batch.delete(docRef);
            }
          }
          await batch.commit();
          if (tasksInsert.length > 0) {
            await Promise.allSettled(tasksInsert);
          }
          if (tasksUpdate.length > 0) {
            await Promise.allSettled(tasksUpdate);
          }
          logger.log(
            isResync
              ? "Resync calendar: Cleared " + calId + " for user: " + uid
              : "Deleted calendar " + calId + " for user: " + uid
          );
          return { statusCode: 200 };
        } else {
          logger.log(
            isResync
              ? "Resync calendar: Clearing failed: wrong credentials " +
                  calId +
                  " for user: " +
                  uid
              : "Deleted calendar: wrong credentials, forcefully deleting calendar for " +
                  calId +
                  " for user: " +
                  uid
          );
          if (!isResync) {
            const delCalendarIntegrations = {
              ...calendarIntegrations,
              calendarType: calendarIntegrations.calendarType,
              accessToken: "",
              refreshToken: null,
              id: calendarIntegrations.id,
              name: "",
              email: "",
              emailHash: "",
              emailGroupHash: "",
              expiry: null,
              lastSync: null,
              calendarGroups: [],
              syncTodos: "disabled",
              syncHabits: "disabled",
              syncJournals: "disabled",
              status: "deleted",
            };

            const removeId =
              type == "google"
                ? `g_${calendarIntegrations.email}`
                : `m_${calendarIntegrations.email}`;

            await createSpecialActivitiesDoc(
              uid,
              undefined,
              true,
              delCalendarIntegrations.docVer,
              "calendarAccountIds",
              [removeId],
              batch
            );

            batch.set(
              db
                .collection("calendarIntegrations")
                .doc(calendarIntegrations.id),
              {
                ...delCalendarIntegrations,
                localUpdatedAt: Timestamp.now(),
                cloudUpdatedAt: Timestamp.now(),
                deletedAt: Timestamp.now(),
                permaDeletedAt: Timestamp.now(),
              },
              { merge: true }
            );
            if (dataList != null && dataList.length > 0) {
              for (const syncData of dataList) {
                const dataReceived = ZDeleteSchema.parse(syncData);
                if (
                  dataReceived.docCollection == "calendarEventActions" ||
                  dataReceived.docCollection == "calendarEventSetups"
                ) {
                  const docRef = db
                    .collection(dataReceived.docCollection)
                    .doc(dataReceived.id);
                  batch.delete(docRef);
                }
              }
            }
            await batch.commit();
            // await db.collection("calendarIntegrations").doc(calId).set({
            //   status: "active",
            //   localUpdatedAt: Timestamp.now(),
            //   cloudUpdatedAt: Timestamp.now(),
            // }, { merge: true });
          }
          if (isResync) {
            return {
              statusCode: 400,
              error: "Resync calendar: Clearing failed: wrong credentials",
            };
          } else {
            logger.log("Deleted calendar " + calId + " for user: " + uid);
            return { statusCode: 200 };
          }
        }
      }
    }
    logger.log(
      isResync
        ? "Resync calendar: Clearing failed: user not found " +
            calId +
            " for user: " +
            uid
        : "Deleted calendar failed: user not found" +
            calId +
            " for user: " +
            uid
    );
    if (!isResync) {
      await db.collection("calendarIntegrations").doc(calId).set(
        {
          status: "active",
          localUpdatedAt: Timestamp.now(),
          cloudUpdatedAt: Timestamp.now(),
        },
        { merge: true }
      );
    }
    return {
      statusCode: 400,
      error: isResync
        ? "Resync calendar: Clearing failed: user not found"
        : "Delete calendar failed: user not found",
    };
  } catch (e) {
    logger.log(
      isResync
        ? "Resync calendar: Clearing failed with error: " + e
        : "Delete calendar failed with error: " + e
    );
    // const calData = await db.collection("calendarIntegrations").doc(calId).get();
    if (!isResync) {
      await db.collection("calendarIntegrations").doc(calId).set(
        {
          status: "active",
          localUpdatedAt: Timestamp.now(),
          cloudUpdatedAt: Timestamp.now(),
        },
        { merge: true }
      );
    }

    return { statusCode: 500 };
  }
}

export async function saveNewCalendarHandler(
  logger: MeLogger,
  uid: string,
  calendarDetails: string,
  calAccId: string,
  mevolveCalId: string | null,
  refreshKey: string | null,
  timeZone: string | null,
  eventsList: string | null,
  tasksList: string | null,
  type: string | null,
  calendarConfig: string | null,
  redirectUri: string | null
) {
  logger.log("Saving New Calendar for: " + uid + " and calId: " + calAccId);
  try {
    const currentCalDetails = convertDatesToTimestamps(
      ZCalDetails.parse(JSON.parse(calendarDetails))
    );
    const db = admin.firestore();
    const batch = db.batch();
    let response;
    const data = await db.collection("users").doc(uid).get();
    const checkExists = await db
      .collection("calendarIntegrations")
      .where("emailHash", "==", currentCalDetails.emailHash)
      .limit(1)
      .get();
    if (data.exists) {
      if (checkExists.empty) {
        if (currentCalDetails.calendarType == "google") {
          const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
          const refreshKey = currentCalDetails.refreshToken ?? "";
          const refreshToken = decryptTextData(refreshKey, userSecret);
          const accessToken = await getAccessToken(
            refreshToken,
            currentCalDetails.calendarType,
            redirectUri
          );
          if (accessToken != null) {
            response = await startWebhookForCalGroup(
              logger,
              null,
              uid,
              "googleCalList",
              calAccId,
              accessToken
            );
          }
        }
        const calEmailId =
          currentCalDetails.calendarType == "google"
            ? `g_${currentCalDetails.email}`
            : `m_${currentCalDetails.email}`;

        const expiry = new Date();
        expiry.setDate(expiry.getDate() + (type == "google" ? 28 : 6));
        if (
          currentCalDetails.webhookExpiry == null ||
          currentCalDetails.webhookExpiry > expiry
        ) {
          currentCalDetails.webhookExpiry = expiry;
        }
        batch.set(db.collection("calendarIntegrations").doc(calAccId), {
          ...currentCalDetails,
          localUpdatedAt: Timestamp.now(),
          cloudUpdatedAt: Timestamp.now(),
          lastSync: Timestamp.now(),
          resourceId:
            response != null && response.status == 200
              ? (response.resourceId ?? null)
              : null,
          channelId:
            response != null && response.status == 200
              ? (response.channelId ?? null)
              : null,
          webhookExpiry: type == "google" ? expiry : null,
        });

        await batch.commit();

        await createSpecialActivitiesDoc(
          uid,
          undefined,
          true,
          currentCalDetails.docVer,
          "calendarAccountIds",
          [calEmailId]
        );

        if (calendarConfig != null) {
          const calGroupsMap: Map<string, string> = new Map(
            Object.entries(JSON.parse(calendarConfig))
          );
          if (calGroupsMap.size > 0 && refreshKey != null && timeZone != null) {
            await saveCalendarConfigHandler(
              logger,
              uid ?? "",
              calendarDetails,
              calAccId,
              mevolveCalId ?? null,
              refreshKey ?? null,
              timeZone,
              eventsList ?? null,
              tasksList ?? null,
              type ?? currentCalDetails.calendarType,
              calendarConfig,
              redirectUri ?? null
            );
          }
        }

        logger.log(
          "Save New Calendar successful for: " + uid + " and calId: " + calAccId
        );
      } else {
        await db
          .collection("calendarIntegrations")
          .doc(checkExists.docs[0].id)
          .set(
            {
              localUpdatedAt: Timestamp.now(),
              cloudUpdatedAt: Timestamp.now(),
              lastSync: Timestamp.now(),
            },
            { merge: true }
          );
        logger.log(
          "Save New Calendar successful for: " +
            uid +
            " and calId: " +
            calAccId +
            " as same calendar already exists"
        );
      }
      return {
        statusCode: 200,
      };
    } else {
      logger.log(
        "Save New Calendar failed for: " +
          uid +
          " and calId: " +
          calAccId +
          " User doesn't exist"
      );
      return {
        statusCode: 500,
      };
    }
  } catch (error) {
    logger.log(`Save New Calendar Error ${error}`);
    return {
      statusCode: 500,
      error: "" + error,
    };
  }
}

export async function resyncCalendarHandler(
  logger: MeLogger,
  uid: string,
  calendarDetails: string,
  calAccId: string,
  mevolveCalId: string | null,
  refreshKey: string,
  timeZone: string,
  eventsList: string | null,
  tasksList: string | null,
  type: string,
  calendarConfig: string,
  redirectUri: string | null
) {
  logger.log(`Resync Calendar integration for: ${uid} and calId: ${calAccId}`);
  const calGroupsMap: Map<string, string> = new Map(
    Object.entries(JSON.parse(calendarConfig))
  );
  const currentCalDetails = ZCalDetails.parse(JSON.parse(calendarDetails));
  const db = admin.firestore();
  const syncTodos = calGroupsMap.get("syncTodos") ?? null;
  const syncHabits = calGroupsMap.get("syncHabits") ?? null;
  const syncJournals = calGroupsMap.get("syncJournals") ?? null;
  try {
    let updatedCalendarIntegrations: ICalendarIntegrations | null = null;
    let updatedMevolveId: string | null = null;

    await db
      .collection("calendarIntegrations")
      .doc(calAccId)
      .set(
        {
          status: "inProgress",
          localUpdatedAt: Timestamp.fromDate(currentCalDetails.localUpdatedAt),
          cloudUpdatedAt: Timestamp.fromDate(currentCalDetails.localUpdatedAt),
        },
        { merge: true }
      );

    // Add this: Refresh the list of available calendars
    await refreshCalendarList(
      logger,
      uid,
      calAccId,
      refreshKey,
      type,
      currentCalDetails,
      redirectUri ?? null
    );

    if (tasksList != null || eventsList != null) {
      await deleteCalendarHandler(
        logger,
        uid,
        calAccId,
        tasksList ?? "",
        type,
        true,
        eventsList ?? ""
      );
    }

    const calEmailId =
      currentCalDetails.calendarType == "google"
        ? `g_${currentCalDetails.email}`
        : `m_${currentCalDetails.email}`;

    await createSpecialActivitiesDoc(
      uid,
      undefined,
      true,
      currentCalDetails.docVer,
      "calendarAccountIds",
      [calEmailId]
    );

    if (currentCalDetails.mevolveStatus != "mevolveError") {
      const checkResponse = await checkMevolveCalendarHandler(
        logger,
        uid,
        calAccId,
        mevolveCalId ?? "",
        refreshKey,
        type,
        false,
        redirectUri ?? null
      );
      if (checkResponse.statusCode == 400) {
        throw new Error("locked");
      }
      if (checkResponse.isMevolveError == true) {
        const response = await createMevolveCalendar(
          logger,
          uid,
          calAccId,
          mevolveCalId ?? "",
          refreshKey,
          type,
          redirectUri ?? null
        );
        if (response.updatedMevolveId != null) {
          mevolveCalId = response.updatedMevolveId;
          updatedMevolveId = mevolveCalId;
        }
      }
    } else {
      const response = await createMevolveCalendar(
        logger,
        uid,
        calAccId,
        mevolveCalId ?? "",
        refreshKey,
        type,
        redirectUri ?? null
      );
      if (response.updatedMevolveId != null) {
        mevolveCalId = response.updatedMevolveId;
        updatedMevolveId = mevolveCalId;
      }
    }

    if (tasksList != null) {
      const response = await pushTasksToCalendarHandler(
        logger,
        uid,
        calAccId,
        mevolveCalId,
        refreshKey,
        timeZone,
        type,
        tasksList,
        syncTodos,
        syncHabits,
        syncJournals,
        null,
        redirectUri ?? null
      );
      if (response.updatedMevolveId != null) {
        updatedMevolveId = response.updatedMevolveId;
      }
    }

    calGroupsMap.delete("syncTodos");
    calGroupsMap.delete("syncHabits");
    calGroupsMap.delete("syncJournals");

    if (calGroupsMap.size > 0) {
      // Clear sync tokens for all calendar groups before resync to force full event refresh.
      // This ensures existing calendar groups get all their events, not just incremental changes.
      for (const calGroupId of calGroupsMap.keys()) {
        const calGroupIndex = currentCalDetails.calendarGroups.findIndex(
          (item) => item.id === calGroupId
        );
        if (calGroupIndex !== -1) {
          currentCalDetails.calendarGroups[calGroupIndex].nextSyncToken = null;
          currentCalDetails.calendarGroups[calGroupIndex].nextPageToken = null;
        }
      }

      const response = await getEventsForCalGroupHandler(
        logger,
        uid,
        calAccId,
        calGroupsMap,
        currentCalDetails,
        refreshKey,
        type,
        eventsList,
        redirectUri ?? null
      );
      if (response.calData != null) {
        updatedCalendarIntegrations = response.calData;
      }
      logger.log(
        `Resync Calendar get Events Parsing done for: ${uid} and calId: ${calAccId}`
      );
    }
    logger.log(
      `Resync Calendar updating calendar integration for: ${uid} and calId: ${calAccId}`
    );
    const calData = await db
      .collection("calendarIntegrations")
      .doc(calAccId)
      .get();
    let currCalendarIntegrations = calData.exists
      ? ZCalendarIntegrations.parse(calData.data() as unknown)
      : currentCalDetails;

    if (updatedCalendarIntegrations != null) {
      currCalendarIntegrations = {
        ...updatedCalendarIntegrations,
        status: "active",
        ...(syncTodos != null && { syncTodos: syncTodos }),
        ...(syncHabits != null && { syncHabits: syncHabits }),
        ...(syncJournals != null && { syncJournals: syncJournals }),
      };
    } else {
      currCalendarIntegrations.status = "active";
      if (syncTodos != null) {
        currCalendarIntegrations.syncTodos = currentCalDetails.syncTodos;
      }
      if (syncHabits != null) {
        currCalendarIntegrations.syncHabits = currentCalDetails.syncHabits;
      }
      if (syncJournals != null) {
        currCalendarIntegrations.syncJournals = currentCalDetails.syncJournals;
      }
    }
    if (updatedMevolveId != null) {
      const mevIndex = currCalendarIntegrations.calendarGroups.findIndex(
        (item) => item.title == "Mevolve"
      );
      if (mevIndex > -1) {
        currCalendarIntegrations.calendarGroups[mevIndex].id = updatedMevolveId;
      }
    }
    const lastSync =
      currCalendarIntegrations.lastSync == null
        ? null
        : (currCalendarIntegrations.lastSync as unknown as Timestamp).toDate();
    const now = new Date();

    let aheadNow = new Date();
    let isAhead = false;
    if (currentCalDetails.localUpdatedAt > aheadNow) {
      isAhead = true;
      aheadNow = currentCalDetails.localUpdatedAt;
      aheadNow = new Date(aheadNow.setTime(aheadNow.getTime() + 5 * 1000));
    }
    const batch = db.batch();
    batch.set(
      db.collection("calendarIntegrations").doc(calAccId),
      {
        ...currCalendarIntegrations,
        localUpdatedAt: isAhead ? aheadNow : Timestamp.now(),
        cloudUpdatedAt: isAhead ? aheadNow : Timestamp.now(),
        lastSync: Timestamp.now(),
        resyncLimit:
          lastSync != null && lastSync.getDate() == now.getDate()
            ? FieldValue.increment(1)
            : 1,
        mevolveStatus: "active",
      },
      { merge: true }
    );

    await batch.commit();
    logger.log(
      `Resync Calendar integration success for: ${uid} and calId: ${calAccId}`
    );
    return {
      statusCode: 200,
    };
  } catch (error) {
    const calData = await db
      .collection("calendarIntegrations")
      .doc(calAccId)
      .get();

    if (calData.exists) {
      const currCalendarIntegrations =
        calData.data() as unknown as ICalendarIntegrations;

      let aheadNow = new Date();
      let isAhead = false;
      if (currentCalDetails.localUpdatedAt > aheadNow) {
        isAhead = true;
        aheadNow = currentCalDetails.localUpdatedAt;
        aheadNow = new Date(aheadNow.setTime(aheadNow.getTime() + 5 * 1000));
      }
      await db
        .collection("calendarIntegrations")
        .doc(calAccId)
        .set(
          {
            ...currCalendarIntegrations,
            status:
              error instanceof Error && error.message == "locked"
                ? "locked"
                : "active",
            localUpdatedAt: isAhead ? aheadNow : Timestamp.now(),
            cloudUpdatedAt: isAhead ? aheadNow : Timestamp.now(),
          },
          { merge: true }
        );
    }
    logger.log(`Resync Calendar integration Error ${error}`);
    return {
      statusCode: 500,
      error: "" + error,
    };
  }
}

export async function saveCalendarConfigHandler(
  logger: MeLogger,
  uid: string,
  calendarDetails: string,
  calAccId: string,
  mevolveCalId: string | null,
  refreshKey: string,
  timeZone: string,
  eventsList: string | null,
  tasksList: string | null,
  type: string,
  calendarConfig: string,
  redirectUri: string | null
) {
  logger.log(`Save Calendar integration for: ${uid} and calId: ${calAccId}`);
  const calGroupsMap: Map<string, string> = new Map(
    Object.entries(JSON.parse(calendarConfig))
  );
  const currentCalDetails = ZCalDetails.parse(JSON.parse(calendarDetails));
  const db = admin.firestore();
  const syncTodos = calGroupsMap.get("syncTodos") ?? null;
  const syncHabits = calGroupsMap.get("syncHabits") ?? null;
  const syncJournals = calGroupsMap.get("syncJournals") ?? null;
  try {
    let updatedCalendarIntegrations: ICalendarIntegrations | null = null;
    let updatedMevolveId: string | null = null;
    await db
      .collection("calendarIntegrations")
      .doc(calAccId)
      .set(
        {
          status: "inProgress",
          localUpdatedAt: Timestamp.fromDate(currentCalDetails.localUpdatedAt),
          cloudUpdatedAt: Timestamp.fromDate(currentCalDetails.localUpdatedAt),
        },
        { merge: true }
      );

    if (currentCalDetails.mevolveStatus != "mevolveError") {
      const checkResponse = await checkMevolveCalendarHandler(
        logger,
        uid,
        calAccId,
        mevolveCalId ?? "",
        refreshKey,
        type,
        false,
        redirectUri ?? null
      );
      if (checkResponse.statusCode == 400) {
        throw new Error("locked");
      }
      if (checkResponse.isMevolveError == true) {
        throw new Error("mevolveError");
      }
    }
    if (tasksList != null) {
      const response = await pushTasksToCalendarHandler(
        logger,
        uid,
        calAccId,
        mevolveCalId,
        refreshKey,
        timeZone,
        type,
        tasksList,
        syncTodos,
        syncHabits,
        syncJournals,
        null,
        redirectUri ?? null
      );
      if (response.updatedMevolveId != null) {
        updatedMevolveId = response.updatedMevolveId;
      }
    }

    calGroupsMap.delete("syncTodos");
    calGroupsMap.delete("syncHabits");
    calGroupsMap.delete("syncJournals");

    if (calGroupsMap.size > 0) {
      const response = await getEventsForCalGroupHandler(
        logger,
        uid,
        calAccId,
        calGroupsMap,
        currentCalDetails,
        refreshKey,
        type,
        eventsList
      );
      if (response.calData != null) {
        updatedCalendarIntegrations = response.calData;
      }
      logger.log(
        `Save Calendar get Events Parsing done for: ${uid} and calId: ${calAccId}`
      );
    }
    logger.log(
      `Save Calendar updating calendar integration for: ${uid} and calId: ${calAccId}`
    );
    const calData = await db
      .collection("calendarIntegrations")
      .doc(calAccId)
      .get();
    let currCalendarIntegrations = calData.exists
      ? ZCalendarIntegrations.parse(calData.data() as unknown)
      : currentCalDetails;
    const batch = db.batch();

    if (updatedCalendarIntegrations != null) {
      currCalendarIntegrations = {
        ...updatedCalendarIntegrations,
        status: "active",
        ...(syncTodos != null && { syncTodos: syncTodos }),
        ...(syncHabits != null && { syncHabits: syncHabits }),
        ...(syncJournals != null && { syncJournals: syncJournals }),
      };
    } else {
      currCalendarIntegrations.status = "active";
      if (syncTodos != null) {
        currCalendarIntegrations.syncTodos = currentCalDetails.syncTodos;
      }
      if (syncHabits != null) {
        currCalendarIntegrations.syncHabits = currentCalDetails.syncHabits;
      }
      if (syncJournals != null) {
        currCalendarIntegrations.syncJournals = currentCalDetails.syncJournals;
      }
    }
    if (updatedMevolveId != null) {
      const mevIndex = currCalendarIntegrations.calendarGroups.findIndex(
        (item) => item.title == "Mevolve"
      );
      if (mevIndex > -1) {
        currCalendarIntegrations.calendarGroups[mevIndex].id = updatedMevolveId;
      }
    }
    let now = new Date();
    let isAhead = false;
    if (currentCalDetails.localUpdatedAt > now) {
      isAhead = true;
      now = currentCalDetails.localUpdatedAt;
      now = new Date(now.setTime(now.getTime() + 5 * 1000));
    }
    batch.set(
      db.collection("calendarIntegrations").doc(calAccId),
      {
        ...currCalendarIntegrations,
        localUpdatedAt: isAhead ? now : Timestamp.now(),
        cloudUpdatedAt: isAhead ? now : Timestamp.now(),
      },
      { merge: true }
    );

    await batch.commit();
    logger.log(
      `Save Calendar integration success for: ${uid} and calId: ${calAccId}`
    );
    return {
      statusCode: 200,
    };
  } catch (error) {
    const calData = await db
      .collection("calendarIntegrations")
      .doc(calAccId)
      .get();

    if (calData.exists) {
      const currCalendarIntegrations =
        calData.data() as unknown as ICalendarIntegrations;
      // currCalendarIntegrations = currentCalDetails;

      let now = new Date();
      let isAhead = false;
      if (currCalendarIntegrations.localUpdatedAt > now) {
        isAhead = true;
        now = currCalendarIntegrations.localUpdatedAt;
        now = new Date(now.setTime(now.getTime() + 5 * 1000));
      }

      await db
        .collection("calendarIntegrations")
        .doc(calAccId)
        .set(
          {
            ...currCalendarIntegrations,
            mevolveStatus:
              error instanceof Error && error.message == "mevolveError"
                ? currCalendarIntegrations.mevolveStatus
                : "mevolveError",
            status:
              error instanceof Error && error.message == "locked"
                ? "locked"
                : "failed",
            localUpdatedAt: isAhead ? now : Timestamp.now(),
            cloudUpdatedAt: isAhead ? now : Timestamp.now(),
          },
          { merge: true }
        );
    }
    logger.log(`Save Calendar integration Error ${error}`);
    return {
      statusCode: 500,
      error: "" + error,
    };
  }
}

// Function to fetch calendar events
export async function getEventsForCalGroupHandler(
  logger: MeLogger,
  uid: string,
  calAccId: string,
  calGroupsMap: Map<string, string>,
  currentCalDetails: ICalDetailsType,
  refreshKey: string,
  type: string,
  eventsList: string | null,
  redirectUri?: string | null,
  isResync?: boolean | null
) {
  const db = admin.firestore();
  const batch = db.batch();
  try {
    const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
    const refreshToken = decryptTextData(refreshKey, userSecret);
    const accessToken = await getAccessToken(refreshToken, type, redirectUri);

    const calData = await db
      .collection("calendarIntegrations")
      .doc(calAccId)
      .get();
    const data = await db.collection("users").doc(uid).get();

    if (calData.exists && data.exists && accessToken != null) {
      // const currentCalDetails = ZCalDetails.parse(JSON.parse(calendarDetails)) as ICalDetailsType;
      let calendarIntegrations =
        calData.data() as unknown as ICalendarIntegrations;
      const userData = data.data() as unknown as IUser;
      for (const [key, value] of calGroupsMap.entries()) {
        if (type == "google") {
          const calGroupId = key;
          const calGroupStatus = value;

          // const calIndex =
          //   calendarIntegrations.googleCalendars.findIndex(
          //     (item) => item.id == calAccId
          //   );

          const calAcc = calendarIntegrations;

          let calGroupIndex = calAcc.calendarGroups.findIndex(
            (item) => item.id == calGroupId
          );

          if (calGroupIndex == -1) {
            calGroupIndex = currentCalDetails.calendarGroups.findIndex(
              (item) => item.id == calGroupId
            );
          }

          const calGroup = calAcc.calendarGroups[calGroupIndex];

          const oauth2Client = getGoogleOAuth2Client(redirectUri ?? null);
          let calendar: calendar_v3.Calendar;
          if (calGroupsMap.size > 1) {
            calendar = getSingleGoogleCalendarInstance();
          } else {
            calendar = getGoogleCalendarInstance();
          }
          oauth2Client.setCredentials({
            access_token: accessToken,
          });

          if (calGroupStatus == "disabled" && isResync != true) {
            if (calGroup.channelId != null && calGroup.resourceId != null) {
              await stopWebhookForCalGroup(
                logger,
                accessToken,
                calGroup.channelId,
                calGroup.resourceId,
                type
              ).catch((_) => {
                null;
              });
            }

            const calMap = {
              id: calGroupId,
              title: calGroup.title ?? "",
              description: calGroup.description ?? null,
              nextSyncToken: null,
              nextPageToken: null,
              syncStatus: calGroupStatus,
              resourceId: null,
              channelId: null,
            };
            calAcc.calendarGroups[calGroupIndex] = calMap;
            // if (calIndex > -1) {
            calendarIntegrations = calAcc;
            // }
            await createSpecialActivitiesDoc(
              uid,
              undefined,
              true,
              calAcc.docVer,
              "calendarIds",
              [calGroup.id],
              batch
            );
            if (eventsList != null) {
              for (const syncData of eventsList) {
                const parsedSyncData = ZSyncRequestModel.parse(syncData);
                const dataReceived: IBaseSchema = JSON.parse(
                  parsedSyncData.data
                );
                if (
                  dataReceived.docCollection == "calendarEventActions" ||
                  dataReceived.docCollection == "calendarEventSetups"
                ) {
                  const docRef = db
                    .collection(dataReceived.docCollection)
                    .doc(dataReceived.id);
                  batch.delete(docRef);
                }
              }
            }
          } else {
            let accountName: string | null = null;

            oauth2Client.setCredentials({
              access_token: accessToken,
              refresh_token: refreshToken,
            });

            const oauth2 = google.oauth2({
              auth: oauth2Client,
              version: "v2",
            });

            const res = await oauth2.userinfo.get();

            accountName = res?.data.name ?? null;

            const calInfo = await calendar.calendarList.get({
              calendarId: calGroupId,
              auth: oauth2Client,
              quotaUser: uid,
            });
            let defaultMinutes: number | null = null;
            if (calInfo.data.defaultReminders != null) {
              const reminder = calInfo.data.defaultReminders.find(
                (item) => item.method == "popup"
              );
              defaultMinutes = reminder?.minutes ?? null;
            }
            const calEvents = await calendar.events.list({
              calendarId: calGroupId,
              auth: oauth2Client,
              timeMin: new Date(
                new Date().setDate(new Date().getDate() - 90)
              ).toISOString(),
              maxResults: 2500,
              singleEvents: false,
              quotaUser: uid,
            });

            if (
              calEvents.data.items != null &&
              calEvents.data.items.length > 0
            ) {
              logger.log(
                "Calendar Event List for group: " +
                  calGroupId +
                  " length: " +
                  calEvents.data.items.length.toString()
              );
              logger.log(
                `Calendar Event List title:  + ${calInfo.data.summary}`
              );
              const calAllEventsMap = await parseGoogleCalendarEvents(
                uid,
                calendarIntegrations.id,
                calendarIntegrations.email,
                calGroupId,
                calendarIntegrations.email == calGroupId
                  ? (accountName ?? calInfo.data.summary ?? "")
                  : (calInfo.data.summary ?? ""),
                calEvents.data.items,
                calGroupStatus,
                defaultMinutes
              );
              const calAllEventSetupsMap = calAllEventsMap[0];
              const calAllEventActionsMap = calAllEventsMap[1];

              calAllEventSetupsMap.forEach((value: object, key: string) => {
                const docRef = db.collection("calendarEventSetups").doc(key);
                batch.set(docRef, value, { merge: true });
              });
              calAllEventActionsMap.forEach((value: object, key: string) => {
                const docRef = db.collection("calendarEventActions").doc(key);
                batch.set(docRef, value, { merge: true });
              });
            }
            logger.log(
              "Calendar Event Parsing done for " + calendarIntegrations.id
            );
            const response = await startWebhookForCalGroup(
              logger,
              calGroupId,
              uid,
              type,
              calAccId,
              accessToken
            );

            const calMap = {
              id: calGroupId,
              title: calGroup.title ?? "",
              description: calGroup.description ?? null,
              nextSyncToken: calEvents.data.nextSyncToken ?? null,
              nextPageToken: calEvents.data.nextPageToken ?? null,
              syncStatus: calGroupStatus,
              resourceId:
                response.status == 200 ? (response.resourceId ?? null) : null,
              channelId:
                response.status == 200 ? (response.channelId ?? null) : null,
            };
            const expiry = new Date();
            expiry.setDate(expiry.getDate() + 28);
            if (calAcc.webhookExpiry == null || calAcc.webhookExpiry > expiry) {
              calAcc.webhookExpiry = expiry;
            }
            calAcc.calendarGroups[calGroupIndex] = calMap;
            // if (calIndex > -1) {
            calendarIntegrations = calAcc;
            // } else {
            //   calendarIntegrations.googleCalendars.push(calAcc);
            // }
            await createSpecialActivitiesDoc(
              uid,
              undefined,
              true,
              calAcc.docVer,
              "calendarIds",
              [calGroup.id],
              batch
            );
          }
        } else if (type == "microsoft") {
          const calGroupId = key;
          const calGroupStatus = value;

          // const calIndex =
          //   calendarIntegrations.microsoftCalendars.findIndex(
          //     (item) => item.id == calAccId
          //   );
          const calAcc = calendarIntegrations;

          let calGroupIndex = calAcc.calendarGroups.findIndex(
            (item) => item.id == calGroupId
          );

          if (calGroupIndex == -1) {
            calGroupIndex = currentCalDetails.calendarGroups.findIndex(
              (item) => item.id == calGroupId
            );
          }

          const calGroup = calAcc.calendarGroups[calGroupIndex];
          const client = getMicrosoftClient(accessToken);
          // console.log("cal at: " + accessToken);
          // console.log(`cal: /me/calendars/${calGroupId}/events`);

          if (calGroupStatus == "disabled") {
            if (calGroup.channelId != null) {
              await stopWebhookForCalGroup(
                logger,
                accessToken,
                calGroup.channelId,
                calGroup.resourceId,
                type
              ).catch((_) => {
                null;
              });
            }

            const calMap = {
              id: calGroupId,
              title: calGroup.title ?? "",
              description: calGroup.description ?? null,
              nextSyncToken: null,
              nextPageToken: null,
              syncStatus: calGroupStatus,
              resourceId: null,
              channelId: null,
            };
            calAcc.calendarGroups[calGroupIndex] = calMap;
            // if (calIndex > -1) {
            calendarIntegrations = calAcc;
            // }
            await createSpecialActivitiesDoc(
              uid,
              undefined,
              true,
              calAcc.docVer,
              "calendarIds",
              [calGroup.id],
              batch
            );
            if (eventsList != null) {
              for (const syncData of eventsList) {
                const parsedSyncData = ZSyncRequestModel.parse(syncData);
                const dataReceived: IBaseSchema = JSON.parse(
                  parsedSyncData.data
                );
                if (
                  dataReceived.docCollection == "calendarEventActions" ||
                  dataReceived.docCollection == "calendarEventSetups"
                ) {
                  const docRef = db
                    .collection(dataReceived.docCollection)
                    .doc(dataReceived.id);
                  batch.delete(docRef);
                }
              }
            }
          } else {
            // const startTime = new Date(
            //   new Date().setDate(new Date().getDate() - 90)
            // ).toISOString();
            // const endTime = new Date(
            //   new Date().setDate(new Date().getFullYear() + 100)
            // ).toISOString();
            // const apiLink = `/me/calendars/${calGroupId}/calendarView/delta?startDateTime=${startTime}&endDateTime=${endTime}`;
            const apiLink = `/me/calendars/${calGroupId}/events?$top=100`; // &filter=start/dateTime ge '${startTime}'
            // console.log("cal: at " + accessToken);
            // console.log("cal: " + apiLink);
            const userResponse = await client.api(apiLink).get();
            const calEvents = userResponse.value as Event[];

            let hasNextLink = false;
            let url: string =
              userResponse["@odata.nextLink"] != null
                ? userResponse["@odata.nextLink"]
                : "";
            hasNextLink = url.length > 0;
            while (hasNextLink) {
              const apiLink = url.replace(
                "https://graph.microsoft.com/v1.0",
                ""
              );
              const userResponse = await client.api(apiLink).get();
              calEvents.push(...(userResponse.value as Event[]));
              url =
                userResponse["@odata.nextLink"] != null
                  ? userResponse["@odata.nextLink"]
                  : "";
              hasNextLink = url.length > 0;
            }
            // const url = new URL(userResponse["@odata.deltaLink"] ?? userResponse["@odata.nextLink"]);
            const nextSyncToken = null; // url.searchParams.get("$deltatoken");

            if (calEvents != null && calEvents.length > 0) {
              logger.log(
                "Calendar Event List for group: " +
                  calGroupId +
                  " length: " +
                  calEvents.length.toString()
              );

              const calAllEventsMap = await parseMicrosoftCalendarEvents(
                uid,
                calendarIntegrations.id,
                calendarIntegrations.email,
                calGroupId,
                calGroup.title ?? "",
                calEvents,
                calGroupStatus,
                accessToken
              );
              const calAllEventSetupsMap = calAllEventsMap[0];
              const calAllEventActionsMap = calAllEventsMap[1];

              calAllEventSetupsMap.forEach((value: object, key: string) => {
                const docRef = db.collection("calendarEventSetups").doc(key);
                batch.set(docRef, value, { merge: true });
              });
              calAllEventActionsMap.forEach((value: object, key: string) => {
                const docRef = db.collection("calendarEventActions").doc(key);
                batch.set(docRef, value, { merge: true });
              });
            }
            logger.log(
              "Calendar Event Parsing done for " + calendarIntegrations.id
            );
            const response = await startWebhookForCalGroup(
              logger,
              calGroupId,
              uid,
              type,
              calAccId,
              accessToken
            );

            const calMap = {
              id: calGroupId,
              title: calGroup.title,
              description: calGroup.description ?? null,
              nextSyncToken: nextSyncToken,
              nextPageToken: null,
              syncStatus: calGroupStatus,
              resourceId:
                response.status == 200 ? (response.resourceId ?? null) : null,
              channelId:
                response.status == 200 ? (response.channelId ?? null) : null,
            };
            const expiry = new Date();
            expiry.setDate(expiry.getDate() + 6);
            if (calAcc.webhookExpiry == null || calAcc.webhookExpiry > expiry) {
              calAcc.webhookExpiry = expiry;
            }
            calAcc.calendarGroups[calGroupIndex] = calMap;
            // if (calIndex > -1) {
            calendarIntegrations = calAcc;
            // } else {
            //   calendarIntegrations.microsoftCalendars.push(calAcc);
            // }
            await createSpecialActivitiesDoc(
              uid,
              undefined,
              true,
              calAcc.docVer,
              "calendarIds",
              [calGroup.id],
              batch
            );
          }
        }
      }

      await batch.commit();
      return {
        statusCode: 200,
        userData: userData,
        calData: calendarIntegrations,
      };
    }
    return {
      statusCode: 200,
    };
  } catch (error) {
    logger.log(`Calendar Tasks Get Error ${error}`);
    return {
      statusCode: 500,
      error: "" + error,
    };
  }
}

export async function syncTasksToCalendarHandler(
  logger: MeLogger,
  uid: string,
  dataList: string,
  timeZone: string,
  redirectUri: string | null
) {
  logger.log("Syncing tasks data for - " + uid);
  const oauth2Client = getGoogleOAuth2Client(redirectUri ?? null);
  const calendar = getSingleGoogleCalendarInstance();

  const tasksInsert: unknown[] = [];
  const tasksUpdate: unknown[] = [];
  const responseDataList: string[] = [];
  try {
    const db = admin.firestore();

    const data = await db
      .collection("calendarIntegrations")
      .where("uid", "==", uid)
      .get();
    if (!data.empty) {
      const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
      // const calendarIntegrations = data.data() as unknown as ICalendarIntegrations;
      const calendarIntegrationsList: ICalendarIntegrations[] = [];
      data.forEach((item) =>
        calendarIntegrationsList.push(
          item.data() as unknown as ICalendarIntegrations
        )
      );
      const todoCals: {
        id: string;
        accessToken: string;
        type: string;
        syncStatus: string;
      }[] = [];
      const habitCals: {
        id: string;
        accessToken: string;
        type: string;
        syncStatus: string;
      }[] = [];
      const journalCals: {
        id: string;
        accessToken: string;
        type: string;
        syncStatus: string;
      }[] = [];
      const calGroupTokens: { [x: string]: string } = {};
      let canSync = false;

      for (let i = 0; i < calendarIntegrationsList.length; i++) {
        const calAcc = calendarIntegrationsList[i];
        const type = calAcc.calendarType;
        if (
          calAcc.status == "active" &&
          calAcc.deletedAt == null &&
          calAcc.mevolveStatus != "mevolveError"
        ) {
          const refreshKey = calAcc.refreshToken ?? "";
          const refreshToken = decryptTextData(refreshKey, userSecret);
          const accessToken = await getAccessToken(
            refreshToken,
            type,
            redirectUri
          );

          const mevolveIndex = calAcc.calendarGroups.findIndex(
            (group) => group.title == "Mevolve"
          );

          const mevolveGroup = calAcc.calendarGroups[mevolveIndex];
          if (accessToken != null) {
            if (mevolveIndex > -1) {
              if (calAcc.syncTodos != "disabled") {
                todoCals.push({
                  id: mevolveGroup.id,
                  accessToken: accessToken,
                  type: type,
                  syncStatus: calAcc.syncTodos ?? "disabled",
                });
                canSync = true;
              }
              if (calAcc.syncHabits != "disabled") {
                habitCals.push({
                  id: mevolveGroup.id,
                  accessToken: accessToken,
                  type: type,
                  syncStatus: calAcc.syncHabits ?? "disabled",
                });
                canSync = true;
              }
              if (calAcc.syncJournals != "disabled") {
                journalCals.push({
                  id: mevolveGroup.id,
                  accessToken: accessToken,
                  type: type,
                  syncStatus: calAcc.syncJournals ?? "disabled",
                });
                canSync = true;
              }
            }
            calGroupTokens[`${calAcc.id}`] = accessToken;
          }
        }
      }
      if (canSync) {
        // const helperData: Map<string, string> = new Map();
        for (const syncData of dataList) {
          const parsedSyncData = ZSyncRequestModel.parse(syncData);
          const dataReceived: IBaseSchema = JSON.parse(parsedSyncData.data);
          const migratedData = dataReceived as unknown as IDocBaseSchema;
          // (await new DataMigrationService().migrateSchema(
          //   dataReceived,
          //   helperData
          // )) as unknown as IDocBaseSchema;

          // const decDocKey = await decryptDocKey(
          //   migratedData.encData.dek,
          //   uid,
          //   userSecret
          // );

          if (
            migratedData.docCollection == "calendarEventActions" ||
            migratedData.docCollection == "calendarEventSetups"
          ) {
            let calEvent: ICalendarEventAction | ICalendarEventSetup;

            if (migratedData.docCollection == "calendarEventActions") {
              calEvent = migratedData as unknown as ICalendarEventAction;
            } else {
              calEvent = migratedData as unknown as ICalendarEventSetup;
            }
            logger.log(
              "starting pushing to calendar " +
                calEvent.id +
                ": " +
                calEvent.calendarAccountId
            );
            if (calEvent.rawData != null) {
              const eventBody = JSON.parse(
                calEvent.rawData
              ) as unknown as calendar_v3.Schema$Event;
              const calDueAt = (calEvent as ICalendarEventAction).dueAt ?? null;
              const startAt =
                migratedData.docCollection == "calendarEventActions"
                  ? calDueAt != null
                    ? new Date(calDueAt.timeWithOffset)
                    : null
                  : calEvent.startAt != null
                    ? new Date(calEvent.startAt.timeWithOffset)
                    : null;
              const endAt =
                startAt != null
                  ? new Date(startAt.getTime() + calEvent.duration * 60000)
                  : null;
              eventBody.summary = calEvent.title;
              eventBody.description =
                calEvent.description == null || calEvent.description.length == 0
                  ? ""
                  : calEvent.description;
              eventBody.start = {
                date: migratedData.isStartTimeSet
                  ? null
                  : migratedData.startAt?.dateString,
                dateTime: migratedData.isStartTimeSet
                  ? startAt?.toISOString()
                  : null,
                timeZone: timeZone,
              };
              eventBody.end = {
                date: migratedData.isStartTimeSet
                  ? null
                  : migratedData.startAt?.dateString,
                dateTime: migratedData.isStartTimeSet
                  ? endAt?.toISOString()
                  : null,
                timeZone: timeZone,
              };
              if (migratedData.docCollection == "calendarEventActions") {
                eventBody.id = undefined; // migratedData.id.substring(8);
                eventBody.iCalUID = undefined;
                if (eventBody.recurringEventId == null) {
                  eventBody.recurringEventId = (
                    calEvent as ICalendarEventAction
                  ).setupId.substring(8);
                }
              }
              eventBody.sequence = undefined;
              const calAccId = calEvent.calendarAccountId;
              oauth2Client.setCredentials({
                access_token: calGroupTokens[calAccId],
              });
              tasksInsert.push(
                calendar.events
                  .update({
                    eventId: migratedData.id.substring(8),
                    calendarId: calAccId,
                    auth: oauth2Client,
                    requestBody: eventBody,
                    quotaUser: uid,
                  })
                  .then((_) => {
                    responseDataList.push(migratedData.id);
                  })
                  .catch((_) => {
                    logger.log(`Calendar tasksInsert ${eventBody.id}: ${_}`);
                    tasksUpdate.push(
                      calendar.events
                        .insert({
                          // eventId: eventBody.id ?? undefined,
                          calendarId: calAccId,
                          auth: oauth2Client,
                          requestBody: eventBody,
                          quotaUser: uid,
                        })
                        .then((_) => {
                          responseDataList.push(migratedData.id);
                        })
                        .catch((e) => {
                          logger.log(
                            `Calendar tasksUpdate ${eventBody.id}: ${e}`
                          );
                        })
                    );
                  })
              );
            }
          } else {
            if (
              migratedData.deletedAt != null ||
              migratedData.startAt != null ||
              migratedData.docCollection == "todos" ||
              migratedData.docCollection == "habitSetups" ||
              migratedData.docCollection == "journalSetups"
            ) {
              const loopLength =
                migratedData.docCollection == "todos"
                  ? todoCals.length
                  : migratedData.docCollection == "habitSetups"
                    ? habitCals.length
                    : journalCals.length;
              for (let i = 0; i < loopLength; i++) {
                const cal =
                  migratedData.docCollection == "todos"
                    ? todoCals[i]
                    : migratedData.docCollection == "habitSetups"
                      ? habitCals[i]
                      : journalCals[i];

                if (
                  migratedData.deletedAt != null
                  //  || migratedData.isPaused == true
                ) {
                  const event = {
                    id: migratedData.id.replace(/-/g, ""),
                  };
                  if (cal.type == "google") {
                    logger.log("sync google:  " + cal.id);
                    oauth2Client.setCredentials({
                      access_token: cal.accessToken,
                    });
                    tasksInsert.push(
                      calendar.events
                        .delete({
                          eventId: event.id ?? "",
                          calendarId: cal.id,
                          auth: oauth2Client,
                        })
                        .then((_) => {
                          responseDataList.push(migratedData.id);
                        })
                    );
                  } else {
                    const client = getMicrosoftClient(cal.accessToken);
                    const event = {
                      transactionId: dataReceived.id.replace(/-/g, ""),
                    };
                    tasksInsert.push(
                      client
                        .api(`/me/calendars/${cal.id}/events`)
                        .post(event)
                        .then((res) => {
                          tasksUpdate.push(
                            client
                              .api(`/me/calendars/${cal.id}/events/${res.id}`)
                              .delete()
                              .catch((_) => {
                                null;
                              })
                          );
                        })
                        .catch((e) => {
                          if (e.code == "ErrorDuplicateTransactionId") {
                            const id = JSON.parse(e.body)[
                              "@event.existingEventId"
                            ];
                            tasksUpdate.push(
                              client
                                .api(`/me/calendars/${cal.id}/events/${id}`)
                                .delete()
                                .catch((_) => {
                                  null;
                                })
                            );
                          } else {
                            logger.log(
                              "MS event: " +
                                JSON.stringify(event) +
                                " failed with error: " +
                                e
                            );
                          }
                        })
                    );
                  }
                } else {
                  if (cal.type == "google") {
                    try {
                      const event = getGoogleEventObject(
                        migratedData,
                        // decDocKey,
                        timeZone,
                        migratedData.docCollection == "todos"
                          ? cal.syncStatus
                          : null,
                        migratedData.docCollection == "habitSetups"
                          ? cal.syncStatus
                          : null,
                        migratedData.docCollection == "journalSetups"
                          ? cal.syncStatus
                          : null
                      );
                      oauth2Client.setCredentials({
                        access_token: cal.accessToken,
                      });

                      tasksInsert.push(
                        calendar.events
                          .insert({
                            calendarId: cal.id,
                            auth: oauth2Client,
                            requestBody: event,
                            quotaUser: uid,
                          })
                          .then((_) => {
                            responseDataList.push(migratedData.id);
                          })
                          .catch((_) => {
                            logger.log(
                              `Calendar tasksInsert ${event.id}: ${_}`
                            );
                            const updateUrl = `https://www.googleapis.com/calendar/v3/calendars/${cal.id}/events/${event.id}?quotaUser=${uid}`;
                            tasksUpdate.push(
                              axios
                                .put(updateUrl, event, {
                                  headers: {
                                    "Content-Type": "application/json",
                                    Authorization: `Bearer ${cal.accessToken}`,
                                  },
                                })
                                .then((_) => {
                                  responseDataList.push(migratedData.id);
                                })
                                .catch((_) => {
                                  logger.log(
                                    `Calendar tasksUpdate ${event.id}: ${_}`
                                  );
                                })
                              // calendar.events
                              //     .update({
                              //       eventId: event.id ?? "",
                              //       calendarId: cal.id,
                              //       auth: oauth2Client,
                              //       requestBody: event,
                              //       quotaUser: uid,
                              //     })
                              //     .then((_) => {
                              //       responseDataList.push(migratedData.id);
                              //     }).catch((_) => {
                              //       console.log(`Calendar tasksUpdate ${event.id}: ${_}`);
                              //     })
                            );
                          })
                      );
                    } catch (error) {
                      logger.log(
                        `Save Calendar Push Tasks Parsing error for google  ${migratedData.docCollection}/${migratedData.id}: ${error}`
                      );
                    }
                  } else {
                    try {
                      const event = getMSEventObject(
                        migratedData,
                        // decDocKey,
                        timeZone,
                        migratedData.docCollection == "todos"
                          ? cal.syncStatus
                          : null,
                        migratedData.docCollection == "habitSetups"
                          ? cal.syncStatus
                          : null,
                        migratedData.docCollection == "journalSetups"
                          ? cal.syncStatus
                          : null
                      );
                      if (event != null) {
                        const client = getMicrosoftClient(cal.accessToken);
                        tasksInsert.push(
                          client
                            .api(`/me/calendars/${cal.id}/events`)
                            .post(event)
                            .then((_) => {
                              responseDataList.push(migratedData.id);
                            })
                            .catch((e) => {
                              if (e.code == "ErrorDuplicateTransactionId") {
                                const id = JSON.parse(e.body)[
                                  "@event.existingEventId"
                                ];
                                tasksUpdate.push(
                                  client
                                    .api(
                                      `/me/calendars/${cal.id}/events/ ${id}`
                                    )
                                    .update(event)
                                    .then((_) => {
                                      responseDataList.push(migratedData.id);
                                    })
                                );
                              } else {
                                logger.log(
                                  "MS event: " +
                                    JSON.stringify(event) +
                                    " failed with error: " +
                                    e
                                );
                              }
                            })
                        );
                      }
                    } catch (error) {
                      logger.log(
                        `Save Calendar Push Tasks Parsing error for ms  ${migratedData.docCollection}/${migratedData.id}: ${error}`
                      );
                    }
                  }
                }
              }
            }
          }
        }
      }
      await Promise.allSettled(tasksInsert);
      if (tasksUpdate.length > 0) {
        logger.log(`Calendar tasksSyncUpdate.length ${tasksUpdate.length}`);
        await Promise.allSettled(tasksUpdate);
      }
    }
    return {
      statusCode: 200,
      body: "Success",
      data: responseDataList,
    };
  } catch (error) {
    logger.error(`Calendar Tasks Sync Error  ${error}`);
    return {
      statusCode: 500,
      error: "" + error,
      data: responseDataList,
    };
  }
}

export async function checkMevolveCalendarHandler(
  logger: MeLogger,
  uid: string,
  calAccId: string,
  mevolveCalId: string,
  refreshKey: string,
  type: string,
  markMevolveError: boolean,
  redirectUri: string | null
) {
  try {
    logger.log(`Calendar Mevolve check for: ${uid} and calId: ${calAccId}`);
    if (type == "google") {
      const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
      const refreshToken = decryptTextData(refreshKey, userSecret);
      const accessToken = await getAccessToken(refreshToken, type, redirectUri);
      const oauth2Client = getGoogleOAuth2Client(redirectUri ?? null);
      const calendar = getGoogleCalendarInstance();
      oauth2Client.setCredentials({
        access_token: accessToken,
      });
      const calGroupsList = await calendar.calendarList.list({
        auth: oauth2Client,
        quotaUser: uid,
      });
      if (
        calGroupsList.data.items == null ||
        calGroupsList.data.items.length == 0 ||
        calGroupsList.data.items.findIndex(
          (item) =>
            item.summary == "Mevolve" &&
            item.id == mevolveCalId &&
            item.deleted != true
        ) == -1
      ) {
        if (markMevolveError == true) {
          const db = admin.firestore();
          const batch = db.batch();
          batch.set(
            db.collection("calendarIntegrations").doc(calAccId),
            {
              localUpdatedAt: Timestamp.now(),
              cloudUpdatedAt: Timestamp.now(),
              mevolveStatus: "mevolveError",
              // resourceId: typeof resourceId === "string" ? resourceId : null,
            },
            { merge: true }
          );
          await batch.commit();
        }
        return {
          statusCode: 200,
          isMevolveError: true,
        };
      }
      return {
        statusCode: 200,
        isMevolveError: false,
      };
    } else if (type == "microsoft") {
      const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
      const refreshToken = decryptTextData(refreshKey, userSecret);
      const accessToken = await getAccessToken(refreshToken, type, redirectUri);
      if (accessToken != null) {
        if (accessToken == "locked") {
          const db = admin.firestore();
          const batch = db.batch();
          batch.set(
            db.collection("calendarIntegrations").doc(calAccId),
            {
              localUpdatedAt: Timestamp.now(),
              cloudUpdatedAt: Timestamp.now(),
              status: "locked",
              // resourceId: typeof resourceId === "string" ? resourceId : null,
            },
            { merge: true }
          );
          await batch.commit();
          return {
            statusCode: 400,
            isMevolveError: false,
          };
        } else {
          // console.log("cal: at " + accessToken);
          const client = getMicrosoftClient(accessToken);
          const calendarsListResponse = await client
            .api("/me/calendars?$select=id,name,color,changeKey,canEdit,owner")
            .get();
          // console.log("cal: " + JSON.stringify(calendarsListResponse));
          const calendarsList = calendarsListResponse.value as Calendar[];

          if (
            calendarsList == null ||
            calendarsList.length == 0 ||
            calendarsList.findIndex(
              (item) => item.name == "Mevolve" && item.id == mevolveCalId
            ) == -1
          ) {
            if (markMevolveError == true) {
              const db = admin.firestore();
              const batch = db.batch();
              batch.set(
                db.collection("calendarIntegrations").doc(calAccId),
                {
                  localUpdatedAt: Timestamp.now(),
                  cloudUpdatedAt: Timestamp.now(),
                  mevolveStatus: "mevolveError",
                  // resourceId: typeof resourceId === "string" ? resourceId : null,
                },
                { merge: true }
              );
              await batch.commit();
            }
            return {
              statusCode: 200,
              isMevolveError: true,
            };
          } else {
            const db = admin.firestore();
            const calData = await db
              .collection("calendarIntegrations")
              .doc(calAccId)
              .get();
            if (calData.exists) {
              const currCalendarIntegrations =
                calData.data() as unknown as ICalendarIntegrations;
              if (currCalendarIntegrations.status == "locked") {
                const batch = db.batch();
                batch.set(
                  db.collection("calendarIntegrations").doc(calAccId),
                  {
                    localUpdatedAt: Timestamp.now(),
                    cloudUpdatedAt: Timestamp.now(),
                    status: "active",
                    // resourceId: typeof resourceId === "string" ? resourceId : null,
                  },
                  { merge: true }
                );
                await batch.commit();
              }
            }
            return {
              statusCode: 200,
              isMevolveError: false,
            };
          }
        }
      }
      return {
        statusCode: 200,
        isMevolveError: false,
      };
    }
    return {
      statusCode: 200,
      isMevolveError: false,
    };
  } catch (error) {
    logger.log(
      `Calendar Mevolve check for: ${uid} and calId: ${calAccId} failed with error: ${error}`
    );
    return {
      statusCode: 400,
      isMevolveError: false,
    };
  }
}

export async function createMevolveCalendar(
  logger: MeLogger,
  uid: string,
  calAccId: string,
  mevolveCalId: string,
  refreshKey: string,
  type: string,
  redirectUri: string | null
) {
  try {
    logger.log(
      `Resync Calendar Mevolve creation for: ${uid} and calId: ${calAccId}`
    );
    if (type == "google") {
      const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
      const refreshToken = decryptTextData(refreshKey, userSecret);
      const accessToken = await getAccessToken(refreshToken, type, redirectUri);
      const oauth2Client = getGoogleOAuth2Client(redirectUri ?? null);
      const calendar = getGoogleCalendarInstance();
      oauth2Client.setCredentials({
        access_token: accessToken,
      });
      const calGroupsList = await calendar.calendarList.list({
        auth: oauth2Client,
        quotaUser: uid,
      });
      const mevIndex =
        calGroupsList?.data?.items?.findIndex(
          (item) => item.summary == "Mevolve" && item.deleted != true
        ) ?? -1;
      if (
        calGroupsList.data.items != null &&
        calGroupsList.data.items.length > 0 &&
        mevIndex > -1
      ) {
        mevolveCalId = calGroupsList.data.items[mevIndex].id ?? "";
      } else {
        const mevolveGroup = await calendar.calendars.insert({
          auth: oauth2Client,
          requestBody: { summary: "Mevolve" },
          quotaUser: uid,
        });
        mevolveCalId = mevolveGroup.data.id ?? "";
      }
      logger.log(
        `Resync Calendar Mevolve creation success for: ${uid}, calId: ${calAccId} and mevoleCalId: ${mevolveCalId}`
      );
      return {
        statusCode: 200,
        updatedMevolveId: mevolveCalId,
      };
      // }
    } else if (type == "microsoft") {
      const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
      const refreshToken = decryptTextData(refreshKey, userSecret);
      const accessToken = await getAccessToken(refreshToken, type, redirectUri);
      if (accessToken != null) {
        const client = getMicrosoftClient(accessToken);
        const calendarsListResponse = await client
          .api("/me/calendars?$select=id,name,color,changeKey,canEdit,owner")
          .get();
        const calendarsList = calendarsListResponse.value as Calendar[];
        const mevIndex =
          calendarsList?.findIndex((item) => item.name == "Mevolve") ?? -1;
        if (
          calendarsList != null &&
          calendarsList.length > 0 &&
          mevIndex > -1
        ) {
          mevolveCalId = calendarsList[mevIndex].id ?? "";
        } else {
          const mevolveGroup = (await client
            .api("/me/calendars")
            .post({ name: "Mevolve" })) as Calendar;
          mevolveCalId = mevolveGroup.id ?? "";
        }
        logger.log(
          `Resync Calendar Mevolve creation success for: ${uid}, calId: ${calAccId} and mevoleCalId: ${mevolveCalId}`
        );
        return {
          statusCode: 200,
          updatedMevolveId: mevolveCalId,
        };
      }
    }
    return {
      statusCode: 500,
    };
  } catch (error) {
    logger.log(
      `Resync Calendar Mevolve creation for: ${uid} and calId: ${calAccId} failed with error: ${error}`
    );
    return {
      statusCode: 500,
    };
  }
}

export async function pushTasksToCalendarHandler(
  logger: MeLogger,
  uid: string,
  calAccId: string,
  mevolveCalId: string | null,
  refreshKey: string,
  timeZone: string,
  type: string,
  taskList: string,
  syncTodos: string | null,
  syncHabits: string | null,
  syncJournals: string | null,
  isResync?: boolean | null,
  redirectUri?: string | null
) {
  logger.log(
    `Save Calendar Push Tasks for: ${uid} and calId: ${calAccId} and taskList: ${taskList.length}`
  );
  try {
    const tasksInsert: unknown[] = [];
    const tasksUpdate: unknown[] = [];
    const isMevIdUpdated = false;
    const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
    const refreshToken = decryptTextData(refreshKey, userSecret);
    const accessToken = await getAccessToken(refreshToken, type);

    if (accessToken != null) {
      if (type == "google") {
        const oauth2Client = getGoogleOAuth2Client(redirectUri ?? null);
        const calendar = getGoogleCalendarInstance();
        oauth2Client.setCredentials({
          access_token: accessToken,
        });

        // if (mevolveCalId == null) {
        //   const mevolveGroup = await calendar.calendars.insert({
        //     auth: oauth2Client,
        //     requestBody: { summary: "Mevolve" },
        //     quotaUser: uid,
        //   });
        //   mevolveCalId = mevolveGroup.data.id ?? "";
        //   isMevIdUpdated = true;
        // }
        if (mevolveCalId != null) {
          // const helperData: Map<string, string> = new Map();
          for (const syncData of taskList) {
            const parsedSyncData = ZSyncRequestModel.parse(syncData);
            const dataReceived: IBaseSchema = JSON.parse(parsedSyncData.data);
            const finalData = dataReceived as unknown as IDocBaseSchema;
            // (await new DataMigrationService().migrateSchema(
            //   dataReceived,
            //   helperData
            // )) as unknown as IDocBaseSchema;

            if (
              ((finalData.docCollection == "todos" &&
                syncTodos == "disabled") ||
                (finalData.docCollection == "habitSetups" &&
                  syncHabits == "disabled") ||
                (finalData.docCollection == "journalSetups" &&
                  syncJournals == "disabled")) &&
              isResync != true
            ) {
              const event = {
                id: finalData.id.replace(/-/g, ""),
              };

              tasksInsert.push(
                calendar.events
                  .delete({
                    eventId: event.id,
                    calendarId: mevolveCalId,
                    auth: oauth2Client,
                    quotaUser: uid,
                  })
                  .catch((_) => {
                    null;
                  })
              );
            } else {
              if (
                finalData.startAt != null &&
                finalData.deletedAt == null &&
                finalData.permaDeletedAt == null &&
                (finalData.docCollection == "todos" ||
                  finalData.docCollection == "habitSetups" ||
                  finalData.docCollection == "journalSetups")
              ) {
                try {
                  const event = getGoogleEventObject(
                    finalData,
                    timeZone,
                    syncTodos,
                    syncHabits,
                    syncJournals
                  );
                  tasksInsert.push(
                    calendar.events
                      .insert({
                        calendarId: mevolveCalId,
                        auth: oauth2Client,
                        requestBody: event,
                        quotaUser: uid,
                      })
                      .catch((_) => {
                        // console.log(`Calendar tasksInsert  ${event.id}: ${e}`);
                        // const updateUrl = `https://www.googleapis.com/calendar/v3/calendars/${mevolveCalId}/events/${event.id}?quotaUser=${uid}`;
                        // tasksUpdate.push(
                        //   axios.put(updateUrl, event, {
                        //     headers: {
                        //       "Content-Type": "application/json",
                        //       Authorization: `Bearer ${accessToken}`,
                        //     },
                        //   })
                        tasksUpdate.push(
                          calendar.events
                            .update({
                              eventId: event.id ?? "",
                              calendarId: mevolveCalId ?? "",
                              auth: oauth2Client,
                              requestBody: event,
                              quotaUser: uid,
                            })
                            .catch((_) => {
                              null;
                            })
                        );
                      })
                  );
                } catch (error) {
                  logger.log(
                    `Save Calendar Push Tasks Parsing error for google ${finalData.docCollection}/${finalData.id}: ${error}`
                  );
                }
              }
            }
          }
        }
      } else {
        const client = getMicrosoftClient(accessToken);
        // console.log("MS event access: " + accessToken);

        if (mevolveCalId == null) {
          const mevolveGroup = (await client
            .api("/me/calendars")
            .post({ name: "Mevolve" })) as Calendar;
          mevolveCalId = mevolveGroup.id ?? "";
        }
        if (mevolveCalId != null) {
          // const helperData: Map<string, string> = new Map();
          for (const syncData of taskList) {
            const parsedSyncData = ZSyncRequestModel.parse(syncData);
            const dataReceived: IBaseSchema = JSON.parse(parsedSyncData.data);
            const finalData = dataReceived as unknown as IDocBaseSchema;
            // (await new DataMigrationService().migrateSchema(
            //   dataReceived,
            //   helperData
            // )) as unknown as IDocBaseSchema;
            if (
              ((finalData.docCollection == "todos" &&
                syncTodos == "disabled") ||
                (finalData.docCollection == "habitSetups" &&
                  syncHabits == "disabled") ||
                (finalData.docCollection == "journalSetups" &&
                  syncJournals == "disabled")) &&
              isResync != true
            ) {
              // const event = getMSEventObject(
              //   finalData,
              //   timeZone,
              //   syncTodos,
              //   syncHabits,
              //   syncJournals
              // );

              const event: Event = {
                subject: "",
                transactionId: finalData.id.replace(/-/g, ""),
              };
              tasksInsert.push(
                client
                  .api(`/me/calendars/${mevolveCalId}/events`)
                  .post(event)
                  .then((res) => {
                    tasksUpdate.push(
                      client
                        .api(`/me/calendars/${mevolveCalId}/events/${res.id}`)
                        .delete()
                        .catch((_) => {
                          null;
                        })
                    );
                  })
                  .catch((e) => {
                    if (e.code == "ErrorDuplicateTransactionId") {
                      const id = JSON.parse(e.body)["@event.existingEventId"];
                      tasksUpdate.push(
                        client
                          .api(`/me/calendars/${mevolveCalId}/events/${id}`)
                          .delete()
                          .catch((_) => {
                            null;
                          })
                      );
                    } else {
                      logger.log(
                        "MS event: " +
                          JSON.stringify(event) +
                          " failed with error: " +
                          e
                      );
                    }
                  })
              );
            } else {
              if (
                finalData.startAt != null &&
                finalData.deletedAt == null &&
                finalData.permaDeletedAt == null
              ) {
                try {
                  const event = getMSEventObject(
                    finalData,
                    timeZone,
                    syncTodos,
                    syncHabits,
                    syncJournals
                  );
                  if (event != null) {
                    tasksInsert.push(
                      client
                        .api(`/me/calendars/${mevolveCalId}/events`)
                        .post(event)
                        .catch((e) => {
                          logger.log("MS event insert error: " + e);
                          if (e.code == "ErrorDuplicateTransactionId") {
                            const id = JSON.parse(e.body)[
                              "@event.existingEventId"
                            ];
                            tasksUpdate.push(
                              client
                                .api(
                                  `/me/calendars/${mevolveCalId}/events/${id}`
                                )
                                .update(event)
                            );
                          } else {
                            logger.log(
                              "MS event: " +
                                JSON.stringify(event) +
                                " failed with error: " +
                                e
                            );
                          }
                        })
                    );
                  }
                } catch (error) {
                  logger.log(
                    `Save Calendar Push Tasks Parsing error for ms ${finalData.docCollection}/${finalData.id}: ${error}`
                  );
                }
              }
            }
          }
        }
      }
    }
    if (tasksInsert.length > 50) {
      for (let i = 0; i < tasksInsert.length; i += 50) {
        await Promise.allSettled(
          tasksInsert.slice(
            i,
            i + 50 >= tasksInsert.length ? i + (tasksInsert.length - i) : i + 50
          )
        );
      }
    } else {
      await Promise.allSettled(tasksInsert);
    }
    if (tasksUpdate.length > 0) {
      if (tasksUpdate.length > 50) {
        for (let i = 0; i < tasksUpdate.length; i += 50) {
          const list = tasksUpdate.slice(
            i,
            i + 50 >= tasksUpdate.length ? i + (tasksUpdate.length - i) : i + 50
          );
          await Promise.allSettled(list);
        }
      } else {
        await Promise.allSettled(tasksUpdate);
      }
    }
    logger.log(
      `Save Calendar Push Tasks success for: ${uid} and calId: ${calAccId}`
    );
    return {
      statusCode: 200,
      body: "Success",
      updatedMevolveId: isMevIdUpdated ? mevolveCalId : null,
    };
  } catch (error) {
    logger.log(`Calendar Push Tasks Error  ${error}`);
    return {
      statusCode: 500,
      error: "" + error,
    };
  }
}

export async function parseGoogleCalendarEvents(
  uid: string,
  calAccountId: string,
  calAccountEmail: string,
  calGroupId: string,
  summary: string,
  eventList: calendar_v3.Schema$Event[],
  calGroupStatus: string | null | undefined,
  defaultMinutes: number | null
): Promise<Map<string, object>[]> {
  const calEventSetupsMap: Map<string, object> = new Map<string, object>();
  const calEventActionsMap: Map<string, object> = new Map<string, object>();
  const secretsMap: Map<string, string> = new Map<string, string>();
  const userPublicKey = (await getUserKeyDocById(uid))?.publicKey;
  if (userPublicKey != null) {
    for (let i = 0; i < eventList.length; i++) {
      const calEvent = eventList[i];
      if (
        calEvent.source?.title != null &&
        calEvent.source?.title.includes("Mevolve")
      ) {
        break;
      }
      // console.log("parseGoogleCalendarEvents: " + JSON.stringify(calEvent));
      let start = 0;
      let end = 0;
      let duration = 0;
      let startAt;
      let isStartTimeSet = true;
      let endAt;
      const reminderAt: number[] = [];
      const calEventStartDT =
        calEvent.start ?? calEvent.originalStartTime ?? null;
      if (calEventStartDT?.dateTime != null) {
        start = Date.parse(calEventStartDT?.dateTime);
        const dateString = new Date(
          calEventStartDT?.dateTime.includes("+")
            ? calEventStartDT?.dateTime.slice(0, -6)
            : calEventStartDT?.dateTime
        );
        startAt = {
          dateString: `${dateString.getFullYear()}-${(dateString.getMonth() + 1).toString().padStart(2, "0")}-${dateString.getDate().toString().padStart(2, "0")}`,
          dateTime: Timestamp.fromMillis(start),
          timeString: `${dateString.getUTCHours().toString().padStart(2, "0").toString()}:${dateString.getUTCMinutes().toString().padStart(2, "0")}`,
          timeWithOffset: calEventStartDT?.dateTime,
        };
      } else if (calEventStartDT?.date != null) {
        isStartTimeSet = false;
        start = Date.parse(calEventStartDT.date);
        const dateString = new Date(start);
        startAt = {
          dateString: calEventStartDT.date,
          dateTime: Timestamp.fromMillis(start),
          timeString: "00:00",
          timeWithOffset: dateString.toISOString(),
        };
      }

      if (calEvent.end != null) {
        if (calEvent.end?.dateTime != null) {
          end = Date.parse(calEvent.end?.dateTime);
          duration = (end - start) / 60000;
          const dateString = new Date(
            calEvent.end?.dateTime.includes("+")
              ? calEvent.end?.dateTime.slice(0, -6)
              : calEvent.end?.dateTime
          );
          endAt = {
            dateString: `${dateString.getFullYear()}-${(dateString.getMonth() + 1).toString().padStart(2, "0")}-${dateString.getDate().toString().padStart(2, "0")}`,
            dateTime: Timestamp.fromMillis(end),
            timeString: `${dateString.getHours().toString().padStart(2, "0").toString()}:${dateString.getMinutes().toString().padStart(2, "0")}`,
            timeWithOffset: calEvent.end?.dateTime,
          };
        } else if (calEvent.end?.date != null) {
          end = Date.parse(calEvent.end.date);
          duration = (end - start) / 60000;
          const dateString = new Date(end);
          endAt = {
            dateString: calEvent.end.date,
            dateTime: Timestamp.fromMillis(end),
            timeString: "00:00",
            timeWithOffset: dateString.toISOString(),
          };
        }
      } else {
        endAt = null;
      }

      if (calGroupStatus == "syncNotifications" && calEvent.reminders != null) {
        if (calEvent.reminders.useDefault == true) {
          reminderAt.push(-Math.abs(defaultMinutes ?? 10));
        }
        if (
          calEvent.reminders.useDefault != true &&
          calEvent.reminders.overrides != null &&
          calEvent.reminders.overrides.length > 0
        ) {
          for (let i = 0; i < calEvent.reminders.overrides.length; i++) {
            const minutes = calEvent.reminders.overrides[i].minutes;
            if (
              calEvent.reminders.overrides[i].method == "popup" &&
              minutes != null
            ) {
              if (minutes == 0) {
                reminderAt.push(0);
              } else {
                reminderAt.push(-Math.abs(minutes));
              }
            }
          }
        }
      }

      if (calEvent.id != null) {
        if (calEvent.recurringEventId == null) {
          const id = `${calAccountId.substring(0, 7)}_${calEvent.id}`;

          let secret = secretsMap.get(calEvent.id);
          if (secret == null) {
            secret = getNewSecret();
            secretsMap.set(calEvent.id, secret);
          }
          const encData = {
            dek: await encryptDocKey(secret, uid, userPublicKey),
            encFields: ["title", "description"],
          };
          if (calEvent.recurrence != null && calEvent.recurrence.length > 0) {
            const rrule = RRule.fromString(calEvent.recurrence[0]);
            if (rrule.options.until != null) {
              const dateString = new Date(rrule.options.until);
              endAt = {
                dateString: `${dateString.getFullYear()}-${(dateString.getMonth() + 1).toString().padStart(2, "0")}-${dateString.getDate().toString().padStart(2, "0")}`,
                dateTime: Timestamp.fromDate(dateString),
                timeString: `${dateString.getHours().toString().padStart(2, "0").toString()}:${dateString.getMinutes().toString().padStart(2, "0")}`,
                timeWithOffset: dateString.toISOString(),
              };
            } else {
              endAt = null;
            }
          }
          const calEventMap = {
            id: id,
            docVer: currentDbVersion,
            docCollection: "calendarEventSetups",
            uid: uid,
            title:
              calEvent.summary == null
                ? ""
                : encryptTextData(calEvent.summary, secret),
            description:
              calEvent.description == null || calEvent.description.length == 0
                ? ""
                : encryptTextData(calEvent.description, secret),
            startAt: startAt ?? null,
            endAt: endAt ?? null,
            isStartTimeSet: isStartTimeSet,
            isTmzAffected: true,
            repeat: calEvent.recurrence ?? [],
            duration: duration,
            reminderAt: reminderAt,
            deletedAt: calEvent.status == "cancelled" ? Timestamp.now() : null,
            permaDeletedAt:
              calEvent.status == "cancelled" ? Timestamp.now() : null,
            createdAt:
              calEvent.created == null
                ? Timestamp.now()
                : Timestamp.fromMillis(Date.parse(calEvent.created)),
            localUpdatedAt: Timestamp.now(),
            cloudUpdatedAt: Timestamp.now(),
            attachments: calEvent.attachments ?? [],
            location: calEvent.location ?? null,
            calendarId: calGroupId,
            calendarIntegrationId: calAccountId,
            calendarName: summary ?? "",
            calendarAccountId: `g_${calAccountEmail}`,
            visibility: calEvent.visibility ?? null,
            eventType: calEvent.eventType ?? null,
            status: calEvent.status ?? null,
            conferenceData: JSON.stringify(calEvent.conferenceData) ?? "",
            rawData: JSON.stringify(calEvent) ?? "",
            encData: encData,
            type: "google",
          };

          calEventSetupsMap.set(
            id,
            calEventMap as unknown as Map<string, unknown>
          );
        } else {
          const calRecurringId = calEvent.recurringEventId;
          const id = `${calAccountId.substring(0, 7)}_${calEvent.id}`;

          const setupId = `${calAccountId.substring(0, 7)}_${calRecurringId}`;

          // const setupEvent = eventList.find(
          //   (item) => item.id == calRecurringId
          // );

          // if (
          //   setupEvent != null &&
          //   (setupEvent.summary != calEvent.summary ||
          //     setupEvent.description != calEvent.description)
          // ) {
          let secret = secretsMap.get(calRecurringId);
          if (secret == null) {
            secret = getNewSecret();
            secretsMap.set(calRecurringId, secret);
          }

          const attachList: object[] = [];
          if (calEvent.attachments != null) {
            for (let i = 0; i < calEvent.attachments.length; i++) {
              const attach = calEvent.attachments[i];
              attachList.push({
                fileId: attach.fileId,
                fileUrl: attach.fileUrl,
                iconLink: attach.iconLink,
                mimeType: attach.mimeType,
                title: attach.title ?? "",
              });
            }
          }

          const encData = {
            dek: await encryptDocKey(secret, uid, userPublicKey),
            encFields: ["title", "description"],
          };

          const subEventMap = {
            id: id,
            docVer: currentDbVersion,
            docCollection: "calendarEventActions",
            setupId: setupId,
            uid: uid,
            title:
              calEvent.summary == null
                ? ""
                : encryptTextData(calEvent.summary, secret),
            description:
              calEvent.description == null || calEvent.description.length == 0
                ? ""
                : encryptTextData(calEvent.description, secret),
            startAt: startAt ?? null,
            endAt: endAt ?? null,
            dueAt: startAt ?? null,
            duration: duration,
            isStartTimeSet: isStartTimeSet,
            isTmzAffected: true,
            reminderAt: reminderAt,
            attachments: calEvent.attachments ?? [],
            deletedAt: calEvent.status == "cancelled" ? Timestamp.now() : null,
            permaDeletedAt:
              calEvent.status == "cancelled" ? Timestamp.now() : null,
            localUpdatedAt: Timestamp.now(),
            cloudUpdatedAt: Timestamp.now(),
            location: calEvent.location ?? null,
            calendarId: calGroupId,
            calendarIntegrationId: calAccountId,
            calendarName: summary ?? "",
            calendarAccountId: `g_${calAccountEmail}`,
            visibility: calEvent.visibility ?? null,
            eventType: calEvent.eventType ?? null,
            status: calEvent.status ?? null,
            conferenceData: JSON.stringify(calEvent.conferenceData) ?? "",
            rawData: JSON.stringify(calEvent) ?? "",
            encData: encData,
            type: "google",
          };

          calEventActionsMap.set(
            id,
            subEventMap as unknown as Map<string, unknown>
          );
        }
      }
    }
  }
  return [calEventSetupsMap, calEventActionsMap];
}

export async function parseMicrosoftCalendarEvents(
  uid: string,
  calAccountId: string,
  calAccountEmail: string,
  calGroupId: string,
  summary: string,
  eventList: Event[],
  calGroupStatus: string | null | undefined,
  accessToken: string
): Promise<Map<string, object>[]> {
  const calEventSetupsMap: Map<string, object> = new Map<string, object>();
  const calEventActionsMap: Map<string, object> = new Map<string, object>();
  const secretsMap: Map<string, string> = new Map<string, string>();
  const userPublicKey = (await getUserKeyDocById(uid))?.publicKey;
  if (userPublicKey != null) {
    for (let i = 0; i < eventList.length; i++) {
      const calEvent = eventList[i];
      // console.log("parseMicrosoftCalendarEvents: " + JSON.stringify(calEvent));
      let start = 0;
      let end = 0;
      let duration = 0;
      let startAt;
      let isStartTimeSet = false;
      let endAt;

      // console.log("cal: event: " + calEvent.subject?.toString());
      const reminderAt: number[] = [];
      if (calEvent.start?.dateTime != null) {
        isStartTimeSet = !(calEvent.isAllDay ?? false);
        start = Date.parse(calEvent.start?.dateTime);
        const dateString = new Date(calEvent.start?.dateTime);
        startAt = {
          dateString: `${dateString.getFullYear()}-${(dateString.getMonth() + 1).toString().padStart(2, "0")}-${dateString.getDate().toString().padStart(2, "0")}`,
          dateTime: Timestamp.fromMillis(start),
          timeString: `${dateString.getUTCHours().toString().padStart(2, "0").toString()}:${dateString.getUTCMinutes().toString().padStart(2, "0")}`,
          timeWithOffset: calEvent.start?.dateTime,
        };
      }

      if (calEvent.end != null && calEvent.end?.dateTime != null) {
        end = Date.parse(calEvent.end?.dateTime);
        duration = (end - start) / 60000;
        if (calEvent?.recurrence?.range?.type != "noEnd") {
          const dateString = new Date(
            calEvent.end?.dateTime.includes("+")
              ? calEvent.end?.dateTime.slice(0, -6)
              : calEvent.end?.dateTime
          );
          endAt = {
            dateString: `${dateString.getFullYear()}-${(dateString.getMonth() + 1).toString().padStart(2, "0")}-${dateString.getDate().toString().padStart(2, "0")}`,
            dateTime: Timestamp.fromMillis(end),
            timeString: `${dateString.getHours().toString().padStart(2, "0").toString()}:${dateString.getMinutes().toString().padStart(2, "0")}`,
            timeWithOffset: calEvent.end?.dateTime,
          };
        }
      } else {
        endAt = null;
      }
      if (
        calGroupStatus == "syncNotifications" &&
        calEvent.isReminderOn == true &&
        calEvent.reminderMinutesBeforeStart != null
      ) {
        if (calEvent.reminderMinutesBeforeStart == 0) {
          reminderAt.push(0);
        } else {
          reminderAt.push(-Math.abs(calEvent.reminderMinutesBeforeStart));
        }
      }
      if (calEvent.id != null) {
        if (
          calEvent.seriesMasterId == undefined ||
          calEvent.seriesMasterId == null
        ) {
          const id = `${calAccountId.substring(0, 7)}_${calEvent.id}`;

          let secret = secretsMap.get(calEvent.id);
          if (secret == null) {
            secret = getNewSecret();
            secretsMap.set(calEvent.id, secret);
          }
          const encData = {
            dek: await encryptDocKey(secret, uid, userPublicKey),
            encFields: ["title", "description"],
          };
          let rrule: RRule | null = null;
          if (
            calEvent.recurrence != null &&
            calEvent.recurrence.pattern?.type != null
          ) {
            const endDate = calEvent.recurrence.range?.endDate;
            let frequency: Frequency;
            const weekday: ByWeekday[] = [];
            let monthDay: number | null = null;
            let byMonth: number | null = null;
            switch (calEvent.recurrence.pattern.type) {
              case "daily":
                frequency = Frequency.DAILY;
                break;
              case "weekly":
                frequency = Frequency.WEEKLY;
                if (calEvent.recurrence.pattern.daysOfWeek != null) {
                  for (
                    let i = 0;
                    i < calEvent.recurrence.pattern.daysOfWeek.length;
                    i++
                  ) {
                    weekday.push(
                      getWeekdayFromDayOfWeek(
                        calEvent.recurrence.pattern.daysOfWeek[i],
                        null
                      ) as ByWeekday
                    );
                  }
                }
                break;
              case "absoluteMonthly":
                frequency = Frequency.MONTHLY;
                monthDay = calEvent.recurrence.pattern.dayOfMonth ?? null;
                break;
              case "relativeMonthly":
                frequency = Frequency.MONTHLY;
                if (calEvent.recurrence.pattern.daysOfWeek != null) {
                  for (
                    let i = 0;
                    i < calEvent.recurrence.pattern.daysOfWeek.length;
                    i++
                  ) {
                    weekday.push(
                      getWeekdayFromDayOfWeek(
                        calEvent.recurrence.pattern.daysOfWeek[i],
                        calEvent.recurrence.pattern.index ?? null
                      )
                    );
                  }
                }
                break;
              case "absoluteYearly":
                frequency = Frequency.YEARLY;
                byMonth = calEvent.recurrence.pattern.month ?? null;
                monthDay = calEvent.recurrence.pattern.dayOfMonth ?? null;
                break;
              case "relativeYearly":
                frequency = Frequency.YEARLY;
                monthDay = calEvent.recurrence.pattern.dayOfMonth ?? null;
                break;
            }

            rrule = new RRule({
              freq: frequency,
              interval: calEvent.recurrence.pattern?.interval ?? 1,
              until:
                endDate != null && calEvent.recurrence.range?.type != "noEnd"
                  ? new Date(endDate)
                  : null,
              bymonth: byMonth,
              bymonthday: monthDay,
              byweekday: weekday, // [RRule.WE, RRule.TH, RRule.FR],
              count:
                endDate == null &&
                calEvent.recurrence.range?.numberOfOccurrences != null &&
                calEvent.recurrence.range?.numberOfOccurrences > 0
                  ? calEvent.recurrence.range?.numberOfOccurrences
                  : null,
              wkst:
                calEvent.recurrence.pattern.firstDayOfWeek != null
                  ? getWeekdayFromDayOfWeek(
                      calEvent.recurrence.pattern.firstDayOfWeek,
                      null
                    )
                  : null,
            });

            if (
              endDate != null &&
              calEvent?.recurrence?.range?.type != "noEnd"
            ) {
              const dateString = new Date(endDate);
              endAt = {
                dateString: `${dateString.getFullYear()}-${(dateString.getMonth() + 1).toString().padStart(2, "0")}-${dateString.getDate().toString().padStart(2, "0")}`,
                dateTime: Timestamp.fromDate(dateString),
                timeString: `${dateString.getHours().toString().padStart(2, "0").toString()}:${dateString.getMinutes().toString().padStart(2, "0")}`,
                timeWithOffset: dateString.toISOString(),
              };
            } else {
              endAt = null;
            }
          }

          const calEventMap = {
            id: id,
            docVer: currentDbVersion,
            docCollection: "calendarEventSetups",
            uid: uid,
            title:
              calEvent.subject == null || calEvent.subject.length == 0
                ? ""
                : encryptTextData(calEvent.subject, secret),
            description:
              calEvent.body?.content == null ||
              calEvent.body.content.length == 0
                ? ""
                : encryptTextData(calEvent.body.content, secret),
            startAt: startAt,
            endAt: endAt,
            isStartTimeSet: isStartTimeSet,
            isTmzAffected: true,
            repeat: rrule == null ? [] : [rrule.toString()],
            duration: duration,
            reminderAt: reminderAt,
            deletedAt: calEvent.isCancelled == true ? Timestamp.now() : null,
            permaDeletedAt:
              calEvent.isCancelled == true ? Timestamp.now() : null,
            createdAt:
              calEvent.createdDateTime == null
                ? Timestamp.now()
                : Timestamp.fromMillis(Date.parse(calEvent.createdDateTime)),
            localUpdatedAt: Timestamp.now(),
            cloudUpdatedAt: Timestamp.now(),
            attachments: calEvent.attachments ?? [],
            location: calEvent.location?.displayName ?? null,
            calendarId: calGroupId,
            calendarIntegrationId: calAccountId,
            calendarName: summary ?? "",
            calendarAccountId: `m_${calAccountEmail}`,
            visibility: calEvent.sensitivity ?? null,
            eventType: calEvent.importance ?? null,
            status: calEvent.responseStatus?.response ?? null,
            conferenceData:
              calEvent.onlineMeeting != null
                ? JSON.stringify(calEvent.onlineMeeting)
                : null,
            rawData: JSON.stringify(calEvent) ?? "",
            encData: encData,
            type: "microsoft",
          };

          calEventSetupsMap.set(
            id,
            calEventMap as unknown as Map<string, unknown>
          );

          if (calEvent.recurrence != null) {
            const client = getMicrosoftClient(accessToken, true);
            const apiUrl =
              `me/Events/${calEvent.id}` +
              "?$select=subject,body,bodyPreview,seriesMasterId,type,recurrence,start,end,isReminderOn," +
              "reminderMinutesBeforeStart,isAllDay,isCancelled,createdDateTime,location,sensitivity,showAs," +
              "importance,responseStatus,onlineMeeting,cancelledOccurrences&$expand=exceptionOccurrences";

            let event: Event | null = null;
            event = (await client.api(apiUrl).get()) as Event;
            // console.log("cal: event: " + JSON.stringify(event));
            if (event.cancelledOccurrences != null) {
              for (let i = 0; i < event.cancelledOccurrences.length; i++) {
                const dateStringCancel = event.cancelledOccurrences[
                  i
                ].substring(event.cancelledOccurrences[i].length - 10);
                const dateObj = new Date(dateStringCancel);

                const id = `${calAccountId.substring(0, 7)}_${calEvent.id}_${dateStringCancel.replace("-", "")}`;
                const setupId = `${calAccountId.substring(0, 7)}_${calEvent.id}`;

                let startCancel = 0;
                // let endCancel = 0;
                let startAtCancel;
                if (calEvent.start?.dateTime != null) {
                  const dateString = new Date(calEvent.start?.dateTime);
                  dateString.setDate(dateObj.getDate());
                  dateString.setMonth(dateObj.getMonth());
                  dateString.setFullYear(dateObj.getFullYear());
                  startCancel = dateString.valueOf();
                  startAtCancel = {
                    dateString: `${dateString.getFullYear()}-${(dateString.getMonth() + 1).toString().padStart(2, "0")}-${dateString.getDate().toString().padStart(2, "0")}`,
                    dateTime: Timestamp.fromMillis(startCancel),
                    timeString: `${dateString.getUTCHours().toString().padStart(2, "0").toString()}:${dateString.getUTCMinutes().toString().padStart(2, "0")}`,
                    timeWithOffset: dateString.toISOString(),
                  };
                }

                const subEventMap = {
                  ...calEventMap,
                  id: id,
                  docVer: currentDbVersion,
                  docCollection: "calendarEventActions",
                  uid: uid,
                  setupId: setupId,
                  startAt: startAtCancel,
                  dueAt: startAtCancel,
                  isTmzAffected: true,
                  reminderAt: [],
                  deletedAt: Timestamp.now(),
                  permaDeletedAt: Timestamp.now(),
                  attachments: [],
                  localUpdatedAt: Timestamp.now(),
                  cloudUpdatedAt: Timestamp.now(),
                  encData: encData,
                  type: "microsoft",
                };
                calEventActionsMap.set(
                  id,
                  subEventMap as unknown as Map<string, unknown>
                );
              }
            }

            if (event.exceptionOccurrences != null) {
              for (let i = 0; i < event.exceptionOccurrences.length; i++) {
                const calEventInstance = event.exceptionOccurrences[i];
                const calRecurringId = calEventInstance.seriesMasterId ?? "";
                const id = `${calAccountId.substring(0, 7)}_${calEventInstance.id}`;

                const setupId = `${calAccountId.substring(0, 7)}_${calRecurringId}`;

                let currEvent = new Map(
                  Object.entries(calEventSetupsMap.get(id) ?? {})
                );
                if (currEvent == null) {
                  currEvent = new Map<string, object>();
                }

                let secret = secretsMap.get(calRecurringId);
                if (secret == null) {
                  secret = getNewSecret();
                  secretsMap.set(calRecurringId, secret);
                }

                const encData = {
                  dek: await encryptDocKey(secret, uid, userPublicKey),
                  encFields: ["title", "description"],
                };

                let start = 0;
                let end = 0;
                let duration = 0;
                let startAt;
                let isStartTimeSet = false;
                let endAt;

                // console.log("cal: event: " + calEvent.subject?.toString());
                const reminderAt: number[] = [];
                if (calEventInstance.start?.dateTime != null) {
                  isStartTimeSet = !(calEventInstance.isAllDay ?? false);
                  start = Date.parse(calEventInstance.start?.dateTime);
                  const dateString = new Date(calEventInstance.start?.dateTime);
                  startAt = {
                    dateString: `${dateString.getFullYear()}-${(dateString.getMonth() + 1).toString().padStart(2, "0")}-${dateString.getDate().toString().padStart(2, "0")}`,
                    dateTime: Timestamp.fromMillis(start),
                    timeString: `${dateString.getUTCHours().toString().padStart(2, "0").toString()}:${dateString.getUTCMinutes().toString().padStart(2, "0")}`,
                    timeWithOffset: calEventInstance.start?.dateTime,
                  };
                }
                if (
                  calEventInstance.end != null &&
                  calEventInstance.end?.dateTime != null
                ) {
                  end = Date.parse(calEventInstance.end?.dateTime);
                  duration = (end - start) / 60000;
                  if (calEventInstance?.recurrence?.range?.type != "noEnd") {
                    end = Date.parse(calEventInstance.end?.dateTime);
                    duration = (end - start) / 60000;
                    const dateString = new Date(
                      calEventInstance.end?.dateTime.includes("+")
                        ? calEventInstance.end?.dateTime.slice(0, -6)
                        : calEventInstance.end?.dateTime
                    );
                    endAt = {
                      dateString: `${dateString.getFullYear()}-${(dateString.getMonth() + 1).toString().padStart(2, "0")}-${dateString.getDate().toString().padStart(2, "0")}`,
                      dateTime: Timestamp.fromMillis(end),
                      timeString: `${dateString.getHours().toString().padStart(2, "0").toString()}:${dateString.getMinutes().toString().padStart(2, "0")}`,
                      timeWithOffset: calEventInstance.end?.dateTime,
                    };
                  }
                } else {
                  endAt = null;
                }
                if (
                  calGroupStatus == "syncNotifications" &&
                  calEventInstance.isReminderOn == true &&
                  calEventInstance.reminderMinutesBeforeStart != null
                ) {
                  if (calEventInstance.reminderMinutesBeforeStart == 0) {
                    reminderAt.push(0);
                  } else {
                    reminderAt.push(
                      -Math.abs(calEventInstance.reminderMinutesBeforeStart)
                    );
                  }
                }
                const subEventMap = {
                  id: id,
                  docVer: currentDbVersion,
                  docCollection: "calendarEventActions",
                  uid: uid,
                  setupId: setupId,
                  title:
                    calEventInstance.subject == null ||
                    calEventInstance.subject.length == 0
                      ? ""
                      : encryptTextData(calEventInstance.subject, secret),
                  description:
                    calEventInstance.body?.content == null ||
                    calEventInstance.body?.content.length == 0
                      ? ""
                      : encryptTextData(calEventInstance.body.content, secret),
                  startAt: startAt,
                  endAt: endAt,
                  dueAt: startAt,
                  duration: duration,
                  isStartTimeSet: isStartTimeSet,
                  isTmzAffected: true,
                  reminderAt: reminderAt,
                  deletedAt:
                    calEventInstance.isCancelled == true
                      ? Timestamp.now()
                      : null,
                  permaDeletedAt:
                    calEventInstance.isCancelled == true
                      ? Timestamp.now()
                      : null,
                  attachments: calEventInstance.attachments ?? [],
                  localUpdatedAt: Timestamp.now(),
                  cloudUpdatedAt: Timestamp.now(),
                  location: calEventInstance.location?.displayName ?? null,
                  calendarId: calGroupId,
                  calendarIntegrationId: calAccountId,
                  calendarName: summary ?? "",
                  calendarAccountId: `m_${calAccountEmail}`,
                  visibility: calEventInstance.sensitivity ?? null,
                  eventType: calEventInstance.importance ?? null,
                  status: calEventInstance.responseStatus?.response ?? null,
                  conferenceData:
                    calEventInstance.onlineMeeting != null
                      ? JSON.stringify(calEventInstance.onlineMeeting)
                      : null,
                  rawData: JSON.stringify(calEventInstance) ?? "",
                  encData: encData,
                  type: "microsoft",
                };

                calEventActionsMap.set(
                  id,
                  subEventMap as unknown as Map<string, unknown>
                );
              }
            }
          }
        }
      }
    }
  }
  return [calEventSetupsMap, calEventActionsMap];
}

function getWeekNameFromNum(num: number): WeekIndex {
  let weekName: WeekIndex;
  switch (num) {
    case 1:
      weekName = "first";
      break;
    case 2:
      weekName = "second";
      break;
    case 3:
      weekName = "third";
      break;
    case 4:
      weekName = "fourth";
      break;
    case -1:
      weekName = "last";
      break;
    default:
      weekName = "first";
  }
  return weekName;
}

function getWeekdayFromDayOfWeek(day: DayOfWeek, index: string | null) {
  if (index == null) {
    return Weekday.fromStr(day.substring(0, 2).toUpperCase() as WeekdayStr);
  }
  let nth: number;
  switch (index) {
    case "first":
      nth = 1;
      break;
    case "second":
      nth = 2;
      break;
    case "third":
      nth = 3;
      break;
    case "fourth":
      nth = 4;
      break;
    case "last":
      nth = -1;
      break;
    default:
      nth = 1;
  }
  return Weekday.fromStr(day.substring(0, 2).toUpperCase() as WeekdayStr).nth(
    nth
  );
}

function getWeekdayListFromNumList(numberList: number[]) {
  const list: DayOfWeek[] = [];
  for (const i of numberList) {
    switch (i) {
      case 0:
        list.push("monday");
        break;
      case 1:
        list.push("tuesday");
        break;
      case 2:
        list.push("wednesday");
        break;
      case 3:
        list.push("thursday");
        break;
      case 4:
        list.push("friday");
        break;
      case 5:
        list.push("saturday");
        break;
      case 6:
        list.push("sunday");
        break;
      default:
        list.push("monday");
        break;
    }
  }
  return list;
}

function getGoogleEventObject(
  finalData: IDocBaseSchema,
  timeZone: string,
  syncTodos: string | null,
  syncHabits: string | null,
  syncJournals: string | null
): calendar_v3.Schema$Event {
  const title = finalData.title;
  const description =
    finalData.description != null && finalData.description.length > 0
      ? finalData.description
      : "";
  const rrule =
    finalData.repeat.length > 0 ? RRule.fromString(finalData.repeat[0]) : null;
  let startAt =
    finalData.startAt != null
      ? new Date(finalData.startAt.timeWithOffset)
      : null;
  let endAt =
    startAt != null
      ? new Date(startAt.getTime() + (finalData.duration ?? 15) * 60000)
      : null;
  // console.log("cal:" + JSON.stringify(rrule));
  const newRrule =
    rrule != null
      ? new RRule({
          freq: rrule.options.freq,
          interval: rrule.options.interval,
          until:
            finalData.endAt != null
              ? new Date(finalData.endAt.dateString)
              : null,
          bymonth: rrule.options.bymonth,
          bymonthday: [
            ...rrule.options.bymonthday,
            ...rrule.options.bynmonthday,
          ],
          bynmonthday: rrule.options.bynmonthday,
          bynweekday: rrule.options.bynweekday,
          byweekday: rrule.options.byweekday,
        })
      : null;
  let ds = finalData.startAt?.timeWithOffset ?? "";
  if (rrule != null) {
    const startRrule = new RRule({
      freq: rrule.options.freq,
      interval: rrule.options.interval,
      until:
        finalData.endAt != null ? new Date(finalData.endAt.dateString) : null,
      bymonth: rrule.options.bymonth,
      bymonthday: [...rrule.options.bymonthday, ...rrule.options.bynmonthday],
      bynmonthday: rrule.options.bynmonthday,
      byweekday: rrule.options.byweekday,
      bynweekday: rrule.options.bynweekday,
      dtstart:
        finalData.startAt?.dateString != null
          ? new Date(finalData.startAt?.dateString)
          : null,
    });
    // console.log("cal:" + JSON.stringify(startRrule));

    const date = startRrule?.after(
      finalData.startAt?.dateString != null
        ? new Date(finalData.startAt?.dateString)
        : new Date(),
      true
    );
    if (date != null) {
      // console.log("cal:date" + JSON.stringify(date));
      if (startAt != null) {
        ds =
          JSON.stringify(date).substring(1, 11) +
          (finalData.startAt?.timeWithOffset.substring(10) ?? "");
        startAt = new Date(ds);
        endAt =
          startAt != null
            ? new Date(startAt.getTime() + (finalData.duration ?? 15) * 60000)
            : null;
        // console.log("cal:" + JSON.stringify(ds));
      }
    }
  }
  const endAtString =
    finalData.endAt != null && endAt != null
      ? JSON.stringify(new Date(finalData.endAt.dateString))
          .replace(/[-:]/g, "")
          .substring(1, 9) +
        endAt.toISOString().replace(/[-:]/g, "").substring(8, 15)
      : null;

  const newRruleString =
    newRrule != null
      ? endAtString != null
        ? `${finalData.repeat[0]};UNTIL=${endAtString}Z`
        : finalData.repeat[0]
      : null;

  // Reminder handling for Google Calendar
  const reminderList: calendar_v3.Schema$EventReminder[] = [];
  let reminderAt = {};

  if (
    (finalData.docCollection == "todos" && syncTodos == "onlySync") ||
    (finalData.docCollection == "habitSetups" && syncHabits == "onlySync") ||
    (finalData.docCollection == "journalSetups" && syncJournals == "onlySync")
  ) {
    // For "onlySync" mode, disable reminders completely
    reminderAt = {
      useDefault: false,
    };
  } else {
    // Process reminders
    if (finalData.reminderAt != null && finalData.reminderAt.length > 0) {
      // 1. Filter to keep only reminders before the event (negative values)
      // 2. Convert to positive minutes (as Google expects)
      // 3. Remove duplicates with Set
      // 4. Sort them (optional, for consistency)
      const uniqueReminderMinutes = [
        ...new Set(
          finalData.reminderAt
            .filter((minutes) => minutes <= 0)
            .map((minutes) => Math.abs(minutes))
        ),
      ].sort((a, b) => a - b);

      // Convert to Google's reminder format
      for (const minutes of uniqueReminderMinutes) {
        reminderList.push({
          method: "popup",
          minutes: minutes,
        });
      }
    }

    // Set the reminder configuration
    if (reminderList.length > 0) {
      reminderAt = {
        overrides: reminderList,
        useDefault: false,
      };
    } else {
      reminderAt = {
        useDefault: true,
      };
    }
  }

  const event: calendar_v3.Schema$Event = {
    id: finalData.id.replace(/-/g, ""),
    summary: title,
    description: description,
    start: {
      date: finalData.isStartTimeSet
        ? null
        : newRrule == null
          ? finalData.startAt?.dateString
          : ds.slice(0, 10),
      dateTime: finalData.isStartTimeSet ? startAt?.toISOString() : null,
      timeZone: timeZone,
    },
    end: {
      date: finalData.isStartTimeSet
        ? null
        : newRrule == null
          ? finalData.startAt?.dateString
          : ds.slice(0, 10),
      dateTime: finalData.isStartTimeSet ? endAt?.toISOString() : null,
      timeZone: timeZone,
    },
    recurrence: newRruleString != null ? [newRruleString] : null,
    reminders: reminderAt,
    // source: {
    //   title: `Mevolve ${finalData.docCollection}`,
    //   url: "https://www.mevolve.app/",
    // },
  };
  // console.log("cal: " + JSON.stringify(event));
  return event;
}

function getMSEventObject(
  finalData: IDocBaseSchema,
  timeZone: string,
  syncTodos: string | null,
  syncHabits: string | null,
  syncJournals: string | null
): Event | null {
  const title = finalData.title;
  const description =
    finalData.description != null && finalData.description.length > 0
      ? finalData.description
      : "";
  const rrule =
    finalData.repeat.length > 0 ? RRule.fromString(finalData.repeat[0]) : null;
  let startAt =
    finalData.startAt != null ? new Date(finalData.startAt.dateTime) : null;
  let endAt =
    startAt != null
      ? new Date(startAt.getTime() + (finalData.duration ?? 15) * 60000)
      : null;
  const newRrule =
    rrule != null
      ? new RRule({
          freq: rrule.options.freq,
          interval: rrule.options.interval,
          until:
            finalData.endAt != null
              ? new Date(finalData.endAt.dateString)
              : null,
          bymonth: rrule.options.bymonth,
          bymonthday: rrule.options.bymonthday,
          bynmonthday: rrule.options.bynmonthday,
          byweekday: rrule.options.byweekday,
          bynweekday: rrule.options.bynweekday,
        })
      : null;
  let ds = finalData.startAt?.timeWithOffset ?? "";
  if (rrule != null) {
    const startRrule = new RRule({
      freq: rrule.options.freq,
      interval: rrule.options.interval,
      until:
        finalData.endAt != null ? new Date(finalData.endAt.dateString) : null,
      bymonth: rrule.options.bymonth,
      bymonthday: rrule.options.bymonthday,
      bynmonthday: rrule.options.bynmonthday,
      byweekday: rrule.options.byweekday,
      bynweekday: rrule.options.bynweekday,
      dtstart:
        finalData.startAt?.dateString != null
          ? new Date(finalData.startAt?.dateString)
          : null,
    });
    const date = startRrule?.after(startAt ?? new Date(), true);
    if (date != null) {
      if (startAt != null) {
        ds =
          JSON.stringify(date).substring(1, 11) +
          (finalData.startAt?.timeWithOffset.substring(10) ?? "");
        startAt = new Date(ds);
        endAt =
          startAt != null
            ? new Date(startAt.getTime() + (finalData.duration ?? 15) * 60000)
            : null;
      }
    }
  }

  // Reminder handling for Microsoft Calendar
  let reminderAt: number | null = 15; // Default is 15 minutes

  if (
    (finalData.docCollection == "todos" && syncTodos == "onlySync") ||
    (finalData.docCollection == "habitSetups" && syncHabits == "onlySync") ||
    (finalData.docCollection == "journalSetups" && syncJournals == "onlySync")
  ) {
    // For "onlySync" mode, disable reminders
    reminderAt = null;
  } else if (finalData.reminderAt != null && finalData.reminderAt.length > 0) {
    // Filter to keep only reminders before the event (negative values)
    const beforeEventReminders = finalData.reminderAt
      .filter((minutes) => minutes <= 0)
      .map((minutes) => Math.abs(minutes));

    if (beforeEventReminders.length > 0) {
      // Since Microsoft only supports one reminder, pick the closest to event time
      // (smallest value means closest to event start)
      reminderAt = Math.min(...beforeEventReminders);
    }
  }

  let type: RecurrencePatternType = "daily";
  let dayOfMonth: number | null = null;
  let month: number | null = null;
  let daysOfWeek: DayOfWeek[] | null = null;
  let indexOfWeek: WeekIndex | null = null;
  // console.log("cal: rrule ms " + JSON.stringify(newRrule?.origOptions));
  switch (newRrule?.origOptions.freq) {
    case Frequency.DAILY:
      type = "daily";
      break;
    case Frequency.WEEKLY:
      type = "weekly";
      daysOfWeek = getWeekdayListFromNumList(newRrule.options.byweekday);
      break;
    case Frequency.MONTHLY:
      if (
        newRrule.origOptions.bynmonthday != null &&
        newRrule.origOptions.bynmonthday.length > 0
      ) {
        if (
          newRrule.origOptions.bynmonthday instanceof Array &&
          newRrule.origOptions.bynmonthday.length > 1
        ) {
          return null;
        }
        if (newRrule.options.bymonth.length > 11) {
          type = "absoluteMonthly";
          dayOfMonth =
            newRrule.origOptions.bynmonthday instanceof Array
              ? newRrule.origOptions.bynmonthday[0] == -1
                ? 31
                : newRrule.origOptions.bynmonthday[0]
              : newRrule.origOptions.bynmonthday == -1
                ? 31
                : newRrule.origOptions.bynmonthday;
        } else if (newRrule.options.bymonth.length == 1) {
          type = "absoluteYearly";
          dayOfMonth =
            newRrule.origOptions.bynmonthday instanceof Array
              ? newRrule.origOptions.bynmonthday[0] == -1
                ? 31
                : newRrule.origOptions.bynmonthday[0]
              : newRrule.origOptions.bynmonthday == -1
                ? 31
                : newRrule.origOptions.bynmonthday;
          month = newRrule.options.bymonth[0];
        } else {
          return null;
        }
      } else if (
        newRrule.origOptions.bymonthday != null &&
        (newRrule.origOptions.bymonthday instanceof Array
          ? newRrule.origOptions.bymonthday.length > 0
          : true)
      ) {
        if (
          newRrule.origOptions.bymonthday instanceof Array &&
          newRrule.origOptions.bymonthday.length > 1
        ) {
          return null;
        }
        if (newRrule.options.bymonth.length > 11) {
          if (
            newRrule.origOptions.bymonthday instanceof Array &&
            newRrule.origOptions.bymonthday.length > 30
          ) {
            type = "daily";
          } else {
            type = "absoluteMonthly";
            dayOfMonth =
              newRrule.origOptions.bymonthday instanceof Array
                ? newRrule.origOptions.bymonthday[0] == -1
                  ? 31
                  : newRrule.origOptions.bymonthday[0]
                : newRrule.origOptions.bymonthday == -1
                  ? 31
                  : newRrule.origOptions.bymonthday;
          }
        } else {
          type = "absoluteYearly";
          dayOfMonth = newRrule.options.bymonthday[0];
          month = newRrule.options.bymonth[0];
        }
      } else if (
        newRrule.origOptions.bynweekday != null &&
        newRrule.origOptions.bynweekday.length > 0
      ) {
        if (newRrule.origOptions.bynweekday.length > 1) {
          return null;
        }
        type = "relativeMonthly";
        const onlyWeekDay: number[] = [];
        onlyWeekDay.push(newRrule.origOptions.bynweekday[0][0]);
        indexOfWeek = getWeekNameFromNum(
          newRrule.origOptions.bynweekday[0][1] as number
        );
        // newRrule.origOptions.bynweekday.forEach((value, index) => {
        //   if (!onlyWeekDay.includes(value[0])) {
        //     onlyWeekDay.push(value[0]);
        //   }
        //   if (index == 0) {
        //     indexOfWeek = getWeekNameFromNum(value[1])
        //   }
        // });
        daysOfWeek = getWeekdayListFromNumList(onlyWeekDay);
      }
      break;
    case Frequency.YEARLY:
      if (
        newRrule.options.bymonthday.length > 0 &&
        newRrule.options.bymonth.length > 0
      ) {
        type = "absoluteYearly";
        dayOfMonth = newRrule.options.bymonthday[0];
        month = newRrule.options.bymonth[0];
      }
      break;
    default:
      type = "daily";
      break;
  }

  const event: Event = {
    subject: title,
    body: {
      contentType: "html",
      content: description,
    },
    start: {
      dateTime: finalData.isStartTimeSet
        ? startAt?.toISOString()
        : newRrule == null
          ? finalData.startAt?.dateString
          : ds.slice(0, 10),
      timeZone: timeZone,
    },
    end: {
      dateTime: finalData.isStartTimeSet
        ? endAt?.toISOString()
        : newRrule == null
          ? finalData.startAt?.dateString
          : ds.slice(0, 10),
      timeZone: timeZone,
    },
    isReminderOn: reminderAt != null,
    reminderMinutesBeforeStart: reminderAt ?? 15,
    recurrence:
      newRrule != null
        ? {
            pattern: {
              ...(dayOfMonth != null && { dayOfMonth: dayOfMonth }),
              firstDayOfWeek: "monday",
              interval: rrule?.options.interval,
              ...(month != null && { month: month }),
              type: type,
              ...(daysOfWeek != null && { daysOfWeek: daysOfWeek }),
              ...(indexOfWeek != null && { index: indexOfWeek }),
            },
            range: {
              ...(finalData.endAt != null && {
                endDate: finalData.endAt.dateString,
              }),
              numberOfOccurrences: rrule?.options.count ?? 0,
              recurrenceTimeZone: timeZone,
              startDate:
                finalData.startAt != null ? finalData.startAt.dateString : "",
              type:
                rrule?.options.count != null && rrule?.options.count > 0
                  ? "numbered"
                  : finalData.endAt != null
                    ? "endDate"
                    : "noEnd",
            },
          }
        : null,
    transactionId: finalData.id.replace(/-/g, ""),
  };
  // console.log("cal: " + JSON.stringify(event));
  return event;
}

const docbaseZodObj = {
  id: z.string(),
  docVer: z.number(),
  docCollection: ZDocCollectionName,
  deletedAt: dateSchema.nullable().optional(),
  permaDeletedAt: dateSchema.nullable().optional(),
  isStartTimeSet: z.boolean().nullable(),
  repeat: z.array(z.string()),
  startAt: meDateTimeObj.nullable(),
  endAt: meDateTimeObj.nullable(),
  title: z.string().nullable(),
  description: z.string().nullable().default(null),
  reminderAt: z.array(z.number()).nullable().optional().default(null),
  isPaused: z.boolean().nullable().optional().default(false),
  duration: z.number().default(15),
};

const ZDocBaseSchema = z.object({
  ...docbaseZodObj,
});

type IDocBaseSchema = z.infer<typeof ZDocBaseSchema>;

const ZDeleteSchema = z.object({
  id: z.string(),
  docCollection: ZDocCollectionName,
});

export type IDeleteSchema = z.infer<typeof ZDeleteSchema>;

export async function getCalendarsToResub() {
  const todaysDate = new Date();
  console.log(`expiry date = ${todaysDate}`);
  const calendarsToResubQuery = await admin
    .firestore()
    .collection("calendarIntegrations")
    .where("webhookExpiry", "<=", todaysDate)
    .limit(200);

  let dataSnapshot = await calendarsToResubQuery.get();
  console.log(`length = ${dataSnapshot.docs.length}`);

  while (dataSnapshot.docs.length > 0) {
    const tasks: Promise<any>[] = [];
    for (const document of dataSnapshot.docs) {
      const calData = document.data() as ICalendarIntegrations;
      if (calData.deletedAt == null && calData.refreshToken != null) {
        const dbVersion = await getUserDbVersion(calData.uid);
        tasks.push(
          publishMessageToPubSub(`resubcalendarwebhooks.v${dbVersion}`, {
            uid: calData.uid,
            calId: calData.id,
          })
        );
        // const uid = calData.uid;
        // const calId = calData.id;
        // const logger = new MeLogger(uid);
        // tasks.push(resubCalendarWebhooksHandler(logger, uid, calId));
      }
    }
    await Promise.allSettled(tasks);
    const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
    dataSnapshot = await calendarsToResubQuery.startAfter(last).get();
    console.log("next length of snapshot = " + dataSnapshot.docs.length);
  }
}

// Add this to calendar_api_handler.ts
export async function refreshCalendarList(
  logger: MeLogger,
  uid: string,
  calAccId: string,
  refreshKey: string,
  type: string,
  currentCalDetails: ICalDetailsType,
  redirectUri: string | null
) {
  logger.log(`Refreshing calendar list for: ${uid} and calId: ${calAccId}`);
  const db = admin.firestore();
  const userSecret = (await getSecretFromKmsForUserByEmail(uid)).key;
  const refreshToken = decryptTextData(refreshKey, userSecret);
  const accessToken = await getAccessToken(refreshToken, type, redirectUri);

  if (!accessToken) {
    logger.log(`Failed to get access token for: ${uid}`);
    return;
  }

  try {
    // Get current calendar groups
    const existingCalendarGroups = [...currentCalDetails.calendarGroups];
    const existingCalendarIds = new Set(
      existingCalendarGroups.map((group) => group.id)
    );

    // Retain Mevolve calendar
    const mevolveGroupIndex = existingCalendarGroups.findIndex(
      (group) => group.title === "Mevolve"
    );
    const mevolveGroup =
      mevolveGroupIndex > -1 ? existingCalendarGroups[mevolveGroupIndex] : null;

    // Fetch current calendar list from provider
    if (type === "google") {
      const oauth2Client = getGoogleOAuth2Client(redirectUri ?? null);
      const calendar = getGoogleCalendarInstance();

      oauth2Client.setCredentials({
        access_token: accessToken,
      });

      const calGroupsList = await calendar.calendarList.list({
        auth: oauth2Client,
        quotaUser: uid,
      });

      if (calGroupsList.data.items && calGroupsList.data.items.length > 0) {
        // Create new calendar groups array
        const updatedCalendarGroups = [];

        // Add Mevolve calendar first if it exists
        if (mevolveGroup) {
          updatedCalendarGroups.push(mevolveGroup);
        }

        // Add all calendars from Google
        for (const calGroup of calGroupsList.data.items) {
          if (
            calGroup.id &&
            !calGroup.summary?.includes("Holiday") &&
            calGroup.summary !== "Mevolve" &&
            !calGroup.deleted
          ) {
            if (existingCalendarIds.has(calGroup.id)) {
              // Calendar already exists - keep existing settings
              const existingGroup = existingCalendarGroups.find(
                (g) => g.id === calGroup.id
              );
              if (existingGroup) {
                updatedCalendarGroups.push(existingGroup);
              }
            } else {
              // New calendar - add with default settings
              const calMap = {
                id: calGroup.id,
                title: calGroup.summary || "",
                description: calGroup.description || null,
                nextSyncToken: null,
                nextPageToken: null,
                syncStatus: "disabled", // Default to disabled for new calendars
                resourceId: null,
                channelId: null,
              };
              updatedCalendarGroups.push(calMap);
              logger.log(
                `Added new calendar: ${calGroup.summary} for user: ${uid}`
              );
            }
          }
        }

        // Update the database
        await db.collection("calendarIntegrations").doc(calAccId).update({
          calendarGroups: updatedCalendarGroups,
          localUpdatedAt: Timestamp.now(),
          cloudUpdatedAt: Timestamp.now(),
        });

        logger.log(
          `Updated calendar list for: ${uid}, found ${updatedCalendarGroups.length} calendars`
        );
      }
    } else if (type === "microsoft") {
      const client = getMicrosoftClient(accessToken);
      const calendarsListResponse = await client
        .api("/me/calendars?$select=id,name,color,changeKey,canEdit,owner")
        .get();
      const calendarsList = calendarsListResponse.value as Calendar[];

      if (calendarsList && calendarsList.length > 0) {
        // Create new calendar groups array
        const updatedCalendarGroups = [];

        // Add Mevolve calendar first if it exists
        if (mevolveGroup) {
          updatedCalendarGroups.push(mevolveGroup);
        }

        // Add all calendars from Microsoft
        for (const calGroup of calendarsList) {
          if (
            calGroup.id &&
            !calGroup.name?.includes("holiday") &&
            calGroup.name !== "Mevolve"
          ) {
            if (existingCalendarIds.has(calGroup.id)) {
              // Calendar already exists - keep existing settings
              const existingGroup = existingCalendarGroups.find(
                (g) => g.id === calGroup.id
              );
              if (existingGroup) {
                updatedCalendarGroups.push(existingGroup);
              }
            } else {
              // New calendar - add with default settings
              const calMap = {
                id: calGroup.id,
                title: calGroup.name || "",
                description: null,
                nextSyncToken: null,
                nextPageToken: null,
                syncStatus: "disabled", // Default to disabled for new calendars
                resourceId: null,
                channelId: null,
              };
              updatedCalendarGroups.push(calMap);
              logger.log(
                `Added new calendar: ${calGroup.name} for user: ${uid}`
              );
            }
          }
        }

        // Update the database
        await db.collection("calendarIntegrations").doc(calAccId).update({
          calendarGroups: updatedCalendarGroups,
          localUpdatedAt: Timestamp.now(),
          cloudUpdatedAt: Timestamp.now(),
        });

        logger.log(
          `Updated calendar list for: ${uid}, found ${updatedCalendarGroups.length} calendars`
        );
      }
    }
  } catch (error) {
    logger.error(`Failed to refresh calendar list: ${error}`);
  }
}
