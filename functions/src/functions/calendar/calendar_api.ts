import * as functions from "firebase-functions/v2";
import {
  checkMevolveCalendarHandler,
  deleteAllCalendars<PERSON>andler,
  deleteCalendarHandler,
  getCalendarDetailsHandler,
  resubCalendarWebhooksHandler,
  resyncCalendar<PERSON>and<PERSON>,
  saveCalendarConfig<PERSON>andler,
  saveNewCalendarHandler,
  syncTasksToCalendarHandler,
} from "./components/calendar_api_handler";
import {
  handleCalListWebhook,
  handleWebhook,
} from "./components/calendar_webhook_handler";
import { onMessagePublished } from "firebase-functions/v2/pubsub";
import { currentDbVersion } from "../migration/versions/base/model_mappings";
import { MeLogger } from "../../utils/logger/models/me_logger";

export const getCalendarDetails = functions.https.onCall(
  {
    memory: "512MiB",
    concurrency: 5,
  },
  async (request) => {
    const logger = new MeLogger(request.data.uid);
    return getCalendarDetailsHandler(
      logger,
      request.data.uid,
      request.data.id,
      request.data.email,
      request.data.refreshToken,
      request.data.serverAuthCode,
      request.data.accessToken,
      request.data.name,
      request.data.type,
      request.data.redirectUri
    );
  }
);

export const saveNewCalendar = functions.https.onCall(
  {
    memory: "512MiB",
    concurrency: 2,
    timeoutSeconds: 240,
  },
  async (request) => {
    console.log("Save New Calendar called with data: ", request.data);
    const logger = new MeLogger(request.auth?.uid);
    return saveNewCalendarHandler(
      logger,
      request.auth?.uid ?? "",
      request.data.calendarDetails,
      request.data.calAccId,
      request.data.mevolveCalId ?? null,
      request.data.refreshKey ?? null,
      request.data.timeZone ?? null,
      request.data.eventsList ?? null,
      request.data.tasksList ?? null,
      request.data.type ?? null,
      request.data.calendarConfig ?? null,
      request.data.redirectUri ?? null
    );
  }
);

export const checkMevolveCalendar = functions.https.onCall(
  {
    memory: "512MiB",
    concurrency: 2,
  },
  async (request) => {
    console.log("Check Mevolve Calendar called with data: ", request.data);
    const logger = new MeLogger(request.auth?.uid);
    return checkMevolveCalendarHandler(
      logger,
      request.auth?.uid ?? "",
      request.data.calAccId,
      request.data.mevolveCalId,
      request.data.refreshKey,
      request.data.type,
      true,
      request.data.redirectUri ?? null
    );
  }
);

export const saveCalendarConfig = functions.https.onCall(
  {
    memory: "512MiB",
    concurrency: 2,
    timeoutSeconds: 300,
  },
  async (request) => {
    console.log("Save Calendar Config called with data: ", request.data);
    const logger = new MeLogger(request.auth?.uid);
    return saveCalendarConfigHandler(
      logger,
      request.auth?.uid ?? "",
      request.data.calendarDetails,
      request.data.calAccId,
      request.data.mevolveCalId ?? null,
      request.data.refreshKey,
      request.data.timeZone,
      request.data.eventsList ?? null,
      request.data.tasksList ?? null,
      request.data.type,
      request.data.calendarConfig,
      request.data.redirectUri ?? null
    );
  }
);

export const syncTasksToCalendar = functions.https.onCall(
  {
    memory: "512MiB",
    concurrency: 5,
  },
  async (request) => {
    console.log("Sync Tasks To Calendar called with data: ", request.data);
    const logger = new MeLogger(request.auth?.uid);
    return syncTasksToCalendarHandler(
      logger,
      request.auth?.uid ?? "",
      request.data.dataList,
      request.data.timeZone,
      request.data.redirectUri ?? null
    );
  }
);

export const resyncCalendar = functions.https.onCall(
  {
    memory: "512MiB",
    concurrency: 2,
    timeoutSeconds: 300,
  },
  async (request) => {
    console.log("Resync Calendar called with data: ", request.data);
    const logger = new MeLogger(request.auth?.uid);
    return resyncCalendarHandler(
      logger,
      request.auth?.uid ?? "",
      request.data.calendarDetails,
      request.data.calAccId,
      request.data.mevolveCalId ?? null,
      request.data.refreshKey,
      request.data.timeZone,
      request.data.eventsList ?? null,
      request.data.tasksList ?? null,
      request.data.type,
      request.data.calendarConfig,
      request.data.redirectUri ?? null
    );
  }
);

export const deleteCalendar = functions.https.onCall(
  {
    memory: "512MiB",
    concurrency: 2,
    timeoutSeconds: 120,
  },
  async (request) => {
    console.log("Delete Calendar called with data: ", request.data);
    const logger = new MeLogger(request.data.uid);
    return deleteCalendarHandler(
      logger,
      request.data.uid,
      request.data.calId,
      request.data.dataList,
      request.data.type,
      false,
      request.data.eventList ?? null,
      request.data.redirectUri ?? null
    );
  }
);

exports.calendarEventHandler = onMessagePublished(
  {
    topic: `calendar.event.v${currentDbVersion}`,
    memory: "512MiB",
    concurrency: 2,
    timeoutSeconds: 120,
  },
  async (event) => {
    // functions.logger.info("getting calendar events", event);
    const data = event.data.message.json;
    const uid = data.token.match("(?<=uid=).+.+?(?=\\?type)")?.[0] ?? "";
    const logger = new MeLogger(uid);
    const type = data.token.match("(?<=type=).+.+?(?=\\?)")?.[0] ?? "";
    if (type == "googleCalList" || type == "microsoftCalList") {
      return handleCalListWebhook(
        logger,
        data.chanId,
        data.token,
        data.resourceId
      );
    } else {
      return handleWebhook(
        logger,
        data.chanId,
        data.token,
        data.resourceId,
        data.eventId
      );
    }
  }
);

exports.googlecalendarEventHandler = onMessagePublished(
  {
    topic: `gocalendar.event.v${currentDbVersion}`,
    memory: "512MiB",
    concurrency: 2,
    timeoutSeconds: 120,
  },
  async (event) => {
    // functions.logger.info("getting calendar events", event);
    const data = event.data.message.json;
    const uid = data.token.match("(?<=uid=).+.+?(?=\\?type)")?.[0] ?? "";
    const logger = new MeLogger(uid);
    const type = data.token.match("(?<=type=).+.+?(?=\\?)")?.[0] ?? "";
    const calId = data.token.match("(?<=calId=).+")?.[0] ?? "";
    logger.log(
      `Calendar Webhook handle received for user: ${uid}, calId: ${calId}, chanId: ${data.chanId} and resId: ${data.resourceId}`
    );
    if (type == "googleCalList" || type == "microsoftCalList") {
      return handleCalListWebhook(
        logger,
        data.chanId,
        data.token,
        data.resourceId
      );
    } else {
      return handleWebhook(
        logger,
        data.chanId,
        data.token,
        data.resourceId,
        data.eventId
      );
    }
  }
);

exports.mscalendarEventHandler = onMessagePublished(
  {
    topic: `mscalendar.event.v${currentDbVersion}`,
    memory: "512MiB",
    concurrency: 2,
    timeoutSeconds: 120,
  },
  async (event) => {
    const data = event.data.message.json;
    const uid = data.token.match("(?<=uid=).+.+?(?=\\?type)")?.[0] ?? "";
    const logger = new MeLogger(uid);
    const type = data.token.match("(?<=type=).+.+?(?=\\?)")?.[0] ?? "";
    const calId = data.token.match("(?<=calId=).+")?.[0] ?? "";
    logger.log(
      `Calendar Webhook handle received for user: ${uid}, calId: ${calId}, chanId: ${data.chanId} and resId: ${data.resourceId}`
    );
    if (type == "googleCalList" || type == "microsoftCalList") {
      return handleCalListWebhook(
        logger,
        data.chanId,
        data.token,
        data.resourceId
      );
    } else {
      return handleWebhook(
        logger,
        data.chanId,
        data.token,
        data.resourceId,
        data.eventId
      );
    }
  }
);

exports.deleteAllCalendars = onMessagePublished(
  {
    topic: `deleteallcalendars.v${currentDbVersion}`,
    memory: "512MiB",
    concurrency: 2,
    timeoutSeconds: 120,
  },
  async (event) => {
    const data = event.data.message.json;
    const uid = data.uid;
    const logger = new MeLogger(uid);
    logger.log(`Delete All Calendars requested for user: ${uid}`);

    return deleteAllCalendarsHandler(logger, data.uid, data.redirectUri ?? null);
  }
);

exports.resubCalendarWebhooks = onMessagePublished(
  {
    topic: `resubcalendarwebhooks.v${currentDbVersion}`,
    memory: "512MiB",
    concurrency: 5,
    timeoutSeconds: 120,
  },
  async (event) => {
    const data = event.data.message.json;
    const uid = data.uid;
    const calId = data.calId;
    const logger = new MeLogger(uid);
    logger.log(
      `Resub Calendar Webhooks requested for user: ${uid} and calId: ${calId}`
    );
    return resubCalendarWebhooksHandler(logger, data.uid, data.calId);
  }
);
