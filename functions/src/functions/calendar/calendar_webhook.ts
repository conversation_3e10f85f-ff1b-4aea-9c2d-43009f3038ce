/* eslint-disable camelcase */
import * as functions from "firebase-functions/v2";
import {
  getUserDbVersion,
  publishMessageToPubSub,
} from "../../utils/utility_methods";
import { ChangeNotification } from "@microsoft/microsoft-graph-types-beta";

export const calendarwebhook = functions.https.onRequest(
  // Check Function: Used for calendar webhook so appcheck need disabled.
  { enforceAppCheck: false, memory: "512MiB", timeoutSeconds: 540 },
  async (req, _res) => {
    _res.status(200).send();
    console.log(
      "Received google calendarwebhook : " + JSON.stringify(req.headers)
    );
    const chanId =
      req.headers["X-Goog-Channel-ID".toLowerCase()]?.toString() ?? "";
    const token =
      req.headers["X-Goog-Channel-Token".toLowerCase()]?.toString() ?? "";
    const state = req.headers["X-Goog-Resource-State".toLowerCase()];
    const resourceId = req.headers["X-Goog-Resource-ID".toLowerCase()];
    if (state != "sync") {
      const uid = token.match("(?<=uid=).+.+?(?=\\?type)")?.[0] ?? "";
      const calId = token.match("(?<=calId=).+")?.[0] ?? "";
      const dbVersion = await getUserDbVersion(uid);
      if (dbVersion > 0) {
        if (dbVersion >= 121) {
          console.log(
            `Publishing gocalendar.event.v${dbVersion} for user : ${uid} and google calId: ${calId}`
          );
          await publishMessageToPubSub(`gocalendar.event.v${dbVersion}`, {
            chanId: chanId,
            token: token,
            resourceId: resourceId,
          });
          console.log(
            `Published gocalendar.event.v${dbVersion} for user : ${uid} and google calId: ${calId}`
          );
        } else {
          console.log(
            `Publishing calendar.event.v${dbVersion} for user : ${uid} and google calId: ${calId}`
          );
          await publishMessageToPubSub(`calendar.event.v${dbVersion}`, {
            chanId: chanId,
            token: token,
            resourceId: resourceId,
          });
          console.log(
            `Published gocalendar.event.v${dbVersion} for user : ${uid} and google calId: ${calId}`
          );
        }
      }
    }
  }
);

export const auth = functions.https.onRequest(
  // OAuth callback endpoint for Microsoft and Google calendar authentication
  { enforceAppCheck: false, memory: "256MiB", timeoutSeconds: 60 },
  async (req, res) => {
    console.log("Received calendar OAuth callback:", {
      query: req.query,
      userAgent: req.headers["user-agent"],
      referer: req.headers["referer"]
    });

    const code = req.query.code as string;
    const error = req.query.error as string;
    const errorDescription = req.query.error_description as string;
    const state = req.query.state as string;

    // Create HTML response with PostMessage communication
    const html = `
    <!DOCTYPE html>
    <html>
    <head>
        <title>Calendar Authentication</title>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }

            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', sans-serif;
                background: #ffffff;
                color: #1a1a1a;
                line-height: 1.6;
                height: 100vh;
                display: flex;
                align-items: center;
                justify-content: center;
                -webkit-font-smoothing: antialiased;
                -moz-osx-font-smoothing: grayscale;
            }

            .container {
                text-align: center;
                padding: 0 20px;
                max-width: 320px;
                width: 100%;
            }

            .icon {
                width: 48px;
                height: 48px;
                margin: 0 auto 24px;
                position: relative;
            }

            /* Minimalist loading spinner */
            .spinner {
                width: 48px;
                height: 48px;
                border: 2px solid #f0f0f0;
                border-top-color: #1a1a1a;
                border-radius: 50%;
                animation: spin 0.8s ease-in-out infinite;
            }

            /* Checkmark icon */
            .checkmark {
                width: 48px;
                height: 48px;
                stroke-width: 2;
                stroke: #1a1a1a;
                fill: none;
                animation: scaleIn 0.3s ease-out;
            }

            .checkmark-circle {
                stroke-dasharray: 166;
                stroke-dashoffset: 166;
                animation: strokeIn 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
            }

            .checkmark-check {
                stroke-dasharray: 48;
                stroke-dashoffset: 48;
                animation: strokeIn 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.3s forwards;
            }

            /* X icon for errors */
            .error-icon {
                width: 48px;
                height: 48px;
                stroke-width: 2;
                stroke: #dc2626;
                fill: none;
                animation: scaleIn 0.3s ease-out;
            }

            .error-circle {
                stroke-dasharray: 166;
                stroke-dashoffset: 166;
                animation: strokeIn 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
            }

            .error-x {
                stroke-dasharray: 48;
                stroke-dashoffset: 48;
                animation: strokeIn 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.3s forwards;
            }

            h2 {
                font-size: 20px;
                font-weight: 500;
                margin-bottom: 8px;
                letter-spacing: -0.02em;
            }

            p {
                font-size: 14px;
                color: #6b6b6b;
                font-weight: 400;
            }

            .error-text {
                color: #dc2626;
            }

            @keyframes spin {
                to { transform: rotate(360deg); }
            }

            @keyframes scaleIn {
                from { transform: scale(0.8); opacity: 0; }
                to { transform: scale(1); opacity: 1; }
            }

            @keyframes strokeIn {
                to { stroke-dashoffset: 0; }
            }

            @keyframes fadeIn {
                from { opacity: 0; transform: translateY(10px); }
                to { opacity: 1; transform: translateY(0); }
            }

            .fade-in {
                animation: fadeIn 0.5s ease-out 0.2s both;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div id="icon" class="icon">
                <div class="spinner"></div>
            </div>
            <h2 id="title" class="fade-in">Authenticating</h2>
            <p id="message" class="fade-in">Please wait a moment</p>
        </div>

        <script>
            console.log('Calendar OAuth callback page loaded');
            console.log('Current URL:', window.location.href);
            console.log('Query params:', {
                code: '${code ? code.substring(0, 10) + "..." : "none"}',
                error: '${error || "none"}',
                state: '${state || "none"}'
            });

            function showSuccess() {
                document.getElementById('icon').innerHTML =
                    '<svg class="checkmark" viewBox="0 0 52 52">' +
                        '<circle class="checkmark-circle" cx="26" cy="26" r="25" fill="none"/>' +
                        '<path class="checkmark-check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>' +
                    '</svg>';
                document.getElementById('title').textContent = 'Success';
                document.getElementById('message').textContent = 'Authentication complete';
            }

            function showError(errorMsg) {
                document.getElementById('icon').innerHTML =
                    '<svg class="error-icon" viewBox="0 0 52 52">' +
                        '<circle class="error-circle" cx="26" cy="26" r="25" fill="none"/>' +
                        '<path class="error-x" fill="none" d="M16 16 L36 36 M36 16 L16 36"/>' +
                    '</svg>';
                document.getElementById('title').textContent = 'Authentication failed';
                document.getElementById('message').textContent = errorMsg || 'Please try again';
                document.getElementById('message').classList.add('error-text');
            }

            function sendMessageAndClose(data, delay = 1000) {
                if (window.opener && !window.opener.closed) {
                    console.log('Sending message to parent window:', data);
                    window.opener.postMessage(data, '*');

                    setTimeout(() => {
                        console.log('Closing popup window');
                        window.close();
                    }, delay);
                } else {
                    console.log('No parent window found, closing directly');
                    setTimeout(() => window.close(), delay);
                }
            }

            // Process the OAuth response
            setTimeout(() => {
                const hasError = ${error ? "true" : "false"};
                const hasCode = ${code ? "true" : "false"};
                
                if (hasError) {
                    console.log('OAuth error detected:', '${error}');
                    showError('${errorDescription || error || "Authentication was cancelled"}');

                    sendMessageAndClose({
                        type: '${state === "google" ? "google_oauth_callback" : "microsoft_oauth_callback"}',
                        error: '${error}',
                        error_description: '${errorDescription || ""}',
                        state: '${state || ""}'
                    }, 2000);
                } else if (hasCode) {
                    console.log('OAuth code received successfully');
                    showSuccess();

                    sendMessageAndClose({
                        type: '${state === "google" ? "google_oauth_callback" : "microsoft_oauth_callback"}',
                        code: '${code}',
                        state: '${state || ""}'
                    }, 1000);
                } else {
                    console.log('No code or error found in URL');
                    showError('No authorization received');

                    sendMessageAndClose({
                        type: '${state === "google" ? "google_oauth_callback" : "microsoft_oauth_callback"}',
                        error: 'no_code'
                    }, 2000);
                }
            }, 300); // Small delay for smooth animation
        </script>
    </body>
    </html>`;

    // Set appropriate headers
    res.set("Content-Type", "text/html");
    res.set("Cache-Control", "no-cache, no-store, must-revalidate");
    res.set("Pragma", "no-cache");
    res.set("Expires", "0");
    
    // Send the HTML response
    res.status(200).send(html);

    console.log("Sent OAuth callback HTML response with PostMessage integration");
  }
);

export const calendarwebhookms = functions.https.onRequest(
  // Check Function: Used for calendar webhook so appcheck need disabled.
  { enforceAppCheck: false, memory: "512MiB", timeoutSeconds: 540 },
  async (req, _res) => {
    if (req.query["validationToken"] != null) {
      console.log("Received ms calendarwebhook : " + JSON.stringify(req.query));
      _res.status(200).send(req.query["validationToken"]);
    } else {
      console.log("Received ms calendarwebhook : " + JSON.stringify(req.body));
      _res.status(200).send();
      const list = req.body.value as ChangeNotification[];
      for (let i = 0; i < list.length; i++) {
        const data = list[i].resourceData as { id: string };
        if (req.query["cgId"] != null) {
          const calGroupId = req.query["cgId"];
          const eventId = data.id;
          const uid =
            list[i].clientState?.match("(?<=uid=).+.+?(?=\\?type)")?.[0] ?? "";
          const dbVersion = await getUserDbVersion(uid);
          const calId = list[i].clientState?.match("(?<=calId=).+")?.[0] ?? "";
          if (dbVersion > 0) {
            if (dbVersion >= 121) {
              console.log(
                `Publishing mscalendar.event.v${dbVersion} for user : ${uid} and ms calId: ${calId}`
              );
              await publishMessageToPubSub(`mscalendar.event.v${dbVersion}`, {
                chanId: calGroupId,
                token: list[i].clientState,
                resourceId: list[i].subscriptionId,
                eventId: eventId,
              });
              console.log(
                `Published mscalendar.event.v${dbVersion} for user : ${uid} and ms calId: ${calId}`
              );
            } else {
              console.log(
                `Publishing calendar.event.v${dbVersion} for user : ${uid} and ms calId: ${calId}`
              );
              await publishMessageToPubSub(`calendar.event.v${dbVersion}`, {
                chanId: calGroupId,
                token: list[i].clientState,
                resourceId: undefined,
                eventId: eventId,
              });
              console.log(
                `Published calendar.event.v${dbVersion} for user : ${uid} and ms calId: ${calId}`
              );
            }
          }
        }
      }
    }
  }
);
