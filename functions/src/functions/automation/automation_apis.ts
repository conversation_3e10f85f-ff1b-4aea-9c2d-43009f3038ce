import * as functions from "firebase-functions/v2";
import * as admin from "firebase-admin";
import { Timestamp } from "firebase-admin/firestore";
import { IThemeColor, IAppTheme } from "../migration/versions/base/base_schema";
import {
  IUser,
  getMeParsedData,
  IUserMetadata,
} from "../migration/versions/base/model_mappings";

exports.initUserData = functions.https.onRequest(
  // Check Function: Used for automation testing by lambda
  { enforceAppCheck: false },
  async (req, res) => {
    const email = req.query.account as string | undefined;
    if (email == undefined) {
      res.json({ result: "invalid email" });
      return;
    }
    let finalEmail;
    if (email.endsWith("@mevolve.app")) {
      finalEmail = email;
    } else {
      finalEmail = `${email}@mevolve.app`;
    }

    const themeColor = req.query.themeColor as IThemeColor;

    const appTheme = req.query.appTheme as IAppTheme;

    const userRecord = await admin.auth().getUserByEmail(finalEmail);

    const userSnapshot = await admin
      .firestore()
      .collection("users")
      .doc(userRecord.uid)
      .get();

    if (userSnapshot.exists == false) {
      res.json({ result: "no record found" });
      return;
    }
    const user = userSnapshot.data() as IUser;
    if (themeColor != undefined) {
      console.log(`updating theme color to: ${themeColor}`);
      // user.appSettings.themeColor = themeColor;
    }
    if (appTheme != undefined) {
      console.log(`updating app theme to: ${appTheme}`);
      // user.appSettings.appTheme = appTheme;
    }
    await admin.firestore().collection("users").doc(user.uid).update(user);
    res.json({ result: `reseted user: ${finalEmail}` });
  }
);

exports.deleteData = functions.https.onRequest(
  // Check Function: Used for automation testing by lambda
  { enforceAppCheck: false },
  async (req, res) => {
    // Grab the text parameter.
    const email = req.query.account as string | undefined;
    if (email == undefined) {
      res.json({ result: "invalid email" });
      return;
    }
    let finalEmail;
    if (email.endsWith("@mevolve.app")) {
      finalEmail = email;
    } else {
      finalEmail = `${email}@mevolve.app`;
    }
    console.log(finalEmail);
    // Push the new message into Firestore using the Firebase Admin SDK.
    const userRecord = await admin.auth().getUserByEmail(finalEmail);
    if (userRecord) {
      const uid = userRecord.uid;
      console.log(finalEmail);
      console.log(uid);
      const tasks: Promise<void>[] = [];
      tasks.push(resetUserData(uid));
      tasks.push(deleteData(uid, "todos"));
      tasks.push(deleteData(uid, "habitSetups"));
      tasks.push(deleteData(uid, "habitActions"));
      tasks.push(deleteData(uid, "journalSetups"));
      tasks.push(deleteData(uid, "journalActions"));
      tasks.push(deleteData(uid, "notes"));
      tasks.push(deleteData(uid, "lists"));
      await Promise.all(tasks);
    } else {
      console.log("no user found");
    }
    res.json({ result: `deleted data for user: ${finalEmail}` });
  }
);

async function resetUserData(uid: string) {
  const userSnapshot = await admin
    .firestore()
    .collection("users")
    .where("uid", "==", uid)
    .get();

  const user = userSnapshot.docs[0].data() as IUser;
  const tempUser = {
    id: user.uid,
    docVer: user.docVer,
    docCollection: user.docCollection,
    uid: user.uid,
    localUpdatedAt: user.localUpdatedAt,
    cloudUpdatedAt: user.cloudUpdatedAt,
    encData: user.encData,
    sessionId: user.sessionId,
    source: user.source,
    createdAt: user.createdAt,
    userInfo: {
      name: user.userInfo.name,
      email: user.userInfo.email,
      createdAt: user.userInfo.createdAt,
    },
    subscriptionInfo: {
      subscriptionStartDate: user.subscriptionInfo.subscriptionStartDate,
      subscriptionExpDate: user.subscriptionInfo.subscriptionExpDate,
      subscriptionState: user.subscriptionInfo.subscriptionState,
    },
  };
  const newUser = getMeParsedData(tempUser) as IUser;
  try {
    await admin.firestore().collection("users").doc(user.uid).update(newUser);
  } catch (error) {
    console.log("error updating user data: " + error + " for user: " + uid);
  }
  console.log("updated user metadata");
  const userMetadata = user as unknown as IUserMetadata;
  const tempUserMetadata = {
    id: userMetadata.uid,
    docVer: userMetadata.docVer,
    docCollection: userMetadata.docCollection,
    uid: userMetadata.uid,
    encData: user.encData,
    localUpdatedAt: userMetadata.localUpdatedAt,
    cloudUpdatedAt: userMetadata.cloudUpdatedAt,
    sessionId: userMetadata.sessionId,
    source: userMetadata.source,
    createdAt: userMetadata.createdAt,
    userInfo: {
      name: user.userInfo.name,
      email: user.userInfo.email,
      createdAt: user.userInfo.createdAt,
    },
    subscriptionInfo: {
      subscriptionStartDate: user.subscriptionInfo.subscriptionStartDate,
      subscriptionExpDate: user.subscriptionInfo.subscriptionExpDate,
      subscriptionState: user.subscriptionInfo.subscriptionState,
    },
  };
  const newDefaultUserMetadata = getMeParsedData(
    tempUserMetadata
  ) as IUserMetadata;
  try {
    await admin
      .firestore()
      .collection("usersMetadata")
      .doc(user.uid)
      .update(newDefaultUserMetadata);
  } catch (error) {
    console.log("error updating user metadata: " + error + " for user: " + uid);
  }
}

async function deleteData(uid: string, collection: string) {
  const fs = admin.firestore();
  const query = fs.collection(collection).where("uid", "==", uid).limit(500);
  let dataSnapshot = await query.get();
  console.log("received data = " + dataSnapshot.docs.length);
  while (dataSnapshot.docs.length > 0) {
    const batch = fs.batch();
    dataSnapshot.forEach((doc) => {
      if (collection == "notes") {
        const createdAt = (doc.data()?.createdAt as Timestamp).toDate();
        const today = new Date();
        if (
          createdAt.getDate() == today.getDate() &&
          createdAt.getMonth() == today.getMonth() &&
          createdAt.getFullYear() == today.getFullYear()
        ) {
          batch.delete(doc.ref);
        }
      } else {
        batch.delete(doc.ref);
      }
    });
    await batch.commit();
    const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
    dataSnapshot = await query.startAfter(last).get();
    console.log("received data = " + dataSnapshot.docs.length);
  }
}
