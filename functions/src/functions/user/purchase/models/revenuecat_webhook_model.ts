/**
 * RevenueCat webhook event types
 * 
 * MAINTAINABILITY STRATEGY: Single Source of Truth
 * ===============================================
 * 
 * All RevenueCat webhook event type references go through this enum to:
 * - Enable easy tracing of where specific events are handled
 * - Allow IDE "Find All References" to locate all event usage
 * - Prevent typos in event type strings
 * - Make refactoring safer and more predictable
 * - Provide single place to update event names if RevenueCat changes them
 */
export const RevenueCatEventType = {
  TEST: "TEST",
  INITIAL_PURCHASE: "INITIAL_PURCHASE",
  PRODUCT_CHANGE: "PRODUCT_CHANGE",
  CANCELLATION: "CANCELLATION",
  EXPIRATION: "EXPIRATION",
  RENEWAL: "RENEWAL",
  NON_RENEWING_PURCHASE: "NON_RENEWING_PURCHASE",
  UNCANCELLATION: "UNCANCELLATION",
  BILLING_ISSUE: "BILLING_ISSUE",
  TRANSFER: "TRANSFER",
  SUBSCRIPTION_PAUSED: "SUBSCRIPTION_PAUSED",
  SUBSCRIBER_ALIAS: "SUBSCRIBER_ALIAS",
  SUBSCRIPTION_EXTENDED: "SUBSCRIPTION_EXTENDED",
  TEMPORARY_ENTITLEMENT_GRANT: "TEMPORARY_ENTITLEMENT_GRANT",
  REFUND_REVERSED: "REFUND_REVERSED",
  INVOICE_ISSUANCE: "INVOICE_ISSUANCE",
  REFUND: "REFUND",
} as const;

export type RevenueCatEventType = typeof RevenueCatEventType[keyof typeof RevenueCatEventType];

export type RevenuecatWebhookEvent = {
    api_version: string,
    app_id: string,
    app_user_id: string,
    country_code: string,
    currency: string,
    entitlement_ids: string[],
    event_timestamp_ms: number,
    environment: string,
    aliases: string[],
    id: string,
    new_product_id?: string,
    offer_code: string,
    original_app_user_id: string,
    original_transaction_id: string,
    period_type: string,
    presented_offering_id: string,
    price: number,
    price_in_purchased_currency: number,
    commission_percentage?: number,
    tax_percentage?: number,
    product_id: string,
    purchased_at_ms: number,
    renewal_number?: number,
    store: string,
    subscriber_attributes?: object,
    transaction_id: string,
    expiration_at_ms: number,
    type: RevenueCatEventType,
    // TRANSFER event specific fields
    transferred_from?: string[],
    transferred_to?: string[],
}
