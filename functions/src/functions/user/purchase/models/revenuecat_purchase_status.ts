import { z } from "zod";
import { dateSchema } from "../../../migration/versions/base/base_schema";

export type RcUserPurchaseStatus = {
  original_app_user_id: string;
  original_application_version: string;
  original_purchase_date: Date;
  entitlements: Entitlement;
  subscriptions: Subscriptions;
};

export type Entitlement = {
  basic: EntitlementDetail;
  pro: EntitlementDetail;
  plus: EntitlementDetail;
};

export type EntitlementDetail = {
  expires_date: Date;
  grace_period_expires_date: Date;
  product_identifier: string;
  product_plan_identifier: string;
  purchase_date: Date;
};

export type Subscriptions = {
  [key: string]: ISubscriptionDetails;
};

export const ZSubscriptionDetails = z.object({
  auto_resume_date: dateSchema.nullable(),
  billing_issues_detected_at: dateSchema.nullable(),
  expires_date: dateSchema.nullable(),
  grace_period_expires_date: dateSchema.nullable(),
  is_sandbox: z.boolean().default(false),
  original_purchase_date: dateSchema,
  period_type: z.string(),
  product_plan_identifier: z.string(),
  purchase_date: dateSchema,
  refunded_at: dateSchema.nullable(),
  store: z.string(),
  store_transaction_id: z.string(),
  unsubscribe_detected_at: dateSchema.nullable(),
});

export type ISubscriptionDetails = z.infer<typeof ZSubscriptionDetails>;
