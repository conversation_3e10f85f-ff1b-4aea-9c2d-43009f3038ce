import { RevenuecatWebhookEvent } from "./models/revenuecat_webhook_model";
import {
  StripeSubscriptionUpdateEventType,
} from "./stripe";
import * as admin from "firebase-admin";
import { MePaymentEvent } from "./models/me_payment_event_model";
import {
  ISubscriptionInfo,
} from "../../migration/versions/base/model_mappings";
import { Timestamp } from "firebase-admin/firestore";

// ============================================================================
// SHARED TRANSACTION LOGGING
// ============================================================================

/**
 * Saves transaction details to Firestore for audit trail
 * Supports both RevenueCat and Stripe events
 * Used by both stripe_webhook.ts and revenuecat_web_hook.ts
 * @param {string} uid - User ID for the transaction
 * @param {ISubscriptionInfo} subscriptionInfo - Subscription information to save
 * @param {RevenuecatWebhookEvent} revenuecatEvent - Optional RevenueCat webhook event data
 * @param {StripeSubscriptionUpdateEventType} stripeEvent - Optional Stripe webhook event data
 */
export async function saveMeTransactionDetails(
  uid: string,
  subscriptionInfo: ISubscriptionInfo,
  revenuecatEvent?: RevenuecatWebhookEvent,
  stripeEvent?: StripeSubscriptionUpdateEventType
) {
  try {
    if (revenuecatEvent) {
      const mePaymentEvetRecipt: MePaymentEvent = {
        uid: uid,
        orderid: revenuecatEvent.original_transaction_id,
        paymentMethod: subscriptionInfo.storeType,
        planType: subscriptionInfo.subscriptionType,
        transactionTime: Timestamp.fromMillis(revenuecatEvent.purchased_at_ms),
        planStartDate: Timestamp.fromDate(
          new Date(subscriptionInfo.subscriptionStartDate!)
        ),
        planEndDate: Timestamp.fromDate(
          new Date(subscriptionInfo.subscriptionExpDate!)
        ),
        subscriptionStatus: subscriptionInfo.subscriptionState,
        revenuecatEvent: revenuecatEvent,
      };
      const db = admin.firestore();
      await db.collection("paymentTransactions").add(mePaymentEvetRecipt);
    } else {
      const mePaymentEvetRecipt: MePaymentEvent = {
        uid: uid,
        orderid: stripeEvent?.id ?? "",
        paymentMethod: subscriptionInfo.storeType,
        planType: subscriptionInfo.subscriptionType,
        transactionTime: Timestamp.fromDate(new Date()),
        planStartDate: Timestamp.fromDate(
          new Date(subscriptionInfo.subscriptionStartDate!)
        ),
        planEndDate: Timestamp.fromDate(
          new Date(subscriptionInfo.subscriptionExpDate!)
        ),
        subscriptionStatus: subscriptionInfo.subscriptionState,
        stripeEvent: stripeEvent,
      };
      const db = admin.firestore();
      await db.collection("paymentTransactions").add(mePaymentEvetRecipt);
    }
  } catch (error) {
    console.log("error in saving transaction details : " + error);
  }
}
