/**
 * STRIPE WEBHOOK PROCESSOR
 * 
 * Complete Stripe integration for web-based subscription management.
 * 
 * STRIPE SUBSCRIPTION LOGIC:
 * =========================
 * 
 * This file handles all Stripe-related functionality including:
 * 
 * 1. CHECKOUT FLOW:
 *    - Creates Stripe checkout sessions for new subscriptions
 *    - Handles existing customer detection and portal redirection
 *    - Manages customer creation and subscription setup
 * 
 * 2. SUBSCRIPTION MANAGEMENT:
 *    - Processes Stripe webhook events via Pub/Sub
 *    - Updates user subscription status and billing information
 *    - Handles subscription updates, cancellations, and renewals
 * 
 * 3. CUSTOMER PORTAL:
 *    - Generates customer portal URLs for subscription management
 *    - Allows users to update payment methods and cancel subscriptions
 * 
 * 4. EMAIL NOTIFICATIONS:
 *    - Sends subscription confirmation emails
 *    - Handles cancellation and expiration notifications
 *    - Supports multi-language email templates
 * 
 * 5. PRODUCT MANAGEMENT:
 *    - Environment-based plan configuration (prod vs test)
 *    - Support for current and legacy plan structures
 *    - Three subscription tiers: Basic, Pro, Plus (monthly/yearly)
 * 
 * 6. TRANSACTION LOGGING:
 *    - Records all subscription transactions for audit trail
 *    - Supports both RevenueCat and Stripe event logging
 *    - Maintains transaction history in Firestore
 * 
 * This consolidates all Stripe functionality into a single file for easier maintenance
 * and matches the organization pattern used by RevenueCat webhook processing.
 */

import { Stripe } from "stripe";
import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v2";
import { getMeEnvironment } from "../../../me_config";
import {
  currentDbVersion,
  IUser,
  getMeParsedData,
} from "../../migration/versions/base/model_mappings";
import { INVALID_ARGUMENT } from "../../../utils/constants";
import { onMessagePublished } from "firebase-functions/v2/pubsub";
import { getEnvironmentProducts, getStripeApiKeyEnvVar, getStripeWebhookSecretEnvVar, StripePackageProduct } from "../../../stripe_config";

// ============================================================================
// STRIPE CLIENT HELPER
// ============================================================================

/**
 * Get configured Stripe client for current environment
 * @return {Stripe} Configured Stripe client instance
 */
export function getStripeClient(): Stripe {
  const apiKey = process.env[getStripeApiKeyEnvVar(getMeEnvironment())] as string;
  return new Stripe(apiKey);
}
import {
  capitalizeFirstLetter,
  convertDatesToTimestamps,
  getAppLink,
  getEmailNameByIdFromAuthService,
  getMeApiBaseDomain,
  getMeBaseDomain,
  getSubscriptionPageLink,
  getSupportLink,
  getViewSettingsByUserId,
} from "../../../utils/utility_methods";
import { DbRepository } from "../../save_data/db.repository";
import { sendSingleEmailViaSes } from "../../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../../utils/emails/email_helper";

// ============================================================================
// TYPE DEFINITIONS
// ============================================================================

export type StripeCustomer = {
  id: string;
  email: string;
};

export type StripeEvent = {
  id: string;
  object: string;
  type: string;
};

export type StripeSubscriptionUpdateEventType = {
  id: string;
  data: {
    object: {
      customer: string;
    };
  };
};



// ============================================================================
// PRODUCT MANAGEMENT (Environment-Based Configuration)
// ============================================================================
 
/**
 * Get current subscription plans based on environment
 * Uses centralized stripe_config.ts for all environment-specific product IDs
 * @param {string} countryCode - Country code for plan filtering (currently unused but kept for API consistency)
 * @param {boolean} useTestMode - Force test mode even in production (for testing)
 * @return {object} Object containing array of Stripe subscription packages
 */
export function getAllPlans(countryCode: string, useTestMode = false): { stripePackageList: StripePackageProduct[] } {
  console.log(`Getting plans for country: ${countryCode}, testMode: ${useTestMode}`);
  const env = getMeEnvironment();
  
  const products = getEnvironmentProducts(env, useTestMode);
  return {
    stripePackageList: products
  };
}


// ============================================================================
// HTTP API FUNCTIONS (Firebase Functions)
// ============================================================================

/**
 * Returns all available subscription plans for the given country
 */
export const getAllSubscriptionPlans = functions.https.onCall(
  async (request) => {
    const countryCode = request.data.countryCode;
    return getAllPlans(countryCode);
  }
);

/**
 * Creates a Stripe payment link for checkout
 */
export const getStripePaymentLink = functions.https.onCall(async (request) => {
  const priceId = request.data.priceId;
  const platform = request.data.platform;
  const email = request.data.email;
  const uid = request.data.uid;
  if (!priceId || !platform || !email || !uid) {
    throw new functions.https.HttpsError(
      INVALID_ARGUMENT,
      "Required parameters missing."
    );
  }
  const session = await stripeCheckoutHandler(priceId, platform, email, uid);
  return {
    id: session.id,
    payment_url: session.url,
  };
});

/**
 * Generates a Stripe customer portal link for subscription management
 */
export const getStripeCustomerPortalLink = functions.https.onCall(
  async (request) => {
    const session = await getCustomerPortalUrl(request.data.email);
    return { url: session.url };
  }
);

/**
 * Updates a user's existing Stripe subscription
 */
export const updateUserStripeSubscription = functions.https.onCall(
  async (request) => {
    try {
      await updateSubscription(request.data.email, request.data.priceId);
    } catch (error) {
      console.log(error);
      throw new functions.https.HttpsError(
        "internal",
        "Error updating subscription"
      );
    }
    return { result: "success" };
  }
);

// ============================================================================
// STRIPE CHECKOUT & CUSTOMER MANAGEMENT
// ============================================================================

/**
 * Creates Stripe checkout session or redirects to customer portal
 * Handles both new customers and existing customers with active subscriptions
 * @param {string} priceId - Stripe price ID for the subscription plan
 * @param {string} platform - Platform requesting the checkout (web, ios, android)
 * @param {string} email - Customer email address
 * @param {string} uid - User ID for metadata tracking
 * @return {Promise<any>} Stripe checkout session or customer portal session
 */
export async function stripeCheckoutHandler(
  priceId: string,
  platform: string,
  email: string,
  uid: string
) {
  const stripe = getStripeClient();

  // Updated URLs to point to stripePaymentResult function (following calendar auth pattern)
  const functionUrl = `https://${getMeApiBaseDomain()}/me-purchasehooks-stripePaymentResult`;
  const successUrl = `${functionUrl}?session_id={CHECKOUT_SESSION_ID}`;
  const cancelUrl = `${functionUrl}?cancelled=true`;
  let stripeCustomerId = "";
  const existingCustomers = await stripe.customers.list({ email: email });
  if (existingCustomers.data.length) {
    const stripeCustomer = existingCustomers.data[0];
    const activeSubscriptions = await getCustomerActiveSubscriptions(
      stripeCustomer.id
    );
    if (activeSubscriptions.length > 0) {
      return await getCustomerPortalUrl(email);
    }
    stripeCustomerId = stripeCustomer.id;
  } else {
    const customer = await stripe.customers.create({
      email: email,
    });
    stripeCustomerId = customer.id;
  }

  const session = await stripe.checkout.sessions
    .create({
      mode: "subscription",
      line_items: [
        {
          price: priceId,
          quantity: 1,
        },
      ],
      success_url: successUrl,
      cancel_url: cancelUrl,
      customer: stripeCustomerId,
      metadata: {
        uid: uid,
      },
    })
    .catch((error) => {
      console.log(error);
      throw error;
    });
  return session;
}

/**
 * Generates Stripe customer portal URL for existing customers
 * Allows customers to manage their subscriptions, payment methods, etc.
 * @param {string} email - Customer email address to lookup in Stripe
 * @return {Promise<any>} Stripe billing portal session
 */
export async function getCustomerPortalUrl(email: string) {
  const stripe = getStripeClient();
  const existingCustomers = await stripe.customers.list({ email: email });
  if (existingCustomers.data.length) {
    const stripeCustomerId = existingCustomers.data[0].id;
    const session = await stripe.billingPortal.sessions.create({
      customer: stripeCustomerId,
      return_url: `https://${getMeBaseDomain()}/drawerSubscription`,
    });
    console.log("customer portal url = " + session.url);
    return session;
  } else {
    console.log("no customer found with given email id");
    throw new functions.https.HttpsError(
      "invalid-argument",
      "no customer found with given email id"
    );
  }
}

/**
 * Updates an existing Stripe subscription to a new price/plan
 * @param {string} email - Customer email address to lookup subscription
 * @param {string} priceId - New Stripe price ID to update subscription to
 * @return {Promise<void>} Promise that resolves when subscription is updated
 */
export async function updateSubscription(email: string, priceId: string) {
  const stripe = getStripeClient();
  const existingCustomers = await stripe.customers.list({ email: email });
  if (existingCustomers.data.length) {
    const stripeCustomer = existingCustomers.data[0];
    const activeSubscriptions = await getCustomerActiveSubscriptions(
      stripeCustomer.id
    );
    if (activeSubscriptions.length > 0) {
      const subscription = activeSubscriptions[0];
      await stripe.subscriptions.update(subscription.id, {
        items: [
          {
            id: subscription.items.data[0].id,
            price: priceId,
          },
        ],
      });
    }
  }
}

/**
 * Gets active subscriptions for a Stripe customer
 * Used to determine if customer should go to checkout or customer portal
 * @param {string} id - Stripe customer ID
 * @return {Promise<any[]>} Array of active Stripe subscriptions
 */
async function getCustomerActiveSubscriptions(id: string) {
  const stripe = getStripeClient();
  const subscriptions = await stripe.subscriptions.list({ customer: id });
  return subscriptions.data.filter((subscription) => {
    return subscription.status === "active";
  });
}

// ============================================================================
// STRIPE WEBHOOK PROCESSING
// ============================================================================

/**
 * Processes Stripe webhook events and publishes to Pub/Sub
 * @param {StripeEvent} event - Raw Stripe webhook event
 */
export async function stripeWebHookHandler(event: StripeEvent) {
  console.log("stripe event received = " + event.type);
  switch (event.type) {
    case "customer.subscription.deleted":
    case "customer.subscription.updated": {
      console.log("updating customer detail - customer.subscription.updated");
      const stripeEvent = event as unknown as StripeSubscriptionUpdateEventType;
      const stripe = getStripeClient();
      const stripeCustomer: StripeCustomer = (await stripe.customers.retrieve(
        stripeEvent.data.object.customer
      )) as unknown as StripeCustomer;
      const userRecord = await admin.auth().getUserByEmail(stripeCustomer.email);

      const { getUserDbVersion, publishMessageToPubSub } = await import("../../../utils/utility_methods");
      const dbVersion = await getUserDbVersion(userRecord.uid);
      if (dbVersion > 0) {
        return publishMessageToPubSub(`purchase_stripe_v${dbVersion}`, {
          uid: userRecord.uid,
          event: stripeEvent,
        });
      }
      break;
    }
    default: {
      break;
    }
  }
  return;
}

/**
 * Stripe webhook event processor (via Pub/Sub)
 * Handles subscription updates and triggers email notifications with signature validation
 */
exports.stripeWebhookEventPubSub = onMessagePublished(
  {
    topic: `purchase_stripe_v${currentDbVersion}`,
  },
  async (event) => {
    console.log("handling stripe webhook", event);
    const data = event.data.message.json;
    
    // Validate webhook signature if raw event data is provided
    if (data.rawBody && data.signature) {
      const stripe = getStripeClient();
      const webhookSecret = process.env[getStripeWebhookSecretEnvVar(getMeEnvironment())] as string;
      
      if (!webhookSecret) {
        console.log("Webhook secret not configured");
        return;
      }

      try {
        // Verify webhook signature
        const verifiedEvent = stripe.webhooks.constructEvent(
          Buffer.from(data.rawBody, "base64"),
          data.signature,
          webhookSecret
        );
        console.log(`Verified Stripe webhook event: ${verifiedEvent.type}`);
      } catch (err) {
        console.log(`Webhook signature verification failed: ${err}`);
        return;
      }
    }

    const uid = data.uid;
    const stripeEvent = data.event as StripeSubscriptionUpdateEventType;
    const userData = await updateUserDetailsFromStripeEvent(stripeEvent, uid);
    if (userData) {
      await sendMailForStripe(userData, stripeEvent);
    }
  }
);

/**
 * Updates user subscription details from Stripe webhook events
 * Processes subscription status changes and updates user data accordingly
 * @param {StripeSubscriptionUpdateEventType} event - Stripe webhook event data
 * @param {string} uid - User ID to update subscription details for
 * @return {Promise<IUser | undefined>} Updated user data or undefined on error
 */
export async function updateUserDetailsFromStripeEvent(
  event: StripeSubscriptionUpdateEventType,
  uid: string
): Promise<IUser | undefined> {
  try {
    const db = admin.firestore();
    const userDataSnapshot = await db.collection("users").doc(uid).get();
    if (userDataSnapshot.exists) {
      const userData = userDataSnapshot.data() as IUser;
      await handleSubscriptionStatusEvent(userData, event);
      return userData;
    } else {
      console.log("no user found for email: " + uid);
    }
  } catch (error) {
    console.log("error in updating user purchase details: " + error);
  }
  return undefined;
}

/**
 * Handles subscription status changes from Stripe events
 * Updates user subscription info and saves to database
 * @param {IUser} userData - User data object to update
 * @param {StripeSubscriptionUpdateEventType} event - Stripe webhook event data
 * @return {Promise<void>} Promise that resolves when user data is updated
 */
async function handleSubscriptionStatusEvent(
  userData: IUser,
  event: StripeSubscriptionUpdateEventType
) {
  console.log(
    "received subscription start for user from stripe: " + userData.uid
  );
  const activeSubscriptions = await getCustomerActiveSubscriptions(
    event.data.object.customer
  );
  if (activeSubscriptions.length > 0) {
    userData.subscriptionInfo.subscriptionState = "subscribed";
    if (activeSubscriptions[0].items.data[0].plan.interval.includes("month")) {
      userData.subscriptionInfo.subscriptionType = "monthly";
    } else {
      userData.subscriptionInfo.subscriptionType = "yearly";
    }
    userData.subscriptionInfo.subscriptionStartDate = new Date(
      activeSubscriptions[0].current_period_start * 1000
    );
    userData.subscriptionInfo.subscriptionExpDate = new Date(
      activeSubscriptions[0].current_period_end * 1000
    );
    userData.subscriptionInfo.storeType = "stripe";
    userData.subscriptionInfo.productId =
      activeSubscriptions[0].items.data[0].price.id;
    userData.subscriptionInfo.entitlement = getUserEntitlement(
      activeSubscriptions[0].items.data[0].price.id
    );
  } else {
    userData.subscriptionInfo.subscriptionState = "subscriptionExpired";
    userData.subscriptionInfo.entitlement = "free";
  }
  const { saveMeTransactionDetails } = await import("./transaction_logging");
  await saveMeTransactionDetails(
    userData.uid,
    userData.subscriptionInfo,
    undefined,
    event
  );
  userData.cloudUpdatedAt = null;
  await new DbRepository().updateData(
    convertDatesToTimestamps(getMeParsedData(userData))
  );
}

// ============================================================================
// EMAIL NOTIFICATION SYSTEM
// ============================================================================

/**
 * Sends appropriate emails based on Stripe subscription events
 * Handles subscription started, expired, and cancelled notifications
 * @param {IUser} userData - User data containing subscription information
 * @param {StripeSubscriptionUpdateEventType} event - Stripe webhook event data
 * @return {Promise<void>} Promise that resolves when email is sent
 */
export async function sendMailForStripe(
  userData: IUser,
  event: StripeSubscriptionUpdateEventType
) {
  const userDetail = await getEmailNameByIdFromAuthService(userData.uid);
  const stripePlan = getPlanFromProductId(
    userData.subscriptionInfo.productId ?? ""
  );
  if (userDetail?.email != undefined && stripePlan != undefined) {
    if (userData.subscriptionInfo.subscriptionState == "subscribed") {
      const viewSettings = await getViewSettingsByUserId(userData.uid);
      const language = viewSettings?.appSettings.language;
      await sendSingleEmailViaSes({
        templateName: MevolveEmails.upgradeSuccessful,
        recipient: userDetail.email,
        templateData: {
          name: userDetail.name ?? "",
          subsType: stripePlan?.entitlement,
          priceString: `${stripePlan?.packagePrice}`,
          appLink: getAppLink(),
          linkToSupportChat: getSupportLink(),
        },
        language: language,
      });
    } else if (userData.subscriptionInfo.unsubscribedAt != null) {
      const userDetail = await getEmailNameByIdFromAuthService(userData.uid);
      if (userDetail?.email != undefined) {
        const viewSettings = await getViewSettingsByUserId(userData.uid);
        const language = viewSettings?.appSettings.language;
        await sendSingleEmailViaSes({
          templateName: MevolveEmails.subscriptionExpired,
          recipient: userDetail.email,
          templateData: {
            name: userDetail.name ?? "",
            subscriptionLink: getSubscriptionPageLink(),
            linkToSupportChat: getSupportLink(),
          },
          language: language,
        });
      }
      // makeUserListsPrivateAndNotify(userData.uid);
    } else {
      const viewSettings = await getViewSettingsByUserId(userData.uid);
      const language = viewSettings?.appSettings.language;
      const plan = getPlanFromProductId(event.data.object.customer);
      const planString =
        plan != null
          ? capitalizeFirstLetter(plan!.entitlement) +
            " " +
            capitalizeFirstLetter(plan!.packageDuration)
          : "";
      await sendSingleEmailViaSes({
        templateName: MevolveEmails.userCancelsSubscriptionFromPremiumPlans,
        recipient: userDetail.email,
        templateData: {
          name: userDetail.name ?? "",
          plan: planString,
          subscriptionLink: getSubscriptionPageLink(),
          linkToSupportChat: getSupportLink(),
        },
        language: language,
      });
      // makeUserListsPrivateAndNotify(userData.uid);
    }
  }
}


// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Determines user entitlement level based on Stripe product/price ID
 * Uses centralized config to lookup entitlement across all environments
 * @param {string} id - Stripe price/product ID to lookup entitlement for
 * @return {"free" | "basic" | "pro" | "plus"} User entitlement level
 */
export function getUserEntitlement(id: string): "free" | "basic" | "pro" | "plus" {
  const plans = getAllPlans("US");
  const plan = plans.stripePackageList.find((plan) => plan.packageId === id);
  return plan?.entitlement || "free";
}

/**
 * Gets plan details from product ID for email notifications
 * Used to provide subscription details in email templates
 * @param {string} productId - Stripe product/price ID to lookup plan details for
 * @return {StripePackage | undefined} Plan details or undefined if not found
 */
function getPlanFromProductId(productId: string) {
  const plans = getAllPlans("US");
  const plan = plans.stripePackageList.find(
    (plan) => plan.packageId === productId
  );
  return plan;
}
