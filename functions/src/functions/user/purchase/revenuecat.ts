/**
 * R<PERSON>VENUECAT WEBHOOK PROCESSOR
 *
 * Handles cross-platform subscription management with smart platform prioritization.
 *
 * CROSS-PLATFORM SUBSCRIPTION LOGIC:
 * ==================================
 *
 * This webhook now mirrors CLIENT RESTORE PURCHASE logic exactly:
 *
 * 1. PLATFORM PRIORITIZATION (All Events):
 *    - TRANSFER events: Prioritize transferred platform first (from event.store)
 *    - Other events: Prioritize user's previous platform (from previousSubscriptionInfo.storeType)
 *    - Fallback to other platform if preferred platform has no active subscriptions
 *    - This matches client behavior: current platform > fallback platform
 *
 * 2. WHY THIS WORKS FOR ALL SCENARIOS:
 *    - TRANSFER: User restored on new platform → prioritize that platform (user choice)
 *    - RENEWAL/CANCELLATION: User stays on same platform → prioritize previous platform
 *    - INITIAL_PURCHASE: User purchases on specific platform → that becomes the platform
 *    - CLIENT RESTORE: Same prioritization logic as webhooks now
 *
 * 3. BUSINESS LOGIC CONSISTENCY:
 *    - Client apps ONLY allow purchases/upgrades when:
 *      a) User has ACTIVE subscription on THAT platform, OR
 *      b) User has EXPIRED subscription (any platform) - cross-platform purchase allowed
 *    - Cross-platform transfers only happen via client restore (user-initiated)
 *    - Webhooks respect the platform user chose (transfer store or existing platform)
 *
 * 4. SEPARATION OF CONCERNS:
 *    - Webhooks: Always send email notifications (user should know about changes)
 *    - Database: Only update if subscription info actually changed (avoid client interference)
 *    - Client apps: Handle platform switching via restore purchase (user choice wins)
 *
 * 5. EMAIL NOTIFICATION STRATEGY (HYBRID APPROACH):
 *    EMAIL PRIORITY ORDER (Most Reliable to Fallback):
 *    1. CANCELLATION webhooks → Always send cancellation email (event data)
 *    2. STATE-BASED EMAILS → Most reliable for subscription changes (prioritized)
 *    3. PRODUCT_CHANGE OVERRIDE → Special case for deferred downgrades
 *    4. PRODUCT_CHANGE FALLBACK → When no state change detected
 *
 *    DEFERRED PRODUCT CHANGE SCENARIO (Pro → Basic Downgrade):
 *    When user downgrades Pro → Basic, RevenueCat fires PRODUCT_CHANGE webhook immediately
 *    but subscription state remains "Pro/subscribed" until current period expires.
 *
 *    PROBLEM: State change logic detects "cancellation" instead of "downgrade"
 *    SOLUTION: Product change override - when state = cancellation + PRODUCT_CHANGE event
 *             → Override with product change logic → sends correct downgrade email
 *
 *    CANCELLATION EMAIL INDEPENDENCE:
 *    CANCELLATION webhooks always trigger immediate emails using webhook event data:
 *    - Uses event.product_id for cancelled subscription details (not current userdata)
 *    - Sends email regardless of API state or userdata changes
 *    - Ensures users get immediate cancellation confirmation for ANY subscription
 *    - Works for multi-subscription scenarios (shows specific cancelled subscription)
 *    - Independent of RevenueCat API update timing
 *
 *    This ensures users get immediate and CORRECT feedback for all subscription changes,
 *    prioritizing reliable state changes while handling deferred product change edge cases.
 *
 * This ensures users get consistent, predictable behavior while maintaining platform flexibility.
 */

import { RcUserPurchaseStatus } from "./models/revenuecat_purchase_status";
import {
  RevenuecatWebhookEvent,
  RevenueCatEventType,
} from "./models/revenuecat_webhook_model";
import * as admin from "firebase-admin";
import { saveMeTransactionDetails } from "./transaction_logging";
import {
  getEmailNameByIdFromAuthService,
  getAppLink,
  getSupportLink,
  getSubscriptionPageLink,
  getViewSettingsByUserId,
  publishMessageToPubSub,
  getCurrentUTCDate,
} from "../../../utils/utility_methods";
import { DbRepository } from "../../save_data/db.repository";
import {
  IUser,
  ISubscriptionInfo,
  currentDbVersion,
  getMeParsedData,
} from "../../migration/versions/base/model_mappings";
import { sendSingleEmailViaSes } from "../../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../../utils/emails/email_helper";
import moment from "moment";

// =====================================================================================
// BUSINESS LOGIC ENUMS - ELIMINATE IMPORTANT STRING DUPLICATION
// =====================================================================================

/**
 * Subscription states for tracking user subscription status
 */
const SubscriptionState = {
  NONE: "none",
  SUBSCRIBED: "subscribed",
  EXPIRED: "subscriptionExpired",
} as const;

type SubscriptionState =
  (typeof SubscriptionState)[keyof typeof SubscriptionState];

/**
 * Entitlement levels for subscription tiers
 */
const Entitlement = {
  FREE: "free",
  BASIC: "basic",
  PRO: "pro",
  PLUS: "plus",
} as const;

type Entitlement = (typeof Entitlement)[keyof typeof Entitlement];

/**
 * Store types in internal format
 */
const StoreType = {
  APP_STORE: "app_store",
  PLAY_STORE: "play_store",
} as const;

type StoreType = (typeof StoreType)[keyof typeof StoreType];

/**
 * RevenueCat store formats (as received from RevenueCat API)
 */
const RevenueCatStore = {
  APP_STORE: "APP_STORE",
  PLAY_STORE: "PLAY_STORE",
} as const;

type RevenueCatStore = (typeof RevenueCatStore)[keyof typeof RevenueCatStore];

/**
 * Subscription billing periods
 */
const SubscriptionPeriod = {
  MONTHLY: "monthly",
  YEARLY: "yearly",
  CUSTOM: "custom",
} as const;

type SubscriptionPeriod =
  (typeof SubscriptionPeriod)[keyof typeof SubscriptionPeriod];

/**
 * Email action types for subscription notifications
 */
const EmailAction = {
  SUBSCRIPTION_STARTED: "subscription_started",
  SUBSCRIPTION_UPGRADED: "subscription_upgraded",
  SUBSCRIPTION_RENEWED: "subscription_renewed",
  SUBSCRIPTION_EXPIRED: "subscription_expired",
  SUBSCRIPTION_CANCELLED: "subscription_cancelled",
  SUBSCRIPTION_DOWNGRADED: "subscription_downgraded",
  SUBSCRIPTION_UPDATED: "subscription_updated",
} as const;

type EmailAction = (typeof EmailAction)[keyof typeof EmailAction];

/**
 * Product period keywords for parsing product IDs
 */
const PeriodKeywords = {
  MONTH: "month",
  YEAR: "year",
  ANNUAL: "annual",
} as const;

type PeriodKeywords = (typeof PeriodKeywords)[keyof typeof PeriodKeywords];

/**
 * Display labels for email templates
 */
const DisplayLabels = {
  MONTHLY: "Monthly",
  YEARLY: "Yearly",
} as const;

type DisplayLabels = (typeof DisplayLabels)[keyof typeof DisplayLabels];

// =====================================================================================
// MAIN WEBHOOK HANDLERS
// =====================================================================================

export async function handleRevenuecatWebHook(
  event: RevenuecatWebhookEvent
): Promise<string> {
  const SUCCESS_RESPONSE = "success";
  const eventType = event.type;
  const userId = event.app_user_id;

  // Early validation
  if (!eventType) {
    return SUCCESS_RESPONSE;
  }

  console.log(
    `RevenueCat webhook ${event.id} received for ${userId}: ${eventType}`
  );

  // Process events based on type - all return success unless there's an error
  switch (eventType) {
    // Test events - no processing needed
    case RevenueCatEventType.TEST:
      console.log("Test event received - no processing required");
      break;

    // Subscription state change events - require full processing
    case RevenueCatEventType.INITIAL_PURCHASE:
    case RevenueCatEventType.PRODUCT_CHANGE:
    case RevenueCatEventType.CANCELLATION:
    case RevenueCatEventType.EXPIRATION:
    case RevenueCatEventType.RENEWAL:
    case RevenueCatEventType.NON_RENEWING_PURCHASE:
    case RevenueCatEventType.UNCANCELLATION:
    case RevenueCatEventType.BILLING_ISSUE:
      await processEvent(userId, event);
      break;

    // Cross-account transfer events - special handling
    case RevenueCatEventType.TRANSFER:
      await processTransferEvent(event);
      break;

    // Informational events - logged only (no processing costs)
    case RevenueCatEventType.SUBSCRIPTION_PAUSED:
    case RevenueCatEventType.SUBSCRIBER_ALIAS:
    case RevenueCatEventType.SUBSCRIPTION_EXTENDED:
    case RevenueCatEventType.TEMPORARY_ENTITLEMENT_GRANT:
    case RevenueCatEventType.REFUND_REVERSED:
    case RevenueCatEventType.INVOICE_ISSUANCE:
    case RevenueCatEventType.REFUND:
      console.log(
        `Informational event received: ${eventType} - no processing required`
      );
      break;

    // Unknown events - log for monitoring
    default:
      console.log(`Unknown event type received: ${eventType}`);
      break;
  }

  return SUCCESS_RESPONSE;
}

/**
 * Central event processing function - handles all subscription events with unified logic
 *
 * PROCESSING FLOW: Mirror Client Restore Purchase
 * ==============================================
 *
 * This function processes all webhook events using the same logic as client restore purchase
 * to ensure consistent subscription state across all platforms and scenarios.
 *
 * STEPS EXPLAINED:
 * 1. Get current user state → Know what user had before
 * 2. Fetch fresh RevenueCat data → Get latest subscription status
 * 3. Apply platform prioritization → Choose correct subscription
 * 4. Update user object → Prepare new state
 * 5. Send emails always → User should know about changes
 * 6. Save to DB conditionally → Only if data actually changed
 * 7. Save transaction details → For audit trail
 *
 * ERROR HANDLING STRATEGY:
 * - Individual step failures are logged but don't stop processing
 * - Email failures don't prevent database updates
 * - Database failures don't prevent transaction logging
 * - Always return undefined on error (webhook should retry)
 *
 * @param {string} userId - User ID to process subscription for
 * @param {RevenuecatWebhookEvent | undefined} event - Optional webhook event data
 * @param {string} transferStore - Optional transfer store for TRANSFER events
 * @return {Promise<IUser | undefined>} Updated user data or undefined on error
 */
export async function processEvent(
  userId: string,
  event: RevenuecatWebhookEvent | undefined,
  transferStore?: string
): Promise<IUser | undefined> {
  try {
    console.log("Processing webhook with client restore purchase logic");

    // Step 1: Get current user data (to know previous state)
    const db = admin.firestore();
    const userDataSnapshot = await db.collection("users").doc(userId).get();
    const userData = userDataSnapshot.data() as IUser;
    const previousSubscriptionInfo = { ...userData.subscriptionInfo };

    // Step 2: Get fresh CustomerInfo from RevenueCat API (like client restore)
    const customerInfo = await getCurrentUserSubscritionStatus(userId);

    // Step 3: Process with client restore purchase logic (prioritizes transfer store or previous platform)
    const newSubscriptionStatus = convertCustomerInfoToStatus(
      customerInfo,
      previousSubscriptionInfo,
      transferStore
    );

    // Step 4: Update user data with new subscription status
    userData.subscriptionInfo = newSubscriptionStatus;
    userData.source = "cloud";
    userData.localUpdatedAt = getCurrentUTCDate();
    userData.cloudUpdatedAt = null;

    const processingContext = transferStore
      ? `Transfer (${transferStore})`
      : "Webhook";
    console.log(
      `${processingContext} processed - Previous: ${previousSubscriptionInfo.entitlement}/${previousSubscriptionInfo.subscriptionState}, New: ${newSubscriptionStatus.entitlement}/${newSubscriptionStatus.subscriptionState}`
    );

    // Step 5: Send emails based on subscription state changes (always send, regardless of DB update)
    await sendEmailBasedOnStateChange(
      userId,
      previousSubscriptionInfo,
      newSubscriptionStatus,
      event
    );

    // Step 6: Save to database ONLY if subscription info actually changed
    // This avoids interfering with client's restore purchase logic
    if (
      isSubscriptionInfoChanged(previousSubscriptionInfo, newSubscriptionStatus)
    ) {
      console.log("Subscription info changed, updating database");
      if (userData.docVer == currentDbVersion) {
        await new DbRepository().updateData(getMeParsedData(userData));
      } else {
        await publishMessageToPubSub(`updateWithVersion_v${userData.docVer}`, {
          userData: JSON.stringify(userData),
        });
      }
    } else {
      console.log(
        "No subscription changes detected, skipping database update (client will handle via restore)"
      );
    }

    // Step 7: Save transaction details only for actual purchase events (not transfers)
    if (event != undefined && isPurchaseEvent(event.type)) {
      await saveMeTransactionDetails(
        userData.uid,
        userData.subscriptionInfo,
        event
      );
    }

    return userData;
  } catch (error) {
    // Log error with context but don't throw - webhook should retry
    console.error(`Error in processing webhook like client restore: ${error}`);
    console.error(
      `Context: userId=${userId}, event=${event?.type}, transferStore=${transferStore}`
    );
  }
  return undefined; // Webhook will retry on undefined return
}

/**
 * Processes TRANSFER events that move subscriptions between user accounts
 *
 * TRANSFER EVENT LOGIC: Cross-Account Subscription Movement
 * ========================================================
 *
 * Transfer events occur when subscriptions move from one user account to another,
 * typically during family sharing changes or account merges. We need to:
 *
 * 1. Process users who LOST subscriptions (transferred_from)
 * 2. Process users who GAINED subscriptions (transferred_to)
 * 3. Use transfer store to prioritize the correct platform
 *
 * BUSINESS IMPACT:
 * - Users losing subscriptions should get updated subscription state
 * - Users gaining subscriptions should get the new platform prioritized
 * - Email notifications should reflect the transfer changes
 *
 * @param {RevenuecatWebhookEvent} event - Transfer webhook event with user arrays
 */
async function processTransferEvent(
  event: RevenuecatWebhookEvent
): Promise<void> {
  const transferStore = event.store; // "APP_STORE" or "PLAY_STORE"
  console.log(`Processing TRANSFER event for store: ${transferStore}`);

  // Process users who lost subscriptions (transferred_from)
  if (event.transferred_from && event.transferred_from.length > 0) {
    for (const userId of event.transferred_from) {
      if (userId && !userId.includes("RCAnonymousID")) {
        console.log(`Processing transfer FROM user: ${userId}`);
        await processEvent(userId, event);
      }
    }
  }

  // Process users who gained subscriptions (transferred_to)
  if (event.transferred_to && event.transferred_to.length > 0) {
    for (const userId of event.transferred_to) {
      if (userId && !userId.includes("RCAnonymousID")) {
        console.log(
          `Processing transfer TO user: ${userId} on store: ${transferStore}`
        );
        await processEvent(userId, event, transferStore);
      }
    }
  }

  console.log("Transfer event processing complete");
}

// =====================================================================================
// CORE BUSINESS LOGIC FUNCTIONS
// =====================================================================================

/**
 * Fetches the latest subscription data directly from RevenueCat API
 *
 * WHY WE CALL THE API: Source of Truth Strategy
 * ============================================
 *
 * We always get fresh data from RevenueCat because:
 * - Webhook events might be delayed or out of order
 * - Multiple platform changes could happen rapidly
 * - RevenueCat API is the definitive source of subscription truth
 * - Ensures we process the most current subscription state
 *
 * This mirrors exactly what the client app does during restore purchase,
 * maintaining perfect consistency between webhook and client processing.
 *
 * @param {string} userId - RevenueCat customer ID to fetch subscription data for
 * @return {Promise<RcUserPurchaseStatus>} Latest subscription status from RevenueCat
 */
async function getCurrentUserSubscritionStatus(
  userId: string
): Promise<RcUserPurchaseStatus> {
  const url = "https://api.revenuecat.com/v1/subscribers/" + userId;
  const options = {
    method: "GET",
    headers: {
      authorization: `Bearer ${process.env.REVENUECAT_V1_API_KEY}`,
    },
  };
  const response = await fetch(url, options);
  const data = await response.json();
  console.log(
    `RevenueCat API response for ${userId}:`,
    JSON.stringify(data, null, 2)
  );
  return data.subscriber as RcUserPurchaseStatus;
}

/**
 * Converts RevenueCat customer info to subscription status (mirrors client restore purchase logic)
 *
 * CRITICAL BUSINESS LOGIC: Cross-Platform Subscription Handling
 * ============================================================
 *
 * This function handles the complex scenario where users can have subscriptions on multiple platforms
 * (iOS App Store + Google Play Store) simultaneously. We must choose which one to prioritize.
 *
 * PLATFORM PRIORITIZATION ALGORITHM:
 * 1. Transfer store (if provided) - User explicitly transferred TO this platform
 * 2. Previous platform - User's existing platform preference
 * 3. Fallback platforms - Any available active subscription
 *
 * EDGE CASES HANDLED:
 * - User has active subs on both iOS + Android → Use previous platform preference
 * - User transfers from iOS → Android → Prioritize Android (user's choice)
 * - User has expired subscription on preferred platform → Switch to active platform
 * - No active subscriptions anywhere → Use most recent expired subscription data
 * - Multiple active subscriptions on same platform → Use longest expiration date
 *
 * WHY THIS MATTERS:
 * - Prevents platform switching confusion
 * - Respects user's explicit choices (transfers)
 * - Maintains consistent experience across app launches
 * - Avoids subscription state flickering between platforms
 *
 * @param {RcUserPurchaseStatus} rcUserPurchaseStatus - RevenueCat user purchase status data
 * @param {ISubscriptionInfo} previousSubscriptionInfo - Previous subscription information for platform prioritization
 * @param {string} transferStore - Optional transfer store ("APP_STORE" or "PLAY_STORE") for transfer events
 * @return {ISubscriptionInfo} Processed subscription information
 */
function convertCustomerInfoToStatus(
  rcUserPurchaseStatus: RcUserPurchaseStatus,
  previousSubscriptionInfo: ISubscriptionInfo,
  transferStore?: string
): ISubscriptionInfo {
  const logContext = transferStore
    ? `transfer prioritization: ${transferStore}`
    : "restore purchase logic";
  console.log(`Processing subscription with ${logContext}`);

  // Step 1: Convert RevenueCat subscriptions to array format for processing
  const allSubscriptions = convertSubscriptionsToArray(
    rcUserPurchaseStatus.subscriptions
  );
  const activeSubscriptions = allSubscriptions.filter(
    (s) => new Date(s.expires_date).getTime() > Date.now()
  );

  console.log(
    `Found ${allSubscriptions.length} total subscriptions, ${activeSubscriptions.length} active`
  );
  console.log(
    "All subscriptions:",
    allSubscriptions.map(
      (s) =>
        `${s.identifier}(${s.store}, ${s.product_plan_identifier}, exp: ${s.expires_date})`
    )
  );
  console.log(
    "Active subscriptions:",
    activeSubscriptions.map(
      (s) =>
        `${s.identifier}(${s.store}, ${s.product_plan_identifier}, exp: ${s.expires_date})`
    )
  );

  // Step 2: Sort all subscriptions by expiration date (latest first) - mirrors client
  if (allSubscriptions.length > 0) {
    sortSubscriptionsByExpirationDate(allSubscriptions);
  }

  // Step 3: Cross-Platform Subscription Prioritization (mirrors client restore purchase)
  let sortedActiveSubscriptionList = [...activeSubscriptions];

  if (sortedActiveSubscriptionList.length > 0) {
    // Convert transfer store to our format (if provided)
    const transferStoreType = transferStore
      ? convertRevenueCatStoreToInternalFormat(transferStore)
      : undefined;
    const currentStoreType = previousSubscriptionInfo?.storeType;

    console.log(
      `Transfer store: "${transferStore}" -> converted to: "${transferStoreType}"`
    );
    console.log(`Previous store type: "${currentStoreType}"`);

    // Separate subscriptions by platform
    const androidSubs = sortedActiveSubscriptionList.filter(
      (s) => s.store === RevenueCatStore.PLAY_STORE
    );
    const iosSubs = sortedActiveSubscriptionList.filter(
      (s) => s.store === RevenueCatStore.APP_STORE
    );

    console.log(
      `Platform separation - Android: ${androidSubs.length} subs, iOS: ${iosSubs.length} subs`
    );
    console.log(
      "Android subs:",
      androidSubs.map((s) => `${s.identifier}(${s.product_plan_identifier})`)
    );
    console.log(
      "iOS subs:",
      iosSubs.map((s) => `${s.identifier}(${s.product_plan_identifier})`)
    );

    // Priority logic: transfer store > previous platform > other platform
    console.log(
      `Checking conditions - transferStoreType === PLAY_STORE: ${transferStoreType === StoreType.PLAY_STORE}, androidSubs > 0: ${androidSubs.length > 0}`
    );
    if (transferStoreType === StoreType.PLAY_STORE && androidSubs.length > 0) {
      // Transfer to Android, prioritize Android
      sortSubscriptionsByExpirationDate(androidSubs);
      sortedActiveSubscriptionList = androidSubs;
      console.log(
        `Prioritizing transferred platform: Android (${androidSubs.length} active)`
      );
    } else if (
      transferStoreType === StoreType.APP_STORE &&
      iosSubs.length > 0
    ) {
      // Transfer to iOS, prioritize iOS
      sortSubscriptionsByExpirationDate(iosSubs);
      sortedActiveSubscriptionList = iosSubs;
      console.log(
        `Prioritizing transferred platform: iOS (${iosSubs.length} active)`
      );
    } else if (
      currentStoreType === StoreType.PLAY_STORE &&
      androidSubs.length > 0
    ) {
      // Fallback to previous platform: Android
      sortSubscriptionsByExpirationDate(androidSubs);
      sortedActiveSubscriptionList = androidSubs;
      console.log(
        `Maintaining previous platform: Android (${androidSubs.length} active)`
      );
    } else if (currentStoreType === StoreType.APP_STORE && iosSubs.length > 0) {
      // Fallback to previous platform: iOS
      sortSubscriptionsByExpirationDate(iosSubs);
      sortedActiveSubscriptionList = iosSubs;
      console.log(
        `Maintaining previous platform: iOS (${iosSubs.length} active)`
      );
    } else if (androidSubs.length > 0) {
      // Final fallback to Android
      sortSubscriptionsByExpirationDate(androidSubs);
      sortedActiveSubscriptionList = androidSubs;
      console.log(
        `Switching to Android platform (${androidSubs.length} active)`
      );
    } else if (iosSubs.length > 0) {
      // Final fallback to iOS
      sortSubscriptionsByExpirationDate(iosSubs);
      sortedActiveSubscriptionList = iosSubs;
      console.log(`Switching to iOS platform (${iosSubs.length} active)`);
    }
  }

  // Step 4: Initialize defaults (mirror client exactly)
  const subscriptionInfo: ISubscriptionInfo = {
    subscriptionState: SubscriptionState.NONE, // Will be computed automatically
    subscriptionStartDate: null,
    subscriptionExpDate: null,
    subscriptionType: null,
    storeType: null,
    productId: null,
    unsubscribedAt: null,
    entitlement: Entitlement.FREE,
  };

  console.log(
    `Final sorted active subscriptions count: ${sortedActiveSubscriptionList.length}`
  );
  if (sortedActiveSubscriptionList.length > 0) {
    console.log(
      "Final selected subscriptions:",
      sortedActiveSubscriptionList.map(
        (s) => `${s.identifier}(${s.store}, ${s.product_plan_identifier})`
      )
    );
  }

  // Step 5: Process ACTIVE subscriptions first (highest priority) - mirrors client
  if (sortedActiveSubscriptionList.length > 0) {
    const premiumSubscription = sortedActiveSubscriptionList[0];
    console.log(
      `Selected ACTIVE subscription: ${premiumSubscription.identifier}(${premiumSubscription.store}, ${premiumSubscription.product_plan_identifier})`
    );
    const fields = extractSubscriptionFields(
      premiumSubscription,
      rcUserPurchaseStatus
    );

    subscriptionInfo.entitlement = fields.entitlement;
    subscriptionInfo.subscriptionStartDate = fields.subscriptionStartDate;
    subscriptionInfo.subscriptionExpDate = fields.subscriptionExpDate;
    subscriptionInfo.storeType = fields.storeType;
    subscriptionInfo.productId = fields.productId;
    subscriptionInfo.subscriptionType = fields.subscriptionType;
    subscriptionInfo.unsubscribedAt = fields.unsubscribedAt;
  }
  // Step 6: Fallback to EXPIRED subscriptions if no active ones - mirrors client
  else if (allSubscriptions.length > 0) {
    const premiumSubscription = allSubscriptions[0];
    console.log(
      `Selected EXPIRED subscription: ${premiumSubscription.identifier}(${premiumSubscription.store}, ${premiumSubscription.product_plan_identifier})`
    );
    const fields = extractSubscriptionFields(
      premiumSubscription,
      rcUserPurchaseStatus
    );

    // Override entitlement for expired subscriptions (mirror client exactly)
    subscriptionInfo.entitlement = Entitlement.FREE;
    subscriptionInfo.subscriptionStartDate = fields.subscriptionStartDate;
    subscriptionInfo.subscriptionExpDate = fields.subscriptionExpDate;
    subscriptionInfo.storeType = fields.storeType;
    subscriptionInfo.productId = fields.productId;
    subscriptionInfo.subscriptionType = fields.subscriptionType;
    subscriptionInfo.unsubscribedAt = fields.unsubscribedAt;
  } else {
    console.log("No subscriptions found - using default FREE state");
  }

  // Step 7: Compute subscription state based on expiration date (mirrors Dart getter)
  subscriptionInfo.subscriptionState = computeSubscriptionState(
    subscriptionInfo.subscriptionExpDate
  );

  const processingType = transferStore ? "Transfer" : "Standard";
  console.log(
    `${processingType} processing complete - Entitlement: ${subscriptionInfo.entitlement}, State: ${subscriptionInfo.subscriptionState}`
  );

  return subscriptionInfo;
}

// =====================================================================================
// EMAIL NOTIFICATION SYSTEM
// =====================================================================================

/**
 * Send emails based on subscription state changes or product change events
 *
 * Hybrid approach: prioritizes state changes, falls back to product change detection
 * @param {string} userId - User ID for fetching user details
 * @param {ISubscriptionInfo} previousSubscriptionInfo - Previous subscription state
 * @param {ISubscriptionInfo} newSubscriptionStatus - New subscription state
 * @param {RevenuecatWebhookEvent} event - Webhook event data for product change detection
 */
async function sendEmailBasedOnStateChange(
  userId: string,
  previousSubscriptionInfo: ISubscriptionInfo,
  newSubscriptionStatus: ISubscriptionInfo,
  event?: RevenuecatWebhookEvent
): Promise<void> {
  try {
    console.log(
      `Checking email actions - Previous: ${previousSubscriptionInfo.entitlement}/${previousSubscriptionInfo.subscriptionState}, New: ${newSubscriptionStatus.entitlement}/${newSubscriptionStatus.subscriptionState}`
    );

    // Get user details for email
    const userDetail = await getEmailNameByIdFromAuthService(userId);
    if (!userDetail?.email) {
      console.log("No email found for user, skipping email notifications");
      return;
    }

    const viewSettings = await getViewSettingsByUserId(userId);
    const language = viewSettings?.appSettings.language;
    const timeZone =
      viewSettings?.notificationSettings.emailNotificationTimezone;

    // Hybrid Email Logic: state changes first, with special product change override for cancellations
    let emailAction = null;

    // Always send cancellation email for any cancellation webhook (independent of state changes)
    if (event && event.type === RevenueCatEventType.CANCELLATION) {
      console.log(
        "CANCELLATION webhook received - sending cancellation email using event data"
      );
      emailAction = EmailAction.SUBSCRIPTION_CANCELLED;
    }

    // Check state changes first (most reliable) - only if no cancellation webhook
    if (!emailAction) {
      emailAction = determineEmailAction(
        previousSubscriptionInfo,
        newSubscriptionStatus
      );
    }

    // Special case: If state change says "cancellation" but we have PRODUCT_CHANGE event,
    // override with product change logic (likely deferred downgrade scenario)
    if (
      emailAction === EmailAction.SUBSCRIPTION_CANCELLED &&
      event &&
      event.type === RevenueCatEventType.PRODUCT_CHANGE &&
      event.new_product_id
    ) {
      console.log(
        "State change detected cancellation, but PRODUCT_CHANGE event present - overriding with product change logic"
      );
      emailAction = determineEmailActionFromProductChange(
        event,
        newSubscriptionStatus
      );
      console.log(
        `Product change override: ${event.product_id} → ${event.new_product_id}, action: ${emailAction}`
      );
    }

    // Final fallback: Check product change event if no state-based action was determined
    if (
      !emailAction &&
      event &&
      event.type === RevenueCatEventType.PRODUCT_CHANGE &&
      event.new_product_id
    ) {
      console.log(
        "No subscription state change, checking product change event for immediate email"
      );
      emailAction = determineEmailActionFromProductChange(
        event,
        newSubscriptionStatus
      );
      console.log(
        `Product change detected: ${event.product_id} → ${event.new_product_id}, action: ${emailAction}`
      );
    }

    if (!emailAction) {
      console.log(
        "No email action needed - no subscription or product changes detected"
      );
      return;
    }

    console.log(`Sending email: ${emailAction}`);

    // Send appropriate email based on action
    switch (emailAction) {
      case EmailAction.SUBSCRIPTION_STARTED:
      case EmailAction.SUBSCRIPTION_UPGRADED:
      case EmailAction.SUBSCRIPTION_RENEWED:
        await sendSingleEmailViaSes({
          templateName: MevolveEmails.upgradeSuccessful,
          recipient: userDetail.email,
          templateData: {
            name: userDetail.name ?? "",
            shortPlanName: extractPlanNameFromProductId(
              newSubscriptionStatus.productId || ""
            ),
            fullPlanName: formatSubscriptionNameForEmails(
              newSubscriptionStatus.productId || ""
            ),
            startDate: newSubscriptionStatus.subscriptionExpDate
              ? moment(newSubscriptionStatus.subscriptionExpDate)
                  .utcOffset(timeZone ?? "")
                  .format("Do MMM YYYY")
              : "",
            appLink: getAppLink(),
            linkToSupportChat: getSupportLink(),
          },
          language: language,
        });
        break;

      case EmailAction.SUBSCRIPTION_EXPIRED:
        await sendSingleEmailViaSes({
          templateName: MevolveEmails.subscriptionExpired,
          recipient: userDetail.email,
          templateData: {
            name: userDetail.name ?? "",
            subscriptionLink: getSubscriptionPageLink(),
            linkToSupportChat: getSupportLink(),
          },
          language: language,
        });
        break;

      case EmailAction.SUBSCRIPTION_CANCELLED: {
        // Use event data for cancellation emails when available, fallback to user data
        const productId =
          event &&
          event.type === RevenueCatEventType.CANCELLATION &&
          event.product_id
            ? event.product_id
            : previousSubscriptionInfo.productId || "";

        await sendSingleEmailViaSes({
          templateName: productId.includes(Entitlement.BASIC)
            ? MevolveEmails.userCancelsSubscriptionFromBasicPlan
            : MevolveEmails.userCancelsSubscriptionFromPremiumPlans,
          recipient: userDetail.email,
          templateData: {
            name: userDetail.name ?? "",
            plan: extractPlanNameFromProductId(productId),
            subscriptionLink: getSubscriptionPageLink(),
            linkToSupportChat: getSupportLink(),
          },
          language: language,
        });
        break;
      }

      case EmailAction.SUBSCRIPTION_DOWNGRADED:
        await sendSingleEmailViaSes({
          templateName: MevolveEmails.subscriptionDowngraded,
          recipient: userDetail.email,
          templateData: {
            name: userDetail.name ?? "",
            oldSubsType: formatSubscriptionNameForEmails(
              previousSubscriptionInfo.productId || ""
            ),
            endDate: newSubscriptionStatus.subscriptionExpDate
              ? moment(newSubscriptionStatus.subscriptionExpDate)
                  .utcOffset(timeZone ?? "")
                  .format("ll")
              : "",
            newSubsType: formatSubscriptionNameForEmails(
              newSubscriptionStatus.productId || ""
            ),
            startDate: newSubscriptionStatus.subscriptionExpDate
              ? moment(newSubscriptionStatus.subscriptionExpDate)
                  .utcOffset(timeZone ?? "")
                  .format("ll")
              : "",
            appLink: getAppLink(),
            linkToSupportChat: getSupportLink(),
          },
          language: language,
        });
        break;

      case EmailAction.SUBSCRIPTION_UPDATED:
        await sendSingleEmailViaSes({
          templateName: MevolveEmails.subscriptionUpdated,
          recipient: userDetail.email,
          templateData: {
            name: userDetail.name ?? "",
            shortPlanName: extractPlanNameFromProductId(
              newSubscriptionStatus.productId || ""
            ),
            fullPlanName: formatSubscriptionNameForEmails(
              newSubscriptionStatus.productId || ""
            ),
            startDate: newSubscriptionStatus.subscriptionExpDate
              ? moment(newSubscriptionStatus.subscriptionExpDate)
                  .utcOffset(timeZone ?? "")
                  .format("Do MMM YYYY")
              : "",
            appLink: getAppLink(),
            linkToSupportChat: getSupportLink(),
          },
          language: language,
        });
        break;
    }
  } catch (error) {
    console.error(`Error sending email notification: ${error}`);
  }
}

/**
 * Determine what email action to take based on subscription state changes
 * @param {ISubscriptionInfo} previousSubscriptionInfo - Previous subscription state
 * @param {ISubscriptionInfo} newSubscriptionStatus - New subscription state
 * @return {string | null} Email action to take or null if no action needed
 */
function determineEmailAction(
  previousSubscriptionInfo: ISubscriptionInfo,
  newSubscriptionStatus: ISubscriptionInfo
): string | null {
  const oldState = previousSubscriptionInfo.subscriptionState;
  const newState = newSubscriptionStatus.subscriptionState;
  const oldEntitlement = previousSubscriptionInfo.entitlement;
  const newEntitlement = newSubscriptionStatus.entitlement;

  // Use unified product hierarchy level function for consistent comparison

  // New subscription (from none/free to active)
  if (
    (oldState === SubscriptionState.NONE ||
      oldEntitlement === Entitlement.FREE) &&
    newState === SubscriptionState.SUBSCRIBED &&
    newEntitlement !== Entitlement.FREE
  ) {
    return EmailAction.SUBSCRIPTION_STARTED;
  }

  // Subscription expired
  if (
    oldState === SubscriptionState.SUBSCRIBED &&
    newState === SubscriptionState.EXPIRED
  ) {
    return EmailAction.SUBSCRIPTION_EXPIRED;
  }

  // Subscription renewed (from expired to active)
  if (
    oldState === SubscriptionState.EXPIRED &&
    newState === SubscriptionState.SUBSCRIBED
  ) {
    return EmailAction.SUBSCRIPTION_RENEWED;
  }

  // Subscription cancelled (unsubscribedAt changed from null to a value)
  if (
    oldState === SubscriptionState.SUBSCRIBED &&
    newState === SubscriptionState.SUBSCRIBED &&
    previousSubscriptionInfo.unsubscribedAt == null &&
    newSubscriptionStatus.unsubscribedAt != null
  ) {
    return EmailAction.SUBSCRIPTION_CANCELLED;
  }

  // Entitlement level changed while active
  if (
    oldState === SubscriptionState.SUBSCRIBED &&
    newState === SubscriptionState.SUBSCRIBED &&
    oldEntitlement !== newEntitlement
  ) {
    const oldLevel = getSubscriptionTierHierarchyLevel(oldEntitlement);
    const newLevel = getSubscriptionTierHierarchyLevel(newEntitlement);

    if (newLevel > oldLevel) {
      return EmailAction.SUBSCRIPTION_UPGRADED;
    } else if (newLevel < oldLevel) {
      return EmailAction.SUBSCRIPTION_DOWNGRADED;
    } else {
      return EmailAction.SUBSCRIPTION_UPDATED; // Same level but different product
    }
  }

  // Product changed within same entitlement level (e.g., monthly to yearly)
  if (
    oldState === SubscriptionState.SUBSCRIBED &&
    newState === SubscriptionState.SUBSCRIBED &&
    oldEntitlement === newEntitlement &&
    previousSubscriptionInfo.productId !== newSubscriptionStatus.productId
  ) {
    return EmailAction.SUBSCRIPTION_UPDATED;
  }

  return null; // No email action needed
}

/**
 * Determine email action for PRODUCT_CHANGE events using event data (handles deferred subscription changes)
 *
 * CRITICAL SCENARIO: Deferred Downgrades (Pro → Basic)
 * ====================================================
 *
 * When a user downgrades from Pro to Basic:
 * 1. RevenueCat immediately fires PRODUCT_CHANGE webhook with old_product_id=pro, new_product_id=basic
 * 2. BUT subscription state remains "Pro/subscribed" until current billing period expires
 * 3. Database state shows no change (still Pro/subscribed)
 * 4. State-based email detection fails because old state = new state
 * 5. User gets NO immediate confirmation email about their downgrade
 *
 * SOLUTION: Event-based product change detection
 * - Use event.new_product_id for what user is changing TO
 * - Use newSubscriptionStatus.productId for what user actually HAD (more reliable than event.product_id)
 * - Compare product hierarchy levels to determine upgrade/downgrade/update
 * - Send immediate confirmation email even though subscription state hasn't changed yet
 *
 * This ensures users get instant feedback that their subscription change was processed,
 * which is critical for user experience and reducing support queries.
 *
 * @param {RevenuecatWebhookEvent} event - Webhook event with product change data
 * @param {ISubscriptionInfo} newSubscriptionStatus - Current subscription status for reliable old product reference
 * @return {string | null} Email action to take or null if no action needed
 */
function determineEmailActionFromProductChange(
  event: RevenuecatWebhookEvent,
  newSubscriptionStatus: ISubscriptionInfo
): string | null {
  if (!event.new_product_id) {
    return null;
  }

  // Use current subscription productId as the "old" product since subscription state didn't change
  // This gives us the reliable product the user actually had before the change
  const oldProductId = newSubscriptionStatus.productId || event.product_id;
  const oldProductLevel = getSubscriptionTierHierarchyLevel(oldProductId);
  const newProductLevel = getSubscriptionTierHierarchyLevel(
    event.new_product_id
  );

  // Determine action based on product level comparison
  if (newProductLevel > oldProductLevel) {
    return EmailAction.SUBSCRIPTION_UPGRADED;
  } else if (newProductLevel < oldProductLevel) {
    return EmailAction.SUBSCRIPTION_DOWNGRADED;
  } else {
    // Same level but different product (e.g., monthly to yearly)
    return EmailAction.SUBSCRIPTION_UPDATED;
  }
}

// =====================================================================================
// DATA VALIDATION & CHANGE DETECTION
// =====================================================================================

/**
 * Check if subscription info has meaningful changes that warrant database update
 *
 * DATABASE UPDATE STRATEGY: Avoid Unnecessary Writes
 * =================================================
 *
 * We only update the database when subscription data actually changes to:
 * - Reduce Firestore write costs
 * - Prevent race conditions with client app restore purchases
 * - Avoid triggering unnecessary downstream processes
 * - Maintain data consistency between webhook and client updates
 *
 * FIELDS COMPARED:
 * - State fields: subscriptionState, entitlement, subscriptionType, storeType, productId
 * - Date fields: subscriptionStartDate, subscriptionExpDate, unsubscribedAt (with null handling)
 *
 * EDGE CASES HANDLED:
 * - Null vs actual date comparisons (subscription start/end dates)
 * - Microsecond timestamp differences (compare timestamps, not Date objects)
 * - Missing fields (treat as no change to avoid false positives)
 *
 * @param {ISubscriptionInfo} previous - Previous subscription information
 * @param {ISubscriptionInfo} current - Current subscription information
 * @return {boolean} True if subscription info changed, false otherwise
 */
function isSubscriptionInfoChanged(
  previous: ISubscriptionInfo,
  current: ISubscriptionInfo
): boolean {
  // Compare all subscription fields for changes
  const fieldsToCompare = [
    "subscriptionState",
    "entitlement",
    "subscriptionType",
    "storeType",
    "productId",
  ] as const;

  // Check basic field changes
  for (const field of fieldsToCompare) {
    if (previous[field] !== current[field]) {
      console.log(
        `Field changed: ${field} from '${previous[field]}' to '${current[field]}'`
      );
      return true;
    }
  }

  // Check date field changes (handle null cases and compare timestamps)
  const dateFieldsToCompare = [
    "subscriptionStartDate",
    "subscriptionExpDate",
    "unsubscribedAt",
  ] as const;

  for (const field of dateFieldsToCompare) {
    const prevDate = previous[field];
    const currDate = current[field];

    // If one is null and other isn't, it's a change
    if ((prevDate == null) !== (currDate == null)) {
      console.log(
        `Date field changed: ${field} from ${prevDate} to ${currDate}`
      );
      return true;
    }

    // If both are dates, compare timestamps (handle both Date objects and Firestore Timestamps)
    if (prevDate != null && currDate != null) {
      const prevDateObj = toDate(prevDate);
      const currDateObj = toDate(currDate);

      if (prevDateObj == null || currDateObj == null) {
        console.error(
          `Invalid date comparison for ${field}: prev=${prevDate}, curr=${currDate}`
        );
        return false;
      }

      if (prevDateObj.getTime() !== currDateObj.getTime()) {
        console.log(
          `Date field changed: ${field} from '${prevDateObj.toISOString()}' to '${currDateObj.toISOString()}'`
        );
        return true;
      }
    }
  }

  return false; // No changes detected
}

// =====================================================================================
// SUBSCRIPTION DATA PROCESSING UTILITIES
// =====================================================================================

/**
 * Extracts subscription fields from RevenueCat subscription data
 * @param {object} subscription - Subscription data from RevenueCat
 * @param {RcUserPurchaseStatus} _rcUserPurchaseStatus - RevenueCat user purchase status (unused but kept for consistency)
 * @return {object} Extracted subscription fields
 */
function extractSubscriptionFields(
  subscription: {
    expires_date: string;
    purchase_date: string;
    product_plan_identifier: string;
    store: string;
    unsubscribe_detected_at?: string;
    identifier: string;
  },
  _rcUserPurchaseStatus: RcUserPurchaseStatus
): {
  entitlement: Entitlement;
  subscriptionStartDate: Date | null;
  subscriptionExpDate: Date | null;
  storeType: string;
  productId: string;
  subscriptionType: SubscriptionPeriod;
  unsubscribedAt: Date | null;
} {
  const expirationDate = new Date(subscription.expires_date);
  const isExpired = expirationDate.getTime() < Date.now();

  const subscriptionStartDate = toDate(subscription.purchase_date);
  const subscriptionExpDate = toDate(subscription.expires_date);
  const unsubscribedAt = toDate(subscription.unsubscribe_detected_at);

  return {
    entitlement: isExpired
      ? Entitlement.FREE
      : parseServerEntitlement(subscription.identifier),
    subscriptionStartDate,
    subscriptionExpDate,
    storeType: convertRevenueCatStoreToInternalFormat(subscription.store),
    productId: subscription.product_plan_identifier,
    subscriptionType: getProductSubscriptionPeriod(
      subscription.product_plan_identifier
    ),
    unsubscribedAt,
  };
}

/**
 * Convert RevenueCat subscriptions to array format for processing
 * @param {any} subscriptions - RevenueCat subscriptions object from API response
 * @return {Array<object>} Array of subscription data
 */
function convertSubscriptionsToArray(subscriptions: any): Array<{
  identifier: string;
  expires_date: string;
  purchase_date: string;
  product_plan_identifier: string;
  store: string;
  unsubscribe_detected_at?: string;
}> {
  const result: Array<{
    identifier: string;
    expires_date: string;
    purchase_date: string;
    product_plan_identifier: string;
    store: string;
    unsubscribe_detected_at?: string;
  }> = [];

  // Convert each subscription
  for (const [key, subscription] of Object.entries(subscriptions)) {
    const sub = subscription as any;
    if (sub && sub.expires_date) {
      // Convert API store format to constants format: "play_store" -> "PLAY_STORE", "app_store" -> "APP_STORE"
      let store = sub.store;
      if (store === "play_store") {
        store = RevenueCatStore.PLAY_STORE;
      } else if (store === "app_store") {
        store = RevenueCatStore.APP_STORE;
      }

      result.push({
        identifier: key,
        expires_date: sub.expires_date,
        purchase_date: sub.original_purchase_date || sub.purchase_date,
        product_plan_identifier: sub.product_plan_identifier || key,
        store: store,
        unsubscribe_detected_at: sub.unsubscribe_detected_at,
      });
    }
  }

  return result;
}

/**
 * Sorts subscriptions by expiration date in descending order (latest first)
 * Mirrors Flutter client's _sortEntitlementsByExpirationDate method EXACTLY
 * @param {Array<object>} subscriptions - Array of subscriptions to sort
 */
function sortSubscriptionsByExpirationDate(
  subscriptions: Array<{ expires_date: string }>
): void {
  subscriptions.sort((a, b) => {
    const dateA = new Date(a.expires_date);
    const dateB = new Date(b.expires_date);
    return dateB.getTime() - dateA.getTime();
  });
}

/**
 * Mirrors Flutter client's parseServerEntitlement method EXACTLY
 * @param {string} productIdentifier - Product identifier string from RevenueCat
 * @return {Entitlement} Parsed entitlement level
 */
function parseServerEntitlement(productIdentifier: string): Entitlement {
  const identifier = productIdentifier.toLowerCase();

  if (identifier.includes(Entitlement.BASIC)) {
    return Entitlement.BASIC;
  } else if (identifier.includes(Entitlement.PRO)) {
    return Entitlement.PRO;
  } else if (identifier.includes(Entitlement.PLUS)) {
    return Entitlement.PLUS;
  } else {
    return Entitlement.FREE;
  }
}

/**
 * Computes subscription state based on expiration date (mirrors Dart getter)
 *
 * This ensures the state is always consistent with the expiration date
 * @param {Date | null} subscriptionExpDate - The subscription expiration date or null
 * @return {SubscriptionState} The computed subscription state
 */
function computeSubscriptionState(
  subscriptionExpDate: Date | null
): SubscriptionState {
  if (subscriptionExpDate == null) {
    return SubscriptionState.NONE;
  } else if (subscriptionExpDate.getTime() > Date.now()) {
    return SubscriptionState.SUBSCRIBED;
  } else {
    return SubscriptionState.EXPIRED;
  }
}

/**
 * Helper method to mirror client functionality
 * @param {string} productIdentifier - Product identifier to parse
 * @return {SubscriptionPeriod} Subscription period type
 */
function getProductSubscriptionPeriod(
  productIdentifier: string
): SubscriptionPeriod {
  const productId = productIdentifier.toLowerCase();

  if (
    productId.includes(SubscriptionPeriod.MONTHLY) ||
    productId.includes(PeriodKeywords.MONTH)
  ) {
    return SubscriptionPeriod.MONTHLY;
  } else if (
    productId.includes(SubscriptionPeriod.YEARLY) ||
    productId.includes(PeriodKeywords.YEAR) ||
    productId.includes(PeriodKeywords.ANNUAL)
  ) {
    return SubscriptionPeriod.YEARLY;
  }

  return SubscriptionPeriod.CUSTOM;
}

// =====================================================================================
// HELPER METHODS - BUSINESS LOGIC UTILITIES
// =====================================================================================

/**
 * Utility function to consistently convert any date value to JavaScript Date
 * Handles: Firestore Timestamps, Date objects, ISO strings, null/undefined
 *
 * @param {any} value - Date value in any format
 * @return {Date | null} JavaScript Date object or null
 */
function toDate(value: any): Date | null {
  if (value === null || value === undefined) return null;
  if (value instanceof Date) return value;
  if (value.toDate && typeof value.toDate === "function") return value.toDate(); // Firestore Timestamp
  try {
    const date = new Date(value);
    return isNaN(date.getTime()) ? null : date;
  } catch (error) {
    console.error(`Failed to convert to date: ${value}`, error);
    return null;
  }
}

/**
 * Determines if a RevenueCat event type represents an actual purchase/transaction
 *
 * BUSINESS PURPOSE: Transaction Logging Scope
 * ==========================================
 *
 * Only actual purchase events should be saved as transaction records.
 * Events like TRANSFER, CANCELLATION, etc. are subscription state changes
 * but not new purchases/transactions.
 *
 * @param {RevenueCatEventType} eventType - RevenueCat event type
 * @return {boolean} True if event represents a purchase transaction
 */
function isPurchaseEvent(eventType: RevenueCatEventType): boolean {
  const purchaseEvents: RevenueCatEventType[] = [
    RevenueCatEventType.INITIAL_PURCHASE,
    RevenueCatEventType.RENEWAL,
    RevenueCatEventType.NON_RENEWING_PURCHASE,
    RevenueCatEventType.PRODUCT_CHANGE, // Product upgrades/downgrades are purchase transactions
  ];

  return purchaseEvents.includes(eventType);
}

/**
 * Gets subscription tier hierarchy level for upgrade/downgrade comparison
 *
 * BUSINESS PURPOSE: Subscription Level Comparison
 * ==============================================
 *
 * Provides single source of truth for product hierarchy levels used in:
 * - Email upgrade/downgrade detection
 * - Subscription state comparison
 * - Product level validation
 *
 * @param {string} productOrEntitlement - Product ID or entitlement name
 * @return {number} Hierarchy level (0=free, 1=basic, 2=pro, 3=plus)
 */
function getSubscriptionTierHierarchyLevel(
  productOrEntitlement: string
): number {
  const name = extractPlanNameFromProductId(productOrEntitlement).toLowerCase();
  switch (name) {
    case Entitlement.FREE:
      return 0;
    case Entitlement.BASIC:
      return 1;
    case Entitlement.PRO:
      return 2;
    case Entitlement.PLUS:
      return 3;
    default:
      return 0;
  }
}

/**
 * Converts RevenueCat store format to internal store type format
 *
 * BUSINESS PURPOSE: Store Type Standardization
 * ============================================
 *
 * Ensures consistent store type format across webhook processing
 * for platform prioritization and subscription management.
 *
 * @param {string} rcStore - RevenueCat store format ("APP_STORE" | "PLAY_STORE")
 * @return {string} Internal store format ("app_store" | "play_store")
 */
function convertRevenueCatStoreToInternalFormat(rcStore: string): string {
  return rcStore === RevenueCatStore.PLAY_STORE
    ? StoreType.PLAY_STORE
    : StoreType.APP_STORE;
}

/**
 * Extracts plan name from complex RevenueCat product identifiers
 *
 * BUSINESS PURPOSE: Plan Level Detection
 * ====================================
 *
 * RevenueCat product IDs can be complex (e.g., "com.mevolve.pro_monthly:basic_yearly")
 * This function extracts just the plan level (basic, pro, plus) for email content
 * and subscription level comparisons.
 *
 * EXAMPLES:
 * - "com.mevolve.pro_monthly" → "pro"
 * - "basic_yearly:something" → "basic"
 * - "plus-annual-v2" → "plus"
 *
 * @param {string} productId - Full RevenueCat product identifier
 * @return {string} Plan name (basic, pro, plus, etc.)
 */
function extractPlanNameFromProductId(productId: string): string {
  return productId.split(":")[0].split("_")[0].split("-")[0];
}

/**
 * Extracts subscription billing period from product ID for email templates
 *
 * BUSINESS PURPOSE: Email Personalization
 * ======================================
 *
 * Users receive different email templates based on whether they have monthly or yearly subscriptions.
 * This function determines the billing period from RevenueCat product identifiers.
 *
 * @param {string} productId - RevenueCat product ID (e.g., "pro_monthly", "basic_yearly")
 * @return {string} Human-readable billing period ("Monthly" or "Yearly")
 */
function getBillingPeriodForEmails(productId: string): string {
  if (productId.includes(SubscriptionPeriod.MONTHLY)) {
    return DisplayLabels.MONTHLY;
  } else {
    return DisplayLabels.YEARLY;
  }
}

/**
 * Creates human-readable subscription names for email templates and UI display
 *
 * BUSINESS PURPOSE: User-Friendly Subscription Names
 * =================================================
 *
 * Combines plan level and billing period to create readable subscription names
 * for email notifications and user-facing displays.
 *
 * EXAMPLES:
 * - "pro_monthly" → "Pro Monthly"
 * - "basic_yearly" → "Basic Yearly"
 *
 * @param {string} productId - RevenueCat product identifier
 * @return {string} Formatted subscription name (e.g., "Pro Monthly")
 */
function formatSubscriptionNameForEmails(productId: string): string {
  return (
    extractPlanNameFromProductId(productId) +
    " " +
    getBillingPeriodForEmails(productId)
  );
}

/**
 * Formats price with proper currency symbol and locale (used for transaction logging)
 *
 * BUSINESS PURPOSE: Transaction Display & Audit
 * =============================================
 *
 * Converts raw price numbers from RevenueCat into human-readable currency strings
 * for transaction records, email notifications, and audit logs.
 *
 * @param {number} price - Raw price amount from RevenueCat (e.g., 9.99)
 * @param {string} currency - ISO currency code (e.g., "USD", "EUR")
 * @return {string} Formatted price string (e.g., "$9.99")
 */
export function getPriceString(price: number, currency: string): string {
  // Create our number formatter.
  const formatter = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currency,
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });

  return formatter.format(price);
}

// ============================================================================
// HTTP WEBHOOK ENDPOINT
// ============================================================================

import * as functions from "firebase-functions/v2";

/**
 * HTTP endpoint to receive RevenueCat webhook events
 * Processes subscription events and triggers appropriate business logic
 */
export const revenuecatWebHook = functions.https.onRequest(
  {
    // Check Function: Revenuecat webhook function so appcheck need to be disabled.
    enforceAppCheck: false,
    memory: "512MiB",
  },
  async (req, res) => {
    const event: RevenuecatWebhookEvent = req.body
      .event as RevenuecatWebhookEvent;

    // Log the event
    console.log("revenuecat event received:", JSON.stringify(event, null, 2));

    const result = await handleRevenuecatWebHook(event);
    res.json({ result: result });
  }
);
