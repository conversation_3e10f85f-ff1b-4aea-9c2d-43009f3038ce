/**
 * PURCHASE WEBHOOKS CONSOLIDATION
 * 
 * This file consolidates all webhook HTTP endpoints for subscription management:
 * - RevenueCat webhook for cross-platform subscription events  
 * - Stripe webhook for direct Stripe subscription events
 * 
 * Both webhooks are grouped here for api_non_version.ts deployment.
 */

import * as functions from "firebase-functions/v2";
import { RevenuecatWebhookEvent } from "./models/revenuecat_webhook_model";
import { handleRevenuecatWebHook } from "./revenuecat";
import { INVALID_ARGUMENT } from "../../../utils/constants";

// ============================================================================
// REVENUECAT WEBHOOK HTTP ENDPOINT
// ============================================================================

/**
 * HTTP endpoint to receive RevenueCat webhook events
 * Processes subscription events and triggers appropriate business logic
 */
exports.revenuecatWebHook = functions.https.onRequest(
  {
    // Check Function: Revenuecat webhook function so appcheck need to be disabled.
    enforceAppCheck: false,
    memory: "512MiB",
  },
  async (req, res) => {
    const event: RevenuecatWebhookEvent = req.body.event as RevenuecatWebhookEvent;

    // Log the event
    console.log("revenuecat event received:", JSON.stringify(event, null, 2));

    const result = await handleRevenuecatWebHook(event);
    res.json({ result: result });
  }
);

// ============================================================================
// STRIPE WEBHOOK HTTP ENDPOINT  
// ============================================================================

// Import the handler function from stripe_webhook.ts (keeping the logic there)
async function stripeWebHookHandler(event: any) {
  // Import the handler dynamically to avoid circular dependencies
  const { stripeWebHookHandler } = await import("./stripe");
  return stripeWebHookHandler(event);
}

// Import helper functions for subscription status
async function getStripeClient() {
  const { getStripeClient } = await import("./stripe");
  return getStripeClient();
}

async function getUserEntitlement(priceId: string) {
  const { getUserEntitlement } = await import("./stripe");
  return getUserEntitlement(priceId);
}

/**
 * HTTP endpoint to receive Stripe webhook events
 * Delegates processing to stripe_webhook.ts stripeWebHookHandler
 */
export const stripeWebhookEvent = functions.https.onRequest(
  { enforceAppCheck: false, memory: "256MiB" },
  async (req, res) => {
    try {
      await stripeWebHookHandler(req.body);
      res.json({ result: "done" });
    } catch (error) {
      console.error("Error processing Stripe webhook:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  }
);

// ============================================================================
// STRIPE PAYMENT RESULT HANDLER (PostMessage Integration)
// ============================================================================

/**
 * Single Stripe payment result handler (success and cancel)
 * Follows calendar_webhook.ts pattern - returns HTML with PostMessage
 * Handles both successful payments and cancelled checkouts
 */
export const stripePaymentResult = functions.https.onRequest(
  { enforceAppCheck: false, memory: "256MiB", timeoutSeconds: 60 },
  async (req, res) => {
    const sessionId = req.query.session_id as string;
    const cancelled = req.query.cancelled as string;

    // Determine success/cancel state
    const isSuccess = sessionId && !cancelled;
    const title = isSuccess ? "Payment Successful!" : "Payment Cancelled";
    const message = isSuccess
      ? "Your subscription is being activated..."
      : "No charges were made to your account.";

    const html = `<!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${title}</title>
      <style>
        * {
          margin: 0;
          padding: 0;
          box-sizing: border-box;
        }

        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', sans-serif;
          background: #ffffff;
          color: #1a1a1a;
          line-height: 1.6;
          height: 100vh;
          display: flex;
          align-items: center;
          justify-content: center;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }

        .container {
          text-align: center;
          padding: 0 20px;
          max-width: 320px;
          width: 100%;
        }

        .icon {
          width: 48px;
          height: 48px;
          margin: 0 auto 24px;
          position: relative;
        }

        .success-icon {
          width: 48px;
          height: 48px;
          stroke-width: 2;
          stroke: #4CAF50;
          fill: none;
          animation: scaleIn 0.3s ease-out;
        }

        .cancel-icon {
          width: 48px;
          height: 48px;
          stroke-width: 2;
          stroke: #dc2626;
          fill: none;
          animation: scaleIn 0.3s ease-out;
        }

        .success-circle, .cancel-circle {
          stroke-dasharray: 166;
          stroke-dashoffset: 166;
          animation: strokeIn 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
        }

        .success-check {
          stroke-dasharray: 48;
          stroke-dashoffset: 48;
          animation: strokeIn 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.3s forwards;
        }

        .cancel-x {
          stroke-dasharray: 48;
          stroke-dashoffset: 48;
          animation: strokeIn 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.3s forwards;
        }

        h2 {
          font-size: 20px;
          font-weight: 500;
          margin-bottom: 8px;
          letter-spacing: -0.02em;
        }

        p {
          font-size: 14px;
          color: #6b6b6b;
          font-weight: 400;
        }

        .error-text {
          color: #dc2626;
        }

        @keyframes scaleIn {
          from { transform: scale(0.8); opacity: 0; }
          to { transform: scale(1); opacity: 1; }
        }

        @keyframes strokeIn {
          to { stroke-dashoffset: 0; }
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(10px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
          animation: fadeIn 0.5s ease-out 0.2s both;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div id="icon" class="icon">
          ${isSuccess ?
            "<svg class=\"success-icon\" viewBox=\"0 0 52 52\"><circle class=\"success-circle\" cx=\"26\" cy=\"26\" r=\"25\" fill=\"none\"/><path class=\"success-check\" fill=\"none\" d=\"M14.1 27.2l7.1 7.2 16.7-16.8\"/></svg>" :
            "<svg class=\"cancel-icon\" viewBox=\"0 0 52 52\"><circle class=\"cancel-circle\" cx=\"26\" cy=\"26\" r=\"25\" fill=\"none\"/><path class=\"cancel-x\" fill=\"none\" d=\"M16 16 L36 36 M36 16 L16 36\"/></svg>"
          }
        </div>
        <h2 class="fade-in">${title}</h2>
        <p class="fade-in ${!isSuccess ? "error-text" : ""}">${message}</p>
      </div>
      <script>
        console.log('Stripe payment result page loaded');

        function sendMessageAndClose(data, delay = 1000) {
          if (window.opener && !window.opener.closed) {
            console.log('Sending PostMessage to parent window:', data);
            window.opener.postMessage(data, '*');

            setTimeout(() => {
              console.log('Closing popup window');
              window.close();
            }, delay);
          } else {
            console.log('No parent window found, closing directly');
            setTimeout(() => window.close(), delay);
          }
        }

        // Process payment result and send PostMessage
        setTimeout(() => {
          const isSuccess = ${isSuccess};
          const messageData = {
            type: 'stripe-payment-complete',
            success: isSuccess,
            sessionId: '${sessionId || ""}',
            cancelled: ${!isSuccess}
          };

          console.log('Sending payment result via PostMessage:', messageData);
          sendMessageAndClose(messageData, isSuccess ? 1000 : 2000);
        }, 300);
      </script>
    </body>
    </html>`;

    // Set headers (same as calendar auth)
    res.set("Content-Type", "text/html");
    res.set("Cache-Control", "no-cache, no-store, must-revalidate");
    res.set("Pragma", "no-cache");
    res.set("Expires", "0");
    res.status(200).send(html);
  }
);

/**
 * Gets the current Stripe subscription status for a user
 * Returns full subscription details including status, dates, and product info
 */
export const getStripeSubscriptionStatus = functions.https.onCall(
  async (request) => {
    try {
      const email = request.data.email;
      if (!email) {
        throw new functions.https.HttpsError(
          INVALID_ARGUMENT,
          "Email is required"
        );
      }

      const stripe = await getStripeClient();
      const existingCustomers = await stripe.customers.list({ email: email });
      
      if (existingCustomers.data.length === 0) {
        // No customer found - return free status
        return {
          status: "none",
          entitlement: "free",
          priceId: null,
          id: null,
          created: null,
          current_period_start: null,
          current_period_end: null,
          canceled_at: null
        };
      }

      const stripeCustomer = existingCustomers.data[0];
      const subscriptions = await stripe.subscriptions.list({ 
        customer: stripeCustomer.id,
        limit: 1,
        status: "all"
      });

      if (subscriptions.data.length === 0) {
        // Customer exists but no subscriptions
        return {
          status: "none",
          entitlement: "free",
          priceId: null,
          id: null,
          created: null,
          current_period_start: null,
          current_period_end: null,
          canceled_at: null
        };
      }

      const subscription = subscriptions.data[0];
      const priceObj = subscription.items.data[0]?.price;
      const priceId = priceObj?.id;
      const billingInterval = priceObj?.recurring?.interval;
      
      return {
        status: subscription.status,
        entitlement: await getUserEntitlement(priceId || ""),
        priceId: priceId,
        billingInterval: billingInterval,
        id: subscription.id,
        created: subscription.created,
        current_period_start: subscription.current_period_start,
        current_period_end: subscription.current_period_end,
        canceled_at: subscription.canceled_at
      };
    } catch (error) {
      console.error("Error getting Stripe subscription status:", error);
      throw new functions.https.HttpsError(
        "internal",
        "Error getting subscription status"
      );
    }
  }
);