/* eslint-disable require-jsdoc */
import * as functions from "firebase-functions/v2";
import * as admin from "firebase-admin";
import { sendSubscriptionExpiringReminderMail } from "./components/subscription_expiring";
import {
  categorizeUsersIntoSegments,
  deleteUserDataAfterSubscriptionGracePeriod,
} from "./components/delete_user_data";
import {
  pushDataNotification,
  sendPushNotificationToIOS,
} from "./components/push_data_notification";
import { removeOldDeletedUserData } from "./components/trash";
import {
  getAppLink,
  getSupportLink,
  getViewSettingsByUserId,
} from "../../../utils/utility_methods";
import { IUser } from "../../migration/versions/base/model_mappings";
import { sendAutoRenewalMail } from "./components/auto_renew_subscription";
import { sendNotSubscribedReminderMail } from "./components/not_subscribed";
import { sendSingleEmailViaSes } from "../../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../../utils/emails/email_helper";
import { getCalendarsToResub } from "../../calendar/components/calendar_api_handler";

// exports.testFunction = functions.https.onRequest(
//   { enforceAppCheck: false },
//   async (req, res) => {
//     console.log("sending push notifications to users");
//     // read data from request object
//     await deleteUserDataAfterSubscriptionGracePeriod();
//     res.send("success");
//   }
// );

exports.testGracePreiodDeletion = functions.https.onRequest(
  {
    // Check Function: Grace period user data deletion test function for internal use.
    enforceAppCheck: false,
    timeoutSeconds: 540,
    memory: "2GiB",
    concurrency: 1,
  },
  async (req, res) => {
    try {
      // Extract userIds from query parameters or request body
      const userIds = req.query.userIds as string | undefined || req.body?.userIds;
      
      let additionalUserIds: string[] | undefined;
      
      if (userIds) {
        // Parse userIds - can be comma-separated string or array
        additionalUserIds = Array.isArray(userIds) ? userIds : userIds.split(",").map((id: string) => id.trim());
        if (additionalUserIds) {
          console.log(`Testing grace period deletion with additional userIds: ${additionalUserIds.join(", ")}`);
        }
      } else {
        console.log("Testing after grace period deletion (normal flow)");
      }
      
      await deleteUserDataAfterSubscriptionGracePeriod(additionalUserIds);
      
      res.send("success");
    } catch (error) {
      console.error("Error in testGracePreiodDeletion:", error);
      res.status(500).send("Internal Server Error");
    }
  }
);

exports.testPushDataNotificationEvery30Minute = functions.https.onRequest(
  {
    // Check Function: Push data notification every 30 minutes test function for internal use.
    enforceAppCheck: false,
  },
  async (req, res) => {
    console.log("sending push notifications to users");
    // read data from request object
    await pushDataNotification();
    res.send("success");
  }
);

exports.testPushDataNotificationIOS = functions.https.onRequest(
  {
    // Check Function: Push data notification to ios test function for internal use.
    enforceAppCheck: false,
  },
  async (req, res) => {
    console.log("sending push notifications to ios");
    // read data from request object
    await sendPushNotificationToIOS();
    res.send("success");
  }
);

exports.testAutoRenewalMail = functions.https.onRequest(
  {
    // Check Function: Auto renewal mail test function for internal use.
    enforceAppCheck: false,
    timeoutSeconds: 540,
    memory: "512MiB",
  },
  async (req, res) => {
    console.log("Testing auto renewal mail");
    // read data from request object
    await sendAutoRenewalMail();
    res.send("success");
  }
);

exports.testSubscriptionExpiringMail = functions.https.onRequest(
  {
    // Check Function: Subscription expiring mail test function for internal use.
    enforceAppCheck: false,
    timeoutSeconds: 540,
    memory: "512MiB",
  },
  async (req, res) => {
    console.log("Testing susbcription expired mail");
    // read data from request object
    await sendSubscriptionExpiringReminderMail();
    res.send("success");
  }
);

exports.testsendNotSubscribedReminderMail = functions.https.onRequest(
  {
    // Check Function: Not subscribed reminder mail test function for internal use.
    enforceAppCheck: false,
    timeoutSeconds: 540,
    memory: "512MiB",
  },
  async (req, res) => {
    console.log("Testing not susbcription reminder mail");
    // read data from request object
    await sendNotSubscribedReminderMail();
    res.send("success");
  }
);

// Pushes data notification every 20 minutes
export const pushDataNotificationEvery30Minute = functions.scheduler.onSchedule(
  "*/20 * * * *",
  async (_event) => {
    return pushDataNotification();
  }
);

// // Deletes user data after 30 days
// export const deleteDataAfterUserDeletion = functions.scheduler.onSchedule(
//   "0 0 * * *",
//   async (_event) => {
//     return deleteUserDataAfterDeletionGracePeriod();
//   }
// );
// This function will categorize users into segments based certain creiteria.
// For single email do: https://europe-west1-mevolve-dev.cloudfunctions.net/updateUsersSegments?email=<EMAIL>
// For all user do: https://europe-west1-mevolve-dev.cloudfunctions.net/updateUsersSegments
exports.updateUsersSegments = functions.https.onRequest(
  {
    enforceAppCheck: false,
    timeoutSeconds: 540,
    concurrency: 1,
  },
  async (req, res) => {
    try {
      // Extract email from query parameters
      const email = req.query.email as string | undefined;

      console.log(
        email
          ? `Categorizing user with email: ${email}`
          : "Categorizing all users into segments in batches"
      );

      // Call the categorization function with optional email
      await categorizeUsersIntoSegments(email);

      res.status(200).send("success");
    } catch (error) {
      console.error("Error during user segmentation:", error);
      res.status(500).send("Internal Server Error");
    }
  }
);

// Deletes user data after 30 days
export const deleteDataAfterSubscriptionNotRenewed =
  functions.scheduler.onSchedule(
    {
      schedule: "0 0 * * *",
      timeoutSeconds: 540,
      memory: "2GiB",
      concurrency: 1,
    },
    async (_event) => {
      return deleteUserDataAfterSubscriptionGracePeriod();
    }
  );

// Send welcome email upon new user creation
exports.welcomeUser = functions.firestore.onDocumentCreated(
  "users/{userId}",
  async (event) => {
    if (event != undefined) {
      const userData = event.data?.data() as IUser;
      const authUser = await admin.auth().getUser(userData.uid);
      if (
        authUser.email &&
        authUser.displayName != null &&
        authUser.displayName != ""
      ) {
        const viewSettings = await getViewSettingsByUserId(userData.uid);
        const language = viewSettings?.appSettings.language;
        await sendSingleEmailViaSes({
          templateName: MevolveEmails.welcomeUser,
          recipient: authUser.email,
          templateData: {
            name: authUser.displayName ?? "",
            appLink: getAppLink(),
            linkToSupportChat: getSupportLink(),
          },
          language: language,
        });
      }
    } else {
      console.log("No event data");
    }
  }
);

// Send user subscription expiring mail before 5 days
export const subscriptionExpiring = functions.scheduler.onSchedule(
  "0 0 * * *",
  async (_event) => {
    return sendSubscriptionExpiringReminderMail();
  }
);

// Send user subscription expiring mail before 5 days
export const autoRenewalAlert = functions.scheduler.onSchedule(
  "0 0 * * *",
  async (_event) => {
    return sendAutoRenewalMail();
  }
);

// Send user trial expired mail
export const notSubscribedAfterTrial = functions.scheduler.onSchedule(
  "0 0 * * *",
  async (_event) => {
    return sendNotSubscribedReminderMail();
  }
);

// Remove old deleted documents permanently
export const removeOldDeletedDocs = functions.scheduler.onSchedule(
  "0 0 * * *",
  async (_event) => {
    return removeOldDeletedUserData();
  }
);

// Resub Calendar Webhooks
export const resubCalendarWebhooksCron = functions.scheduler.onSchedule(
  "30 0 * * *",
  async (_event) => {
    return getCalendarsToResub();
  }
);