import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import {
  capitalizeFirstLetter,
  getEmailNameByIdFromAuthService,
  getSupportLink,
  getViewSettingsByUserId,
} from "../../../../utils/utility_methods";
import { IUser } from "../../../migration/versions/base/model_mappings";
import { EmailInfo, sendBulkEmail } from "../../../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../../../utils/emails/email_helper";
import { Timestamp } from "firebase-admin/firestore";
import moment from "moment";

const emailLimit = 200;

export async function sendAutoRenewalMail() {
  const daysLeft = 5;
  const todaysDate = new Date();
  const startDate = new Date(
    todaysDate.getFullYear(),
    todaysDate.getMonth(),
    todaysDate.getDate() + daysLeft,
    0,
    0,
    0
  );
  const endDate = new Date(
    todaysDate.getFullYear(),
    todaysDate.getMonth(),
    todaysDate.getDate() + daysLeft,
    23,
    59,
    29
  );
  console.log(`start date = ${startDate}`);
  const usersToBeDeletedQuery = await admin
    .firestore()
    .collection("users")
    .where("subscriptionInfo.subscriptionState", "==", "subscribed")
    .where("subscriptionInfo.unsubscribedAt", "==", null)
    .where("subscriptionInfo.subscriptionExpDate", ">=", startDate)
    .where("subscriptionInfo.subscriptionExpDate", "<", endDate)
    .limit(emailLimit);

  let dataSnapshot = await usersToBeDeletedQuery.get();
  console.log(`length = ${dataSnapshot.docs.length}`);
  while (dataSnapshot.docs.length > 0) {
    const tasks: Promise<EmailInfo | undefined>[] = [];
    for (const document of dataSnapshot.docs) {
      const userData = document.data() as IUser;
      console.log(userData.userInfo.email);
      tasks.push(getUserDetails(userData, daysLeft));
    }
    const emailList = await Promise.all(tasks);
    await sendBulkEmail(
      MevolveEmails.autoRenewalAlert,
      emailList.filter((emailInfo) => emailInfo != undefined) as EmailInfo[]
    );
    const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
    dataSnapshot = await usersToBeDeletedQuery.startAfter(last).get();
    console.log("next length of snapshot = " + dataSnapshot.docs.length);
  }
}

async function getUserDetails(
  userData: IUser,
  daysToExpire: number
): Promise<EmailInfo | undefined> {
  const result = await Promise.all([
    getEmailNameByIdFromAuthService(userData.uid),
    getViewSettingsByUserId(userData.uid)
  ])
  const userDetails = result[0];
  if (userDetails?.email == undefined) {
    functions.logger.error("email not found");
    return undefined;
  }
  const viewSettings = result[1];
  const language = viewSettings?.appSettings.language;
  const timeZone = viewSettings?.notificationSettings.emailNotificationTimezone;
  const expDate = moment((userData.subscriptionInfo.subscriptionExpDate as unknown as Timestamp).toMillis()).utcOffset(timeZone ?? "").format("Do MMM YYYY");
  return {
    to: userDetails.email,
    language: language,
    data: {
      name: userDetails.name,
      daysToExpire: daysToExpire,
      subsType: `${capitalizeFirstLetter(userData.subscriptionInfo.entitlement ?? "")} ${capitalizeFirstLetter(userData.subscriptionInfo.subscriptionType ?? "")}`,
      expiryDate: expDate, // "19th March, 2023",
      linkToSupportChat: getSupportLink(),
    },
  };
}
