import * as admin from "firebase-admin";
import { getMeEnvironment } from "../../../../me_config";

export async function pushDataNotification(fcmToken = null) {
  const env = getMeEnvironment();

  console.log("sending push notifications to users");

  const topic = "data_push_notification_topic";

  // Create the base message without destination
  const baseMessage = {
    data: {
      custom_task: "data_sync",
    },
    apns: {
      headers: {
        "apns-priority": "5",
        "apns-push-type": "background",
        "apns-topic": `app.mevolve.daily.mobile.${env}`,
      },
      payload: {
        aps: {
          "content-available": 1,
        },
      },
    },
  };

  // Create the properly typed message with destination
  let message: admin.messaging.Message;

  if (fcmToken) {
    message = {
      ...baseMessage,
      token: fcmToken,
    };
  } else {
    message = {
      ...baseMessage,
      topic: topic,
    };
  }

  // Response is a message ID string.
  console.log("Scheduled message:", message);

  // Send a message to devices subscribed to the provided topic.
  await admin
    .messaging()
    .send(message)
    .then((response) => {
      // Response is a message ID string.
      console.log("Successfully sent message:", response);
    })
    .catch((error) => {
      console.log("Error sending message:", error);
    });
}

export async function sendPushNotificationToIOS(fcmToken = null) {
  console.log("sending push notifications to users");

  const topic = "data_push_notification_topic_test";

  // Create the base message without destination
  const baseMessage = {
    notification: {
      title: "up 1.43% on the day",
      body: "gained 11.80 points to close at 835.67, up 1.43% on the day.",
    },
  };

  // Create the properly typed message with destination
  let message: admin.messaging.Message;

  if (fcmToken) {
    message = {
      ...baseMessage,
      token: fcmToken,
    };
  } else {
    message = {
      ...baseMessage,
      topic: topic,
    };
  }

  // Response is a message ID string.
  console.log("Scheduled message:", message);

  // Send a message to devices subscribed to the provided topic.
  await admin
    .messaging()
    .send(message)
    .then((response) => {
      // Response is a message ID string.
      console.log("Successfully sent message:", response);
    })
    .catch((error) => {
      console.log("Error sending message:", error);
    });
}
