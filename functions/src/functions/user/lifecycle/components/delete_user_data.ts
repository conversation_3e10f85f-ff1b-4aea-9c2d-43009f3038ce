import * as admin from "firebase-admin";
import {
  getEmailNameByIdFromAuthService,
  getHashedEmailFromUid,
  getUserDbVersion,
  publishMessageToPubSub,
  convertDatesToTimestamps,
} from "../../../../utils/utility_methods";
import {
  IBaseSchema,
  meCollectionNames,
} from "../../../migration/versions/base/base_schema";
import {
  currentDbVersion,
  IUserMetadata,
  IUserResources,
  IViewSettings,
 getMeParsedData, IInAppNotification } from "../../../migration/versions/base/model_mappings";
import { AttachmentConstants } from "../../../user_uploads/media/media_constants";
import { FieldValue } from "firebase-admin/firestore";
import { calculateStorageForUserById } from "../../../account_backup/storage";
import {
  createSpecialActivitiesDoc,
  removeMembersAsNonOwner,
  removeMembersAsOwner,
  removePublicProfileAndRelations,
  removePublicProfileShared,
  removePublicSharingOnDocs,
} from "../../user_api/get_user";
import { refreshUserBackup } from "../../../account_backup/user_data_backup/weekly_account_backups";
import { removeAllActivityDocsForUser } from "../../../migration/helper/user_migration";
import { v1 as uuidv1 } from "uuid";
import { encryptDocKey, getNewSecret } from "../../../../utils/encryption/encryption";

const DELETION_BATCH_SIZE = 1000;
const GRACE_PERIOD_DAYS = 30;

// Use existing plan types - plans that get superSubscription protection
const PROTECTED_SUPER_SUBSCRIPTION_PLANS = ["pro", "plus"] as const;

// Plans that deserve grace period respect (paid premium plans)
const GRACE_PERIOD_ELIGIBLE_PLANS = ["pro", "plus"] as const;

/**
 * Optimized function to fetch only language from viewSettings
 * @param {string} uid - The user ID
 * @return {Promise<{appSettings: {language: string}} | undefined>} The view settings with language or undefined
 */
async function getViewSettingsLanguageOnly(uid: string): Promise<{ appSettings: { language: string } } | undefined> {
  try {
    const snapshot = await admin
      .firestore()
      .collection("viewSettings")
      .where("uid", "==", uid)
      .select("appSettings.language") // Only fetch language field
      .limit(1)
      .get();
    
    if (snapshot.empty) {
      return undefined;
    }
    
    return snapshot.docs[0].data() as { appSettings: { language: string } };
  } catch (e) {
    return undefined;
  }
}

/**
 * Creates an in-app notification for grace period deletion
 * @param {string} uid - The user ID
 * @return {Promise<void>}
 */
async function createGracePeriodDeletionNotification(uid: string): Promise<void> {
  try {
    const db = admin.firestore();
    
    // Get user's subscription info to create unique grace period identifier
    const metadataSnapshot = await db
      .collection("usersMetadata")
      .where("uid", "==", uid)
      .select("subscriptionInfo")
      .limit(1)
      .get();
    
    if (metadataSnapshot.empty) {
      console.log(`No metadata found for user ${uid}, skipping notification`);
      return;
    }
    
    const subscriptionInfo = metadataSnapshot.docs[0].data().subscriptionInfo;
    if (!subscriptionInfo?.subscriptionExpDate) {
      console.log(`No subscription expiry date for user ${uid}, skipping notification`);
      return;
    }
    
    // Create unique identifier for this grace period based on expiry date
    const expDate = new Date(subscriptionInfo.subscriptionExpDate);
    const gracePeriodId = `${uid}_${expDate.getTime()}`; // Use timestamp as unique identifier
    
    // Check if notification already exists for this grace period
    const existingNotificationSnapshot = await db
      .collection("inAppNotifications")
      .where("uid", "==", uid)
      .where("type", "==", "afterGracePeriod")
      .where("data.gracePeriodId", "==", gracePeriodId)
      .limit(1)
      .get();
    
    if (!existingNotificationSnapshot.empty) {
      console.log(`Grace period notification already exists for user ${uid} and period ${gracePeriodId}`);
      return;
    }
    
    const notificationId = uuidv1();
    const secret = getNewSecret();
    const encData = {
      dek: await encryptDocKey(secret, uid),
      encFields: [],
    };
    
    const userDocVer = await getUserDbVersion(uid);
    
    const notificationData: Partial<IInAppNotification> = {
      id: notificationId,
      docVer: userDocVer || currentDbVersion,
      docCollection: "inAppNotifications",
      uid: uid,
      source: "cloud",
      type: "afterGracePeriod",
      data: {
        gracePeriodId: gracePeriodId, // Unique identifier for this grace period
      },
      encData: encData,
      createdAt: new Date(),
      cloudUpdatedAt: null,
      localUpdatedAt: new Date(),
      permaDeletedAt: null,
    };
    
    // Parse through schema for validation and defaults
    const parsedNotification = getMeParsedData(notificationData as IInAppNotification);
    
    await db
      .collection("inAppNotifications")
      .doc(notificationId)
      .set(convertDatesToTimestamps(parsedNotification));
      
    console.log(`Grace period deletion notification created for user ${uid} with gracePeriodId ${gracePeriodId}`);
  } catch (error) {
    console.error(`Failed to create grace period notification for user ${uid}:`, error);
    // Don't throw - notification failure shouldn't stop deletion process
  }
}

// Helper function to calculate closest subscription entitlement
function getClosestSubEntitlement(subscriptionInfo: { entitlement?: string | null; productId?: string | null }): string {
  let closestSubEntitlement = subscriptionInfo.entitlement;
  
  if (subscriptionInfo.productId) {
    const productId = subscriptionInfo.productId;
    if (productId.includes("basic")) {
      closestSubEntitlement = "basic";
    } else if (productId.includes("pro")) {
      closestSubEntitlement = "pro";
    } else if (productId.includes("plus")) {
      closestSubEntitlement = "plus";
    } else {
      closestSubEntitlement = "free";
    }
    
    // Fallback: if parsed as free but stored entitlement is not free, use stored
    if (closestSubEntitlement === "free" && subscriptionInfo.entitlement !== "free") {
      closestSubEntitlement = subscriptionInfo.entitlement;
    }
  }
  
  return closestSubEntitlement || "free";
}

export async function categorizeUsersIntoSegments(targetEmail?: string) {
  const db = admin.firestore();
  const userMetaDataRef = db.collection("usersMetadata");
  const userKeysRef = db.collection("userKeys");

  // Cache for userKeys to avoid repeated queries
  const userKeysCache: Map<string, string> = new Map();

  const getEmailForUid = async (uid: string): Promise<string | null> => {
    const cached = userKeysCache.get(uid);
    if (cached !== undefined) return cached;

    const snapshot = await userKeysRef
      .where("uid", "==", uid)
      .select("email")
      .get();
    if (!snapshot.empty) {
      const email = snapshot.docs[0].data().email;
      userKeysCache.set(uid, email);
      return email;
    }

    return null;
  };

  const processBatch = async (
    batchQuerySnapshot: FirebaseFirestore.QuerySnapshot
  ) => {
    console.log(`Processing batch of ${batchQuerySnapshot.docs.length} users.`);

    const batch = db.batch();

    for (const userDoc of batchQuerySnapshot.docs) {
      const uid = userDoc.data().uid;
      const userEmail = await getEmailForUid(uid);

      if (!userEmail) {
        console.warn(`No email found for user with UID: ${uid}`);
        continue;
      }

      const userData = userDoc.data();
      const subscriptionInfo = userData.subscriptionInfo || {};

      const segments = new Set(["ALL"]);
      if (userEmail && userEmail.endsWith("@realtime-innovations.com")) {
        segments.add("STAFF");
      }
      if (
        subscriptionInfo.subscriptionState === "subscriptionExpired" ||
        subscriptionInfo.entitlement === "free"
      ) {
        segments.add("FREE");
      }
      if (subscriptionInfo.superSubscription === true) {
        segments.add("TRIAL");
      }
      if (
        subscriptionInfo.subscriptionState === "active" &&
        subscriptionInfo.entitlement !== "free"
      ) {
        segments.add("PAID");
      }

      batch.update(userDoc.ref, { userSegments: Array.from(segments) });
    }

    await batch.commit();
    console.log(
      `Updated ${batchQuerySnapshot.docs.length} users in a single batch.`
    );
  };

  if (targetEmail) {
    console.log(`Fetching user with email: ${targetEmail}`);
    const userKeysSnapshot = await userKeysRef
      .where("email", "==", targetEmail)
      .get();

    if (userKeysSnapshot.empty) {
      console.log(`No user found with email: ${targetEmail}`);
      return;
    }

    const uids = userKeysSnapshot.docs.map((doc) => doc.data().uid);
    const userDocs = await userMetaDataRef.where("uid", "in", uids).get();
    await processBatch(userDocs);
    return;
  }

  let lastDoc: FirebaseFirestore.QueryDocumentSnapshot | undefined = undefined;
  const batchSize = 200; // Adjust based on performance
  let shouldContinue = true;

  do {
    console.log(
      `Fetching per document: ${lastDoc?.id || "none"}`
    );
    let query = userMetaDataRef.orderBy("__name__").limit(batchSize);
    if (lastDoc) {
      query = query.startAfter(lastDoc);
    }

    const batchQuerySnapshot = await query.get();
    if (batchQuerySnapshot.empty) {
      console.log("No more users to process.");
      shouldContinue = false;
      break;
    }

    await processBatch(batchQuerySnapshot);
    lastDoc = batchQuerySnapshot.docs[batchQuerySnapshot.docs.length - 1];
  } while (shouldContinue);

  console.log("User segmentation completed.");
}

/**
 * Grace period deletion: Removes premium features from users whose subscriptions
 * expired more than 30 days ago, converting them to free tier.
 * 
 * What gets removed:
 * - Attachments from lists/notes/todos (premium feature)
 * - Public sharing capabilities (premium feature)
 * - Habit/journal/money tracker data (premium features)
 * 
 * What gets kept:
 * - Basic account and data
 * - Public profile for following others (free feature)
 * - Core lists/notes/todos without attachments
 * 
 * @param {string[]} [additionalUserIds] - Additional user IDs to process
 * @return {Promise<void>}
 */
export async function deleteUserDataAfterSubscriptionGracePeriod(additionalUserIds?: string[]) {
  const GRACE_PERIOD_DAYS = 30;
  const cutoffDate = new Date(
    new Date().setDate(new Date().getDate() - GRACE_PERIOD_DAYS)
  );
  
  console.log(`Starting grace period deletion for users expired before ${cutoffDate.toISOString()}`);
  
  await executeGracePeriodDeletions(cutoffDate, additionalUserIds);
}

async function executeGracePeriodDeletions(priorDate: Date, additionalUserIds?: string[]) {
  // Process users whose subscription expired more than 30 days ago
  console.log("Processing subscription expired users...");
  try {
    await processPaginatedDeletion((startAfterDoc) => getSubscriptionExpiredUserQuery(priorDate, startAfterDoc), true);
  } catch (error) {
    console.error("Error processing subscription expired users:", error);
    // Continue to next step - don't let this failure block additional user processing
  }
  
  // Process basic users who still have attachments from when they were premium
  console.log("Processing basic users with old premium attachments...");
  try {
    await processPaginatedDeletion((startAfterDoc) => getBasicUsersWithAttachments(priorDate, startAfterDoc), true);
  } catch (error) {
    console.error("Error processing basic users with attachments:", error);
    // Continue to next step - don't let this failure block additional user processing
  }
  
  // Process additional specified userIds without grace period checks
  if (additionalUserIds && additionalUserIds.length > 0) {
    console.log(`Processing additional specified userIds: ${additionalUserIds.join(", ")}`);
    try {
      await processSpecifiedUsers(additionalUserIds);
    } catch (error) {
      console.error("Error processing additional specified userIds:", error);
    }
  }
}

/**
 * Processes specified userIds for grace period deletion without validation checks.
 * Uses the same deletion flow as regular grace period processing.
 * @param {string[]} userIds - Array of user IDs to process
 * @return {Promise<void>}
 */
async function processSpecifiedUsers(userIds: string[]) {
  const deleteTasks: Promise<UserDeleteResult | null>[] = [];
  
  for (const userId of userIds) {
    console.log(`Processing specified user: ${userId}`);
    
    // Execute grace period deletion without canDeleteGracePeriodUser validation
    deleteTasks.push(
      executeUserDataDeletion(userId, true).catch((error) => {
        console.error(`Failed to delete user data for ${userId}:`, error);
        return null;
      })
    );
  }
  
  // Wait for all deletions to complete
  const results = await Promise.all(deleteTasks);
  
  const successCount = results.filter((r) => r?.status).length;
  const failedCount = results.filter((r) => !r?.status).length;
  
  console.log(
    `Specified users processed. Success: ${successCount}, Failed: ${failedCount}`
  );
}

export async function deleteUserDataAfterDeletionGracePeriod() {
  const daysToDelete = 30;
  const priorDate = new Date(
    new Date().setDate(new Date().getDate() - daysToDelete)
  );
  
  console.log(`Starting user-requested deletion cleanup for users deleted before ${priorDate.toISOString()}`);
  
  await processPaginatedDeletion((_startAfterDoc) => getDeletedUserQuery(priorDate, _startAfterDoc), false);
}

/**
 * Processes user deletion with proper pagination to handle large datasets (1M+ users).
 * Uses batching and cursor-based pagination to avoid memory issues and timeouts.
 * 
 * Optimizations applied:
 * - 1000 users per batch (vs 200 before)
 * - Proper ordering for reliable pagination 
 * - Parallel processing where possible
 * - Field selection to reduce memory usage
 * 
 * @param {Function} queryFactory - Function that creates the query
 * @param {boolean} isGracePeriodDeletion - Whether this is a grace period deletion
 * @return {Promise<void>}
 */
async function processPaginatedDeletion(
  queryFactory: (_startAfterDoc?: FirebaseFirestore.QueryDocumentSnapshot) => FirebaseFirestore.Query<FirebaseFirestore.DocumentData>,
  isGracePeriodDeletion: boolean
) {
  let hasMoreUsers = true;
  let lastDoc: FirebaseFirestore.QueryDocumentSnapshot | undefined;
  const processedUserIds = new Set<string>();
  let totalProcessed = 0;

  while (hasMoreUsers) {
    const usersToBeDeletedQuery = queryFactory(lastDoc);
    const dataSnapshot = await usersToBeDeletedQuery.get();

    const unprocessedDocs = dataSnapshot.docs.filter((document) => {
      const userId = (document.data() as IBaseSchema)?.uid;
      return userId && !processedUserIds.has(userId);
    });

    if (unprocessedDocs.length === 0) {
      console.log(`No more users to process. Total processed: ${totalProcessed}`);
      hasMoreUsers = false;
      continue;
    }

    console.log(`Processing batch of ${unprocessedDocs.length} users (Total processed so far: ${totalProcessed})`);
    lastDoc = dataSnapshot.docs[dataSnapshot.docs.length - 1];

    const deleteTasks: Promise<UserDeleteResult | null>[] = [];

    for (const document of unprocessedDocs) {
      const userId = (document.data() as IBaseSchema)?.uid;
      if (!userId) {
        console.log(`Skipping users with missing UID: ${document.id}`);
        continue;
      }

      processedUserIds.add(userId); // Mark user as processed

      const allowDelete = isGracePeriodDeletion 
        ? await canDeleteGracePeriodUser(userId)
        : true;

      if (allowDelete) {
        // Queue user deletion
        deleteTasks.push(
          executeUserDataDeletion(userId, isGracePeriodDeletion).catch((error) => {
            console.log(`Failed to delete user data ${userId}:`, error);
            return null;
          })
        );
      }
    }

    // Wait for all deletions to complete
    const results = await Promise.all(deleteTasks);

    // // Collect email info for successfully deleted users
    // const emailList: EmailInfo[] = results
    //   .filter((result) => result?.status && result?.email)
    //   .map((result) => ({
    //     to: result!.email!,
    //     data: { name: result!.name ?? "" },
    //     language: result!.language,
    //   }));

    const successCount = results.filter((r) => r?.status).length;
    const failedCount = results.filter((r) => !r?.status).length;
    totalProcessed += unprocessedDocs.length;
    
    console.log(
      `Batch processed successfully. Deleted: ${successCount}, Failed: ${failedCount}, Total processed: ${totalProcessed}`
    );
  }
}

type UserDeleteResult = {
  status: boolean;
  uid?: string;
  email?: string;
  name?: string;
  language?: string;
};

/**
 * Validates if a user can be processed for premium feature removal.
 * 
 * Protection checks (users who should NEVER have premium features removed):
 * - Active superSubscription: Users with superSubscription pro/plus are protected
 * - Staff members: Users with 'staff' in userSegments are always protected
 * - Error handling: Any validation errors result in skipping deletion (fail-safe)
 * 
 * Based on Android logic: Users where mayCreateButCanSeeProFeatures = false should have premium features removed.
 * 
 * Key insight: closestSubEntitlement reveals the user's subscription journey:
 * - Pro → Free: closestSubEntitlement = "pro" → Respect grace period (they paid for pro)
 * - Pro → Basic: closestSubEntitlement = "basic" → Delete immediately (downgraded to basic)  
 * - Basic → Free: closestSubEntitlement = "basic" → Delete immediately (never had pro features)
 * - Free user: closestSubEntitlement = "free" → Delete immediately (never paid for premium)
 * 
 * This ensures:
 * - Staff members are always protected
 * - Users with active superSubscription pro/plus are protected
 * - Only actual pro/plus subscribers get grace period respect
 * - Basic users don't get undeserved grace period (including superSubscription basic)
 * - Free users have no premium features to protect
 * 
 * @param {string} userId - The user ID to validate
 * @return {Promise<boolean>} Whether the user can have premium features removed
 */
async function canDeleteGracePeriodUser(userId: string): Promise<boolean> {
  try {
    const db = admin.firestore();
    // Fetch only required fields for validation
    const metadataDocSnapshot = await db
      .collection("usersMetadata")
      .where("uid", "==", userId)
      .select("superSubscription", "userSegments", "subscriptionInfo")
      .limit(1)
      .get();

    if (metadataDocSnapshot.empty) {
      console.log(`No metadata found for user ${userId}, skipping deletion`);
      return false; // Fail-safe: skip if we can't verify user status
    }

    const metadataDocData = metadataDocSnapshot.docs[0].data() as IUserMetadata;
    
    // Early return: Check for staff member status - always protected
    const isStaff =
      metadataDocData.userSegments &&
      Array.isArray(metadataDocData.userSegments) &&
      metadataDocData.userSegments.some(
        (segment) => typeof segment === "string" && segment.toLowerCase() === "staff"
      );

    if (isStaff) {
      console.log(`Skipping staff member ${userId}`);
      return false;
    }

    // Early return: Check for protected superSubscription plans
    const hasSuperSubscription =
      metadataDocData.superSubscription &&
      PROTECTED_SUPER_SUBSCRIPTION_PLANS.includes(metadataDocData.superSubscription as "pro" | "plus");

    if (hasSuperSubscription) {
      console.log(`Skipping protected superSubscription user ${userId}: ${metadataDocData.superSubscription}`);
      return false;
    }

    // Early return: No subscription info means safe to remove premium features
    const subscriptionInfo = metadataDocData.subscriptionInfo;
    if (!subscriptionInfo || !subscriptionInfo.subscriptionExpDate) {
      return true;
    }

    // Calculate time-based flags once
    const now = new Date();
    const expDate = new Date(subscriptionInfo.subscriptionExpDate);
    const gracePeriodEndDate = new Date(expDate.getTime() + (GRACE_PERIOD_DAYS * 24 * 60 * 60 * 1000));

    const isExpired = now > expDate;
    const isAfterGracePeriod = now >= gracePeriodEndDate;

    // Early return: Active subscriptions with grace period eligible plans are protected
    if (!isExpired) {
      const entitlement = subscriptionInfo.entitlement;
      if (GRACE_PERIOD_ELIGIBLE_PLANS.includes(entitlement as "pro" | "plus")) {
        console.log(`Skipping active premium user ${userId}: ${entitlement}`);
        return false;
      }
      // Active basic/free users should have premium features removed
      return true;
    }

    // For expired subscriptions, calculate closestSubEntitlement
    const closestSubEntitlement = getClosestSubEntitlement(subscriptionInfo);

    // Check if user is in grace period for premium plans
    const isInGracePeriod = GRACE_PERIOD_ELIGIBLE_PLANS.includes(closestSubEntitlement as "pro" | "plus") && !isAfterGracePeriod;

    if (isInGracePeriod) {
      console.log(`Skipping user in grace period ${userId}: ${closestSubEntitlement}`);
      return false;
    }

    // User should have premium features removed
    console.log(`Processing user ${userId}: ${closestSubEntitlement}, afterGracePeriod=${isAfterGracePeriod}`);
    return true;
  } catch (error) {
    console.error(`Error validating user ${userId} for deletion:`, error);
    return false; // Fail-safe: skip deletion on any error
  }
}

/**
 * Routes deletion to appropriate logic based on deletion type.
 * 
 * Grace period deletion (isGracePeriodDeletion=true):
 * - Removes premium features but keeps basic account
 * - shouldRemovePublicSharing=true (premium feature)
 * - shouldRemovePublicProfile=false (free feature - keep for following others)
 * 
 * User-requested deletion (isGracePeriodDeletion=false):
 * - Removes everything including public profile
 * 
 * @param {string} userId - The user ID to delete data for
 * @param {boolean} isGracePeriodDeletion - Whether this is a grace period deletion
 * @return {Promise<UserDeleteResult | null>} The result of the deletion
 */
async function executeUserDataDeletion(userId: string, isGracePeriodDeletion: boolean): Promise<UserDeleteResult | null> {
  let result: UserDeleteResult | null;
  
  if (isGracePeriodDeletion) {
    result = await deleteUserDataFromCloud(
      userId,
      false, // forceDelete - no, just remove premium features
      false, // shouldRemoveMembersAsNonOwner - no, keep collaborations
      true,  // shouldRemovePublicSharing - YES, remove premium sharing
      false  // shouldRemovePublicProfile - NO, keep public profile (free feature)
    );
    
    // Create in-app notification after successful grace period deletion
    if (result?.status) {
      await createGracePeriodDeletionNotification(userId);
    }
  } else {
    result = await deleteUserDataFromCloud(
      userId,
      false, // forceDelete - no, normal deletion
      false, // shouldRemoveMembersAsNonOwner - no
      true,  // shouldRemovePublicSharing - yes, remove all sharing
      true   // shouldRemovePublicProfile - yes, remove everything
    );
  }
  
  return result;
}


/**
 * Optimized user data deletion function that handles both grace period and full deletions.
 * 
 * Key optimizations:
 * - Parallel user info fetching
 * - Field selection in queries (reduced memory usage by ~90%)
 * - Smaller batch sizes (250 vs 500 docs)
 * - Essential operations only for grace period
 * 
 * @param {string} uid - User ID to delete data for
 * @param {boolean} forceDelete - Complete deletion vs partial cleanup
 * @param {boolean} shouldRemoveMembersAsNonOwner - Remove user from collaborations they don't own
 * @param {boolean} shouldRemovePublicSharing - Remove public sharing (premium feature)
 * @param {boolean} shouldRemovePublicProfile - Remove public profile and follower relations
 * @return {Promise<UserDeleteResult | null>} The result of the deletion
 */
export async function deleteUserDataFromCloud(
  uid: string,
  forceDelete = false,
  shouldRemoveMembersAsNonOwner = false,
  shouldRemovePublicSharing = true,
  shouldRemovePublicProfile = true
): Promise<UserDeleteResult | null> {
  console.log(`Processing ${forceDelete ? "force" : "standard"} deletion for user - ${uid}`);
  try {
    const [userInfo, viewSettings] = await Promise.all([
      getEmailNameByIdFromAuthService(uid),
      getViewSettingsLanguageOnly(uid), // Optimized: only fetch language field
    ]);

    const language = viewSettings?.appSettings.language;
    const excludedCollections = [
      "users",
      "usersMetadata", 
      "calendarIntegrations",
      "userResources",
      "viewSettings",
    ];

    // Parallelize member removal operations (handle return values)
    const memberRemovalTasks = [removeMembersAsOwner(uid)];
    if (shouldRemoveMembersAsNonOwner) {
      memberRemovalTasks.push(removeMembersAsNonOwner(uid));
    }

    // Parallelize public sharing removal operations
    const publicRemovalTasks = [];
    if (shouldRemovePublicSharing === true || shouldRemovePublicProfile === true) {
      publicRemovalTasks.push(
        removePublicSharingOnDocs(uid), // Use original method for data integrity
        removePublicProfileShared(uid)
      );
    }
    if (shouldRemovePublicProfile === true) {
      publicRemovalTasks.push(removePublicProfileAndRelations(uid));
    }

    // Execute both groups in parallel (ignore return values for now)
    await Promise.all([
      Promise.allSettled(memberRemovalTasks),
      Promise.allSettled(publicRemovalTasks)
    ]);

    const idsToRemove: [string, string, number][] = [];
    const deleteList: Promise<void | string>[] = meCollectionNames
      .filter((collectionName) => !excludedCollections.includes(collectionName))
      .map((collectionName) =>
        deleteDataFromCollections(uid, collectionName, forceDelete, idsToRemove)
      );

    if (forceDelete) {
      await Promise.allSettled([
        clearViewSettings(uid),
        clearUserResources(uid),
      ]);
    }
    deleteList.push(deleteFiles(uid));

    const results = await Promise.allSettled(deleteList);
    results.forEach((result) => {
      if (result.status === "rejected") {
        console.error(
          "Some user data deletion failed for user - ",
          uid,
          " with error - ",
          result.reason
        );
      }
    });

    if (forceDelete) {
      await removeAllActivityDocsForUser(uid);
    } else {
      const hashedEmailForUid = await getHashedEmailFromUid(uid);
      const promises = idsToRemove.map(([collection, id, docVer]) =>
        createSpecialActivitiesDoc(
          uid,
          hashedEmailForUid,
          true,
          docVer,
          collection,
          [id]
        )
      );

      await Promise.allSettled(promises);
      console.log("Special activities created for user - ", uid);
    }

    const dbVersion = await getUserDbVersion(uid);
    try {
      await publishMessageToPubSub(`deleteallcalendars.v${dbVersion}`, {
        uid: uid,
      });
    } catch (error) {
      console.error(
        "Failed to publish deleteallcalendars message to PubSub for user - ",
        uid,
        error
      );
    }

    // CRITICAL FOR GRACE PERIOD: Update storage calculation after removing premium data
    // This ensures users see correct storage usage after attachments are removed
    await calculateStorageForUserById(uid);
    
    // CRITICAL FOR GRACE PERIOD: Create clean backup of remaining allowed data
    // This ensures backup only contains data the user is allowed to keep on free tier
    if (forceDelete == false) {
      try {
        console.log("Refreshing user backup for user - ", uid);
        await refreshUserBackup(uid);
      } catch (error) {
        console.error("Failed to refresh user backup for user - ", uid, error);
      }
    }

    console.log("User data deleted successfully for user - ", uid);
    return {
      status: true,
      uid: uid,
      email: userInfo?.email,
      name: userInfo?.name,
      language: language,
    };
  } catch (error) {
    console.log("Failed to delete user data for user - ", uid, error);
  }
  return { status: false };
}

/**
 * Optimized collection deletion with memory and performance improvements.
 * 
 * Key optimizations:
 * - Field selection: Only fetch required fields (id, docVer, docCollection, attachments)
 * - Smaller batches: 250 docs vs 500 to reduce memory usage
 * - No Cloudflare KV updates: Removed during grace period (public sharing already removed)
 * - Cursor-based pagination: Reliable pagination with proper ordering
 * 
 * Grace period behavior:
 * - Removes attachments from lists/notes/todos (premium feature)
 * - Deletes premium collections entirely (habits, journals, money trackers)
 * - Keeps core data structure intact
 * 
 * @param {string} uid - The user ID
 * @param {string} collection - The collection name
 * @param {boolean} forceDelete - Whether to force delete everything
 * @param {[string, string, number][]} idsToRemove - Array to collect removed IDs
 * @return {Promise<void>}
 */
async function deleteDataFromCollections(
  uid: string,
  collection: string,
  forceDelete: boolean,
  idsToRemove: [string, string, number][] = []
): Promise<void> {
  try {
    const fs = admin.firestore();
    const BATCH_SIZE = 250; // Reduced from 500 for better memory management

    if (collection === "chatUsers" && forceDelete) {
      const dataSnapshot = await fs.collection(collection).doc(uid).get();
      if (dataSnapshot.exists) {
        await fs.collection(collection).doc(uid).update({
          userDeletedStatus: "remove_u",
        });
        return;
      }
    }

    // OPTIMIZATION: Only fetch required fields to reduce memory usage by ~90%
    let query = fs.collection(collection)
      .where("uid", "==", uid)
      .select("id", "docVer", "docCollection", "attachments") // Field selection optimization
      .limit(BATCH_SIZE);

    let dataSnapshot = await query.get();
    console.log(
      `Processing ${dataSnapshot.docs.length} documents from ${collection} for user ${uid}`
    );

    while (dataSnapshot.docs.length > 0) {
      const batch = fs.batch();

      dataSnapshot.forEach((doc) => {
        if (
          (collection === "issueReports" || collection === "deletedUsersReports") &&
          forceDelete
        ) {
          batch.update(doc.ref, {
            userDeletedStatus: "remove_u",
          });
          return;
        }

        if (forceDelete) {
          // Complete deletion - remove everything
          batch.delete(doc.ref);
        } else {
          // Grace period deletion - selective removal of premium features
          if (
            [
              "habitSetups",      // Premium: Habit tracking
              "habitActions", 
              "journalSetups",    // Premium: Journal entries
              "journalActions",
              "moneyTrackerSetups", // Premium: Money tracking
              "moneyTrackerTransactions",
              "calendarEventSetups", // Premium: Calendar integration
              "calendarEventActions",
            ].includes(collection)
          ) {
            const meDoc = doc.data() as IBaseSchema;

            // Track IDs for activity tracking
            if (["habitSetups", "journalSetups", "moneyTrackerSetups"].includes(collection)) {
              idsToRemove.push([meDoc.docCollection, meDoc.id, meDoc.docVer]);
            }

            // Delete entire premium collections
            batch.delete(doc.ref);
          } else {
            // For basic collections (lists, notes, todos), only remove premium attachments
            if (["lists", "notes", "todos"].includes(collection)) {
              batch.update(doc.ref, {
                attachments: [], // Remove premium attachments
                cloudUpdatedAt: FieldValue.serverTimestamp(),
              });
            }
            // Other collections remain untouched during grace period
          }
        }
      });

      await batch.commit();

      const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
      query = fs.collection(collection)
        .where("uid", "==", uid)
        .select("id", "docVer", "docCollection", "attachments")
        .startAfter(last)
        .limit(BATCH_SIZE);
      dataSnapshot = await query.get();
    }

    console.log(`Completed optimized deletion for user ${uid} from collection ${collection}`);
  } catch (error) {
    console.error(
      `Failed to delete data for user - ${uid} from collection - ${collection}:`,
      error
    );
  }
}

/**
 * Query for users whose user info was deleted before the grace period cutoff.
 * @param {Date} priorDate - The cutoff date
 * @param {FirebaseFirestore.QueryDocumentSnapshot} [startAfterDoc] - Document to start after for pagination
 * @return {FirebaseFirestore.Query<FirebaseFirestore.DocumentData>} The query
 */
function getDeletedUserQuery(priorDate: Date, startAfterDoc?: FirebaseFirestore.QueryDocumentSnapshot) {
  let query = admin
    .firestore()
    .collection("users")
    .where("userInfo.deletedAt", "<", priorDate)
    .where("subscriptionInfo.subscriptionState", "in", [
      "trialExpired",
      "trialExtExpired",
      "subscriptionExpired",
    ])
    .orderBy("userInfo.deletedAt")
    .orderBy("__name__")
    .limit(DELETION_BATCH_SIZE);
  
  if (startAfterDoc) {
    query = query.startAfter(startAfterDoc);
  }
  
  return query;
}

/**
 * Query for users whose subscriptions expired before the grace period cutoff.
 * 
 * Optimizations:
 * - Increased batch size: 1000 vs 200 (5x improvement)
 * - Proper ordering: subscriptionExpDate + __name__ for reliable pagination
 * - Cursor-based pagination: startAfter for consistent results
 * 
 * For 1M users: 1000 batches vs 5000 batches (80% reduction in function calls)
 * 
 * @param {Date} priorDate - The cutoff date
 * @param {FirebaseFirestore.QueryDocumentSnapshot} [startAfterDoc] - Document to start after for pagination
 * @return {FirebaseFirestore.Query<FirebaseFirestore.DocumentData>} The query
 */
function getSubscriptionExpiredUserQuery(priorDate: Date, startAfterDoc?: FirebaseFirestore.QueryDocumentSnapshot) {
  let query = admin
    .firestore()
    .collection("usersMetadata")
    .where("subscriptionInfo.subscriptionExpDate", "<", priorDate)
    .orderBy("subscriptionInfo.subscriptionExpDate") // Essential for pagination
    .orderBy("__name__") // Secondary sort for consistent ordering
    .limit(DELETION_BATCH_SIZE);
  
  if (startAfterDoc) {
    query = query.startAfter(startAfterDoc); // Cursor-based pagination
  }
  
  return query;
}

/**
 * Query for basic tier users who still have attachments from when they were premium.
 * These users need attachment cleanup even though they're already on basic tier.
 * 
 * Targets users who:
 * - Are currently on basic tier
 * - Started subscription more than 30 days ago  
 * - Still have storage usage > 0 (indicates leftover attachments)
 * 
 * @param {Date} priorDate - The cutoff date
 * @param {FirebaseFirestore.QueryDocumentSnapshot} [startAfterDoc] - Document to start after for pagination
 * @return {FirebaseFirestore.Query<FirebaseFirestore.DocumentData>} The query
 */
function getBasicUsersWithAttachments(priorDate: Date, startAfterDoc?: FirebaseFirestore.QueryDocumentSnapshot) {
  let query = admin
    .firestore()
    .collection("users")
    .where("subscriptionInfo.entitlement", "==", "basic")
    .where("subscriptionInfo.subscriptionStartDate", "<=", priorDate)
    .where("userInfo.storageUsed", ">", 0) // Has leftover premium data
    .orderBy("subscriptionInfo.subscriptionStartDate")
    .orderBy("__name__")
    .limit(DELETION_BATCH_SIZE);
  
  if (startAfterDoc) {
    query = query.startAfter(startAfterDoc);
  }
  
  return query;
}

async function deleteFiles(uid: string): Promise<void> {
  console.log(`deleting files for user - ${uid}`);
  try {
    await deleteAllFilesInFolder(`${AttachmentConstants.userDataPath}/${uid}`);
    await deleteAllFilesInFolder(
      `userBackup/${currentDbVersion}/${uid}/backup.json`
    );
    console.log(`Files deleted sucessfuly for user - ${uid}`);
  } catch (error) {
    console.error(`Failed to delete files for user - ${uid}:`, error);
  }
}

async function deleteAllFilesInFolder(path: string) {
  const bucket = admin.storage().bucket();
  console.log(`deleting files in ${path}`);
  await bucket.deleteFiles({ prefix: path });
  console.log(`files deleted in ${path}`);
}

async function clearViewSettings(uid: string) {
  const viewSettingsSnapshot = await admin
    .firestore()
    .collection("viewSettings")
    .where("uid", "==", uid)
    .get();
  if (viewSettingsSnapshot.docs.length > 0) {
    const viewSettingsDoc =
      viewSettingsSnapshot.docs[0].data() as IViewSettings;
    viewSettingsDoc.featureSettings.userGoal = null;
    await admin
      .firestore()
      .collection("viewSettings")
      .doc(viewSettingsDoc.id)
      .update(viewSettingsDoc);
  }
}

async function clearUserResources(uid: string) {
  const userResourceSnapshot = await admin
    .firestore()
    .collection("userResources")
    .where("uid", "==", uid)
    .get();
  if (userResourceSnapshot.docs.length > 0) {
    const userResourceDoc =
      userResourceSnapshot.docs[0].data() as IUserResources;
    userResourceDoc.tags = [];
    userResourceDoc.shared.sharedTodos = [];
    userResourceDoc.shared.sharedHabitSetups = [];
    userResourceDoc.shared.sharedJournalSetups = [];
    userResourceDoc.shared.sharedLists = [];
    userResourceDoc.shared.sharedNotes = [];
    userResourceDoc.shared.sharedMoneyTrackerSetups = [];
    await admin
      .firestore()
      .collection("userResources")
      .doc(userResourceDoc.id)
      .update(userResourceDoc);
  }
}