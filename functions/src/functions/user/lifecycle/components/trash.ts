import * as admin from "firebase-admin";
import {
  DocumentData,
  Filter,
  QueryDocumentSnapshot,
  DocumentSnapshot,
} from "firebase-admin/firestore";
import { IAttachmentModel } from "../../../migration/versions/base/attachments_model";
import {
  IBaseSchema,
  ZDocCollectionName,
} from "../../../migration/versions/base/base_schema";
import {
  AttachmentArrayType,
  DbRepository,
} from "../../../save_data/db.repository";
import {
  IUser,
  getMeParsedData,
} from "../../../migration/versions/base/model_mappings";
import PromisePool from "@supercharge/promise-pool";
import {
  getOriginalFilePath,
  getOptimizedFilePath,
  getThumbnailFilePath,
  convertDatesToTimestamps,
} from "../../../../utils/utility_methods";
import { error } from "firebase-functions/logger";
import { calculateStorageForUserById } from "../../../account_backup/storage";
import {
  createSpecialActivitiesDoc,
  createSpecialActivitiesForSharedMembersForTaskRemove,
} from "../../user_api/get_user";

const queryLimit = 500;

type DeletedAtDataType = {
  deletedAt: admin.firestore.Timestamp;
  attachments: IAttachmentModel[];
  uid: string;
  id: string;
  docCollection: string;
  docVer: number;
};

export async function removeOldDeletedUserData() {
  const daysToDelete = 30;
  const priorDate = new Date(
    new Date().setDate(new Date().getDate() - daysToDelete)
  );
  await deleteDataForCollection("todos", priorDate);
  await deleteDataForCollection("notes", priorDate);
  await deleteDataForCollection("lists", priorDate);
  await deleteDataForCollection("habitSetups", priorDate);
  await deleteDataForCollection("journalSetups", priorDate);
}

async function deleteDataForCollection(
  collectionName: string,
  priorDate: Date
) {
  console.log("deleting old docs for collectionName = " + collectionName);
  const dataToBeDeletedQuery = admin
    .firestore()
    .collection(collectionName)
    .where(
      Filter.or(
        Filter.where("deletedAt", "<", priorDate),
        Filter.where("permaDeletedAt", "!=", null)
      )
    )
    .limit(queryLimit);

  let dataSnapshot = await dataToBeDeletedQuery.get();
  console.log(`length = ${dataSnapshot.docs.length}`);
  while (dataSnapshot.docs.length > 0) {
    const tasks: Promise<unknown>[] = [];
    for (const document of dataSnapshot.docs) {
      tasks.push(deleteDoc(document));
    }
    await Promise.all(tasks);
    const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
    dataSnapshot = await dataToBeDeletedQuery.startAfter(last).get();
    console.log("next length of snapshot = " + dataSnapshot.docs.length);
  }
}

export async function deleteDoc(
  document:
    | QueryDocumentSnapshot<DocumentData>
    | DocumentSnapshot<DocumentData>
    | DocumentData,
  transaction?: admin.firestore.Transaction
): Promise<void> {
  let doc: DeletedAtDataType;
  let ref: FirebaseFirestore.DocumentReference;

  if (
    document instanceof QueryDocumentSnapshot ||
    document instanceof DocumentSnapshot
  ) {
    doc = document.data() as DeletedAtDataType;
    ref = document.ref;
  } else {
    doc = document as DeletedAtDataType;
    const db = admin.firestore();
    ref = db.collection(doc.docCollection).doc(doc.id);
  }

  const collectionName = doc.docCollection;
  let totalSize = 0;

  if (doc.attachments) {
    totalSize = await deleteAttachments(
      doc.uid,
      doc.attachments,
      doc.docCollection,
      doc.id
    );
  }

  if (transaction) {
    transaction.delete(ref);
  } else {
    await ref.delete();
  }

  await addDeletedDocsToUser(doc);
  await decrementUserStorage(doc.uid, totalSize);

  if (isSetupCollection(collectionName)) {
    await deleteActions(
      collectionName == "habitSetups" ? "habitActions" : "journalActions",
      doc.id
    );
  }
}

export async function deleteDocs(
  uid: string,
  documents: IBaseSchema[]
): Promise<void> {
  console.log("Deleting docs: " + documents.length);
  let totalSize = 0;
  await PromisePool.withConcurrency(100)
    .for(documents)
    .process(async (doc) => {
      if ((doc as unknown as AttachmentArrayType).attachments) {
        totalSize += await deleteAttachments(
          doc.uid,
          (doc as unknown as AttachmentArrayType).attachments,
          doc.docCollection,
          doc.id
        );
      }
      if (isSetupCollection(doc.docCollection)) {
        await deleteActions(doc.docCollection, doc.id);
      }
      const db = admin.firestore();
      await db.collection(doc.docCollection).doc(doc.id).delete();
    });
  const db = admin.firestore();
  await db.runTransaction(async (t) => {
    const doc = await t.get(db.collection("users").doc(uid));
    if (doc.exists) {
      const user = getMeParsedData(doc.data() as IBaseSchema) as IUser;
      for (const doc of documents) {
        createSpecialActivitiesDoc(
          uid,
          undefined,
          true,
          doc.docVer,
          doc.docCollection,
          [doc.id]
        );
      }
      user.userInfo.storageUsed -= totalSize;
      user.cloudUpdatedAt = null;
      t.update(
        db.collection("users").doc(uid),
        convertDatesToTimestamps(getMeParsedData(user))
      );
    }
  });
  // Also generate special activities if the doc is shared for shared memebers.
  await createSpecialActivitiesForSharedMembersForTaskRemove(
    uid,
    documents.map((doc) => ({ oldDoc: doc }))
  );
  const userDocSnapshot = await db.collection("users").doc(uid).get();
  await calculateStorageForUserById((userDocSnapshot.data() as IUser).uid);
  console.log("Deleted docs: " + documents.length);
}

async function deleteActions(setupName: string, setupId: string) {
  console.log("deleting action docs for setup = " + setupId);
  let collectionName = "";
  switch (setupName) {
    case "habitSetups":
      collectionName = "habitActions";
      break;
    case "journalSetups":
      collectionName = "journalActions";
      break;
    default:
      error(
        "setup not supported yet. Please implement this for deletion: " +
          setupName
      );
  }
  const dataToBeDeletedQuery = admin
    .firestore()
    .collection(collectionName)
    .where("setupId", "==", setupId)
    .limit(queryLimit);

  let dataSnapshot = await dataToBeDeletedQuery.get();
  console.log(`length = ${dataSnapshot.docs.length}`);
  while (dataSnapshot.docs.length > 0) {
    const tasks: Promise<unknown>[] = [];
    for (const document of dataSnapshot.docs) {
      tasks.push(deleteDoc(document));
    }
    await Promise.all(tasks);
    const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
    dataSnapshot = await dataToBeDeletedQuery.startAfter(last).get();
    console.log("next length of snapshot = " + dataSnapshot.docs.length);
  }
}

async function deleteAttachments(
  uid: string,
  attachments: IAttachmentModel[],
  docCollectionName: string,
  docId: string
): Promise<number> {
  const tasks: Promise<unknown>[] = [];
  let totalSize = 0;

  for (const attachment of attachments) {
    const extension = attachment.format ?? "jpeg";
    const originalFilePath = getOriginalFilePath(
      uid,
      docCollectionName,
      docId,
      attachment.id,
      extension
    );
    const optimizedFilePath = getOptimizedFilePath(
      uid,
      docCollectionName,
      docId,
      attachment.id,
      extension
    );
    const thumbnailFilePath = getThumbnailFilePath(
      uid,
      docCollectionName,
      docId,
      attachment.id,
      extension
    );
    tasks.push(deleteFile(originalFilePath));
    tasks.push(deleteFile(optimizedFilePath));
    tasks.push(deleteFile(thumbnailFilePath));
    totalSize += attachment.size;
  }

  await Promise.all(tasks);
  return totalSize;
}

async function deleteFile(filePath: string) {
  try {
    await admin.storage().bucket().file(filePath).delete();
  } catch (error) {
    // console.error("Error deleting file: ", error);
  }
}

async function updateUser(uid: string, updateData: (_userData: IUser) => void) {
  const userRef = admin
    .firestore()
    .collection(ZDocCollectionName.Enum.users)
    .doc(uid);

  // Retrieve the user document
  const userDoc = await userRef.get();
  if (!userDoc.exists) {
    console.log("No such user!");
    return;
  }

  const userData = userDoc.data() as IUser;

  // Check if userData exists before updating
  if (userData) {
    // Update the userData
    userData.cloudUpdatedAt = null;
    updateData(userData);
    await new DbRepository().updateData(getMeParsedData(userData));
  }
}

async function addDeletedDocsToUser(doc: DeletedAtDataType) {
  await createSpecialActivitiesDoc(
    doc.uid,
    undefined,
    true,
    doc.docVer,
    doc.docCollection,
    [doc.id]
  );
}

async function decrementUserStorage(uid: string, size: number) {
  await updateUser(uid, (userData) => (userData.userInfo.storageUsed -= size));
}

function isSetupCollection(collectionName: string): boolean {
  return collectionName === "habitSetups" || collectionName === "journalSetups";
}
