import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import {
  getEmailNameByIdFromAuthService,
  getSubscriptionPageLink,
  getSupportLink,
  getViewSettingsByUserId,
} from "../../../../utils/utility_methods";
import { IUser } from "../../../migration/versions/base/model_mappings";
import { EmailInfo, sendBulkEmail } from "../../../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../../../utils/emails/email_helper";

const emailLimit = 200;

export async function sendNotSubscribedReminderMail() {
  const daysPassed = 15;
  const priorDate = new Date(
    new Date().setDate(new Date().getDate() - daysPassed)
  );
  const startDate = new Date(
    priorDate.getFullYear(),
    priorDate.getMonth(),
    priorDate.getDate(),
    0,
    0,
    0
  );
  const endDate = new Date(
    priorDate.getFullYear(),
    priorDate.getMonth(),
    priorDate.getDate(),
    23,
    59,
    29
  );
  console.log(`start date = ${startDate}`);
  console.log(`end date = ${endDate}`);
  const usersToBeDeletedQuery = await admin
    .firestore()
    .collection("users")
    .where("subscriptionInfo.entitlement", "==", "free")
    .where("subscriptionInfo.subscriptionExpDate", "==", null)
    .where("createdAt", ">=", startDate)
    .where("createdAt", "<=", endDate)
    .limit(emailLimit);

  let dataSnapshot = await usersToBeDeletedQuery.get();
  console.log(`length = ${dataSnapshot.docs.length}`);
  while (dataSnapshot.docs.length > 0) {
    const tasks: Promise<EmailInfo | undefined>[] = [];
    for (const document of dataSnapshot.docs) {
      const userData = document.data() as IUser;
      tasks.push(getUserDetails(userData));
    }
    const emailList = await Promise.all(tasks);
    await sendBulkEmail(
      MevolveEmails.notSubscribed,
      emailList.filter((emailInfo) => emailInfo != undefined) as EmailInfo[]
    );
    const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
    dataSnapshot = await usersToBeDeletedQuery.startAfter(last).get();
    console.log("next length of snapshot = " + dataSnapshot.docs.length);
  }
}
async function getUserDetails(
  userData: IUser,
): Promise<EmailInfo | undefined> {
  const results = await Promise.all([getEmailNameByIdFromAuthService(userData.uid), getViewSettingsByUserId(userData.uid)]);
  const userDetails = results[0];
  const viewSettings = results[1];
  const language = viewSettings?.appSettings.language;
  if (userDetails?.email == undefined) {
    functions.logger.error("email not found");
    return undefined;
  }
  return {
    to: userDetails.email,
    data: {
      name: userDetails.name,
      subscriptionLink: getSubscriptionPageLink(),
      linkToSupportChat: getSupportLink(),
    },
    language: language,
  };
}
