import { MeLogger } from "../../../utils/logger/models/me_logger";
import { UserMigration } from "../../migration/helper/user_migration";
import { IBaseSchema } from "../../migration/versions/base/base_schema";
import * as admin from "firebase-admin";

export async function downgradeUserData(
  userId: string,
  finalVersion: number,
  logger: MeLogger
) {
  const userSnapshot = await admin
    .firestore()
    .collection("users")
    .doc(userId)
    .get();
  if (userSnapshot.exists) {
    if (userSnapshot.data()?.docVer == finalVersion) {
      logger.log("user already migrated:" + userId);
      return { result: "success" };
    } else {
      const result = await new UserMigration().migrateUser(
        userSnapshot.data() as IBaseSchema,
        finalVersion
      );
      const migrationResult = result ? "success" : "failed";
      logger.log(
        "user migrated. Result = " + migrationResult + ", user: " + userId
      );
      return { result: migrationResult };
    }
  } else {
    return {
      result: "failed",
      reason: "no user found with id: " + userId,
    };
  }
}
