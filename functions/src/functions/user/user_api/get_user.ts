/* eslint-disable require-jsdoc */
import * as functions from "firebase-functions/v2";
import * as admin from "firebase-admin";
import {
  DocumentData,
  FieldValue,
  Timestamp,
  WriteBatch,
} from "firebase-admin/firestore";

import { v1 as uuidv1 } from "uuid";
import { INTERNAL_SERVER_ERROR } from "../../../utils/constants";
import {
  getNewSecret,
  encryptDocKey,
  encryptTextData,
  getSecretFromKmsForUserByEmail,
  generateECCKeyPair,
  hashData,
} from "../../../utils/encryption/encryption";
import {
  getEmailNameByIdFromAuthService,
  convertDatesToTimestamps,
  getUuidFromHashedEmail,
  getHashedEmailFromUid,
  getViewSettingsByUserId,
  getCurrentUTCDate,
} from "../../../utils/utility_methods";
import {
  IAppLanguage,
  IBaseSchema,
  ZSourceOfChange,
} from "../../migration/versions/base/base_schema";
import {
  IUser,
  currentDbVersion,
  getMeParsedData,
  IUserMetadata,
  IUserKeys,
  IViewSettings,
  ISpecialActivities,
  IPublicUsers,
} from "../../migration/versions/base/model_mappings";
import { DbRepository } from "../../save_data/db.repository";
import { deleteUserDataFromCloud } from "../lifecycle/components/delete_user_data";
import { MeLogger } from "../../../utils/logger/models/me_logger";
import { sendSingleEmailViaSes } from "../../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../../utils/emails/email_helper";
import {
  deletePublicUser,
  setPublicUser,
} from "../../../services/cloudfare/user.service";

export async function getUserData(
  uid: string,
  displayName: string | null,
  email: string,
  userLanguage: IAppLanguage,
  timeZone: string,
  logger: MeLogger
): Promise<{ user: string; viewSettings: string; userKey: string }> {
  const userSnapshot = await admin
    .firestore()
    .collection("users")
    .doc(uid)
    .get();

  if (userSnapshot.exists) {
    logger.log("got user " + uid);

    // Get user key with the new function
    const userSecret = await getUserKMSSecret(email);
    const userKeyInfo = await getOrCreateUserKey(email, uid, userSecret);

    // Get view settings
    const viewSettingsSnapshot = await admin
      .firestore()
      .collection("viewSettings")
      .where("uid", "==", uid)
      .orderBy("cloudUpdatedAt", "desc")
      .limit(1)
      .get();

    console.log("length " + viewSettingsSnapshot.size);
    let viewSettingsData = null;
    if (viewSettingsSnapshot.docs.length > 0) {
      viewSettingsData = viewSettingsSnapshot.docs[0].data() as IBaseSchema;
    }

    const user = userSnapshot.data() as IUser;

    return {
      user: JSON.stringify(user),
      userKey: JSON.stringify(userKeyInfo),
      viewSettings: JSON.stringify(
        viewSettingsData ??
          getViewSettingsDocument(
            uid,
            userKeyInfo.publicKey,
            userLanguage,
            timeZone
          )
      ),
    };
  } else {
    logger.log("creating new user for uid - " + uid);
    if (email == null) {
      throw new functions.https.HttpsError(
        INTERNAL_SERVER_ERROR,
        "Email is required to create a new user"
      );
    }

    const result = await Promise.all([
      getUserKMSSecret(email),
      generatePseudoName(),
    ]);
    const userSecret = result[0];
    const pseudoName = result[1];

    // Get or create user key with the new function
    const userKeyInfo = await getOrCreateUserKey(email, uid, userSecret);

    // Create user document
    const userDocDek = getNewSecret();
    const encData = {
      dek: await encryptDocKey(userDocDek, uid, userKeyInfo.publicKey),
      encFields: ["userInfo.email", "userInfo.name"],
    };

    const tempUser = {
      id: uid,
      docVer: currentDbVersion,
      docCollection: "users" as IBaseSchema["docCollection"],
      uid: uid,
      localUpdatedAt: new Date(),
      cloudUpdatedAt: null,
      createdAt: new Date(),
      sessionId: uuidv1(),
      source: ZSourceOfChange.enum.cloud,
      encData: encData,
      userInfo: {
        name:
          displayName == null || displayName == ""
            ? ""
            : encryptTextData(displayName ?? "", userDocDek),
        email: encryptTextData(email, userDocDek),
        createdAt: new Date(),
        pseudoName: pseudoName,
      },
    };
    const user = getMeParsedData(tempUser);
    const viewSettings = await getViewSettingsDocument(
      uid,
      userKeyInfo.publicKey,
      userLanguage,
      timeZone
    );
    const userResources = await getUserResourcesDocument(
      uid,
      userKeyInfo.publicKey
    );

    // saving view settings first so that welcome email trigger can get user language.
    (
      viewSettings as IViewSettings
    ).notificationSettings.emailNotificationTimezone = timeZone;
    await new DbRepository().updateData(viewSettings);
    await Promise.all([
      new DbRepository().updateData(user),
      new DbRepository().updateData(userResources, userKeyInfo),
    ]);

    return {
      user: JSON.stringify(user),
      userKey: JSON.stringify(userKeyInfo),
      viewSettings: JSON.stringify(viewSettings),
    };
  }
}

async function getViewSettingsDocument(
  uid: string,
  publicKey: string,
  userLanguage: IAppLanguage,
  timeZone: string
): Promise<IBaseSchema> {
  const userDocDek = getNewSecret();
  const encData = {
    dek: await encryptDocKey(userDocDek, uid, publicKey),
    encFields: ["featureSettings.userGoal"],
  };
  const viewSettings = getMeParsedData({
    id: `vs_${uid}`,
    docVer: currentDbVersion,
    docCollection: "viewSettings" as IBaseSchema["docCollection"],
    uid: uid,
    localUpdatedAt: new Date(),
    cloudUpdatedAt: new Date(),
    createdAt: new Date(),
    sessionId: uuidv1(),
    source: ZSourceOfChange.enum.cloud,
    encData: encData,
  }) as IViewSettings;
  viewSettings.appSettings.language = userLanguage;
  viewSettings.appSettings.supportLanguage = userLanguage;
  viewSettings.notificationSettings.emailNotificationTimezone = timeZone;
  return viewSettings;
}

async function getUserResourcesDocument(
  uid: string,
  publicKey: string
): Promise<IBaseSchema> {
  const userDocDek = getNewSecret();
  const encData = {
    dek: await encryptDocKey(userDocDek, uid, publicKey),
    encFields: ["tags[].tag"],
  };
  return getMeParsedData({
    id: `ur_${uid}`,
    docVer: currentDbVersion,
    docCollection: "userResources" as IBaseSchema["docCollection"],
    uid: uid,
    localUpdatedAt: new Date(),
    cloudUpdatedAt: new Date(),
    createdAt: new Date(),
    sessionId: uuidv1(),
    source: ZSourceOfChange.enum.cloud,
    encData: encData,
  });
}

async function getUserKMSSecret(email: string): Promise<string> {
  const userSecret = (await getSecretFromKmsForUserByEmail(email)).key;
  return userSecret;
}

export async function deleteUserData(uid: string, logger: MeLogger) {
  logger.log("Deleting user data for user - " + uid);
  await deleteUserDataFromCloud(uid, true, true);
  // need to delete calendar integrations
  logger.log("Delete data complete for user - " + uid);
}

export async function deleteUser(uid: string, logger: MeLogger) {
  const db = admin.firestore();
  const userInfo = await getEmailNameByIdFromAuthService(uid);
  try {
    const userDocRef = db.collection("users").doc(uid);
    const doc = await userDocRef.get();
    const metaDocRef = db.collection("usersMetadata").doc(uid);
    const metaDoc = await metaDocRef.get();
    if (doc.exists && doc.data() != undefined) {
      // Collect all information needed before deletion
      const viewSettings = await getViewSettingsByUserId(uid);
      const language = viewSettings?.appSettings.language;
      
      let userMetadata: IUserMetadata | null = null;
      let userRecord: any = null;
      if (metaDoc.exists && metaDoc.data() != undefined) {
        userMetadata = metaDoc.data() as IUserMetadata;
        userRecord = await admin.auth().getUser(uid);
      }

      // Delete user data
      await deleteUserDataFromCloud(uid, true, true);
      logger.log(`Delete data complete for user - ${uid}`);

      // Explicitly delete core user documents that are excluded from deleteUserDataFromCloud
      try {
        const batch = db.batch();
        
        // Delete users document
        batch.delete(userDocRef);
        
        // Delete viewSettings document if it exists
        if (viewSettings) {
          const viewSettingsQuery = await db.collection("viewSettings").where("uid", "==", uid).get();
          viewSettingsQuery.forEach((doc) => {
            batch.delete(doc.ref);
          });
        }
        
        // Delete userResources document
        const userResourcesQuery = await db.collection("userResources").where("uid", "==", uid).get();
        userResourcesQuery.forEach((doc) => {
          batch.delete(doc.ref);
        });
        
        await batch.commit();
        logger.log(`Core user documents deleted for user - ${uid}`);
      } catch (error) {
        logger.error(`Error deleting core user documents for user ${uid}:`, error);
      }

      // Revoke refresh tokens
      try {
        await admin.auth().revokeRefreshTokens(uid);
      } catch (error) {
        logger.error("Error revoking tokens:", error);
      }

      // Update user metadata with deletion markers
      if (userMetadata && userRecord) {
        // Handle tokensValidAfterTime safely
        let timestampMillis = Date.now();
        if (userRecord.tokensValidAfterTime) {
          const tokenTime = new Date(userRecord.tokensValidAfterTime).getTime();
          if (!isNaN(tokenTime)) {
            timestampMillis = tokenTime;
          }
        }

        userMetadata.userInfo.deletedAt = new Date();
        userMetadata.userDeletedStatus = "remove_u";
        userMetadata.userInfo.name = "";
        userMetadata.userInfo.email = "";
        userMetadata.userInfo.tokensValidAfterTime = Timestamp.fromMillis(timestampMillis).toDate();
        userMetadata.cloudUpdatedAt = Timestamp.fromMillis(Date.now()).toDate();
        userMetadata.localUpdatedAt = Timestamp.fromMillis(Date.now()).toDate();
        await metaDocRef.update(
          convertDatesToTimestamps(getMeParsedData(userMetadata))
        );
        console.log("User metadata successfully updated");
      }

      // Delete user keys and Firebase Auth account
      const deleteKeyQuery =
        userInfo?.email != null
          ? db.collection("userKeys").where("email", "==", userInfo.email).get()
          : db.collection("userKeys").where("uid", "==", uid).get();
      await Promise.all([
        admin.auth().deleteUser(uid),
        deleteKeyQuery.then((snapshot) => {
          snapshot.forEach(async (doc) => {
            await doc.ref.delete();
          });
        }),
      ]);

      // Send deletion confirmation email
      if (userInfo?.email != null) {
        await sendSingleEmailViaSes({
          templateName: MevolveEmails.accountDeleted,
          recipient: userInfo.email,
          templateData: {
            name: userInfo?.name ?? "",
            daysToDelete: "0",
          },
          language: language,
        });
      } else {
        console.log("User email not found");
      }

      console.log("User successfully deleted permanently");
    } else {
      console.log("User not found. Considered as already deleted.");
    }
  } catch (error) {
    console.log(error);
    throw new functions.https.HttpsError(
      INTERNAL_SERVER_ERROR,
      "Unknown error occurred in updating user data."
    );
  }
}

export async function addUserDeletionReason(
  userId: string,
  reason: string,
  feedback: string,
  uname: string
) {
  const db = admin.firestore();
  try {
    const id = db.collection("deletedUsersReports").doc().id;
    await db.collection("deletedUsersReports").doc(id).set({
      reason: reason,
      feedback: feedback,
      deletedAt: FieldValue.serverTimestamp(),
      userDeletedStatus: "remove_u",
      uid: userId,
      uname: uname,
      id: id,
    });
  } catch (error) {
    throw new functions.https.HttpsError(
      INTERNAL_SERVER_ERROR,
      "Unknown error occurred in updating user data."
    );
  }
}

export async function updateSupportCollections(
  userId: string,
  uname: string,
  restore: boolean
) {
  try {
    console.log("Updating collection data for deleted user");
    await Promise.all([
      updateDataFromCollections(userId, uname, "chatUsers", restore),
      updateDataFromCollections(userId, uname, "issueReports", restore),
      updateDataFromCollections(userId, uname, "deletedUsersReports", restore),
    ]);
    console.log("Collection data successfully updated for deleted user");
  } catch (error) {
    console.log(error);
  }
}

async function updateDataFromCollections(
  userId: string,
  uname: string,
  collection: string,
  restore: boolean
) {
  const db = admin.firestore();
  try {
    if (collection == "chatUsers") {
      const chatUser = await db.collection(collection).doc(userId).get();
      if (chatUser.exists) {
        if (restore) {
          await db.collection(collection).doc(userId).update({
            userDeletedStatus: null,
          });
        } else {
          await db.collection(collection).doc(userId).update({
            userDeletedStatus: "deleted",
          });
        }
      }
    } else if (
      collection == "issueReports" ||
      collection == "deletedUsersReports"
    ) {
      const query = db
        .collection(collection)
        .where("uid", "==", userId)
        .limit(500);
      let dataSnapshot = await query.get();
      console.log("received data = " + dataSnapshot.docs.length);
      while (dataSnapshot.docs.length > 0) {
        const batch = db.batch();
        dataSnapshot.forEach((doc) => {
          if (restore) {
            batch.update(doc.ref, { userDeletedStatus: null });
          } else {
            batch.update(doc.ref, { userDeletedStatus: "deleted" });
          }
        });
        await batch.commit();
        const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
        dataSnapshot = await query.startAfter(last).get();
        console.log("received data = " + dataSnapshot.docs.length);
      }
    }
  } catch (error) {
    throw new functions.https.HttpsError(
      INTERNAL_SERVER_ERROR,
      "Unknown error occurred in updating data from collections."
    );
  }
}

export async function generatePseudoName(): Promise<string> {
  let pseudoName = "";
  const db = admin.firestore();
  const countQuery = db.collection("serverMetadata").doc("appMetadata");
  const countData = await countQuery.get();
  if (!countData.exists) {
    await countQuery.set({ usersCount: 0 }, { merge: true });
  }
  const count = (countData.data()?.usersCount as number) ?? 0;
  pseudoName = "" + (count + 1);
  return pseudoName;
}

export async function getOrCreateUserKey(
  email: string,
  uid: string,
  userSecret: string
): Promise<IUserKeys> {
  const db = admin.firestore();

  // Define types for our document collections
  type DocWithTimestamp = {
    doc: FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>;
    data: IUserKeys;
    timestamp: Date | null;
  };

  // Use a transaction to ensure atomicity
  return db.runTransaction(async (transaction) => {
    // Get all documents that might be related to this user (by email OR uid)
    const emailQuery = db.collection("userKeys").where("email", "==", email);
    const uidQuery = db.collection("userKeys").where("uid", "==", uid);

    const [emailDocs, uidDocs] = await Promise.all([
      transaction.get(emailQuery),
      transaction.get(uidQuery),
    ]);

    // Collect all documents, avoiding duplicates
    const allDocs = new Map<
      string,
      FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>
    >();
    emailDocs.docs.forEach((doc) => allDocs.set(doc.id, doc));
    uidDocs.docs.forEach((doc) => allDocs.set(doc.id, doc));

    // Find exact matches (both email and uid match)
    const exactMatches: DocWithTimestamp[] = [];
    // Find documents to delete (only email matches OR only uid matches)
    const docsToDelete: FirebaseFirestore.QueryDocumentSnapshot<FirebaseFirestore.DocumentData>[] =
      [];

    allDocs.forEach((doc) => {
      const data = doc.data() as IUserKeys;

      // Convert Firestore Timestamp to JavaScript Date
      const cloudUpdatedAt =
        data.cloudUpdatedAt instanceof Timestamp
          ? data.cloudUpdatedAt.toDate()
          : data.cloudUpdatedAt;

      if (data.email === email && data.uid === uid && cloudUpdatedAt) {
        // Exact match
        exactMatches.push({
          doc,
          data,
          timestamp: cloudUpdatedAt,
        });
      } else {
        // Either email-only or uid-only match - mark for deletion
        docsToDelete.push(doc);
      }
    });

    // Always delete non-exact matches
    docsToDelete.forEach((doc) => {
      transaction.delete(doc.ref);
    });

    // If we have exact matches
    if (exactMatches.length > 0) {
      // Sort by timestamp (newest first)
      exactMatches.sort(
        (a, b) => (b.timestamp?.getTime() || 0) - (a.timestamp?.getTime() || 0)
      );

      // Keep only the most recent exact match
      const mostRecent = exactMatches[0];

      // Delete any older exact matches
      for (let i = 1; i < exactMatches.length; i++) {
        transaction.delete(exactMatches[i].doc.ref);
      }

      return mostRecent.data;
    }

    // No exact match found, create a new key
    const eccKeyPair = generateECCKeyPair();
    const userKey: IUserKeys = {
      id: uuidv1(),
      uid: uid,
      email: email,
      emailHash: hashData(email),
      encPrivateKey: encryptTextData(eccKeyPair.privateKey, userSecret),
      publicKey: eccKeyPair.publicKey,
      privateKeyPasswordType: "KMS",
      cloudUpdatedAt: null,
    };

    // Add the new key
    const docRef = db.collection("userKeys").doc(userKey.id);
    transaction.set(docRef, convertDatesToTimestamps(userKey));

    return userKey;
  });
}

export async function createSpecialActivitiesDoc(
  uid: string,
  activityForEmailHash: string | undefined,
  activityForCreator: boolean,
  entityDocVer: number,
  entityDocCollection: IBaseSchema["docCollection"] | string,
  entityIds: string[],
  batch?: WriteBatch
): Promise<void> {
  let emailHash = activityForEmailHash;

  if (activityForCreator === true && !emailHash) {
    // Fetch the email hash of the user
    emailHash = await getHashedEmailFromUid(uid);
    if (!emailHash) {
      console.error(
        "Email hash not provided for user but as user is creator tried fetching but failedd to so so for user with userId",
        uid
      );
      return;
    }
  } else if (!emailHash) {
    console.error(
      "Email hash not provided for user for which this special activity was meant for"
    );
    return;
  }

  const id = uuidv1();
  const specialActivity: ISpecialActivities = {
    id,
    uid,
    docCollection: "specialActivities" as IBaseSchema["docCollection"],
    activityForEmailHash: emailHash ?? "",
    entityDocVer,
    entityDocCollection,
    entityIds,
    actionType: "remove",
    createdAt: new Date(),
  };

  const docRef = admin.firestore().collection("specialActivities").doc(id);

  console.log(
    `Creating special activity doc for user - ${uid} with email hash - ${emailHash} for entity - ${entityDocCollection} with ids - ${entityIds.join(",")}`
  );
  if (batch) {
    batch.set(docRef, specialActivity); // Add to batch if available
  } else {
    await docRef.set(specialActivity); // Standard Firestore write
  }
}

// When marking a shared tasks for delete we have to avoid creating special activities for activity creator even if exists in
// the shared members list as change by them is already acted as requierd in client.
// Also, if activity creator is not owner of the doc then create a special activity for the doc owner also.
// Avoids creating special activities for a hashedEmail if the new members list also has that hashedEmail.
// Also exclude the new doc owner from hashedEmails to create special activity for them.
// In simle word if something is still acessible by the user then no need to create special activity for them as it of use to them and if not also
// the client will take care of it :)
export async function createSpecialActivitiesForSharedMembersForTaskRemove(
  activityCreatorUid: string,
  documentPairs: { oldDoc: DocumentData; newDoc?: DocumentData }[]
): Promise<void> {
  console.log(
    `Creating special activity docs for ${documentPairs.length} document pairs by user: ${activityCreatorUid}`
  );

  const batch = admin.firestore().batch();
  let activityCount = 0;

  for (const { oldDoc, newDoc } of documentPairs) {
    console.log(`Processing document ID: ${oldDoc.id}`);

    if (!oldDoc.members) {
      console.warn(
        `Skipping document ID: ${oldDoc.id} due to missing 'members' field in oldDoc.`
      );
      continue;
    }

    const oldMembers = new Set<string>(
      oldDoc.members["memberHashedEmails"] || []
    );
    const newMembers = new Set<string>(
      newDoc?.members?.["memberHashedEmails"] || []
    );
    const newDocOwnerUid = newDoc?.uid;
    let newDocOwnerHashedEmail: string | undefined;

    if (newDocOwnerUid) {
      newDocOwnerHashedEmail = await getHashedEmailFromUid(newDocOwnerUid);
    }

    const removedMembers: string[] = [...oldMembers].filter(
      (email) => !newMembers.has(email) && email !== newDocOwnerHashedEmail
    );

    if (removedMembers.length === 0) {
      console.log(
        `No members removed for document ID: ${oldDoc.id}, skipping activity creation.`
      );
      continue;
    }

    const activityCreatorHashedEmail =
      await getHashedEmailFromUid(activityCreatorUid);
    const affectedMembers = removedMembers.filter(
      (email) =>
        typeof email === "string" && email !== activityCreatorHashedEmail
    );

    if (oldDoc.uid !== activityCreatorUid) {
      const ownerEmail = await getHashedEmailFromUid(oldDoc.uid);
      if (typeof ownerEmail === "string") {
        affectedMembers.push(ownerEmail);
      }
    }

    for (const memberHashedEmail of affectedMembers) {
      if (typeof memberHashedEmail !== "string" || !memberHashedEmail) {
        console.log(`Skipping invalid email hash in document ID: ${oldDoc.id}`);
        continue;
      }

      console.log(
        `Creating remove special activity for member: ${memberHashedEmail} for document ID: ${oldDoc.id}`
      );

      await createSpecialActivitiesDoc(
        activityCreatorUid,
        memberHashedEmail,
        false,
        oldDoc.docVer,
        oldDoc.docCollection,
        [oldDoc.id],
        batch
      );

      activityCount++;
    }
  }

  if (activityCount > 0) {
    await batch.commit(); // Commit all writes at once
  }
}

// remove member from the entity if owner is deleting the account
export async function removeMembersAsOwner(userId: string) {
  console.log("Removing members as owner for user with userId", userId);

  const documentsToUpdate = new Map<
    string,
    { ref: admin.firestore.DocumentReference; data: any }
  >();
  const memberHashToIds = new Map<string, Set<string>>();

  // Perform all collection queries in parallel
  const queryPromises = sharedCollections.map((collection) =>
    admin
      .firestore()
      .collection(collection)
      .where("uid", "==", userId)
      .select("members") // Fetch only required fields
      .get()
  );

  try {
    const snapshots = await Promise.all(queryPromises);

    // Collect all `memberHashedEmails` and document references
    snapshots.forEach((snapshot, index) => {
      const collection = sharedCollections[index];
      snapshot.docs.forEach((doc) => {
        const data = doc.data();
        if (data.members?.memberHashedEmails?.length) {
          const docIdWithCollection = `${collection},${doc.id}`;
          documentsToUpdate.set(docIdWithCollection, {
            ref: doc.ref,
            data: data,
          });

          data.members.memberHashedEmails.forEach(
            (memberHashedEmail: string) => {
              if (!memberHashToIds.has(memberHashedEmail)) {
                memberHashToIds.set(memberHashedEmail, new Set());
              }
              memberHashToIds.get(memberHashedEmail)?.add(docIdWithCollection);
            }
          );
        }
      });
    });

    // 🔹 Batch remove all `memberHashedEmails` in Firestore (limit: 500 per batch)
    const batchSize = 500;
    let batch = admin.firestore().batch();
    let batchCount = 0;
    const batchCommits: Promise<FirebaseFirestore.WriteResult[]>[] = [];

    for (const [, docInfo] of documentsToUpdate.entries()) {
      batch.update(docInfo.ref, {
        "members.memberHashedEmails": [], // Remove all members at once
        "members.membersConfig": {}, // Clear membersConfig
        cloudUpdatedAt: new Date(),
        localUpdatedAt: new Date(),
      });
      batchCount++;

      if (batchCount === batchSize) {
        batchCommits.push(batch.commit());
        batch = admin.firestore().batch();
        batchCount = 0;
      }
    }
    if (batchCount > 0) {
      batchCommits.push(batch.commit());
    }

    await Promise.all(batchCommits);
    console.log("All members removed from shared docs of user: ", userId);

    // 🔹 Batch update `userResources` by removing ALL document IDs at once
    const updateBatchCommits: Promise<FirebaseFirestore.WriteResult[]>[] = [];
    let updateBatch = admin.firestore().batch();
    let updateBatchCount = 0;

    for (const [memberHashedEmail, ids] of memberHashToIds.entries()) {
      const hashedEmailUid = await getUuidFromHashedEmail(memberHashedEmail);
      if (!hashedEmailUid) continue;

      // Fetch the user's resource doc
      const userResourceDoc = await admin
        .firestore()
        .collection("userResources")
        .where("uid", "==", hashedEmailUid)
        .limit(1)
        .get();

      if (!userResourceDoc.empty) {
        const docRef = userResourceDoc.docs[0].ref;
        const idsArray = Array.from(ids).map((id) => id.split(",")[1]); // Extract docIds

        const updatedUserResource = sharedCollections.reduce(
          (acc, collection) => {
            const fieldName = getArrayFieldName(collection);
            if (!fieldName) return acc; // Skip if no valid field name exists
            return {
              ...acc,
              [`shared.${fieldName}`]: FieldValue.arrayRemove(...idsArray),
            };
          },
          {
            cloudUpdatedAt: new Date(),
            localUpdatedAt: new Date(),
          }
        );

        // Remove ALL shared document IDs at once
        updateBatch.update(docRef, updatedUserResource);
        updateBatchCount++;

        if (updateBatchCount === batchSize) {
          updateBatchCommits.push(updateBatch.commit());
          updateBatch = admin.firestore().batch();
          updateBatchCount = 0;
        }
      }
    }
    if (updateBatchCount > 0) {
      updateBatchCommits.push(updateBatch.commit());
    }

    await Promise.all(updateBatchCommits);

    console.log(
      "User resources updated for members to remove docs shared by user: ",
      userId
    );

    return { success: true };
  } catch (error) {
    console.error(
      "Error in removing members of docs shared by user:",
      userId,
      error
    );
    throw error;
  }
}

export async function removeMembersAsNonOwner(userId: string) {
  console.log("Removing members as non-owner for user with userId", userId);
  const myHashedEmail = await getHashedEmailFromUid(userId);
  if (myHashedEmail) {
    const documentsToRemove = new Map<string, string>(); // collection -> docId
    // Perform all collection queries in parallel
    const queryPromises = sharedCollections.map((collection) =>
      admin
        .firestore()
        .collection(collection)
        .where("members.memberHashedEmails", "array-contains", myHashedEmail)
        .get()
    );

    try {
      const snapshots = await Promise.all(queryPromises);
      const batchSize = 500;
      const batches: admin.firestore.WriteBatch[] = [];
      let currentBatch = admin.firestore().batch();
      let operationCount = 0;

      // Process members in parallel
      await Promise.all(
        snapshots.map(async (snapshot, index) => {
          const collection = sharedCollections[index];

          snapshot.docs.forEach((doc) => {
            const data = doc.data();
            if (data.members?.memberHashedEmails) {
              const memberIndex =
                data.members.memberHashedEmails.indexOf(myHashedEmail);

              if (memberIndex !== -1) {
                // Store document to remove from userResources
                documentsToRemove.set(collection, doc.id);

                const docRef = doc.ref;
                const updatedMembers = {
                  ...data.members,
                  memberHashedEmails: data.members.memberHashedEmails.filter(
                    (email: string) => email !== myHashedEmail
                  ),
                };

                if (data.members.membersConfig) {
                  updatedMembers.membersConfig = Object.fromEntries(
                    Object.entries(data.members.membersConfig).filter(
                      ([key]) => key !== myHashedEmail
                    )
                  );
                }

                const updates = {
                  members: updatedMembers,
                  cloudUpdatedAt: new Date(),
                };

                currentBatch.update(docRef, updates);
                operationCount++;

                if (operationCount === batchSize) {
                  batches.push(currentBatch);
                  currentBatch = admin.firestore().batch();
                  operationCount = 0;
                }
              }
            }
          });
        })
      );

      // Get user's resource document
      const userResourceDoc = await admin
        .firestore()
        .collection("userResources")
        .where("uid", "==", userId)
        .limit(1)
        .get();

      if (!userResourceDoc.empty) {
        const docRef = userResourceDoc.docs[0].ref;
        const updates: { [key: string]: any } = {
          cloudUpdatedAt: new Date(),
        };

        // Remove documents from corresponding arrays in userResources
        documentsToRemove.forEach((docId, collection) => {
          const arrayFieldName = getArrayFieldName(collection);
          if (arrayFieldName) {
            const fieldPath = `shared.${arrayFieldName}`;
            updates[fieldPath] = FieldValue.arrayRemove(docId);
          }
        });

        currentBatch.update(docRef, updates);
        operationCount++;

        if (operationCount === batchSize) {
          batches.push(currentBatch);
          currentBatch = admin.firestore().batch();
          operationCount = 0;
        }
      }

      // Add final batch if there are pending operations
      if (operationCount > 0) {
        batches.push(currentBatch);
      }

      // Commit all batches
      await Promise.all(batches.map((batch) => batch.commit()));

      return { success: true };
    } catch (error) {
      console.error("Error in removeMembersAsNonOwner:", error);
      return { success: false };
    }
  }
  return { success: false, error: "Hashed email not found" };
}

export async function removePublicSharingOnDocs(userId: string) {
  console.log("Removing public sharing info for user with userId: ", userId);
  // Perform all collection queries in parallel to fetch full document data
  const queryPromises = publicSharedCollections.map(
    (collection) =>
      admin.firestore().collection(collection).where("uid", "==", userId).get() // Fetch full document data to reparse later
  );

  try {
    const documentsToUpdate = new Map<
      string,
      { ref: admin.firestore.DocumentReference; data: any }
    >();

    const snapshots = await Promise.all(queryPromises);

    // Collect document references and full data
    snapshots.forEach((snapshot, index) => {
      const collection = sharedCollections[index];
      snapshot.docs.forEach((doc) => {
        const data = doc.data();
        const docIdWithCollection = `${collection},${doc.id}`;
        documentsToUpdate.set(docIdWithCollection, {
          ref: doc.ref,
          data: data,
        });
      });
    });

    // Batch update Firestore (limit: 500 per batch)
    const batchSize = 500;
    let batch = admin.firestore().batch();
    let batchCount = 0;
    const batchCommits: Promise<FirebaseFirestore.WriteResult[]>[] = [];

    for (const [, docInfo] of documentsToUpdate.entries()) {
      const updatedData = { ...docInfo.data }; // Clone original data

      // DELETE the fields before reparsing
      delete updatedData.isPublic;
      delete updatedData.publicId;
      delete updatedData.inviteLink;

      // Assign new timestamps
      updatedData.cloudUpdatedAt = null;
      updatedData.localUpdatedAt = getCurrentUTCDate();

      // Reparse the document to assign defaults
      const parsedDoc = convertDatesToTimestamps(getMeParsedData(updatedData));

      // Update Firestore
      batch.update(docInfo.ref, parsedDoc);
      batchCount++;

      if (batchCount === batchSize) {
        batchCommits.push(batch.commit());
        batch = admin.firestore().batch();
        batchCount = 0;
      }
    }
    if (batchCount > 0) {
      batchCommits.push(batch.commit());
    }

    await Promise.all(batchCommits);

    console.log("Docs public sharing info removed for user: ", userId);
  } catch (error) {
    console.error(
      "Error in removing public sharing on user docs: ",
      userId,
      error
    );
  }
}

export async function removePublicProfileShared(userId: string) {
  console.log("Removing public profile shared by user with userId: ", userId);
  const db = admin.firestore();

  // Get user's public profile
  const publicUserSnapshot = await db
    .collection("publicUsers")
    .where("uid", "==", userId)
    .orderBy("cloudUpdatedAt", "desc")
    .limit(1)
    .get();

  try {
    const doc = publicUserSnapshot.docs[0];
    const docId = doc?.id;
    const docData: DocumentData = doc?.data();
    const mevolveId: string | undefined = docData?.mevolveId;

    if (!mevolveId || !docId) {
      console.log(
        `No mevolveId or public user docId found for userId: ${userId} so skipping public profile shared removal.`
      );
      return;
    }

    // Update notes and lists public shared reference on public profile doc
    const publicUserdata = docData as IPublicUsers;
    publicUserdata.publicNotesMetadata = [];
    publicUserdata.publicListsMetadata = [];
    publicUserdata.cloudUpdatedAt = Timestamp.fromMillis(Date.now()).toDate();
    publicUserdata.localUpdatedAt = Timestamp.fromMillis(Date.now()).toDate();

    // Create a reference to the document before updating it
    const publicUserdataRef = db.collection("publicUsers").doc(docId);

    await publicUserdataRef.update(
      convertDatesToTimestamps(getMeParsedData(publicUserdata))
    );

    setPublicUser(new Map(Object.entries(publicUserdata)));

    console.log("Public profile shared removed for user: ", userId);
  } catch (error) {
    console.error(
      "Error in removing public profile shared for user: ",
      userId,
      error
    );
  }
}

export async function removePublicProfileAndRelations(userId: string) {
  console.log(
    "Removing public profile and relations for user with userId: ",
    userId
  );
  const db = admin.firestore();
  const batch = db.batch();

  // Get user's public profile
  const publicUserSnapshot = await db
    .collection("publicUsers")
    .where("uid", "==", userId)
    .orderBy("cloudUpdatedAt", "desc")
    .limit(1)
    .get();

  try {
    const doc = publicUserSnapshot.docs[0];
    const docId = doc?.id;
    const docData: DocumentData = doc?.data();
    const mevolveId: string | undefined = docData?.mevolveId;

    if (!mevolveId || !docId) {
      console.log(
        `No mevolveId or public user docId found for userId: ${userId} so skipping public profile deletion and its relations removal.`
      );
      return;
    }

    // Todo: Update the public profile to remove the public docs then use
    // setPublicUser(new Map(Object.entries(parsedFinalData))); to update the public user
    // Then delete the public profile and relations

    // Delete the public profile
    batch.delete(db.collection("publicUsers").doc(docId));

    // Add to special activities for deletion
    await createSpecialActivitiesDoc(
      userId,
      await getHashedEmailFromUid(userId),
      false,
      docData.docVer,
      "publicUsers",
      [docId],
      batch
    );

    // Handle followers - get all relations where this user is being followed
    const followersSnapshot = await db
      .collection("publicUserRelations")
      .where("followerMevolveId", "==", mevolveId)
      .where("followerId", "==", userId)
      .get();

    // Handle following - get all relations where this user is following others
    const followingSnapshot = await db
      .collection("publicUserRelations")
      .where("mevolveId", "==", mevolveId)
      .where("userId", "==", userId)
      .get();

    // Delete follower relations and update counts
    for (const doc of followersSnapshot.docs) {
      const relation = doc.data();
      batch.delete(doc.ref);

      // Decrement followersCount for the user being followed
      const followedUserDoc = await db
        .collection("publicUsers")
        .where("mevolveId", "==", relation.mevolveId)
        .where("uid", "==", relation.userId)
        .limit(1)
        .get();

      if (!followedUserDoc.empty) {
        const ref = followedUserDoc.docs[0].ref;
        const data = followedUserDoc.docs[0].data();
        batch.update(ref, {
          followers: FieldValue.increment(-1),
          cloudUpdatedAt: Timestamp.now(),
        });
        const updatedData = {
          ...data,
          followers: Math.max(0, (data.followers || 0) - 1),
        };
        console.log("updatedData-followers", updatedData);
        setPublicUser(new Map(Object.entries(updatedData)));
      }
    }

    // Delete following relations and update counts
    for (const doc of followingSnapshot.docs) {
      const relation = doc.data();
      batch.delete(doc.ref);

      // Decrement followingCount for the followed user
      const followerUserDoc = await db
        .collection("publicUsers")
        .where("mevolveId", "==", relation.followerMevolveId)
        .where("uid", "==", relation.followerId)
        .limit(1)
        .get();

      if (!followerUserDoc.empty) {
        const ref = followerUserDoc.docs[0].ref;
        const data = followerUserDoc.docs[0].data();
        batch.update(ref, {
          following: FieldValue.increment(-1),
          cloudUpdatedAt: Timestamp.now(),
        });
        const updatedData = {
          ...data,
          following: Math.max(0, (data.following || 0) - 1),
        };
        console.log("updatedData-following", updatedData);
        setPublicUser(new Map(Object.entries(updatedData)));
      }
    }
    await deletePublicUser(userId);
    // Commit all changes
    await batch.commit();

    console.log("Public profile and relations removed for user: ", userId);
  } catch (error) {
    console.error(
      "Error in removing public profile and relations for user: ",
      userId,
      error
    );
  }
}

function getArrayFieldName(collection: string): string {
  const mapping: { [key: string]: string } = {
    todos: "sharedTodos",
    habitSetups: "sharedHabitSetups",
    journalSetups: "sharedJournalSetups",
    notes: "sharedNotes",
    lists: "sharedLists",
    moneyTrackerSetups: "sharedMoneyTrackerSetups",
  } as const;

  return mapping[collection as keyof typeof mapping] || "";
}

export const sharedCollections = [
  "todos",
  "habitSetups",
  "habitActions",
  "journalSetups",
  "journalActions",
  "notes",
  "lists",
  "moneyTrackerSetups",
  "moneyTrackerTransactions",
];

export const publicSharedCollections = ["notes", "lists"];
