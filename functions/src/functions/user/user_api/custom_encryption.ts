import * as admin from "firebase-admin";

import {
  decryptTextData,
  encryptTextData,
  getSecretFromKmsForUserByEmail,
  getUserKeyDocByEmail,
} from "../../../utils/encryption/encryption";
import { MeLogger } from "../../../utils/logger/models/me_logger";
import { IUser } from "../../migration/versions/base/model_mappings";
import { convertDatesToTimestamps } from "../../../utils/utility_methods";

export async function changeUserEncKey(
  enableCustomEncKey: boolean,
  userKeyInBase64: string,
  email: string,
  logger: MeLogger
) {
  logger.log("Changing encryption key for user: " + email);
  const userKeyDoc = await getUserKeyDocByEmail(email);
  if (!userKeyDoc) {
    return Promise.reject(Error("User key doc not found"));
  }
  console.log("User key doc found: ", userKeyInBase64);
  const encryptedPvtKey = userKeyDoc.encPrivateKey;
  const kmsSecret = (await getSecretFromKmsForUserByEmail(email)).key;
  if (enableCustomEncKey) {
    const decryptedPrivateKey = await decryptTextData(
      encryptedPvtKey,
      kmsSecret
    );
    const encryptedPrivateKey = encryptTextData(
      decryptedPrivateKey,
      userKeyInBase64
    );
    userKeyDoc.encPrivateKey = encryptedPrivateKey;
  } else {
    const decryptedPrivateKey = await decryptTextData(
      encryptedPvtKey,
      userKeyInBase64
    );
    const encryptedPrivateKey = encryptTextData(decryptedPrivateKey, kmsSecret);
    userKeyDoc.encPrivateKey = encryptedPrivateKey;
  }

  userKeyDoc.privateKeyPasswordType = enableCustomEncKey ? "CUSTOM" : "KMS";
  userKeyDoc.cloudUpdatedAt = null;
  if (userKeyDoc.uid != null) {
    const db = admin.firestore();
    const docRef = db.collection("users").doc(userKeyDoc.uid);
    await db.runTransaction(async (transaction) => {
      const doc = await transaction.get(docRef);
      if (!doc.exists) {
        throw new Error("Document does not exist!");
      }
      const userDoc = doc.data() as IUser;
      userDoc.userInfo.isUsingCustomKey = enableCustomEncKey;
      transaction.update(
        admin.firestore().collection("userKeys").doc(userKeyDoc.id),
        convertDatesToTimestamps(userKeyDoc)
      );
      userDoc.cloudUpdatedAt = null;
      transaction.update(docRef, convertDatesToTimestamps(userDoc));
    });

    return Promise.resolve({ result: "success" });
  } else {
    return Promise.reject(Error("User key doc not found"));
  }
}
