import * as functions from "firebase-functions/v2";
import {
  addUserDeletionReason,
  deleteUser,
  deleteUserData,
  getUserData,
  updateSupportCollections,
} from "./user_api/get_user";
import { downgradeUserData } from "./user_api/downgrade_user";
import {
  currentDbVersion,
  getMeParsedData,
  IFeedback,
} from "../migration/versions/base/model_mappings";
import * as admin from "firebase-admin";
import { MeLogger } from "../../utils/logger/models/me_logger";
import { changeUserEncKey } from "./user_api/custom_encryption";
import { FieldValue } from "firebase-admin/firestore";
import { logoutUserFromAllDevices } from "../login/sign_in";
import { refreshBackupForUserById } from "../account_backup/user_data_backup/weekly_account_backups";
import { IAppLanguage } from "../migration/versions/base/base_schema";
import { sendSingleEmailViaSes } from "../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../utils/emails/email_helper";
import {
  getViewSettingsByUserId,
  getAppLink,
  getSupportLink,
} from "../../utils/utility_methods";
import { onMessagePublished } from "firebase-functions/v2/pubsub";
import { DbRepository } from "../save_data/db.repository";

export const get = functions.https.onCall(
  {
    memory: "512MiB",
    concurrency: 20,
  },
  async (request) => {
    const sessionId = request.data.sessionId;
    const userLanguage = request.data.language as IAppLanguage | null;
    const timeZone = request.data.timeZone;
    console.log("userLanguage: ", userLanguage);
    const logger = new MeLogger(request.auth?.uid, sessionId);
    logger.log("Creating/getting user: " + request.auth?.uid);
    const uid = request.auth?.uid;
    if (uid == null) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "uid of user not found"
      );
    }
    const email = request.auth?.token.email;
    if (email == null) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "email of user not found"
      );
    }
    const displayName = request.data.displayName as string | null;
    return getUserData(
      uid,
      displayName,
      email,
      userLanguage ?? "english",
      timeZone,
      logger
    );
  }
);

export const sendWelcomeMail = functions.https.onCall(async (request) => {
  const sessionId = request.data.sessionId;
  const uid = request.auth?.uid as string;
  const logger = new MeLogger(uid, sessionId);
  logger.log("Sending welcome mail for user with id: " + uid);
  const email = request.auth?.token.email;
  const displayName = request.data.displayName as string | null;
  if (email == null || displayName == null) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "email or name of user not found"
    );
  }
  const viewSettings = await getViewSettingsByUserId(uid);
  const language = viewSettings?.appSettings.language;
  await sendSingleEmailViaSes({
    templateName: MevolveEmails.welcomeUser,
    recipient: email,
    templateData: {
      name: displayName ?? "",
      appLink: getAppLink(),
      linkToSupportChat: getSupportLink(),
    },
    language: language,
  });
});

export const changeEncKey = functions.https.onCall(
  {
    memory: "512MiB",
  },
  async (request) => {
    const sessionId = request.data.sessionId;
    const logger = new MeLogger(request.auth?.uid, sessionId);
    logger.log("Changing encryption key for user: " + request.auth?.uid);
    const userKey = request.data.userKey;
    const email = request.auth?.token.email;
    if (email == null) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "email of user not found"
      );
    }
    return changeUserEncKey(
      request.data.enableCustomEncKey,
      userKey,
      email,
      logger
    );
  }
);

export const downgrade = functions.https.onCall(
  {
    memory: "1GiB",
    concurrency: 2,
    timeoutSeconds: 540,
  },
  async (request) => {
    const sessionId = request.data.sessionId;
    const logger = new MeLogger(request.auth?.uid, sessionId);
    logger.log(
      "downgrading user: " +
        request.auth?.uid +
        " to version: " +
        request.data.migrateToVersion
    );
    const uid = request.auth?.uid;
    if (uid == null) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "uid of user not found"
      );
    }
    return downgradeUserData(uid, request.data.migrateToVersion, logger);
  }
);

// export const assignPseudoNames = functions.https.onRequest(
//   { enforceAppCheck: false },
//   async (_request, response) => {
//     const db = admin.firestore();
//     response.send("Success");
//     functions.logger.info("User AssignPseudoName started");
//     const getUserDataQuery = db.collection("users");
//     const dataSnapshot = await getUserDataQuery.get();
//     functions.logger.info(`length = ${dataSnapshot.docs.length}`);
//     for (const document of dataSnapshot.docs) {
//       const index = dataSnapshot.docs.indexOf(document);
//       const userDoc = { ...(document.data() as unknown as IUser) };
//       const str = "" + (index + 1);
//       const userInfoMap = {
//         ...userDoc.userInfo,
//         pseudoName: str,
//       };
//       await getUserDataQuery.doc(userDoc.uid).set({
//         ...userDoc,
//         userInfo: userInfoMap,
//       });
//       const metaRef = await db.collection("usersMetadata").doc(userDoc.uid);
//       const doc = await metaRef.get();
//       const userMetadataDoc = doc.data() as IUserMetadata;
//       await metaRef.set({
//         ...userMetadataDoc,
//         userInfo: userInfoMap,
//       });
//     }
//     const countRef = db.collection("serverMetadata").doc("appMetadata");
//     await countRef.update({
//       usersCount: dataSnapshot.docs.length,
//     });
//   }
// );

export const assignPseudoNames = functions.https.onRequest(
  // Check Function: No use found of this fucntion. Maybe used in support app previously. Check it's usage frequncy and remove if not needed.
  { enforceAppCheck: false },
  async (_request, response) => {
    const db = admin.firestore();
    const batch = db.batch();
    response.send("Success");
    functions.logger.info("User AssignPseudoName started");
    const getUserDataQuery = db.collection("usersFeedback");
    const dataSnapshot = await getUserDataQuery.get();
    functions.logger.info(`length = ${dataSnapshot.docs.length}`);
    for (const document of dataSnapshot.docs) {
      const feedbackDoc = { ...(document.data() as unknown as IFeedback) };
      // const metaRef = db.collection("usersMetadata").doc(feedbackDoc.uid);
      // const doc = await metaRef.get();
      // const userMetadataDoc = doc.data() as IUserMetadata;
      const userInfoMap = {
        ...feedbackDoc,
        // uname: userMetadataDoc.userInfo.pseudoName,
        userInfo: FieldValue.delete(),
      };
      batch.set(
        getUserDataQuery.doc(document.id),
        {
          ...userInfoMap,
        },
        { merge: true }
      );
    }
    await batch.commit();
  }
);

export const deleteData = functions.https.onCall(async (request) => {
  const sessionId = request.data.sessionId;
  const logger = new MeLogger(request.auth?.uid, sessionId);
  logger.log("deleting user complete data: " + request.data.uid);
  await deleteUserData(request.data.uid, logger);
  await refreshBackupForUserById(request.data.uid);
  await logoutUserFromAllDevices(request.data.uid);
  logger.log("deleting user complete data: " + request.data.uid + " done");
  return { result: "success" };
});

export const deletePermanently = functions.https.onCall(
  {
    memory: "1GiB",
    concurrency: 2,
    timeoutSeconds: 540,
  },
  async (request) => {
    const sessionId = request.data.sessionId;
    const logger = new MeLogger(request.auth?.uid, sessionId);
    logger.log("deleting user: " + request.data.uid);
    await deleteUser(request.data.uid, logger);
    try {
      await addUserDeletionReason(
        request.data.uid,
        request.data.reason,
        request.data.feedback,
        request.data.uname
      );
    } catch (e) {
      logger.error("Error while adding user deletion reason: " + e);
    }
    try {
      await updateSupportCollections(
        request.data.uid,
        request.data.uname,
        false
      );
    } catch (e) {
      logger.error(
        "Error while updating support collections for user deletion: " + e
      );
    }
    return { result: "success" };
  }
);

exports.updateWithVersion = onMessagePublished(
  {
    topic: `updateWithVersion_v${currentDbVersion}`,
  },
  async (event) => {
    functions.logger.info("Updating user with version");
    const data = event.data.message.json;
    const userData = getMeParsedData(JSON.parse(data.userData));
    functions.logger.info("Updating user with version: ", userData.docVer);
    userData.cloudUpdatedAt = null;
    await new DbRepository().updateData(getMeParsedData(userData));
  }
);
