import * as functions from "firebase-functions/v2";
import * as admin from "firebase-admin";
import { appCheckConfig } from "../..";

export const fetchAppCheckToken = functions.https.onCall(async (request) => {
  const appId = request.data.appId;

  if (!appId) {
    throw new functions.https.HttpsError("invalid-argument", "App ID is required");
  }

  // If appCheckConfig is false, then no need to give App Check token. Return null token.
  if (appCheckConfig === false) {
    return { token: null, expiresAt: null };
  }

  try {
    const appCheckToken = await admin.appCheck().createToken(appId);
    const expiresAt = Math.floor(Date.now() / 1000) + 60 * 60;
    return { token: appCheckToken.token, expiresAt: expiresAt };
  } catch (err) {
    console.error("Unable to create App Check token:", err);
    throw new functions.https.HttpsError("permission-denied", "Unauthorized");
  }
});