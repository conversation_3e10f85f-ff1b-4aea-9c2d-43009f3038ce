import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v2";
import Translate = require("@google-cloud/translate");
import { Timestamp, WriteResult } from "firebase-admin/firestore";
import {
  IChatMessages,
  ZChatMessages,
} from "../migration/versions/base/model_mappings";
import {
  convertDatesToTimestamps,
  getCurrentUTCDate,
} from "../../utils/utility_methods";

const translate = new Translate.v2.Translate();

export const translateToolMsg = functions.https.onCall(async (request) => {
  // async function translateText(text: string, chatMsg: string, target: string,): Promise<string> {
  const text = request.data.text;
  const chatMsg = request.data.chatMsg;
  const target = request.data.target;
  try {
    const db = admin.firestore();
    const chatMsgObj = ZChatMessages.parse(JSON.parse(chatMsg));
    const [translatedText] = await translate.translate(text, {
      from: "en",
      to: getLanguageCode(target),
    });
    console.log("tr: " + translatedText);
    const docRef = db.collection("chatMessages").doc(chatMsgObj.id);
    const updatedMsg = updateMsgForLang(
      getLanguageCode(target),
      translatedText,
      chatMsgObj
    );
    updatedMsg.localUpdatedAt = chatMsgObj.localUpdatedAt;
    docRef.set(
      {
        // Preserve original localUpdatedAt for proper message ordering
        localUpdatedAt: chatMsgObj.localUpdatedAt,
        cloudUpdatedAt: Timestamp.now(),
        message: updatedMsg.message,
      },
      { merge: true }
    );
    return {
      statusCode: 200,
    };
  } catch (error) {
    console.log(
      "Failed to translate text: " +
        text +
        " to lang: " +
        target +
        " with error: " +
        error
    );
    return {
      statusCode: 500,
      error: "" + error,
    };
  }
});

export const translateToSupportLang = functions.https.onCall(
  async (request) => {
    const dataMap: Map<string, string> = new Map(Object.entries(request.data));
    if (
      request.auth?.uid == dataMap.get("uid") &&
      dataMap.has("uid") &&
      dataMap.get("target")
    ) {
      const uid = dataMap.get("uid");
      const target = dataMap.get("target") as string;
      try {
        const messages: string[] = [];
        const chatMessages: IChatMessages[] = [];
        const db = admin.firestore();
        const batch = db.batch();
        const dataSnapshot = await db
          .collection("chatMessages")
          .where("uid", "==", uid)
          .get();
        if (dataSnapshot.docs.length > 0) {
          dataSnapshot.forEach((doc) => {
            const chatMsg = doc.data() as unknown as IChatMessages;
            const msg = getMsgFromLang(target, chatMsg);
            if (msg.length == 0) {
              const ogMsg = getMsgFromLang(
                getLanguageCode(chatMsg.supportLanguage),
                chatMsg
              );
              messages.push(ogMsg);
              chatMessages.push(chatMsg);
            }
          });
          if (messages.length > 0) {
            const [translatedText] = await translate.translate(
              messages,
              target
            );
            chatMessages.forEach((chatMsg, i) => {
              const docRef = db.collection("chatMessages").doc(chatMsg.id);
              const updatedMsg = updateMsgForLang(
                target,
                translatedText[i],
                chatMsg
              );
              updatedMsg.localUpdatedAt = chatMsg.localUpdatedAt;
              batch.update(docRef, convertDatesToTimestamps(updatedMsg));
            });
            await batch.commit();
          }
        }
        return true;
      } catch (e) {
        console.log(
          "Failed to translate msg to lang " +
            target +
            " for user " +
            uid +
            " with error: " +
            e
        );
        return false;
      }
    } else {
      return false;
    }
  }
);

export const batchTranslateText = functions.https.onCall(async (request) => {
  const dataMap: Map<string, string> = new Map(Object.entries(request.data));
  if (
    request.auth?.uid == dataMap.get("uid") &&
    dataMap.has("uid") &&
    dataMap.get("target")
  ) {
    const uid = dataMap.get("uid");
    const target = dataMap.get("target") as string;
    try {
      const messages: string[] = [];
      const chatMessages: IChatMessages[] = [];
      const db = admin.firestore();
      const dataSnapshot = await db
        .collection("chatMessages")
        .where("uid", "==", uid)
        .get();
      if (dataSnapshot.docs.length > 0) {
        dataSnapshot.forEach((doc) => {
          const chatMsg = doc.data() as unknown as IChatMessages;
          const msg = getMsgFromLang(target, chatMsg);
          if (msg.length == 0) {
            const ogMsg = getMsgFromLang(
              getLanguageCode(chatMsg.supportLanguage),
              chatMsg
            );
            messages.push(ogMsg);
            chatMessages.push(chatMsg);
          }
        });
        if (messages.length > 0) {
          const [translatedText] = await translate.translate(messages, target);
          const tasks: Promise<WriteResult>[] = [];
          chatMessages.forEach((chatMsg, i) => {
            const docRef = db.collection("chatMessages").doc(chatMsg.id);
            const updatedMsg = updateMsgForLang(
              target,
              translatedText[i],
              chatMsg
            );
            updatedMsg.localUpdatedAt = chatMsg.localUpdatedAt;
            tasks.push(docRef.update(convertDatesToTimestamps(updatedMsg)));
          });
          await Promise.allSettled(tasks);
        }
      }
      return true;
    } catch (e) {
      console.log(
        "Failed to translate batch msgs to lang " +
          target +
          " for user " +
          uid +
          " with error: " +
          e
      );
      return false;
    }
  } else {
    return false;
  }
});

export async function translateToEnglish(
  chatMsg: IChatMessages,
  from: string
): Promise<string> {
  const code = getLanguageCode(chatMsg.supportLanguage);
  const text = getMsgFromLang(code, chatMsg);
  const [translatedText] = await translate.translate(text, {
    from: getLanguageCode(from),
    to: "en",
  });
  return translatedText;
}

function getLanguageCode(language: string): string {
  switch (language) {
    case "english":
      return "en";
    case "french":
      return "fr";
    case "german":
      return "de";
    case "italian":
      return "it";
    case "portuguese":
      return "pt";
    case "spanish":
      return "es";
    default:
      return "en";
  }
}

function getMsgFromLang(languageCode: string, chatMsg: IChatMessages): string {
  switch (languageCode) {
    case "en":
      return chatMsg.message.en ?? "";
    case "fr":
      return chatMsg.message.fr ?? "";
    case "de":
      return chatMsg.message.de ?? "";
    case "it":
      return chatMsg.message.it ?? "";
    case "pt":
      return chatMsg.message.pt ?? "";
    case "es":
      return chatMsg.message.es ?? "";
    default:
      return chatMsg.message.en ?? "";
  }
}

function updateMsgForLang(
  languageCode: string,
  message: string,
  chatMsg: IChatMessages
): IChatMessages {
  chatMsg.cloudUpdatedAt = getCurrentUTCDate();
  // Don't modify localUpdatedAt to preserve message ordering
  switch (languageCode) {
    case "en":
      chatMsg.message.en = message;
      return chatMsg;
    case "fr":
      chatMsg.message.fr = message;
      return chatMsg;
    case "de":
      chatMsg.message.de = message;
      return chatMsg;
    case "it":
      chatMsg.message.it = message;
      return chatMsg;
    case "pt":
      chatMsg.message.pt = message;
      return chatMsg;
    case "es":
      chatMsg.message.es = message;
      return chatMsg;
    default:
      chatMsg.message.en = message;
      return chatMsg;
  }
}
