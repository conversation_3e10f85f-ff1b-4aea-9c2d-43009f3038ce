import * as functions from "firebase-functions/v2";
import { getTranslationReportBody } from "../../utils/db_status/db_status_report";
import { sendSingleEmailWithHtmlBody } from "../../utils/emails/aws_ses_email";

export const sendStatusEmail = functions.https.onRequest(
  // Check Function: Used for sending translation status email for internal use.
  { enforceAppCheck: false },
  async (request, response) => {
    if (request.body.secretToken != null) {
      if (request.body.tranReport == "true") {
        const body = getTranslationReportBody(
          request.body.body,
          request.body.varBody,
          request.body.count,
          request.body.varCount
        );
        sendSingleEmailWithHtmlBody(
          ["<EMAIL>"],
          request.body.subject,
          body
        );
      } else {
        sendSingleEmailWithHtmlBody(
          ["<EMAIL>"],
          request.body.subject,
          request.body.body
        );
      }
      response.status(200).send("Success");
    } else {
      response.status(401).send("Unauthorized");
    }
  }
);
