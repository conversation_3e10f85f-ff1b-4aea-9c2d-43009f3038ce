import * as functions from "firebase-functions/v2";
import * as admin from "firebase-admin";
import { getMeBaseDomain } from "../../utils/utility_methods";

export const createRawPublicLink = (collection: string, id: string): string => {
  return `/public?collection=${collection}&id=${id}`;
};

export const redirect = functions.https.onRequest(
  // Check Function: No use found rightnow, previously might have been used for redirecting user to a task from invite link.
  { enforceAppCheck: false },
  async (request, response) => {
    const collection =
      request.path.split("/")[1] == "user"
        ? "publicUsers"
        : request.path.split("/")[1];

    const code =
      collection == "publicUsers"
        ? request.path.split("/")[2]
        : collection + "/" + request.path.split("/")[2];

    const featureCollection = admin.firestore().collection(collection);
    console.log("code", code);
    // check if short link is for list or invited user
    const collectionData =
      collection == "publicUsers"
        ? await featureCollection.where("mevolveId", "==", code).get()
        : await featureCollection.where("publicId", "==", code).get();
    if (!collectionData.empty) {
      const userId = collectionData.docs[0].data().uid;
      const rawLink =
        collection == "publicUsers"
          ? `/public?collection=publicUsers&id=${userId}`
          : collectionData.docs[0].data().inviteLink;
      if (rawLink.includes("http://") || rawLink.includes("https://")) {
        response.redirect(collectionData.docs[0].data().inviteLink);
      } else {
        const baseUrl = getMeBaseDomain();
        if (collection == "publicUsers") {
          response.redirect("https://" + baseUrl + rawLink);
        } else {
          response.redirect(
            "https://" + baseUrl + collectionData.docs[0].data().inviteLink
          );
        }
      }
      return;
    }

    // const hasUid = await getUserByEmailFromAuthService(
    //   invitedUserSnapshot.docs[0].data().email
    // );
    // if (hasUid != undefined) {
    //   await acceptInvitationFunction({
    //     userId: hasUid.uid,
    //     email: invitedUserSnapshot.docs[0].data().email,
    //     name: invitedUserSnapshot.docs[0].data().invitedByName,
    //     inviteAcceptedVia: "email",
    //     inviteId: invitedUserSnapshot.docs[0].id,
    //   });
    // }

    // if (!invitedUserSnapshot.empty) {
    //   const rawLink = invitedUserSnapshot.docs[0].data().inviteLink;
    //   const acceptedQuery = hasUid != undefined ? "&accepted=true" : "";
    //   if (rawLink.includes("http://") || rawLink.includes("https://")) {
    //     response.redirect(
    //       invitedUserSnapshot.docs[0].data().inviteLink + acceptedQuery
    //     );
    //   } else {
    //     const baseUrl = getMeBaseDomain();
    //     response.redirect(
    //       "https://" +
    //         baseUrl +
    //         invitedUserSnapshot.docs[0].data().inviteLink +
    //         acceptedQuery
    //     );
    //   }
    //   return;
    // }

    response.json({ error: "Not found" });
  }
);
