export type LanguageCollaboration =
  | "english"
  | "french"
  | "german"
  | "italian"
  | "portuguese"
  | "spanish";
export type CollectionCollaboration =
  | "notes"
  | "habitSetups"
  | "journalSetups"
  | "moneyTrackerSetups"
  | "todos";

export type EntityTranslations = {
  /* eslint-disable no-unused-vars */
  [K in LanguageCollaboration]: {
    [C in CollectionCollaboration]: string;
  };
  /* eslint-disable no-unused-vars */
};

export const entityTranslations: EntityTranslations = {
  english: {
    notes: "note",
    habitSetups: "habit",
    journalSetups: "journal",
    moneyTrackerSetups: "money tracker",
    todos: "to-do",
  },
  french: {
    notes: "note",
    habitSetups: "habitude",
    journalSetups: "journal",
    moneyTrackerSetups: "suivi des dépenses",
    todos: "Faire",
  },
  german: {
    notes: "Notiz",
    habitSetups: "Gewohnheit",
    journalSetups: "Tagebuch",
    moneyTrackerSetups: "Geldverfolger",
    todos: "Aufgaben",
  },
  italian: {
    notes: "nota",
    habitSetups: "abitudine",
    journalSetups: "diario",
    moneyTrackerSetups: "tracciatore di denaro",
    todos: "Fare",
  },
  portuguese: {
    notes: "nota",
    habitSetups: "hábito",
    journalSetups: "diário",
    moneyTrackerSetups: "rastreador de dinheiro",
    todos: "Pendência",
  },
  spanish: {
    notes: "nota",
    habitSetups: "hábito",
    journalSetups: "diario",
    moneyTrackerSetups: "rastreador de dinero",
    todos: "Hacer",
  },
};

export function translateEntity(
  language: LanguageCollaboration,
  collection: CollectionCollaboration
): string {
  return entityTranslations[language][collection];
}
export type SharedType = {
  sharedLists: string[];
  sharedNotes: string[];
  sharedTodos: string[];
  sharedMoneyTrackerSetups: string[];
  sharedHabitSetups: string[];
  sharedJournalSetups: string[];
};
