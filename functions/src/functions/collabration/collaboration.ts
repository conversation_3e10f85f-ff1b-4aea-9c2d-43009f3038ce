import * as admin from "firebase-admin";
import { Timestamp, FieldValue } from "firebase-admin/firestore";
import * as functions from "firebase-functions/v2";
import { v1 as uuidv1 } from "uuid";
import { meCollectionNames } from "../migration/versions/base/base_schema";
import {
  currentDbVersion,
  getMeParsedData,
  IPublicUsers,
  IUserKeys,
} from "../migration/versions/base/model_mappings";
import {
  encryptTextData,
  getSecretFromKmsForUserByEmail,
  generateECCKeyPair,
  hashData,
  getNewSecret,
  encryptDocKey,
} from "../../utils/encryption/encryption";
import {
  convertDatesToTimestamps,
  getUserDbVersion,
  getMeBaseDomain,
  getSupportLink,
  getUuidFromHashedEmail,
  getViewSettingsByUserId,
  getUserByEmailFromAuthService,
} from "../../utils/utility_methods";
import { MeLogger } from "../../utils/logger/models/me_logger";
import { createRawPublicLink } from "./invite";
import { setPublicUser } from "../../services/cloudfare/user.service";
import { sendSingleEmailViaSes } from "../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../utils/emails/email_helper";
import {
  CollectionCollaboration,
  LanguageCollaboration,
  translateEntity,
} from "./constants";

export const enum ActionUpdationType {
  /* eslint-disable no-unused-vars */
  Addition,
  Deletion,
  LeaftOrBlock,
  /* eslint-enable no-unused-vars */
}

export const updateListWithShortLink = functions.https.onCall(
  async (request) => {
    const { id, ownerNameOrEmail, collection } = request.data;
    const db = admin.firestore();
    const docCollection: (typeof meCollectionNames)[number] = collection;
    const inviteLink = createRawPublicLink(docCollection, id);
    const publicIdInitial = await createShortLink(ownerNameOrEmail);
    const publicId = collection + "/" + publicIdInitial;
    if (docCollection == "lists") {
      const listsCollection = db.collection("lists");
      const listData = (await listsCollection.doc(id).get()).data();
      if (!listData) {
        throw new Error("List not found");
      }
      return { message: "success", publicId: publicId, inviteLink: inviteLink };
    } else if (docCollection == "notes") {
      const notesCollection = db.collection("notes");
      const noteData = (await notesCollection.doc(id).get()).data();
      if (!noteData) {
        throw new Error("note not found");
      }
      return { message: "success", publicId: publicId, inviteLink: inviteLink };
    } else {
      throw new Error("Invalid collection");
    }
  }
);

export const removeCollaborator = functions.https.onCall(async (request) => {
  const { id, hashedEmail, collection } = request.data;
  const db = admin.firestore();

  // Get user data
  const uuidOfHashedEmail = await getUuidFromHashedEmail(hashedEmail);
  // if uid is null, that mean user does not exits our app
  if (uuidOfHashedEmail === null || uuidOfHashedEmail === undefined) {
    await removeUserFromMemberConfigForCollection(collection, id, hashedEmail);
    // check if userKeys contains the emailHash and id to remove the id from sharedTasks
    const userKeysRef = db
      .collection("userKeys")
      .where("emailHash", "==", hashedEmail);

    const userKeysSnapshot = await userKeysRef.get();
    if (!userKeysSnapshot.empty) {
      const userKeysData = userKeysSnapshot.docs[0].data();
      if (userKeysData) {
        const sharedArrayKey = `shared.shared${capitalizeFirst(collection)}`;
        const sharedArray =
          userKeysData.shared[`shared${capitalizeFirst(collection)}`];
        const itemIndex = sharedArray.indexOf(id);

        if (itemIndex > -1) {
          sharedArray.splice(itemIndex, 1);
          await db
            .collection("userKeys")
            .doc(userKeysSnapshot.docs[0].id)
            .update({
              [sharedArrayKey]: sharedArray,
              cloudUpdatedAt: Timestamp.now(),
              localUpdatedAt: Timestamp.now(),
            });
          console.log("removed shared from userKeys");
        }
      }
    }
    return;
  }
  const userResorcesDoc = uuidOfHashedEmail
    ? await db
        .collection("userResources")
        .where("uid", "==", uuidOfHashedEmail)
        .get()
    : null;
  if (
    userResorcesDoc === null ||
    userResorcesDoc === undefined ||
    userResorcesDoc.empty
  ) {
    throw new Error("UserResorceDoc not found with uid " + uuidOfHashedEmail);
  }
  // is null or undefined or empty
  const userData = userResorcesDoc?.docs[0]?.data();
  if (!userData) {
    throw new Error("User not found");
  }

  await removeUserFromMemberConfigForCollection(collection, id, hashedEmail);

  // Update user's shared items if user exists
  if (userData && uuidOfHashedEmail) {
    const sharedArrayKey = `shared.shared${capitalizeFirst(collection)}`;
    const sharedArray = userData.shared[`shared${capitalizeFirst(collection)}`];
    const itemIndex = sharedArray.indexOf(id);

    if (itemIndex > -1) {
      sharedArray.splice(itemIndex, 1);
      await db
        .collection("userResources")
        .doc(userResorcesDoc.docs[0].id)
        .update({
          [sharedArrayKey]: sharedArray,
          cloudUpdatedAt: Timestamp.now(),
          localUpdatedAt: Timestamp.now(),
        });
    }
  }
});

function capitalizeFirst(str: string): string {
  return str.charAt(0).toUpperCase() + str.slice(1);
}

exports.leaveOrBlockTask = functions.https.onCall(async (request) => {
  const { taskId, userId, collection, leaveType, hashedEmail } = request.data;
  const db = admin.firestore();
  // Input validation
  if (!taskId || !userId || !collection || !leaveType || !hashedEmail) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Missing required parameters"
    );
  }

  // Validate collection name
  const validCollections = [
    "moneyTrackerSetups",
    "todos",
    "lists",
    "notes",
    "habitSetups",
    "journalSetups",
  ];
  if (!validCollections.includes(collection)) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Invalid collection name"
    );
  }

  try {
    // Run all operations in a transaction to ensure data consistency
    await db.runTransaction(async (transaction) => {
      const userResourceRef = db
        .collection("userResources")
        .where("uid", "==", userId);
      const userResourceDoc = await transaction.get(userResourceRef);
      if (!userResourceDoc.docs[0]) {
        throw new functions.https.HttpsError(
          "not-found",
          "User Resource document not found"
        );
      }

      // Get task document reference
      const taskRef = db.collection(collection).doc(taskId);
      const taskDoc = await transaction.get(taskRef);

      if (!taskDoc.exists) {
        throw new functions.https.HttpsError(
          "not-found",
          "Task document not found"
        );
      }

      // Update user's shared collection
      const userData = userResourceDoc.docs[0].data();
      const sharedData = userData?.shared || {};

      // Determine which shared array to update based on collection
      let sharedArrayName;
      switch (collection) {
        case "todos":
          sharedArrayName = "sharedTodos";
          break;
        case "moneyTrackerSetups":
          sharedArrayName = "sharedMoneyTrackerSetups";
          break;
        case "habitSetups":
          sharedArrayName = "sharedHabitSetups";
          break;
        case "journalSetups":
          sharedArrayName = "sharedJournalSetups";
          break;
        case "lists":
          sharedArrayName = "sharedLists";
          break;
        case "notes":
          sharedArrayName = "sharedNotes";
          break;
      }

      if (sharedArrayName === undefined) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Invalid collection name"
        );
      }

      // Remove taskId from user's shared collection
      if (sharedData[sharedArrayName]) {
        const updatedSharedArray = sharedData[sharedArrayName].filter(
          (id: string) => id !== taskId
        );

        transaction.update(userResourceDoc.docs[0].ref, {
          [`shared.${sharedArrayName}`]: updatedSharedArray,
          cloudUpdatedAt: Timestamp.now(),
          localUpdatedAt: Timestamp.now(),
        });
      }

      // Update task's members
      const taskData = taskDoc.data();
      const members = taskData?.members || {
        memberHashedEmails: [],
        membersConfig: {},
      };

      // Remove hashedEmail from memberHashedEmails array
      const updatedMemberEmails = members.memberHashedEmails.filter(
        (email: string) => email !== hashedEmail
      );

      // Update member status in membersConfig
      if (members.membersConfig && members.membersConfig[hashedEmail]) {
        members.membersConfig[hashedEmail].status = leaveType;
      }

      // Update task document
      transaction.update(taskRef, {
        "members.memberHashedEmails": updatedMemberEmails,
        [`members.membersConfig.${hashedEmail}.status`]: leaveType,
        cloudUpdatedAt: Timestamp.now(),
        localUpdatedAt: Timestamp.now(),
      });

      // Update actions if needed
      const collectionHasActions: boolean =
        collection !== "todos" &&
        collection !== "lists" &&
        collection !== "notes";

      if (collectionHasActions) {
        const actionCollection =
          getActionCollectionFromSetupCollection(collection);
        if (actionCollection) {
          console.log("Updating actions for task", taskId);
          await updateActionsInBatches(
            db,
            actionCollection,
            taskId,
            hashedEmail,
            {
              role: taskData!.members.membersConfig[hashedEmail].role,
              status: leaveType,
              hashedEmail: hashedEmail,
              dek: taskData!.members.membersConfig[hashedEmail].dek,
              eEmail: taskData!.members.membersConfig[hashedEmail].eEmail,
            },
            ActionUpdationType.LeaftOrBlock
          );
        }
      }
    });

    return { success: true };
  } catch (error) {
    console.error("Error in leaveOrBlockTask:", error);
    throw new functions.https.HttpsError(
      "internal",
      "Error processing request",
      error
    );
  }
});

const createShortLink = async (nameOrEmail: string) => {
  const db = admin.firestore();
  const utilityCollectionRef = db.collection("utility");
  const shortLinksDocRef = utilityCollectionRef.doc("shortLinkNumber");
  const PAD_CHAR = "X"; // Define pad character

  // Ensure 'utility' collection has the tracking document
  const utilityCollectionExists =
    (await utilityCollectionRef.limit(1).get()).size > 0;
  if (!utilityCollectionExists) {
    await shortLinksDocRef.set({ number: 1000 });
  }

  // Remove all spaces
  let sanitizedInput = nameOrEmail.replace(/\s+/g, "");

  // Ensure at least two characters
  sanitizedInput =
    sanitizedInput.length >= 2
      ? sanitizedInput.slice(0, 2)
      : sanitizedInput.padEnd(2, PAD_CHAR);

  return db.runTransaction(async (transaction) => {
    const shortLinkNumberDoc = await transaction.get(shortLinksDocRef);
    const shortLinkNumber = shortLinkNumberDoc.data()?.number;

    if (shortLinkNumber === undefined) {
      throw new Error("Short link number is undefined.");
    }

    const newShortLinkNumber = shortLinkNumber + 1;
    transaction.update(shortLinksDocRef, { number: newShortLinkNumber });

    return sanitizedInput + newShortLinkNumber;
  });
};

async function getUserKMSSecret(email: string): Promise<string> {
  const userSecret = (await getSecretFromKmsForUserByEmail(email)).key;
  return userSecret;
}

async function createUserKeyDoc(email: string): Promise<IUserKeys> {
  try {
    const eccKeyPair = generateECCKeyPair();
    const emailHash: string = hashData(email);
    const userSecret: string = await getUserKMSSecret(email);
    const userKey: IUserKeys = {
      id: uuidv1(),
      uid: null,
      email: email,
      emailHash: emailHash,
      encPrivateKey: encryptTextData(eccKeyPair.privateKey, userSecret),
      publicKey: eccKeyPair.publicKey,
      privateKeyPasswordType: "KMS",
      cloudUpdatedAt: null,
    };
    await admin
      .firestore()
      .collection("userKeys")
      .doc(userKey.id)
      .set(convertDatesToTimestamps(userKey));
    return userKey;
  } catch (error) {
    console.log("Error in createUserKeyDoc", error);
    throw error;
  }
}

export const fetchUserKey = functions.https.onCall(
  { memory: "512MiB" },
  async (request) => {
    const logger = new MeLogger(request.auth?.uid);
    console.log("FETCHIUNG USER KEY");
    try {
      const data = request.data;
      const email = data.email;
      // check if user key is already present in userKeys collection
      const userKeyCollection = admin.firestore().collection("userKeys");
      const userKeySnapshot = await userKeyCollection
        .where("email", "==", email)
        .get();
      
      if (!userKeySnapshot.empty) {
        const userKeyData = userKeySnapshot.docs[0].data() as IUserKeys;
        console.log("USER KEY ALREADY EXISTS");
        logger.info("User key already exists for email: " + email);
        return { data: userKeyData };
      }
      
      const userKey: IUserKeys = await createUserKeyDoc(email);
      console.log("USER KEY CREATED");
      logger.info("User key created for email: " + email);
      return { data: userKey };
    } catch (error) {
      logger.error(error);
      throw error;
    }
  }
);

export const addMemberToTask = functions.https.onCall(async (request) => {
  const {
    collection,
    setupId,
    hashedEmail,
    role,
    dek,
    eEmail,
    uid,
    emailId,
    adminName,
    title,
    isOwnerBased,
    notificationMap,
    description,
  } = request.data;
  
  
  const collectionHasActions: boolean =
    collection !== "todos" && collection !== "lists" && collection !== "notes";

  // find doc with docIc as setupId in the collection
  const db = admin.firestore();
  const collectionRef = db.collection(collection);
  const doc = await collectionRef.doc(setupId).get();
  if (!doc.exists) {
    throw new functions.https.HttpsError("not-found", "Doc not found");
  }
  const docData = doc.data();
  if (!docData) {
    throw new functions.https.HttpsError("not-found", "Doc data not found");
  }
  const members = docData.members;
  const memberHashedEmails = members.memberHashedEmails;
  const membersConfig = members.membersConfig;

  // check if member already exists
  if (memberHashedEmails.includes(hashedEmail)) {
    throw new functions.https.HttpsError(
      "already-exists",
      "Member already exists"
    );
  }

  // add member to membersConfig
  membersConfig[hashedEmail] = {
    role: role,
    status: "active",
    hashedEmail: hashedEmail,
    dek: dek,
    eEmail: eEmail,
  };

  // add member to memberHashedEmails
  memberHashedEmails.push(hashedEmail);

  // make sure that memberHashedEmails contains all unique values
  const uniqueMemberHashedEmails = Array.from(new Set(memberHashedEmails));

  // update doc
  await collectionRef.doc(setupId).update({
    members: {
      memberHashedEmails: uniqueMemberHashedEmails,
      membersConfig: membersConfig,
    },
    cloudUpdatedAt: Timestamp.now(),
    localUpdatedAt: Timestamp.now(),
  });

  if (collectionHasActions) {
    const actionCollection: string | null =
      getActionCollectionFromSetupCollection(collection);
    if (actionCollection) {
      await updateActionsInBatches(
        db,
        actionCollection,
        setupId,
        hashedEmail,
        {
          role: role,
          status: "active",
          hashedEmail: hashedEmail,
          dek: dek,
          eEmail: eEmail,
        },
        ActionUpdationType.Addition
      );
    }
  }
  await addTaskIdToUserDoc(
    setupId,
    uid,
    collection,
    emailId,
    adminName,
    title,
    description
  );
  await createInAppListNotificationFunction(
    notificationMap,
    isOwnerBased,
    hashedEmail
  );
  return { success: true };
});

async function updateActionsInBatches(
  db: FirebaseFirestore.Firestore,
  actionCollection: string,
  setupId: string,
  hashedEmail: string,
  memberConfig: {
    role: string;
    status: string;
    hashedEmail: string;
    dek: string;
    eEmail: string;
  },
  type: ActionUpdationType
): Promise<void> {
  const batchSize = 500;
  const query = db.collection(actionCollection).where("setupId", "==", setupId);

  let lastDoc = null;
  let hasMore = true;
  while (hasMore) {
    let currentQuery = query.limit(batchSize);
    if (lastDoc) {
      currentQuery = currentQuery.startAfter(lastDoc);
    }

    const snapshot = await currentQuery.get();
    if (snapshot.empty) {
      hasMore = false;
      break;
    }

    const batch = db.batch();

    snapshot.docs.forEach((doc) => {
      const docData = doc.data();
      const currentMembers = docData.members || {
        memberHashedEmails: [],
        membersConfig: {},
      };

      console.log(
        "Updating action with member",
        hashedEmail,
        "and config",
        memberConfig
      );
      const updatedMembers = {
        memberHashedEmails: Array.from(
          new Set([...currentMembers.memberHashedEmails, hashedEmail])
        ),
        membersConfig: {
          ...currentMembers.membersConfig,
          [hashedEmail]: memberConfig,
        },
      };

      batch.update(doc.ref, {
        members: updatedMembers,
        cloudUpdatedAt: Timestamp.now(),
        localUpdatedAt: Timestamp.now(),
      });

      if (
        type === ActionUpdationType.Deletion &&
        currentMembers.memberHashedEmails.includes(hashedEmail)
      ) {
        const updatedMembers = {
          memberHashedEmails: currentMembers.memberHashedEmails.filter(
            (email: string) => email !== hashedEmail
          ),
          membersConfig: {
            ...currentMembers.membersConfig,
          },
        };
        delete updatedMembers.membersConfig[hashedEmail];
        batch.update(doc.ref, {
          members: updatedMembers,
          cloudUpdatedAt: Timestamp.now(),
          localUpdatedAt: Timestamp.now(),
        });
      }
    });

    await batch.commit();

    lastDoc = snapshot.docs[snapshot.docs.length - 1];
    if (snapshot.docs.length < batchSize) {
      hasMore = false;
      break;
    }
  }
}

// V2
export const addTaskIdToUserDoc = async (
  id: string,
  uid: string,
  collection: string,
  emailId: string,
  adminName: string,
  title: string,
  description?: string
) => {
  console.log(
    "ADDING TASK ID TO USER DOC id ",
    id,
    " uid ",
    uid,
    " collection ",
    collection
  );
  // Input validation
  if (!id || !collection) {
    console.log("Missing required fields");
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Missing required fields"
    );
  }
  console.log("Validating collection name");
  if (
    ![
      "lists",
      "notes",
      "todos",
      "moneyTrackerSetups",
      "habitSetups",
      "journalSetups",
    ].includes(collection)
  ) {
    console.log("Invalid collection name");
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Invalid collection name"
    );
  }

  const db = admin.firestore();
  const userResourceCollection = db.collection("userResources");

  try {
    console.log("Inside try catch block UId IS ", uid);
    const userResourceRef = userResourceCollection.where("uid", "==", uid);
    const userResourceSnapshot = await userResourceRef.get();

    const updateField =
      collection === "habitSetups"
        ? "shared.sharedHabitSetups"
        : collection === "journalSetups"
          ? "shared.sharedJournalSetups"
          : collection === "moneyTrackerSetups"
            ? "shared.sharedMoneyTrackerSetups"
            : collection === "todos"
              ? "shared.sharedTodos"
              : collection === "lists"
                ? "shared.sharedLists"
                : "shared.sharedNotes";

    console.log("user field", updateField);
    console.log("user exists", userResourceSnapshot.empty ? "no" : "yes");

    if (userResourceSnapshot.empty) {
      console.log("User Resorce not found, adding id to userKey doc");
      const userKeyRef = db
        .collection("userKeys")
        .where("email", "==", emailId);
      const userKeyDoc = await userKeyRef.get();
      if (!userKeyDoc.empty) {
        console.log("doc lenght is ", userKeyDoc.docs.length);
        const taskId: string = id;
        console.log("id is ", id, " task id is ", taskId);
        await userKeyDoc.docs[0].ref.update({
          [updateField]: FieldValue.arrayUnion(taskId),
          cloudUpdatedAt: Timestamp.now(),
          localUpdatedAt: Timestamp.now(),
        });
      } else {
        console.log("User Key doc not found");
      }
    } else {
      const userDoc = userResourceSnapshot.docs[0];
      console.log("userDocId", userDoc.id);
      
      const updateObject = {
        [updateField]: FieldValue.arrayUnion(id),
        cloudUpdatedAt: Timestamp.now(),
        localUpdatedAt: Timestamp.now(),
      };

      await userResourceCollection.doc(userDoc.id).update(updateObject);
    }
    const baseUrl = getMeBaseDomain();
    const url =
      "https://" + baseUrl + "/shared?collection=" + collection + "&id=" + id;

    // get name of user from auth
    const userInfo = await getUserByEmailFromAuthService(emailId);
    // email until @
    const smallEmail = emailId.includes("@") ? emailId.split("@")[0] : emailId;
    const name =
      userInfo &&
      userInfo.displayName !== null &&
      userInfo.displayName !== undefined
        ? userInfo.displayName
        : smallEmail;

    const desc = description ? description : "";
    const language = userInfo
      ? (await getViewSettingsByUserId(userInfo.uid))?.appSettings.language
      : "english";
    console.log("Descrption is ", desc);

    const entityName = translateEntity(
      language as LanguageCollaboration,
      collection as CollectionCollaboration
    );

    console.log("SENDING EMAIL WITH ENTITY NAME ", entityName);
    console.log("Language is ", language);

    await sendSingleEmailViaSes({
      templateName:
        collection === "lists"
          ? desc == null || desc == ""
            ? MevolveEmails.collaborationInviteOfListNoDesc
            : MevolveEmails.collaborationInviteOfList
          : MevolveEmails.collaborationInviteForNote,
      recipient: emailId,
      templateData:
        collection === "lists"
          ? {
              name: name,
              collabName: adminName,
              listTitle: title,
              listDescription: desc,
              inviteLink: url,
              linkToSupportChat: getSupportLink(),
            }
          : {
              name: name,
              collabName: adminName,
              listTitle: title,
              inviteLink: url,
              linkToSupportChat: getSupportLink(),
              entity: entityName,
            },
      language: language,
    });
    console.log("Task id added to user resorce doc for task ", updateField);
    return { success: true };
  } catch (error) {
    console.error("Error in addTaskIdToUserResorceDoc:", error);
    throw new functions.https.HttpsError(
      "internal",
      "An error occurred while updating the user document"
    );
  }
};

export const makeTaskPrivate = functions.https.onCall(async (request) => {
  const { id, collection } = request.data;
  const db = admin.firestore();
  const userResorcesCollection = db.collection("userResources");
  // get all the users who have id in their sharedTasks
  const query: string =
    collection === "lists" ? "public.publicLists" : "public.publicNotes";
  const userSnapshot = await userResorcesCollection
    .where(query, "array-contains", id)
    .get();
  if (userSnapshot.empty) {
    return;
  }

  for (const user of userSnapshot.docs) {
    // remove id from sharedTasks
    const userData = user.data();
    const sharedTasks = collection == "lists" ? "publicLists" : "publicNotes";

    userData["public"][sharedTasks].splice(
      userData["public"][sharedTasks].indexOf(id),
      1
    );
    // update user doc
    await userResorcesCollection.doc(user.id).update({
      public: userData.public,
      cloudUpdatedAt: Timestamp.now(),
      localUpdatedAt: Timestamp.now(),
    });
  }
});

async function removeUserFromMemberConfigForCollection(
  collection: string,
  id: string,
  email: string
) {
  const db = admin.firestore();
  const collectionHasActions: boolean =
    collection !== "todos" && collection !== "lists" && collection !== "notes";

  try {
    // First handle the setup document in a transaction
    await db.runTransaction(async (transaction) => {
      const setupDocRef = db.collection(collection).doc(id);
      const setupDoc = await transaction.get(setupDocRef);

      if (!setupDoc.exists) {
        throw new Error(`${collection} not found`);
      }

      const docData = setupDoc.data();
      if (!docData?.members) {
        throw new Error("Invalid document structure");
      }

      const membersConfigObject = { ...docData.members.membersConfig };
      const hashedEmails = [...docData.members.memberHashedEmails];

      if (!membersConfigObject[email]) {
        throw new Error("User not found in the collection");
      }

      // Remove member from config
      delete membersConfigObject[email];

      // Remove from hashedEmails
      const emailIndex = hashedEmails.indexOf(email);
      if (emailIndex !== -1) {
        hashedEmails.splice(emailIndex, 1);
      }

      // Update the setup document
      transaction.update(setupDocRef, {
        cloudUpdatedAt: Timestamp.now(),
        localUpdatedAt: Timestamp.now(),
        members: {
          membersConfig: membersConfigObject,
          memberHashedEmails: hashedEmails,
        },
      });
    });

    // After successful setup update, handle actions if needed
    if (collectionHasActions) {
      const actionCollection =
        getActionCollectionFromSetupCollection(collection);
      if (actionCollection) {
        await updateActionsInBatches(
          db,
          actionCollection,
          id,
          email,
          {
            role: "",
            status: "inactive",
            hashedEmail: email,
            dek: "",
            eEmail: "",
          },
          ActionUpdationType.Deletion
        );
      }
    }

    console.log(`Successfully removed user ${email} from ${collection} ${id}`);
    return true;
  } catch (error) {
    console.error("Error in removeUserFromMemberConfigForCollection:", error);
    throw error;
  }
}

export const createMevolveId = functions.https.onCall(
  { memory: "512MiB" },
  async (request) => {
    try {
      const { mevolveId, dek, name, uid } = request.data;

      // Input validation
      if (!mevolveId || !dek || !name || !uid) {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "Missing required fields"
        );
      }

      const db = admin.firestore();
      const publicUsersCollection = db.collection("publicUsers");

      // Perform pre-transaction checks
      const mevolveIdCheck = await publicUsersCollection
        .where("mevolveId", "==", mevolveId.toLowerCase())
        .get();

      if (!mevolveIdCheck.empty) {
        throw new functions.https.HttpsError(
          "already-exists",
          "MevolveId already exists"
        );
      }

      const existingUserCheck = await publicUsersCollection
        .where("uid", "==", uid)
        .get();

      let shouldOnlyUpdate = false;
      let existingDocId: string | null = null;
      let existingPublicUser: IPublicUsers | null = null;

      if (!existingUserCheck.empty) {
        existingDocId = existingUserCheck.docs[0].id;
        existingPublicUser = existingUserCheck.docs[0].data() as IPublicUsers;
        if (existingPublicUser.mevolveId) {
          shouldOnlyUpdate = true;
        }
      }

      // Prepare the user object
      const now = new Date();
      let publicUser: IPublicUsers;

      if (shouldOnlyUpdate && existingPublicUser) {
        publicUser = {
          ...existingPublicUser,
          mevolveId: mevolveId.toLowerCase(),
          cloudUpdatedAt: now,
        };
      } else {
        publicUser = {
          mevolveId: mevolveId.toLowerCase(),
          encData: {
            dek,
            encFields: [],
          },
          bio: null,
          docCollection: "publicUsers",
          docVer: currentDbVersion,
          followers: 0,
          following: 0,
          id: uuidv1(),
          name,
          source: "cloud",
          uid,
          sessionId: uuidv1(),
          createdAt: now,
          profileTags: [],
          publicListsMetadata: [],
          publicNotesMetadata: [],
          socialLinks: [],
          cloudUpdatedAt: now,
          localUpdatedAt: now,
          availableTags: [],
        };
      }

      // First, update KV store - if this fails, we won't proceed to Firestore update
      try {
        await setPublicUser(new Map(Object.entries(publicUser)));
      } catch (kvError) {
        console.error("KV update failed, aborting Firestore update:", kvError);
        throw new functions.https.HttpsError(
          "internal",
          "Failed to update user data in KV store"
        );
      }

      // If KV update succeeded, now proceed with Firestore update
      await db.runTransaction(async (transaction) => {
        // Double-check inside transaction that conditions haven't changed
        const mevolveIdQuery = await transaction.get(
          publicUsersCollection.where(
            "mevolveId",
            "==",
            mevolveId.toLowerCase()
          )
        );

        if (!mevolveIdQuery.empty) {
          // Only throw if it's not our own document
          const conflictDoc = mevolveIdQuery.docs[0];
          if (
            !shouldOnlyUpdate ||
            (shouldOnlyUpdate &&
              existingDocId !== null &&
              conflictDoc.id !== existingDocId)
          ) {
            throw new functions.https.HttpsError(
              "already-exists",
              "MevolveId already exists"
            );
          }
        }

        if (shouldOnlyUpdate && existingDocId !== null) {
          transaction.update(
            publicUsersCollection.doc(existingDocId),
            convertDatesToTimestamps(getMeParsedData(publicUser))
          );

          // This won't run if the above transaction fails
          try {
            // Update mevolveId in the publicRelations collection
            await updateRelationsAsync(uid, mevolveId.toLowerCase());
          } catch (relationsError) {
            console.error("Failed to update relations:", relationsError);
            // We don't throw here as the primary update already succeeded
          }
        } else {
          transaction.set(
            publicUsersCollection.doc(publicUser.id),
            convertDatesToTimestamps(getMeParsedData(publicUser))
          );
        }
      });

      return { data: publicUser };
    } catch (error) {
      console.error("Error creating mevolveId:", error);
      if (error instanceof functions.https.HttpsError) {
        throw error;
      }
      throw new functions.https.HttpsError(
        "internal",
        "An internal error occurred"
      );
    }
  }
);

export const getMevolveIdFromUid = functions.https.onRequest(
  {
    cors: [
      "https://dev-mevolve.pages.dev",
      "https://qa-mevolve.pages.dev",
      "https://staging-mevolve.pages.dev",
      "https://prod-mevolve.pages.dev",
      "http://localhost:4200",
      "https://web.mevolve.app",
    ],
  },
  async (req, res) => {
    try {
      // Validate HTTP method
      if (req.method !== "GET") {
        res.status(405).json({
          success: false,
          data: null,
          errors: { code: "method-not-allowed", message: "Method not allowed" },
        });
      }

      const { uid } = req.query;

      // Validate required field
      if (!uid) {
        res.status(400).json({
          success: false,
          data: null,
          errors: {
            code: "invalid-argument",
            message: "Missing required field: uid",
          },
        });
      }

      const db = admin.firestore();
      const publicUsersCollection = db.collection("publicUsers");

      // Query the Firestore collection
      const userQuery = await publicUsersCollection
        .where("uid", "==", uid)
        .get();

      if (userQuery.empty) {
        res.status(404).json({
          success: false,
          data: null,
          errors: { code: "not-found", message: "User not found" },
        });
      }

      // Get the mevolveId
      const mevolveId = userQuery.docs[0].data().mevolveId;

      // Send success response
      res.status(200).json({
        success: true,
        data: { mevolveId },
        errors: null,
      });
    } catch (error) {
      console.error("Error fetching mevolveId:", error);

      // Return error response
      res.status(500).json({
        success: false,
        data: null,
        errors: {
          code: "internal-error",
          message: "An internal error occurred",
        },
      });
    }
  }
);

export const followUser = functions.https.onCall(async (request) => {
  const {
    currentUserId,
    targetUserId,
    targetMevolveId,
    currentMevolveId,
    currentName,
    targetName,
  } = request.data;

  const db = admin.firestore();

  if (currentUserId === targetUserId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Cannot follow yourself"
    );
  }

  const relationRef = db.collection("publicUserRelations").doc();

  return db.runTransaction(async (transaction) => {
    // Check for existing relation
    const existingRelationQuery = await transaction.get(
      db
        .collection("publicUserRelations")
        .where("mevolveId", "==", targetMevolveId)
        .where("userId", "==", targetUserId)
        .where("followerMevolveId", "==", currentMevolveId)
        .where("followerId", "==", currentUserId)
        .limit(1)
    );

    if (!existingRelationQuery.empty) {
      throw new functions.https.HttpsError(
        "already-exists",
        "Already following this user"
      );
    }

    // Get user documents
    const [targetUserDoc, currentUserDoc] = await Promise.all([
      transaction.get(
        db
          .collection("publicUsers")
          .where("mevolveId", "==", targetMevolveId)
          .where("uid", "==", targetUserId)
          .limit(1)
      ),
      transaction.get(
        db
          .collection("publicUsers")
          .where("mevolveId", "==", currentMevolveId)
          .where("uid", "==", currentUserId)
          .limit(1)
      ),
    ]);

    if (targetUserDoc.empty || currentUserDoc.empty) {
      throw new functions.https.HttpsError("not-found", "User not found");
    }

    const targetUserData = targetUserDoc.docs[0].data();
    const currentUserData = currentUserDoc.docs[0].data();

    // Create relation
    const relationData: PublicUserRelationInterface = {
      id: relationRef.id,
      userId: targetUserId,
      followerId: currentUserId,
      name: currentName,
      mevolveId: targetMevolveId,
      followerMevolveId: currentMevolveId,
      updatedAt: Timestamp.now(),
      followerName: targetName,
    };

    // Perform updates atomically
    transaction.create(relationRef, relationData);

    transaction.update(targetUserDoc.docs[0].ref, {
      followers: FieldValue.increment(1),
      cloudUpdatedAt: Timestamp.now(),
      localUpdatedAt: Timestamp.now(),
    });

    transaction.update(currentUserDoc.docs[0].ref, {
      following: FieldValue.increment(1),
      cloudUpdatedAt: Timestamp.now(),
      localUpdatedAt: Timestamp.now(),
    });

    // Update cached data
    const updateTargetUserData = {
      ...targetUserData,
      followers: (targetUserData.followers || 0) + 1,
    };

    const updateCurrentUserData = {
      ...currentUserData,
      following: (currentUserData.following || 0) + 1,
    };

    await setPublicUser(new Map(Object.entries(updateTargetUserData)));
    await setPublicUser(new Map(Object.entries(updateCurrentUserData)));

    return { success: true };
  });
});

export const unfollowUser = functions.https.onCall(async (request) => {
  const { currentUserId, targetUserId, targetMevolveId, currentMevolveId } =
    request.data;
  const db = admin.firestore();

  if (currentUserId === targetUserId) {
    throw new functions.https.HttpsError(
      "invalid-argument",
      "Cannot unfollow yourself"
    );
  }

  return db.runTransaction(async (transaction) => {
    // Get relation document
    const relationQuery = await transaction.get(
      db
        .collection("publicUserRelations")
        .where("mevolveId", "==", targetMevolveId)
        .where("userId", "==", targetUserId)
        .where("followerMevolveId", "==", currentMevolveId)
        .where("followerId", "==", currentUserId)
        .limit(1)
    );

    if (relationQuery.empty) {
      throw new functions.https.HttpsError("not-found", "Relation not found");
    }

    // Get user documents
    const [targetUserDoc, currentUserDoc] = await Promise.all([
      transaction.get(
        db
          .collection("publicUsers")
          .where("mevolveId", "==", targetMevolveId)
          .where("uid", "==", targetUserId)
          .limit(1)
      ),
      transaction.get(
        db
          .collection("publicUsers")
          .where("mevolveId", "==", currentMevolveId)
          .where("uid", "==", currentUserId)
          .limit(1)
      ),
    ]);

    if (targetUserDoc.empty || currentUserDoc.empty) {
      throw new functions.https.HttpsError("not-found", "User not found");
    }

    const targetUserData = targetUserDoc.docs[0].data();
    const currentUserData = currentUserDoc.docs[0].data();

    // Delete relation and update counts atomically
    transaction.delete(relationQuery.docs[0].ref);

    transaction.update(targetUserDoc.docs[0].ref, {
      followers: FieldValue.increment(-1),
      cloudUpdatedAt: Timestamp.now(),
    });

    transaction.update(currentUserDoc.docs[0].ref, {
      following: FieldValue.increment(-1),
      cloudUpdatedAt: Timestamp.now(),
    });

    // Update cached data
    const updateTargetUserData = {
      ...targetUserData,
      followers: Math.max(0, (targetUserData.followers || 0) - 1),
    };

    const updateCurrentUserData = {
      ...currentUserData,
      following: Math.max(0, (currentUserData.following || 0) - 1),
    };

    await setPublicUser(new Map(Object.entries(updateTargetUserData)));
    await setPublicUser(new Map(Object.entries(updateCurrentUserData)));

    return { success: true };
  });
});

export const createInAppListNotification = functions.https.onCall(
  async (request) => {
    console.log("process started");
    const { notificationMap, isOwnerBased, hashedEmail } = request.data;
    await createInAppListNotificationFunction(
      notificationMap,
      isOwnerBased,
      hashedEmail
    );
  }
);

const createInAppListNotificationFunction = async (
  notificationMap: any,
  isOwnerBased: any,
  hashedEmail: any
) => {
  console.log("creating in app notification");
  
  const db = admin.firestore();
  const inAppNotificationCollection = db.collection("inAppNotifications");

  const inAppNotificationDoc = inAppNotificationCollection.doc(
    notificationMap.id
  );
  console.log("encryption started");
  
  let uid;
  if (isOwnerBased) {
    uid = notificationMap.uid;
  } else {
    uid = await getUidFromHashedEmail(hashedEmail);
  }
  
  if (!uid) {
    console.log("User not fully registered yet, skipping in-app notification (email invitation sent)");
    return; // Skip notification creation for unregistered users
  }

  console.log("got user secret");
  const secret = getNewSecret();

  const encData = {
    dek: await encryptDocKey(secret, uid),
    encFields: [],
  };
  const userDocVer: number = await getUserDbVersion(uid);

  const newNotification = {
    ...notificationMap,
    encData: encData,
    docVer: userDocVer ?? notificationMap.docVer,
    uid: uid,
    cloudUpdatedAt: Timestamp.now(),
    createdAt: new Date(notificationMap.createdAt),
    localUpdatedAt: new Date(notificationMap.localUpdatedAt),
    permaDeletedAt:
      notificationMap.permaDeletedAt != null
        ? new Date(notificationMap.permaDeletedAt)
        : null,
  };
  await inAppNotificationDoc.set(convertDatesToTimestamps(newNotification));
  console.log("notification added", inAppNotificationDoc.id);
};

interface PublicUserRelationInterface {
  id: string;
  userId: string;
  followerId: string;
  name: string;
  mevolveId: string;
  followerMevolveId: string;
  followerName: string;
  updatedAt: Timestamp;
}

async function getUidFromHashedEmail(
  hashedEmail: string
): Promise<string | null> {
  const userKeyCollection = admin.firestore().collection("userKeys");
  const doc = await userKeyCollection
    .where("emailHash", "==", hashedEmail)
    .get();
    
  if (doc.empty) {
    return null;
  }
  
  const userData = doc.docs[0].data();
  return userData.uid;
}

const updateRelationsAsync = async (uid: string, newMevolveId: string) => {
  try {
    const db = admin.firestore();
    const publicUserRelationsCollection = db.collection("publicUserRelations");

    // Get all relevant documents
    const [userRefs, followerRefs] = await Promise.all([
      publicUserRelationsCollection.where("userId", "==", uid).get(),
      publicUserRelationsCollection.where("followerId", "==", uid).get(),
    ]);

    // Batch updates for better performance
    const batch = db.batch();

    // Update user references
    userRefs.docs.forEach((doc) => {
      batch.update(doc.ref, { mevolveId: newMevolveId });
    });

    // Update follower references
    followerRefs.docs.forEach((doc) => {
      batch.update(doc.ref, { followerMevolveId: newMevolveId });
    });

    // Commit all updates
    await batch.commit();

    console.log(`Successfully updated relations for user ${uid}`);
  } catch (error) {
    console.error("Error updating relations:", error);
  }
};

function getActionCollectionFromSetupCollection(
  collection: string
): string | null {
  switch (collection) {
    case "moneyTrackerSetups":
      return "moneyTrackerTransactions";
    case "habitSetups":
      return "habitActions";
    case "journalSetups":
      return "journalActions";
    default:
      return null;
  }
}
