import { IBaseSchema } from "../versions/base/base_schema";

export abstract class BaseMigrationScript {
  docVer: number;

  constructor(docVersion: number) {
    this.docVer = docVersion;
  }

  abstract preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void>;

  abstract upgradeSchema(
    _source: IBaseSchema,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema>;

  abstract downgradeSchema(
    _source: IBaseSchema,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema>;

  abstract postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>,
    _allUserData?: IBaseSchema[]
  ): Promise<IBaseSchema[]>;
}
