import * as admin from "firebase-admin";
import * as functions from "firebase-functions";
import PromisePool from "@supercharge/promise-pool";
import { IBaseSchema, meCollectionNames } from "../versions/base/base_schema";
import { saveUserDataSnapshot } from "../../account_backup/user_data_backup/weekly_account_backups";
import { DbRepository } from "../../save_data/db.repository";
import {
  convertDatesToTimestamps,
  getCurrentUTCDate,
} from "../../../utils/utility_methods";
import { getMigrationScript } from "./migration_version_mapping";
import { IUser } from "../versions/base/model_mappings";
import { sharedCollections } from "../../user/user_api/get_user";

export class UserMigration {
  async migrateUser(
    userData: IBaseSchema,
    requiredFinalVersion: number
  ): Promise<boolean> {
    const userId = userData.uid;
    try {
      // Get all data for user
      console.log("Getting all data for user: " + userId);
      let allUserData: IBaseSchema[] = await this.getAllUserData(userData);

      console.log("Saving user data backup before migrations: " + userId);
      await saveUserDataSnapshot(
        allUserData.filter(
          (item) =>
            item.docCollection != "calendarEventSetups" &&
            item.docCollection != "calendarEventActions" &&
            item.docCollection != "calendarIntegrations"
        )
      );

      // Migrate all data
      console.log("Migrating data for user: " + userId);
      allUserData = await this.migrateCollection(
        allUserData,
        requiredFinalVersion
      );

      // Search for all activity documents created for this user email hash and then delete those documents
      removeAllActivityDocsForUser(userId);

      // Save all data again
      console.log("Saving migrated data for user: " + userId);
      await this.saveData(allUserData);

      console.log("Saving user data backup after migrations: " + userId);
      await saveUserDataSnapshot(
        allUserData.filter(
          (item) =>
            item.docCollection != "calendarEventSetups" &&
            item.docCollection != "calendarEventActions" &&
            item.docCollection != "calendarIntegrations"
        )
      );
      return true;
    } catch (error) {
      functions.logger.error(
        "Error in migrating user: " + userId + " with error:\n" + error
      );
      return false;
    }
  }

  async getAllUserData(userData: IBaseSchema): Promise<IBaseSchema[]> {
    const userId = userData.uid;
    const docVer = userData.docVer;
    const tasks: Promise<IBaseSchema[]>[] = [];

    console.log(
      `Starting data retrieval for user: ${userId} with docVer: ${docVer}`
    );

    // Loop through each collection name except "users"
    for (const collection of meCollectionNames.filter(
      (item) => item != "users"
    )) {
      tasks.push(this.getAllDataForCollection(collection, userId, docVer));
    }

    // Wait for all promises in tasks to resolve, fetching data from all collections
    const results = await Promise.all(tasks);

    // Log the count of items fetched from each collection
    results.forEach((data, index) => {
      console.log(
        `Collection ${meCollectionNames[index]}: Retrieved ${data.length} items`
      );
    });

    console.log(`Fetched data from all collections for user: ${userId}`);

    // Filter data to include only records with docVer >= 87
    const updatedResults = results.flatMap((data) =>
      data.filter((d) => d.docVer >= 87)
    );

    // Store all user data in array, starting with the provided userData
    const allUserData: IBaseSchema[] = [];
    allUserData.push(userData);
    for (const result of updatedResults) {
      allUserData.push(result);
    }

    return allUserData;
  }

  async getAllDataForCollection(
    collectionName: string,
    userId: string,
    docVer: number
  ): Promise<IBaseSchema[]> {
    const dataSnapshot = await admin
      .firestore()
      .collection(collectionName)
      .where("uid", "==", userId)
      .where("docVer", "==", docVer)
      .get();

    console.log(
      `Retrieved ${dataSnapshot.docs.length} documents from collection "${collectionName}" for user ID "${userId}" with docVer "${docVer}".`
    );

    let dataList: IBaseSchema[] = dataSnapshot.empty
      ? []
      : dataSnapshot.docs.map((doc) => doc.data() as IBaseSchema);

    // Filter the documents to include only those with docVer >= 62 or if the ID is undefined
    dataList = dataList.filter(
      (data) => data.docVer >= 62 || data.id === undefined
    );

    return dataList;
  }

  async migrateCollection(
    dataList: IBaseSchema[],
    requiredFinalVersion: number
  ): Promise<IBaseSchema[]> {
    const user = dataList.find(
      (data) => data.docCollection === "users"
    ) as IUser;

    // Get the current version and the difference from the required version
    const userInitialDbVersion = user.docVer;
    const dbVersionDifference = requiredFinalVersion - userInitialDbVersion;

    // Migration direction (upgrade or downgrade)
    const isUpgrading = dbVersionDifference >= 0;

    // Helper data for migration scripts
    const helperData: Map<string, any> = new Map();
    helperData.set("userId", user.uid);
    // helperData.set("currency", user.moneyTrackerSettings?.config?.currency ?? null);

    // Store the original documents count
    let expectedCount = dataList.length;

    // Loop over each version increment to migrate step-by-step to the required version
    for (let i = 1; i <= Math.abs(dbVersionDifference); i++) {
      // The version to migrate to in this iteration
      const versionRequired = isUpgrading
        ? userInitialDbVersion + i
        : userInitialDbVersion - (i - 1);

      try {
        console.log("Migrating data for version: " + versionRequired);

        // Retrieve the migration script for the target version
        const script = getMigrationScript(versionRequired);

        // Run pre migration step of the script
        await script.preMigrationSetup(isUpgrading, helperData);

        // Loop through each document and apply the upgrade or downgrade as needed
        // IMP NOTE: If any migration script is creating new documents, then the new documents
        // are not added to the dataList so they won't go through the migration process. So,
        // the documents should be created for the latest docVer in the migration script.
        for (let j = 0; j < dataList.length; j++) {
          if (isUpgrading) {
            dataList[j] = await script.upgradeSchema(dataList[j], helperData);
          } else {
            dataList[j] = await script.downgradeSchema(dataList[j], helperData);
          }

          // Convert dates to timestamps for compatibility with the schema
          dataList[j] = convertDatesToTimestamps(dataList[j]);

          // Update cloud update timestamp for shared collections so their listeners can sync db change in client
          // and do syncs for incompatiblility and compatibility otherwise the update will be rejected due to no timestamp change or lower timestamp
          if (sharedCollections.includes(dataList[j].docCollection)) {
            dataList[j].cloudUpdatedAt = null;
            dataList[j].localUpdatedAt = getCurrentUTCDate();
          }
        }

        // Run post-migration and collect new/updated documents
        const newDocs = await script.postMigration(
          isUpgrading,
          helperData,
          dataList
        );
        if (newDocs?.length) {
          // Handle new vs updated documents
          for (const newDoc of newDocs) {
            const existingIndex = dataList.findIndex(
              (doc) =>
                doc.id === newDoc.id &&
                doc.docCollection === newDoc.docCollection
            );

            if (existingIndex !== -1) {
              // Update existing document
              dataList[existingIndex] = newDoc;
              console.log(
                `Updated existing document: ${newDoc.docCollection}/${newDoc.id}`
              );
            } else {
              // Add new document
              dataList.push(newDoc);
              expectedCount += 1;
              console.log(
                `Added new document: ${newDoc.docCollection}/${newDoc.id}`
              );
            }
          }
        }

        console.log("Migration complete for version: " + versionRequired);
      } catch (error) {
        const data = dataList[i];

        throw new Error(
          `Error in migrating document: ${data.uid}/${data.docCollection}/${data.docVer}/${data.id}\n${error}`
        );
      }
    }

    console.log("Migration complete for all versions.");

    // Verify that all documents reached the required final version
    const incompleteMigration = dataList.find(
      (data) => data.docVer !== requiredFinalVersion
    );
    if (incompleteMigration) {
      throw new Error(
        `Documents not migrated: ${incompleteMigration.uid}/${incompleteMigration.docCollection}/${incompleteMigration.docVer}/${incompleteMigration.id}`
      );
    }

    // Ensure the number of documents matches the original count
    if (dataList.length !== expectedCount) {
      throw new Error(
        `Documents migrated final count mismatch. Expected count: ${expectedCount}, Found count: ${dataList.length}`
      );
    }

    return dataList;
  }

  async saveData(allUserData: IBaseSchema[]) {
    // Save non-user collections first
    await this.saveCollection(
      allUserData.filter((data) => data.docCollection != "users")
    );
    // Save user collection last
    await this.saveCollection(
      allUserData.filter((data) => data.docCollection == "users")
    );
  }

  async saveCollection(data: IBaseSchema[]): Promise<void> {
    if (data.length === 0) {
      console.log("No documents to save in the collection.");
      return;
    }

    let successfulSaves = 0;
    const collectionName =
      data[0].docCollection === "users"
        ? "user collection"
        : "other collections";

    const result = await PromisePool.withConcurrency(500)
      .for(data)
      .process(async (item) => {
        try {
          await this.saveDocument(item);
          successfulSaves++;
        } catch (error) {
          console.error(`Error saving document ${item.id}:`, error);
        }
      });

    if (result.errors.length > 0) {
      console.error("Errors encountered during save:", result.errors);
    }

    console.log(
      `Total successfully saved documents in '${collectionName}': ${successfulSaves}`
    );
  }

  async saveDocument(data: IBaseSchema) {
    return new DbRepository().updateMigratedData(data);
  }
}

export async function removeAllActivityDocsForUser(_uid: string) {
  // const userEmailHash = await getHashedEmailFromUid(_uid);
  // TODO: Implement logic to delete all activity docs for this user
  return;
}
