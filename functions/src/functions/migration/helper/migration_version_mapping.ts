import { BaseMigrationScript } from "./base_migration_model";
import { V87 } from "../versions/v87/migration";
import { V88 } from "../versions/v88/migration";
import { V89 } from "../versions/v89/migration";
import { V90 } from "../versions/v90/migration";
import { V91 } from "../versions/v91/migration";
import { V92 } from "../versions/v92/migration";
import { V93 } from "../versions/v93/migration";
import { V94 } from "../versions/v94/migration";
import { V95 } from "../versions/v95/migration";
import { V96 } from "../versions/v96/migration";
import { V97 } from "../versions/v97/migration";
import { V98 } from "../versions/v98/migration";
import { V99 } from "../versions/v99/migration";
import { V100 } from "../versions/v100/migration";
import { V101 } from "../versions/v101/migration";
import { V102 } from "../versions/v102/migration";
import { V103 } from "../versions/v103/migration";
import { V104 } from "../versions/v104/migration";
import { V105 } from "../versions/v105/migration";
import { V106 } from "../versions/v106/migration";
import { V107 } from "../versions/v107/migration";
import { V108 } from "../versions/v108/migration";
import { V109 } from "../versions/v109/migration";
import { V110 } from "../versions/v110/migration";
import { V111 } from "../versions/v111/migration";
import { V112 } from "../versions/v112/migration";
import { V113 } from "../versions/v113/migration";
import { V114 } from "../versions/v114/migration";
import { V115 } from "../versions/v115/migration";
import { V116 } from "../versions/v116/migration";
import { V117 } from "../versions/v117/migration";
import { V118 } from "../versions/v118/migration";
import { V119 } from "../versions/v119/migration";
import { V120 } from "../versions/v120/migration";
import { V121 } from "../versions/v121/migration";
import { V122 } from "../versions/v122/migration";
import { V123 } from "../versions/v123/migration";
import { V124 } from "../versions/v124/migration";
import { V125 } from "../versions/v125/migration";
import { V126 } from "../versions/v126/migration";
import { V127 } from "../versions/v127/migration";
import { V128 } from "../versions/v128/migration";
import { V129 } from "../versions/v129/migration";
import { V130 } from "../versions/v130/migration";
import { V131 } from "../versions/v131/migration";
import { V132 } from "../versions/v132/migration";

export function getMigrationScript(version: number): BaseMigrationScript {
  switch (version) {
    case 87:
      return new V87(version);
    case 88:
      return new V88(version);
    case 89:
      return new V89(version);
    case 90:
      return new V90(version);
    case 91:
      return new V91(version);
    case 92:
      return new V92(version);
    case 93:
      return new V93(version);
    case 94:
      return new V94(version);
    case 95:
      return new V95(version);
    case 96:
      return new V96(version);
    case 97:
      return new V97(version);
    case 98:
      return new V98(version);
    case 99:
      return new V99(version);
    case 100:
      return new V100(version);
    case 101:
      return new V101(version);
    case 102:
      return new V102(version);
    case 103:
      return new V103(version);
    case 104:
      return new V104(version);
    case 105:
      return new V105(version);
    case 106:
      return new V106(version);
    case 107:
      return new V107(version);
    case 108:
      return new V108(version);
    case 109:
      return new V109(version);
    case 110:
      return new V110(version);
    case 111:
      return new V111(version);
    case 112:
      return new V112(version);
    case 113:
      return new V113(version);
    case 114:
      return new V114(version);
    case 115:
      return new V115(version);
    case 116:
      return new V116(version);
    case 117:
      return new V117(version);
    case 118:
      return new V118(version);
    case 119:
      return new V119(version);
    case 120:
      return new V120(version);
    case 121:
      return new V121(version);
    case 122:
      return new V122(version);
    case 123:
      return new V123(version);
    case 124:
      return new V124(version);
    case 125:
      return new V125(version);
    case 126:
      return new V126(version);
    case 127:
      return new V127(version);
    case 128:
      return new V128(version);
    case 129:
      return new V129(version);
    case 130:
      return new V130(version);
    case 131:
      return new V131(version);
    case 132:
      return new V132(version);
    default:
      throw new Error("Migration script not found for version: " + version);
  }
}
