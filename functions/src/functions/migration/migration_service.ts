/* eslint-disable require-jsdoc */
import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v2";
import { onMessagePublished } from "firebase-functions/v2/pubsub";
import { UserMigration } from "./helper/user_migration";
import { IBaseSchema } from "./versions/base/base_schema";
import { currentDbVersion } from "./versions/base/model_mappings";
import { ImageMigrationDetails } from "./versions/base/image_migrations_details";
import { getMigrationScript } from "./helper/migration_version_mapping";
import { MeLogger } from "../../utils/logger/models/me_logger";

exports.processImage = onMessagePublished(
  {
    topic: "data-process_image_migration",
  },
  async (event) => {
    const logger = new MeLogger();
    logger.info("Starting image migration");
    const data = event.data.message.json;
    const message = JSON.parse(data.message) as ImageMigrationDetails;
    try {
      switch (message.version) {
        case 1: {
          // await new ImageMigrationV1().processImage(message);
          break;
        }
      }
    } catch (error) {
      logger.log(`image migration for failed, payload -
        ${message}, error - ${error}`);
    }
  }
);

export const user = functions.https.onCall(
  {
    memory: "1GiB",
    concurrency: 2,
    timeoutSeconds: 540,
  },
  async (request) => {
    const userId = request.data.uid;
    const logger = new MeLogger(userId);
    logger.log("migration started for user id: " + userId);
    return startUserMigration(userId, logger, currentDbVersion);
  }
);

async function startUserMigration(
  userId: string,
  meLogger: MeLogger,
  requiredFinalVersion: number
) {
  const userSnapshot = await admin
    .firestore()
    .collection("users")
    .doc(userId as string)
    .get();
  if (userSnapshot.exists) {
    if (userSnapshot.data()?.docVer == requiredFinalVersion) {
      meLogger.log("user already migrated:" + userId);
      return { result: "success" };
    } else {
      const result = await new UserMigration().migrateUser(
        userSnapshot.data() as IBaseSchema,
        requiredFinalVersion
      );
      const migrationResult = result ? "success" : "failed";
      meLogger.log(
        "user migrated. Result = " + migrationResult + ", user: " + userId
      );
      return { result: migrationResult };
    }
  } else {
    return {
      result: "failed",
      reason: "no user found with id: " + userId,
    };
  }
}

exports.testUser = functions.https.onRequest(
  {
    memory: "1GiB",
    concurrency: 2,
    // Test function so no need to enforce app check.
    enforceAppCheck: false,
  },
  async (req, res) => {
    const uid = req.query.uid as string | undefined;
    const requiredFinalVersion = parseInt(
      (req.query.version as string | undefined) ?? currentDbVersion.toString()
    );
    const logger = new MeLogger(uid);
    logger.log("starting user migration for user: " + uid);
    if (uid) {
      await startUserMigration(uid as string, logger, requiredFinalVersion);
      res.send("success");
    } else {
      res.send("failed");
    }
  }
);

export class DataMigrationService {
  async migrateSchema(
    source: IBaseSchema,
    helperData: Map<string, string>
  ): Promise<IBaseSchema> {
    if (source.docVer == undefined) source.docVer = 1;
    if (source.docVer == currentDbVersion) {
      return source;
    } else if (source.docVer < currentDbVersion) {
      return await this.upgradeSchema(source, helperData);
    } else if (source.docVer > currentDbVersion) {
      return await this.downgradeSchema(source, helperData);
    } else {
      return source;
    }
  }

  private async upgradeSchema(
    source: IBaseSchema,
    helperData: Map<string, string>
  ): Promise<IBaseSchema> {
    while (source.docVer != currentDbVersion) {
      // console.log("doc id = " + source.id + " ver = " + source.docVer);
      source = await getMigrationScript(source.docVer + 1)?.upgradeSchema(
        source,
        helperData
      );
    }
    return source;
  }

  private async downgradeSchema(
    source: IBaseSchema,
    helperData: Map<string, string>
  ): Promise<IBaseSchema> {
    while (source.docVer != currentDbVersion) {
      // console.log("doc id = " + source.id + " ver = " + source.docVer);
      source = await getMigrationScript(source.docVer)?.downgradeSchema(
        source,
        helperData
      );
    }
    return source;
  }
}

// exports.testFunctionMigration = functions.https.onRequest(
//   { enforceAppCheck: false },
//   async (req, res) => {
//     console.log("sending push notifications to users");
//     await startUserMigration("C42A78s0cQMVVymu7BQYHQ6cS9y2");
//     res.send("success");
//   }
// );

// Uncomment the below code to enable to test data migration locally

// export const start = functions.https.onRequest(
//   { enforceAppCheck: false },
//   async (request, response) => {
//     functions.logger.info("starting data migration");
//     await runUserBasedMigration();
//     response.send("Migration event successfully");
//   }
// );

// const simultaneousUserLimit = 100;

// async function runUserBasedMigration() {
//   functions.logger.info("User based migration started");
//   const getUserDataQuery = admin
//     .firestore()
//     .collection("users")
//     .limit(simultaneousUserLimit);
//   let dataSnapshot = await getUserDataQuery.get();
//   functions.logger.info(`length = ${dataSnapshot.docs.length}`);
//   while (dataSnapshot.docs.length > 0) {
//     const tasks: Promise<boolean>[] = [];
//     for (const document of dataSnapshot.docs) {
//       tasks.push(
//         new UserMigration().migrateUser(document.data() as IBaseSchema)
//       );
//     }
//     const result = await Promise.all(tasks);
//     functions.logger.info("result length = " + result.length);
//     const last = dataSnapshot.docs[dataSnapshot.docs.length - 1];
//     dataSnapshot = await getUserDataQuery.startAfter(last).get();
//     functions.logger.info(
//       "next length of snapshot = " + dataSnapshot.docs.length
//     );
//   }
//   await sendDbStatusReport(false);
//   functions.logger.info("User based migration completed");
// }
