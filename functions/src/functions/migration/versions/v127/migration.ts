import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import { v4 as uuidv4 } from "uuid";

export class V127 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;

    if (source.docCollection === "lists") {
      const list = source as any;
      if (Array.isArray(list.listItems)) {
        const newListItems: Record<string, any> = {};
        list.listItems.forEach((item: any, index: number) => {
          const itemId = uuidv4();
          newListItems[itemId] = {
            ...item,
            id: itemId, // Assign new UUID
            position: index, // Use array index as position
            deletedAt: null,
            encData: {
              ...list.encData,
              encFields: [
                "title",
                "description",
                "ownerName",
                "ownerEmail",
                "listItems{}.item",
                "members.membersConfig{}.eEmail",
              ],
            },
          };
        });
        list.listItems = newListItems;
      }
    }

    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
