import {
  decryptDoc<PERSON>ey,
  encryptTextData,
  generateECCKeyPair,
  generateRSAKeyPair,
  getDecryptedPrivateKeyOfUserById,
  getSecretFromKmsForUserByEmail,
  getUserKeyDocByEmail,
  pemToHex,
} from "../../../../utils/encryption/encryption";
import * as crypto from "crypto";
import { getEmailByIdFromAuthService } from "../../../../utils/utility_methods";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import * as eciesjs from "eciesjs";
import * as admin from "firebase-admin";
import { Timestamp } from "firebase-admin/firestore";

export class V92 extends BaseMigrationScript {
  async preMigrationSetup(
    isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    try {
      const userId = _helperData.get("userId");
      if (!userId) {
        throw new Error("userId not found in helperData");
      }

      const cachedPrivateKey = _helperData.has("privateKey");
      if (!cachedPrivateKey) {
        const decryptedUserPrivateKey =
          await getDecryptedPrivateKeyOfUserById(userId, null);
        const email = await getEmailByIdFromAuthService(userId);
        const userKeyDoc = await getUserKeyDocByEmail(email!);

        if (!userKeyDoc) {
          throw new Error(`userKeyDoc is null for user ${userId}`);
        }

        const publicKey = userKeyDoc.publicKey;

        if (!decryptedUserPrivateKey || !email || !publicKey) {
          throw new Error(`Invalid data fetched for user ${userId}`);
        }

        if (isUpgrading) {
          const eccKeyPair = generateECCKeyPair();
          const eccPrivateKey = eccKeyPair.privateKey;
          const eccPublicKey = eccKeyPair.publicKey;

          const encEccPrivateKey = encryptTextData(
            eccPrivateKey,
            (await getSecretFromKmsForUserByEmail(email)).key
          );

          _helperData.set("privateKey", decryptedUserPrivateKey);
          _helperData.set("email", email);
          _helperData.set("publicKey", publicKey);
          _helperData.set("eccPrivateKey", eccPrivateKey);
          _helperData.set("eccPublicKey", eccPublicKey);
          _helperData.set("encEccPrivateKey", encEccPrivateKey);
        } else {
          const rsaKeyPair = generateRSAKeyPair();
          const rsaPrivateKey = rsaKeyPair.privateKey;
          const rsaPublicKey = rsaKeyPair.publicKey;

          const rsaEccPrivateKey = encryptTextData(
            rsaPrivateKey,
            (await getSecretFromKmsForUserByEmail(email)).key
          );

          _helperData.set("privateKey", decryptedUserPrivateKey);
          _helperData.set("email", email);
          _helperData.set("publicKey", publicKey);
          _helperData.set("rsaPrivateKey", rsaEccPrivateKey);
          _helperData.set("rsaPublicKey", rsaPublicKey);
          _helperData.set("encRsaPrivateKey", rsaEccPrivateKey);
        }
      }

      return Promise.resolve();
    } catch (error) {
      console.error("Error in preMigrationSetup:", error);
      throw error;
    }
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;

    // Access user data from helperData.
    const privateKey = _helperData.get("privateKey");
    const rsaPublicKey = _helperData.get("rsaPublicKey");

    if (source.encData.dek) {
      // Decrypt old encData.dek.
      const decryptedDek = oldDecryptDocKey(source.encData.dek, privateKey!);

      // Re-encrypt encData.dek with new method.
      source.encData.dek = newEncryptDocKey(decryptedDek, rsaPublicKey!);
    }

    // Done.
    return source;
  }

  async downgradeSchema(
    source: IBaseSchema,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema> {
    source.docVer = this.docVer - 1;
    // Access user data from helperData.
    const privateKey = _helperData.get("privateKey");
    const rsaPublicKey = _helperData.get("rsaPublicKey");

    if (source.encData.dek) {
      // Decrypt old encData.dek.
      const decryptedDek = await decryptDocKey(source.encData.dek, privateKey!);

      // Re-encrypt encData.dek with new method.
      source.encData.dek = await encryptDocKeyWithRsa(
        decryptedDek,
        rsaPublicKey!
      );
    }
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Access user data from helperData.
    const userId = _helperData.get("userId");
    const eccPublicKey = _helperData.get("eccPublicKey");
    const encEccPrivateKey = _helperData.get("encEccPrivateKey");

    if (!userId || !eccPublicKey || !encEccPrivateKey) {
      throw new Error(
        `Post migration failed. Required data not found in helperData for user ${userId}`
      );
    }

    await updateUserKeyDocByUid(userId, eccPublicKey, encEccPrivateKey);
    return [];
  }
}

function oldDecryptDocKey(
  encryptedDocKey: string,
  decryptedUserPrivateKey: string
) {
  if (decryptedUserPrivateKey.startsWith("-----BEGIN RSA ")) {
    if (encryptedDocKey.startsWith("encRsa_")) {
      const encryptedBuffer = Buffer.from(
        encryptedDocKey.substring(7),
        "base64"
      );
      const decryptedData = crypto.privateDecrypt(
        decryptedUserPrivateKey,
        encryptedBuffer
      );
      return decryptedData.toString("base64");
    } else {
      throw new Error("Invalid encrypted doc key");
    }
  } else {
    throw new Error("This document key decryption needs RSA private key");
  }
}

function newEncryptDocKey(dek: string, userPublicKey: string) {
  const data = Buffer.from(dek, "base64");
  const encryptedData = eciesjs.encrypt(pemToHex(userPublicKey, true), data);
  return "encEcc_" + encryptedData.toString("base64");
}

async function encryptDocKeyWithRsa(dek: string, userSecret: string) {
  if (dek.startsWith("encRsa_")) {
    throw new Error("Dek is already encrypted");
  }
  const data = Buffer.from(dek, "base64");
  const encryptedData = crypto.publicEncrypt(userSecret, data);
  return "encRsa_" + encryptedData.toString("base64");
}

async function updateUserKeyDocByUid(
  uid: string,
  newPublicKey: string,
  newEncPrivateKey: string
): Promise<void> {
  const userKeySnapshot = await admin
    .firestore()
    .collection("userKeys")
    .where("uid", "==", uid)
    .get();

  if (userKeySnapshot.empty) {
    throw new Error(`No user key document found for UID: ${uid}`);
  }

  const userDoc = userKeySnapshot.docs[0];

  // Update the document with the new publicKey and encPrivateKey.
  await userDoc.ref.update({
    publicKey: newPublicKey,
    encPrivateKey: newEncPrivateKey,
    cloudUpdatedAt: Timestamp.now(),
  });

  console.log(`User key document updated for UID: ${uid}`);
}
