import { z } from "zod";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import models from "../v124/models";

export class V124 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    if (source.docCollection == "viewSettings") {
      const viewSettings = source as z.infer<typeof models.ZViewSettings>;

      // Use Zod to derive the default values
      const defaultTodaySettings = models.ZViewSettings.shape.todaySettings._def.defaultValue();
      const defaultPastSettings = models.ZViewSettings.shape.pastSettings._def.defaultValue();
      const defaultFutureSettings = models.ZViewSettings.shape.futureSettings._def.defaultValue();
      const defaultListSettings = models.ZViewSettings.shape.listSettings._def.defaultValue();
      const defaultMoneyTrackerSettings = models.ZViewSettings.shape.moneyTrackerSettings._def.defaultValue();
      const defaultNoteSettings = models.ZViewSettings.shape.noteSettings._def.defaultValue();
      const defaultFeatureSettings = models.ZViewSettings.shape.featureSettings._def.defaultValue();

      // Ensure default values are fully resolved to meet the expected type
      viewSettings.todaySettings = models.ZViewSettings.shape.todaySettings.parse(defaultTodaySettings);
      viewSettings.pastSettings = models.ZViewSettings.shape.pastSettings.parse(defaultPastSettings);
      viewSettings.futureSettings = models.ZViewSettings.shape.futureSettings.parse(defaultFutureSettings);
      viewSettings.listSettings = models.ZViewSettings.shape.listSettings.parse(defaultListSettings);
      viewSettings.moneyTrackerSettings = models.ZViewSettings.shape.moneyTrackerSettings.parse(defaultMoneyTrackerSettings);
      viewSettings.noteSettings = models.ZViewSettings.shape.noteSettings.parse(defaultNoteSettings);
      viewSettings.featureSettings = models.ZViewSettings.shape.featureSettings.parse(defaultFeatureSettings);
    }

    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
