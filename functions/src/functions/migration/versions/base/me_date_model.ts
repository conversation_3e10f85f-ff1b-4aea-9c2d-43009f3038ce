import { z } from "zod";
import { Timestamp } from "firebase-admin/firestore";
import { dateSchema } from "./base_schema";

export const meDateTimeObj = z.object({
  dateTime: dateSchema,
  timeString: z.string(),
  dateString: z.string(),
  timeWithOffset: z.string(),
});

export type IMeDateTime = z.infer<typeof meDateTimeObj>;

export type MeDateTimeFirestoreType = {
  dateTime: Timestamp;
  timeString: string;
  dateString: string;
  timeWithOffset: string;
};
