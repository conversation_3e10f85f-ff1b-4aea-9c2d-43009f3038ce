import { z } from "zod";
// change this import when migrating
import model from "../v132/models";
import { IBaseSchema } from "./base_schema";
import * as commonConfig from "../../../../releaseConfig.json";

// Make the version at package.json when common_config.json is updated.
// It is not a issue technically but it is a good practice to keep the version in package.json updated.
export const currentDbVersion = commonConfig.dbVersion;

// Change Maping of new versions here.
export const ZUser = model.ZUser;
export type IUser = z.infer<typeof ZUser>;

export const ZUserMetadata = model.ZUserMetadata;
export type IUserMetadata = z.infer<typeof ZUserMetadata>;

export const ZUserKeys = model.ZUserKeys;
export type IUserKeys = z.infer<typeof ZUserKeys>;

export const ZViewSettings = model.ZViewSettings;
export type IViewSettings = z.infer<typeof ZViewSettings>;

export const ZUserResources = model.ZUserResources;
export type IUserResources = z.infer<typeof ZUserResources>;

export const ZSubscriptionInfo = model.ZSubscriptionInfo;
export type ISubscriptionInfo = z.infer<typeof ZSubscriptionInfo>;

export const ZTodo = model.ZTodo;
export type ITodo = z.infer<typeof ZTodo>;

export const ZHabitSetup = model.ZHabitSetup;
export type IHabitSetup = z.infer<typeof ZHabitSetup>;

export const ZHabitAction = model.ZHabitAction;
export type IHabitAction = z.infer<typeof ZHabitAction>;

export const ZJournalSetup = model.ZJournalSetup;
export type IJournalSetup = z.infer<typeof ZJournalSetup>;

export const ZJournalAction = model.ZJournalAction;
export type IJournalAction = z.infer<typeof ZJournalAction>;

export const ZNote = model.ZNote;
export type INote = z.infer<typeof ZNote>;

export const ZLists = model.ZLists;
export type IList = z.infer<typeof ZLists>;

export const ZChatMessages = model.ZChatMessage;
export type IChatMessages = z.infer<typeof ZChatMessages>;

export const ZChatUser = model.ZChatUser;
export type IChatUser = z.infer<typeof ZChatUser>;

export const ZIssueReport = model.ZIssueReport;
export type IIssueReport = z.infer<typeof ZIssueReport>;

export const ZFeedback = model.ZFeedback;
export type IFeedback = z.infer<typeof ZFeedback>;

export const ZInAppNotification = model.ZInAppNotification;
export type IInAppNotification = z.infer<typeof ZInAppNotification>;

export const ZMoneyTrackerTransactions = model.ZMoneyTrackerTransactions;
export type IMoneyTrackerTransactions = z.infer<
  typeof ZMoneyTrackerTransactions
>;

export const ZMoneyTrackerSetups = model.ZMoneyTrackerSetups;
export type IMoneyTrackerSetups = z.infer<typeof ZMoneyTrackerSetups>;

export const ZCalendarEventSetup = model.ZCalendarEventSetup;
export type ICalendarEventSetup = z.infer<typeof ZCalendarEventSetup>;

export const ZCalendarEventAction = model.ZCalendarEventAction;
export type ICalendarEventAction = z.infer<typeof ZCalendarEventAction>;

export const ZCalDetails = model.ZCalDetailsType;
export type ICalDetailsType = z.infer<typeof ZCalDetails>;

export const ZCalendarIntegrations = model.ZCalendarIntegrations;
export type ICalendarIntegrations = z.infer<typeof ZCalendarIntegrations>;

export const ZPublicUsers = model.ZPublicUsers;
export type IPublicUsers = z.infer<typeof ZPublicUsers>;

export const ZNoteMetaData = model.ZPublicNoteMetaData;
export type INoteMetaData = z.infer<typeof ZNoteMetaData>;

export const ZListMetaData = model.ZPublicListMetaData;
export type IListMetaData = z.infer<typeof ZListMetaData>;

export const ZSpecialActivities = model.ZSpecialActivities;
export type ISpecialActivities = z.infer<typeof ZSpecialActivities>;

export function getMeParsedData(finalData: IBaseSchema): IBaseSchema {
  try {
    switch (finalData.docCollection) {
      case "users": {
        return ZUser.parse(finalData);
      }
      case "usersMetadata": {
        return ZUserMetadata.parse(finalData);
      }
      case "viewSettings": {
        return ZViewSettings.parse(finalData);
      }
      case "userResources": {
        return ZUserResources.parse(finalData);
      }
      case "todos": {
        return ZTodo.parse(finalData);
      }
      case "habitSetups": {
        return ZHabitSetup.parse(finalData);
      }
      case "habitActions": {
        return ZHabitAction.parse(finalData);
      }
      case "journalSetups": {
        return ZJournalSetup.parse(finalData);
      }
      case "journalActions": {
        return ZJournalAction.parse(finalData);
      }
      case "notes": {
        return ZNote.parse(finalData);
      }
      case "lists": {
        return ZLists.parse(finalData);
      }
      case "chatUsers": {
        return ZChatUser.parse(finalData);
      }
      case "chatMessages": {
        return ZChatMessages.parse(finalData);
      }
      case "issueReports": {
        return ZIssueReport.parse(finalData);
      }
      case "usersFeedback": {
        return ZFeedback.parse(finalData);
      }
      case "inAppNotifications": {
        return ZInAppNotification.parse(finalData);
      }
      case "moneyTrackerTransactions": {
        return ZMoneyTrackerTransactions.parse(finalData);
      }
      case "moneyTrackerSetups": {
        return ZMoneyTrackerSetups.parse(finalData);
      }
      case "calendarEventSetups": {
        return ZCalendarEventSetup.parse(finalData);
      }
      case "calendarEventActions": {
        return ZCalendarEventAction.parse(finalData);
      }
      case "calendarIntegrations": {
        return ZCalendarIntegrations.parse(finalData);
      }
      case "publicUsers": {
        return ZPublicUsers.parse(finalData);
      }
    }
    throw new Error("unknown document found");
  } catch (error) {
    throw new Error(
      `error in parsing document: ${finalData.uid}/${finalData.docCollection}/${finalData.docVer}/${finalData.id}\n${error}`
    );
  }
}
