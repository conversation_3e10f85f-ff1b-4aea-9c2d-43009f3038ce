import { z } from "zod";
import { dateSchema } from "./base_schema";

export const atachmentDetailZodObject = z.object({
  id: z.number(),
  createdAt: dateSchema,
  status: z.enum(["local", "cloud", "temporary"]),
  size: z.number(),
  metadata: z.record(z.string(), z.any()).nullable(),
  format: z.string(),
  localPath: z.string().nullable(),
  deletedAt: dateSchema.nullable(),
  fileType: z.string(),
  containsThumbnail: z.boolean().default(false).nullable().optional(),
  originalFileName: z.string().nullable().optional(),
});

export type IAttachmentModel = z.infer<typeof atachmentDetailZodObject>;

export type ImageMetadata = {
  height: number;
  width: number;
};

export type AudioVideoMetadata = {
  duration: number;
};
