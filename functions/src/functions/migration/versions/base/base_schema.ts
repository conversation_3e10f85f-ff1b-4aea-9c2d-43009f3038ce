import { Timestamp } from "firebase-admin/firestore";
import { z } from "zod";

export const meCollectionNames = [
  "users",
  "usersMetadata",
  "viewSettings",
  "userResources",
  "todos",
  "habitSetups",
  "habitActions",
  "journalSetups",
  "journalActions",
  "notes",
  "lists",
  "chatUsers",
  "chatMessages",
  "supportChat",
  "issueReports",
  "deletedUsersReports",
  "viewedSnippets",
  "usersFeedback",
  "inAppNotifications",
  "moneyTrackerTransactions",
  "moneyTrackerSetups",
  "calendarEventSetups",
  "calendarEventActions",
  "calendarIntegrations",
  "publicUsers",
  "specialActivities",
] as const;

export const ZDocCollectionName = z.enum(meCollectionNames);

export type IDocCollectionName = z.infer<typeof ZDocCollectionName>;

export const dateSchema = z.preprocess((arg: any) => {
  if (arg == undefined) return null;
  if (typeof arg == "string" || arg instanceof Date) return new Date(arg);
  if (arg instanceof Timestamp) return arg.toDate();
  if ("_seconds" in arg && "_nanoseconds" in arg) {
    return new Date(arg._seconds * 1000 + arg._nanoseconds / 1000000);
  }
  return null;
}, z.date());

export const ZSourceOfChange = z.enum(["client", "cloud"]);

export const baseZodObj = {
  id: z.string(),
  docVer: z.number(),
  docCollection: ZDocCollectionName,
  uid: z.string(),
  createdAt: dateSchema.optional(),
  localUpdatedAt: dateSchema,
  cloudUpdatedAt: dateSchema.nullable(),
  deletedAt: dateSchema.nullable().optional(),
  permaDeletedAt: dateSchema.nullable().optional(),
  sessionId: z.string().uuid().optional(),
  source: ZSourceOfChange.optional(),
  encData: z.object({
    dek: z.string().min(1),
    encFields: z.array(z.string()),
  }),
};

export const ZBaseSchema = z.object({
  ...baseZodObj,
});

export type IBaseSchema = z.infer<typeof ZBaseSchema>;

export const zMeThemeColor = z.enum(["blue", "green", "purple", "red", "pink"]);
export type IThemeColor = z.infer<typeof zMeThemeColor>;

export const zAppTheme = z.enum(["dark", "light", "systemDefault"]);
export type IAppTheme = z.infer<typeof zAppTheme>;

export function isSupportCollection(docCollection: string) {
  if (docCollection == "chatMessages" || docCollection == "issueReports") {
    return true;
  }
  return false;
}

export const ZSyncRequestModel = z.object({
  id: z.string(),
  localUpdatedAt: dateSchema,
  updateType: z.enum(["raw", "patch"]).optional().default("raw"),
  isCreate: z.boolean().optional().default(true),
  data: z.string(),
});

export type ISyncRequestModel = z.infer<typeof ZSyncRequestModel>;

export const dataBackupStatsSchema = z.object({
  status: z.enum(["none", "inProgress", "success", "failed"]).default("none"),
  lastUpdatedAt: dateSchema.nullable().default(null),
});

export const zAppLanguage = z.enum([
  "english",
  "french",
  "german",
  "italian",
  "portuguese",
  "spanish",
]);

export type IAppLanguage = z.infer<typeof zAppLanguage>;
