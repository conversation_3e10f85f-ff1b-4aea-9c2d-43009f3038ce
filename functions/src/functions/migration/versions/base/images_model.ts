import { z } from "zod";
import { dateSchema } from "./base_schema";

export const imageDetailZodObject = z.object({
  id: z.number(),
  createdAt: dateSchema,
  status: z.enum(["local", "cloud"]),
  size: z.number(),
  height: z.number(),
  width: z.number(),
  format: z.string(),
  localPath: z.string().nullable(),
  deletedAt: dateSchema.nullable(),
});

export type IImageModel = z.infer<typeof imageDetailZodObject>;
