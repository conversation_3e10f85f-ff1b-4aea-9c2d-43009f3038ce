import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";

export class V114 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    if (source.docCollection === "viewSettings") {
      const encData = source.encData || {};
      const encFields = encData.encFields || [];

      // Add "featureSettings.userGoal" if it is not already in the array
      if (!encFields.includes("featureSettings.userGoal")) {
        encFields.push("featureSettings.userGoal");
      }

      // Update the source encData map
      encData.encFields = encFields;
      source.encData = encData;
    }

    if (
      source.docCollection === "users" ||
      source.docCollection === "usersMetadata"
    ) {
      const encData = source.encData || {};
      const encFields = encData.encFields || [];

      // Remove "userInfo.tags[].tag" and "featureSettings.userGoal" if they exist in the array
      const fieldsToRemove = [
        "userInfo.tags[].tag",
        "featureSettings.userGoal",
      ];
      encData.encFields = encFields.filter(
        (field) => !fieldsToRemove.includes(field)
      );

      // Update the source encData map
      source.encData = encData;
    }

    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
