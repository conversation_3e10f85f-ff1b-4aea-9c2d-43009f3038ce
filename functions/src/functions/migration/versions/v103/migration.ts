import {
  getOptimizedFilePath,
  getOriginalFilePath,
  getThumbnailFilePath,
} from "../../../../utils/utility_methods";
import { AttachmentArrayType } from "../../../save_data/db.repository";
import { AttachmentConstants } from "../../../user_uploads/media/media_constants";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import * as admin from "firebase-admin";
import { IAttachmentModel } from "../base/attachments_model";
import { z } from "zod";
import models from "../v103/models";

export class V103 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;

    if (
      source.docCollection === "users" ||
      source.docCollection === "usersMetadata"
    ) {
      const item = source as z.infer<typeof models.ZUser>;
      item.removedDocsIDs = {
        ...item.removedDocsIDs,
        inAppNotificationIds: [],
      };
    }

    if (
      source.docCollection === "todos" ||
      source.docCollection === "notes" ||
      source.docCollection === "habitActions" ||
      source.docCollection === "journalActions" ||
      source.docCollection === "moneyTrackerTransactions"
    ) {
      const item = source as AttachmentArrayType;
      const task: Promise<void>[] = [];
      for (let i = 0; i < item.attachments.length; i++) {
        const attachment = item.attachments[i];
        if (attachment.deletedAt == null && attachment.status === "cloud") {
          task.push(
            this.moveAttachment(
              attachment,
              source.uid,
              source.docCollection,
              source.id
            )
          );
        }
      }
      await Promise.all(task);
    }
    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }

  async moveAttachment(
    attachment: IAttachmentModel,
    uid: string,
    docCollection: string,
    docId: string
  ) {
    // move attachment to new location
    const oldOriginalPath = `${AttachmentConstants.userDataPath}/${uid}/${docCollection}/originalFiles/${attachment.id}.${attachment.format}`;
    const bucket = admin.storage().bucket();
    const originalFileRef = bucket.file(oldOriginalPath);
    try {
      if ((await originalFileRef.exists())[0]) {
        await originalFileRef.move(
          getOriginalFilePath(
            uid,
            docCollection,
            docId,
            attachment.id,
            attachment.format
          )
        );
      }
      if (attachment.fileType === "image") {
        const oldOptimizedFilePath = `${AttachmentConstants.userDataPath}/${uid}/${docCollection}/optimizedFiles/${attachment.id}.${attachment.format}`;
        const oldOptimizedFileRef = bucket.file(oldOptimizedFilePath);
        if ((await oldOptimizedFileRef.exists())[0]) {
          await oldOptimizedFileRef.move(
            getOptimizedFilePath(
              uid,
              docCollection,
              docId,
              attachment.id,
              attachment.format
            )
          );
        }
        const oldThumbnailFilePath = `${AttachmentConstants.userDataPath}/${uid}/${docCollection}/thumbnails/${attachment.id}.${attachment.format}`;
        const oldThumbnailFileRef = bucket.file(oldThumbnailFilePath);
        if ((await oldThumbnailFileRef.exists())[0]) {
          await oldThumbnailFileRef.move(
            getThumbnailFilePath(
              uid,
              docCollection,
              docId,
              attachment.id,
              attachment.format
            )
          );
        }
      }
    } catch (error) {
      console.error("Error moving attachment", error);
    }
  }
}
