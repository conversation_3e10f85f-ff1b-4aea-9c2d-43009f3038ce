import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema, ZSourceOfChange } from "../base/base_schema";
import { v4 as uuidv4 } from "uuid";

export class V94 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    return {
      ...source,
      createdAt: new Date(),
      sessionId: uuidv4(),
      source: ZSourceOfChange.enum.cloud,
    };
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    delete source.createdAt;
    delete source.sessionId;
    delete source.source;
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
