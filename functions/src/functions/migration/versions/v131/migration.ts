import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";

export class V131 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    
    // Add snapshotOfInviteLink field to lists and notes collections
    if (source.docCollection === "lists" || source.docCollection === "notes") {
      (source as any).snapshotOfInviteLink = null;
    }

    // Remove devicesList field from users and usersMetadata collections
    if (source.docCollection === "users" || source.docCollection === "usersMetadata") {
      if ((source as any).userInfo && (source as any).userInfo.devicesList) {
        delete (source as any).userInfo.devicesList;
      }
    }
    
    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    
    // Remove snapshotOfInviteLink field from lists and notes collections
    if (source.docCollection === "lists" || source.docCollection === "notes") {
      if ((source as any).snapshotOfInviteLink !== undefined) {
        delete (source as any).snapshotOfInviteLink;
      }
    }
    
    // Add back empty devicesList field to users and usersMetadata collections
    if (source.docCollection === "users" || source.docCollection === "usersMetadata") {
      if ((source as any).userInfo && !(source as any).userInfo.devicesList) {
        (source as any).userInfo.devicesList = [];
      }
    }
    
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
