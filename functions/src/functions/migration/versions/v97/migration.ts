import { z } from "zod";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import models from "../v97/models";
import { getEmailByIdFromAuthService } from "../../../../utils/utility_methods";

export class V97 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    if (source.docCollection == "lists") {
      type IList = z.infer<typeof models.ZLists>;
      const list = source as unknown as IList;
      const email = await getEmailByIdFromAuthService(list.uid);
      // Add new fields
      list.isPublic = false;
      list.publicId = null;
      list.ownerEmail = email ?? "";
      list.ownerName = "";
      const members = {
        memberHashedEmails: [] as string[],
        membersConfig: {} as Record<string, any>,
      };
      list.members = members;
    }

    if (source.docCollection == "users") {
      type IUser = z.infer<typeof models.ZUser>;
      const user = source as unknown as IUser;
      // Add new fields
      user.shared.sharedLists = [];
    }
    // Added new List Model
    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
