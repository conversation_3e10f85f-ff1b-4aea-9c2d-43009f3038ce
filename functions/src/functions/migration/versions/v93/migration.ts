import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import * as admin from "firebase-admin";
import models92 from "../v92/models";
import { z } from "zod";
import { Timestamp } from "firebase-admin/firestore";
import models93 from "../v93/models";

export class V93 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    if (source.docCollection == "users") {
      const db = admin.firestore();

      type IUser92 = z.infer<typeof models92.ZUser>;
      const userData = source as unknown as IUser92;
      const calInt = userData.calendarIntegrations;
      await db
        .collection("calendarIntegrations")
        .doc(userData.uid)
        .set({
          id: userData.uid,
          docVer: this.docVer,
          docCollection: "calendarIntegrations",
          uid: userData.uid,
          localUpdatedAt: Timestamp.now(),
          cloudUpdatedAt: Timestamp.now(),
          deletedAt: null,
          permaDeletedAt: null,
          encData: {
            dek: "0",
            encFields: [],
          },
          googleWebhookExpiry: calInt.googleWebhookExpiry,
          microsoftWebhookExpiry: calInt.microsoftWebhookExpiry,
          googleCalendars: calInt.googleCalendars,
          microsoftCalendars: calInt.microsoftCalendars,
        });
      return {
        ...source,
        calendarIntegrations: {},
      };
    }

    if (source.docCollection == "usersMetadata") {
      return {
        ...source,
        calendarIntegrations: {},
      };
    }
    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    if (
      source.docCollection == "users" ||
      source.docCollection == "usersMetadata"
    ) {
      type ICalendarIntegrations92 = z.infer<typeof models93.ZCalendarIntegrations>;
      const db = admin.firestore();
      const data = await db
        .collection("calendarIntegrations")
        .doc(source.uid)
        .get();
      if (data.exists) {
        const calInt = data.data() as unknown as ICalendarIntegrations92;
        return {
          ...source,
          calendarIntegrations: {
            googleWebhookExpiry: calInt.googleWebhookExpiry,
            microsoftWebhookExpiry: calInt.microsoftWebhookExpiry,
            googleCalendars: calInt.googleCalendars,
            microsoftCalendars: calInt.microsoftCalendars,
          },
        };
      } else {
        return {
          ...source,
          calendarIntegrations: {
            googleWebhookExpiry: null,
            microsoftWebhookExpiry: null,
            googleCalendars: [],
            microsoftCalendars: [],
          },
        };
      }
    }
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
