import { z } from "zod";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import models from "../v102/models";
import { getEmailByIdFromAuthService } from "../../../../utils/utility_methods";

export class V102 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;

    if (source.docCollection == "notes") {
      type INote = z.infer<typeof models.ZLists>;
      const note = source as unknown as INote;
      const email = await getEmailByIdFromAuthService(note.uid);
      // Add new fields
      note.isPublic = false;
      note.publicId = null;
      note.ownerEmail = email ?? "";
      note.ownerName = "";
      const members = {
        memberHashedEmails: [] as string[],
        membersConfig: {} as Record<string, any>,
      };
      note.members = members;
    }

    if (source.docCollection == "users") {
      type IUser = z.infer<typeof models.ZUser>;
      const user = source as unknown as IUser;
      // Add new fields
      user.public = user.public || {};
      user.shared = user.shared || {};

      // Add new fields
      user.shared.sharedNotes = [];
      user.public.publicLists = [];
      user.public.publicNotes = [];
    }

    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
