import { getMeEnvironment } from "../../../../me_config";
import {
  encryptDocKey,
  getAESDecryptionCypher,
  getAESEncryptionCypher,
  getNewSecret,
  getUserKeyDocByEmail,
  iterateMap,
} from "../../../../utils/encryption/encryption";
import * as crypto from "crypto";
import {
  getApiSecret,
  getEmailByIdFromAuthService,
} from "../../../../utils/utility_methods";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";

export class V90 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, helperData: Map<string, string>) {
    source.docVer = this.docVer;
    let userOldSecretById = "";
    let email = "";
    let publicKey = "";
    if (!helperData.has("userKmsSecretWithId")) {
      helperData.set(
        "userKmsSecretWithId",
        (await getSecretFromKmsById(source.uid)).key
      );
    }
    if (!helperData.has("email")) {
      const result = (await getEmailByIdFromAuthService(source.uid)) ?? "";
      helperData.set("email", result);
    }
    userOldSecretById = helperData.get("userKmsSecretWithId") as string;
    email = helperData.get("email") as string;

    if (!helperData.has("userPublicKey")) {
      const userDocKey = await getUserKeyDocByEmail(email);
      if (userDocKey != null) {
        helperData.set("userPublicKey", userDocKey.publicKey);
      }
    }
    publicKey = helperData.get("userPublicKey") as string;

    if (source.encData.dek == "") {
      const secret = getNewSecret();
      source.encData.dek = await encryptDocKeyInOldFormat(
        secret,
        userOldSecretById
      );
    }

    // decrytp doc secret
    const docUnEncryptedSecret = await decryptDocKey(
      source.encData.dek,
      userOldSecretById
    );

    for (const field of source.encData.encFields) {
      const parts = field.split(".");
      iterateMapWithOldEncryption(source, parts, 0, docUnEncryptedSecret);
    }
    for (const field of source.encData.encFields) {
      const parts = field.split(".");
      iterateMap(
        source,
        parts,
        0,
        docUnEncryptedSecret,
        true,
        source.id,
        source.docCollection
      );
    }
    source.encData.dek = await encryptDocKey(
      docUnEncryptedSecret,
      source.uid,
      publicKey
    );
    return source;
  }

  async downgradeSchema(
    source: IBaseSchema,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema> {
    source.docVer = this.docVer - 1;
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}

interface MapType {
  [key: string]: any;
}

function iterateMapWithOldEncryption(
  map: MapType,
  fields: string[],
  index: number,
  secret: string
): number {
  let value: any = map;
  for (let i = index; i < fields.length; ) {
    const part = fields[i];
    if (part.includes("[]")) {
      value = value[part.replace("[]", "")] as any[];
      let temp = 0;
      for (let j = 0; j < value.length; j++) {
        temp = iterateMapWithOldEncryption(value[j], fields, i + 1, secret);
      }
      i = temp;
    } else if (part.includes("<>")) {
      value = value[part.replace("<>", "")] as MapType;
      let temp = 0;
      const keys = Object.keys(value);
      for (let j = 0; j < keys.length; j++) {
        temp = iterateMapWithOldEncryption(
          value[keys[j]],
          fields,
          i + 1,
          secret
        );
      }
      i = temp;
    } else if (i < fields.length - 1) {
      i = iterateMapWithOldEncryption(map[part], fields, i + 1, secret);
    } else {
      try {
        if (value[part] !== null && value[part] !== "") {
          value[part] = decryptTextDataOldFormat(value[part], secret);
        }
      } catch (error) {
        console.log(
          `error encrypting/decrypting for ${map["docCollection"]}/${fields.join(".")}: ${error}`
        );
      }
    }
    return i;
  }
  return -1;
}

function decryptTextDataOldFormat(
  encryptedData: string,
  secret: string
): string {
  if (encryptedData == null || encryptedData == "") {
    throw new Error("Data to decrypt is null or empty");
  }
  const encryptedBuffer = Buffer.from(encryptedData, "base64");
  const iv = encryptedBuffer.subarray(0, 16);
  const decipher = getAESDecryptionCypher(secret, iv);
  const decryptedData = Buffer.concat([
    decipher.update(encryptedBuffer.subarray(16)),
    decipher.final(),
  ]);
  return decryptedData.toString("utf-8");
}

export async function encryptDocKeyInOldFormat(
  dek: string,
  userSecretKey: string
) {
  const iv = crypto.randomBytes(16);
  const cipher = getAESEncryptionCypher(userSecretKey, iv);
  const encryptedData = Buffer.concat([
    iv,
    cipher.update(dek, "base64"),
    cipher.final(),
  ]);
  return encryptedData.toString("base64");
}

export async function decryptDocKey(
  encryptedDek: string,
  userSecretKey: string
) {
  try {
    const encryptedBuffer = Buffer.from(encryptedDek, "base64");
    const iv = encryptedBuffer.subarray(0, 16);
    const decipher = getAESDecryptionCypher(userSecretKey, iv);
    const decryptedData = Buffer.concat([
      decipher.update(encryptedBuffer.subarray(16)),
      decipher.final(),
    ]);
    return decryptedData.toString("base64");
  } catch (error) {
    throw new Error("Error decrypting doc key: " + error);
  }
}

async function getSecretFromKmsById(id: string) {
  console.log("fetching key for user: " + id);
  const payload = {
    uid: id,
    apiSecret: getApiSecret(),
  };
  // return "tesPGz04JWs87OPR6m8p1NWXwHsrnSjL";
  const url = `https://europe-west1-mevolve-kms.cloudfunctions.net/getSecret${getMeEnvironment()}`;
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  };
  const response = await fetch(url, options);
  console.log(response.status);
  if (response.status == 200) {
    return await response.json();
  } else if (response.status == 401) {
    console.log("Error fetching key");
    throw new Error("Authorization error");
  } else {
    console.log("Error fetching key");
    throw new Error("Error fetching key");
  }
}
