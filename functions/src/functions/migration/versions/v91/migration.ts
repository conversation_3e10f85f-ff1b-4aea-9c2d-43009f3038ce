import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";

export class V91 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    if (source.docCollection == "users") {
      return {
        ...source,
        calendarIntegrations: {
          googleWebhookExpiry: null,
          googleCalendars: [],
          microsoftWebhookExpiry: null,
          microsoftCalendars: [],
        },
        removedDocsIDs: {
          todos: [],
          notes: [],
          lists: [],
          habitActions: [],
          habitSetups: [],
          journalActions: [],
          journalSetups: [],
          moneyTrackerTransactions: [],
          calendarEventSetups: [],
          calendarEventActions: [],
          calendarAccountIds: [],
          calendarIds: [],
        },
      };
    }
    if (source.docCollection == "usersMetadata") {
      return {
        ...source,
        calendarIntegrations: {
          googleWebhookExpiry: null,
          googleCalendars: [],
          microsoftWebhookExpiry: null,
          microsoftCalendars: [],
        },
      };
    }
    return source;
  }

  async downgradeSchema(
    source: IBaseSchema,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema> {
    source.docVer = this.docVer - 1;
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
