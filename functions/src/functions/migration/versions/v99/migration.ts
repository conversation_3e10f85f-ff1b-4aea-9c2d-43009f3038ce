import { z } from "zod";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import models from "../v98/models";
import * as admin from "firebase-admin";
import { v4 as uuidv4 } from "uuid";
import { Timestamp } from "firebase-admin/firestore";
import { encryptDocKey, getNewSecret, getUserKeyDocById } from "../../../../utils/encryption/encryption";

export class V99 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    if (_isUpgrading) {
      try {
        const userId = _helperData.get("userId");
        if (!userId) {
          throw new Error("userId not found in helperData");
        }
        const userPublicKey = (await getUserKeyDocById(userId))?.publicKey;
        const secret = getNewSecret();
        const encData = {
          dek: await encryptDoc<PERSON><PERSON>(secret, userId, userPublicKey),
          encFields: [],
        };
        _helperData.set("encDataDek", encData.dek);
      } catch (error) {
        console.error("Error in preMigrationSetup:", error);
        throw error;
      }
    }
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    // Calendar Individual account migration
    if (source.docCollection == "users") {
      type IUser97 = z.infer<typeof models.ZUser>;
      const userData = source as unknown as IUser97;
      const appSettings = {
        ...userData.appSettings,
        isSpeechToTextEnabled: true,
      }
      return {
        ...source,
        appSettings: appSettings,
      }
    }
    if (source.docCollection == "calendarIntegrations") {
      const db = admin.firestore();
      const batch = db.batch();
      type ICalendarIntegrations98 = z.infer<typeof models.ZCalendarIntegrations>;
      const calInt = source as unknown as ICalendarIntegrations98;
      for (let i = 0; i < calInt.googleCalendars.length; i++) {
        const calInfo = calInt.googleCalendars[i];
        const id = uuidv4();
        batch.set(db.collection("calendarIntegrations").doc(id), {
          ...calInfo,
          id: id,
          docVer: this.docVer,
          docCollection: "calendarIntegrations",
          uid: calInt.uid,
          localUpdatedAt: Timestamp.now(),
          cloudUpdatedAt: Timestamp.now(),
          deletedAt: null,
          permaDeletedAt: null,
          encData: {
            dek: _helperData.get("encDataDek"),
            encFields: [],
          },
          webhookExpiry: calInt.googleWebhookExpiry,
          email: calInfo.id,
        });
      }
      for (let i = 0; i < calInt.microsoftCalendars.length; i++) {
        const calInfo = calInt.microsoftCalendars[i];
        const id = uuidv4();
        batch.set(db.collection("calendarIntegrations").doc(id), {
          ...calInfo,
          id: id,
          docVer: this.docVer,
          docCollection: "calendarIntegrations",
          uid: calInt.uid,
          localUpdatedAt: Timestamp.now(),
          cloudUpdatedAt: Timestamp.now(),
          deletedAt: null,
          permaDeletedAt: null,
          encData: {
            dek: _helperData.get("encDataDek"),
            encFields: [],
          },
          webhookExpiry: calInt.microsoftWebhookExpiry,
          email: calInfo.id,
        })
      }
      await batch.commit();
      return {
        ...source,
        docVer: 0,
      }
    }
    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    if (_isUpgrading) {
      try {
        const userId = _helperData.get("userId");
        if (!userId) {
          throw new Error("userId not found in helperData");
        }
        const db = admin.firestore();
        await db
          .collection("calendarIntegrations")
          .doc(userId).delete();
      } catch (error) {
        console.error("Error in postMigrationSetup:", error);
        throw error;
      }
    }

    return [];
  }
}
