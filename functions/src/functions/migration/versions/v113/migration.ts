import { z } from "zod";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema, ZSourceOfChange } from "../base/base_schema";
import { v1 as uuidv1 } from "uuid";
import oldModels from "../v112/models";
import newModels from "../v113/models";
import * as admin from "firebase-admin";
import { convertDatesToTimestamps } from "../../../../utils/utility_methods";

export class V113 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    if (source.docCollection === "users") {
      const uid = _helperData.get("userId") as string;
      const userDoc = source as z.infer<typeof oldModels.ZUser>;
      const viewSettingsDoc = await getViewSettingsDocument(
        uid,
        userDoc.encData.dek
      );
      viewSettingsDoc.appSettings = userDoc.appSettings;
      viewSettingsDoc.featureSettings = userDoc.featureSettings;
      viewSettingsDoc.futureSettings = userDoc.futureSettings;
      viewSettingsDoc.insightSettings = userDoc.insightSettings;
      viewSettingsDoc.listSettings = userDoc.listSettings;
      viewSettingsDoc.moneyTrackerSettings = userDoc.moneyTrackerSettings;
      viewSettingsDoc.noteSettings = userDoc.noteSettings;
      viewSettingsDoc.notificationSettings = userDoc.notificationSettings;
      viewSettingsDoc.pastSettings = userDoc.pastSettings;
      viewSettingsDoc.todaySettings = userDoc.todaySettings;

      const userResourcesDoc = await getUserResourcesDocument(
        uid,
        userDoc.encData.dek
      );
      userResourcesDoc.tags = userDoc.userInfo.tags;
      userResourcesDoc.favorites = userDoc.userInfo.favorites;
      userResourcesDoc.public = userDoc.public;
      userResourcesDoc.shared = userDoc.shared;

      _helperData.set("viewSettingsDoc", JSON.stringify(viewSettingsDoc));
      _helperData.set("userResourcesDoc", JSON.stringify(userResourcesDoc));
    }

    if (
      source.docCollection === "users" ||
      source.docCollection === "usersMetadata"
    ) {
      source = newModels.ZUser.strip().parse(source);
    }

    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    const viewSettingsDoc = JSON.parse(
      _helperData.get("viewSettingsDoc") as string
    ) as z.infer<typeof newModels.ZViewSettings>;
    const userResourcesDoc = JSON.parse(
      _helperData.get("userResourcesDoc") as string
    ) as z.infer<typeof newModels.ZUserResources>;
    await Promise.all([
      admin
        .firestore()
        .collection(viewSettingsDoc.docCollection)
        .doc(viewSettingsDoc.id)
        .set(convertDatesToTimestamps(newModels.ZViewSettings.parse(viewSettingsDoc))),
      admin
        .firestore()
        .collection(userResourcesDoc.docCollection)
        .doc(userResourcesDoc.id)
        .set(convertDatesToTimestamps(newModels.ZUserResources.parse(userResourcesDoc))),
    ]);

    // Return new documents so they can be included in the next migration
    return [viewSettingsDoc, userResourcesDoc];
  }
}

async function getViewSettingsDocument(
  uid: string,
  docKey: string
): Promise<z.infer<typeof newModels.ZViewSettings>> {
  const encData = {
    dek: docKey,
    encFields: [
      "featureSettings.userGoal"
    ],
  };

  return newModels.ZViewSettings.parse({
    id: uuidv1(),
    docVer: 113,
    docCollection: "viewSettings" as IBaseSchema["docCollection"],
    uid: uid,
    localUpdatedAt: new Date(),
    cloudUpdatedAt: new Date(),
    createdAt: new Date(),
    sessionId: uuidv1(),
    source: ZSourceOfChange.enum.cloud,
    encData: encData,
  });
}

async function getUserResourcesDocument(
  uid: string,
  dek: string
): Promise<z.infer<typeof newModels.ZUserResources>> {
  const encData = {
    dek: dek,
    encFields: ["tags[].tag"],
  };
  return newModels.ZUserResources.parse({
    id: uuidv1(),
    docVer: 113,
    docCollection: "userResources" as IBaseSchema["docCollection"],
    uid: uid,
    localUpdatedAt: new Date(),
    cloudUpdatedAt: new Date(),
    createdAt: new Date(),
    sessionId: uuidv1(),
    source: ZSourceOfChange.enum.cloud,
    encData: encData,
  });
}
