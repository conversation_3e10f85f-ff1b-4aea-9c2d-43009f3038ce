import { z } from "zod";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import models from "../v115/models";

export class V116 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    if (
      source.docCollection == "users" ||
      source.docCollection == "usersMetadata"
    ) {
      const userDoc = source as z.infer<typeof models.ZUser>;
      const userInfo = userDoc.userInfo;
      return {
        ...source,
        userInfo: {
          ...userInfo,
          isUsingCustomKey: false,
        },
      };
    }
    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
