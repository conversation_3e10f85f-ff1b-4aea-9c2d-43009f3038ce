import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import models from "./models";

type IViewSettings = ReturnType<typeof models.ZViewSettings.parse>;

/**
 * V132 Migration: Migrate isPaused field from setup documents to hidden maps in viewSettings,
 * remove public field from userResources, and remove position field from lists
 *
 * UPGRADE FLOW (V131 → V132):
 * - Remove isPaused field from habitSetups, journalSetups, moneyTrackerSetups, calendarIntegrations
 * - Migrate paused setup IDs to hidden maps in viewSettings.featureSettings
 * - Remove public field from userResources documents
 * - Remove position field from lists documents
 *
 * DOWNGRADE FLOW (V132 → V131):
 * - Extract hidden maps from viewSettings.featureSettings
 * - Restore isPaused field to setup documents based on hidden map data
 * - Remove hidden map fields from viewSettings
 * - Restore public field to userResources documents
 * - Restore position field to lists documents (set to 0 as default)
 */
export class V132 extends BaseMigrationScript {
  // Helper constants for better readability
  private readonly PAUSED_MAP_KEYS = {
    habitSetups: "pausedHabits",
    journalSetups: "pausedJournals",
    moneyTrackerSetups: "pausedMoneytrackers",
    calendarIntegrations: "pausedCalendars",
  } as const;

  private readonly HIDDEN_FIELD_NAMES = {
    pausedHabits: "hiddenHabits",
    pausedJournals: "hiddenJournals",
    pausedMoneytrackers: "hiddenMoneytrackers",
    pausedCalendars: "hiddenCalendars",
  } as const;

  /**
   * PRE-MIGRATION SETUP
   * Initializes helperData maps to coordinate data between documents
   * @param {boolean} _isUpgrading - Boolean indicating whether this is an upgrade or downgrade
   * @param {Map<string, any>} helperData - Map to store data between migration steps
   */
  async preMigrationSetup(
    _isUpgrading: boolean,
    helperData: Map<string, any>
  ): Promise<void> {
    // Initialize empty objects for each paused type
    Object.values(this.PAUSED_MAP_KEYS).forEach((key) => {
      helperData.set(key, {});
    });

    helperData.set("viewSettingsProcessed", false);
    helperData.set("uid", "");

    return Promise.resolve();
  }

  /**
   * UPGRADE SCHEMA (V131 → V132)
   * @param {IBaseSchema} source - The document being migrated
   * @param {Map<string, any>} helperData - Map to store data between migration steps
   */
  async upgradeSchema(source: IBaseSchema, helperData: Map<string, any>) {
    source.docVer = this.docVer;

    // Store uid for potential postMigration operations
    if (source.uid) {
      helperData.set("uid", source.uid);
    }

    // Handle setup documents
    if (this.isSetupDocument(source.docCollection)) {
      await this.processSetupDocumentUpgrade(source, helperData);
    }

    // Handle viewSettings document
    else if (source.docCollection === "viewSettings") {
      await this.processViewSettingsUpgrade(source, helperData);
    }

    // Handle userResources document
    else if (source.docCollection === "userResources") {
      await this.processUserResourcesUpgrade(source, helperData);
    }

    // Handle lists document
    else if (source.docCollection === "lists") {
      await this.processListsUpgrade(source, helperData);
    }

    return source;
  }

  /**
   * DOWNGRADE SCHEMA (V132 → V131)
   * @param {IBaseSchema} source - The document being migrated
   * @param {Map<string, any>} helperData - Map to store data between migration steps
   */
  async downgradeSchema(source: IBaseSchema, helperData: Map<string, any>) {
    source.docVer = this.docVer - 1;

    // Store uid for potential postMigration operations
    if (source.uid) {
      helperData.set("uid", source.uid);
    }

    // Handle setup documents
    if (this.isSetupDocument(source.docCollection)) {
      await this.processSetupDocumentDowngrade(source, helperData);
    }

    // Handle viewSettings document
    else if (source.docCollection === "viewSettings") {
      await this.processViewSettingsDowngrade(source, helperData);
    }

    // Handle userResources document
    else if (source.docCollection === "userResources") {
      await this.processUserResourcesDowngrade(source, helperData);
    }

    // Handle lists document
    else if (source.docCollection === "lists") {
      await this.processListsDowngrade(source, helperData);
    }

    return source;
  }

  /**
   * POST-MIGRATION CLEANUP
   * @param {boolean} isUpgrading - Boolean indicating whether this is an upgrade or downgrade
   * @param {Map<string, any>} helperData - Map containing data from migration steps
   * @param {IBaseSchema[]} allUserData - Array of all user documents
   */
  async postMigration(
    isUpgrading: boolean,
    helperData: Map<string, any>,
    allUserData?: IBaseSchema[]
  ): Promise<IBaseSchema[]> {
    const uid = helperData.get("uid") || "";

    if (!uid) {
      return [];
    }

    if (isUpgrading) {
      return await this.handleUpgradePostMigration(
        uid,
        helperData,
        allUserData
      );
    } else {
      return await this.handleDowngradePostMigration(helperData, allUserData);
    }
  }

  // Helper methods for better organization

  private isSetupDocument(collection: string): boolean {
    return Object.keys(this.PAUSED_MAP_KEYS).includes(collection);
  }

  private async processSetupDocumentUpgrade(
    source: IBaseSchema,
    helperData: Map<string, any>
  ): Promise<void> {
    const mapKey =
      this.PAUSED_MAP_KEYS[
        source.docCollection as keyof typeof this.PAUSED_MAP_KEYS
      ];

    // Collect paused data before removing the field
    if ((source as any).isPaused === true) {
      const pausedMap = helperData.get(mapKey) || {};

      // Store the timestamp directly - no conversion
      const timestamp = source.localUpdatedAt || source.createdAt;
      pausedMap[source.id] = timestamp;
      helperData.set(mapKey, pausedMap);
    }

    // Remove isPaused field
    if ((source as any).isPaused !== undefined) {
      delete (source as any).isPaused;
    }
  }

  private async processViewSettingsUpgrade(
    source: IBaseSchema,
    helperData: Map<string, any>
  ): Promise<void> {
    helperData.set("viewSettingsProcessed", true);

    if (!(source as any).featureSettings) {
      (source as any).featureSettings = {};
    }

    const featureSettings = (source as any).featureSettings;

    // Initialize hidden maps and apply any collected data
    Object.entries(this.HIDDEN_FIELD_NAMES).forEach(
      ([pausedKey, hiddenField]) => {
        if (!featureSettings[hiddenField]) {
          featureSettings[hiddenField] = {};
        }

        const pausedData = helperData.get(pausedKey) || {};

        // Merge with existing data
        featureSettings[hiddenField] = {
          ...featureSettings[hiddenField],
          ...pausedData,
        };
      }
    );
  }

  private async processSetupDocumentDowngrade(
    source: IBaseSchema,
    helperData: Map<string, any>
  ): Promise<void> {
    const mapKey =
      this.PAUSED_MAP_KEYS[
        source.docCollection as keyof typeof this.PAUSED_MAP_KEYS
      ];

    if ((source as any).isPaused === undefined) {
      const pausedMap = helperData.get(mapKey) || {};
      const wasPaused = Object.prototype.hasOwnProperty.call(
        pausedMap,
        source.id
      );
      (source as any).isPaused = wasPaused;

      // Remove from helperData since we've processed it
      if (wasPaused) {
        delete pausedMap[source.id];
        helperData.set(mapKey, pausedMap);
      }
    }
  }

  private async processViewSettingsDowngrade(
    source: IBaseSchema,
    helperData: Map<string, any>
  ): Promise<void> {
    helperData.set("viewSettingsProcessed", true);

    if (!(source as any).featureSettings) {
      return;
    }

    const featureSettings = (source as any).featureSettings;

    // Extract and store hidden maps data
    Object.entries(this.HIDDEN_FIELD_NAMES).forEach(
      ([pausedKey, hiddenField]) => {
        if (featureSettings[hiddenField] !== undefined) {
          // Store the data directly in helperData
          const existingData = helperData.get(pausedKey) || {};
          const mergedData = {
            ...existingData,
            ...featureSettings[hiddenField],
          };

          helperData.set(pausedKey, mergedData);
          delete featureSettings[hiddenField];
        }
      }
    );

    // Remove ordering fields from noteSettings and listSettings for v131
    this.removeOrderingFields(source as any);
  }

  private async processUserResourcesUpgrade(
    source: IBaseSchema,
    _helperData: Map<string, any>
  ): Promise<void> {
    // Remove public field from userResources document
    if ((source as any).public !== undefined) {
      delete (source as any).public;
    }
  }

  private async processUserResourcesDowngrade(
    source: IBaseSchema,
    _helperData: Map<string, any>
  ): Promise<void> {
    // Restore public field to userResources document
    if ((source as any).public === undefined) {
      (source as any).public = {
        publicLists: [],
        publicNotes: [],
      };
    }
  }

  private async processListsUpgrade(
    source: IBaseSchema,
    _helperData: Map<string, any>
  ): Promise<void> {
    // Remove position field from lists document
    if ((source as any).position !== undefined) {
      delete (source as any).position;
    }
  }

  private async processListsDowngrade(
    source: IBaseSchema,
    _helperData: Map<string, any>
  ): Promise<void> {
    // Restore position field to lists document (set to 0 as default)
    if ((source as any).position === undefined) {
      (source as any).position = 0;
    }
  }

  private removeOrderingFields(source: any): void {
    // Remove ordering field from noteSettings
    if (source.noteSettings && source.noteSettings.ordering !== undefined) {
      delete source.noteSettings.ordering;
    }

    // Remove ordering field from listSettings
    if (source.listSettings && source.listSettings.ordering !== undefined) {
      delete source.listSettings.ordering;
    }
  }

  private async handleUpgradePostMigration(
    uid: string,
    helperData: Map<string, any>,
    allUserData?: IBaseSchema[]
  ): Promise<IBaseSchema[]> {
    // Find viewSettings document from allUserData
    const viewSettingsDoc = allUserData?.find(
      (doc) => doc.docCollection === "viewSettings" && doc.uid === uid
    );

    if (!viewSettingsDoc) {
      return [];
    }

    // Collect all remaining paused data
    let hasRemainingData = false;

    Object.entries(this.HIDDEN_FIELD_NAMES).forEach(([pausedKey]) => {
      const pausedData = helperData.get(pausedKey) || {};
      if (Object.keys(pausedData).length > 0) {
        hasRemainingData = true;
      }
    });

    if (!hasRemainingData) {
      return [];
    }

    // Create updated viewSettings document
    const updatedViewSettings = { ...viewSettingsDoc } as IViewSettings;

    if (!updatedViewSettings.featureSettings) {
      updatedViewSettings.featureSettings =
        {} as IViewSettings["featureSettings"];
    }

    const featureSettings = updatedViewSettings.featureSettings;

    // Merge remaining data with existing data
    Object.entries(this.HIDDEN_FIELD_NAMES).forEach(
      ([pausedKey, hiddenField]) => {
        const pausedData = helperData.get(pausedKey) || {};
        if (Object.keys(pausedData).length > 0) {
          const currentHiddenData = featureSettings[hiddenField] || {};

          // Merge with existing data
          featureSettings[hiddenField] = {
            ...currentHiddenData,
            ...pausedData,
          };
        }
      }
    );

    return [updatedViewSettings as IBaseSchema];
  }

  private async handleDowngradePostMigration(
    helperData: Map<string, any>,
    allUserData?: IBaseSchema[]
  ): Promise<IBaseSchema[]> {
    const updatedDocuments: IBaseSchema[] = [];

    if (!allUserData) {
      return [];
    }

    const uid = helperData.get("uid") || "";

    // Handle setup documents
    for (const [collection, pausedKey] of Object.entries(
      this.PAUSED_MAP_KEYS
    )) {
      const pausedData = helperData.get(pausedKey) || {};
      const ids = Object.keys(pausedData);

      if (ids.length === 0) continue;

      // Find and update documents from allUserData
      for (const id of ids) {
        const document = allUserData.find(
          (doc) => doc.docCollection === collection && doc.id === id
        );

        if (document) {
          const updatedDoc = { ...document };
          (updatedDoc as any).isPaused = true;

          updatedDocuments.push(updatedDoc);
        }
      }
    }

    // Handle viewSettings document - remove hidden fields
    const viewSettingsDoc = allUserData.find(
      (doc) => doc.docCollection === "viewSettings" && doc.uid === uid
    );

    if (viewSettingsDoc) {
      const updatedViewSettings = { ...viewSettingsDoc } as any;

      if (updatedViewSettings.featureSettings) {
        const featureSettings = updatedViewSettings.featureSettings;

        // Remove hidden fields
        Object.values(this.HIDDEN_FIELD_NAMES).forEach((hiddenField) => {
          if (featureSettings[hiddenField] !== undefined) {
            delete featureSettings[hiddenField];
          }
        });

        updatedDocuments.push(updatedViewSettings);
      }
    }

    return updatedDocuments;
  }
}
