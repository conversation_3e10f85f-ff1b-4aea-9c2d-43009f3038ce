import { z } from "zod";
import {
  baseZodObj,
  dataBackupStatsSchema,
  dateSchema,
  zAppLanguage,
  zAppTheme,
  ZDocCollectionName,
} from "../base/base_schema";
import { meDateTimeObj } from "../base/me_date_model";
import { atachmentDetailZodObject } from "../base/attachments_model";

const ZCalDetailsType = z.object({
  ...baseZodObj,
  calendarType: z.string(),
  accessToken: z.string(),
  refreshToken: z.string().nullable(),
  id: z.string(),
  email: z.string(),
  emailHash: z.string().nullable().optional(),
  emailGroupHash: z.string().nullable().optional(),
  name: z.string().nullable().default(""),
  webhookExpiry: dateSchema.nullable(),
  lastSync: dateSchema.nullable(),
  status: z.string().nullable().optional().default("deleted"),
  calendarGroups: z
    .object({
      id: z.string(),
      title: z.string().nullable(),
      description: z.string().nullable(),
      nextSyncToken: z.string().nullable(),
      nextPageToken: z.string().nullable(),
      syncStatus: z.string().nullable().optional().default("disabled"),
      resourceId: z.string().nullable().optional().default(null),
      channelId: z.string().nullable().optional().default(null),
    })
    .array()
    .default([]),
  syncTodos: z.string().nullable().optional().default("disabled"),
  syncHabits: z.string().nullable().optional().default("disabled"),
  syncJournals: z.string().nullable().optional().default("disabled"),
  resyncLimit: z.number().nullable().optional().default(0),
  resourceId: z.string().nullable().optional().default(null),
  channelId: z.string().nullable().optional().default(null),
  mevolveStatus: z.string().nullable().optional().default("deleted"),
});

const calendarIntegrationObject = {
  ...baseZodObj,
  calendarType: z.string(),
  accessToken: z.string(),
  refreshToken: z.string().nullable(),
  id: z.string(),
  name: z.string().nullable().default(""),
  email: z.string(),
  emailHash: z.string().nullable().optional(),
  emailGroupHash: z.string().nullable().optional(),
  webhookExpiry: dateSchema.nullable(),
  lastSync: dateSchema.nullable(),
  status: z.string().nullable().optional().default("deleted"),
  calendarGroups: z
    .object({
      id: z.string(),
      title: z.string().nullable(),
      description: z.string().nullable(),
      nextSyncToken: z.string().nullable(),
      nextPageToken: z.string().nullable(),
      syncStatus: z.string().nullable().optional().default("disabled"),
      resourceId: z.string().nullable().optional().default(null),
      channelId: z.string().nullable().optional().default(null),
    })
    .array()
    .default([]),
  syncTodos: z.string().nullable().optional().default("disabled"),
  syncHabits: z.string().nullable().optional().default("disabled"),
  syncJournals: z.string().nullable().optional().default("disabled"),
  resyncLimit: z.number().nullable().optional().default(0),
  resourceId: z.string().nullable().optional().default(null),
  channelId: z.string().nullable().optional().default(null),
  mevolveStatus: z.string().nullable().optional().default("deleted"),
};

const ZSubscriptionInfo = z
  .object({
    subscriptionState: z
      .enum(["none", "subscribed", "subscriptionExpired"])
      .default("none"),
    subscriptionStartDate: dateSchema.nullable().default(null),
    subscriptionExpDate: dateSchema.nullable().default(null),
    subscriptionType: z
      .enum(["monthly", "yearly", "custom"])
      .nullable()
      .default(null),
    storeType: z.string().nullable().default(null),
    productId: z.string().nullable().default(null),
    unsubscribedAt: dateSchema.nullable().default(null),
    entitlement: z.enum(["free", "basic", "pro", "plus"]).default("free"),
  })
  .default({});

const userObject = {
  ...baseZodObj,
  userInfo: z.object({
    name: z.string(),
    email: z.string(),
    createdAt: dateSchema,
    deletedAt: dateSchema.nullable().default(null),
    tokensValidAfterTime: dateSchema.nullable().default(null),
    storageUsed: z.number().default(0),
    resetPasscode: z.boolean().default(false),
    pseudoName: z.string().nullable(),
    isUsingCustomKey: z.boolean().default(false),
  }),
  subscriptionInfo: ZSubscriptionInfo,
  securitySettings: z
    .object({
      passcode: z.string().nullable().default(null),
      isPasscodeEnabled: z.boolean().default(false),
      isBiometricEnabled: z.boolean().default(false),
    })
    .default({}),
  dataBackupInfo: z
    .object({
      export: dataBackupStatsSchema.default({}),
      import: dataBackupStatsSchema.default({}),
    })
    .default({}),
  muid: z.string().nullable().default(null),
};

const ZViewSettings = z.object({
  ...baseZodObj,
  todaySettings: z
    .object({
      todayTabSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showHabitResponse: z.boolean().default(false),
        showMood: z.boolean().default(false),
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showChecklist: z.boolean().default(false),
        showImage: z.boolean().default(false),
        showCalendarName: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        showTags: z.boolean().default(false),
        showInvalidEntries: z.boolean().default(false),
      }),
      overdueSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showChecklist: z.boolean().default(false),
        showImage: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        showTags: z.boolean().default(false),
      }),
      unscheduleSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showCompletedAt: z.boolean().default(true),
        showChecklist: z.boolean().default(false),
        showImage: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        showTags: z.boolean().default(false),
      }),
    })
    .default({
      todayTabSettings: {},
      overdueSettings: {},
      unscheduleSettings: {},
    }),
  notificationSettings: z
    .object({
      isDailyAgendaMobileNotificationEnabled: z.boolean().default(false),
      isDailyAgendaEmailNotificationEnabled: z.boolean().default(false),
      isOverdueEmailNotificationEnabled: z.boolean().default(false),
      emailNotificationTimezone: z.string().nullable().default(null),
      emailNotificationTime: meDateTimeObj.nullable().default(null),
      pinReminder: z.boolean().default(false),
      remindMeType: z.enum(["sameDay", "previousDay"]).default("sameDay"),
      mobileSnoozeType: z
        .enum(["five", "ten", "fifteen", "twenty", "twentyFive", "thirty"])
        .default("five"),
      mobileSoundType: z
        .enum([
          "none",
          "mevolve1",
          "mevolve2",
          "mevolve3",
          "mevolve4",
          "mevolve5",
        ])
        .default("mevolve1"),
      muteAllDevice: meDateTimeObj.nullable().default(null),
      muteAllDeviceStartAt: meDateTimeObj.nullable().default(null),
      mobileDailyAgendaNotificationTime: meDateTimeObj.nullable().default(null),
      muteAllCustomPresetSelected: z.boolean().default(false),
      isDailyAgendaTmzDependent: z.boolean().default(false),
      isMuteAllDeviceTmzDependent: z.boolean().default(true),
    })
    .default({}),
  futureSettings: z
    .object({
      futureTodoSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showChecklist: z.boolean().default(false),
        showImage: z.boolean().default(false),
        showTags: z.boolean().default(false),
        showEmptyDays: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBy: z.enum(["date"]).default("date"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
      }),
      futureHabitSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showHabitResponse: z.boolean().default(false),
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showImage: z.boolean().default(false),
        showDate: z.boolean().default(false),
        showTags: z.boolean().default(false),
        showEmptyDays: z.boolean().default(false),
        showInvalidEntries: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBy: z.enum(["date"]).default("date"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
      }),
      futureJournalSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showMood: z.boolean().default(false),
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showImage: z.boolean().default(false),
        showDate: z.boolean().default(false),
        showTags: z.boolean().default(false),
        showEmptyDays: z.boolean().default(false),
        showInvalidEntries: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBy: z.enum(["date"]).default("date"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
      }),
      futureMoneyTrackerSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showSetupTitle: z.boolean().default(true),
        showImage: z.boolean().default(false),
        showDate: z.boolean().default(false),
        showHashtag: z.boolean().default(false),
        showEmptyDays: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBySettings: z
          .enum(["date", "hashtag", "transactionType", "setup"])
          .default("date"),
        collapsedView: z.boolean().default(false),
        showNetAmount: z.boolean().default(false),
      }),
      futureCalendarEventSettings: z.object({
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showCalendarName: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBy: z.enum(["date"]).default("date"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
      }),
    })
    .default({
      futureTodoSettings: {},
      futureHabitSettings: {},
      futureJournalSettings: {},
      futureMoneyTrackerSettings: {},
      futureCalendarEventSettings: {},
    }),
  featureSettings: z
    .object({
      showTodoFeature: z.boolean().default(true),
      showNoteFeature: z.boolean().default(true),
      showListFeature: z.boolean().default(true),
      showTimebox: z.boolean().default(true),
      viewType: z.enum(["chronological", "category"]).default("chronological"),
      showFeatureLabels: z.boolean().default(false),
      userGoal: z.string().nullable().default(null),
      showUserGoal: z.boolean().default(false),
      hideCompletedItems: z.boolean().default(false),
      showCalendarView: z.boolean().default(false),
      hiddenHabits: z.record(z.string(), dateSchema).default({}),
      hiddenJournals: z.record(z.string(), dateSchema).default({}),
      hiddenMoneytrackers: z.record(z.string(), dateSchema).default({}),
      hiddenCalendars: z.record(z.string(), dateSchema).default({}),
    })
    .default({}),
  noteSettings: z
    .object({
      myNoteSettings: z
        .object({
          noteDescriptionType: z
            .enum(["none", "short", "full"])
            .default("none"),
          noteViewTime: z.boolean().default(false),
          noteViewDate: z.boolean().default(true),
          noteViewImage: z.boolean().default(false),
          noteViewMood: z.boolean().default(false),
          noteViewTags: z.boolean().default(false),
          notesGroupBy: z
            .enum(["none", "date", "hashtag", "mood"])
            .default("none"),
          viewType: z.enum(["compact", "custom"]).default("compact"),
          showFilterRow: z.boolean().default(false),
          collapsedView: z.boolean().default(false),
          showMemberCount: z.boolean().default(false),
          showCounts: z.boolean().default(false),
        })
        .default({}),
      sharedNoteSettings: z
        .object({
          noteDescriptionType: z
            .enum(["none", "short", "full"])
            .default("none"),
          noteViewTime: z.boolean().default(false),
          noteViewDate: z.boolean().default(true),
          noteViewImage: z.boolean().default(false),
          noteViewMood: z.boolean().default(false),
          notesGroupBy: z
            .enum(["none", "date", "hashtag", "mood"])
            .default("none"),
          viewType: z.enum(["compact", "custom"]).default("compact"),
          showFilterRow: z.boolean().default(false),
          collapsedView: z.boolean().default(false),
          showCounts: z.boolean().default(false),
          showAccess: z.boolean().default(false),
        })
        .default({}),
      savedNoteSettings: z
        .object({
          noteDescriptionType: z
            .enum(["none", "short", "full"])
            .default("none"),
          noteViewTime: z.boolean().default(false),
          noteViewDate: z.boolean().default(true),
          noteViewImage: z.boolean().default(false),
          noteViewMood: z.boolean().default(false),
          notesGroupBy: z
            .enum(["none", "date", "hashtag", "mood"])
            .default("none"),
          viewType: z.enum(["compact", "custom"]).default("compact"),
          showFilterRow: z.boolean().default(false),
          collapsedView: z.boolean().default(false),
          showCounts: z.boolean().default(false),
        })
        .default({}),
      ordering: z.record(z.string(), z.number()).default({}),
    })
    .default({
      myNoteSettings: {},
      sharedNoteSettings: {},
      savedNoteSettings: {},
      ordering: {},
    }),
  insightSettings: z
    .object({
      insightHabitSettings: z.object({
        resultInPercentage: z.boolean().default(false),
      }),
      insightJournalSettings: z.object({
        resultInPercentage: z.boolean().default(false),
      }),
      insightTodoSettings: z.object({
        resultInPercentage: z.boolean().default(false),
      }),
    })
    .default({
      insightHabitSettings: {},
      insightJournalSettings: {},
      insightTodoSettings: {},
    }),
  pastSettings: z
    .object({
      pastHabitSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showHabitResponse: z.boolean().default(false),
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showImage: z.boolean().default(false),
        showDate: z.boolean().default(false),
        showTags: z.boolean().default(false),
        showEmptyDays: z.boolean().default(false),
        showInvalidEntries: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBy: z.enum(["date", "hashtag"]).default("date"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
      }),
      pastTodoSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showChecklist: z.boolean().default(false),
        showImage: z.boolean().default(false),
        showDate: z.boolean().default(false),
        showTags: z.boolean().default(false),
        showEmptyDays: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBy: z.enum(["date", "status", "hashtag"]).default("date"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
      }),
      pastJournalSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showMood: z.boolean().default(false),
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showImage: z.boolean().default(false),
        showDate: z.boolean().default(false),
        showTags: z.boolean().default(false),
        showEmptyDays: z.boolean().default(false),
        showInvalidEntries: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBy: z.enum(["date", "hashtag", "mood"]).default("date"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
      }),
      pastMoneyTrackerSettings: z.object({
        descriptionType: z.enum(["none", "short", "full"]).default("none"),
        showSetupTitle: z.boolean().default(true),
        showImage: z.boolean().default(false),
        showDate: z.boolean().default(false),
        showHashtag: z.boolean().default(false),
        showEmptyDays: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBySettings: z
          .enum(["date", "hashtag", "transactionType", "setup"])
          .default("date"),
        collapsedView: z.boolean().default(false),
        showNetAmount: z.boolean().default(false),
      }),
      pastCalendarEventSettings: z.object({
        showTime: z.boolean().default(true),
        showReminder: z.boolean().default(false),
        showDuration: z.boolean().default(false),
        showRepeat: z.boolean().default(false),
        showCalendarName: z.boolean().default(false),
        viewSettingType: z.enum(["compact", "custom"]).default("compact"),
        groupBy: z.enum(["date"]).default("date"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
      }),
    })
    .default({
      pastHabitSettings: {},
      pastTodoSettings: {},
      pastJournalSettings: {},
      pastMoneyTrackerSettings: {},
      pastCalendarEventSettings: {},
    }),
  listSettings: z
    .object({
      myListSettings: z.object({
        showDescription: z.boolean().default(false),
        itemCount: z.boolean().default(true),
        showCollaboratorsCount: z.boolean().default(false),
        showInvitedUsersCount: z.boolean().default(false),
        showAwaitingUserCount: z.boolean().default(false),
        showHashtags: z.boolean().default(false),
        groupByType: z.enum(["none", "hashtag"]).default("none"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
        viewSettingType: z
          .enum(["compact", "custom"])
          .default("compact")
          .optional(),
      }),
      sharedListSettings: z.object({
        showDescription: z.boolean().default(false),
        itemCount: z.boolean().default(true),
        showAccess: z.boolean().default(false),
        showHashtags: z.boolean().default(false),
        groupByType: z.enum(["none", "hashtag"]).default("none"),
        collapsedView: z.boolean().default(false),
        showCounts: z.boolean().default(false),
        viewSettingType: z
          .enum(["compact", "custom"])
          .default("compact")
          .optional(),
      }),
      myListItemSheetSettings: z.object({
        viewType: z.enum(["def", "custom", "compact"]).default("compact"),
        showLastUpdatedBy: z.boolean().default(false),
        showLastUpdatedAt: z.boolean().default(false),
        showDescription: z.boolean().default(false),
      }),
      sharedListItemSettings: z.object({
        viewType: z.enum(["def", "custom", "compact"]).default("compact"),
        showLastUpdatedBy: z.boolean().default(false),
        showLastUpdatedAt: z.boolean().default(false),
        showDescription: z.boolean().default(false),
      }),
      publicListSettings: z.object({
        showDescription: z.boolean().default(false),
        itemCount: z.boolean().default(true),
        viewSettingType: z
          .enum(["compact", "custom"])
          .default("compact")
          .optional(),
      }),
      publicListItemSheetSettings: z.object({
        viewType: z.enum(["def", "custom", "compact"]).default("compact"),
        showLastUpdatedBy: z.boolean().default(false),
        showLastUpdatedAt: z.boolean().default(false),
        showDescription: z.boolean().default(false),
      }),
      ordering: z.record(z.string(), z.number()).default({}),
    })
    .default({
      myListSettings: {},
      sharedListSettings: {},
      myListItemSheetSettings: {},
      sharedListItemSettings: {},
      publicListSettings: {},
      publicListItemSheetSettings: {},
      ordering: {},
    }),
  appSettings: z
    .object({
      themeColor: z.string().default("theme1"),
      appTheme: zAppTheme.default("dark"),
      isVibrationEnabled: z.boolean().default(false),
      isSpeechToTextEnabled: z.boolean().default(true),
      language: zAppLanguage.default("english"),
      supportLanguage: zAppLanguage.nullable().default("english"),
    })
    .default({}),
  moneyTrackerSettings: z
    .object({
      config: z
        .object({
          title: z.string().nullable().default(null),
          currency: z.string().default("$"),
          hasSetCurrency: z.boolean().default(false),
        })
        .default({}),
      listViewSettings: z
        .object({
          showDate: z.boolean().default(true),
          showHashtag: z.boolean().default(false),
          descriptionType: z.enum(["none", "short", "full"]).default("none"),
          showImage: z.boolean().default(false),
          groupBySettings: z
            .enum(["date", "hashtag", "transactionType", "setup"])
            .default("date"),
          viewSettingType: z.enum(["compact", "custom"]).default("compact"),
          showEmptyDays: z.boolean().default(false),
          collapsedView: z.boolean().default(false),
          showNetAmount: z.boolean().default(false),
        })
        .default({}),
    })
    .default({}),
});

const ZUserResources = z.object({
  ...baseZodObj,
  favorites: z
    .object({
      id: z.string(),
      widgetType: z.string(),
      feature: z.string(),
    })
    .array()
    .nullable()
    .default([]),
  tags: z
    .object({
      id: z.string(),
      tag: z.string(),
    })
    .array()
    .default([]),
  shared: z
    .object({
      sharedLists: z.array(z.string()).default([]),
      sharedNotes: z.array(z.string()).default([]),
      sharedTodos: z.array(z.string()).default([]),
      sharedMoneyTrackerSetups: z.array(z.string()).default([]),
      sharedHabitSetups: z.array(z.string()).default([]),
      sharedJournalSetups: z.array(z.string()).default([]),
    })
    .default({
      sharedLists: [],
      sharedNotes: [],
      sharedTodos: [],
      sharedMoneyTrackerSetups: [],
      sharedHabitSetups: [],
      sharedJournalSetups: [],
    }),
});

const memberObject = z
  .object({
    memberHashedEmails: z.string().array().default([]),
    membersConfig: z.record(
      z.object({
        hashedEmail: z.string(),
        role: z.enum(["viewer", "editor", "owner"]),
        status: z.enum(["active", "blocked", "left", "removed"]),
        eEmail: z.string(),
        dek: z.string(),
      })
    ),
  })
  .default({ memberHashedEmails: [], membersConfig: {} });

const metadataObject = {
  ...userObject,
  superSubscription: z.enum(["basic", "pro", "plus"]).nullable().default(null),
  featureUsageInfo: z
    .object({
      todosCount: z.number().default(0),
      todosUpdatedAt: dateSchema.nullable().default(null),
      notesCount: z.number().default(0),
      notesUpdatedAt: dateSchema.nullable().default(null),
      habitSetupsCount: z.number().default(0),
      habitSetupsUpdatedAt: dateSchema.nullable().default(null),
      habitActionsCount: z.number().default(0),
      habitActionsUpdatedAt: dateSchema.nullable().default(null),
      journalSetupsCount: z.number().default(0),
      journalSetupsUpdatedAt: dateSchema.nullable().default(null),
      journalActionsCount: z.number().default(0),
      journalActionsUpdatedAt: dateSchema.nullable().default(null),
      listsCount: z.number().default(0),
      listsUpdatedAt: dateSchema.nullable().default(null),
    })
    .default({}),
  chatStatus: z.string().nullable().default(null),
  reportStatus: z.string().nullable().default(null),
  userSegments: z.array(z.string()).default([]),
  userDeletedStatus: z
    .enum(["deleted", "remove_u", "remove_a"])
    .nullable()
    .default(null),
  isFeedbackGiven: z.boolean().optional().nullable().default(false),
  isStoreFeedbackGiven: z.boolean().optional().nullable().default(false),
};

const ZUser = z.object(userObject);
const ZUserMetadata = z.object(metadataObject);

const ZCalendarIntegrations = z.object(calendarIntegrationObject);

const ZUserKeys = z.object({
  id: z.string(),
  uid: z.string().nullable(),
  email: z.string(),
  emailHash: z.string(),
  privateKeyPasswordType: z.enum(["KMS", "CUSTOM"]),
  publicKey: z.string(),
  encPrivateKey: z.string(),
  cloudUpdatedAt: dateSchema.nullable(),
  shared: z
    .object({
      sharedLists: z.array(z.string()).default([]),
      sharedNotes: z.array(z.string()).default([]),
      sharedTodos: z.array(z.string()).default([]),
      sharedMoneyTrackerSetups: z.array(z.string()).default([]),
      sharedHabitSetups: z.array(z.string()).default([]),
      sharedJournalSetups: z.array(z.string()).default([]),
    })
    .default({
      sharedLists: [],
      sharedNotes: [],
      sharedTodos: [],
      sharedMoneyTrackerSetups: [],
      sharedHabitSetups: [],
      sharedJournalSetups: [],
    })
    .optional(),
});

const ZSpecialActivities = z.object({
  id: z.string(),
  docCollection: ZDocCollectionName,
  uid: z.string(),
  activityForEmailHash: z.string(),
  entityDocVer: z.number(),
  entityDocCollection: z.union([ZDocCollectionName, z.string()]),
  entityIds: z.array(z.string()).default([]),
  actionType: z.enum(["remove"]),
  createdAt: dateSchema.nullable(),
});

const ZTodo = z.object({
  ...baseZodObj,
  createdAt: dateSchema,
  startAt: meDateTimeObj.nullable(),
  isStartTimeSet: z.boolean(),
  isTmzAffected: z.boolean().default(true),
  endAt: meDateTimeObj.nullable(),
  description: z.string().nullable(),
  id: z.string(),
  reminderAt: z.array(z.number()),
  repeat: z.array(z.string()),
  tags: z.array(z.string()),
  title: z.string(),
  uid: z.string(),
  attachments: atachmentDetailZodObject.array(),
  actions: z.array(
    z.object({
      dueAt: z.string().nullable(),
      completedAt: dateSchema.nullable(),
      isSkipped: z.boolean().default(false),
    })
  ),
  duration: z.number().default(15),
  checkLists: z.array(
    z.object({
      id: z.string(),
      checklistText: z.string(),
      isCompleted: z.boolean(),
    })
  ),
  members: memberObject,
  ownerEmail: z.string(),
  ownerName: z.string(),
  lastUpdatedBy: z.string().nullable().default(null).optional(),
});

const ZHabitSetup = z.object({
  ...baseZodObj,
  title: z.string(),
  createdAt: dateSchema,
  uid: z.string(),
  startAt: meDateTimeObj,
  endAt: meDateTimeObj.nullable(),
  tags: z.array(z.string()),
  repeat: z.array(z.string()),
  isStartTimeSet: z.boolean(),
  isTmzAffected: z.boolean().default(false),
  reminderAt: z.array(z.number()),
  numericGoal: z.number().nullable(),
  timerGoal: z.string().nullable(),
  alarmSoundType: z.string(),
  isVibrationEnabled: z.boolean(),
  alarmSoundVolume: z.number().default(100),
  numericUnit: z.string().nullable(),
  habitType: z.string(),
  habitOptions: z
    .object({
      id: z.number(),
      value: z.string(),
    })
    .array()
    .default([]),
  durationRepeatCount: z.number().default(1),
  durationRepeatType: z.number().default(0),
  timerStopType: z.string().default("onRoundEnd"),
  ownerEmail: z.string(),
  ownerName: z.string(),
  members: memberObject,
  duration: z.number().default(15),
});

const ZHabitAction = z.object({
  ...baseZodObj,
  description: z.string(),
  setupId: z.string(),
  dueAt: meDateTimeObj,
  tags: z.string().array(),
  attachments: z.array(atachmentDetailZodObject),
  singleAnswer: z.number().nullable(),
  booleanAnswer: z.boolean().nullable(),
  numericAnswer: z
    .object({
      id: z.string(),
      value: z.number(),
      timestamp: dateSchema.nullable(),
    })
    .array()
    .nullable(),
  timerAnswer: z
    .object({
      id: z.string(),
      startTimestamp: dateSchema.nullable(),
      endTimestamp: dateSchema.nullable(),
      durationRepeatCount: z.number().default(1),
    })
    .array()
    .nullable(),
  multipleAnswer: z.array(z.number()).nullable(),
  ownerEmail: z.string(),
  ownerName: z.string(),
  members: memberObject,
  lastUpdatedBy: z.string().nullable().default(null).optional(),
});

const ZJournalSetup = z.object({
  ...baseZodObj,
  title: z.string(),
  createdAt: dateSchema,
  uid: z.string(),
  startAt: meDateTimeObj,
  endAt: meDateTimeObj.nullable(),
  tags: z.array(z.string()),
  repeat: z.array(z.string()),
  isStartTimeSet: z.boolean(),
  isTmzAffected: z.boolean().default(false),
  reminderAt: z.array(z.number()),
  ownerEmail: z.string(),
  ownerName: z.string(),
  members: memberObject,
  duration: z.number().default(15),
});

const ZJournalAction = z.object({
  ...baseZodObj,
  uid: z.string(),
  description: z.string(),
  setupId: z.string(),
  emotion: z.number().nullable(),
  attachments: z.array(atachmentDetailZodObject),
  tags: z.array(z.string()),
  dueAt: meDateTimeObj,
  completedAt: dateSchema.nullable().default(null),
  ownerEmail: z.string(),
  ownerName: z.string(),
  members: memberObject,
  lastUpdatedBy: z.string().nullable().default(null).optional(),
});

const ZNote = z.object({
  ...baseZodObj,
  title: z.string(),
  createdAt: dateSchema,
  tags: z.array(z.string()),
  uid: z.string(),
  description: z.string(),
  emotion: z.number().nullable(),
  attachments: atachmentDetailZodObject.array(),
  noteUpdatedAt: dateSchema.nullable().default(null),
  members: memberObject,
  ownerEmail: z.string(),
  ownerName: z.string(),
  inviteLink: z.string().nullable().default(null),
  snapshotOfInviteLink: z.string().nullable().default(null),
  isPublic: z.boolean().default(false),
  publicId: z.string().nullable().default(null),
  isFav: z.boolean().default(false),
  lastUpdatedBy: z.string().nullable().default(null),
});

const ZLists = z.object({
  ...baseZodObj,
  title: z.string(),
  isPublic: z.boolean().default(false),
  publicId: z.string().nullable().default(null),
  createdAt: dateSchema,
  listItems: z.record(
    z.string(),
    z.object({
      id: z.string(),
      item: z.string(),
      done: z.boolean(),
      lastUpdatedBy: z.string(),
      lastUpdatedAt: dateSchema,
      description: z.string().nullable().default(null),
      customText: z.string().nullable().default(null),
      position: z.number().nullable().default(null),
      deletedAt: dateSchema.nullable().default(null),
    })
  ),
  uid: z.string(),
  description: z.string().nullable(),
  collaboratorLimit: z.number().default(5),
  inviteLink: z.string().nullable().default(null),
  snapshotOfInviteLink: z.string().nullable().default(null),
  hashtags: z.string().array().default([]),
  members: memberObject,
  ownerEmail: z.string(),
  ownerName: z.string(),
  isFav: z.boolean().default(false),
  addOnType: z.enum(["checkbox", "customText", "none"]).default("checkbox"),
});

const ZChatUser = z.object({
  ...baseZodObj,
  uid: z.string(),
  uname: z.string(),
  message: z.string(),
  updatedBy: z.string(),
  status: z.string(),
  prevStatus: z.string().nullable(),
  clarify: z.boolean().nullable(),
  attachmentCount: z.number().nullable(),
  lastSid: z.string().nullable(),
  sname: z.string().nullable(),
});

const ZChatMessage = z.object({
  ...baseZodObj,
  updatedBy: z.string(),
  attachments: z.array(atachmentDetailZodObject).nullable(),
  supportLanguage: z.string().default("english"),
  message: z
    .object({
      en: z.string().default(""),
      fr: z.string().default(""),
      de: z.string().default(""),
      it: z.string().default(""),
      pt: z.string().default(""),
      es: z.string().default(""),
    })
    .default({}),
});

const ZCalendarEventSetup = z.object({
  ...baseZodObj,
  id: z.string(),
  uid: z.string(),
  title: z.string(),
  description: z.string().nullable(),
  createdAt: dateSchema,
  startAt: meDateTimeObj.nullable(),
  isStartTimeSet: z.boolean(),
  isTmzAffected: z.boolean().default(true),
  endAt: meDateTimeObj.nullable(),
  duration: z.number(),
  rawData: z.string().nullable(),
  calendarId: z.string(),
  calendarName: z.string(),
  calendarAccountId: z.string(),
  visibility: z.string().nullable(),
  eventType: z.string().nullable(),
  status: z.string().nullable(),
  conferenceData: z.string().nullable(),
  deletedAt: dateSchema.nullable(),
  permaDeletedAt: dateSchema.nullable().default(null),
  reminderAt: z.array(z.number()),
  repeat: z.array(z.string()),
  type: z.string().nullable().optional(),
  calendarIntegrationId: z.string(),
});

const ZCalendarEventAction = z.object({
  ...baseZodObj,
  id: z.string(),
  uid: z.string(),
  title: z.string(),
  description: z.string().nullable(),
  setupId: z.string(),
  createdAt: dateSchema,
  startAt: meDateTimeObj.nullable(),
  dueAt: meDateTimeObj.nullable(),
  isStartTimeSet: z.boolean(),
  isTmzAffected: z.boolean().default(true),
  endAt: meDateTimeObj.nullable(),
  duration: z.number().default(15),
  rawData: z.string().nullable(),
  calendarId: z.string(),
  calendarIntegrationId: z.string(),
  calendarName: z.string(),
  calendarAccountId: z.string(),
  visibility: z.string().nullable(),
  eventType: z.string().nullable(),
  status: z.string().nullable(),
  conferenceData: z.string().nullable(),
  deletedAt: dateSchema.nullable(),
  permaDeletedAt: dateSchema.nullable().default(null),
  reminderAt: z.array(z.number()),
  repeat: z.array(z.string()),
  type: z.string().nullable().optional(),
});

const ZFeedback = z.object({
  ...baseZodObj,
  uid: z.string(),
  emotion: z.number(),
  feedbackText: z.string().nullable(),
  isStoreFeedbackGiven: z.boolean().nullable(),
  uname: z.string().nullable().optional(),
  attachments: z
    .array(atachmentDetailZodObject)
    .nullable()
    .optional()
    .default([]),
});

const ZInAppNotification = z.object({
  ...baseZodObj,
  type: z.enum([
    "list",
    "note",
    "habit",
    "journal",
    "todo",
    "moneyTracker",
    "afterGracePeriod",
  ]),
  createdAt: dateSchema,
  data: z.record(z.unknown()),
});

const ZIssueReport = z.object({
  ...baseZodObj,
  uid: z.string(),
  uname: z.string(),
  reportedAt: dateSchema,
  updatedBy: z.string(),
  lastSid: z.string().nullable(),
  sname: z.string().nullable(),
  status: z.string(),
  prevStatus: z.string().nullable(),
  type: z.string(),
  description: z.string(),
  attachments: z.array(atachmentDetailZodObject).nullable(),
});

const ZMoneyTrackerTransactions = z.object({
  ...baseZodObj,
  title: z.string(),
  amount: z.number(),
  description: z.string().nullable().default(null),
  tags: z.array(z.string()).default([]),
  attachments: atachmentDetailZodObject.array(),
  transactionDate: meDateTimeObj,
  transactionType: z.enum(["income", "expense"]),
  setupId: z.string(),
  ownerEmail: z.string(),
  ownerName: z.string(),
  members: memberObject,
  lastUpdatedBy: z.string().nullable().default(null).optional(),
});

const ZMoneyTrackerSetups = z.object({
  ...baseZodObj,
  title: z.string(),
  currency: z.string(),
  tags: z.array(z.string()).default([]),
  ownerEmail: z.string(),
  ownerName: z.string(),
  members: memberObject,
});

const ZPublicListMetaData = z.object({
  id: z.string(),
  title: z.string(),
  itemCount: z.number().default(0),
  completedCount: z.number().default(0),
  createdAt: dateSchema,
  updatedAt: dateSchema,
  shortLinkCode: z.string().nullable().optional(),
  deletedAt: dateSchema.nullable().optional(),
  permaDeletedAt: dateSchema.nullable().optional(),
});
const ZPublicNoteMetaData = z.object({
  id: z.string(),
  title: z.string(),
  attachmentCount: z.number().default(0),
  createdAt: dateSchema,
  updatedAt: dateSchema,
  emotion: z.number().nullable().default(null),
  shortLinkCode: z.string().nullable().optional(),
  deletedAt: dateSchema.nullable().optional(),
  permaDeletedAt: dateSchema.nullable().optional(),
});
const ZPublicUsers = z.object({
  ...baseZodObj,
  name: z.string().nullable().default(null),
  mevolveId: z.string().nullable().default(null),
  bio: z.string().nullable().default(null),
  followers: z.number().default(0),
  following: z.number().default(0),
  socialLinks: z.array(z.string()).default([]),
  profileTags: z.array(z.string()).default([]),
  publicListsMetadata: z.array(ZPublicListMetaData).default([]),
  publicNotesMetadata: z.array(ZPublicNoteMetaData).default([]),
  availableTags: z.array(z.string()).default([]),
});

export default {
  ZUser,
  ZUserMetadata,
  ZViewSettings,
  ZUserResources,
  ZUserKeys,
  ZSpecialActivities,
  ZSubscriptionInfo,
  ZTodo,
  ZHabitSetup,
  ZHabitAction,
  ZJournalSetup,
  ZJournalAction,
  ZNote,
  ZLists,
  ZChatUser,
  ZChatMessage,
  ZCalendarEventSetup,
  ZCalendarEventAction,
  ZFeedback,
  ZInAppNotification,
  ZIssueReport,
  ZMoneyTrackerTransactions,
  ZMoneyTrackerSetups,
  ZCalDetailsType,
  ZCalendarIntegrations,
  ZPublicUsers,
  ZPublicNoteMetaData,
  ZPublicListMetaData,
};
