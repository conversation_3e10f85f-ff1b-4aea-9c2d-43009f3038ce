import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import { getEmailByIdFromAuthService } from "../../../../utils/utility_methods";
import { z } from "zod";
import models from "../v104/models";

export class V104 extends BaseMigrationScript {
  // Cache for email lookups
  private emailCache: Map<string, string> = new Map();

  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    return Promise.resolve();
  }

  private async getEmailForUser(uid: string): Promise<string> {
    if (this.emailCache.has(uid)) {
      return this.emailCache.get(uid)!;
    }
    const email = await getEmailByIdFromAuthService(uid);
    this.emailCache.set(uid, email ?? "");
    return email ?? "";
  }

  private createMemberFields(email: string): {
    ownerEmail: string;
    ownerName: string;
    members: {
      memberHashedEmails: string[];
      membersConfig: Record<string, any>;
    };
  } {
    return {
      ownerEmail: email,
      ownerName: "",
      members: {
        memberHashedEmails: [],
        membersConfig: {},
      },
    };
  }

  private async upgradeDocWithMembers(source: IBaseSchema): Promise<any> {
    const email = await this.getEmailForUser(source.uid);

    const item = source as unknown as any;
    return Object.assign(item, this.createMemberFields(email));
  }

  private async upgradeUserDoc(source: IBaseSchema): Promise<any> {
    const user = source as z.infer<typeof models.ZUser>;

    // Initialize objects if they don't exist
    user.public = user.public || {};
    user.shared = user.shared || {};

    // Add new shared arrays
    user.shared = {
      ...user.shared,
      sharedHabitSetups: [],
      sharedJournalSetups: [],
      sharedMoneyTrackerSetups: [],
      sharedTodos: [],
    };

    return user;
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    try {
      source.docVer = this.docVer;

      switch (source.docCollection) {
        case "habitSetups":
        case "habitActions":
        case "journalSetups":
        case "journalActions":
        case "todos":
        case "moneyTrackerSetups":
        case "moneyTrackerTransactions":
          return this.upgradeDocWithMembers(source);

        case "users":
          return this.upgradeUserDoc(source);

        default:
          return source;
      }
    } catch (error) {
      console.error(
        `Error upgrading schema for ${source.docCollection}, id: ${source.id}`,
        error
      );
      throw error;
    }
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Clear cache
    this.emailCache.clear();
    return [];
  }
}
