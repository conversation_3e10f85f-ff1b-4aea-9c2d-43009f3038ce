import { Timestamp } from "firebase-admin/firestore";
import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import * as admin from "firebase-admin";
import {
  encryptDocKey,
  encryptTextData,
  getNewSecret,
  getUserKeyDocById,
} from "../../../../utils/encryption/encryption";

export class V96 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    if (_isUpgrading) {
      try {
        const userId = _helperData.get("userId");
        if (!userId) {
          throw new Error("userId not found in helperData");
        }
        const db = admin.firestore();
        const setupId =
          userId.substring(0, 8) +
          "-" +
          userId.substring(8, 12) +
          "-" +
          userId.substring(12, 16) +
          "-" +
          userId.substring(16);
        const userPublicKey = (await getUserKeyDocById(userId))?.publicKey;
        const secret = getNewSecret();
        const encData = {
          dek: await encryptDocKey(secret, userId, userPublicKey),
          encFields: ["title"],
        };
        await db
          .collection("moneyTrackerSetups")
          .doc(setupId)
          .set({
            id: setupId,
            docVer: this.docVer,
            docCollection: "moneyTrackerSetups",
            uid: userId,
            localUpdatedAt: Timestamp.now(),
            cloudUpdatedAt: Timestamp.now(),
            deletedAt: null,
            permaDeletedAt: null,
            encData: encData,
            title: encryptTextData("Daily expense", secret),
            currency: _helperData.get("currency") ?? "",
            tags: [],
          });

        return Promise.resolve();
      } catch (error) {
        console.error("Error in preMigrationSetup:", error);
        throw error;
      }
    } else {
      try {
        const userId = _helperData.get("userId");
        if (!userId) {
          throw new Error("userId not found in helperData");
        }
        const db = admin.firestore();
        const setupId =
          userId.substring(0, 8) +
          "-" +
          userId.substring(8, 12) +
          "-" +
          userId.substring(12, 16) +
          "-" +
          userId.substring(16);
        await db.collection("moneyTrackerSetups").doc(setupId).delete();
      } catch (error) {
        console.error("Error in preMigrationSetup:", error);
        throw error;
      }
    }
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    if (source.docCollection == "moneyTrackerTransactions") {
      const setupId =
        source.uid.substring(0, 8) +
        "-" +
        source.uid.substring(8, 12) +
        "-" +
        source.uid.substring(12, 16) +
        "-" +
        source.uid.substring(16);
      return {
        ...source,
        setupId: setupId,
      };
    }
    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
