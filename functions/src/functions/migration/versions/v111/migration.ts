import { BaseMigrationScript } from "../../helper/base_migration_model";
import { IBaseSchema } from "../base/base_schema";
import { z } from "zod";
import models from "./models";

export class V111 extends BaseMigrationScript {
  async preMigrationSetup(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<void> {
    // do nothing
    return Promise.resolve();
  }

  async upgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer;
    // Added new viewSettingType field in ViewSettings
    if (source.docCollection == "users") {
      type IUser = z.infer<typeof models.ZUser>;
      const user = source as unknown as IUser;
      // Add new fields
      user.listSettings.publicListSettings = {
        showDescription: false,
        itemCount: false,
      };
      user.listSettings.publicListItemSheetSettings = {
        viewType: "def",
        showLastUpdatedBy: false,
        showLastUpdatedAt: false,
        showDescription: true,
      };
    }
    return source;
  }

  async downgradeSchema(source: IBaseSchema, _helperData: Map<string, string>) {
    source.docVer = this.docVer - 1;
    // do nothing
    return source;
  }

  async postMigration(
    _isUpgrading: boolean,
    _helperData: Map<string, string>
  ): Promise<IBaseSchema[]> {
    // Return new documents that were inserted in firestore
    // to include in next migration.
    return [];
  }
}
