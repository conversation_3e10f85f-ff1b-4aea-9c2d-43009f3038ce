import { getMeEnvironment } from "../../me_config";
import * as functions from "firebase-functions/v2";

export const appleSignIn = functions.https.onRequest(
  // Check Function: Used for apple signin.
  { enforceAppCheck: false },
  (request, response) => {
    const redirect = getAppleSignInRedirectUrl(request.body);
    functions.logger.info(`Redirecting to ${redirect}`);
    response.redirect(307, redirect);
  }
);

function getAppleSignInRedirectUrl(body: string): string {
  functions.logger.info("Signin in with apple");
  const url = `intent://callback?${new URLSearchParams(body).toString()}#Intent;package=app.mevolve.daily.mobile.${getMeEnvironment()};scheme=signinwithapple;end`;
  console.log(url);
  return url;
}
