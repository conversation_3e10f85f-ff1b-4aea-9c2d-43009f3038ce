import * as admin from "firebase-admin";
import { FieldValue, Timestamp } from "firebase-admin/firestore";
import * as crypto from "crypto";
import { sendSingleEmailViaSes } from "../../utils/emails/aws_ses_email";
import { MevolveEmails } from "../../utils/emails/email_helper";
import { getMeEnvironment } from "../../me_config";

type AuthRefDetails = {
  otp: string;
  timestamp: Timestamp | FieldValue;
  email: string;
  retries: number;
  expiryTime: Timestamp;
};

// Constants
const OTP_EXPIRY_MS = 180000; // Time in milliseconds after which OTP expires (3 minutes)
const TEST_OTP = "0000";
const MAX_OTP_RETRIES = 3;
const OTP_LENGTH = 4;

// Check if email belongs to a test account
function isTestEmail(email: string): boolean {
  const testEmailList = process.env.QA_TESTER_EMAILS;
  const isTest =
    email.endsWith("@mevolve.app") ||
    email.endsWith("@realtime-innovations.com") ||
    (testEmailList?.toLowerCase().split(",").includes(email.toLowerCase()) ??
      false);
  return isTest;
}

export async function emailSendOtp(
  email: string,
  language: string | undefined
) {
  // Validate email format
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  if (!emailRegex.test(email)) {
    throw new Error("Invalid email");
  }

  // Generate OTP - random digits
  const characters = "**********";
  let otp = "";
  for (let i = 0; i < OTP_LENGTH; i++) {
    otp += characters[Math.floor(Math.random() * characters.length)];
  }

  // Use fixed OTP for test accounts
  if (isTestEmail(email)) {
    console.log("Test account, setting otp to test otp: '0000'");
    otp = TEST_OTP;
  }

  // Create authentication data
  const now = Date.now();
  const db = admin.firestore();
  const collectionRef = db.collection("otpAuth");
  const authData: AuthRefDetails = {
    otp: hashData(otp),
    timestamp: Timestamp.fromMillis(now),
    email: email,
    retries: 0,
    expiryTime: Timestamp.fromMillis(now + OTP_EXPIRY_MS),
  };

  // Log OTP in non-production environments
  const env = getMeEnvironment();
  if (env === "dev" || env === "qa" || env === "staging") {
    console.log("OTP for email:", email, "is:", otp);
  }

  // Save OTP data to Firestore
  const docRef = await collectionRef.add(authData);

  // Send OTP via email
  console.log("Sending OTP email. Language:", language);
  await sendSingleEmailViaSes({
    templateName: MevolveEmails.loginAndSignupVerification,
    recipient: email,
    templateData: { otp },
    language,
  });

  return { refId: docRef.id };
}

export async function verifyAuthOtp(refId: string, otp: string) {
  const db = admin.firestore();
  const docRef = db.collection("otpAuth").doc(refId);
  const doc = await docRef.get();

  const response = {
    error: undefined as
      | "expired"
      | "wrong"
      | "exceededRetry"
      | "noUserFound"
      | undefined,
    isNewUser: undefined as boolean | undefined,
    token: undefined as string | undefined,
  };

  // Check if document exists
  if (!doc.exists) {
    response.error = "expired";
    return response;
  }

  const authData = doc.data() as AuthRefDetails;

  const now = Date.now();
  const timestampMillis = (authData.timestamp as Timestamp).toMillis();
  const isExpired = now - timestampMillis > OTP_EXPIRY_MS;

  if (isExpired) {
    response.error = "expired";
    await docRef.delete();
    return response;
  }

  // Only compare hashes directly (since client already hashed)
  if (authData.otp === otp) {
    console.log("OTP matched for email.");

    let userRecord = null;

    try {
      userRecord = await admin.auth().getUserByEmail(authData.email);
    } catch (error) {
      console.log("No user found for this email, create a new user");
    }

    // Handle user creation if needed
    if (userRecord === null) {
      userRecord = await admin.auth().createUser({
        email: authData.email,
        emailVerified: true,
      });
      response.isNewUser = true;
    } else {
      response.isNewUser = false;
    }

    // Create authentication token
    if (userRecord !== null) {
      response.token = await admin.auth().createCustomToken(userRecord.uid);
    }

    return response;
  } else {
    // Handle incorrect OTP
    authData.retries += 1;

    if (authData.retries >= MAX_OTP_RETRIES) {
      response.error = "exceededRetry";
      await docRef.delete();
    } else {
      response.error = "wrong";
      await docRef.update(authData);
    }

    return response;
  }
}

function hashData(data: string, algorithm = "sha256"): string {
  const hash = crypto.createHash(algorithm);
  hash.update(data);
  return hash.digest("hex");
}
