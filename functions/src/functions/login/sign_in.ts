import * as functions from "firebase-functions/v2";
import { emailSendOtp, verifyAuthOtp } from "./email_otp_auth";
import * as admin from "firebase-admin";
import { Timestamp } from "firebase-admin/firestore";
import { MeLogger } from "../../utils/logger/models/me_logger";

export const sendOtp = functions.https.onCall(async (request) => {
  const sessionId = request.data.sessionId;
  const language = request.data.language;
  const logger = new MeLogger(request.auth?.uid, sessionId);
  logger.log("Sending OTP");
  return emailSendOtp(request.data.email, language);
});

export const verifyOtp = functions.https.onCall(async (request) => {
  const sessionId = request.data.sessionId;
  const logger = new MeLogger(request.auth?.uid, sessionId);
  logger.log("Verifying OTP");
  return verifyAuthOtp(request.data.refId, request.data.otp);
});

export const logoutAll = functions.https.onCall(async (request) => {
  const { uid } = request.data;
  return logoutUserFromAllDevices(uid);
});

export async function logoutUserFromAllDevices(uid: string): Promise<{ status: string; timeStamp?: string, error?: unknown }> {
  try {
    await admin.auth().revokeRefreshTokens(uid);
    const userRecord = await admin.auth().getUser(uid);
    const timestamp = new Date(userRecord.tokensValidAfterTime!).getTime();
    // update user doc to force refresh of token
    const usersCollection = admin.firestore().collection("usersMetadata");
    const userDoc = await usersCollection.doc(uid).get();
    const userInfo = userDoc.data()?.userInfo;
    userInfo.tokensValidAfterTime = Timestamp.fromMillis(timestamp);
    // userInfo.
    await userDoc.ref.update({
      userInfo: userInfo,
      cloudUpdatedAt: Timestamp.fromMillis(Date.now()),
      localUpdatedAt: Timestamp.fromMillis(Date.now()),
    });
    return { status: "success", timeStamp: timestamp.toString() };
  } catch (error) {
    console.error("Error revoking tokens:", error);
    return { status: "error", error: error };
  }
}
