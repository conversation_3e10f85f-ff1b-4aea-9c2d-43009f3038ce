import * as functions from "firebase-functions/v2";

export const filterUserKeysData = functions.https.onRequest(
  { enforceAppCheck: false },
  async (request, response) => {
    try {
      const originalData = request.body.data;
      
      if (!originalData) {
        response.status(400).json({ error: "No data provided" });
        return;
      }

      const filteredData = {
        uid: originalData.uid || null,
        email: originalData.email || null,
      };

      response.json({ data: filteredData });
    } catch (error) {
      console.error("Error in filterUserKeysData transform:", error);
      response.status(500).json({ error: "Transform function failed" });
    }
  }
);