import * as functions from "firebase-functions/v2";
import * as admin from "firebase-admin";
import { ZodError } from "zod";
import { INTERNAL_SERVER_ERROR, INVALID_ARGUMENT } from "../../utils/constants";
import { DbRepository } from "./db.repository";
import {
  IBaseSchema,
  ISyncRequestModel,
  ZSyncRequestModel,
} from "../migration/versions/base/base_schema";
import { deleteDocs } from "../user/lifecycle/components/trash";
import { DataMigrationService } from "../migration/migration_service";
import {
  getMeParsedData,
  IMoneyTrackerTransactions,
} from "../migration/versions/base/model_mappings";
import { MeLogger } from "../../utils/logger/models/me_logger";
import * as jsonpatch from "fast-json-patch";
import { createSpecialActivitiesForSharedMembersForTaskRemove } from "../user/user_api/get_user";
import { DocumentData } from "firebase-admin/firestore";

// import pj = require("../../../package.json");

// export const currentDbVersion = Number(pj.version.split(".")[0]);

export const save = functions.https.onCall(
  {
    memory: "512MiB",
  },
  async (request) => {
    const sessionId = request.data.sessionId;
    const uid = request.auth?.uid;
    if (!uid) {
      throw new functions.https.HttpsError(
        INVALID_ARGUMENT,
        "Unauthorized access. Please login to continue."
      );
    }
    const logger = new MeLogger(uid, sessionId);
    logger.log("Syncing data for - " + uid);
    const finalResult: {
      id: string;
      success: boolean;
      error?: string | undefined;
    }[] = [];
    try {
      const docsToDelete: IBaseSchema[] = [];
      const docsToDeleteForSharedMembers: {
        oldDoc: DocumentData;
        newDoc: DocumentData;
      }[] = [];
      const helperData: Map<string, string> = new Map();
      helperData.set("userId", uid);
      const requestData = request.data.dataList as ISyncRequestModel[];
      const parsedRequestData = requestData.map((data) =>
        ZSyncRequestModel.parse(data)
      );
      parsedRequestData.sort(
        (a, b) => a.localUpdatedAt.getTime() - b.localUpdatedAt.getTime()
      );
      const patchDataList = parsedRequestData.filter(
        (data: ISyncRequestModel) => {
          return data.updateType == "patch";
        }
      );
      const rawDataList = request.data.dataList.filter(
        (data: ISyncRequestModel) => {
          return data.updateType == "raw";
        }
      );
      console.log("pathDataList - ", patchDataList);
      console.log("patchDataList length - ", patchDataList.length);
      for (const syncData of patchDataList) {
        const parsedSyncData = ZSyncRequestModel.parse(syncData);
        try {
          logger.log("type of update received - " + parsedSyncData.updateType);
          const patchData = JSON.parse(parsedSyncData.data);
          const patchMap = JSON.parse(patchData.patch) as jsonpatch.Operation[];

          const result: {
            requireDeletion: boolean;
            requireSharedMemberDeletion: boolean;
            documentToDelete: IBaseSchema;
            oldDoc: DocumentData | undefined;
          } = await new DbRepository().updateDataWithPatch(
            patchData.docCollection,
            patchData.id,
            parsedSyncData.isCreate,
            patchMap
          );

          if (result.requireDeletion) {
            docsToDelete.push(result.documentToDelete);
          }
          if (result.requireSharedMemberDeletion) {
            if (result.oldDoc) {
              docsToDeleteForSharedMembers.push({
                oldDoc: result.oldDoc,
                newDoc: result.documentToDelete,
              });
            }
          }
          finalResult.push({ id: parsedSyncData.id, success: true });
        } catch (error) {
          finalResult.push({
            id: parsedSyncData.id,
            success: false,
            error: error instanceof Error ? error.message : JSON.stringify(error),
          });
          logger.error("Error in patching data - " + error);
        }
      }

      console.log("rawDataList length - ", rawDataList.length);
      console.log("rawDataList - ", rawDataList);
      for (const syncData of rawDataList) {
        const parsedSyncData = ZSyncRequestModel.parse(syncData);
        logger.log("type of update received - " + parsedSyncData.updateType);
        const tasks: Promise<boolean>[] = [];
        const dataReceived: IBaseSchema = JSON.parse(parsedSyncData.data);
        const finalData = await new DataMigrationService().migrateSchema(
          dataReceived,
          helperData
        );

        // Check if the document needs to be deleted for some users.
        // Also avoid creating docs that are already deleted or deleting.
        // Also avoid deleting docs using an old update if new updated state already exist.
        let dontCreateUpdateDoc = false;
        const db = admin.firestore();
        const docRef = db.collection(finalData.docCollection).doc(finalData.id);
        await db.runTransaction(async (transaction) => {
          const oldDoc = await transaction.get(docRef);
          if (oldDoc.exists) {
            const oldDocData = oldDoc.data() as IBaseSchema;
            if (finalData.permaDeletedAt) {
              if (
                new Date(oldDocData.localUpdatedAt).getTime() <
                new Date(finalData.localUpdatedAt).getTime()
              ) {
                docsToDelete.push(finalData);
              }
              dontCreateUpdateDoc = true;
            } else if (
              finalData.docCollection === "moneyTrackerTransactions" &&
              (finalData as IMoneyTrackerTransactions).setupId !=
                (oldDocData as IMoneyTrackerTransactions).setupId
            ) {
              if (oldDocData) {
                docsToDeleteForSharedMembers.push({
                  oldDoc: oldDocData,
                  newDoc: finalData,
                });
              }
            }
          } else {
            // If doc doesn't exists and is update operation, return
            if (parsedSyncData.isCreate === false) {
              dontCreateUpdateDoc = true;
            }
          }
        });

        if (dontCreateUpdateDoc) {
          continue;
        } else {
          tasks.push(new DbRepository().updateData(getMeParsedData(finalData)));
        }
        await Promise.all(tasks);
      }

      // Handle deletions in bulk
      if (docsToDelete.length > 0) {
        logger.log("deleting documents - " + docsToDelete.length);
        await deleteDocs(uid, docsToDelete);
      }

      // Handle shared member tasks for remove from shared member using special activities
      if (docsToDeleteForSharedMembers.length > 0) {
        logger.log(
          "creating special activities for remove for shared memebers - " +
            docsToDeleteForSharedMembers.length
        );
        await createSpecialActivitiesForSharedMembersForTaskRemove(
          uid,
          docsToDeleteForSharedMembers
        );
      }

      logger.info("Data synced successfully");
      return finalResult;
    } catch (error) {
      logger.error("Error in updating data save function: ");
      console.log(error);
      if (error instanceof Error) {
        logger.error((error as Error).message);
      }
      if (error instanceof ZodError) {
        throw new functions.https.HttpsError(
          INVALID_ARGUMENT,
          JSON.stringify((error as ZodError).issues)
        );
      }
      throw new functions.https.HttpsError(
        INTERNAL_SERVER_ERROR,
        "Unknown error occurred in updating data.\n" + error
      );
    }
  }
);
