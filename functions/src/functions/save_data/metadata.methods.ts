import * as admin from "firebase-admin";
import { FieldValue, Timestamp } from "firebase-admin/firestore";
import { IBaseSchema } from "../migration/versions/base/base_schema";
import {
  IChatUser,
  II<PERSON>ueReport,
  IFeedback,
  IUserMetadata,
  ZUserMetadata,
  IViewSettings,
} from "../migration/versions/base/model_mappings";
import { convertDatesToTimestamps } from "../../utils/utility_methods";

export async function updateServerMetadata(
  source: IBaseSchema,
  currentSource: IBaseSchema | null,
  exists = true
) {
  const db = admin.firestore();
  console.log(
    "Updating metaData: " + source.docCollection + ", id = " + source.id
  );

  if (source.docCollection == "users") {
    const docRef = db.collection("usersMetadata").doc(source.uid);
    let userMetadata;
    if (exists) {
      userMetadata = { ...(source as unknown as IUserMetadata) };
    } else {
      userMetadata = ZUserMetadata.parse(source);
      userMetadata.userSegments = ["ALL"];
    }
    if (currentSource != null) {
      userMetadata.localUpdatedAt = currentSource.localUpdatedAt;
    }
    try {
      userMetadata.docCollection = "usersMetadata";
      const metaMap = convertDatesToTimestamps(userMetadata);
      await docRef.set(metaMap, { merge: true });
    } catch (error) {
      console.log(
        "error in updating user metadata: " +
          source.docCollection +
          "/v" +
          String(source.docVer)
      );
      console.log(error);
    }
  } else if (source.docCollection == "viewSettings") {
    const docRef = db.collection("usersMetadata").doc(source.uid);
    const viewSettings: IViewSettings = {
      ...(source as unknown as IViewSettings),
    };

    if (currentSource != null) {
      viewSettings.localUpdatedAt = currentSource.localUpdatedAt;
    }
    try {
      viewSettings.docCollection = "usersMetadata";
      const metaMap = {
        appSettings: viewSettings.appSettings,
        featureSettings: viewSettings.featureSettings,
        notificationSettings: viewSettings.notificationSettings,
      };
      await docRef.set(metaMap, { merge: true });
    } catch (error) {
      console.log(
        "error in updating user metadata: " +
          source.docCollection +
          "/v" +
          String(source.docVer)
      );
      console.log(error);
    }
  } else if (source.docCollection == "chatUsers") {
    const batch = db.batch();

    const docRef = db.collection("serverMetadata").doc("supportChatCount");
    try {
      let currDataChat;
      if (exists) {
        currDataChat = currentSource as IChatUser;
        const currStatus = currDataChat?.status;

        if (
          !currDataChat.clarify &&
          currStatus != null &&
          currStatus != "notReplied"
        ) {
          batch.set(
            docRef,
            {
              notReplied: FieldValue.increment(1),
              [currStatus]: FieldValue.increment(-1),
            },
            { merge: true }
          );
          batch.set(
            db.collection("usersMetadata").doc(source.uid),
            {
              chatStatus: "notReplied",
            },
            { merge: true }
          );
        }
      } else {
        batch.set(
          docRef,
          {
            notReplied: FieldValue.increment(1),
          },
          { merge: true }
        );
        batch.set(
          db.collection("usersMetadata").doc(source.uid),
          {
            chatStatus: "notReplied",
          },
          { merge: true }
        );
      }
      batch.commit();
    } catch (error) {
      console.log(
        "error in updating supportchat counts metadata: " +
          source.docCollection +
          "/v" +
          String(source.docVer)
      );
      console.log(error);
    }
  } else if (source.docCollection == "issueReports") {
    const docRef = db.collection("serverMetadata").doc("issueReportsCount");
    try {
      const dataReport = source as unknown as IIssueReport;
      const status = dataReport.status;
      if (!exists) {
        const countMap = {
          [status]: FieldValue.increment(1),
        };
        await docRef.update(countMap);
      }
    } catch (error) {
      console.log(
        "error in updating issuereports counts metadata: " +
          source.docCollection +
          "/v" +
          String(source.docVer)
      );
      console.log(error);
    }
  } else if (source.docCollection == "usersFeedback") {
    const batch = db.batch();
    const docRef = db.collection("usersMetadata").doc(source.uid);
    const countRef = db.collection("serverMetadata").doc("appMetadata");
    try {
      const feedback = source as unknown as IFeedback;
      if (feedback.isStoreFeedbackGiven ?? false) {
        batch.set(
          docRef,
          {
            isFeedbackGiven: true,
            isStoreFeedbackGiven: true,
            cloudUpdatedAt: Timestamp.fromMillis(Date.now()),
            localUpdatedAt: Timestamp.fromMillis(Date.now()),
          },
          { merge: true }
        );
      } else {
        batch.set(docRef, { isFeedbackGiven: true }, { merge: true });
      }
      if (!exists) {
        batch.set(
          countRef,
          { feedbacksCount: FieldValue.increment(1) },
          { merge: true }
        );
      }
      await batch.commit();
    } catch (error) {
      console.log(
        "error in updating user metadata: " +
          source.docCollection +
          "/v" +
          String(source.docVer)
      );
      console.log(error);
    }
  }
}
