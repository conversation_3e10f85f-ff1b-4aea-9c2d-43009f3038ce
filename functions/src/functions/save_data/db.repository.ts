import { DocumentData, Timestamp } from "@google-cloud/firestore";
import * as admin from "firebase-admin";
import { FieldValue } from "firebase-admin/firestore";
import {
  ImageMetadata,
  AudioVideoMetadata,
  IAttachmentModel,
} from "../migration/versions/base/attachments_model";
import {
  IBaseSchema,
  isSupportCollection,
} from "../migration/versions/base/base_schema";
import {
  currentDbVersion,
  IUser,
  IChatMessages,
  getMeParsedData,
  IChatUser,
  IUserResources,
  IUserKeys,
  IList,
  IMoneyTrackerTransactions,
} from "../migration/versions/base/model_mappings";
import { translateToEnglish } from "../translation/translation_methods";
import { updateServerMetadata } from "./metadata.methods";
import {
  convertDatesToTimestamps,
  getOptimizedFilePath,
  getOriginalFilePath,
  getThumbnailFilePath,
} from "../../utils/utility_methods";
import { generatePseudoName } from "../user/user_api/get_user";
import * as jsonpatch from "fast-json-patch";
import { deleteDoc } from "../user/lifecycle/components/trash";
import { DataMigrationService } from "../migration/migration_service";
import { setPublicUser } from "../../services/cloudfare/user.service";
import { setPublicNote } from "../../services/cloudfare/note.service";
import { setPublicList } from "../../services/cloudfare/list.service";
import { calculateStorageForUserById } from "../account_backup/storage";
import { SharedType } from "../collabration/constants";

export class DbRepository {
  async updateDataWithPatch(
    docCollection: string,
    docId: string,
    isCreate: boolean,
    patchData: jsonpatch.Operation[]
  ): Promise<{
    requireDeletion: boolean;
    requireSharedMemberDeletion: boolean;
    documentToDelete: IBaseSchema;
    oldDoc: DocumentData | undefined;
    saveHappened: boolean;
  }> {
    console.log("Updating with patch: " + docCollection + ", id = " + docId);
    const db = admin.firestore();
    const docRef = db.collection(docCollection).doc(docId);
    return await db.runTransaction(async (transaction) => {
      const oldDoc = await transaction.get(docRef);
      let oldDataAsMap = {};
      if (oldDoc.exists) {
        oldDataAsMap = getMeParsedData(oldDoc.data() as IBaseSchema);
      }
      const patchedData = jsonpatch.applyPatch(oldDataAsMap, patchData)
        .newDocument as IBaseSchema;
      // parse and verify patched data
      const patchedNewDoc = getMeParsedData(patchedData);
      const helperData: Map<string, string> = new Map();
      const finalData = await new DataMigrationService().migrateSchema(
        patchedNewDoc,
        helperData
      );

      if (!oldDoc.exists) {
        // If doc doesn't exists and is update operation, return
        if (isCreate === false) {
          return {
            requireDeletion: false,
            requireSharedMemberDeletion: false,
            documentToDelete: finalData,
            oldDoc: oldDoc.data(),
            saveHappened: false,
          };
        }
      }

      // parse final data to check data integrity
      const parsedFinalData = getMeParsedData(finalData);

      // check for permaDeletedAt
      if (finalData.permaDeletedAt) {
        let stopDeletion = false;
        if (oldDoc.exists) {
          stopDeletion =
            new Date((oldDoc.data() as IBaseSchema).localUpdatedAt).getTime() >
            new Date(finalData.localUpdatedAt).getTime();
        }

        const saveHappened = !stopDeletion;

        // Only update public data if save actually happened
        if (saveHappened) {
          this.updatePublicData(parsedFinalData);
        }

        return {
          requireDeletion: stopDeletion ? false : true,
          requireSharedMemberDeletion: false,
          documentToDelete: finalData,
          oldDoc: oldDoc.data(),
          saveHappened,
        };
      } else {
        let saveHappened = false;
        if (oldDoc.exists) {
          const oldDocData = oldDoc.data();
          saveHappened = await this.saveDocIfExist(
            transaction,
            docRef,
            oldDoc.data() as IBaseSchema,
            parsedFinalData as IBaseSchema
          );

          // Only update public data if save actually happened
          if (saveHappened) {
            this.updatePublicData(parsedFinalData);
          }

          if (
            finalData.docCollection === "moneyTrackerTransactions" &&
            (finalData as IMoneyTrackerTransactions).setupId !=
              (oldDocData as IMoneyTrackerTransactions).setupId
          ) {
            return {
              requireDeletion: false,
              requireSharedMemberDeletion: true,
              documentToDelete: finalData,
              oldDoc: oldDoc.data(),
              saveHappened,
            };
          }
        } else {
          saveHappened = await this.saveNewDocument(
            transaction,
            docRef,
            parsedFinalData as IBaseSchema
          );

          // Only update public data if save actually happened
          if (saveHappened) {
            this.updatePublicData(parsedFinalData);
          }
        }
        return {
          requireDeletion: false,
          requireSharedMemberDeletion: false,
          documentToDelete: finalData,
          oldDoc: oldDoc.data(),
          saveHappened,
        };
      }
    });
  }

  // Helper method to update public data based on document collection
  private updatePublicData(data: IBaseSchema): void {
    if (data.docCollection === "publicUsers") {
      setPublicUser(new Map(Object.entries(data)));
    } else if (data.docCollection === "notes") {
      setPublicNote(new Map(Object.entries(data)));
    } else if (data.docCollection === "lists") {
      setPublicList(new Map(Object.entries(data)));
    }
  }

  async updateData(
    data: IBaseSchema,
    userKey?: IUserKeys | null
  ): Promise<boolean> {
    if (data.docVer != currentDbVersion) {
      throw new Error("invalid doc version");
    }
    const docId =
      data.docCollection == "users" || data.docCollection == "chatUsers"
        ? data.uid
        : data.id;
    console.log("Updating: " + data.docCollection + ", id = " + docId);
    const db = admin.firestore();
    const docRef = db.collection(data.docCollection).doc(docId);
    try {
      let saveHappened = false;
      await db
        .runTransaction(async (t) => {
          const doc = await t.get(docRef);
          if (doc.exists) {
            saveHappened = await this.saveDocIfExist(
              t,
              docRef,
              doc.data() as IBaseSchema,
              data
            );
          } else {
            saveHappened = await this.saveNewDocument(t, docRef, data, userKey);
          }
        })
        .catch((error) => {
          console.error("Exception in executing db transaction");
          console.log(error);
          throw error;
        });
      console.log("User transaction success, save happened:", saveHappened);
      return saveHappened;
    } catch (e) {
      console.error(
        `User transaction failure: ${data.uid}/${data.docCollection}/${data.id}`,
        e
      );
      throw e;
    }
  }

  async saveNewDocument(
    t: admin.firestore.Transaction,
    docRef: admin.firestore.DocumentReference,
    data: IBaseSchema,
    userKey?: IUserKeys | null
    // shared?: SharedType | null
  ): Promise<boolean> {
    console.log("User document not found, updating with new one");
    const db = admin.firestore();
    if (data.docCollection == "users") {
      const usersData = { ...(data as unknown as IUser) };
      usersData.userInfo.pseudoName = await generatePseudoName();
      t.create(docRef, convertDatesToTimestamps(usersData));
      const countRef = db.collection("serverMetadata").doc("appMetadata");
      t.set(
        countRef,
        {
          usersCount: FieldValue.increment(1),
        },
        { merge: true }
      );
    } else if (data.docCollection == "chatMessages") {
      const chatMsg = { ...(data as unknown as IChatMessages) };
      if (chatMsg.supportLanguage != "english") {
        const text = await translateToEnglish(chatMsg, chatMsg.supportLanguage);
        const messageMap = {
          ...chatMsg.message,
          en: text,
        };
        chatMsg.message = messageMap;
        t.create(docRef, convertDatesToTimestamps(chatMsg));
      } else {
        t.create(docRef, convertDatesToTimestamps(chatMsg));
      }
    } else if (data.docCollection === "userResources") {
      const userResources = { ...(data as unknown as IUserResources) };
      const shared: SharedType | null = userKey?.shared ?? null;
      const containsShared = shared !== null && shared !== undefined;
      if (containsShared) {
        userResources.shared = shared;
      }
      t.create(docRef, convertDatesToTimestamps(userResources));
      // remove shared key from userKeys
      if (containsShared && userKey) {
        const updatedUserKeys: IUserKeys = Object.fromEntries(
          Object.entries(userKey).filter(([key]) => key !== "shared")
        ) as IUserKeys;
        const userKeysRef = db.collection("userKeys").doc(userKey.id);
        t.set(userKeysRef, convertDatesToTimestamps(updatedUserKeys));
      }
    } else {
      t.create(docRef, convertDatesToTimestamps(data));
    }
    await updateServerMetadata(data, null, false);
    return true; // Document was saved successfully
  }

  async saveDocIfExist(
    transaction: admin.firestore.Transaction,
    docRef: admin.firestore.DocumentReference,
    doc: IBaseSchema,
    newData: IBaseSchema
  ): Promise<boolean> {
    const localUpdatedAt = getMeParsedData(doc as IBaseSchema).localUpdatedAt;
    if (doc?.docVer !== newData.docVer) {
      console.log("Document is from outdated client");
      const err = new Error("Document is from outdated client");
      err.name = "OutdatedClient";
      throw err;
    } else if (
      localUpdatedAt.getTime() > new Date(newData.localUpdatedAt).getTime()
    ) {
      // if collection is list, check if new item is added
      if (
        newData.docCollection === "lists" &&
        newData.docVer >= currentDbVersion
      ) {
        // allow save if new item is added
        const listData = doc as unknown as IList;
        const newListData = newData as unknown as IList;

        const isNewItemAdded = Object.keys(newListData.listItems).some(
          (item) => !listData.listItems[item]
        );
        const isAnyItemEdited = Object.keys(newListData.listItems).some(
          (item) =>
            listData.listItems[item]?.item !==
              newListData.listItems[item]?.item ||
            listData.listItems[item]?.deletedAt !==
              newListData.listItems[item]?.deletedAt
        );
        if (isNewItemAdded || isAnyItemEdited) {
          // merge the list items and update the document
          const mergedListItems = {
            ...listData.listItems,
            ...newListData.listItems,
          };

          (newData as IList).listItems = mergedListItems;
          // remove the items with deletedAt not null
          (newData as IList).listItems = Object.keys(mergedListItems).reduce(
            (acc, item) => {
              if (mergedListItems[item]?.deletedAt == null) {
                acc[item] = mergedListItems[item];
              }
              return acc;
            },
            {} as any
          );
          newData.cloudUpdatedAt = Timestamp.now().toDate();
          newData.localUpdatedAt = Timestamp.now().toDate();
          console.log(
            "Updating server metadata, cloudUpdatedAt: ",
            newData.cloudUpdatedAt,
            "localUpdatedAt: ",
            newData.localUpdatedAt
          );
          await updateServerMetadata(newData, doc as IBaseSchema, true);
          transaction.update(docRef, convertDatesToTimestamps(newData));
          return true; // Save happened for lists with new or edited items
        }
      }
      console.log("Document is outdated");
      return false; // No save happened due to outdated document
    } else if (
      new Date(newData.localUpdatedAt).getTime() - Date.now() >
      180000
    ) {
      console.log("Document is too far in the future");
      return false; // No save happened due to date validation
    } else {
      await this.checkForImageDeletion(newData);
      // Check if document needs to be deleted based on conditions
      if (newData.permaDeletedAt) {
        console.log("Deleting document based on deletion criteria");
        await deleteDoc(doc, transaction);
        return true; // Document deletion is considered a "save" operation
      } else {
        // Proceed with the update logic
        if (newData.docCollection == "users") {
          const usersData = { ...(newData as unknown as IUser) };

          // Preserve the old pseudoName
          if (usersData.userInfo.pseudoName == null) {
            usersData.userInfo.pseudoName = (
              doc as unknown as IUser
            ).userInfo.pseudoName;
          }

          // Preserve the old dataBackupInfo as should not be changed from client
          usersData.dataBackupInfo = (doc as unknown as IUser).dataBackupInfo;

          transaction.update(docRef, convertDatesToTimestamps(usersData));
        } else if (newData.docCollection == "chatUsers") {
          const chatUser = { ...(newData as unknown as IChatUser) };
          chatUser.clarify = (doc as unknown as IChatUser).clarify;
          chatUser.status = "notReplied";
          transaction.update(docRef, convertDatesToTimestamps(chatUser));
        } else {
          transaction.update(docRef, convertDatesToTimestamps(newData));
        }
        await updateServerMetadata(newData, doc as IBaseSchema, true);
        return true; // Save happened successfully
      }
    }
  }

  async updateAttachmentData(
    docId: string,
    docCollectionName: string,
    fileId: number,
    fileSize: number,
    metadata: ImageMetadata | AudioVideoMetadata | null,
    format: string | null,
    fileType: string
  ) {
    console.log("Updating attachment data for: " + docCollectionName);
    const db = admin.firestore();
    const docRef = db.collection(docCollectionName).doc(docId);
    try {
      await db
        .runTransaction(async (t) => {
          const doc = await t.get(docRef);
          if (doc.exists && doc.data() != undefined) {
            const docData = doc.data() as AttachmentArrayType;
            const itemIndex = docData.attachments.findIndex(
              (item) => item.id == fileId
            );
            if (itemIndex >= 0) {
              const imageItem = docData.attachments[itemIndex];
              imageItem.size = fileSize;
              if (metadata != null) {
                let height = null;
                let width = null;
                let duration = null;
                if (
                  fileType == "image" &&
                  "height" in metadata &&
                  "width" in metadata
                ) {
                  height = metadata.height;
                  width = metadata.width;
                }
                if (
                  (fileType == "audio" || fileType == "video") &&
                  "duration" in metadata
                ) {
                  duration = metadata.duration;
                }
                imageItem.metadata =
                  fileType == "image"
                    ? { height: height, width: width }
                    : fileType == "audio" || fileType == "video"
                      ? { duration: duration }
                      : null;
              }
              // imageItem.localPath = null;
              imageItem.status = "cloud";
              if (format != null) {
                imageItem.format = format;
              }
              imageItem.fileType = fileType;
            } else {
              const imageItem = {
                id: fileId,
                createdAt: Timestamp.now().toDate(),
                status: "cloud",
                size: fileSize,
                metadata: null,
                format: format,
                localPath: "",
                deletedAt: null,
                fileType: fileType,
              } as IAttachmentModel;
              if (metadata != null) {
                let height = null;
                let width = null;
                let duration = null;
                if (
                  fileType == "image" &&
                  "height" in metadata &&
                  "width" in metadata
                ) {
                  height = metadata.height;
                  width = metadata.width;
                }
                if (
                  (fileType == "audio" || fileType == "video") &&
                  "duration" in metadata
                ) {
                  duration = metadata.duration;
                }
                imageItem.metadata =
                  fileType == "image"
                    ? { height: height, width: width }
                    : fileType == "audio" || fileType == "video"
                      ? { duration: duration }
                      : null;
              }
              if (format != null) {
                imageItem.format = format;
              }
              docData.attachments.push(imageItem);
            }
            docData.cloudUpdatedAt = null;

            t.update(
              docRef,
              convertDatesToTimestamps(docData as unknown as IBaseSchema)
            );
            if (!isSupportCollection(docCollectionName)) {
              const userDocRef = admin
                .firestore()
                .collection("users")
                .doc(docData.uid);
              t.update(userDocRef, {
                "userInfo.storageUsed": FieldValue.increment(fileSize),
                cloudUpdatedAt: Timestamp.now(),
              });
              const userMetadataDocRef = admin
                .firestore()
                .collection("usersMetadata")
                .doc(docData.uid);
              t.update(userMetadataDocRef, {
                "userInfo.storageUsed": FieldValue.increment(fileSize),
                cloudUpdatedAt: Timestamp.now(),
              });
            }
          } else {
            console.log("No action found for Id" + docId);
          }
        })
        .catch((error) => {
          console.log("Exception in executing db transaction");
          console.log(error);
          throw error;
        });
      console.log("Transaction success - Attachment data updated");
    } catch (e) {
      console.log("Transaction failure - Attachment data", e);
    }
  }

  async checkForImageDeletion(data: IBaseSchema) {
    if (
      data.docCollection == "todos" ||
      data.docCollection == "journalActions" ||
      data.docCollection == "habitActions" ||
      data.docCollection == "notes" ||
      data.docCollection == "moneyTrackerTransactions"
    ) {
      const imageArray = (data as unknown as AttachmentArrayType).attachments;
      const tasks: Promise<void>[] = [];
      imageArray.forEach((value) => {
        if (value.deletedAt != null) {
          tasks.push(
            this.deleteUserMedia(
              value.id,
              data.uid,
              data.id,
              value.size,
              data.docCollection,
              value.format
            )
          );
        }
      });
      (data as unknown as AttachmentArrayType).attachments = imageArray.filter(
        (value) => value.deletedAt == null
      );
      await Promise.all(tasks);
      await calculateStorageForUserById(data.uid);
    }
  }

  async deleteUserMedia(
    fileId: number,
    uid: string,
    docId: string,
    fileSize: number,
    docCollectionName: string,
    format: string
  ) {
    console.log("deleting image: " + fileId);
    const bucket = admin.storage().bucket();
    const extension = format ?? "jpeg";
    const originalImageRef = bucket.file(
      getOriginalFilePath(uid, docCollectionName, docId, fileId, extension)
    );
    const optimizedImageRef = bucket.file(
      getOptimizedFilePath(uid, docCollectionName, docId, fileId, extension)
    );
    const thumbnailImageRef = bucket.file(
      getThumbnailFilePath(uid, docCollectionName, docId, fileId, extension)
    );
    // const userDocRef = admin.firestore().collection("users").doc(uid);

    await Promise.all([
      originalImageRef.delete({ ignoreNotFound: true }),
      optimizedImageRef.delete({ ignoreNotFound: true }),
      thumbnailImageRef.delete({ ignoreNotFound: true }),
      // userDocRef.update({
      //   "userInfo.storageUsed": FieldValue.increment(-1 * fileSize),
      //   cloudUpdatedAt: FieldValue.serverTimestamp(),
      // }),
    ]).catch((e) => {
      console.log(e);
    });
  }

  async updateMigratedData(data: IBaseSchema): Promise<void> {
    const db = admin.firestore();
    const docId =
      data.docCollection == "users" ||
      data.docCollection == "usersMetadata" ||
      data.docCollection == "chatUsers"
        ? data.uid
        : data.id;
    const docRef = db.collection(data.docCollection).doc(docId);
    await docRef.set(convertDatesToTimestamps(data));
  }
}

export type AttachmentArrayType = {
  attachments: Array<IAttachmentModel>;
} & IBaseSchema;

export function isDocHasAttachments(doc: any): boolean {
  return "attachments" in doc && Array.isArray(doc.attachments);
}
