{"internalDescription": "Seperate pause functionlity from task", "externalDescription": {"de": "Mit Ihnen geteilte Funktionen verbergen, ohne die Ansicht anderer zu beeinträchtigen", "fr": "Masquer les fonctionnalités partagées avec vous sans affecter la vue des autres", "pt": "Ocultar recursos compartilhados com você sem detectar a visualização de outras pessoas", "en": "Hide features shared with you without afttecting others view", "es": "Ocultar funciones compartidas contigo sin afectar la vista de los demás", "it": "Nascondi le funzionalità condivise con te senza compromettere la vista degli altri"}, "rid": 0.104, "dbVersion": 132, "envs": {"qa": {"enabledPlatforms": ["ios", "android", "web", "mac", "windows", "linux"], "enabledSegments": ["ALL"], "featureFlags": {"support": ["ALL"], "debugScreen": ["ALL"], "allowShorebirdPatchInstall": ["ALL"]}, "parameters": {"modalBarrierOpacity_n": 70, "adWaitTimeInSec_n": 5, "colorsVersion_n": 47, "translationsVersion_n": 234, "gracePeriodInMin_n": 43200, "passcodeReAuthTimeout_n": 300000, "noOfInteractionsForAds_n": 5, "showAds_b": true, "maxPasscodeAttempts_n": 5}}, "staging": {"parameters": {"gracePeriodInMin_n": 43200, "passcodeReAuthTimeout_n": 300000, "translationsVersion_n": 234, "noOfInteractionsForAds_n": 5, "maxPasscodeAttempts_n": 5, "colorsVersion_n": 47, "adWaitTimeInSec_n": 5, "modalBarrierOpacity_n": 70, "showAds_b": true}, "enabledSegments": ["ALL"], "enabledPlatforms": ["ios", "android", "web", "mac", "windows"], "featureFlags": {"allowShorebirdPatchInstall": ["staff"], "debugScreen": ["ALL", "staff"], "support": ["ALL"]}}, "hotfix": {"featureFlags": {"support": ["ALL"], "allowShorebirdPatchInstall": ["staff"], "debugScreen": ["staff"]}, "enabledSegments": ["ALL"], "enabledPlatforms": ["ios", "android", "web", "windows", "mac", "linux"], "parameters": {"adWaitTimeInSec_n": 5, "noOfInteractionsForAds_n": 5, "modalBarrierOpacity_n": 70, "showAds_b": true, "translationsVersion_n": 234, "passcodeReAuthTimeout_n": 300000, "gracePeriodInMin_n": 43200, "maxPasscodeAttempts_n": 5, "colorsVersion_n": 47}}, "prod": {"featureFlags": {"debugScreen": ["ALL", "staff"], "allowShorebirdPatchInstall": ["ALL"], "support": ["ALL"]}, "parameters": {"maxPasscodeAttempts_n": 5, "translationsVersion_n": 234, "noOfInteractionsForAds_n": 7, "colorsVersion_n": 47, "passcodeReAuthTimeout_n": 300000, "adWaitTimeInSec_n": 5, "showAds_b": true, "gracePeriodInMin_n": 43200, "modalBarrierOpacity_n": 70}, "enabledSegments": ["ALL"], "enabledPlatforms": ["android", "web", "windows", "mac", "linux", "ios"]}, "dev": {"parameters": {"showAds_b": true, "translationsVersion_n": 234, "gracePeriodInMin_n": 43200, "modalBarrierOpacity_n": 70, "colorsVersion_n": 47, "maxPasscodeAttempts_n": 5, "noOfInteractionsForAds_n": 5, "adWaitTimeInSec_n": 5, "passcodeReAuthTimeout_n": 300000}, "enabledSegments": ["ALL"], "enabledPlatforms": ["web", "ios", "android", "mac", "windows", "linux"], "featureFlags": {"allowShorebirdPatchInstall": ["ALL"], "debugScreen": ["ALL"], "support": ["ALL"]}}}, "id": "5df00a99-c38d-4185-92ae-7d2c84f27928", "deletedAt": null}