/**
 * STRIPE CONFIGURATION - Source of Truth
 * 
 * This file contains all Stripe-related configuration for all environments.
 * Update this file when adding new products or changing pricing.
 * 
 * Environment mapping:
 * - prod: Production account (both live and test mode)
 * - dev: Development sandbox account (test mode only)
 * - qa: QA sandbox account (test mode only) 
 * - staging: Staging sandbox account (test mode only)
 * - hotfix: Hotfix sandbox account (test mode only)
 */

export interface StripeProduct {
  entitlement: "basic" | "pro" | "plus";
  productName: string;
  priceUsdCents: number;
  interval: "month" | "year";
  description: string;
}

export interface StripeEnvironmentConfig {
  accountId: string;
  products: {
    [key: string]: {
      priceId: string;
      liveMode?: boolean;
    };
  };
}

export interface StripePackageProduct {
  entitlement: "basic" | "pro" | "plus";
  mePackageId: string;
  productId: string;
  packageId: string;
  packagePrice: string;
  packageCurrency: string;
  packageDuration: "monthly" | "yearly";
  description: string;
  liveMode: boolean;
}

// Base product definitions (same across all environments)
export const STRIPE_PRODUCTS: Record<string, StripeProduct> = {
  basic_monthly: {
    entitlement: "basic",
    productName: "Basic Monthly",
    priceUsdCents: 199, // $1.99
    interval: "month",
    description: "Give access to all basic features of app without ads"
  },
  basic_yearly: {
    entitlement: "basic", 
    productName: "Basic Yearly",
    priceUsdCents: 2390, // $23.90
    interval: "year",
    description: "Give access to all basic features of app without ads"
  },
  pro_monthly: {
    entitlement: "pro",
    productName: "Pro Monthly", 
    priceUsdCents: 399, // $3.99
    interval: "month",
    description: "1 GB of storage"
  },
  pro_yearly: {
    entitlement: "pro",
    productName: "Pro Yearly",
    priceUsdCents: 4790, // $47.90
    interval: "year", 
    description: "1 GB of storage"
  },
  plus_monthly: {
    entitlement: "plus",
    productName: "Plus Monthly",
    priceUsdCents: 799, // $7.99
    interval: "month",
    description: "10 GB of storage"
  },
  plus_yearly: {
    entitlement: "plus",
    productName: "Plus Yearly", 
    priceUsdCents: 9590, // $95.90
    interval: "year",
    description: "10 GB of storage"
  }
};

// Environment-specific Stripe configurations
export const STRIPE_ENVIRONMENTS: Record<string, StripeEnvironmentConfig> = {
  prod: {
    accountId: "acct_1OHnuGLYG8kwP9Sp",
    products: {
      // Live mode price IDs for production
      basic_monthly: { priceId: "price_1RgVplLYG8kwP9SpQfd7qNc9", liveMode: true },
      basic_yearly: { priceId: "price_1RgVpmLYG8kwP9SprkxE4NZE", liveMode: true },
      pro_monthly: { priceId: "price_1RgVpoLYG8kwP9SpR1PZvUdw", liveMode: true },
      pro_yearly: { priceId: "price_1RgVppLYG8kwP9Spl3fBTMOu", liveMode: true },
      plus_monthly: { priceId: "price_1RgVpqLYG8kwP9SpYFGkrU7S", liveMode: true },
      plus_yearly: { priceId: "price_1RgVprLYG8kwP9SpNYpBxK0q", liveMode: true },
      
      // Test mode price IDs for production (for testing in prod)
      basic_monthly_test: { priceId: "price_1RgVYvLYG8kwP9Sp3qNrMx8a", liveMode: false },
      basic_yearly_test: { priceId: "price_1RgVZALYG8kwP9SpuXDim5h9", liveMode: false },
      pro_monthly_test: { priceId: "price_1RgVZBLYG8kwP9Sp0K49Rl7b", liveMode: false },
      pro_yearly_test: { priceId: "price_1RgVZCLYG8kwP9SpCR3tOkR4", liveMode: false },
      plus_monthly_test: { priceId: "price_1RgVZDLYG8kwP9SpxdVRQhx8", liveMode: false },
      plus_yearly_test: { priceId: "price_1RgVZELYG8kwP9SpjVrJDkeE", liveMode: false }
    }
  },
  
  dev: {
    accountId: "acct_1RgVzAQ4cKv26Lhd",
    products: {
      basic_monthly: { priceId: "price_1RgW6NQ4cKv26LhdAssfH0fP" },
      basic_yearly: { priceId: "price_1RgW6OQ4cKv26Lhdu9ZDfsHC" },
      pro_monthly: { priceId: "price_1RgW6PQ4cKv26LhdB4fqWPTz" },
      pro_yearly: { priceId: "price_1RgW6QQ4cKv26Lhdoc5aj1CN" },
      plus_monthly: { priceId: "price_1RgW6RQ4cKv26LhddsnivVT2" },
      plus_yearly: { priceId: "price_1RgW6SQ4cKv26LhdMR0BTw7o" }
    }
  },
  
  qa: {
    accountId: "acct_1RgVzrQ54sLZs2Ap",
    products: {
      basic_monthly: { priceId: "price_1RgW6hQ54sLZs2ApM5mpKWB1" },
      basic_yearly: { priceId: "price_1RgW6jQ54sLZs2Ap2JrCKgry" },
      pro_monthly: { priceId: "price_1RgW6kQ54sLZs2Apb4nUsGhB" },
      pro_yearly: { priceId: "price_1RgW6lQ54sLZs2ApC9GKGC1u" },
      plus_monthly: { priceId: "price_1RgW6mQ54sLZs2ApSnfh9jHL" },
      plus_yearly: { priceId: "price_1RgW6nQ54sLZs2ApUmPm8xnr" }
    }
  },
  
  staging: {
    accountId: "acct_1RgW0LPsv3yxIeA0",
    products: {
      basic_monthly: { priceId: "price_1RgW72Psv3yxIeA0wCh3648m" },
      basic_yearly: { priceId: "price_1RgW73Psv3yxIeA0MdptyP1G" },
      pro_monthly: { priceId: "price_1RgW74Psv3yxIeA0vxFCo7X9" },
      pro_yearly: { priceId: "price_1RgW75Psv3yxIeA04UFya8gl" },
      plus_monthly: { priceId: "price_1RgW76Psv3yxIeA052zf2qQV" },
      plus_yearly: { priceId: "price_1RgW78Psv3yxIeA0CJMgjfC7" }
    }
  },
  
  hotfix: {
    accountId: "acct_1RgW0bPrbsT1mkUQ",
    products: {
      basic_monthly: { priceId: "price_1RgW7MPrbsT1mkUQ2LW6DEcQ" },
      basic_yearly: { priceId: "price_1RgW7NPrbsT1mkUQe48HdkJY" },
      pro_monthly: { priceId: "price_1RgW7OPrbsT1mkUQTWcyJlgB" },
      pro_yearly: { priceId: "price_1RgW7QPrbsT1mkUQ8sq5nlFB" },
      plus_monthly: { priceId: "price_1RgW7RPrbsT1mkUQ9cT5asUu" },
      plus_yearly: { priceId: "price_1RgW7SPrbsT1mkUQ2M4KSlGv" }
    }
  }
};

/**
 * Get Stripe configuration for a specific environment
 * @param {string} environment - Environment name (prod, dev, qa, staging, hotfix)
 * @return {StripeEnvironmentConfig | null} Environment configuration or null if not found
 */
export function getStripeConfig(environment: string): StripeEnvironmentConfig | null {
  return STRIPE_ENVIRONMENTS[environment] || null;
}

/**
 * Get all products for a specific environment with their details
 * @param {string} environment - Environment name (prod, dev, qa, staging, hotfix)
 * @param {boolean} useTestMode - Force test mode even in production (for testing)
 * @return {StripePackageProduct[]} Array of Stripe subscription packages for the environment
 */
export function getEnvironmentProducts(environment: string, useTestMode = false): StripePackageProduct[] {
  const config = getStripeConfig(environment);
  if (!config) {
    throw new Error(`Unknown environment: ${environment}`);
  }
  
  const products: StripePackageProduct[] = [];
  
  for (const [productKey, baseProduct] of Object.entries(STRIPE_PRODUCTS)) {
    // For production, choose test vs live mode products
    let priceKey = productKey;
    if (environment === "prod" && useTestMode) {
      priceKey = `${productKey}_test`;
    }
    
    const priceConfig = config.products[priceKey];
    if (priceConfig) {
      products.push({
        entitlement: baseProduct.entitlement,
        mePackageId: productKey,
        productId: productKey,
        packageId: priceConfig.priceId,
        packagePrice: (baseProduct.priceUsdCents / 100).toFixed(2),
        packageCurrency: "USD",
        packageDuration: baseProduct.interval === "month" ? "monthly" : "yearly",
        description: baseProduct.description,
        liveMode: priceConfig.liveMode || false
      });
    }
  }
  
  return products;
}

/**
 * Helper to get the correct API key environment variable name
 * @param {string} environment - Environment name (prod, dev, qa, staging, hotfix)
 * @param {boolean} useTestMode - Force test mode even in production (for testing)
 * @return {string} Environment variable name for Stripe API key
 */
export function getStripeApiKeyEnvVar(environment: string, useTestMode = false): string {
  if (environment === "prod") {
    return useTestMode ? "STRIPE_API_KEY_TEST" : "STRIPE_API_KEY";
  }
  
  // All sandbox environments use test mode only
  return "STRIPE_API_KEY_TEST";
}

/**
 * Helper to get the correct webhook secret environment variable name
 * @param {string} environment - Environment name (prod, dev, qa, staging, hotfix)
 * @param {boolean} useTestMode - Force test mode even in production (for testing)
 * @return {string} Environment variable name for Stripe webhook secret
 */
export function getStripeWebhookSecretEnvVar(environment: string, useTestMode = false): string {
  if (environment === "prod") {
    return useTestMode ? "STRIPE_WEBHOOK_SECRET_TEST" : "STRIPE_WEBHOOK_SECRET";
  }
  
  // All sandbox environments use test mode
  return "STRIPE_WEBHOOK_SECRET_TEST";
}