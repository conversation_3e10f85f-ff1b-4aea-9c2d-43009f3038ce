import {
  CF_API_TOKEN,
  CF_API_MEVID_UID_MAP_NAMESPACE,
} from "./cloudflare.data";

export async function setMevidUidMapper(
  mevolveId: string,
  uid: string
): Promise<void> {
  const key = mevolveId;
  const url = `${CF_API_MEVID_UID_MAP_NAMESPACE}/values/${key}`;
  await fetch(url, {
    method: "PUT",
    headers: {
      Authorization: `Bearer ${CF_API_TOKEN}`,
      "Content-Type": "text/plain",
    },
    body: uid,
  });
}

export async function deleteMevidUidMapper(mevolveId: string): Promise<void> {
  const key = mevolveId;
  const url = `${CF_API_MEVID_UID_MAP_NAMESPACE}/values/${key}`;
  await fetch(url, {
    method: "DELETE",
  });
}
