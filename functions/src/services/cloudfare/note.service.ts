import { CF_API_NOTE_NAMESPACE, CF_API_TOKEN } from "./cloudflare.data";
import {
  mapToPublicNoteInterface,
  PublicNoteInterface,
} from "./models/note.interface";
import { updateKV } from "./utils";

export async function getPublicNote(id: string): Promise<string> {
  const key = `note-${id}`;
  const url = `${CF_API_NOTE_NAMESPACE}/values/${key}`;
  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${CF_API_TOKEN}`,
    },
  });

  if (response.status !== 200) {
    throw new Error(`Failed to get value for key ${key}`);
  }

  return response.text();
}

export async function setPublicNote(value: Map<string, any>): Promise<void> {
  const noteObject = Object.fromEntries(value);
  if (noteObject.isPublic !== true) {
    console.log("This note is not public, skipping", noteObject);
    return;
  }
  console.log("start updating public note", noteObject);
  const note: PublicNoteInterface = mapToPublicNoteInterface(noteObject);
  if (note.shortLinkCode === undefined) {
    console.error("Short link code is undefined");
    return;
  }
  const keyId = note.shortLinkCode.split("/")[1];
  if (keyId === undefined) {
    console.error("Short link code is undefined");
    return;
  }
  await updateKV("note", keyId, note);
}

export async function removePublicNote(id: string): Promise<void> {
  const key = `note-${id}`;
  const url = `${CF_API_NOTE_NAMESPACE}/values/${key}`;
  const response = await fetch(url, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${CF_API_TOKEN}`,
      "Content-Type": "text/plain",
    },
  });

  if (response.status !== 200) {
    throw new Error(`Failed to delete value for key ${key}`);
  }
}
