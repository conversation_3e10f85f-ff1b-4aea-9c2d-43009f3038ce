export interface PublicNoteInterface {
  id: string;
  title: string;
  content?: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  attachmentsUrl: string[];
  emotion?: number;
  deletedAt?: Date;
  permaDeletedAt?: Date;
  shortLinkCode: string;
}

export function mapToPublicNoteInterface(data: any): PublicNoteInterface {
  return {
    id: data.id,
    title: data.title,
    content: data.description,
    attachmentsUrl: Array.isArray(data.attachments) ? data.attachments : [],
    emotion: data.emotion,
    userId: data.uid,
    createdAt: new Date(data.createdAt),
    updatedAt: new Date(),
    deletedAt: data.deletedAt ? new Date(data.deletedAt) : undefined,
    permaDeletedAt: data.permaDeletedAt
      ? new Date(data.permaDeletedAt)
      : undefined,
    shortLinkCode: data.publicId,
  };
}
