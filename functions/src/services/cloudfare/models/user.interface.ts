import {
  IListMetaData,
  INoteMetaData,
} from "../../../functions/migration/versions/base/model_mappings";

export interface PublicUserInterface {
  id: string;
  name: string;
  mevolveId: string;
  createdAt: Date;
  updatedAt: Date;
  notes: INoteMetaData[];
  lists: IListMetaData[];
  followingCount: number;
  followersCount: number;
  bio?: string;
  profileTags: string[];
  socialLinks: string[];
  uid: string;
}

// relations
//
export function mapToPublicUserInterface(data: any): PublicUserInterface {
  return {
    id: data.id,
    name: data.name,
    mevolveId: data.mevolveId,
    createdAt: new Date(data.createdAt),
    updatedAt: new Date(),
    notes: Array.isArray(data.publicNotesMetadata)
      ? data.publicNotesMetadata
      : [],
    lists: Array.isArray(data.publicListsMetadata)
      ? data.publicListsMetadata
      : [],
    followingCount: Number(data.following),
    followersCount: Number(data.followers),
    bio: data.bio,
    profileTags: Array.isArray(data.profileTags) ? data.profileTags : [],
    socialLinks: Array.isArray(data.socialLinks) ? data.socialLinks : [],
    uid: data.uid,
  };
}
