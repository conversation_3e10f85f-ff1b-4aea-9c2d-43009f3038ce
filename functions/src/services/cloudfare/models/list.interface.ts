export interface PublicListInterface {
  id: string;
  title: string;
  createdAt: Date;
  updatedAt: Date;
  userId: string;
  items: ListItemInterface[];
  deletedAt?: Date;
  permaDeletedAt?: Date;
  shortLinkCode: string;
}
export interface ListItemInterface {
  id: string;
  item: string;
  done: boolean;
  description?: string;
  customText?: string;
  position: number;
  deletedAt?: Date;
}

export function mapToListItemInterface(data: any): ListItemInterface {
  return {
    id: data.id,
    item: data.item,
    done: data.done,
    description: data.description,
    customText: data.customText,
    position: data.position,
    deletedAt: data.deletedAt ? new Date(data.deletedAt) : undefined,
  };
}

export function mapToPublicListInterface(data: any): PublicListInterface {
  const updatedData = {
    id: data.id,
    title: data.title,
    userId: data.uid,
    createdAt: new Date(data.createdAt),
    updatedAt: new Date(),
    items: Object.values(data.listItems || {}).map((item: any) => ({
      id: item.id,
      item: item.item,
      done: item.done,
      description: item.description,
      customText: item.customText,
      position: item.position,
      deletedAt: item.deletedAt ? new Date(item.deletedAt) : undefined,
    })),
    deletedAt: data.deletedAt ? new Date(data.deletedAt) : undefined,
    permaDeletedAt: data.permaDeletedAt
      ? new Date(data.permaDeletedAt)
      : undefined,
    shortLinkCode: data.publicId,
  };
  return updatedData;
}
