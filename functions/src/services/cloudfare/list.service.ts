import { CF_API_LIST_NAMESPACE, CF_API_TOKEN } from "./cloudflare.data";
import {
  mapToPublicListInterface,
  PublicListInterface,
} from "./models/list.interface";
import { updateKV } from "./utils";

export async function getPublicList(id: string): Promise<Partial<PublicListInterface> | null> {
  const key = `list-${id}`;
  const url = `${CF_API_LIST_NAMESPACE}/values/${key}`;
  console.log("getting list with keyId", key);
  try {
    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${CF_API_TOKEN}`,
      },
    });

    if (response.status === 200) {
      return JSON.parse(await response.text());
    } else if (response.status === 404) {
      return null; // Key doesn't exist
    } else {
      throw new Error(`Failed to get value for key ${key}`);
    }
  } catch (error) {
    console.error("Error fetching KV:", error);
    return null;
  }
}

export async function setPublicList(value: Map<string, any>): Promise<void> {

  const listObject = Object.fromEntries(value);
  if (listObject.isPublic !== true) {
    console.log("list is not public, skipping", listObject);
    return;
  }
  console.log("start updating public list", listObject);
  const list: PublicListInterface = mapToPublicListInterface(listObject);
  if (list.shortLinkCode === undefined) {
    console.error("Short link code is undefined");
    return;
  }
  console.log("setting list with shortLinkCode", list.shortLinkCode);
  const keyId = list.shortLinkCode.split("/")[1];
  if (keyId === undefined) {
    console.error("Short link code is undefined");
    return;
  }

  await updateKV("list", keyId, list);
}

export async function removePublicList(id: string): Promise<void> {
  const key = `list-${id}`;
  const url = `${CF_API_LIST_NAMESPACE}/values/${key}`;
  const response = await fetch(url, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${CF_API_TOKEN}`,
      "Content-Type": "text/plain",
    },
  });

  if (response.status !== 200) {
    throw new Error(`Failed to remove value for key ${key}`);
  }
}
