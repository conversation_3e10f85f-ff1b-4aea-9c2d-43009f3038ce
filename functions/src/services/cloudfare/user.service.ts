import { CF_API_TOKEN, CF_API_USER_NAMESPACE } from "./cloudflare.data";
import { removePublicList } from "./list.service";
import { setMevidUidMapper } from "./mevid-uid-mapper.service";
import {
  mapToPublicUserInterface,
  PublicUserInterface,
} from "./models/user.interface";
import { removePublicNote } from "./note.service";
import { getKVData, updateKV } from "./utils";

export async function getPublicUser(id: string): Promise<string> {
  const key = `user-${id}`;
  const url = `${CF_API_USER_NAMESPACE}/values/${key}`;
  const response = await fetch(url, {
    headers: {
      Authorization: `Bearer ${CF_API_TOKEN}`,
    },
  });
  if (response.status !== 200) {
    throw new Error(`Failed to get value for key ${key}`);
  }
  return response.text();
}

export async function setPublicUser(value: Map<string, any>): Promise<void> {
  try {
    const userObject = Object.fromEntries(value);
    const user: PublicUserInterface = mapToPublicUserInterface(userObject);
    console.log("user is ", user);
    if (!user.uid) {
      console.error("User ID is missing, cannot proceed.");
      throw new Error("User ID is missing, cannot proceed with setPublicUser");
    }

    // Fetch existing user data before updating
    const existingUser = await getKVData("user", user.uid);
    console.log("existing user is ", existingUser);
    const oldUser: PublicUserInterface = existingUser
      ? mapToPublicUserInterface(existingUser)
      : ({} as PublicUserInterface);

    // Find items to delete
    const itemsToDelete = findItemsToDelete(oldUser, user);
    console.log("itemsToDelete is ", itemsToDelete);

    console.log("ADDING/UPDATING PUBLIC USER KV with id ", user.uid);
    // **Use common update function**
    const updateSuccess = await updateKV("user", user.uid, user);
    console.log("ADDING/UPDATING PUBLIC USER KV response ", updateSuccess);

    // If the update failed, throw an error immediately
    if (!updateSuccess) {
      throw new Error(`Failed to update user KV for uid: ${user.uid}`);
    }

    // **Additional logic if mevolveId changed**
    if (oldUser.mevolveId !== user.mevolveId) {
      console.log("MEVOLVE ID CHANGED, updating mevid-uid mapper");
      const response = await setMevidUidMapper(user.mevolveId, user.uid);
      console.log("MEVOLVE ID UPDATED KV response ", response);
    }

    // **Perform deletions if necessary**
    await handleDeletions(itemsToDelete);
    console.log("Public user updated successfully:", user.uid);
  } catch (error) {
    console.error("Error in setPublicUser:", error);
    throw error;
  }
}

export async function deletePublicUser(id: string): Promise<void> {
  console.log("Deleting public user with id ", id);
  const key = `user-${id}`;
  const url = `${CF_API_USER_NAMESPACE}/values/${key}`;
  await fetch(url, {
    method: "DELETE",
    headers: {
      Authorization: `Bearer ${CF_API_TOKEN}`,
    },
  });
}

interface ItemsToDelete {
  noteId?: string;
  listId?: string;
}

function findItemsToDelete(
  oldUser: PublicUserInterface,
  newUser: PublicUserInterface
): ItemsToDelete {
  const itemsToDelete: ItemsToDelete = {};

  // Check for deleted lists
  if (oldUser.lists?.length > (newUser.lists?.length || 0)) {
    const deletedList = oldUser.lists?.find(
      (oldList) => !newUser.lists?.some((newList) => newList.id === oldList.id)
    );
    if (deletedList) {
      itemsToDelete.listId = deletedList.id;
    }
  }

  // Check for deleted notes
  if (oldUser.notes?.length > (newUser.notes?.length || 0)) {
    const deletedNote = oldUser.notes?.find(
      (oldNote) => !newUser.notes?.some((newNote) => newNote.id === oldNote.id)
    );
    if (deletedNote) {
      itemsToDelete.noteId = deletedNote.id;
    }
  }

  return itemsToDelete;
}

async function handleDeletions(itemsToDelete: ItemsToDelete): Promise<void> {
  const deletionPromises: Promise<void>[] = [];

  if (itemsToDelete.noteId) {
    console.log("Removing note with id ", itemsToDelete.noteId);
    deletionPromises.push(
      removePublicNote(itemsToDelete.noteId).catch((error) => {
        console.error("Error removing note:", error);
        throw error;
      })
    );
  }

  if (itemsToDelete.listId) {
    console.log("Removing list with id ", itemsToDelete.listId);
    deletionPromises.push(
      removePublicList(itemsToDelete.listId).catch((error) => {
        console.error("Error removing list:", error);
        throw error;
      })
    );
  }

  // Wait for all deletions to complete
  if (deletionPromises.length > 0) {
    try {
      const response = await Promise.all(deletionPromises);
      console.log("All deletions completed successfully:", response);
    } catch (error) {
      console.error("Error during deletions:", error);
      throw new Error("Failed to complete all deletions");
    }
  }
}
