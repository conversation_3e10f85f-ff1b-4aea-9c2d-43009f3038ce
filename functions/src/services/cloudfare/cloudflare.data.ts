import { getMeEnvironment } from "../../me_config";

const env = getMeEnvironment();

export const CF_USER_NAMESPACE_ID =
  env === "dev"
    ? "4d8385fbc7c54da1891705f68ae0da15"
    : env === "staging"
      ? "4e90bb6034594031b3155553bc56804b"
      : env === "prod"
        ? "fecd13a3f73c45b19725c6948d0b814f"
        : env === "hotfix"
          ? "ddb53c1c146f447aa0373fcdda50af91"
          : "d10c7afb99344f57ac9e6b173f3d2c25"; // qa;
export const CF_NOTE_NAMESPACE_ID =
  env === "dev"
    ? "2d0600135ff14abfa1feca0c9be3a1fc"
    : env === "staging"
      ? "fd62fe6cc939416b946667e9526852c2"
      : env == "prod"
        ? "1ddeca5a1ff74a71a481ccb9a85c05c1"
        : env === "hotfix"
          ? "b4150af9a04d4217b86bf45dd932a259"
          : "9aee349962c34ec18fd422b03df5786d"; // qa;
export const CF_LIST_NAMESPACE_ID =
  env === "dev"
    ? "131468c2fced4898afa968b1c7d30197"
    : env === "staging"
      ? "34e4527a48b340a9824e6fd0f2ba4703"
      : env === "prod"
        ? "95ef851538844e6d9913b5a5fdcf8b44"
        : env === "hotfix"
          ? "d46a1e82f68b43dbb12d2400377b351b"
          : "86fbd2582d3043098d24447de0ec6f9e"; // qa;

export const CF_MEVID_UID_MAPER_NAMESPACE_ID =
  env === "dev"
    ? "fc5c48a5b5214575b414a7177e23df6a"
    : env === "staging"
      ? "1df0759b689a4735a886ebc307c06814"
      : env === "prod"
        ? "f2db3b66c0964065aa6f3402018c7083"
        : env === "hotfix"
          ? "2fe5c710a26e49e28e3c17548729bffa"
          : "98a7253192994518b50a4994c1f982f2"; // qa;

export const CF_ACCOUNT_ID = process.env.CLOUDFLARE_ACCOUNT_ID;
export const CF_API_TOKEN = process.env.CLOUDFLARE_KV_RW_TOKEN;
export const CF_API_BASE = `https://api.cloudflare.com/client/v4/accounts/${CF_ACCOUNT_ID}/storage/kv/namespaces`;
export const CF_API_USER_NAMESPACE = `${CF_API_BASE}/${CF_USER_NAMESPACE_ID}`;
export const CF_API_NOTE_NAMESPACE = `${CF_API_BASE}/${CF_NOTE_NAMESPACE_ID}`;
export const CF_API_LIST_NAMESPACE = `${CF_API_BASE}/${CF_LIST_NAMESPACE_ID}`;
export const CF_API_MEVID_UID_MAP_NAMESPACE = `${CF_API_BASE}/${CF_MEVID_UID_MAPER_NAMESPACE_ID}`;

export const CF_R2_ENDPOINT = `https://${CF_ACCOUNT_ID}.r2.cloudflarestorage.com`;
export const CF_R2_BUCKET_TOKEN = process.env.CLOUDFLARE_R2_TOKEN;
export const CF_R2_BUCKET_ACCESS_KEY = process.env.CLOUDFLARE_R2_ACCESS_KEY;
export const CF_R2_BUCKET_SECRET_KEY = process.env.CLOUDFLARE_R2_SECRET_KEY;

// FOR LOCAL TESTING Comment the above and uncomment the below - In case of using firebase emulators
// export const CF_ACCOUNT_ID = "cb90d5ab72a9339876034d4ab1ca1c6e";
// export const CF_USER_NAMESPACE_ID = "4c552861e8dd446f8ea0a906ce94c615";
// export const CF_NOTE_NAMESPACE_ID = "cb25a0f874e647ca8ba66183212a2564";
// export const CF_LIST_NAMESPACE_ID = "087213ef98e94499947800095f9a5698";
// export const CF_MEVID_UID_MAPER_NAMESPACE_ID = "c4ec4ce0295d4ce9810ba4aa1f56f403";
// export const CF_API_TOKEN = "STy3V1SwoKDKvi3u3NfUckXu3KRx7Hw6neg_uD_-";
// export const CF_API_BASE = `https://api.cloudflare.com/client/v4/accounts/${CF_ACCOUNT_ID}/storage/kv/namespaces`;
// export const CF_API_USER_NAMESPACE = `${CF_API_BASE}/${CF_USER_NAMESPACE_ID}`;
// export const CF_API_NOTE_NAMESPACE = `${CF_API_BASE}/${CF_NOTE_NAMESPACE_ID}`;
// export const CF_API_LIST_NAMESPACE = `${CF_API_BASE}/${CF_LIST_NAMESPACE_ID}`;
// export const CF_API_MEVID_UID_MAP_NAMESPACE = `${CF_API_BASE}/${CF_MEVID_UID_MAPER_NAMESPACE_ID}`;
// export const CF_R2_ENDPOINT = "https://cb90d5ab72a9339876034d4ab1ca1c6e.r2.cloudflarestorage.com";
// export const CF_R2_BUCKET_TOKEN = "YTUmCBBaJ2Dt_4qNs0-VX0xenm9gn2djJSTB5MsX";
// export const CF_R2_BUCKET_ACCESS_KEY = "8195495a5f7e578b599c8663fb3ef8c3";
// export const CF_R2_BUCKET_SECRET_KEY =
//   "1349b90e6917b4b56ba5d442d21e4e2cae633cd83e1b03348b19dd562ef5b5be";
