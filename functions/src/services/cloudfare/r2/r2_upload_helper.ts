import {
  S3Client,
  PutObjectCommand,
  DeleteObjectCommand,
} from "@aws-sdk/client-s3";

import { getSignedUrl } from "@aws-sdk/s3-request-presigner";

import {
  CF_ACCOUNT_ID,
  CF_R2_BUCKET_ACCESS_KEY,
  CF_R2_BUCKET_SECRET_KEY,
  CF_R2_ENDPOINT,
} from "../cloudflare.data";
import { lookup } from "mime-types";

const s3 = new S3Client({
  endpoint: CF_R2_ENDPOINT,
  region: "auto",
  credentials: {
    accessKeyId: CF_R2_BUCKET_ACCESS_KEY || "",
    secretAccessKey: CF_R2_BUCKET_SECRET_KEY || "",
    accountId: CF_ACCOUNT_ID,
  },
});

// Function to Generate a signed URL for a PUT request to upload a file to R2.
// in public-attachments bucket
// Ref: https://developers.cloudflare.com/r2/api/s3/presigned-urls/
// Ref: https://developers.cloudflare.com/r2/examples/aws/aws-sdk-js-v3/#generate-presigned-urls
export async function getR2SignedURLForPath(uploadPath: string): Promise<{
  url: string;
  method: string;
  filePath: string;
  expiresIn: number;
  headers: Record<string, string>;
}> {
  try {
    const expiresIn = 1800; // 30 minutes
    const contentType = lookup(uploadPath) || "application/octet-stream";

    const command = new PutObjectCommand({
      Bucket: "public-attachments",
      Key: uploadPath,
      ContentType: contentType,
      ContentDisposition: "inline",
    });

    const presignedUrl = await getSignedUrl(s3, command, {
      expiresIn: expiresIn,
    });

    return {
      url: presignedUrl,
      method: "PUT",
      filePath: uploadPath,
      expiresIn: expiresIn,
      headers: {
        "Content-Type": contentType,
        "Content-Disposition": "inline",
      },
    };
  } catch (error) {
    console.error("Error generating R2 credentials:", error);
    throw new Error("Failed to generate R2 credentials.");
  }
}

// upload
export async function uploadFileToR2(
  fileName: string,
  body: Buffer
): Promise<void> {
  console.log("Uploading file to R2 at path", fileName);
  const contentType = lookup(fileName) || "application/octet-stream";
  console.log(
    "BUKCET CONFIG IS ",
    CF_R2_ENDPOINT,
    CF_R2_BUCKET_ACCESS_KEY,
    CF_R2_BUCKET_SECRET_KEY
  );
  const params = {
    Bucket: "public-attachments",
    Key: fileName,
    Body: body,
    ContentType: contentType,
    ContentDisposition: "inline",
  };
  try {
    await s3.send(new PutObjectCommand(params));
    console.log("File uploaded to R2");
  } catch (err) {
    console.log("Error uploading file to R2", err);
  }
}

// delete
export async function deleteFileFromR2(fileName: string): Promise<void> {
  console.log("Deleting file from R2 at path", fileName);
  console.log(
    "BUKCET CONFIG IS ",
    CF_R2_ENDPOINT,
    CF_R2_BUCKET_ACCESS_KEY,
    CF_R2_BUCKET_SECRET_KEY
  );
  const params = {
    Bucket: "public-attachments",
    Key: fileName,
  };
  try {
    await s3.send(new DeleteObjectCommand(params));
    console.log("File deleted from R2");
  } catch (err) {
    console.log("Error deleting file from R2 at path", fileName, err);
  }
}
