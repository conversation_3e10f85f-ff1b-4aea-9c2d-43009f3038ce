import * as functions from "firebase-functions/v2";
import { getR2SignedURLForPath } from "./r2_upload_helper";

export const getR2SignedURLForUserAttachment = functions.https.onCall(
  { timeoutSeconds: 60 },
  async (request) => {
    console.log("Generating R2 credentials for user upload");
    const userId = request.data.uid;
    const uploadPath = request.data.uploadPath;

    if (!userId) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "User ID is required."
      );
    }

    if (!uploadPath) {
      throw new functions.https.HttpsError(
        "invalid-argument",
        "Upload path is required."
      );
    }

    // Validate that the path starts with the required base path
    const requiredBasePath = `userData/attachments/${userId}/`;
    if (!uploadPath.startsWith(requiredBasePath)) {
      throw new functions.https.HttpsError(
        "permission-denied",
        `Path must start with '${requiredBasePath}' for security reasons.`
      );
    }

    try {
      const signedUrlData = await getR2SignedURLForPath(uploadPath);
      return signedUrlData;
    } catch (error) {
      console.error("Error generating R2 credentials:", error);
      throw new functions.https.HttpsError(
        "internal",
        "Failed to generate R2 credentials."
      );
    }
  }
);
