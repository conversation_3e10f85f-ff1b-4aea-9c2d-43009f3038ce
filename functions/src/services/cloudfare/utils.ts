import {
  CF_API_LIST_NAMESPACE,
  CF_API_NOTE_NAMESPACE,
  CF_API_TOKEN,
  CF_API_USER_NAMESPACE,
} from "./cloudflare.data";
import isEqual from "lodash/isEqual";
import cloneDeep from "lodash/cloneDeep";

interface KVNamespaceConfig {
  namespace: string;
  keyPrefix: string;
}

const maxRetries = 3;
const retryDelay = 2000; // 2 second delay
// Config for different KV namespaces
const KV_CONFIGS: Record<string, KVNamespaceConfig> = {
  list: { namespace: CF_API_LIST_NAMESPACE, keyPrefix: "list-" },
  note: { namespace: CF_API_NOTE_NAMESPACE, keyPrefix: "note-" },
  user: { namespace: CF_API_USER_NAMESPACE, keyPrefix: "user-" },
};

// Common function for Kv Updates
export async function updateKV(
  type: "list" | "note" | "user",
  id: string,
  newData: Partial<any>
): Promise<boolean> {
  const config = KV_CONFIGS[type];
  if (!config) {
    console.error(`Invalid type: ${type}`);
    return false;
  }

  const key = `${config.keyPrefix}${id}`;
  console.log(`Updating KV for ${type} with key: ${key}`);

  // Fetch existing KV value
  const existingData = await getKVData(type, id);

  // Merge old data with new update
  const updatedData = { ...existingData, ...newData };
  console.log("Existing Data:", existingData);
  console.log("New Update:", newData);
  console.log("Merged Data:", updatedData);

  // Skip update if nothing changed
  const ignoredPaths = [
    "createdAt",
    "updatedAt",
    "permaDeletedAt",
    "deletedAt",
    "attachmentsUrl[].createdAt",
    "notes[].createdAt",
    "notes[].updatedAt",
    "lists[].createdAt",
    "lists[].updatedAt",
  ];
  const cleaned1 = deepOmitWithPaths(existingData, ignoredPaths);
  const cleaned2 = deepOmitWithPaths(updatedData, ignoredPaths);

  if (isEqual(cleaned1, cleaned2)) {
    console.log(`No changes detected for ${key}, skipping KV update.`);
    // Return true since this is not a failure case, just no changes needed
    return true;
  }

  // Write updated data to KV
  const url = `${config.namespace}/values/${key}`;
  console.log(`WRITING TO KV FOR TYPE ${type} WITH KEY: ${key}`);

  return await updateWithRetry(url, key, updatedData);
}

async function updateWithRetry(
  url: string,
  key: string,
  updatedData: any
): Promise<boolean> {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    const response = await fetch(url, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${CF_API_TOKEN}`,
        "Content-Type": "text/plain",
      },
      body: JSON.stringify(updatedData),
    });

    if (response.status === 200) {
      console.log(`Successfully updated ${key} on attempt ${attempt}`);
      return true;
    } else {
      console.error(
        `Attempt ${attempt} failed for ${key}, status: ${response.status}`
      );

      if (attempt < maxRetries) {
        console.log(`Retrying in ${retryDelay}ms...`);
        await new Promise((resolve) => setTimeout(resolve, retryDelay));
      } else {
        console.error(`Failed to update ${key} after ${maxRetries} attempts`);
        return false;
      }
    }
  }

  // This line should never be reached due to the return in the final else block,
  // but TypeScript may require it for compile-time checking
  return false;
}

// ✅ Common function for fetching KV values
export async function getKVData(
  type: "list" | "note" | "user",
  id: string
): Promise<Partial<any> | null> {
  const config = KV_CONFIGS[type];
  if (!config) return null;

  console.log("getting KV data for", type, id);
  const key = `${config.keyPrefix}${id}`;
  const url = `${config.namespace}/values/${key}`;

  try {
    const response = await fetch(url, {
      headers: { Authorization: `Bearer ${CF_API_TOKEN}` },
    });

    if (response.status === 200) {
      const responseText = await response.text(); // Read response once
      console.log("KV data fetched successfully", responseText);
      return JSON.parse(responseText);
    } else if (response.status === 404) {
      console.log("KV data not found for", key);
      return null;
    } else {
      throw new Error(`Failed to get value for key ${key}`);
    }
  } catch (error) {
    console.error(`Error fetching ${type} KV:`, error);
    return null;
  }
}

function deepOmitWithPaths(obj: any, pathsToIgnore: string[]): any {
  if (!obj) return obj;
  const clone = cloneDeep(obj);
  const visited = new WeakSet();

  function omitRecursively(current: any, currentPath: string[] = []) {
    if (!current || typeof current !== "object") return;
    if (visited.has(current)) return; // Prevent circular reference issues
    visited.add(current);
    
    if (Array.isArray(current)) {
      current.forEach((item) => {
        omitRecursively(item, [...currentPath, "[]"]);
      });
    } else if (typeof current === "object" && current !== null) {
      Object.keys(current).forEach((key) => {
        const nextPath = [...currentPath, key];
        const pathStr = nextPath.map((p) => (p === "[]" ? "[]" : p)).join(".");

        const shouldOmit = pathsToIgnore.some(
          (p) => p === key || p === pathStr
        );

        if (shouldOmit) {
          delete current[key];
        } else {
          omitRecursively(current[key], nextPath);
        }
      });
    }
  }

  omitRecursively(clone);
  return clone;
}
