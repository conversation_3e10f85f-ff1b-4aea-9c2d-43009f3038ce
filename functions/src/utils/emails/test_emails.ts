import { sendSingleEmailViaSes } from "./aws_ses_email";
import { MevolveEmails } from "./email_helper";
import { getAppLink, getSubscriptionPageLink, getSupportLink } from "../utility_methods";
import moment from "moment";

export class TestEmails {
    testEmail = "<EMAIL>";
    displayName = "Test User";
    language = "english";

    async sendAccountDeletedMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.accountDeleted,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
            },
            language: this.language,
        });
    }

    async sendAutoRenewalAlertMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.autoRenewalAlert,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                daysToExpire: 5,
                subsType: "Monthly",
                expiryDate: moment(new Date()).format("Do MMM YYYY"), // "19th March, 2023",
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendCollaborationInviteForNoteMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.collaborationInviteForNote,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                collabName: "Admin name",
                listTitle: "Sample list",
                inviteLink: "https://mevolve.app/",
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendCollaborationInviteOfListMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.collaborationInviteOfList,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                collabName: "Admin name",
                listTitle: "Sample list",
                listDescription: "Sample desicription",
                inviteLink: "https://mevolve.app/",
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendDataImportFailedMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.dataImportFailed,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendDataImportSuccessfullMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.dataImportSuccessfull,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendDownloadBackupDataMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.downloadBackupData,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                dateOfBackup: moment(new Date()).format("lll"),
                linkExpireDate: moment(new Date()).format("LL"),
                downloadDataLink: "https://mevolve.app/",
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendLoginAndSignupVerificationMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.loginAndSignupVerification,
            recipient: this.testEmail,
            templateData: {
                otp: "1234",
            },
            language: this.language,
        });
    }

    async sendNotSubscribedMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.notSubscribed,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                subscriptionLink: getSubscriptionPageLink(),
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendSubscriptionDowngradedMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.subscriptionDowngraded,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                oldSubsType: "Pro Yearly",
                endDate: moment(new Date()).format("Do MMMM YYYY"),
                newSubsType: "Basic Monthly",
                startDate: moment(new Date()).format("Do MMMM YYYY"),
                appLink: getAppLink(),
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendSubscriptionExpiredMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.subscriptionExpired,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                subscriptionLink: getSubscriptionPageLink(),
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendSubscriptionExpiringAlertMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.subscriptionExpiringAlert,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                daysLeft: 5,
                subscriptionLink: getSubscriptionPageLink(),
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendSubscriptionUpdatdMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.subscriptionUpdated,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                shortPlanName: "basic",
                fullPlanName: "Basic Monthly",
                startDate: moment(new Date()).format("Do MMMM YYYY"),
                appLink: getAppLink(),
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendUpgradeSuccessfulMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.upgradeSuccessful,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                shortPlanName: "basic",
                fullPlanName: "Basic Monthly",
                startDate: moment(new Date()).format("Do MMMM YYYY"),
                appLink: getAppLink(),
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendUserCancelsSubscriptionFromBasicPlanMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.userCancelsSubscriptionFromBasicPlan,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                plan: "Basic Monthly",
                subscriptionLink: getSubscriptionPageLink(),
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendUserCancelsSubscriptionFromPremiumPlansMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.userCancelsSubscriptionFromPremiumPlans,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                plan: "Basic Monthly",
                subscriptionLink: getSubscriptionPageLink(),
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }

    async sendWelcomeUserMail() {
        await sendSingleEmailViaSes({
            templateName: MevolveEmails.welcomeUser,
            recipient: this.testEmail,
            templateData: {
                name: this.displayName,
                appLink: getAppLink(),
                linkToSupportChat: getSupportLink(),
            },
            language: this.language,
        });
    }
}