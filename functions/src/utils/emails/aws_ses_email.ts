import {
  SendEmailCommand,
  SendTemplatedEmailCommand,
  SESClient,
} from "@aws-sdk/client-ses";
import { MevolveEmails } from "./email_helper";

const sesClient = new SESClient({
  region: "eu-west-1",
  credentials: {
    accessKeyId: process.env.AWS_SES_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SES_SECRET_ACCESS_KEY || "",
  },
});

// Interface for email template substitution variables
interface TemplateData {
  [key: string]: unknown;
}

// Interface for email sending parameters
interface SendTemplatedEmailParams {
  templateName: MevolveEmails;
  recipient: string | string[];
  templateData: TemplateData;
  language: string | undefined;
}

export async function sendSingleEmailViaSes({
  templateName,
  recipient,
  templateData,
  language,
}: SendTemplatedEmailParams) {
  try {
    // Normalize recipients to an array
    const recipients = Array.isArray(recipient) ? recipient : [recipient];
    const languageCode = getLanguageCodeFromString(language ?? "english");
    const templateNameWithLang = `me-daily-${MevolveEmails[templateName]}-${languageCode}`;
    console.log("templateNameWithLang: ", templateNameWithLang);
    // Prepare the email send command
    const command = new SendTemplatedEmailCommand({
      Source:
        process.env.MEVOLVE_SENDER_EMAIL ?? "'Mevolve' <<EMAIL>>",
      Destination: {
        ToAddresses: recipients,
      },
      Template: templateNameWithLang,
      TemplateData: JSON.stringify(templateData),
    });

    // Send the email
    const response = await sesClient.send(command);
    console.log(
      `Email sent successfully using template ${MevolveEmails[templateName]}:`
    );
    return response;
  } catch (error) {
    console.error("Error sending templated email:", error);
    throw error;
  }
}

export type EmailInfo = {
  to: string;
  data: TemplateData;
  language: string | undefined;
};

export async function sendBulkEmail(
  templateId: MevolveEmails,
  emailList: EmailInfo[]
) {
  const tasks = emailList.map((item) => {
    return sendSingleEmailViaSes({
      templateName: templateId,
      language: item.language,
      recipient: item.to,
      templateData: item.data,
    });
  });
  return Promise.all(tasks);
}

function getLanguageCodeFromString(language: string): string {
  switch (language) {
    case "english": {
      return "en";
    }
    case "french": {
      return "fr";
    }
    case "german": {
      return "de";
    }
    case "italian": {
      return "it";
    }
    case "portuguese": {
      return "pt";
    }
    case "spanish": {
      return "es";
    }
  }
  return "en";
}

export async function sendSingleEmailWithHtmlBody(
  to: string[],
  subject: string,
  htmlBody: string
) {
  try {
    const command = new SendEmailCommand({
      Source:
        process.env.MEVOLVE_SENDER_EMAIL ?? "'Mevolve' <<EMAIL>>",
      Destination: {
        ToAddresses: to,
      },
      Message: {
        Subject: {
          Data: subject,
          Charset: "UTF-8",
        },
        Body: {
          Html: {
            Data: htmlBody,
            Charset: "UTF-8",
          },
        },
      },
    });

    const response = await sesClient.send(command);
    console.log("Email sent successfully:", response);
    return response;
  } catch (error) {
    console.error("Error sending email:", error);
    throw error;
  }
}
