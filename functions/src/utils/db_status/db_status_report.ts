import * as admin from "firebase-admin";
import * as functions from "firebase-functions/v2";
import { getMeEnvironment } from "../../me_config";
import { ZodObject, ZodTypeAny } from "zod";
import {
  currentDbVersion,
  ZUser,
  ZUserMetadata,
  ZTodo,
  ZHabitSetup,
  ZHabitAction,
  ZJournalSetup,
  ZJournalAction,
  ZNote,
  ZLists,
  ZChatMessages,
  ZChatUser,
  ZIssueReport,
} from "../../functions/migration/versions/base/model_mappings";
import { sendSingleEmailWithHtmlBody } from "../emails/aws_ses_email";

exports.getReport = functions.https.onRequest(async (_req, res) => {
  console.log("fetching current db status");
  res.send(await sendDbStatusReport(false));
});

export async function sendDbStatusReport(sendMail: boolean) {
  const db = admin.firestore();
  const result = await Promise.all([
    db.collection("users").count().get(),
    db
      .collection("users")
      .where("docVer", "==", currentDbVersion)
      .count()
      .get(),
  ]);
  const totalUsers = result[0].data().count;
  const migratedSuccessfully = result[1].data().count;
  const dbStatus: DbStatus = {
    totalUsers: totalUsers,
    success: migratedSuccessfully,
    failures: totalUsers - migratedSuccessfully,
    currentDbVersion: currentDbVersion,
  };
  console.log("Migration results: " + JSON.stringify(dbStatus));
  if (sendMail) {
    await sendMigrationResults(dbStatus);
  }
  return dbStatus;
}

async function sendMigrationResults(result: DbStatus) {
  if (process.env.FUNCTIONS_EMULATOR) return;
  await sendSingleEmailWithHtmlBody(
    ["<EMAIL>"],
    "DB Migration Results - " + getMeEnvironment(),
    `
      <!DOCTYPE html>
      <html>
      <head>
      <style>
      table {
        font-family: arial, sans-serif;
        border-collapse: collapse;
        width: 100%;
      }

      td, th {
        border: 1px solid #dddddd;
        text-align: left;
        padding: 8px;
      }

      tr:nth-child(even) {
        background-color: #dddddd;
      }
      </style>
      </head>
      <body>
      <h1>Migration results:</h1>
      <h3>Environment: ${getMeEnvironment()}</h3>
      <table>
        <tr>
            <th>Total Users</th>
            <th>Success</th>
            <th>Failures</th>
        </tr>
        <tr>
            <td>${result.totalUsers}</td>
            <td>${result.success}</td>
            <td>${result.failures}</td>
        </tr>
      </table>
      </body>
      </html>
          `
  );
}

type DbStatus = {
  totalUsers: number;
  success: number;
  failures: number;
  currentDbVersion: number;
};
export const getSchema = functions.https.onCall(async (_request) => {
  console.log("fetching current db status");
  return {
    statusCode: 200,
    schema: getSchemaMap(),
    currentDbVersion: currentDbVersion,
  };
});

function getSchemaMap() {
  // Print the shape of the mock user object
  console.log("Shape of the User object:");
  return {
    users: printZodSchema(ZUser),
    userMetadata: printZodSchema(ZUserMetadata),
    todo: printZodSchema(ZTodo),
    habitSetup: printZodSchema(ZHabitSetup),
    habitAction: printZodSchema(ZHabitAction),
    journalSetup: printZodSchema(ZJournalSetup),
    journalAction: printZodSchema(ZJournalAction),
    note: printZodSchema(ZNote),
    lists: printZodSchema(ZLists),
    chatMessages: printZodSchema(ZChatMessages),
    chatUser: printZodSchema(ZChatUser),
    issueReport: printZodSchema(ZIssueReport),
  };
}

// Function to print the schema of a Zod object with nested Zod objects
function printZodSchema<T extends ZodTypeAny>(
  schema: ZodObject<any, any>,
  indent = ""
): T["_output"] {
  const shape = schema.shape;
  const result: any = {};
  for (const key in shape) {
    if (Object.prototype.hasOwnProperty.call(shape, key)) {
      const fieldSchema = shape[key as keyof typeof shape];

      if (fieldSchema instanceof ZodObject) {
        console.log(`if ${indent}${key}: ${fieldSchema._def.typeName}`);
        result[key] = printZodSchema(
          fieldSchema as ZodObject<any, any>,
          indent + "    "
        );
      } else {
        console.log(`else ${indent}${key}: ${fieldSchema._def.typeName}`);
        result[key] = fieldSchema._def.typeName;
      }
    }
  }
  return result as T["_output"];
}

export function getTranslationReportBody(
  data: string,
  varData: string,
  count: string,
  varCount: string
): string {
  // Always create the basic HTML structure
  let body = `
    <!DOCTYPE html>
    <html>
    <head>
    <style>
    table {
      font-family: arial, sans-serif;
      border-collapse: collapse;
      width: 100%;
    }

    td, th {
      border: 1px solid #dddddd;
      text-align: left;
      padding: 8px;
    }

    tr:nth-child(even) {
      background-color: #dddddd;
    }
    </style>
    </head>
    <body>
    <h1>Translation Report:</h1>`;

  // MeString instances section
  body += `<h3>Missing translations: ${count}</h3>`;
  if (data.length > 0) {
    body += `<table>
      <tr>
        <th>Path</th>
        <th>Line #</th>
        <th>Text</th>
      </tr>`;
    const lines = data.split("((*))");
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].length > 0) {
        const path = (lines[i].match(".+?(?=:[0-9]+:)")?.[0] ?? "").replace(
          "/home/<USER>/work/mevolve/",
          ""
        );
        const lineNo = lines[i].match(":[0-9]+:")?.[0].replaceAll(":", "");
        const text = lines[i].match("(?<=:[0-9]+:).+")?.[0];
        body += ` <tr>
        <td>${path}</td>
        <td>${lineNo}</td>
        <td>${text}</td>
        </tr>
      `;
      }
    }
    body += "</table>";
  } else {
    body += "<p>No MeString instances with string literals found.</p>";
  }

  // fromVariable instances section
  body += `<br><br>
  <h3>Variable translations: ${varCount}</h3>`;
  if (varData.length > 0) {
    body += `<table>
      <tr>
        <th>Path</th>
        <th>Line #</th>
        <th>Text</th>
      </tr>`;
    const varLines = varData.split("((*))");
    for (let i = 0; i < varLines.length; i++) {
      if (varLines[i].length > 0) {
        const path = (varLines[i].match(".+?(?=:[0-9]+:)")?.[0] ?? "").replace(
          "/home/<USER>/work/mevolve/",
          ""
        );
        const lineNo = varLines[i].match(":[0-9]+:")?.[0].replaceAll(":", "");
        const text = varLines[i].match("(?<=:[0-9]+:).+")?.[0];
        body += ` <tr>
        <td>${path}</td>
        <td>${lineNo}</td>
        <td>${text}</td>
        </tr>
      `;
      }
    }
    body += "</table>";
  } else {
    body += "<p>No MeString.fromVariable instances found.</p>";
  }

  // Summary section
  body += `<br><br>
  <h3>Summary</h3>
  <ul>
    <li>Total MeString instances: ${count}</li>
    <li>Total MeString.fromVariable instances: ${varCount}</li>
    <li>Total translation strings: ${parseInt(count || "0") + parseInt(varCount || "0")}</li>
  </ul>`;

  // Add timestamp for reference
  const timestamp = new Date().toISOString();
  body += `<p><em>Report generated at: ${timestamp}</em></p>`;

  body += `
    </body>
    </html>
  `;

  return body;
}
