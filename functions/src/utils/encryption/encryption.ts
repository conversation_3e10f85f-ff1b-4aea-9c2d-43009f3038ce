import * as crypto from "crypto";
import * as eciesjs from "eciesjs";
import * as functions from "firebase-functions/v2";
import { promises as fs } from "fs";
import { getMeEnvironment } from "../../me_config";
import { getApiSecret } from "../utility_methods";
import * as firebaseAdmin from "firebase-admin";
import { generateKeyPairSync, KeyPairSyncResult } from "crypto";
import { IUserKeys } from "../../functions/migration/versions/base/model_mappings";
import { MeLogger } from "../logger/models/me_logger";

export const getKeyForUser = functions.https.onCall(
  { region: "europe-west1", memory: "512MiB" },
  async (request) => {
    const sessionId = request.data.sessionId;
    const logger = new MeLogger(request.auth?.uid, sessionId);
    logger.log("fetching key for user: " + request.data.email);
    return getSecretFromKmsForUserByEmail(request.data.email);
  }
);

export async function getSecretFromKmsForUserByEmail(email: string) {
  console.log("fetching key for user: " + email);

  // For local development/emulator, return a mock key
  if (process.env.USE_LOCAL_KMS === "true") {
    console.log("Using local mock KMS key for development");
    return { key: "JdZjVkxC4aIjkv6m4tD4baK6tJsFnmAEUlGsCbno/Rk=" };
  }

  let salt = "";
  if (email.length >= 6) {
    salt = email.substring(0, 6);
  } else {
    salt = email + "123456".substring(0, 6 - email.length);
  }

  const payload = {
    uid: salt + email,
    apiSecret: getApiSecret(),
  };
  // return "tesPGz04JWs87OPR6m8p1NWXwHsrnSjL";
  const url = `https://europe-west1-mevolve-kms.cloudfunctions.net/getSecret${getMeEnvironment()}`;
  const options = {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(payload),
  };
  const response = await fetch(url, options);
  console.log(response.status);
  if (response.status == 200) {
    return await response.json();
  } else if (response.status == 401) {
    console.log("Error fetching key");
    throw new Error("Authorization error");
  } else {
    console.log("Error fetching key");
    throw new Error("Error fetching key");
  }
}

export async function getUserEncryptedDocKeyForShare(
  ownerUserId: string,
  newUserId: string,
  dek: string
) {
  const docKey = await decryptDocKey(dek, ownerUserId);
  return encryptDocKey(docKey.substring(7), newUserId);
}

export function pemToHex(pem: string, isPublicKey: boolean): string {
  if (isPublicKey) {
    // Remove headers, footers, and newlines to get the base64-encoded part.
    const base64Content = pem
      .replace(/-----BEGIN PUBLIC KEY-----/, "")
      .replace(/-----END PUBLIC KEY-----/, "")
      .replace(/\n/g, "");

    // Base64 decode the cleaned PEM content
    const derBuffer = Buffer.from(base64Content, "base64");

    // Extract the public key bytes from the DER buffer.
    // Based on the DER structure: Public key bytes start after Algorithm Identifier.
    // Look for the sequence '03 42 00 04', then extract the key bytes following this pattern.

    const derHex = derBuffer.toString("hex");

    // Find the start index of the public key.
    const publicKeyStart = derHex.indexOf("03420004") + 8; // Offset 8 for '03420004'.
    const publicKeyHex = derHex.substring(publicKeyStart);

    return publicKeyHex;
  } else {
    // Remove headers, footers, and newlines to get the base64-encoded part.
    const base64Content = pem
      .replace(/-----BEGIN PRIVATE KEY-----/, "")
      .replace(/-----END PRIVATE KEY-----/, "")
      .replace(/\n/g, "");

    // Base64 decode the cleaned PEM content.
    const derBuffer = Buffer.from(base64Content, "base64");

    // Parse the DER format to extract the private key bytes.
    // PrivateKeyInfo structure: SEQUENCE (3 elem)
    // - version
    // - privateKeyAlgorithm
    // - privateKey (OCTET STRING)

    const derHex = derBuffer.toString("hex");

    // Extract the private key bytes from DER hex string.
    // The private key starts after 'privateKey' OCTET STRING identifier '02 01 01 04 20'.
    const privateKeyStart = derHex.indexOf("0201010420") + 10; // Offset 10 for '0201010420'
    const privateKeyHex = derHex.substring(
      privateKeyStart,
      privateKeyStart + 64
    ); // 32 bytes of private key in hex.

    // // Convert the hex to a Buffer (raw private key bytes)
    // const privateKeyBytes = Buffer.from(privateKeyHex, 'hex');
    return privateKeyHex;
  }
}

export async function encryptDocKey(
  dek: string,
  userId: string,
  userPublicKey?: string | undefined
) {
  if (dek.startsWith("encEcc_")) {
    throw new Error("Dek is already encrypted");
  }
  let userSecret;
  if (userPublicKey) {
    userSecret = userPublicKey;
  } else {
    userSecret = await getPublicKeyOfUserById(userId);
  }
  const data = Buffer.from(dek, "base64");
  const encryptedData = eciesjs.encrypt(pemToHex(userSecret, true), data);
  return "encEcc_" + encryptedData.toString("base64");
}

export async function decryptDocKey(
  encryptedDocKey: string,
  userId: string,
  userPrivateKey?: string | undefined
) {
  if (encryptedDocKey.startsWith("encEcc_")) {
    let decryptedUserPrivateKey;
    if (userPrivateKey) {
      decryptedUserPrivateKey = userPrivateKey;
    } else {
      decryptedUserPrivateKey = await getDecryptedPrivateKeyOfUserById(
        userId,
        null
      );
    }
    if (decryptedUserPrivateKey == null) {
      throw new Error("User private key not found");
    }
    const encryptedBuffer = Buffer.from(encryptedDocKey.substring(7), "base64");
    const decryptedData = eciesjs.decrypt(
      pemToHex(decryptedUserPrivateKey, false),
      encryptedBuffer
    );
    return decryptedData.toString("base64");
  } else {
    throw new Error("Invalid encrypted doc key");
  }
}

// Function to encrypt data using AES-256-CBC and
// return a base64-encoded string
export function encryptTextData(data: string, secret: string): string {
  if (data == null || data == "") {
    throw new Error("Data to encrypt is null or empty");
  }
  if (data.startsWith("encAes_")) {
    throw new Error("Data is already encrypted");
  }
  const iv = crypto.randomBytes(16);
  const cipher = getAESEncryptionCypher(secret, iv);
  const encryptedData = Buffer.concat([
    iv,
    cipher.update(data, "utf-8"),
    cipher.final(),
  ]);
  return "encAes_" + encryptedData.toString("base64");
}

// Function to decrypt a base64-encoded string using AES-256-CBC
export function decryptTextData(encryptedData: string, secret: string): string {
  if (encryptedData == null || encryptedData == "") {
    throw new Error("Data to decrypt is null or empty");
  }
  if (encryptedData.startsWith("encAes_")) {
    const encryptedBuffer = Buffer.from(encryptedData.substring(7), "base64");
    const iv = encryptedBuffer.subarray(0, 16);
    const decipher = getAESDecryptionCypher(secret, iv);
    const decryptedData = Buffer.concat([
      decipher.update(encryptedBuffer.subarray(16)),
      decipher.final(),
    ]);
    return decryptedData.toString("utf-8");
  } else {
    throw new Error("Data is not encrypted - " + encryptedData);
  }
}

export async function getDecryptedPrivateKeyOfUserById(
  id: string,
  secret: string | undefined | null
) {
  const userKey = await getUserKeyDocById(id);
  if (userKey) {
    const encryptedPvtKey = userKey.encPrivateKey;
    const decryptedPrivateKey = await decryptTextData(
      encryptedPvtKey,
      secret ?? (await getSecretFromKmsForUserByEmail(userKey.email)).key
    );
    return decryptedPrivateKey;
  } else {
    return null;
  }
}

export async function getUserKeyDocByEmail(
  email: string
): Promise<IUserKeys | null> {
  const userSnapshot = await firebaseAdmin
    .firestore()
    .collection("userKeys")
    .where("email", "==", email)
    .get();
  if (userSnapshot.docs.length > 0) {
    return userSnapshot.docs[0].data() as IUserKeys;
  } else {
    return null;
  }
}

export async function getUserKeyDocById(
  uid: string
): Promise<IUserKeys | null> {
  const userSnapshot = await firebaseAdmin
    .firestore()
    .collection("userKeys")
    .where("uid", "==", uid)
    .get();
  if (userSnapshot.docs.length > 0) {
    return userSnapshot.docs[0].data() as IUserKeys;
  } else {
    return null;
  }
}

export async function getPublicKeyOfUserById(id: string) {
  const userSnapshot = await firebaseAdmin
    .firestore()
    .collection("userKeys")
    .where("uid", "==", id)
    .get();
  if (userSnapshot.docs.length > 0) {
    const user = userSnapshot.docs[0].data();
    return user.publicKey;
  } else {
    return null;
  }
}

// Function to encrypt a file using AES-128-CBC
export async function encryptFile(
  inputFilePath: string,
  outputFilePath: string,
  secretKey: string
): Promise<string> {
  const fileContent = await fs.readFile(inputFilePath);
  const iv = crypto.randomBytes(16);
  const cipher = getAESEncryptionCypher(secretKey, iv);
  const encryptedData = Buffer.concat([
    iv,
    cipher.update(fileContent),
    cipher.final(),
  ]);
  await fs.writeFile(outputFilePath, encryptedData);
  return outputFilePath;
}

export async function encryptFileByChunk(
  inputPath: string,
  outputPath: string,
  secret: string
): Promise<string> {
  const fileData = await fs.readFile(inputPath);
  const encryptedData = await encryptLargeData(fileData, secret);
  await fs.writeFile(outputPath, encryptedData);
  return outputPath;
}

async function encryptLargeData(
  data: Buffer,
  secret: string,
  chunkSize: number = 64 * 1024
): Promise<Buffer> {
  const iv = crypto.randomBytes(16);
  const cipher = getAESEncryptionCypher(secret, iv);

  const encryptedChunks: Buffer[] = [iv];

  for (let i = 0; i < data.length; i += chunkSize) {
    const chunk = data.subarray(i, i + chunkSize);
    const encryptedChunk = cipher.update(chunk);
    encryptedChunks.push(encryptedChunk);
  }

  encryptedChunks.push(cipher.final());

  return Buffer.concat(encryptedChunks);
}

export async function decryptFile(
  inputFilePath: string,
  outputFilePath: string,
  secretKey: string
): Promise<string> {
  const encryptedBuffer = await fs.readFile(inputFilePath);
  const iv = encryptedBuffer.subarray(0, 16);
  const decipher = getAESDecryptionCypher(secretKey, iv);
  const decryptedData = Buffer.concat([
    decipher.update(encryptedBuffer.subarray(16)),
    decipher.final(),
  ]);
  await fs.writeFile(outputFilePath, decryptedData);
  return outputFilePath;
}

export function getAESEncryptionCypher(key: string, iv: Buffer) {
  return crypto.createCipheriv("aes-256-cbc", Buffer.from(key, "base64"), iv);
}

export function getAESDecryptionCypher(key: string, iv: Buffer) {
  return crypto.createDecipheriv("aes-256-cbc", Buffer.from(key, "base64"), iv);
}

export function getNewSecret() {
  const bytesView = crypto.randomBytes(32);
  return bytesView.toString("base64");
}

interface MapType {
  [key: string]: any;
}

export function iterateMap(
  map: MapType,
  fields: string[],
  index: number,
  secret: string,
  isEncrypting: boolean,
  docId: string,
  docCollection: string
): number {
  let value: any = map;
  for (let i = index; i < fields.length; ) {
    const part = fields[i];
    if (part.includes("[]")) {
      value = value[part.replace("[]", "")] as any[];
      let temp = 0;
      for (let j = 0; j < value.length; j++) {
        temp = iterateMap(
          value[j],
          fields,
          i + 1,
          secret,
          isEncrypting,
          docId,
          docCollection
        );
      }
      i = temp;
    } else if (part.includes("{}")) {
      value = value[part.replace("{}", "")] as MapType;
      let temp = 0;
      const keys = Object.keys(value);
      for (let j = 0; j < keys.length; j++) {
        temp = iterateMap(
          value[keys[j]],
          fields,
          i + 1,
          secret,
          isEncrypting,
          docId,
          docCollection
        );
      }
      i = temp;
    } else if (i < fields.length - 1) {
      i = iterateMap(
        map[part],
        fields,
        i + 1,
        secret,
        isEncrypting,
        docId,
        docCollection
      );
    } else {
      try {
        if (value[part] && value[part] !== null && value[part] !== "") {
          if (isEncrypting) {
            value[part] = encryptTextData(value[part], secret);
          } else {
            value[part] = decryptTextData(value[part], secret);
          }
        }
      } catch (error) {
        console.log(
          `error encrypting/decrypting for ${docCollection}/${docId}/${fields.join(".")}: ${error}`
        );
      }
    }
    return i;
  }
  return -1;
}

// export async function testEcnrtyption() {
//   const db = admin.firestore();
//   const userDocSnapshot = await db.collection("users")
//       .doc("O9aFE56PeLZjvbjTPTdi7CdPBGPz").get();
//   const userDocData = userDocSnapshot.data() as IUser;
//   const todoDocSnapshot = await db.collection("todos")
//       .doc("092930e0-b846-11ee-944a-f3254023ec2f").get();
//   const todoDocData = todoDocSnapshot.data() as ITodo;
//   const userIv = userDocData.encData.iv;
//   const userSecret =
//     await getEncryptionKeyForUser("O9aFE56PeLZjvbjTPTdi7CdPBGPz");
//   const decryptedDocKey =
//     decryptAes128Cbc(todoDocData.encData.dek, userSecret, userIv);
//   console.log("decrypted doc key: " + decryptedDocKey);
//   const title = decryptAes128Cbc(todoDocData.title,
//       decryptedDocKey, todoDocData.encData.iv);
//   console.log("decrypted title: " + title);
// }

// exports.getUserSecret = functions.https.onRequest(async (_req, _res) => {
//   const uid = "O9aFE56PeLZjvbjTPTdi7CdPBGPz";
//   const docSecret = getNewSecret();
//   console.log("doc secret = " + docSecret);
//   const encryptedDocSecret = await encryptDocKey(docSecret, uid);
//   console.log("encrypted doc secret = " + encryptedDocSecret);
//   const decryptedDocKey = await decryptDocKey(encryptedDocSecret, uid);
//   console.log("decrypted doc secret = " + decryptedDocKey);
//   const plainText = "this is a sample text";
//   const encryptedText = encryptAes256Cbc(plainText, decryptedDocKey);
//   console.log("encrypted text = " + encryptedText);
//   const decryptedText = decryptAes256Cbc(encryptedText, decryptedDocKey);
//   console.log("decrypted text = " + decryptedText);
// });

// async function getEncryptionKeyForUser(uid: string) {
//   console.log("fetching key for user: " + uid);
//   return "tesPGz04JWs87OPR6m8p1NWXwHsrnSjL";
// }

export function generateECCKeyPair(): KeyPairSyncResult<string, string> {
  return generateKeyPairSync("ec", {
    namedCurve: "secp256k1", // Curve name for ECC
    publicKeyEncoding: {
      type: "spki", // "Subject Public Key Info"
      format: "pem",
    },
    privateKeyEncoding: {
      type: "pkcs8",
      format: "pem",
    },
  });
}

export function generateRSAKeyPair(): KeyPairSyncResult<string, string> {
  return generateKeyPairSync("rsa", {
    modulusLength: 2048,
    publicKeyEncoding: {
      type: "pkcs1", // "Public Key Cryptography Standards 1"
      format: "pem", // "Privacy-Enhanced Mail"
    },
    privateKeyEncoding: {
      type: "pkcs1",
      format: "pem",
    },
  });
}

export function hashData(data: string, algorithm = "sha256"): string {
  const hash = crypto.createHash(algorithm);
  hash.update(data);
  return hash.digest("hex");
}
