/* eslint-disable require-jsdoc */
import { FieldValue, Timestamp } from "firebase-admin/firestore";
import * as fs from "fs";
import sharp from "sharp";
import { getMeEnvironment } from "../me_config";
import * as admin from "firebase-admin";
import { PubSub } from "@google-cloud/pubsub";
import { functionsRegion } from "..";
import {
  currentDbVersion,
  IViewSettings,
} from "../functions/migration/versions/base/model_mappings";
import { AttachmentConstants } from "../functions/user_uploads/media/media_constants";

export function convertToTimestamp(
  source: Timestamp | Date | null | undefined
) {
  if (source === undefined) {
    return null;
  }
  if (source === null || source instanceof Timestamp) {
    return source;
  } else {
    return Timestamp.fromDate(source);
  }
}

export function getImageMetaData(
  localOriginalFile: string
): Promise<sharp.Metadata> {
  return sharp(localOriginalFile).metadata();
}

export function getFileSize(filename: string): number {
  const stats = fs.statSync(filename);
  const fileSizeInBytes = stats.size;
  return Math.round(fileSizeInBytes / 1024);
}

export function getApiSecret(): string | undefined {
  return process.env.MEVOLVE_REST_SECRET;
}

export function getMeBaseDomain(): string {
  const envName = getMeEnvironment();
  if (envName === "prod") {
    return "web.mevolve.app";
  }
  return `${envName}-mevolve.pages.dev`;
}

export function getMeApiBaseDomain(): string {
  const envName = getMeEnvironment();
  return `${functionsRegion}-mevolve-${envName}.cloudfunctions.net`;
}

export async function getEmailByIdFromAuthService(
  uid: string
): Promise<string | undefined> {
  const authUser = await admin.auth().getUser(uid);
  if (authUser && authUser.email) {
    return authUser.email;
  } else {
    return undefined;
  }
}

export async function getEmailNameByIdFromAuthService(uid: string): Promise<{
  email: string | undefined;
  name: string | undefined;
}> {
  try {
    const authUser = await admin.auth().getUser(uid);
    return { email: authUser.email, name: authUser.displayName };
  } catch (e) {
    console.log("Error in getEmailNameByIdFromAuthService");
    throw e;
  }
}

export async function getNameFromEmail(email: string): Promise<{
  name: string | undefined;
}> {
  try {
    const authUser = await admin.auth().getUserByEmail(email);
    return { name: authUser.displayName };
  } catch (e) {
    console.log("Error in getNameFromEmail");
    return { name: undefined };
  }
}

export async function getUserByEmailFromAuthService(
  email: string
): Promise<admin.auth.UserRecord | undefined> {
  try {
    return await admin.auth().getUserByEmail(email);
  } catch (e) {
    return undefined;
  }
}

export async function getUuidFromHashedEmail(
  hashedEmail: string
): Promise<string | undefined> {
  try {
    const snapshot = await admin
      .firestore()
      .collection("userKeys")
      .where("emailHash", "==", hashedEmail)
      .get();
    if (snapshot.empty) {
      return undefined;
    } else {
      return snapshot.docs[0].data().uid == undefined
        ? undefined
        : snapshot.docs[0].data().uid;
    }
  } catch (e) {
    return undefined;
  }
}

export async function getHashedEmailFromUid(
  uid: string
): Promise<string | undefined> {
  try {
    const snapshot = await admin
      .firestore()
      .collection("userKeys")
      .where("uid", "==", uid)
      .get();
    if (snapshot.empty) {
      return undefined;
    } else {
      return snapshot.docs[0].data().emailHash == undefined
        ? undefined
        : snapshot.docs[0].data().emailHash;
    }
  } catch (e) {
    return undefined;
  }
}

// async function getUserDetailsIfAuthEmailIsNull(
//   authUser: admin.auth.UserRecord
// ): Promise<{
//   email: string | undefined;
//   name: string | undefined;
// }> {
//   if (
//     authUser.email === undefined ||
//     authUser.email === null ||
//     authUser.email === ""
//   ) {
//     const userSnapshot = await admin
//       .firestore()
//       .collection("users")
//       .doc(authUser.uid)
//       .get();
//     const userData = userSnapshot.data() as IUser;
//     if (userData) {
//       userData.userInfo.email;
//       const secret = await decryptDocKey(userData.encData.dek, authUser.uid);
//       return {
//         email: decryptTextData(userData.userInfo.email, secret),
//         name: decryptTextData(userData.userInfo.name, secret),
//       };
//     }
//   }
//   return { email: authUser.email, name: authUser.displayName };
// }

export function getCurrentUTCDate(): Date {
  const d1 = new Date();
  return new Date(
    d1.getUTCFullYear(),
    d1.getUTCMonth(),
    d1.getUTCDate(),
    d1.getUTCHours(),
    d1.getUTCMinutes(),
    d1.getUTCSeconds()
  );
}

export function getAppLink() {
  return `https://${getMeBaseDomain()}`;
}

export function getSupportLink() {
  return `https://${getMeBaseDomain()}/drawerSupportChat`;
}

export function getSubscriptionPageLink() {
  return `https://${getMeBaseDomain()}/drawerSubscription`;
}

export async function getUserDbVersion(userId: string): Promise<number> {
  const db = admin.firestore();
  const docSnapshot = await db.collection("users").doc(userId).get();
  if (docSnapshot.exists) {
    const userData = docSnapshot.data();
    if (userData) {
      return userData.docVer;
    }
  }
  return currentDbVersion;
}

const pubSubClient = new PubSub();
export async function publishMessageToPubSub(
  topicName: string,
  data: object
): Promise<string> {
  return pubSubClient
    .topic(topicName, {
      gaxOpts: {
        timeout: 540000,
      },
    })
    .publishMessage({ data: Buffer.from(JSON.stringify(data)) });
}

export function convertDatesToTimestamps(obj: any): any {
  // Iterate over each key in the object
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key];

      // If the value is a Date object, convert it to a Firestore Timestamp
      if (value instanceof Date) {
        obj[key] = Timestamp.fromDate(value);
      }

      if (key == "cloudUpdatedAt" && obj[key] == null) {
        obj[key] = FieldValue.serverTimestamp();
      }

      // If the value is a String object, convert it to a Firestore Timestamp
      if (key == "localUpdatedAt" && typeof value === "string") {
        obj[key] = Timestamp.fromMillis(Date.parse(value));
      }

      // If the value is an array, iterate over its elements
      else if (Array.isArray(value)) {
        obj[key] = value.map((item) => {
          if (typeof item === "object" && item !== null) {
            return convertDatesToTimestamps(item);
          }
          return item;
        });
      }

      // If the value is an object, recurse
      else if (typeof value === "object" && value !== null) {
        convertDatesToTimestamps(value);
      }
    }
  }

  return obj;
}

export function getOriginalFilePath(
  uid: string,
  docCollectionName: string,
  docId: string,
  fileId: number,
  extension: string
): string {
  return `${AttachmentConstants.userDataPath}/${uid}/${docCollectionName}/${docId}/originalFiles/${fileId}.${extension}`;
}

export function getOptimizedFilePath(
  uid: string,
  docCollectionName: string,
  docId: string,
  fileId: number,
  extension: string
): string {
  return `${AttachmentConstants.userDataPath}/${uid}/${docCollectionName}/${docId}/optimizedFiles/${fileId}.${extension}`;
}

export function getThumbnailFilePath(
  uid: string,
  docCollectionName: string,
  docId: string,
  fileId: number,
  extension: string
): string {
  return `${AttachmentConstants.userDataPath}/${uid}/${docCollectionName}/${docId}/thumbnails/${fileId}.${extension}`;
}

export async function getViewSettingsByUserId(
  uid: string
): Promise<IViewSettings | undefined> {
  try {
    const snapshot = await admin
      .firestore()
      .collection("viewSettings")
      .where("uid", "==", uid)
      .get();
    if (snapshot.empty) {
      return undefined;
    } else {
      return snapshot.docs[0].data() as IViewSettings;
    }
  } catch (e) {
    return undefined;
  }
}

export function capitalizeFirstLetter(val: string) {
  return String(val).charAt(0).toUpperCase() + String(val).slice(1);
}
