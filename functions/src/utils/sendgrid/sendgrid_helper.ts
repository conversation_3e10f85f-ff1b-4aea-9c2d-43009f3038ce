import sgMail = require("@sendgrid/mail");
import { Client } from "@sendgrid/client";
import { PersonalizationData } from "@sendgrid/helpers/classes/personalization";
export abstract class SendGridTemplateConstants {
  static readonly accountDeleted = "d-8c556bd1c809476abe02dc53b0264d86";
  static readonly autoRenewalAlert = "d-ccf6b9692c0345659af19f265a1e6527";
  static readonly collaborationInvite = "d-2765f23ca559435084e07043ff7c7988";
  static readonly collaborationRequest = "d-042efe1690514036aa09cf7d70efd8e3";
  static readonly dataImportFailed = "d-0fb5e480cfde4c89844930b97040a9e5";
  static readonly dataImportSuccessfull = "d-fb4861c3f29145c48b40ae283eb00ff9";
  static readonly downloadBackupData = "d-a190a482d29e44a5a06ac56ef9deb16b";
  static readonly freeTrialExpired = "d-3debd0dbe27b4e8198076b301cded6fd";
  static readonly freeTrialExpiringAlert = "d-76b8774716784b15ba2cf2913b46d452";
  static readonly loginAndSignupVerification =
    "d-14a7dd8834e24c688d3dba5fac38167a";
  static readonly notSubscribed = "d-097147aeed2a4ffa8abfb94846b0efc1";
  static readonly subscriptionDowngraded = "d-894a2a02a0704b2da05b44e653e8cb7f";
  static readonly subscriptionExpired = "d-4c37056825524a62a8eb895c5def6a34";
  static readonly subscriptionExpiringAlert =
    "d-7bf300e10c5144e4b879612d9e9946e6";
  static readonly subscriptionSuccessfull =
    "d-38196a1347f34801a0b8c15510eb40bf";
  static readonly subscriptionUpdated = "d-4c3c7e14ef1e41daafcd130ac138de34";
  static readonly upgradeSuccessful = "d-a28a0ef510af40a39bf11a5c89f7a0c0";
  static readonly userCancelsSubscription =
    "d-2e39015516f24f198d1f16627fa200b0";
  static readonly userCancelsSubscriptionFromPremiumPlans =
    "d-1a96a2f3ad0c4f19b38e6460e1ef9106";
  static readonly welcomeUser = "d-7bffa3873c2c466b86229a640060f6cb";
}

export async function sendSingleEmail(
  to: string,
  templateId: string,
  customData: { [key: string]: unknown }
) {
  if (process.env.SENDGRID_API_KEY == undefined) return;
  console.log("sending email to users");
  sgMail.setClient(new Client());
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);

  const msg: sgMail.MailDataRequired = {
    to: to,
    from: process.env.MEVOLVE_SENDER_EMAIL ?? "'Mevolve' <<EMAIL>>",
    templateId: templateId,
    dynamicTemplateData: customData,
  };
  await sgMail
    .send(msg)
    .then(() => {
      console.log("Email sent");
    })
    .catch((error: Error) => {
      console.error("error is:" + error);
    });
}

export type EmailInfo = {
  to: string;
  data: { [key: string]: unknown };
};

export async function sendBulkEmail(
  templateId: string,
  emailList: EmailInfo[]
) {
  if (process.env.SENDGRID_API_KEY == undefined) return;
  console.log("sending email to users");
  sgMail.setClient(new Client());
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  const personalizationDataList: PersonalizationData[] = [];
  for (const emailInfo of emailList) {
    personalizationDataList.push({
      to: emailInfo.to,
      dynamicTemplateData: emailInfo.data,
    });
  }
  const msg: sgMail.MailDataRequired = {
    from: process.env.MEVOLVE_SENDER_EMAIL ?? "'Mevolve' <<EMAIL>>",
    personalizations: personalizationDataList,
    templateId: templateId,
  };
  await sgMail
    .send(msg)
    .then(() => {
      console.log("Email sent");
    })
    .catch((error: Error) => {
      console.error("error is:" + error);
    });
}

export async function sendSingleEmailWithHtmlBody(
  to: string[],
  subject: string,
  htmlBody: string
) {
  if (process.env.SENDGRID_API_KEY == undefined) return;
  console.log("sending html email");
  sgMail.setClient(new Client());
  sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  sgMail.send(
    {
      from: process.env.MEVOLVE_SENDER_EMAIL ?? "'Mevolve' <<EMAIL>>",
      to: to,
      subject: subject,
      html: htmlBody,
    },
    false,
    (err) => {
      if (err) console.error(err);
      else console.log("Sent email");
    }
  );
}
