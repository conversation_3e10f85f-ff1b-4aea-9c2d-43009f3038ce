import { Log, Logging } from "@google-cloud/logging";
import { currentDbVersion } from "../../../functions/migration/versions/base/model_mappings";

export class CloudLogger {
  private logName: string;
  private logging: Logging;
  private log: Log;
  private labels: Record<string, string>;

  constructor(type: "general" | "auth" | "performance" | "error") {
    this.logName =
      process.env.EVENTARC_CLOUD_EVENT_SOURCE ?? "no-name-available";
    this.logging = new Logging();
    this.log = this.logging.log(this.logName);

    // Labels that will be attached to each log entry
    this.labels = {
      source: "cloud-functions",
      type: type,
    };
  }

  // Log method to send logs to Google Cloud Logging
  async logToCloud(
    message: string,
    uid: string | undefined,
    session: string | undefined,
    severity: "INFO" | "DEBUG" | "WARNING" | "ERROR" | "CRITICAL"
  ) {
    const metadata = {
      resource: { type: "global" }, // Adjust resource type as per your environment
      severity: severity,
      labels: this.labels,
    };

    // JSON payload to log
    const payload = {
      message: message,
      uid: uid,
      session: session,
      dbVersion: currentDbVersion,
    };

    // Create the log entry and write it
    const entry = this.log.entry(metadata, payload);
    await this.log.write(entry);
  }
}
