import { CloudLogger } from "./cloud_logger";

const useLogger = false;

// Create a Logger class that wraps Bunyan and LoggingBunyan
export class MeLogger {
  cloudLogger: CloudLogger;
  userId: string | undefined;
  sessionId: string | undefined;
  constructor(userId?: string | undefined, sessionId?: string | undefined) {
    this.userId = userId;
    this.sessionId = sessionId;
    this.cloudLogger = new CloudLogger("general");
  }

  debug(message?: any, ...optionalParams: any[]) {
    if (process.env.FUNCTIONS_EMULATOR || !useLogger) {
      console.debug(message, optionalParams);
    }
    else {
      this.cloudLogger.logToCloud(message, this.userId, this.sessionId, "DEBUG");
    }
  }

  log(message?: any, ...optionalParams: any[]) {
    if (process.env.FUNCTIONS_EMULATOR || !useLogger) {
      console.log(message, optionalParams);
    } else {
      this.cloudLogger.logToCloud(message, this.userId, this.sessionId, "INFO");
    }
  }

  info(message?: any, ...optionalParams: any[]) {
    if (process.env.FUNCTIONS_EMULATOR || !useLogger) {
      console.info(message, optionalParams);
    } else {
      this.cloudLogger.logToCloud(message, this.userId, this.sessionId, "INFO");
    }
  }

  warning(message?: any, ...optionalParams: any[]) {
    if (process.env.FUNCTIONS_EMULATOR || !useLogger) {
      console.warn(message, optionalParams);
    } else {
      this.cloudLogger.logToCloud(
        message,
        this.userId,
        this.sessionId,
        "WARNING"
      );
    }
  }

  error(message?: any, ...optionalParams: any[]) {
    if (process.env.FUNCTIONS_EMULATOR || !useLogger) {
      console.error(message, optionalParams);
    } else {
      this.cloudLogger.logToCloud(message, this.userId, this.sessionId, "ERROR");
    }
    // console.log({ ..._additionalInfo }, message);
  }

  critical(message?: any, ...optionalParams: any[]) {
    if (process.env.FUNCTIONS_EMULATOR || !useLogger) {
      console.error(message, optionalParams);
    } else {
      this.cloudLogger.logToCloud(
        message,
        this.userId,
        this.sessionId,
        "CRITICAL"
      );
    }
  }
}
