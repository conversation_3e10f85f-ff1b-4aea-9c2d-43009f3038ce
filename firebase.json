{"emulators": {"auth": {"port": 9099, "host": "0.0.0.0"}, "functions": {"port": 5001, "host": "0.0.0.0"}, "firestore": {"port": 8080, "host": "0.0.0.0"}, "hosting": {"port": 5002, "host": "0.0.0.0"}, "storage": {"port": 9199, "host": "0.0.0.0"}, "ui": {"enabled": true, "port": 4000}, "eventarc": {"port": 9299}, "database": {"port": 9000}, "pubsub": {"port": 8085}, "singleProjectMode": true}, "functions": {"predeploy": ["npm --prefix \"$RESOURCE_DIR\" run lint", "npm --prefix \"$RESOURCE_DIR\" run build"], "source": "functions", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log"]}, "storage": [{"target": "primary", "rules": "storage.primary.rules"}, {"target": "secondary", "rules": "storage.secondary.rules"}], "firestore": {"rules": "firestore.rules", "indexes": "firestore.indexes.json"}, "hosting": {"target": "mevolve_web", "public": "build/web", "rewrites": [{"source": "**", "destination": "/index.html"}], "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "headers": [{"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "no-cache"}]}]}, "database": {"rules": "database.rules.json"}, "flutter": {"platforms": {"android": {"default": {"projectId": "mevolve-dev", "appId": "1:825137847857:android:34fb23e42a203e67ff3f10", "fileOutput": "android/app/google-services.json"}, "buildConfigurations": {"src/qa": {"projectId": "mevolve-qa", "appId": "1:922862417651:android:385a723046ffe1027fe986", "fileOutput": "android/app/src/qa/google-services.json"}, "src/staging": {"projectId": "mevolve-staging", "appId": "1:953131275065:android:a8a92a924d36f4370a8ee8", "fileOutput": "android/app/src/staging/google-services.json"}, "src/prod": {"projectId": "mevolve-prod", "appId": "1:176451695467:android:f7e14798f69802d8d4922e", "fileOutput": "android/app/src/prod/google-services.json"}, "src/hotfix": {"projectId": "mevolve-hotfix", "appId": "1:872725797921:android:04e04bd86b53c916661760", "fileOutput": "android/app/src/hotfix/google-services.json"}}}, "ios": {"default": {"projectId": "mevolve-dev", "appId": "1:825137847857:ios:3fda22830af45d3cff3f10", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.dev.plist"}, "buildConfigurations": {"Debug-dev": {"projectId": "mevolve-dev", "appId": "1:825137847857:ios:3fda22830af45d3cff3f10", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.dev.plist"}, "Debug-qa": {"projectId": "mevolve-qa", "appId": "1:922862417651:ios:22590b292022447a7fe986", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.qa.plist"}, "Debug-staging": {"projectId": "mevolve-staging", "appId": "1:953131275065:ios:90a39deceaf1dece0a8ee8", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.staging.plist"}, "Debug-prod": {"projectId": "mevolve-prod", "appId": "1:176451695467:ios:2aa400b4d4749de3d4922e", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.prod.plist"}, "Debug-hotfix": {"projectId": "mevolve-hotfix", "appId": "1:872725797921:ios:64e7f2907a78bfde661760", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.hotfix.plist"}, "Release-dev": {"projectId": "mevolve-dev", "appId": "1:825137847857:ios:3fda22830af45d3cff3f10", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.dev.plist"}, "Release-qa": {"projectId": "mevolve-qa", "appId": "1:922862417651:ios:22590b292022447a7fe986", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.qa.plist"}, "Release-staging": {"projectId": "mevolve-staging", "appId": "1:953131275065:ios:90a39deceaf1dece0a8ee8", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.staging.plist"}, "Release-prod": {"projectId": "mevolve-prod", "appId": "1:176451695467:ios:2aa400b4d4749de3d4922e", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.prod.plist"}, "Release-hotfix": {"projectId": "mevolve-hotfix", "appId": "1:872725797921:ios:64e7f2907a78bfde661760", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.hotfix.plist"}, "Profile-dev": {"projectId": "mevolve-dev", "appId": "1:825137847857:ios:3fda22830af45d3cff3f10", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.dev.plist"}, "Profile-qa": {"projectId": "mevolve-qa", "appId": "1:922862417651:ios:22590b292022447a7fe986", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.qa.plist"}, "Profile-staging": {"projectId": "mevolve-staging", "appId": "1:953131275065:ios:90a39deceaf1dece0a8ee8", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.staging.plist"}, "Profile-prod": {"projectId": "mevolve-prod", "appId": "1:176451695467:ios:2aa400b4d4749de3d4922e", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.prod.plist"}, "Profile-hotfix": {"projectId": "mevolve-hotfix", "appId": "1:872725797921:ios:64e7f2907a78bfde661760", "uploadDebugSymbols": true, "fileOutput": "ios/Config/GoogleService-Info-app.mevolve.daily.mobile.hotfix.plist"}}}, "macos": {"default": {"projectId": "mevolve-dev", "appId": "1:825137847857:ios:3fda22830af45d3cff3f10", "uploadDebugSymbols": true, "fileOutput": "macos/Runner/GoogleService-Info.plist"}, "buildConfigurations": {"Debug-dev": {"projectId": "mevolve-dev", "appId": "1:825137847857:ios:f2d7621bbe59cdd6ff3f10", "uploadDebugSymbols": true, "fileOutput": "macos/Runner/GoogleService-Info.plist"}}}, "dart": {"lib/firebase_options_dev.dart": {"projectId": "mevolve-dev", "configurations": {"android": "1:825137847857:android:34fb23e42a203e67ff3f10", "ios": "1:825137847857:ios:3fda22830af45d3cff3f10", "macos": "1:825137847857:ios:f2d7621bbe59cdd6ff3f10", "web": "1:825137847857:web:82e67e31cf528c29ff3f10", "windows": "1:825137847857:web:a35f2bb1f977ce96ff3f10"}}, "lib/firebase_options_qa.dart": {"projectId": "mevolve-qa", "configurations": {"android": "1:922862417651:android:385a723046ffe1027fe986", "ios": "1:922862417651:ios:22590b292022447a7fe986", "macos": "1:922862417651:ios:22590b292022447a7fe986", "windows": "1:922862417651:web:0c504a259ad9965c7fe986"}}, "lib/firebase_options_staging.dart": {"projectId": "mevolve-staging", "configurations": {"android": "1:953131275065:android:a8a92a924d36f4370a8ee8", "ios": "1:953131275065:ios:90a39deceaf1dece0a8ee8", "macos": "1:953131275065:ios:90a39deceaf1dece0a8ee8", "windows": "1:953131275065:web:1f6f6216188e635c0a8ee8"}}, "lib/firebase_options_prod.dart": {"projectId": "mevolve-prod", "configurations": {"android": "1:176451695467:android:f7e14798f69802d8d4922e", "ios": "1:176451695467:ios:2aa400b4d4749de3d4922e", "macos": "1:176451695467:ios:2aa400b4d4749de3d4922e", "windows": "1:176451695467:web:09625f594e7f361cd4922e"}}, "lib/firebase_options_hotfix.dart": {"projectId": "mevolve-hotfix", "configurations": {"android": "1:872725797921:android:04e04bd86b53c916661760", "ios": "1:872725797921:ios:64e7f2907a78bfde661760", "macos": "1:872725797921:ios:64e7f2907a78bfde661760", "windows": "1:872725797921:web:4badb38cd130dd3f661760"}}, "lib/firebase_options.dart": {"projectId": "mevolve-dev", "configurations": {"macos": "1:825137847857:ios:3fda22830af45d3cff3f10"}}}}}}