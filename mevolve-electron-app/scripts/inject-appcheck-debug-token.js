#!/usr/bin/env node

// <PERSON><PERSON><PERSON> to inject App Check debug token into the built Flutter web app for production builds
// This reads the debug token from appcheck_debug_tokens_prod.json and injects it into the web app's index.html
// The web directory path is determined from config.js which uses WEB_FOLDER_PATH from the .env file

const fs = require('fs')
const path = require('path')

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Get command line arguments
const args = process.argv.slice(2)
if (args.length < 1) {
  log('red', '❌ Usage: node inject-appcheck-debug-token.js <environment>')
  log('yellow', 'Example: node inject-appcheck-debug-token.js prod')
  process.exit(1)
}

const environment = args[0]

// Only inject for production builds
if (environment !== 'prod') {
  log('blue', `ℹ️  Skipping App Check debug token injection for ${environment} environment`)
  process.exit(0)
}

log('blue', '🔧 Injecting App Check debug token into built web app for production')

// Import config to get the correct web directory path
let config
try {
  config = require('../src/config')
} catch (error) {
  log('red', `❌ Failed to load config: ${error.message}`)
  log('yellow', 'ℹ️  Make sure prepare-build-env.js has been run first to create .env file')
  process.exit(1)
}

// Read the debug token file
const tokenFilePath = path.join(__dirname, 'appcheck_debug_tokens_prod.json')
if (!fs.existsSync(tokenFilePath)) {
  log('red', `❌ App Check debug token file not found: ${tokenFilePath}`)
  process.exit(1)
}

let debugTokens
try {
  const tokenFileContent = fs.readFileSync(tokenFilePath, 'utf8')
  debugTokens = JSON.parse(tokenFileContent)
} catch (error) {
  log('red', `❌ Failed to read or parse debug token file: ${error.message}`)
  process.exit(1)
}

// Validate required token exists
if (!debugTokens.webAppCheckDebugToken) {
  log('red', '❌ webAppCheckDebugToken not found in debug token file')
  process.exit(1)
}

// Get the web directory path from config (uses WEB_FOLDER_PATH from .env)
// Follow the same pattern as copy-web-build.js
const electronDir = path.dirname(__dirname)
const webAppPath = path.resolve(electronDir, config.webFolderPath)
const webAppIndexPath = path.join(webAppPath, 'index.html')

log('blue', `📁 Web app path: ${webAppPath}`)

// Check if the web app directory exists
if (!fs.existsSync(webAppPath)) {
  log('red', `❌ Web app directory not found: ${webAppPath}`)
  log('yellow', 'ℹ️  Make sure copy-web-build.js has been run first')
  process.exit(1)
}

// Check if index.html exists
if (!fs.existsSync(webAppIndexPath)) {
  log('red', `❌ Web app index.html not found: ${webAppIndexPath}`)
  log('yellow', 'ℹ️  Make sure the Flutter web build has been copied to the web directory')
  process.exit(1)
}

// Read the index.html file
let indexContent = fs.readFileSync(webAppIndexPath, 'utf8')

// Create the App Check debug token injection script
const debugToken = debugTokens.webAppCheckDebugToken
const injectionScript = `
  <!-- Firebase App Check Debug Token -->
  <script>
    self.FIREBASE_APPCHECK_DEBUG_TOKEN = "${debugToken}";
  </script>`

// Check if the token script is already injected
const tokenScriptRegex = /<!-- Firebase App Check Debug Token -->/
if (tokenScriptRegex.test(indexContent)) {
  // Replace existing injection
  const existingScriptRegex = /<!-- Firebase App Check Debug Token -->[\s\S]*?<\/script>/
  indexContent = indexContent.replace(existingScriptRegex, `${injectionScript}`)
  log('yellow', '🔄 Updated existing Firebase App Check debug token in index.html')
} else {
  // Find the opening <body> tag and inject the script right after it
  const bodyOpenRegex = /<body>/
  if (bodyOpenRegex.test(indexContent)) {
    indexContent = indexContent.replace(bodyOpenRegex, `<body>\n${injectionScript}`)
    log('green', '✅ Added Firebase App Check debug token script to index.html')
  } else {
    log('red', '❌ Could not find <body> tag in index.html')
    process.exit(1)
  }
}

// Write back to index.html file
try {
  fs.writeFileSync(webAppIndexPath, indexContent, 'utf8')
  log('green', '✅ App Check debug token injection into web app completed')
  log('blue', `📋 Token injected into: ${webAppIndexPath}`)
  log('blue', `📋 Debug token: ${debugToken}`)
} catch (error) {
  log('red', `❌ Failed to write index.html file: ${error.message}`)
  process.exit(1)
}

log('green', '🎯 Production web app ready with App Check debug token!')
log('yellow', '💡 Note: The Flutter app will automatically use self.FIREBASE_APPCHECK_DEBUG_TOKEN for Firebase App Check')