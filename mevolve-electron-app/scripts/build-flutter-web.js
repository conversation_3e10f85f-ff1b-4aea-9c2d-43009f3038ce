#!/usr/bin/env node

// Cross-platform Flutter web build script for Electron
// Replaces build-flutter-web.sh for Windows/Linux/macOS compatibility

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

// Colors for output (works on all platforms with modern terminals)
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Import centralized config instead of using process.env directly
const config = require('../src/config')

// ============================================================================
// OPTIMIZATION TOGGLE - Change this flag to control build optimization
// ============================================================================
const ENABLE_OPTIMIZATION = false // Set to true for release builds testing, false for debugging
// ============================================================================

// Get environment from centralized config
const environment = config.flavor

if (!environment) {
  log('red', '❌ FLAVOR not specified!')
  log('yellow', 'FLAVOR should be loaded from .env file via dotenv')
  log('yellow', 'Example: npm run start:dev')
  process.exit(1)
}

log('blue', `Building Flutter web for environment: ${environment}`)

// Validate environment
const validEnvironments = ['dev', 'qa', 'staging', 'prod', 'hotfix']
if (!validEnvironments.includes(environment)) {
  log('red', `Error: Invalid environment '${environment}'. Valid options: ${validEnvironments.join(', ')}`)
  process.exit(1)
}

log('green', `Valid environment: ${environment}`)

// Define paths (cross-platform)
const scriptDir = __dirname
const electronDir = path.dirname(scriptDir)
const flutterProjectDir = path.dirname(electronDir)
const flutterBuildDir = path.join(flutterProjectDir, 'build', 'web')

// Check if Flutter project exists
if (!fs.existsSync(flutterProjectDir)) {
  log('red', `Error: Flutter project directory not found at ${flutterProjectDir}`)
  process.exit(1)
}

// Check if pubspec.yaml exists
const pubspecPath = path.join(flutterProjectDir, 'pubspec.yaml')
if (!fs.existsSync(pubspecPath)) {
  log('red', 'Error: pubspec.yaml not found. Are you in a Flutter project directory?')
  process.exit(1)
}

log('blue', `Building Flutter web with flavor: ${environment}`)
log('yellow', `Optimization mode: ${ENABLE_OPTIMIZATION ? 'ENABLED (Release Testing)' : 'DISABLED (Debugging)'}`)

try {
  // Build Flutter web with environment flavor and configurable optimization
  const baseCommand = [
    'flutter', 'build', 'web',
    `--dart-define=flavor=${environment}`,
    `--dart-define-from-file=api-keys.${environment}.json`
  ]

  // Add optimization-specific flags based on ENABLE_OPTIMIZATION toggle
  const optimizationFlags = ENABLE_OPTIMIZATION ? [
    '--release', // Use release mode for production optimization
    '--dart-define=Dart2jsOptimization=O4', // Maximum optimization
    '--tree-shake-icons', // Remove unused Material Design icons
    '--web-renderer=canvaskit' // Use CanvasKit for better performance
  ] : [
    '--profile', // Use profile mode for debugging
    '--dart-define=Dart2jsOptimization=O0', // No optimization for faster builds
    '--source-maps' // Include source maps for debugging
  ]

  const flutterCommand = [...baseCommand, ...optimizationFlags].join(' ')
  
  log('blue', `Command: ${flutterCommand}`)
  
  // Execute in Flutter project directory
  execSync(flutterCommand, { 
    cwd: flutterProjectDir, 
    stdio: 'inherit' 
  })
  
} catch (error) {
  log('red', `Error: Flutter build failed: ${error.message}`)
  process.exit(1)
}

// Check if build was successful
if (!fs.existsSync(flutterBuildDir)) {
  log('red', 'Error: Flutter web build failed. Build directory not found.')
  process.exit(1)
}

log('green', `✅ Successfully built Flutter web for ${environment} environment`)