#!/usr/bin/env node

// Script to prepare unified .env file for build process
// This combines environment-specific .env.{environment} with BUILD_PLATFORM
// The resulting .env file will be packaged with the app for runtime use

const fs = require('fs')
const path = require('path')

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Function to detect current platform
function detectPlatform() {
  const platform = process.platform
  switch (platform) {
    case 'darwin': return 'mac'
    case 'win32': return 'win'
    case 'linux': return 'linux'
    default:
      log('red', `❌ Unsupported platform: ${platform}`)
      process.exit(1)
  }
}

// Get command line arguments
const args = process.argv.slice(2)
if (args.length < 1) {
  log('red', '❌ Usage: node prepare-build-env.js <environment> [build_platform]')
  log('yellow', 'Example: node prepare-build-env.js dev')
  log('yellow', 'Example: node prepare-build-env.js dev win')
  process.exit(1)
}

const environment = args[0]
const buildPlatform = args[1] || detectPlatform() // Auto-detect if not provided

// Validate inputs
const validEnvironments = ['dev', 'qa', 'staging', 'prod', 'hotfix']
const validPlatforms = ['mac', 'win', 'linux']

if (!validEnvironments.includes(environment)) {
  log('red', `❌ Invalid environment: ${environment}. Valid options: ${validEnvironments.join(', ')}`)
  process.exit(1)
}

if (!validPlatforms.includes(buildPlatform)) {
  log('red', `❌ Invalid build platform: ${buildPlatform}. Valid options: ${validPlatforms.join(', ')}`)
  process.exit(1)
}

log('blue', `🔧 Preparing build environment for: ${environment} (${buildPlatform})`)

// Read environment-specific .env file
const envFilePath = path.join(__dirname, '..', `.env.${environment}`)
if (!fs.existsSync(envFilePath)) {
  log('red', `❌ Environment file not found: ${envFilePath}`)
  process.exit(1)
}

// Read the environment file content
const envContent = fs.readFileSync(envFilePath, 'utf8')
log('green', `✅ Loaded environment variables from: .env.${environment}`)

// Add BUILD_PLATFORM to the content
const enhancedContent = `${envContent.trim()}\nBUILD_PLATFORM=${buildPlatform}\n`

// Write to unified .env file
const unifiedEnvPath = path.join(__dirname, '..', '.env')
fs.writeFileSync(unifiedEnvPath, enhancedContent, 'utf8')

log('green', `✅ Created unified .env file with BUILD_PLATFORM=${buildPlatform}`)

// Parse and display the final configuration
const finalEnvLines = enhancedContent.trim().split('\n').filter(line => line.trim() && !line.startsWith('#'))
log('blue', '📋 Final environment configuration:')
finalEnvLines.forEach(line => {
  const [key, value] = line.split('=')
  log('blue', `  • ${key}: ${value}`)
})

log('green', '🎯 Build environment ready!')