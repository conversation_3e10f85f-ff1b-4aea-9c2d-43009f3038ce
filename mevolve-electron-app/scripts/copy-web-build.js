#!/usr/bin/env node

// Shared script to copy Flutter web build to environment-specific folder
// Used by: build-flutter-web.js (local) and CI/CD (pre-built web)

const fs = require('fs')
const path = require('path')

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Import centralized config instead of using process.env directly
const config = require('../src/config')

// Validate required configuration
if (!config.webFolderPath) {
  log('red', '❌ WEB_FOLDER_PATH not specified!')
  log('yellow', 'WEB_FOLDER_PATH should be loaded from .env file via dotenv')
  process.exit(1)
}

const scriptDir = __dirname
const electronDir = path.dirname(scriptDir)
const flutterProjectDir = path.dirname(electronDir)
const sourceBuildDir = path.join(flutterProjectDir, 'build', 'web')
const targetWebDir = path.resolve(electronDir, config.webFolderPath)

log('blue', '📁 Copying Flutter web build to Electron app...')

// Check if source build exists
if (!fs.existsSync(sourceBuildDir)) {
  log('red', `❌ Flutter web build not found: ${sourceBuildDir}`)
  log('yellow', 'Make sure Flutter web build was completed before this step')
  process.exit(1)
}

// Check if index.html exists in source
const sourceIndexPath = path.join(sourceBuildDir, 'index.html')
if (!fs.existsSync(sourceIndexPath)) {
  log('red', `❌ Invalid Flutter web build: ${sourceIndexPath} not found`)
  log('yellow', 'Source build directory exists but appears incomplete')
  process.exit(1)
}

// Remove existing target directory if it exists
if (fs.existsSync(targetWebDir)) {
  fs.rmSync(targetWebDir, { recursive: true, force: true })
}

// Copy Flutter web build to target directory
try {
  log('blue', '📋 Copying Flutter web build...')
  copyDirectory(sourceBuildDir, targetWebDir)
} catch (error) {
  log('red', `❌ Failed to copy Flutter web build: ${error.message}`)
  process.exit(1)
}

// Verify copy was successful
const targetIndexPath = path.join(targetWebDir, 'index.html')
if (!fs.existsSync(targetIndexPath)) {
  log('red', `❌ Copy verification failed: ${targetIndexPath} not found`)
  process.exit(1)
}

log('green', `✅ Successfully copied Flutter web build`)

// Cross-platform directory copy function
function copyDirectory(src, dest) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true })
  }
  
  const entries = fs.readdirSync(src, { withFileTypes: true })
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name)
    const destPath = path.join(dest, entry.name)
    
    if (entry.isDirectory()) {
      copyDirectory(srcPath, destPath)
    } else {
      fs.copyFileSync(srcPath, destPath)
    }
  }
}