#!/usr/bin/env node

// Cross-platform icon generation script for Electron
// Handles platform-specific icon generation requirements

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const os = require('os')

// Colors for output
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
}

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`)
}

// Import centralized config instead of using process.env directly
const config = require('../src/config')

// Validate required configuration
if (!config.webFolderPath) {
  log('red', '❌ WEB_FOLDER_PATH not specified!')
  log('yellow', 'WEB_FOLDER_PATH should be loaded from .env file via dotenv')
  log('yellow', 'Example: npm run icons (with proper dotenv setup)')
  process.exit(1)
}

const platform = os.platform()
const scriptDir = __dirname
const electronDir = path.dirname(scriptDir)
const iconsDir = path.resolve(electronDir, config.webFolderPath, 'icons')
const sourceIcon = path.join(iconsDir, 'Icon-512.png')

log('blue', '🎨 Cross-platform icon generation...')
log('yellow', `Platform: ${platform}`)
log('yellow', `Icons directory: ${iconsDir}`)

// Check if source icon exists
if (!fs.existsSync(sourceIcon)) {
  log('red', `❌ Source icon not found: ${sourceIcon}`)
  log('yellow', 'Please ensure Icon-512.png exists in the icons directory')
  process.exit(1)
}

// Platform-specific icon generation
switch (platform) {
  case 'darwin': // macOS
    generateMacOSIcons()
    break
    
  case 'win32': // Windows
    generateWindowsIcons()
    break
    
  case 'linux': // Linux
    generateLinuxIcons()
    break
    
  default:
    log('yellow', `⚠️  Icon generation not specifically configured for platform: ${platform}`)
    log('green', '✅ Using existing PNG icons (should work on most platforms)')
}

function generateMacOSIcons() {
  log('blue', '🍎 Generating macOS .icns file...')
  
  const iconsetDir = path.join(iconsDir, 'icon.iconset')
  const outputIcns = path.join(iconsDir, 'icon.icns')
  
  try {
    // Remove old iconset and icns if they exist
    if (fs.existsSync(iconsetDir)) fs.rmSync(iconsetDir, { recursive: true })
    if (fs.existsSync(outputIcns)) fs.unlinkSync(outputIcns)
    
    // Create iconset directory
    fs.mkdirSync(iconsetDir, { recursive: true })
    
    log('blue', '📐 Creating all required icon sizes...')
    
    // Generate all required sizes using sips (built-in macOS tool)
    const iconSizes = [
      { size: '16x16', file: 'icon_16x16.png', dimensions: '16 16' },
      { size: '16x16@2x', file: '<EMAIL>', dimensions: '32 32' },
      { size: '32x32', file: 'icon_32x32.png', dimensions: '32 32' },
      { size: '32x32@2x', file: '<EMAIL>', dimensions: '64 64' },
      { size: '128x128', file: 'icon_128x128.png', dimensions: '128 128' },
      { size: '128x128@2x', file: '<EMAIL>', dimensions: '256 256' },
      { size: '256x256', file: 'icon_256x256.png', dimensions: '256 256' },
      { size: '256x256@2x', file: '<EMAIL>', dimensions: '512 512' },
      { size: '512x512', file: 'icon_512x512.png', dimensions: '512 512' },
      { size: '512x512@2x', file: '<EMAIL>', dimensions: '1024 1024' }
    ]
    
    for (const icon of iconSizes) {
      const outputPath = path.join(iconsetDir, icon.file)
      execSync(`sips -z ${icon.dimensions} "${sourceIcon}" --out "${outputPath}"`, { stdio: 'ignore' })
    }
    
    log('blue', '🔧 Converting iconset to .icns...')
    
    // Convert iconset to icns using iconutil
    execSync(`iconutil -c icns "${iconsetDir}" -o "${outputIcns}"`)
    
    // Clean up iconset directory
    fs.rmSync(iconsetDir, { recursive: true })
    
    log('green', `✅ Generated proper .icns file: ${outputIcns}`)
    log('green', '📊 Icon contains all required sizes for proper macOS dock display')
    
  } catch (error) {
    log('red', `❌ Error generating macOS icons: ${error.message}`)
    log('yellow', 'Note: This requires macOS with sips and iconutil tools')
    process.exit(1)
  }
}

function generateWindowsIcons() {
  log('green', '✅ Using PNG icons for Windows')
}

function generateLinuxIcons() {
  log('green', '✅ Using PNG icons for Linux')
}

log('green', '🎉 Icon generation complete!')