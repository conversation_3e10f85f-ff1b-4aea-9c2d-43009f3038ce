#!/usr/bin/env node

// Static analysis script to check for unauthorized process.env usage
// This runs before build/start to ensure centralized config is used

const fs = require('fs')
const path = require('path')

// Get restricted variables from config.js
function getRestrictedVars() {
  const configPath = path.join(__dirname, '../src/config.js')
  const configContent = fs.readFileSync(configPath, 'utf8')
  
  const envVarsMatch = configContent.match(/const ENV_VARS = \{([^}]+)\}/s)
  if (envVarsMatch) {
    const envVarsContent = envVarsMatch[1]
    const keys = envVarsContent.match(/(\w+):/g)
    return keys ? keys.map(key => key.replace(':', '')) : []
  }
  return []
}

// Files allowed to access process.env directly
const ALLOWED_FILES = [
  'src/config.js',
  'scripts/build-config.js'  // This now uses config, but keeping for safety
]

// Scan a file for unauthorized process.env and dotenv usage
function scanFile(filePath, restrictedVars) {
  const content = fs.readFileSync(filePath, 'utf8')
  const violations = []
  
  // Look for process.env.RESTRICTED_VAR patterns
  restrictedVars.forEach(varName => {
    const regex = new RegExp(`process\\.env\\.${varName}\\b`, 'g')
    let match
    while ((match = regex.exec(content)) !== null) {
      const lineNumber = content.substring(0, match.index).split('\n').length
      violations.push({
        type: 'process.env',
        variable: varName,
        line: lineNumber,
        file: filePath
      })
    }
  })
  
  // Look for unauthorized require('dotenv') usage (but not in strings/comments)
  const lines = content.split('\n')
  lines.forEach((line, index) => {
    const trimmedLine = line.trim()
    
    // Skip comment lines and string literals containing the pattern
    if (trimmedLine.startsWith('//') || trimmedLine.startsWith('*') || trimmedLine.startsWith('console.log')) {
      return
    }
    
    // Look for actual require('dotenv') calls
    const dotenvRegex = /(?:^|[^'"`])require\s*\(\s*['"`]dotenv['"`]\s*\)/
    if (dotenvRegex.test(line)) {
      violations.push({
        type: 'dotenv',
        variable: 'dotenv',
        line: index + 1,
        file: filePath
      })
    }
  })
  
  return violations
}

// Recursively scan directory
function scanDirectory(dir, restrictedVars, violations = []) {
  const items = fs.readdirSync(dir)
  
  for (const item of items) {
    const fullPath = path.join(dir, item)
    const stat = fs.statSync(fullPath)
    
    if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
      scanDirectory(fullPath, restrictedVars, violations)
    } else if (stat.isFile() && item.endsWith('.js')) {
      const relativePath = path.relative(process.cwd(), fullPath)
      
      // Skip allowed files (normalize path separators for cross-platform compatibility)
      const normalizedPath = relativePath.replace(/\\/g, '/')
      if (!ALLOWED_FILES.some(allowedFile => normalizedPath.endsWith(allowedFile))) {
        const fileViolations = scanFile(fullPath, restrictedVars)
        violations.push(...fileViolations)
      }
    }
  }
  
  return violations
}

// Main execution
function main() {
  console.log('🔍 Checking for unauthorized process.env and dotenv usage...')
  
  const restrictedVars = getRestrictedVars()
  console.log(`   Restricted variables: ${restrictedVars.join(', ')}`)
  
  const projectRoot = process.cwd()
  const violations = scanDirectory(projectRoot, restrictedVars)
  
  if (violations.length > 0) {
    console.log('\n🚨 UNAUTHORIZED ENVIRONMENT ACCESS FOUND!\n')
    
    violations.forEach(violation => {
      console.log(`❌ File: ${violation.file}:${violation.line}`)
      
      if (violation.type === 'process.env') {
        console.log(`   Variable: process.env.${violation.variable}`)
        console.log(`   Fix: Use require('./src/config').${violation.variable.toLowerCase()} instead\n`)
      } else if (violation.type === 'dotenv') {
        console.log(`   Issue: require('dotenv') usage`)
        console.log(`   Fix: Remove this line - dotenv is already loaded in src/config.js\n`)
      }
    })
    
    console.log('💡 Environment access rules:')
    console.log('   • All environment variables must be accessed through src/config.js')
    console.log('   • Only src/config.js should use require("dotenv")')
    console.log('   • This ensures centralized configuration and prevents env var abuse.\n')
    
    process.exit(1)
  }
  
  console.log('✅ No unauthorized process.env or dotenv usage found')
}

main()