// Import centralized config instead of using process.env directly
const config = require('../src/config')

// Get configuration values from centralized config
const flavor = config.flavor
const platform = config.buildPlatform
const webFolderPath = config.webFolderPath

if (!platform) throw new Error('BUILD_PLATFORM environment variable is required (mac, win, linux)')

// Debug: Show code signing status
console.log('🔍 Code signing status:', {
  hasSigningCert: config.codeSigning.hasSigningCert,
  certPath: config.codeSigning.certPath ? '***set***' : 'not set',
  certPassword: config.codeSigning.certPassword ? '***set***' : 'not set',
  identity: config.codeSigning.identity || 'not set'
})

// Platform-specific configurations
const platformConfigs = {
  mac: {
    mac: {
      category: 'public.app-category.productivity',
      target: [{ target: 'dmg', arch: ['x64', 'arm64'] }],
      icon: `${webFolderPath}/icons/icon.icns`,
      // Use dynamic identity (SHA-1 or name) if available, otherwise disable signing
      ...(config.codeSigning.hasSigningCert ? {
        identity: config.codeSigning.identity
      } : { identity: null }),
      type: 'distribution'
    },
    dmg: {
      // Sign the DMG file as well
      sign: config.codeSigning.hasSigningCert
    }
  },
  win: {
    win: {
      target: [
        { 
          target: 'nsis', 
          arch: ['x64', 'ia32']
        },
        { 
          target: 'portable', 
          arch: ['x64'] 
        }
      ],
      icon: `${webFolderPath}/icons/Icon-512.png`
    },
    nsis: {
      // Clean minimal installer - current user only, no auto behaviors
      oneClick: false,                    // Assisted installer (user control)
      allowElevation: false,              // Current user only - no system install
      allowToChangeInstallationDirectory: true, // User can choose location
      createDesktopShortcut: "always",    // Essential shortcuts
      createStartMenuShortcut: true,      
      shortcutName: config.displayName,   
      perMachine: false,                  // Current user only
      selectPerMachineByDefault: false,   // Force current user
      runAfterFinish: true,               // Show run checkbox (unchecked by default)
      displayLanguageSelector: false,     // Clean UI
      deleteAppDataOnUninstall: true,     // Complete cleanup on uninstall
      
      // Proper installer/uninstaller naming
      installerLanguages: ["en"],         // English only
      uninstallDisplayName: config.displayName, // Clean uninstaller name
      menuCategory: "Productivity"         // Start menu category
    }
  },
  linux: {
    linux: {
      target: [
        { target: 'AppImage', arch: ['x64'] },
        { target: 'deb', arch: ['x64'] }
      ],
      icon: `${webFolderPath}/icons/Icon-512.png`,
      category: 'Utility',
      maintainer: '<EMAIL>'
    }
  }
}

module.exports = {
  appId: config.appId,
  productName: config.displayName,
  directories: {
    output: config.distFolderPath
  },
  files: [
    'main.js',
    'src/**/*',
    `${webFolderPath}/**/*`,
    '.env' // Include the unified environment file
  ],
  ...platformConfigs[platform]
}