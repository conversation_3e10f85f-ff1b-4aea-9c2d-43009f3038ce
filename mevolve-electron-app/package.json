{"name": "mevolve", "version": "0.104.132+1", "description": "Mevolve - Personal Growth & Habit Tracking App", "main": "main.js", "scripts": {"_comment": "=== START COMMANDS (Flutter Build + Copy + Icons + Start Electron) ===", "start:dev": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js dev && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron .'", "start:qa": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js qa && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron .'", "start:staging": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js staging && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron .'", "start:prod": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js prod && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/inject-appcheck-debug-token.js prod && node scripts/generate-icons.js && electron .'", "start:hotfix": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js hotfix && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron .'", "_comment2": "=== BUILD COMMANDS - MAC (Flutter Build + Copy + Icons + Electron Build) ===", "build:dev:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js dev mac && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'", "build:qa:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js qa mac && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'", "build:staging:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js staging mac && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'", "build:prod:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js prod mac && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/inject-appcheck-debug-token.js prod && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'", "build:hotfix:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js hotfix mac && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'", "_comment3": "=== BUILD COMMANDS - WINDOWS (Flutter Build + Copy + Icons + Electron Build) ===", "build:dev:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js dev win && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "build:qa:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js qa win && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "build:staging:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js staging win && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "build:prod:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js prod win && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/inject-appcheck-debug-token.js prod && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "build:hotfix:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js hotfix win && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "_comment4": "=== BUILD COMMANDS - LINUX (Flutter Build + Copy + Icons + Electron Build) ===", "build:dev:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js dev linux && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "build:qa:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js qa linux && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "build:staging:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js staging linux && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "build:prod:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js prod linux && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/inject-appcheck-debug-token.js prod && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "build:hotfix:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js hotfix linux && bash -c 'node scripts/build-flutter-web.js && node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "_comment5": "=== CI BUILD COMMANDS (Web Copy + Icons + Electron Build) ===", "ci-build:dev:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js dev win && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "ci-build:qa:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js qa win && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "ci-build:staging:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js staging win && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "ci-build:prod:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js prod win && bash -c 'node scripts/copy-web-build.js && node scripts/inject-appcheck-debug-token.js prod && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "ci-build:hotfix:win": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js hotfix win && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --win --config scripts/build-config.js'", "ci-build:dev:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js dev linux && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "ci-build:qa:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js qa linux && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "ci-build:staging:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js staging linux && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "ci-build:prod:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js prod linux && bash -c 'node scripts/copy-web-build.js && node scripts/inject-appcheck-debug-token.js prod && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "ci-build:hotfix:linux": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js hotfix linux && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --linux --config scripts/build-config.js'", "ci-build:dev:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js dev mac && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'", "ci-build:qa:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js qa mac && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'", "ci-build:staging:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js staging mac && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'", "ci-build:prod:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js prod mac && bash -c 'node scripts/copy-web-build.js && node scripts/inject-appcheck-debug-token.js prod && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'", "ci-build:hotfix:mac": "node scripts/check-env-usage.js && node scripts/prepare-build-env.js hotfix mac && bash -c 'node scripts/copy-web-build.js && node scripts/generate-icons.js && electron-builder --mac --config scripts/build-config.js'"}, "homepage": "https://mevolve.app", "author": {"name": "Mevolve", "email": "<EMAIL>"}, "license": "UNLICENSED", "dependencies": {"dotenv": "^16.5.0", "express": "^4.21.2"}, "devDependencies": {"dotenv-cli": "^8.0.0", "electron": "^28.0.0", "electron-builder": "^24.9.1"}}