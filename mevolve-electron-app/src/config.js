const path = require('path')
const fs = require('fs')

// ================================
// CENTRALIZED ENVIRONMENT CONFIGURATION
// ================================
// This is the ONLY place where environment variables should be accessed.
// All other modules should import and use this config object instead.

// Load environment variables from .env file (created by prepare-build-env.js)
// This ensures both development and packaged apps have consistent env access
require('dotenv').config({ path: path.join(__dirname, '..', '.env') })

// Secure environment variable access - freeze process.env access after initial load
const ENV_VARS = {
  FLAVOR: process.env.FLAVOR,
  APP_DISPLAY_NAME: process.env.APP_DISPLAY_NAME,
  APP_ID: process.env.APP_ID,
  DEBUG: process.env.DEBUG,
  SPLASH_MIN_DURATION: process.env.SPLASH_MIN_DURATION,
  WEB_FOLDER_PATH: process.env.WEB_FOLDER_PATH,
  DIST_FOLDER_PATH: process.env.DIST_FOLDER_PATH,
  BUILD_PLATFORM: process.env.BUILD_PLATFORM,
  // Code signing environment variables
  CSC_LINK: process.env.CSC_LINK,
  CSC_KEY_PASSWORD: process.env.CSC_KEY_PASSWORD,
  CSC_NAME: process.env.CSC_NAME,
  CSC_KEYCHAIN: process.env.CSC_KEYCHAIN,
  CODESIGN_IDENTITY: process.env.CODESIGN_IDENTITY
}

// Validate required environment variables
const requiredVars = ['FLAVOR', 'APP_DISPLAY_NAME', 'APP_ID', 'DEBUG', 'SPLASH_MIN_DURATION', 'WEB_FOLDER_PATH', 'DIST_FOLDER_PATH']
for (const varName of requiredVars) {
  if (!ENV_VARS[varName]) {
    throw new Error(`${varName} environment variable is required`)
  }
}

// Function to validate web folder exists
function validateWebFolder() {
  const webPath = path.resolve(__dirname, '..', ENV_VARS.WEB_FOLDER_PATH)
  
  if (!fs.existsSync(webPath)) {
    throw new Error(`Web folder does not exist: ${webPath}. Run the build command first.`)
  }
  
  const indexPath = path.join(webPath, 'index.html')
  if (!fs.existsSync(indexPath)) {
    throw new Error(`index.html not found in web folder: ${indexPath}. Run the build command first.`)
  }
  
  return true
}

// Centralized configuration object
const config = {
  // Basic app info (from env vars)
  name: ENV_VARS.FLAVOR,
  flavor: ENV_VARS.FLAVOR,
  displayName: ENV_VARS.APP_DISPLAY_NAME,
  appId: ENV_VARS.APP_ID,
  
  // Build configuration
  buildPlatform: ENV_VARS.BUILD_PLATFORM,
  
  // Paths
  webFolderPath: ENV_VARS.WEB_FOLDER_PATH,
  webPath: path.resolve(__dirname, '..', ENV_VARS.WEB_FOLDER_PATH),
  distFolderPath: ENV_VARS.DIST_FOLDER_PATH,
  distPath: path.resolve(__dirname, '..', ENV_VARS.DIST_FOLDER_PATH),
  
  // Feature flags (computed from DEBUG)
  features: {
    debug: ENV_VARS.DEBUG === 'true',
    devTools: ENV_VARS.DEBUG === 'true',
    menu: ENV_VARS.DEBUG === 'true',
    splash: true,
    windowState: true,
  },
  
  // Splash configuration
  splash: {
    minDuration: parseInt(ENV_VARS.SPLASH_MIN_DURATION)
  },
  
  // Code signing configuration
  codeSigning: {
    hasSigningCert: !!(ENV_VARS.CSC_LINK && ENV_VARS.CSC_KEY_PASSWORD) || !!(ENV_VARS.CSC_NAME && ENV_VARS.CSC_KEYCHAIN),
    certPath: ENV_VARS.CSC_LINK,
    certPassword: ENV_VARS.CSC_KEY_PASSWORD,
    certName: ENV_VARS.CSC_NAME,
    keychain: ENV_VARS.CSC_KEYCHAIN,
    identity: ENV_VARS.CODESIGN_IDENTITY
  },
  
  // Computed properties for backwards compatibility
  get environment() { return this.name },
  get isDebug() { return this.features.debug },
  
  // Method to safely get environment variables (for scripts that need them)
  getEnvVar(name) {
    if (ENV_VARS.hasOwnProperty(name)) {
      return ENV_VARS[name]
    }
    throw new Error(`Environment variable '${name}' is not available through config. Add it to ENV_VARS if needed.`)
  }
}

// Prevent modification of the config object
Object.freeze(config)
Object.freeze(config.features)
Object.freeze(config.splash)
Object.freeze(config.codeSigning)

// Export config as default and validateWebFolder as named export
module.exports = {
  ...config,
  validateWebFolder
}