const APP_CONFIG = require('./config')

/**
 * Professional logging system for Mevolve Electron app
 * Clean API with method chaining and proper log levels
 */
class Logger {
  constructor() {
    this.isDebug = APP_CONFIG.isDebug
  }

  // Electron logs with clean API
  get electron() {
    return {
      debug: (msg) => this._log('🔍 [ELECTRON]', msg, true),
      info: (msg) => this._log('⚡ [ELECTRON]', msg, false),
      warn: (msg) => this._log('⚠️  [ELECTRON]', msg, false),
      error: (msg) => this._log('❌ [ELECTRON]', msg, false),
      success: (msg) => this._log('✅ [ELECTRON]', msg, false)
    }
  }

  // Flutter logs with clean API
  get flutter() {
    return {
      log: (msg, level = 0) => this._flutterLog(msg, level),
      debug: (msg) => this._log('🔍 [FLUTTER]', msg, true),
      info: (msg) => this._log('💬 [FLUTTER]', msg, true),
      warn: (msg) => this._log('⚠️  [FLUTTER]', msg, true),
      error: (msg) => this._log('❌ [FLUTTER]', msg, false),
      ready: (msg) => this._log('🎯 [FLUTTER]', msg, false)
    }
  }

  // Server logs with clean API
  get server() {
    return {
      debug: (msg) => this._log('🔍 [SERVER]', msg, true),
      info: (msg) => this._log('🌐 [SERVER]', msg, false),
      warn: (msg) => this._log('⚠️  [SERVER]', msg, false),
      error: (msg) => this._log('❌ [SERVER]', msg, false),
      success: (msg) => this._log('✅ [SERVER]', msg, false)
    }
  }


  // Always show important events
  important(message) {
    console.log(`🎯 [IMPORTANT] ${message}`)
  }

  // Private methods
  _log(prefix, message, debugOnly = false) {
    if (debugOnly && !this.isDebug) return
    console.log(`${prefix} ${message}`)
  }

  _flutterLog(message, level) {
    if (!this.isDebug) return

    // Skip noisy security warnings
    if (message.includes('Electron Security Warning') || 
        message.includes('This warning will not show up')) {
      return
    }

    const prefix = level === 0 ? '💬 [FLUTTER]' :  // log
                   level === 1 ? '🔍 [FLUTTER]' :  // info  
                   level === 2 ? '⚠️  [FLUTTER]' :  // warn
                   level === 3 ? '❌ [FLUTTER]' :  // error
                   '💬 [FLUTTER]'

    console.log(`${prefix} ${message}`)
  }
}

// Export singleton instance
module.exports = new Logger()