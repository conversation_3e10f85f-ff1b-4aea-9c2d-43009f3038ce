const express = require('express')
const path = require('path')
const logger = require('./logger')
const APP_CONFIG = require('./config')
const validateWebFolder = APP_CONFIG.validateWebFolder

let server = null
let currentPort = null
const PORT = 0 // Let system assign available port

function startServer() {
  // If server is already running, return the current port
  if (server && server.listening && currentPort) {
    logger.server.info(`Server already running on http://localhost:${currentPort}`)
    return Promise.resolve(currentPort)
  }
  
  return new Promise((resolve, reject) => {
    try {
      // Validate that the web folder exists for current environment
      logger.server.info(`Starting server for ${APP_CONFIG.name} environment...`)
      logger.server.debug(`Using web path: ${APP_CONFIG.webPath}`)
      
      try {
        validateWebFolder()
      } catch (error) {
        logger.server.error(error.message)
        reject(error)
        return
      }
      
      const expressApp = express()
      
      // Performance optimizations for Express
      expressApp.disable('x-powered-by') // Remove Express header
      
      // Serve static files with caching and compression from environment-specific web folder
      const webPath = APP_CONFIG.webPath
      logger.server.debug(`Serving static files from: ${webPath}`)
      
      expressApp.use(express.static(webPath, {
        maxAge: '1h', // Cache static files for 1 hour
        etag: true,
        lastModified: true,
        setHeaders: (res, path) => {
          // Add security headers
          res.setHeader('X-Content-Type-Options', 'nosniff')
          res.setHeader('X-Frame-Options', 'DENY')
          res.setHeader('Cross-Origin-Opener-Policy', 'unsafe-none')
          res.setHeader('Cross-Origin-Embedder-Policy', 'unsafe-none')
          // Cache control for different file types
          if (path.endsWith('.js') || path.endsWith('.css')) {
            res.setHeader('Cache-Control', 'public, max-age=3600') // 1 hour
          } else if (path.endsWith('.png') || path.endsWith('.jpg') || path.endsWith('.svg')) {
            res.setHeader('Cache-Control', 'public, max-age=86400') // 24 hours
          }
        }
      }))
      
      // Handle all routes by serving index.html (SPA support) from environment web folder
      expressApp.get('*', (req, res) => {
        res.sendFile(path.join(webPath, 'index.html'))
      })
      
      // Create server with proper error handling
      const newServer = expressApp.listen(PORT, 'localhost')
      
      // Handle successful listening
      newServer.on('listening', () => {
        try {
          const address = newServer.address()
          if (!address) {
            logger.server.error('Server address is null after listening')
            newServer.close()
            reject(new Error('Server address is null'))
            return
          }
          
          server = newServer
          currentPort = address.port
          logger.server.success(`Local server running on http://localhost:${currentPort}`)
          resolve(currentPort)
        } catch (err) {
          logger.server.error(`Error in listening handler: ${err.message}`)
          newServer.close()
          reject(err)
        }
      })
      
      // Handle server errors
      newServer.on('error', (error) => {
        logger.server.error(`Server error during startup: ${error.message}`)
        server = null
        currentPort = null
        reject(error)
      })
      
      // Add timeout to prevent hanging
      const timeoutId = setTimeout(() => {
        logger.server.error('Server startup timeout')
        newServer.close()
        reject(new Error('Server startup timeout'))
      }, 10000) // 10 second timeout
      
      newServer.on('listening', () => {
        clearTimeout(timeoutId)
      })
      
    } catch (error) {
      logger.server.error(`Error creating server: ${error.message}`)
      reject(error)
    }
  })
}

function stopServer() {
  if (server && server.listening) {
    const serverToClose = server
    server = null
    currentPort = null
    serverToClose.close()
  }
}

module.exports = { startServer, stopServer }