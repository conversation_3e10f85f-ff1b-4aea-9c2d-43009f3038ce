<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mevolve</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;500;700&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html, body {
            height: 100%;
            overflow: hidden;
            background: var(--color-bg);
            color: var(--color-text);
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            perspective: 1000px;
            user-select: none; /* Disable text selection */
            -webkit-user-select: none; /* Safari */
            -moz-user-select: none; /* Firefox */
            -ms-user-select: none; /* IE/Edge */
        }

        /* Canvas particle field */
        #particleCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
        }

        /* Subtle noise overlay */
        body::before {
            content: '';
            position: absolute;
            inset: 0;
            opacity: var(--opacity-noise);
            background:
                repeating-conic-gradient(from 0deg at 50% 50%,
                    transparent 0deg,
                    currentColor 0.1deg,
                    transparent 0.5deg,
                    transparent 2deg);
            pointer-events: none;
            animation: noiseRotate var(--noise-duration) linear infinite;
            z-index: 1;
        }

        @keyframes noiseRotate {
            to { transform: rotate(360deg); }
        }

        /* Main container */
        .splash-container {
            position: relative;
            width: 200px;
            height: 200px;
            display: flex;
            align-items: center;
            justify-content: center;
            transform-style: preserve-3d;
            transition: transform var(--transition-quick) ease-out;
            z-index: 10;
        }

        /* Morphing shapes */
        .morph-shape {
            position: absolute;
            border: 2px solid currentColor;
            opacity: 0;
            transition: border-color var(--transition-color) ease;
        }

        .morph-shape.accent-active {
            border-color: var(--color-accent);
        }

        /* Each shape morphs differently */
        .morph-shape:nth-child(1) {
            width: 120px;
            height: 120px;
        }

        .morph-shape:nth-child(2) {
            width: 140px;
            height: 140px;
        }

        .morph-shape:nth-child(3) {
            width: 160px;
            height: 160px;
        }

        @keyframes morphLife {
            0% {
                transform: scale(0) rotate(0deg);
                border-radius: 50%;
                opacity: 0;
            }
            15% {
                transform: scale(1) rotate(45deg);
                border-radius: 50%;
                opacity: 0.15;
            }
            30% {
                transform: scale(1.1) rotate(90deg);
                border-radius: 40%;
                opacity: var(--opacity-morph-max);
            }
            45% {
                transform: scale(1) rotate(135deg);
                border-radius: 20%;
                opacity: 0.15;
            }
            60% {
                transform: scale(0.95) rotate(180deg);
                border-radius: 10%;
                opacity: 0.1;
            }
            75% {
                transform: scale(1) rotate(225deg);
                border-radius: 30%;
                opacity: 0.15;
            }
            90% {
                transform: scale(0.9) rotate(270deg);
                border-radius: 50%;
                opacity: 0.1;
            }
            100% {
                transform: scale(0.8) rotate(360deg);
                border-radius: 50%;
                opacity: 0.05;
            }
        }

        /* Custom logo design */
        .logo-mark {
            position: relative;
            z-index: 10;
            width: 80px;
            height: 80px;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: translateZ(20px);
        }

        @keyframes logoReveal {
            from {
                opacity: 0;
                transform: scale(0.8) rotate(-90deg) translateZ(20px);
            }
            to {
                opacity: 1;
                transform: scale(1) rotate(0deg) translateZ(20px);
            }
        }

        /* Logo border */
        .logo-border {
            position: absolute;
            inset: 0;
            border: 2px solid currentColor;
            border-radius: 20px;
            opacity: 0.2;
            transition: all var(--transition-quick) ease;
        }

        .logo-border.accent-active {
            border-color: var(--color-accent);
            opacity: var(--opacity-accent-border);
        }

        /* Logo text */
        .logo-text {
            font-size: 28px;
            font-weight: 700;
            letter-spacing: -1px;
            position: relative;
            z-index: 1;
            transition: color var(--transition-color) ease;
        }

        .logo-text.accent-active {
            color: var(--color-accent);
        }

        /* Text animation */
        .logo-text span {
            display: inline-block;
        }

        @keyframes letterFloat {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-2px);
            }
        }

        /* Brand name */
        .brand-name {
            position: absolute;
            bottom: 60px;
            font-size: 14px;
            font-weight: 300;
            letter-spacing: 4px;
            text-transform: uppercase;
            opacity: 0;
            z-index: 10;
            transition: color var(--transition-color) ease;
        }

        .brand-name.accent-active {
            color: var(--color-accent);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: var(--opacity-brand-name);
                transform: translateY(0);
            }
        }

        /* Progress indicator - FULL WIDTH BOTTOM BAR */
        .progress-indicator {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            width: 100%;
            height: 4px;
            background: rgba(128, 128, 128, 0.2);  /* Gray background */
            overflow: hidden;
            z-index: 20;
        }

        .progress-bar {
            height: 100%;
            background: var(--color-text);  /* Use text color initially */
            width: 0%;
            transition: background-color var(--transition-color) ease;
        }

        .progress-bar.accent-active {
            background: var(--color-accent);
        }

        @keyframes progress {
            to { width: 100%; }
        }
    </style>
</head>
<body>
<!-- Canvas for particle system -->
<canvas id="particleCanvas"></canvas>

<div class="splash-container" id="container">
    <!-- Morphing shapes -->
    <div class="morph-shape" id="morph1"></div>
    <div class="morph-shape" id="morph2"></div>
    <div class="morph-shape" id="morph3"></div>

    <!-- Custom logo -->
    <div class="logo-mark">
        <div class="logo-border" id="logoBorder"></div>
        <div class="logo-text" id="logoText">
            <span>M</span><span>e</span>
        </div>
    </div>
</div>

<!-- Brand name -->
<div class="brand-name" id="brandName">Mevolve</div>

<!-- Progress -->
<div class="progress-indicator" id="progressIndicator">
    <div class="progress-bar" id="progressBar"></div>
</div>

<script>
    // ===== MAIN CONFIGURATION =====
    // Read config from URL parameters - professional approach
    const urlParams = new URLSearchParams(window.location.search);
    const durationMs = parseInt(urlParams.get('duration')) || 4000; // Default 4 seconds
    const durationSeconds = durationMs / 1000;
    
    console.log('🔍 Config from URL - Duration:', durationMs + 'ms');
    
    const CONFIG = {
        // Duration from Electron config
        totalDuration: durationSeconds,

        // Colors
        colors: {
            // Main theme colors
            accent: '#38a169',      // Green (try: '#ff6b6b' red, '#4c6ef5' blue, '#f59e0b' orange)
            background: {
                light: '#ffffff',
                dark: '#000000'
            },
            text: {
                light: '#000000',
                dark: '#ffffff'
            },

            // Opacity values
            opacity: {
                noise: 0.015,
                morphMax: 0.2,
                brandName: 0.7,
                accentBorder: 0.4
            },

            // Particle specific
            particle: {
                shadowBlur: 2,
                accentOpacity: 0.9,
                baseOpacity: 0.7,
                waveWidth: 30,
                particleBoost: 2
            }
        },

        // Transition durations
        transitions: {
            color: 0.5,          // Color transitions
            quick: 0.1,          // Quick transitions (mouse)
            noise: 60            // Noise rotation (doesn't scale with total)
        }
    };

    // ===== APPLY CSS VARIABLES =====
    // Detect dark mode
    const isDarkMode = window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches;

    // Create CSS variables from config
    const root = document.documentElement;
    root.style.setProperty('--color-bg', isDarkMode ? CONFIG.colors.background.dark : CONFIG.colors.background.light);
    root.style.setProperty('--color-text', isDarkMode ? CONFIG.colors.text.dark : CONFIG.colors.text.light);
    root.style.setProperty('--color-accent', CONFIG.colors.accent);
    root.style.setProperty('--opacity-noise', CONFIG.colors.opacity.noise);
    root.style.setProperty('--opacity-morph-max', CONFIG.colors.opacity.morphMax);
    root.style.setProperty('--opacity-brand-name', CONFIG.colors.opacity.brandName);
    root.style.setProperty('--opacity-accent-border', CONFIG.colors.opacity.accentBorder);

    // Transition durations
    root.style.setProperty('--transition-color', `${CONFIG.transitions.color}s`);
    root.style.setProperty('--transition-quick', `${CONFIG.transitions.quick}s`);
    root.style.setProperty('--noise-duration', `${CONFIG.transitions.noise}s`);

    // ===== TIMING SETUP =====
    const timing = {
        // Main animations
        morphShapes: CONFIG.totalDuration,
        logoAppear: CONFIG.totalDuration * 0.2,
        brandNameAppear: CONFIG.totalDuration * 0.2,
        progressBar: CONFIG.totalDuration,

        // Delays
        logoDelay: CONFIG.totalDuration * 0.125,
        brandNameDelay: CONFIG.totalDuration * 0.25,
        particlesDelay: CONFIG.totalDuration * 0.15,
        colorChangeDelay: CONFIG.totalDuration * 0.75,

        // Special effects
        letterFloating: Math.min(2, CONFIG.totalDuration * 0.5),
        morphStagger: 0.2,
    };

    // Convert to milliseconds
    const ms = (seconds) => seconds * 1000;

    // ===== APPLY ANIMATIONS =====
    // Morphing shapes
    const morphShapes = document.querySelectorAll('.morph-shape');
    morphShapes.forEach((shape, index) => {
        shape.style.animation = `morphLife ${timing.morphShapes}s cubic-bezier(0.4, 0, 0.2, 1) forwards`;
        shape.style.animationDelay = `${index * timing.morphStagger}s`;
    });

    // Logo reveal
    const logo = document.querySelector('.logo-mark');
    logo.style.animation = `logoReveal ${timing.logoAppear}s ease-out ${timing.logoDelay}s forwards`;

    // Letter floating
    const letters = document.querySelectorAll('.logo-text span');
    letters.forEach((letter, index) => {
        letter.style.animation = `letterFloat ${timing.letterFloating}s ease-in-out infinite`;
        letter.style.animationDelay = `${index * 0.1}s`;
    });

    // Brand name
    const brandName = document.querySelector('.brand-name');
    brandName.style.animation = `fadeInUp ${timing.brandNameAppear}s ease-out ${timing.brandNameDelay}s forwards`;

    // Progress bar - USING TOTAL DURATION
    const progressBar = document.querySelector('.progress-bar');
    console.log('🎯 CONFIG.totalDuration:', CONFIG.totalDuration);
    console.log('🎯 Setting progress animation to:', `progress ${CONFIG.totalDuration}s linear forwards`);
    progressBar.style.animation = `progress ${CONFIG.totalDuration}s linear forwards`;

    // ===== CANVAS PARTICLES =====
    const canvas = document.getElementById('particleCanvas');
    const ctx = canvas.getContext('2d');

    // High DPI support
    const dpr = window.devicePixelRatio || 1;

    function resizeCanvas() {
        canvas.width = window.innerWidth * dpr;
        canvas.height = window.innerHeight * dpr;
        canvas.style.width = window.innerWidth + 'px';
        canvas.style.height = window.innerHeight + 'px';
        ctx.scale(dpr, dpr);
    }

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Particle system
    const particles = [];
    const particleCount = 4000;
    let isAccentActive = false;

    class Particle {
        constructor(x, y) {
            this.x = x;
            this.y = y;
            this.baseX = x;
            this.baseY = y;
            this.size = Math.random() * 0.6 + 0.2; // Tiny: 0.2-0.8px
            this.baseOpacity = isDarkMode ? (Math.random() * 0.12 + 0.03) : (Math.random() * 0.08 + 0.02);
            this.opacity = this.baseOpacity;
            this.twinkleSpeed = Math.random() * 0.03 + 0.01;
            this.twinklePhase = Math.random() * Math.PI * 2;
            this.waveIntensity = 0;
            this.waveDecay = 0.94;
        }

        update() {
            this.twinklePhase += this.twinkleSpeed;
            const twinkle = Math.sin(this.twinklePhase) * 0.5 + 0.5;
            this.waveIntensity *= this.waveDecay;
            this.opacity = this.baseOpacity * twinkle + (this.waveIntensity * 0.5);
            this.opacity = Math.min(this.opacity, 0.8);
        }

        triggerWave(waveRadius, centerX, centerY, intensity = 1) {
            const distance = Math.sqrt(Math.pow(this.x - centerX, 2) + Math.pow(this.y - centerY, 2));
            const waveWidth = CONFIG.colors.particle.waveWidth;

            if (Math.abs(distance - waveRadius) < waveWidth) {
                const proximity = 1 - Math.abs(distance - waveRadius) / waveWidth;
                const boost = CONFIG.colors.particle.particleBoost;
                this.waveIntensity = Math.max(this.waveIntensity, proximity * intensity * boost);
            }
        }

        draw() {
            ctx.save();

            if (this.waveIntensity > 0.3) {
                if (isAccentActive) {
                    ctx.shadowBlur = CONFIG.colors.particle.shadowBlur;
                    ctx.shadowColor = CONFIG.colors.accent;
                    ctx.fillStyle = CONFIG.colors.accent;
                } else {
                    ctx.fillStyle = isDarkMode ? CONFIG.colors.text.dark : CONFIG.colors.text.light;
                }
                ctx.globalAlpha = this.opacity + (this.waveIntensity * 0.3);
            } else {
                ctx.fillStyle = isDarkMode ? CONFIG.colors.text.dark : CONFIG.colors.text.light;
                ctx.globalAlpha = this.opacity;
            }

            ctx.fillRect(this.x, this.y, this.size, this.size);
            ctx.restore();
        }
    }

    // Create particles
    for (let i = 0; i < particleCount; i++) {
        const x = Math.random() * window.innerWidth;
        const y = Math.random() * window.innerHeight;
        particles.push(new Particle(x, y));
    }

    // Ripple wave system
    let waveRadius = 80;
    const waveSpeed = 2;
    const centerX = window.innerWidth / 2;
    const centerY = window.innerHeight / 2;

    // Animation loop
    function animate() {
        ctx.clearRect(0, 0, window.innerWidth, window.innerHeight);

        waveRadius += waveSpeed;
        if (waveRadius > Math.sqrt(window.innerWidth * window.innerWidth + window.innerHeight * window.innerHeight) / 2) {
            waveRadius = 80;
        }

        particles.forEach(particle => {
            particle.triggerWave(waveRadius, centerX, centerY, isAccentActive ? CONFIG.colors.particle.accentOpacity : CONFIG.colors.particle.baseOpacity);
            particle.update();
            particle.draw();
        });

        requestAnimationFrame(animate);
    }

    // ===== TIMING EVENTS =====
    // Start particles
    setTimeout(() => {
        animate();
    }, ms(timing.particlesDelay));

    // Color transformation
    setTimeout(() => {
        isAccentActive = true;

        // Add accent class to all elements
        ['logoText', 'logoBorder', 'brandName', 'progressBar', 'morph1', 'morph2', 'morph3']
            .forEach(id => document.getElementById(id).classList.add('accent-active'));
    }, ms(timing.colorChangeDelay));

    // ===== 3D MOUSE INTERACTION =====
    const container = document.getElementById('container');

    document.addEventListener('mousemove', (e) => {
        const rect = container.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        const angleX = (e.clientX - centerX) / window.innerWidth * 40;
        const angleY = (e.clientY - centerY) / window.innerHeight * 40;

        container.style.transform = `rotateY(${angleX}deg) rotateX(${-angleY}deg)`;
    });

    document.addEventListener('mouseleave', () => {
        container.style.transform = 'rotateY(0) rotateX(0)';
    });
</script>
</body>
</html>