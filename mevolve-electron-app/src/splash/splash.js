const { BrowserWindow } = require('electron')
const path = require('path')
const APP_CONFIG = require('../config')
const logger = require('../logger')

let splashWindow = null
let splashManager = null

// Splash management class to handle complex splash logic
class SplashManager {
  constructor() {
    this.flutterSplashRemoved = false
    this.minTimeReached = false
    this.onReadyCallback = null
  }

  // Setup splash management with callback for when ready
  setupSplashManagement(onReadyCallback) {
    this.onReadyCallback = onReadyCallback
    
    // Set minimum splash time based on config
    if (APP_CONFIG.splash.minDuration > 0) {
      setTimeout(() => {
        logger.electron.debug(`Minimum splash time (${APP_CONFIG.splash.minDuration}ms) reached`)
        this.minTimeReached = true
        this.checkAndTriggerReady()
      }, APP_CONFIG.splash.minDuration)
    } else {
      logger.electron.debug('No minimum splash time - will show when Flutter ready')
      this.minTimeReached = true
    }
  }

  // Handle Flutter splash removal
  handleFlutterSplashRemoval() {
    logger.flutter.ready('App ready - splash removed')
    this.flutterSplashRemoved = true
    this.checkAndTriggerReady()
  }

  // Check if both conditions are met and trigger ready callback
  checkAndTriggerReady() {
    if (this.flutterSplashRemoved && this.minTimeReached && this.onReadyCallback) {
      logger.important('Both conditions met - showing main window')
      this.onReadyCallback()
    }
  }

  // Reset state for new splash cycle
  reset() {
    this.flutterSplashRemoved = false
    this.minTimeReached = false
    this.onReadyCallback = null
  }
}

// Create splash screen
function createSplashWindow(serverPort = null) {
  logger.electron.debug('Creating splash screen...')
  
  // Don't create multiple splash windows
  if (splashWindow && !splashWindow.isDestroyed()) {
    logger.electron.debug('Splash already exists, skipping creation')
    return
  }
  
  logger.electron.debug('Creating new splash window')
  splashWindow = new BrowserWindow({
    width: 600,
    height: 500,
    frame: false,
    alwaysOnTop: true,
    transparent: true,
    center: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      webSecurity: false  // Allow local file access
    },
    icon: path.join(__dirname, '../../web-dev/icons/Icon-192.png') // Use dev icons for splash
  })

  // Add event listeners for tracking
  splashWindow.once('ready-to-show', () => {
    logger.electron.debug('Splash window ready-to-show')
  })
  
  splashWindow.once('show', () => {
    logger.electron.debug('Splash window shown!')
  })
  
  splashWindow.once('closed', () => {
    logger.electron.debug('Splash window closed')
  })
  
  logger.electron.debug('Loading Electron splash HTML file...')
  
  // Pass config as URL parameters - clean and professional
  const duration = APP_CONFIG.splash.minDuration
  const splashUrl = `file://${path.join(__dirname, 'splash.html')}?duration=${duration}`
  logger.electron.debug(`Loading splash with config: ${duration}ms`)
  
  splashWindow.loadURL(splashUrl)
  
  logger.electron.debug('Showing splash window...')
  splashWindow.show()
  
  logger.electron.debug('Splash screen creation complete')
}

function closeSplash() {
  if (splashWindow) {
    if (!splashWindow.isDestroyed()) {
      logger.electron.info('Closing splash screen')
      try {
        // Force hide first, then close
        splashWindow.hide()
        splashWindow.setAlwaysOnTop(false)
        splashWindow.close()
      } catch (error) {
        logger.electron.error(`Error closing splash: ${error.message}`)
      }
    } else {
      logger.electron.debug('Splash window already destroyed')
    }
    splashWindow = null
  } else {
    logger.electron.debug('No splash window to close')
  }
  
  // Reset splash manager state
  if (splashManager) {
    splashManager.reset()
  }
}

// Initialize splash management
function initializeSplashManagement(onReadyCallback) {
  if (!splashManager) {
    splashManager = new SplashManager()
  }
  splashManager.setupSplashManagement(onReadyCallback)
}

// Handle Flutter splash removal message
function handleFlutterSplashMessage(message) {
  // IMPORTANT: This message must match exactly what's logged in Flutter web build index.html
  // See removeSplashFromWeb() function in Flutter web build
  if (message.includes('Flutter splash elements removed')) {
    if (splashManager) {
      splashManager.handleFlutterSplashRemoval()
    }
    return true // Indicate this message was handled
  }
  return false // Indicate this message was not handled
}

module.exports = { createSplashWindow, closeSplash, initializeSplashManagement, handleFlutterSplashMessage }