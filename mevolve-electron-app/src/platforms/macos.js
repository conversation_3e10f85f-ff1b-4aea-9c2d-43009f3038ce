const { saveWindowState } = require('../windowState')
const path = require('path')
const logger = require('../logger')

// macOS-specific window configuration
function getWindowConfig(baseConfig) {
  return {
    ...baseConfig,
    icon: path.join(__dirname, '../../web-dev/icons/icon.icns'), // macOS uses .icns (dev icons)
    titleBarStyle: 'default'
  }
}

// macOS-specific window behavior
function setupWindowHandlers(mainWindow, stopServer) {
  // Close button = hide window (never quit)
  mainWindow.on('close', (event) => {
    event.preventDefault()
    logger.electron.info('Hiding window (macOS)')
    saveWindowState(mainWindow)
    mainWindow.hide()
  })

  // Window closed handler
  mainWindow.on('closed', () => {
    logger.electron.info('Window closed')
    mainWindow = null
  })
}

// App-level handlers
function setupAppHandlers(app, stopServer) {
  // Quit command = actually quit
  app.on('before-quit', () => {
    logger.electron.info('Quitting app (macOS)')
    stopServer()
    app.exit(0)
  })

  // Keep app running when all windows closed
  app.on('window-all-closed', () => {
    // Don't quit - this is macOS behavior
  })
}

// Handle activate event (dock icon click)
function setupActivateHandler(app, getMainWindow, createWindow) {
  app.on('activate', () => {
    // Show existing hidden window or create new one
    const mainWindow = getMainWindow()
    if (mainWindow === null || mainWindow === undefined) {
      logger.electron.info('No window exists, creating new one')
      createWindow()
    } else if (!mainWindow.isDestroyed()) {
      logger.electron.info('Showing existing window')
      mainWindow.show()
      mainWindow.focus()
    } else {
      logger.electron.info('Window was destroyed, creating new one')
      createWindow()
    }
  })
}

module.exports = { getWindowConfig, setupWindowHandlers, setupAppHandlers, setupActivateHandler }