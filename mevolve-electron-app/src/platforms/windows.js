const { saveWindowState } = require('../windowState')
const path = require('path')
const logger = require('../logger')

// Windows/Linux-specific window configuration
function getWindowConfig(baseConfig) {
  return {
    ...baseConfig,
    icon: path.join(__dirname, '../../web-dev/icons/Icon-192.png'), // Windows uses .png/.ico (dev icons)
    titleBarStyle: 'default'
  }
}

// Windows/Linux-specific window behavior
function setupWindowHandlers(mainWindow, stopServer) {
  // Close button = quit app
  mainWindow.on('close', (event) => {
    event.preventDefault()
    logger.electron.info('Closing app (Windows/Linux)')
    saveWindowState(mainWindow)
    stopServer()
    mainWindow.destroy()
  })

  // Window closed handler
  mainWindow.on('closed', () => {
    logger.electron.info('Window closed')
    mainWindow = null
  })
}

// App-level handlers
function setupAppHandlers(app, stopServer) {
  // Quit when all windows closed
  app.on('window-all-closed', () => {
    app.quit()
  })
}

// Handle activate event (no-op for Windows/Linux)
function setupActivateHandler(app, getMainWindow, createWindow) {
  // Windows/Linux don't have activate behavior
}

module.exports = { getWindowConfig, setupWindowHandlers, setupAppHandlers, setupActivateHandler }