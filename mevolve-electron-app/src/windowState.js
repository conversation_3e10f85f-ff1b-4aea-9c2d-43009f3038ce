const path = require('path')
const fs = require('fs')
const { app } = require('electron')
const logger = require('./logger')

// Default window state
let windowState = {
  width: 1400,
  height: 900,
  x: undefined,
  y: undefined
}

// Load saved window state
function loadWindowState() {
  try {
    const stateFile = path.join(app.getPath('userData'), 'window-state.json')
    if (fs.existsSync(stateFile)) {
      const savedState = JSON.parse(fs.readFileSync(stateFile, 'utf8'))
      windowState = { ...windowState, ...savedState }
    }
  } catch (error) {
    logger.electron.error('Could not load window state: ' + error.message)
  }
}

// Save window state
function saveWindowState(window) {
  try {
    const bounds = window.getBounds()
    const state = {
      width: bounds.width,
      height: bounds.height,
      x: bounds.x,
      y: bounds.y
    }
    const stateFile = path.join(app.getPath('userData'), 'window-state.json')
    fs.writeFileSync(stateFile, JSON.stringify(state, null, 2))
  } catch (error) {
    logger.electron.error('Could not save window state: ' + error.message)
  }
}

function getWindowState() {
  return windowState
}

module.exports = { loadWindowState, saveWindowState, getWindowState }