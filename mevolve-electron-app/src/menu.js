const { Menu, dialog, shell, app } = require('electron')
const APP_CONFIG = require('./config')

// Smart Menu System - Debug vs Production
function createAppMenu() {
  if (!APP_CONFIG.isDebug || !APP_CONFIG.features.menu) {
    // Production: Clean interface, no menu
    Menu.setApplicationMenu(null)
    return
  }

  // Debug: Full developer menu with useful tools
  const template = [
    // App menu (macOS) / File menu (Windows/Linux)
    {
      label: process.platform === 'darwin' ? APP_CONFIG.name : 'File',
      submenu: [
        ...(process.platform === 'darwin' ? [
          { 
            label: `About ${APP_CONFIG.name}`,
            click: () => {
              dialog.showMessageBox({
                type: 'info',
                title: `About ${APP_CONFIG.name}`,
                message: `${APP_CONFIG.name} Desktop`,
                detail: `Version: ${APP_CONFIG.version}\nElectron: ${process.versions.electron}\nNode: ${process.versions.node}\nChrome: ${process.versions.chrome}`
              })
            }
          },
          { type: 'separator' }
        ] : []),
        {
          label: 'Clear App Data',
          click: () => {
            dialog.showMessageBox({
              type: 'warning',
              buttons: ['Clear', 'Cancel'],
              title: 'Clear App Data',
              message: 'This will clear all app data and restart the app. Continue?'
            }).then(result => {
              if (result.response === 0) {
                app.relaunch()
                app.exit()
              }
            })
          }
        },
        {
          label: 'Show App Data Folder',
          click: () => {
            shell.openPath(app.getPath('userData'))
          }
        },
        { type: 'separator' },
        ...(process.platform === 'darwin' ? [
          { role: 'hide' },
          { role: 'hideothers' },
          { role: 'unhide' },
          { type: 'separator' }
        ] : []),
        { role: 'quit' }
      ]
    },

    // Edit menu (essential for copy/paste)
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' },
        { role: 'selectall' }
      ]
    },

    // Developer tools menu
    {
      label: 'Developer',
      submenu: [
        {
          label: 'Toggle DevTools',
          accelerator: process.platform === 'darwin' ? 'Cmd+Option+I' : 'Ctrl+Shift+I',
          click: (item, focusedWindow) => {
            if (focusedWindow) focusedWindow.webContents.toggleDevTools()
          }
        },
        {
          label: 'Reload App',
          accelerator: process.platform === 'darwin' ? 'Cmd+R' : 'Ctrl+R',
          click: (item, focusedWindow) => {
            if (focusedWindow) focusedWindow.reload()
          }
        },
        {
          label: 'Force Reload',
          accelerator: process.platform === 'darwin' ? 'Cmd+Shift+R' : 'Ctrl+Shift+R',
          click: (item, focusedWindow) => {
            if (focusedWindow) focusedWindow.webContents.reloadIgnoringCache()
          }
        },
        { type: 'separator' },
        {
          label: 'Zoom In',
          accelerator: process.platform === 'darwin' ? 'Cmd+Plus' : 'Ctrl+Plus',
          role: 'zoomin'
        },
        {
          label: 'Zoom Out',
          accelerator: process.platform === 'darwin' ? 'Cmd+-' : 'Ctrl+-',
          role: 'zoomout'
        },
        {
          label: 'Reset Zoom',
          accelerator: process.platform === 'darwin' ? 'Cmd+0' : 'Ctrl+0',
          role: 'resetzoom'
        },
        { type: 'separator' },
        {
          label: 'Toggle Fullscreen',
          accelerator: process.platform === 'darwin' ? 'Ctrl+Cmd+F' : 'F11',
          role: 'togglefullscreen'
        }
      ]
    },

    // Window menu
    {
      label: 'Window',
      submenu: [
        { role: 'minimize' },
        { role: 'close' },
        ...(process.platform === 'darwin' ? [
          { type: 'separator' },
          { role: 'front' }
        ] : [])
      ]
    },

    // Help menu
    {
      label: 'Help',
      submenu: [
        {
          label: 'Debug Info',
          click: () => {
            dialog.showMessageBox({
              type: 'info',
              title: 'Debug Information',
              message: 'System Information',
              detail: `Platform: ${process.platform}\nArch: ${process.arch}\nApp Path: ${app.getAppPath()}\nUser Data: ${app.getPath('userData')}\nPackaged: ${app.isPackaged}\nDebug Mode: ${APP_CONFIG.isDebug}`
            })
          }
        }
      ]
    }
  ]

  Menu.setApplicationMenu(Menu.buildFromTemplate(template))
}

module.exports = { createAppMenu }