# How to Verify Linux Build Signatures

This build includes a public GPG key (realtime_public_key.asc) that can be used to verify the authenticity of signed files.

## Prerequisites
- Install GPG: `sudo apt install gnupg` (Ubuntu/Debian) or `sudo yum install gnupg` (RHEL/CentOS)

## Verification Steps

1. Import the public key:
   gpg --import realtime_public_key.asc

2. Verify a signed file:
   gpg --verify <signed_file.AppImage.asc> <signed_file.AppImage>

3. Check the signature output:
   - Look for "Good signature from" message
   - Verify the key ID matches the expected Realtime Innovations Limited key
   - Check for "WARNING: This key is not certified" (normal for self-signed keys)

4. If signature is good: ✅ File is authentic and signed by Realtime Innovations Limited
   If signature fails: ❌ File may have been tampered with or signed by different entity

## Example Commands

### For AppImage files:
gpg --verify myapp.AppImage.asc myapp.AppImage

### For DEB packages:
gpg --verify myapp.deb.asc myapp.deb

### For RPM packages:
gpg --verify myapp.rpm.asc myapp.rpm

### For TAR.GZ archives:
gpg --verify myapp.tar.gz.asc myapp.tar.gz

## GPG Key Details
Organization: Realtime Innovations Limited
Country: Ireland (IE)
Location: Dublin
Email: <EMAIL>

## Important Notes
- Only use the realtime_public_key.asc included with this build
- Never trust packages that don't have valid signatures
- The warning about uncertified key is normal for self-signed keys
- Report any signature verification <NAME_EMAIL>

## Alternative Verification (without importing key)
gpg --keyring ./realtime_public_key.asc --verify <signature_file> <original_file>