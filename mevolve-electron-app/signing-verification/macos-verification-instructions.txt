# How to Verify macOS Build Signatures

This build includes a public certificate (realtime_public_cert.pem) that can be used to verify the authenticity of signed files.

## Prerequisites
- macOS system with codesign and openssl available (pre-installed)

## Verification Steps

1. Verify the file is signed:
   codesign -v <signed_file.app>
   codesign -v <signed_file.dmg>

2. Extract certificate from signed file:
   codesign -d --extract-certificates <signed_file.app>
   # This creates codesign0 file with the certificate

3. Convert extracted certificate to PEM format:
   openssl x509 -inform DER -in codesign0 -out extracted_cert.pem

4. Compare with trusted public certificate:
   diff -q extracted_cert.pem realtime_public_cert.pem

5. Clean up temporary files:
   rm -f codesign0 extracted_cert.pem

## Alternative Verification
Check certificate details directly:
codesign -d -vv <signed_file.app>

## Certificate Details
Organization: Realtime Innovations Limited
Country: Ireland (IE)
Location: Dublin
Email: <EMAIL>

## Important Notes
- Only use the realtime_public_cert.pem included with this build
- Verify the certificate subject matches "Realtime Innovations Limited"
- Report any signature verification <NAME_EMAIL>