# How to Verify Windows Build Signatures

This build includes a public certificate (realtime_public_cert.pem) that can be used to verify the authenticity of signed files.

## Prerequisites
- Install osslsigncode: https://github.com/mtrojnar/osslsigncode

## Verification Steps

1. Verify the file is signed:
   osslsigncode verify <signed_file.exe>

2. Extract certificate from signed file:
   osslsigncode extract-signature -in <signed_file.exe> -out extracted_cert.pem

3. Compare with trusted public certificate:
   diff -q extracted_cert.pem realtime_public_cert.pem

4. If certificates match: ✅ File is authentic and signed by Realtime Innovations Limited
   If certificates differ: ❌ File may have been tampered with or signed by different entity

## Certificate Details
Organization: Realtime Innovations Limited
Country: Ireland (IE)
Location: Dublin
Email: <EMAIL>

## Important Notes
- Only use the realtime_public_cert.pem included with this build
- Never trust executables that don't match this certificate
- Report any signature verification <NAME_EMAIL>