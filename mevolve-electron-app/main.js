const { app, BrowserWindow } = require('electron')
const path = require('path')

// Request single instance lock FIRST before any other imports
const gotTheLock = app.requestSingleInstanceLock()

if (!gotTheLock) {
  // Another instance is already running, quit immediately
  app.quit()
  return // Stop execution here
}

// Only import modules after confirming we have the lock
const APP_CONFIG = require('./src/config')
const logger = require('./src/logger')
const { startServer, stopServer } = require('./src/server')
const { loadWindowState, saveWindowState, getWindowState } = require('./src/windowState')
const { createSplashWindow, closeSplash, initializeSplashManagement, handleFlutterSplashMessage } = require('./src/splash/splash')
const { createAppMenu } = require('./src/menu')
const { getPlatformHandlers } = require('./src/platforms')

// Handle when another instance tries to run
app.on('second-instance', (event, commandLine, workingDirectory) => {
  logger.electron.info('Second instance attempted to start')
  // If we have a window, focus it
  if (mainWindow) {
    if (mainWindow.isMinimized()) mainWindow.restore()
    if (!mainWindow.isVisible()) mainWindow.show()
    mainWindow.focus()
  }
})

// Performance optimizations
app.commandLine.appendSwitch('--enable-features', 'VaapiVideoDecoder')
app.commandLine.appendSwitch('--disable-backgrounding-occluded-windows')
app.commandLine.appendSwitch('--disable-background-timer-throttling')
app.commandLine.appendSwitch('--disable-renderer-backgrounding')
app.commandLine.appendSwitch('--disable-features', 'TranslateUI')
app.commandLine.appendSwitch('--disable-dev-shm-usage')
app.commandLine.appendSwitch('--disable-web-security', 'false')

// GPU acceleration for better Flutter CanvasKit performance
app.commandLine.appendSwitch('--enable-gpu-rasterization')
app.commandLine.appendSwitch('--enable-zero-copy')
app.commandLine.appendSwitch('--enable-hardware-overlays')
app.commandLine.appendSwitch('--enable-features', 'VizDisplayCompositor')

let mainWindow = null

// Get platform-specific handlers once
const platformHandlers = getPlatformHandlers()

async function createWindow() {
  // Prevent multiple window creation
  if (mainWindow && !mainWindow.isDestroyed()) {
    mainWindow.show()
    mainWindow.focus()
    return
  }
  
  // Load window state
  loadWindowState()
  const windowState = getWindowState()
  
  try {
    // Start local server first
    const port = await startServer()
    
    // Show splash screen with server port
    createSplashWindow(port)
    
    // Create the main browser window with saved state
    const baseConfig = {
      width: windowState.width,
      height: windowState.height,
      x: windowState.x,
      y: windowState.y,
      minWidth: 800,
      minHeight: 600,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        webSecurity: true,
        enableRemoteModule: false,
        enableWebSQL: false,
        experimentalFeatures: false,
        spellcheck: false,
        backgroundThrottling: false,
        sandbox: false,
        contentSecurityPolicy: "default-src * 'unsafe-inline' 'unsafe-eval' data: blob:;"
      },
      show: false,
      autoHideMenuBar: true,
      title: APP_CONFIG.name,
      useContentSize: true,
      enableLargerHeapSpace: true,
      center: true,
      movable: true,
      resizable: true
    }
    
    mainWindow = new BrowserWindow(platformHandlers.getWindowConfig(baseConfig))

    // Save window state on resize/move
    mainWindow.on('resize', () => saveWindowState(mainWindow))
    mainWindow.on('move', () => saveWindowState(mainWindow))

    // Load the Flutter web app via HTTP server
    logger.electron.debug('Loading main window in background...')
    await mainWindow.loadURL(`http://localhost:${port}`)
    logger.electron.debug('Flutter web app loaded')

    // Initialize splash management with callback
    const showMainWindow = () => {
      closeSplash()
      mainWindow.show()
      mainWindow.focus()
    }
    
    initializeSplashManagement(showMainWindow)
    
    // Global Flutter console message handler - captures ALL Flutter logs
    mainWindow.webContents.on('console-message', (event, level, message) => {
      // Handle splash removal (critical for app functionality)
      if (handleFlutterSplashMessage(message)) {
        return // Message was handled by splash module
      }
      
      // Log all other Flutter messages (debug mode only)
      logger.flutter.log(message, level)
    })

    // Handle load errors
    mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription) => {
      logger.electron.error(`Failed to load: ${errorDescription}`)
    })

    // Open DevTools in debug mode if enabled
    if (APP_CONFIG.isDebug && APP_CONFIG.features.devTools) {
      // mainWindow.webContents.openDevTools() // Uncomment to auto-open DevTools
    }

    // Setup platform-specific window behavior
    platformHandlers.setupWindowHandlers(mainWindow, stopServer)

  } catch (error) {
    logger.electron.error(`Failed to create window: ${error.message}`)
    closeSplash()
    app.quit()
  }
}

// Setup platform-specific app behavior
platformHandlers.setupAppHandlers(app, stopServer)

app.whenReady().then(() => {
  // Debug logging for configuration
  if (APP_CONFIG.isDebug) {
    logger.electron.info('🔧 DEBUG MODE ENABLED')
    logger.electron.debug('📊 Config: ' + JSON.stringify(APP_CONFIG, null, 2))
  }
  
  createWindow()
  createAppMenu()
  
  // Setup platform-specific activate behavior
  platformHandlers.setupActivateHandler(app, () => mainWindow, createWindow)
})