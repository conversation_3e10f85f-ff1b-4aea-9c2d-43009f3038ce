#!/bin/bash

# Start Firebase emulators in the background
firebase emulators:start &

# Wait for a short period to ensure Firebase emulators start properly
sleep 5

# Change directory to 'functions'
cd functions

# Start TypeScript compiler in watch mode
node_modules/.bin/tsc -p tsconfig.json --watch

# Optionally, bring the Firebase emulators back to the foreground
# if you want to be able to stop them with Ctrl+C
# fg %1
