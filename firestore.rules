rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    function isSupportUser() {
      return "isSupportUser" in request.auth.token && request.auth.token.isSupportUser;
    }
    function hashEmail(email) {
        let hash = hashing.sha256(email).toHexString().lower();
        return hash;
    }
    match /habitActions/{document=**} {
          allow read: if request.auth.uid == resource.data.uid || resource.data.members.memberHashedEmails.hasAny([hashEmail(request.auth.token.email)]) || isSupportUser();
    }
    match /habitSetups/{document=**} {
          allow read: if request.auth.uid == resource.data.uid || resource.data.members.memberHashedEmails.hasAny([hashEmail(request.auth.token.email)]) || isSupportUser();
    }
    match /journalActions/{document=**} {
          allow read: if request.auth.uid == resource.data.uid || resource.data.members.memberHashedEmails.hasAny([hashEmail(request.auth.token.email)]) || isSupportUser();
    }
    match /journalSetups/{document=**} {
          allow read: if request.auth.uid == resource.data.uid || resource.data.members.memberHashedEmails.hasAny([hashEmail(request.auth.token.email)]) || isSupportUser();
    }
    match /lists/{document=**} {
          allow read: if resource.data.isPublic == true || request.auth.uid == resource.data.uid || resource.data.members.memberHashedEmails.hasAny([hashEmail(request.auth.token.email)]) || isSupportUser();
    }
    match /invitedUsers/{document=**} {
        allow read: if true;
    }
    match /todos/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || resource.data.members.memberHashedEmails.hasAny([hashEmail(request.auth.token.email)]) || isSupportUser();
    }
    match /notes/{document=**} {
          allow read: if resource.data.isPublic == true || request.auth.uid == resource.data.uid || resource.data.members.memberHashedEmails.hasAny([hashEmail(request.auth.token.email)]) || isSupportUser();
    }
    match /calendarIntegrations/{document=**} {
        allow read: if request.auth.uid == resource.data.uid;
    }
    match /calendarEventSetups/{document=**} {
        allow read: if request.auth.uid == resource.data.uid;
    }
    match /calendarEventActions/{document=**} {
        allow read: if request.auth.uid == resource.data.uid;
    }
    match /viewSettings/{document=**} {
            allow read: if request.auth.uid == resource.data.uid || isSupportUser();
    }
    match /userResources/{document=**} {
            allow read: if request.auth.uid == resource.data.uid;
    }
    match /users/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || isSupportUser();
    }
    match /specialActivities/{document=**} {
            allow read: if hashEmail(request.auth.token.email) == resource.data.activityForEmailHash;
    }
    match /otpAuth/{document=**} {
        allow read, write: if false;
    }
    match /supportUsers/{document=**} {
        allow read: if request.auth.token.email.matches('.*@realtime-innovations[.]com');
        allow create, write: if request.auth.token.email.matches('.*@realtime-innovations[.]com') && request.auth.uid == request.resource.data.sid && (!request.resource.data.keys().hasAny(['permissions']));
        allow update: if request.auth.token.email.matches('.*@realtime-innovations[.]com') && request.auth.uid == request.resource.data.sid && (!request.resource.data.diff(resource.data).affectedKeys().hasAny(['permissions']));
    }
    match /chatUsers/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || isSupportUser();
        allow write: if request.auth.uid == request.resource.data.uid || isSupportUser();
    }
    match /chatMessages/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || isSupportUser();
        allow write: if request.auth.uid == request.resource.data.uid || isSupportUser();
    }
    match /issueReports/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || isSupportUser();
        allow write: if request.auth.uid == request.resource.data.uid || isSupportUser();
    }
    match /usersFeedback/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || isSupportUser();
        allow write: if request.auth.uid == request.resource.data.uid || isSupportUser();
    }
    match /inAppNotifications/{document=**} {
            allow read: if request.auth.uid == resource.data.uid;
            allow write: if request.auth.uid == request.resource.data.uid;
        }
    match /deletedUsersReports/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || isSupportUser();
        allow write: if request.auth.uid == request.resource.data.uid || isSupportUser();
    }
    match /snippets/{document=**} {
        allow read: if request.auth.uid != null;
        allow write: if isSupportUser();
    }
    match /viewedSnippets/{document=**} {
        allow read: if request.auth.uid == resource.data.uid;
    }
    match /snippetsMetadata/{document=**} {
        allow read, write: if isSupportUser();
    }
    match /usersMetadata/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || request.auth.uid == resource.data.id || isSupportUser();
        allow write: if isSupportUser();
    }
    match /userKeys/{document=**} {
        allow read: if isSupportUser();
    }
    match /serverMetadata/{document=**} {
        allow read, write: if isSupportUser();
    }
    match /appEvals/{document=**} {
        allow read: if request.auth.uid != null;
    }
    match /dataSnapshots/{document=**} {
        allow read: if request.auth.uid != null;
    }
    match /releaseConfigs/{document=**} {
        allow read: if true;
    }
    match /moneyTrackerTransactions/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || resource.data.members.memberHashedEmails.hasAny([hashEmail(request.auth.token.email)]);
    }
    match /moneyTrackerSetups/{document=**} {
        allow read: if request.auth.uid == resource.data.uid || resource.data.members.memberHashedEmails.hasAny([hashEmail(request.auth.token.email)]);
    }
    match /publicUsers/{document=**} {
           allow read: if request.auth.uid == resource.data.uid;
        }
    match /publicUserRelations/{document=**} {
           allow read: if request.auth.uid != null;
      }
    match /userSegments/{document=**} {
        allow read: if isSupportUser();
        allow write: if isSupportUser();
    }
    match /userSegmentActivities/{document=**} {
        allow read: if isSupportUser();
        allow write: if isSupportUser();
    }
    match /paymentTransactions/{document=**} {
        allow read: if isSupportUser();
        allow write: if isSupportUser();
    }
  }
}