{"indexes": [{"collectionGroup": "appEvals", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "calendarEventActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "calendarEvents", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "calendarEventSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "calendarIntegrations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "chatMessages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatMessages", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "lastSid", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "status", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "lastSid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "lastSid", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "lastSid", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "lastSid", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "lastSid", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "clarify", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "chatUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deletedUsersReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "deletedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deletedUsersReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "deletedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deletedUsersReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "deletedAt", "order": "DESCENDING"}]}, {"collectionGroup": "deletedUsersReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "reason", "order": "ASCENDING"}, {"fieldPath": "deletedAt", "order": "DESCENDING"}]}, {"collectionGroup": "habitActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "habitActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "habitActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "habitActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "habitActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "habitSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "deletedAt", "order": "ASCENDING"}, {"fieldPath": "permaDeletedAt", "order": "ASCENDING"}]}, {"collectionGroup": "habitSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "habitSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "habitSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "habitSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "habitSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "inAppNotifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "deletedAt", "order": "ASCENDING"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "inAppNotifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "invitedUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "acceptanceType", "order": "ASCENDING"}, {"fieldPath": "acceptedUserId", "order": "ASCENDING"}, {"fieldPath": "inviteType", "order": "ASCENDING"}, {"fieldPath": "invitedAt", "order": "DESCENDING"}, {"fieldPath": "shortLinkCode", "order": "DESCENDING"}]}, {"collectionGroup": "issueReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "issueReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lastSid", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "issueReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "lastSid", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "issueReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "issueReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "issueReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "reportedAt", "order": "ASCENDING"}]}, {"collectionGroup": "issueReports", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalActions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "journalSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "deletedAt", "order": "ASCENDING"}, {"fieldPath": "permaDeletedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "journalSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "lists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "deletedAt", "order": "ASCENDING"}, {"fieldPath": "permaDeletedAt", "order": "ASCENDING"}]}, {"collectionGroup": "lists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "lists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "lists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "lists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "lists", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "moneyTrackerSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "moneyTrackerSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "moneyTrackerSetups", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "moneyTrackerTransactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "moneyTrackerTransactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "moneyTrackerTransactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "notes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "deletedAt", "order": "ASCENDING"}, {"fieldPath": "permaDeletedAt", "order": "ASCENDING"}]}, {"collectionGroup": "notes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "notes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "notes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "notes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "notes", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "publicUserRelations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "followerId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "publicUserRelations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "publicUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "publicUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.dev.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "dbVersion", "order": "ASCENDING"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.dev.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.hotfix.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "dbVersion", "order": "ASCENDING"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.hotfix.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.prod.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "dbVersion", "order": "ASCENDING"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.prod.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.qa.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "dbVersion", "order": "ASCENDING"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.qa.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.staging.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "dbVersion", "order": "ASCENDING"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "releaseConfigs", "queryScope": "COLLECTION", "fields": [{"fieldPath": "envs.staging.enabledPlatforms", "arrayConfig": "CONTAINS"}, {"fieldPath": "rid", "order": "DESCENDING"}]}, {"collectionGroup": "snippets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "snippets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "snippets", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "position", "order": "ASCENDING"}]}, {"collectionGroup": "specialActivities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "activityForEmailHash", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "supportUsers", "queryScope": "COLLECTION", "fields": [{"fieldPath": "sid", "order": "ASCENDING"}, {"fieldPath": "lastLogin", "order": "DESCENDING"}]}, {"collectionGroup": "todos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "deletedAt", "order": "ASCENDING"}, {"fieldPath": "permaDeletedAt", "order": "ASCENDING"}]}, {"collectionGroup": "todos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "todos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "todos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "members.memberHashedEmails", "arrayConfig": "CONTAINS"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "todos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "todos", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "userResources", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionInfo.entitlement", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.subscriptionExpDate", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionInfo.entitlement", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.subscriptionStartDate", "order": "ASCENDING"}, {"fieldPath": "userInfo.storageUsed", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionInfo.subscriptionState", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.subscriptionExpDate", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionInfo.subscriptionState", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.subscriptionExpDate", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.unsubscribedAt", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionInfo.subscriptionState", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.unsubscribedAt", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.subscriptionExpDate", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionInfo.subscriptionState", "order": "ASCENDING"}, {"fieldPath": "userInfo.deletedAt", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "subscriptionState", "order": "ASCENDING"}, {"fieldPath": "deletedAt", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "userSegmentActivities", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userSegmentId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "userSegments", "queryScope": "COLLECTION", "fields": [{"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "emotion", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "emotion", "order": "ASCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "emotion", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "isArchived", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "isArchived", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "emotion", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "emotion", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "emotion", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "emotion", "order": "ASCENDING"}, {"fieldPath": "isArchived", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isArchived", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isArchived", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "localUpdatedAt", "order": "DESCENDING"}, {"fieldPath": "emotion", "order": "DESCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isArchived", "order": "ASCENDING"}, {"fieldPath": "emotion", "order": "ASCENDING"}]}, {"collectionGroup": "usersFeedback", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "appSettings.appTheme", "order": "ASCENDING"}, {"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "appSettings.theme", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "chatStatus", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "notificationSettings.isDailyAgendaEmail", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "notificationSettings.isDailyAgendaEmailNotificationEnabled", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "notificationSettings.isDailyAgendaMobile", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "notificationSettings.isDailyAgendaMobileNotificationEnabled", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "reportStatus", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "securitySettings.isBiometricEnabled", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "securitySettings.isPasscodeEnabled", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.entitlement", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.expiresAt", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.state", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "subscriptionInfo.subscriptionState", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "superSubscription", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "userDeletedStatus", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}, {"fieldPath": "subscriptionInfo.subscriptionExpDate", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "userInfo.createdAt", "order": "DESCENDING"}, {"fieldPath": "userInfo.name", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "userInfo.name", "order": "ASCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "id", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "usersMetadata", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "localUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "viewSettings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "ASCENDING"}]}, {"collectionGroup": "viewSettings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "doc<PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "viewSettings", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uid", "order": "ASCENDING"}, {"fieldPath": "cloudUpdatedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}