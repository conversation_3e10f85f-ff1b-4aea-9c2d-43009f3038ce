{"scripts": {"lint": "eslint --ext .js,.ts .", "wdio": "wdio run ./dist/wdio.conf.js", "build": "tsc", "start": "npm run build && npm run wdio && allure generate allure-results --clean", "testWithConfig": "npx ts-node test.conf.ts", "report": "allure open"}, "dependencies": {"@cucumber/cucumber": "^9.1.0", "@sendgrid/mail": "^7.7.0", "@sinonjs/fake-timers": "^10.0.2", "@types/webdriverio": "^5.0.0", "@wdio/appium-service": "^8.8.8", "@wdio/cli": "^8.8.8", "@wdio/cucumber-framework": "^8.8.7", "@wdio/local-runner": "^8.8.8", "@wdio/mocha-framework": "^8.8.7", "@wdio/spec-reporter": "^8.8.7", "@wdio/sync": "^7.27.0", "@wdio/types": "^8.1.2", "allure-commandline": "^2.23.0", "appium": "^2.0.0-beta.52", "appium-flutter-driver": "^1.14.3", "appium-flutter-finder": "^0.1.0", "chromedriver": "^112.0.0", "date-fns": "^2.30.0", "dotenv": "^16.3.1", "expect-webdriverio": "^4.1.3", "jszip": "^3.10.1", "multiple-cucumber-html-reporter": "*", "node-fetch": "^2.6.12", "wdio-chromedriver-service": "^8.1.1", "wdio-cucumberjs-json-reporter": "^5.1.5", "wdio-wait-for": "^3.0.3", "webdriverio": "^8.2.3"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.6.0", "@typescript-eslint/parser": "^6.6.0", "@wdio/allure-reporter": "^8.12.3", "concurrently": "^8.2.0", "eslint": "^8.35.0", "eslint-config-google": "^0.14.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-unused-imports": "^3.0.0", "ts-node": "^10.9.1", "typescript": "^4.9.5"}}