Feature: Mevolve app

  Scenario Outline: As a user, I can log into the secure area
    Given I am on the login screen
    When I login with using an email
    Then I should land on Today screen

  Scenario Outline: As a user, I can add todos
    Given I am on the today screen to add todo
    When I click on FAB and select 'Add Todo' and add data with Today's date and clicked Save
    Then I should see added todo on Today screen

  Scenario Outline: As a user, I can mark done on an added todo
    Given I am on the today screen with an added TODO
    When I click on the radio button present with that TODO
    Then I should see that TODO should be turned to Done or checked state

  Scenario Outline: As a user, I can edit todos
    Given I am on the today screen with an added TODO to be edited
    When I click on that particular todo and after doing changes and saving
    Then I should see edited todo on Today screen

  Scenario Outline: As a user, I can delete todos
    Given I am on the today screen with an added TODO to be deleted
    When I click on that particular todo and after performing delete action
    Then I should not see that todo in today screen

  @skipScenario
  Scenario Outline: As a user, I can add notes
    Given I am on the today screen to add notes
    When I click on FAB and select 'Add Note' and add data with Today's date and clicked Save
    Then I should see added note on Today screen

  @skipScenario
  Scenario Outline: As a user, I can edit notes
    Given I am on the today screen with an added Note
    When I click on that particular note and after doing changes and saving
    Then I should see edited note on Today screen

  @skipScenario
  Scenario Outline: As a user, I can delete notes
    Given I am on the today screen with an added NOTE to be deleted
    When I click on that particular note and after performing delete action
    Then I should not see that note in today screen

  @skipScenario
  Scenario Outline: As a user, I can add journal
    Given I am on the today screen to add journal
    When I click on FAB and select 'Add Journal' and add data with Today's date and clicked Save
    Then I should see added journal on Today screen

  @skipScenario
  Scenario Outline: As a user, I can edit journal
    Given I am on the today screen with an added Journal
    When I click on that particular journal and after doing changes and saving
    Then I should see edited journal on Today screen

  @skipScenario
  Scenario Outline: As a user, I can delete journal
    Given I am on the today screen with an added journal to be deleted
    When I click on that particular journal and after performing delete action
    Then I should not see that journal in today screen

  Scenario Outline: As a user, I can add 'Yes or No' habits
    Given I am on the today screen to add 'Yes or No' habits
    When I click on FAB and select 'Setup Habit' and add a 'Yes or No' habit with Today's date and clicked Save
    Then I should see added 'Yes or No' habit on Today screen

  Scenario Outline: As a user, I can edit 'Yes or No' habit
    Given I am on the today screen with an added 'Yes or No' habit
    When I click on that particular 'Yes or No' habit and after doing changes and saving
    Then I should see edited 'Yes or No' habit on Today screen

  Scenario Outline: As a user, I can delete 'Yes or No' habit
    Given I am on the today screen with an added 'Yes or No' habit to be deleted
    When I click on that particular 'Yes or No' habit and after performing delete action
    Then I should not see that 'Yes or No' habit in today screen

  Scenario Outline: As a user, I can add 'Numerical' habits
    Given I am on the today screen to add 'Numerical' habits
    When I click on FAB and select 'Setup Habit' and add a 'Numerical' habit with Today's date and clicked Save
    Then I should see added 'Numerical' habit on Today screen

  Scenario Outline: As a user, I can edit 'Numerical' habit
    Given I am on the today screen with an added 'Numerical' habit
    When I edit that 'Numerical' habit and after performing actions
    Then I should see edited 'Numerical' habit on Today screen

  Scenario Outline: As a user, I can delete 'Numerical' habit
    Given I am on the today screen with an added 'Numerical' habit to be deleted
    When I click on that particular 'Numerical' habit and after performing delete action
    Then I should not see that 'Numerical' habit in today screen

  @skipScenario
  Scenario Outline: As a user, I can add 'TimeType' habits
    Given I am on the today screen to add 'TimeType' habits
    When I click on FAB and select 'Setup Habit' and add a 'TimeType' habit with Today's date and clicked Save
    Then I should see added 'TimeType' habit on Today screen

  @skipScenario
  Scenario Outline: As a user, I can edit 'TimeType' habit
    Given I am on the today screen with an added 'TimeType' habit
    When I edit that 'TimeType' habit and after performing actions
    Then I should see edited 'TimeType' habit on Today screen

  @skipScenario
  Scenario Outline: As a user, I can delete 'TimeType' habit
    Given I am on the today screen with an added 'TimeType' habit to be deleted
    When I click on that particular 'TimeType' habit and after performing delete action
    Then I should not see that 'TimeType' habit in today screen

  Scenario Outline: As a user, I can add 'Multi Choice' habits
    Given I am on the today screen to add 'Multi Choice' habits
    When I click on FAB and select 'Setup Habit' and add a 'Multi Choice' habit with Today's date and clicked Save
    Then I should see added 'Multi Choice' habit on Today screen

  Scenario Outline: As a user, I can edit 'Multi Choice' habit
    Given I am on the today screen with an added 'Multi Choice' habit
    When I edit that 'Multi Choice' habit and after performing actions
    Then I should see edited 'Multi Choice' habit on Today screen

  Scenario Outline: As a user, I can delete 'Multi Choice' habit
    Given I am on the today screen with an added 'Multi Choice' habit to be deleted
    When I click on that particular 'Multi Choice' habit and after performing delete action
    Then I should not see that 'Multi Choice' habit in today screen

  Scenario Outline: As a user, I can add 'Single Choice' habits
    Given I am on the today screen to add 'Single Choice' habits
    When I click on FAB and select 'Setup Habit' and add a 'Single Choice' habit with Today's date and clicked Save
    Then I should see added 'Single Choice' habit on Today screen

  Scenario Outline: As a user, I can edit 'Single Choice' habit
    Given I am on the today screen with an added 'Single Choice' habit
    When I edit that 'Single Choice' habit and after performing actions
    Then I should see edited 'Single Choice' habit on Today screen

  Scenario Outline: As a user, I can delete 'Single Choice' habit
    Given I am on the today screen with an added 'Single Choice' habit to be deleted
    When I click on that particular 'Single Choice' habit and after performing delete action
    Then I should not see that 'Single Choice' habit in today screen

  @skipScenario
  Scenario Outline: As a user, I can filter past todo list based on available datefilters
    Given I am on the past screen
    When I select different date filters
    Then I should see added todo list based on selected date filter

  @skipScenario
  Scenario Outline: As a user, I can filter past todo list based on available statusfilters
    Given I am on the past screen1
    When I select different status filters
    Then I should see added todo list based on selected status filter

  @skipScenario
  Scenario Outline: As a user, I can mark done todo's from insight page
    Given I am on the insight screen
    When I select a todo and mark it as done
    Then I should see that todo as completed in insight page

  @skipScenario
  Scenario Outline: As a user, I can filter past todo list based on available hashagfilters
    Given I am on the past screen2
    When I select different hashtag filters
    Then I should see added todo list based on selected hashtag filter

  @skipScenario
  Scenario Outline: As a user, I can filter past note list based on available moodfilters
    Given I am on the past screen with notes
    When I select different mood filters
    Then I should see added note list based on selected mood filter

  @skipScenario
  Scenario Outline: As a user, I can filter past habit list based on available habitfilters
    Given I am on the past screen with habits
    When I select different habits from filters
    Then I should see selected habit actions only

  @skipScenario
  Scenario Outline: As a user,  I can perform habit action's from insight page
    Given I am on the insight screen with habits
    When I select a habit and mark it as done for some particular days
    Then I should see that habit action as completed in insight page for those particular days

  @skipScenario
  Scenario Outline: As a user, I can filter past journal list based on available journalfilters
    Given I am on the past screen with journals
    When I select different journals from filters
    Then I should see selected journal actions only

  @skipScenario
  Scenario Outline: As a user,  I can add journal details from insight page
    Given I am on the insight screen with journals
    When I select a journal and edit some details for some particular days
    Then I should see that journal in insight page for those particular days

  @skipScenario
  Scenario Outline: As a user, I can perform filter combinations in past lists
    Given I am on the past screen with todo,habits,journals,notes
    When I select different combinations from filters for every list
    Then I should see list result based on selected filter combinations only

  @skipScenario
  Scenario Outline: As a user, I can perform filters in future todo list
    Given I am on the future page Todo section
    When I add todo with future date range and perform different filters
    Then I should see added future todo list based on selected filters

  @skipScenario
  Scenario Outline: As a user, I can add active,upcoming,completed habit setups
    Given I am on the futute page with habit setup
    When I add habits with different date range
    Then I should see added habits under correct upcomimg,active,completed sections correctly

  @skipScenario
  Scenario Outline: As a user, I can add active,upcoming,completed journal setups
    Given I am on the futute page with journal setup
    When I add journals with different date range
    Then I should see added journals under correct upcomimg,active,completed sections correctly

  @skipScenario
  Scenario Outline: As a user, I want to check different functions in create,edit and delete toast messages
    Given I am on the insight screen with no data
    When I add,edit,delete any todo,notes,habit and journals
    Then I should see corresponding toast messages

  @skipScenario
  Scenario Outline: As a user, I can add overdue todos
    Given I am on the today screen to add an overdue todo
    When I click on FAB and select 'Add Todo' and add data as some past date and clicked Save
    Then I should see added todo on Overdue tab of Today screen

  @skipScenario
  Scenario Outline: As a user, I can mark done on an added overdue todo
    Given I am on the Overdue tab on Today screen with an added overdue TODO
    When I check or uncheck on the radio button present with that overdue TODO
    Then I should see that overdue todo either in past if checked and in overdue tab if unchecked

  @skipScenario
  Scenario Outline: As a user, I can delete overdue todos
    Given I am on the Overdue tab on today screen with an added overdue TODO to be deleted
    When I click on that particular overdue todo and after performing delete action
    Then I should not see that overdue todo in Overdue tab on today screen and on past screen

  @skipScenario
  Scenario Outline: As a user, I can add unscheduled todos
    Given I am on the today screen to add an unscheduled todo
    When I click on FAB and select 'Add Todo' and add data as no date and clicked Save
    Then I should see added todo on unscheduled tab of Today screen

  @skipScenario
  Scenario Outline: As a user, I can mark done on an added unscheduled todo
    Given I am on the unscheduled tab on Today screen with an added unscheduled TODO
    When I check or uncheck on the radio button present with that unscheduled TODO and perform filter based on status
    Then I should see that unscheduled todo in unscheduled tab itself based on action either under 'todo' or 'completed' status

  @skipScenario
  Scenario Outline: As a user, I can delete unscheduled todos
    Given I am on the unscheduled tab on today screen with an added unscheduled TODO to be deleted
    When I click on that particular unscheduled todo and after performing delete action
    Then I should not see that unscheduled todo in unscheduled tab on today screen

  @skipScenario
  Scenario Outline: As a user when I create list
    Given I am on the List screen
    When I create new list
    Then I should see the created list

  @skipScenario
  Scenario Outline: As a user when I rearrange list
    Given I am on the added list page
    When I drag list from list page
    Then I should see the rearranged lists

  @skipScenario
  Scenario Outline: As a user when I search lists
    Given I am on the search text field
    When I pass the lists inputs
    Then I should see the passed list

  @skipScenario
  Scenario Outline: As a user when I check and uncheck items
    Given I am on the added item screen
    When I check and uncheck the items
    Then I should see the checked and unchecked items

  @skipScenario
  Scenario Outline: As a user when I swipe left items
    Given I am on the added items screen
    When I swipe left side in items
    Then I should see only not deleted item

  @skipScenario
  Scenario Outline: As a user, when I create items
    Given I am on item page
    When I add items in the list
    Then I should see the created items

  @skipScenario
  Scenario Outline: As a user I rearrange items
    Given I am on the added item page
    When I drag item from items list
    Then I should see the rearraged items

  @skipScenario
  Scenario Outline: As a user, when I click on check all,uncheck all, update, delete
    Given I am on bottom sheet
    When I tap on check all,uncheck all, update and delete
    Then I should see check all,uncheck all, update and delete

  @skipScenario
  Scenario Outline: As a user, when I click on undo on the toast messages
    Given I am on toast message screen
    When I tap on undo button
    Then I should see the deleted list again

  Scenario Outline: As a user, I can set and change the themes
    Given I am on the hamburger menu1
    When I click on the settings
    Then I should see the settings Page

  Scenario Outline: As a user, I can set the pin
    Given I am n the setting screen
    When I click on mevolve pin
    Then I should see the entering new pin bottom sheet

  Scenario Outline: As a user, I can change theme
    Given I am on the settings screen
    When I click on the theme
    Then I see light and dark themes

  Scenario Outline: As a user, I can change colours
    Given I am on the setting screen1
    When I click on the colours
    Then I should see the choose colour popup

  Scenario Outline: As a user, I can turn on vibration
  Given I am on the settings screen1
  When I click on the vibration
  Then I should see the turned on toggle button
  
  Scenario Outline: As a user, I can set the notifications
    Given I am on the hamburgerr menu
    When I click on notifications
    Then I should see notifications page

  Scenario Outline: As a user, I can mute all devices
    Given I am on the notificaion settings page
    When I click on the mute all devices
    Then mute bottom sheet appears

  Scenario Outline: As a user, I can mute for sometime
    Given I am on the mute bottom sheet
    When I select on the mute timings
    Then I should see the mute timing selected

  Scenario Outline: as a user, I can mute this device
    Given I am on the notification settings page1
    When I click on mute this device
    Then mute bottom sheets appears

  Scenario Outline: As a user, I can mute that for sometime
    Given I am on the mutes bottom sheet
    When I select on the mute timing
    Then I should see the mute timings selected

  Scenario Outline: As a user, I can toggle on daily agenda
    Given I am on the notification settings screen
    When I tap on the daily agenda
    Then I should see the daily agenda toggle button oned

  Scenario Outline: As a user, I can set the time for notifications
    Given I am on the notification settings pages
    When I tap on the time
    Then I should see the timer widget

  Scenario Outline: As a user, I can set the sound
    Given I am on the notification settings pages1
    When I tap on the sound
    Then I should see the available sounds

  Scenario Outline: As a user, I can snooze durations
    Given I am on the notification page
    When I click on snooze duration
    Then I should see the snooze duration timings
  
  Scenario Outline: As a user, I can pin reminder
    Given I am on the notification settings page
    When I tap on the pinned reminder
    Then I should see the pinned reminder toggle button on

  @skipScenario
  Scenario Outline: As a user, when I click on subscription
    Given I am on the hamburger screen now
    When I click on subscription
    Then I should see subscription page

  @skipScenario
  Scenario Outline: As a user, When I click on support chat
    Given I am on the hamburger3 screen
    When I click on support chat
    Then I should see the mevolve support pages

  @skipScenario
  Scenario Outline: As a user, When I click on cross button
    Given I am on the opened support page
    When I click on close icon
    Then I should see the Today main page

  @skipScenario
  Scenario Outline: As a user when I click on account
    Given I am on the account scession
    When I tap on the account
    Then I should see account details

  @skipScenario
  Scenario Outline: As a user when I click on languages
    Given I am on the account details
    When I tap on the languages
    Then I should see the multiple languages

  @skipScenario
  Scenario Outline: As a user when I click on manage button
    Given I am on the account details pages
    When I tap on the manage button
    Then I should see subscription page

  @skipScenario
  Scenario Outline: As a user when I click on delete account
    Given I am on the account details page
    When I tap on delete account button
    Then I should see delete account page

  @skipScenario
  Scenario Outline: As a user, I can report an issue
    Given I am on the hamburger1 menu
    When I click on report an issue
    Then I should see report an issue page

  @skipScenario
  Scenario Outline: As a user, I can report
    Given I am on the report screen
    When I click on the reason
    Then I should see the selected reason

  @skipScenario
  Scenario Outline: As a user, I can add brief issues
    Given I am on the add report screen
    When I click on brief issues
    Then I should see the today pages

  @skipScenario
  Scenario Outline: As a user, I can see the images from the gallary
    Given I am on the hamburger2 menu
    When I click on gallary
    Then I should see the gallary photos

  @skipScenario
  Scenario Outline: As a user, I want to logout
    Given I am on the hamburger menu
    When I click on the logout
    Then I should see login page
