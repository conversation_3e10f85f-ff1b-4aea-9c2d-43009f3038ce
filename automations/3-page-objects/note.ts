
import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from "assert";

class Notes {
  public async addNote() {
    await driver.execute("flutter:waitForTappable", byText("Notes"));
    await driver.elementClick(byText("Notes"));
    await driver.execute("flutter:waitForTappable", by<PERSON><PERSON><PERSON><PERSON><PERSON>("kebabMenu"));
    //5185//appMobile_screens_note_kebabMenu_icon_stateDefault
    //4303//appMobile_screens_note_kebabMenu_icon_actionTap
    await driver.elementClick(byValue<PERSON>ey("kebabMenu"));
    await driver.execute("flutter:waitForTappable", byText("View Configuration"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_noteKebabMenu_2397");//appMobile_screens_note_kebabMenu_bottomsheet_stateDefault
    //bottomSheet_noteKebabMenu_4027//appMobile_screens_note_kebabMenu_bottomsheet_stateWithViewConfirguration
    await driver.elementClick(byText("View Configuration"));
    await driver.execute("flutter:waitForTappable", byText("Description"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_viewConfiguration_5883");//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_stateDefault
    //bottomSheet_noteKebabMenu_6374//appMobile_screens_note_kebabMenu_bottomsheet_actionTapViewConfiguration
    await driver.elementClick(byText("Description"));
    await driver.execute("flutter:waitForTappable", byText("None"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_viewDescription_4955")//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_stateOpenDescriptionBottomsheet
    //bottomSheet_viewConfiguration_2047//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_actionTapDescription
    await driver.elementClick(byText("None"));
    //bottomSheet_viewDescription_2780//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_actionTapDescriptionNone
    await driver.execute("flutter:waitForTappable", byText("Time"));
    await driver.elementClick(byText("Time"));
    await driver.execute("flutter:waitForTappable", byText("Image"));
    await driver.elementClick(byText("Image"));
    await driver.execute("flutter:waitForTappable", byText("Mood"));
    await driver.elementClick(byText("Mood"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_viewConfiguration_5286");//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_stateAllOptionsDisabledAndDescriptionNone
    await driver.elementClick(byValueKey("close"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_noteKebabMenu_3441")//appMobile_screens_note_kebabMenu_bottomsheet_stateNoneViewConfiguration
    await driver.elementClick(byValueKey("close"));
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "New Note_1 " + currentDate;
    const valuee1 = "New Note_1"
    const valuee2 = "New Note_2"
    const fabutton = byValueKey("primaryButton");
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    await driver.execute("flutter:waitForTappable", byValueKey("addNoteTitle"));
    //screen_notes_6385//appMobile_screens_note_actionTapButtonFab
    await driver.execute("smartui.takeScreenshot=bottomSheet_noteAction_5952");
    //S-5678//appMobile_commonUI_note_addAndEditBottomsheet_stateEmpty
    await driver.elementClick(byValueKey("addNoteTitle"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), valuee1);
    await driver.execute("smartui.takeScreenshot=bottomSheet_title_5496");
    //S-5496_bottomSheet_title
    await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
    await driver.elementClick(byValueKey("sendButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("moodSelector"));
    await driver.elementClick(byValueKey("moodSelector"));
    await driver.execute("flutter:waitForTappable", byText("Meh"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_moodSelect_948");
    await driver.elementClick(byText("Meh"));
    try {
      await driver.execute("flutter:waitFor", byValueKey("addHashTag"), 2000);
      await driver.elementClick(byValueKey("addHashTag"));
    } catch (warn) {
      await driver.execute("flutter:waitForTappable", byValueKey("overflowMenu"));
      await driver.elementClick(byValueKey("overflowMenu"));
      await driver.execute("flutter:waitForTappable", byText("Add hashtag"));
      await driver.elementClick(byText("Add hashtag"));
    }
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("#goodmorning"));
    await driver.elementClick(byText("#goodmorning"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    //S-2873_bottomSheet_actionCommon
    //S-6187_bottomSheet_noteAction
    await driver.elementClick(byText("CLOSE"));
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    await driver.execute("flutter:waitForTappable", byValueKey("addNoteTitle"));
    await driver.elementClick(byValueKey("addNoteTitle"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), valuee2);
    await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
    await driver.elementClick(byValueKey("sendButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("moodSelector"));
    await driver.elementClick(byValueKey("moodSelector"));
    await driver.execute("flutter:waitForTappable", byText("Very Sad"));
    await driver.elementClick(byText("Very Sad"));
    try {
      await driver.execute("flutter:waitFor", byValueKey("addHashTag"), 2000);
      await driver.elementClick(byValueKey("addHashTag"));
    } catch (warn) {
      await driver.execute("flutter:waitForTappable", byValueKey("overflowMenu"));
      await driver.elementClick(byValueKey("overflowMenu"));
      await driver.execute("flutter:waitForTappable", byText("Add hashtag"));
      await driver.elementClick(byText("Add hashtag"));
    }
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("#goodmorning"));
    await driver.elementClick(byText("#goodmorning"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    await driver.elementClick(byText("CLOSE"));
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    await driver.execute("flutter:waitForTappable", byValueKey("addNoteTitle"));
    await driver.elementClick(byValueKey("addNoteTitle"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), valuee);
    await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
    await driver.elementClick(byValueKey("sendButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("moodSelector"));
    await driver.elementClick(byValueKey("moodSelector"));
    await driver.execute("flutter:waitForTappable", byText("Very Happy"));
    await driver.elementClick(byText("Very Happy"));
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    await driver.elementClick(byText("CLOSE"));
    await driver.execute("flutter:waitForTappable", byValueKey("kebabMenu"));
    //5185//appMobile_screens_note_kebabMenu_icon_stateDefault
    //4303//appMobile_screens_note_kebabMenu_icon_actionTap
    await driver.elementClick(byValueKey("kebabMenu"));
    await driver.execute("flutter:waitForTappable", byText("View Configuration"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_noteKebabMenu_2397");//appMobile_screens_note_kebabMenu_bottomsheet_stateDefault
    //bottomSheet_noteKebabMenu_4027//appMobile_screens_note_kebabMenu_bottomsheet_stateWithViewConfirguration
    await driver.elementClick(byText("View Configuration"));
    await driver.execute("flutter:waitForTappable", byText("Description"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_viewConfiguration_5883");//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_stateDefault
    //bottomSheet_noteKebabMenu_6374//appMobile_screens_note_kebabMenu_bottomsheet_actionTapViewConfiguration
    await driver.elementClick(byText("Description"));
    await driver.execute("flutter:waitForTappable", byText("Full"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_viewDescription_4955")//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_stateOpenDescriptionBottomsheet
    //bottomSheet_viewConfiguration_2047//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_actionTapDescription
    await driver.elementClick(byText("Full"));
    //bottomSheet_viewDescription_4582//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_actionTapDescriptionFull
    await driver.execute("flutter:waitForTappable", byText("Time"));
    await driver.elementClick(byText("Time"));
    await driver.execute("flutter:waitForTappable", byText("Image"));
    await driver.elementClick(byText("Image"));
    await driver.execute("flutter:waitForTappable", byText("Mood"));
    await driver.elementClick(byText("Mood"));
    await driver.execute("flutter:waitForTappable", byText("Hashtag"));
    await driver.elementClick(byText("Hashtag"));
    //bottomSheet_viewConfiguration_1633//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_actionTapHashtag
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_viewConfiguration_5390");//appMobile_screens_note_kebabMenu_bottomsheet_viewConfigurationBottomSheet_stateAllOptionsEnabled
    await driver.elementClick(byValueKey("close"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
  }

  public async addNoteSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "New Note_1 " + currentDate;
    await driver.execute("flutter:waitForTappable", byText(valuee));
    const elem = await driver.getElementText(byText(valuee));
    assert.strictEqual(elem, valuee);
  }
  public async editNote() {
    console.log("FAB inside editnote1");
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "New Note_1 " + currentDate;
    await driver.elementClick(byText(valuee));
    await driver.execute("flutter:waitForTappable", byValueKey("addNoteTitle"));
    await driver.elementClick(byValueKey("addNoteTitle"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), "Updated " + valuee);
    await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
    await driver.elementClick(byValueKey("sendButton"));
    try {
      await driver.execute("flutter:waitFor", byValueKey("addHashTag"), 2000);
      await driver.elementClick(byValueKey("addHashTag"));
    } catch (warn) {
      await driver.execute("flutter:waitForTappable", byValueKey("overflowMenu"));
      await driver.elementClick(byValueKey("overflowMenu"));
      await driver.execute("flutter:waitForTappable", byText("Add hashtag"));
      await driver.elementClick(byText("Add hashtag"));
      console.log("qwerty");
    }
    await driver.execute("flutter:waitForTappable", byText("#goodmorning"));//S-1610
    await driver.execute("flutter:scroll", byText("#goodmorning"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });// delete hashtag
    await driver.execute("flutter:waitForTappable", byValueKey("editHashTag"));
    ////await waitFor(6500, "bottomSheet_hashtagSelect_4002")
    await driver.execute("smartui.takeScreenshot=bottomSheet_hashtagSelect_4002");
    await driver.elementClick(byValueKey("editHashTag"));//S-4002
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.elementClear(byValueKey('textFieldBottomSheet'))
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), "morning");
    await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
    await driver.elementClick(byValueKey("sendButton"));
    await driver.execute("flutter:waitForTappable", byText("#morning"));
    await driver.execute("flutter:scroll", byText("#morning"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });// delete hashtag
    await driver.execute("flutter:waitForTappable", byValueKey("deleteHashTag"));//S-4002
    await driver.elementClick(byValueKey("deleteHashTag"));
    await driver.execute("flutter:waitForTappable", byText("DELETE"));
    await driver.elementClick(byText("DELETE"));//S-1376
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.execute("flutter:scroll", byText("#smile"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });// delete hashtag
    await driver.execute("flutter:waitForTappable", byValueKey("deleteHashTag"));
    await driver.elementClick(byValueKey("deleteHashTag"));
    await driver.execute("flutter:waitForTappable", byText("DELETE"));//S-3563 ,S-2133
    await driver.elementClick(byText("DELETE"));//S-3898, S-3354
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
    /* await driver.execute('flutter:waitForTappable', byValueKey('addImages'))
        await driver.elementClick(byValueKey('addImages'))
        await driver.switchContext('NATIVE_APP')
        const el10 = await driver.$('//android.widget.FrameLayout[@content-desc=\'Photo taken on 19 Apr 2023, 6:26:44 am\']/androidx.cardview.widget.CardView/android.widget.FrameLayout/android.widget.ImageView')
        await el10.click()
        const el11 = await driver.$('/hierarchy/android.widget.Fra6meLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout[2]/android.widget.Button[2]')
        await el11.click()
        const el12 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.view.View[3]/android.widget.ImageView[1]')
        await el12.click()
        const el13 = await driver.$('//android.widget.FrameLayout[@content-desc=\'Photo taken on 18 Apr 2023, 6:39:25 am\']/androidx.cardview.widget.CardView/android.widget.FrameLayout/android.widget.FrameLayout')
        await el13.click()
        const el14 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout[2]/android.widget.Button[2]')
        await el14.click()
        await driver.switchContext('FLUTTER') */
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    await driver.elementClick(byText("CLOSE"));
  }

  public async editNoteSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const value = "Updated New Note_1 " + currentDate;
    await driver.execute("flutter:waitForTappable", byText(value));
    const elem = await driver.getElementText(byText(value));
    assert.strictEqual(elem, value);
  }

  public async deleteNote() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "New Note_1 " + currentDate;
    const value1 = "Updated " + valuee;
    const valuee1 = "New Note_1"
    const valuee2 = "New Note_2"
    await driver.execute("flutter:waitForTappable", byText(value1));
    await driver.elementClick(byText(value1));
    await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
    await driver.elementClick(byValueKey("trashIcon"));
    await driver.execute("flutter:waitForTappable", byText("CANCEL"));
    //S-1972
    await driver.elementClick(byText("CANCEL"));//S-5235
    await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
    await driver.elementClick(byValueKey("trashIcon"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));//S-26,S-4558
    await driver.execute("flutter:waitForTappable", byText(valuee1));
    await driver.elementClick(byText(valuee1));
    await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
    await driver.elementClick(byValueKey("trashIcon"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));//S-26,S-4558
    await driver.execute("flutter:waitForTappable", byText(valuee2));
    await driver.elementClick(byText(valuee2));
    await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
    await driver.elementClick(byValueKey("trashIcon"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));//S-26,S-4558
  }

  public async NoteFilter() {
    await driver.execute("flutter:waitForTappable", byValueKey("searchFilter"));
    await driver.elementClick(byValueKey("searchFilter"));

    await driver.execute("smartui.takeScreenshot=bottomSheet_search_4123");//appMobile_commonUI_filters_searchFilter_textField_stateEmpty
    //screen_common_2823//appMobile_commonUI_filters_searchFilter_button_actionTap
    await driver.elementSendKeys(byValueKey("searchFilter"), "note_1");
    //bottomSheet_search_1095//appMobile_commonUI_filters_searchFilter_textField_ruleCaseSensitive
    //bottomSheet_search_3031//appMobile_commonUI_filters_searchFilter_textField_ruleEntering
    await driver.execute("flutter:waitForTappable", byTooltip("Delete"));

    await driver.execute("smartui.takeScreenshot=screen_common_1989");//appMobile_commonUI_filters_searchFilter_button_stateUpdated
    //bottomSheet_search_225//appMobile_commonUI_filters_searchFilter_textField_stateEnteringKeyword
    //bottomSheet_search_3361//appMobile_commonUI_filters_searchFilter_textField_stateFilled
    await driver.elementClick(byTooltip("Delete"));
    //screen_common_1875//appMobile_commonUI_filters_searchFilter_button_actionCloseIconTap
    await driver.execute("flutter:waitForTappable", byValueKey("moodFilter"));
    await driver.elementClick(byValueKey("moodFilter"));
    await driver.execute("flutter:waitForTappable", byText("Very Happy"));

    await driver.execute("smartui.takeScreenshot=moodfilter_bottomsheet_871");//appMobile_commonUI_filters_moodFilter_bottomSheet_stateDefault
    await driver.elementClick(byText("Very Happy"));
    await driver.execute("flutter:waitForTappable", byText("Happy"));
    await driver.elementClick(byText("Happy"));
    await driver.execute("flutter:waitForTappable", byText("Very Sad"));
    await driver.elementClick(byText("Very Sad"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("notesTodoFilterList"));
    //
    await driver.execute("smartui.takeScreenshot=moodfilter_result");
    await driver.execute("flutter:scroll", byValueKey("notesTodoFilterList"), { dx: -800, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("hashTagFilter"));
    await driver.elementClick(byValueKey("hashTagFilter"));
    await driver.execute("flutter:waitForTappable", byText("#goodmorning"));
    await driver.elementClick(byText("#goodmorning"));
    // await driver.execute('flutter:scrollUntilVisible', byValueKey('hashTagFilterList'), { item: byText('seashore'), dxScroll: 0, dyScroll: -50 }) // vertical scroll done
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("notesTodoFilterList"));

    await driver.execute("smartui.takeScreenshot=moodfilter&hashtag_result");
    await driver.execute("flutter:scroll", byValueKey("notesTodoFilterList"), { dx: 1200, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("searchFilter"));
    await driver.elementClick(byValueKey("searchFilter"));
    await driver.elementSendKeys(byValueKey("searchFilter"), "new note_2");
    await driver.execute("flutter:waitForTappable", byValueKey("notesTodoFilterList"));
    await driver.execute("smartui.takeScreenshot=moodfilter&hashtag&search_result");
    await driver.execute("flutter:scroll", byValueKey("notesTodoFilterList"), { dx: -1200, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Clear all"));
    await driver.elementClick(byText("Clear all"));
    await driver.execute("flutter:waitForTappable", byValueKey("kebabMenu"));
    await driver.elementClick(byValueKey("kebabMenu"));
    await driver.execute("flutter:waitForTappable", byText("View Configuration"));
    await driver.elementClick(byText("View Configuration"));
    await driver.execute("flutter:waitForTappable", byText("Hashtag"));
    await driver.elementClick(byText("Hashtag"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
  }
}
export default new Notes();