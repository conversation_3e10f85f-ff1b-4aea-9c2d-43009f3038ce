import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
class Logout {

    public async logout() {
        const hamburger = byValue<PERSON>ey('hamburgerMenu')
        await driver.elementClick(hamburger);
        const logout = byText('Logout')
        await driver.execute('smartui.takeScreenshot=Logout_DialougeBox')
        await driver.elementClick(logout);
        const logoutUpper = byText('LOGOUT')
        await driver.elementClick(logoutUpper);
        await driver.execute('smartui.takeScreenshot=Login_Screen')
    }

    public async logoutSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Continue with Google'))
        const element = await driver.getElementText(byText('Continue with Google'))
        console.log('element value is:' + element)
        const valuee = 'Continue with Google'
        assert.strictEqual(element, valuee)
    }
}
export default new Logout();
