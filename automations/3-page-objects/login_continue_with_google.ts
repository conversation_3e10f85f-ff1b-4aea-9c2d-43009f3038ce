import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
import { addStartDate, addtime, addtitle } from "./page";


class LoginPage1 {

  public async loginmevolve() {
    console.log('FAB');
    try {
      await driver.execute('flutter:waitFor', byText('Deny'), 2000)
      await driver.elementClick(byText('Deny'))
    } catch (warn) { console.log('qwerty') }

    console.log('Continue with google')
    await driver.execute('flutter:waitForTappable', byVal<PERSON><PERSON><PERSON>('continueWithGoogle'))
    //await driver.execute('smartui.takeScreenshot=Login_screen')
    await driver.elementClick(byValueKey('continueWithGoogle'))
    /*await driver.switchContext('NATIVE_APP')
    const el6 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.support.v7.widget.RecyclerView/android.widget.LinearLayout[4]/android.widget.LinearLayout')
    await el6.click()
    await driver.switchContext('FLUTTER') */
    console.log('login done')
    console.log('exit when');
  }


  public async addtodo1() {
    ////await setDriver();
    console.log('FAB inside addtodo1');
    const fabutton = byTooltip('increment');
    console.log('FAB button');
    await driver.execute('flutter:waitForTappable', fabutton);
    await driver.elementClick(fabutton);
    console.log('FAB clicked');
    const addtodo = byText('Add Todo');
    await driver.execute('flutter:waitForTappable', addtodo);
    await driver.elementClick(addtodo);
    console.log('Add TODO open');
    const d = new Date();
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear();
    const valuee = 'New Todo_1 ' + currentDate;
    await driver.execute('flutter:waitForTappable', byValueKey('addTitle'))
    //await driver.execute('smartui.takeScreenshot=todo_page') // Screenshot_todo page
    await driver.elementClick(byValueKey('addTitle'));
    await addtitle(valuee)
    ////await setDriver();
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', (byValueKey('todayToggleButton')))
    await driver.elementClick(byValueKey('todayToggleButton'))
  }
  public async edittodo1() {
    console.log('FAB inside edittodo1');
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'New Todo_1 ' + currentDate
    await driver.elementClick(byText(valuee))
    //await driver.execute('flutter:waitForTappable', byValueKey('addTime'))
    await addtime()
    await addStartDate()
    await driver.execute('flutter:waitForTappable', byValueKey('repeatTodo'))
    await driver.elementClick(byValueKey('repeatTodo'))
    console.log('repeat widget')
    await driver.execute('flutter:waitForTappable', byText('Weekly'))
    //await driver.execute('smartui.takeScreenshot=repeat_widget') // Screenshot_repeat
    // if repeat option is Weekly
    /* await driver.elementClick(byText('Weekly'))
    //await waitFor(1000)
    await driver.switchContext('NATIVE_APP')
    const el17 = await driver.$('(//android.view.View[@content-desc="S"])[2]')
    await el17.click()
    const el18 = await driver.$('(//android.view.View[@content-desc="S"])[1]')
    await el18.click()
    const el19 = await driver.$('(//android.view.View[@content-desc="T"])[1]')
    await el19.click()
    const el20 = await driver.$('(//android.view.View[@content-desc="T"])[2]')
    await el20.click()
    await driver.switchContext('FLUTTER')
    //await waitFor(2000) */
    // if repeat option is Yearly
    /* await driver.elementClick(byText('Yearly'))
    //await waitFor(1500)
    await driver.execute('flutter:scroll', byValueKey('yearlyMonthSpinner'), { dx: 50, dy: -100, durationMilliseconds: 200, frequency: 70 })
    //await waitFor(1500)
    await driver.execute('flutter:scroll', byValueKey('yearlyDateSpinner'), { dx: 50, dy: -100, durationMilliseconds: 200, frequency: 20 })
    //await waitFor(1500) */
    // if repeat option is Monthly
    await driver.execute('flutter:waitForTappable', byText('Monthly'))
    await driver.elementClick(byText('Monthly')) // repeatMonthDaySpinner
    await driver.execute('flutter:waitForTappable', byValueKey('daySpinner'))
    //await driver.execute('smartui.takeScreenshot=repeat_monthly_widget') // Screenshot_repeat_monthly
    await driver.execute('flutter:scroll', byValueKey('daySpinner'), { dx: 50, dy: -100, durationMilliseconds: 200, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
  }

  public async deletetodo1() {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'New Todo_1 ' + currentDate
    await driver.elementClick(byText(valuee))
    await driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
    await driver.elementClick(byValueKey('trashIcon'))
    await driver.execute('flutter:waitForTappable', byText('Cancel'))
    await driver.elementClick(byText('Cancel'))
    await driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
    await driver.elementClick(byValueKey('trashIcon'))
    await driver.execute('flutter:waitForTappable', byText('Delete'))
    await driver.elementClick(byText('Delete'))
  }
  public async donetodo1() {
    //await driver.execute('flutter:waitForTappable', byValueKey('addTitle'))
    // //await setDriver();
    console.log('FAB');
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'New Todo_1 ' + currentDate
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    await driver.switchContext('NATIVE_APP')
    const el3 = await driver.$(`//android.view.View[@content-desc="${valuee}"]/android.view.View`)
    await el3.click()
    await driver.switchContext('FLUTTER')
    assert.strictEqual(await driver.getElementText(elem), valuee)
    console.log('Success')
    //await driver.elementClick(byText(valuee))*/
  }

  public async addnote1() {
    ////await setDriver();
    await driver.execute('flutter:waitForTappable', (byValueKey('todayToggleButton')))
    await driver.elementClick(byValueKey('todayToggleButton'))
    console.log('FAB inside addnote1');
    const d = new Date();
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear();
    const valuee = 'New Note_1 ' + currentDate
    const fabutton = byTooltip('increment')
    await driver.execute('flutter:waitForTappable', fabutton)
    console.log('Note1')
    await driver.elementClick(fabutton)

    const addNote = byText('Add Note')
    await driver.execute('flutter:waitForTappable', addNote)
    console.log('Note2')
    await driver.elementClick(addNote)
    await driver.execute('flutter:waitForTappable', byValueKey('addNoteTitle'))
    //await driver.execute('smartui.takeScreenshot=add_note') // Screenshot_repeat_monthly
    await driver.elementClick(byValueKey('addNoteTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    console.log('Note4')
    await driver.elementSendKeys(byValueKey('textFieldBottomSheet'), valuee)
    await driver.execute('flutter:waitForTappable', byValueKey('sendButton'))

    console.log('Note5')
    await driver.elementClick(byValueKey('sendButton'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    console.log('Note6')
    await driver.elementClick(byText('Save'))
    console.log('Note7')
    await driver.execute('flutter:waitForTappable', (byValueKey('todayToggleButton')))
    await driver.elementClick(byValueKey('todayToggleButton'))
  }
  public async editnote1() {
    console.log('FAB inside edittodo1');
    await driver.execute('flutter:scrollIntoView', byValueKey('noteList'), { alignment: 0.5 })
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'New Note_1 ' + currentDate

    await driver.elementClick(byText(valuee))
    await driver.execute('flutter:waitForTappable', byValueKey('moodSelector'))
    await driver.elementClick(byValueKey('moodSelector'))
    await driver.execute('flutter:waitForTappable', byText('Happy'))
    //await driver.execute('smartui.takeScreenshot=mood_selector_widget') // Screenshot_repeat_monthly
    await driver.elementClick(byText('Happy'))
    console.log('Note13')
    await driver.execute('flutter:waitForTappable', byValueKey('addNoteTitle'))
    await driver.elementClick(byValueKey('addNoteTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'Updated ' + valuee)
    await driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await driver.elementClick(byValueKey('sendButton'))
    console.log('Note14')
    /* await driver.execute('flutter:waitForTappable', byValueKey('addImages'))
    await driver.elementClick(byValueKey('addImages'))
  
    await driver.switchContext('NATIVE_APP')
    const el10 = await driver.$('//android.widget.FrameLayout[@content-desc=\'Photo taken on 19 Apr 2023, 6:26:44 am\']/androidx.cardview.widget.CardView/android.widget.FrameLayout/android.widget.ImageView')
    await el10.click()
    const el11 = await driver.$('/hierarchy/android.widget.Fra6meLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout[2]/android.widget.Button[2]')
    await el11.click()
    const el12 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.view.View[3]/android.widget.ImageView[1]')
    await el12.click()
    const el13 = await driver.$('//android.widget.FrameLayout[@content-desc=\'Photo taken on 18 Apr 2023, 6:39:25 am\']/androidx.cardview.widget.CardView/android.widget.FrameLayout/android.widget.FrameLayout')
    await el13.click()
    const el14 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout[2]/android.widget.Button[2]')
    await el14.click()
  
    await driver.switchContext('FLUTTER') */
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))

  }

  public async deletenote1() {

    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'New Note_1 ' + currentDate
    const value1 = 'Updated ' + valuee
    await driver.execute('flutter:scrollIntoView', byValueKey('noteList'), { alignment: 0.5 })
    await driver.execute('flutter:waitForTappable', byText(value1))
    await driver.elementClick(byText(value1))
    await driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
    await driver.elementClick(byValueKey('trashIcon'))
    await driver.execute('flutter:waitForTappable', byText('Cancel'))
    await driver.elementClick(byText('Cancel'))
    await driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
    await driver.elementClick(byValueKey('trashIcon'))
    await driver.execute('flutter:waitForTappable', byText('Delete'))
    await driver.elementClick(byText('Delete'))
    await driver.elementClick(byValueKey('todayToggleButton'))

  }


  public async addjournal1() {
    ////await setDriver();
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()

    const valuee = 'Describe today ' + currentDate
    const fabutton = byTooltip('increment')
    await driver.execute('flutter:waitForTappable', fabutton)
    await driver.elementClick(fabutton)
    const setupjournal = byText('Setup Journal')
    await driver.execute('flutter:waitForTappable', setupjournal)
    await driver.elementClick(setupjournal)

    await driver.execute('flutter:waitForTappable', byValueKey('addJournalTitle'))
    //await driver.execute('smartui.takeScreenshot=journal_page')
    // await driver.execute('flutter:waitFor', byValueKey('addjournalTitle'), 8000)
    await driver.elementClick(byValueKey('addJournalTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await driver.elementSendKeys(byValueKey('textFieldBottomSheet'), valuee)
    await driver.execute('flutter:waitForTappable', byValueKey('sendButton'))

    await driver.elementClick(byValueKey('sendButton'))
    await driver.execute('flutter:waitForTappable', byValueKey('selectJournalDateRange'))

    await driver.elementClick(byValueKey('selectJournalDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    //await driver.execute('smartui.takeScreenshot=daterange_widget')
    await driver.elementClick(byValueKey('startDate'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate'))
    /* await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))
    await driver.execute('flutter:waitForTappable', byValueKey('selectJournalRepeat'))
    await driver.elementClick(byValueKey('selectJournalRepeat'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    // Since conflict for S-Saturday S- Sunday T-Tuesday T-Thursday; taking code from appium inspector
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byValueKey('selectJournalTime'))
    await driver.elementClick(byValueKey('selectJournalTime'))
    await driver.execute('flutter:waitForTappable', byValueKey('timeHourSpinner'))
    // await driver.elementClick(byValueKey('toggle_time'))
    await driver.execute('flutter:scroll', byValueKey('timeHourSpinner'), { dx: 0, dy: 1000, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byValueKey('timeMinSpinner'))
    await driver.execute('flutter:scroll', byValueKey('timeMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byValueKey('selectionAmPm'))
    await driver.execute('flutter:scroll', byValueKey('selectionAmPm'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byText('Set'))
    await driver.elementClick(byText('Set'))
    /* await driver.execute('flutter:waitForTappable', byValueKey('setjournalReminder'))
    await driver.elementClick(byValueKey('setjournalReminder'))
    await driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byText('Set'))
    await driver.elementClick(byText('Set')) */

    await driver.execute('flutter:waitForTappable', byText('Save'))
    // await driver.elementClick(byValueKey('tog_reminder'))
    await driver.elementClick(byText('Save'))
    // journalList
    // //await waitFor(10000)

    await driver.execute('flutter:waitForTappable', (byValueKey('todayToggleButton')))
    await driver.elementClick(byValueKey('todayToggleButton'))
  }
  public async editjournal1() {
    console.log('FAB inside edittodo1');
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'Describe today ' + currentDate

    const elem = byText(valuee)
    await driver.execute('flutter:scrollUntilVisible', byValueKey('toDoLists'), { item: byText('Journals'), dxScroll: 0, dyScroll: 500 })
    await driver.execute('flutter:scrollIntoView', byValueKey('journalList'), { alignment: 1 })
    // await driver.execute('flutter:scrollUntilTapable', byValueKey('journalList'), { item: byText(valuee), dxScroll: 0, dyScroll: -400 })
    //  const elem = byText(valuee)
    await driver.execute('flutter:waitForTappable', elem)
    assert.strictEqual(await driver.getElementText(elem), valuee)
    await driver.elementClick(elem)
    await driver.execute('flutter:waitForTappable', byValueKey('showJournalBottomSheet'))
    await driver.elementClick(byValueKey('showJournalBottomSheet')) // Edit journal setup;Reset journal entry
    await driver.execute('flutter:waitForTappable', byText('Edit journal setup'))
    await driver.elementClick(byText('Edit journal setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('addJournalTitle'))
    await driver.elementClick(byValueKey('addJournalTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'updated' + valuee)
    await driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await driver.elementClick(byValueKey('sendButton'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
  }

  public async deletejournal1() {

    await driver.execute('flutter:waitForTappable', byValueKey('showJournalBottomSheet'))
    await driver.elementClick(byValueKey('showJournalBottomSheet')) // Edit journal setup;Reset journal entry
    await driver.execute('flutter:waitForTappable', byText('Edit journal setup'))
    await driver.elementClick(byText('Edit journal setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))

    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byText('Delete'))
    await driver.elementClick(byText('Delete'))
    await driver.elementClick(byValueKey('todayToggleButton'))
    await driver.elementClick(byValueKey('todayToggleButton'))
    console.log('today_journal done')
  }
  public async addyesornohabit1() {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'Are you happy ? ' + currentDate
    console.log(valuee)

    const fabutton = byTooltip('increment')
    await driver.execute('flutter:waitForTappable', fabutton)
    console.log('Habit ' + valuee)
    await driver.elementClick(fabutton)
    console.log('Habit ' + valuee)
    const setuphabit = byText('Setup Habit')
    await driver.execute('flutter:waitForTappable', setuphabit)
    await driver.elementClick(setuphabit)
    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
    await addtitle(valuee)
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await driver.elementClick(byValueKey('selectHabitDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    //await driver.execute('smartui.takeScreenshot=daterangepicker') // Screenshot_bottomsheet
    await driver.elementClick(byValueKey('startDate')) // Today;Next Saturday;Next Sunday;After 1 week;
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save')) // Today is selected
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends;15 days later;30 days later;60 days later
    /*await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitRepeat'))
    await driver.elementClick(byValueKey('selectHabitRepeat'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    //await driver.execute('smartui.takeScreenshot=habit_repeat') // Screenshot_bottomsheet
    await driver.elementClick(byText('Save')) // if need specific days need to use alphabets and for Tuesday;Thursday and Saturday;Sunday need appium code
    //await addtime()
    /* await driver.elementClick(byValueKey('sethabitReminder'))
    //await waitFor(2000)
    await driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    //await waitFor(2000)
    await driver.elementClick(byText('Set'))
    //await waitFor(2000) */
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    // await driver.elementClick(byValueKey('todaytoggleButton'))
    // //await waitFor(2000)
    // await driver.elementClick(byValueKey('todayToggleButton'))

    //await driver.elementClick(byValueKey('todayToggleButton'))
  }
  public async editYesorNoHabitsToday() {

    await driver.execute('flutter:scrollIntoView', byValueKey('habitList'), { alignment: 1 })
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'Are you happy ? ' + currentDate
    const valuee1 = 'Updated_Are you happy ? ' + currentDate
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    assert.strictEqual(await driver.getElementText(elem), valuee)
    console.log('Success')
    await driver.execute('flutter:waitForTappable', byText(valuee))
    await driver.elementClick(byText(valuee))
    await driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await driver.elementClick(byValueKey('showHabitBottomSheet'))
    await driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    await driver.elementClick(byText('Edit habit setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
    await addtitle(valuee1)
    await driver.execute('flutter:waitForTappable', byText(valuee1))
    await driver.elementClick(byText(valuee1))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
    await addtitle(valuee)
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))

  }
  public async deleteYesorNoHabitsToday() {

    await driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await driver.elementClick(byValueKey('showHabitBottomSheet'))
    await driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet') // Screenshot_bottomsheet
    await driver.elementClick(byText('Edit habit setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    //await driver.execute('smartui.takeScreenshot=edit_habit_page')
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byText('Delete'))
    await driver.elementClick(byText('Delete'))
    await driver.elementClick(byValueKey('todayToggleButton'))
  }


  public async addnumericalhabit1() {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'How much distance you walked? ' + currentDate
    const fabutton = byTooltip('increment')
    await driver.execute('flutter:waitForTappable', fabutton)
    await driver.elementClick(fabutton)

    const setuphabit = byText('Setup Habit')
    await driver.execute('flutter:waitForTappable', setuphabit)
    await driver.elementClick(setuphabit)
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitType'))
    await driver.elementClick(byValueKey('selectHabitType'))
    await driver.execute('flutter:waitForTappable', byText('Numerical Value'))
    await driver.elementClick(byText('Numerical Value'))

    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await driver.elementSendKeys(byValueKey('textFieldBottomSheet'), valuee)
    await driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await driver.elementClick(byValueKey('sendButton'))
    await driver.execute('flutter:waitForTappable', byValueKey('setGoal'))
    await driver.elementClick(byValueKey('setGoal'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await driver.elementSendKeys(byValueKey('textFieldBottomSheet'), '5')
    await driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await driver.elementClick(byValueKey('sendButton'))

    await driver.execute('flutter:waitForTappable', byValueKey('setUnit'))
    await driver.elementClick(byValueKey('setUnit'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'kms')
    await driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await driver.elementClick(byValueKey('sendButton'))

    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await driver.elementClick(byValueKey('selectHabitDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate')) // Today;Next Saturday;Next Sunday;After 1 week;
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save')) // Today is selected
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends;15 days later;30 days later;60 days later
    /* await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitRepeat'))
    await driver.elementClick(byValueKey('selectHabitRepeat'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save')) // if need specific days need to use alphabets and for Tuesday;Thursday and Saturday;Sunday need appium code
    await driver.execute('flutter:waitForTappable', byValueKey('addTime'))
    await driver.elementClick(byValueKey('addTime'))

    await driver.execute('flutter:scroll', byValueKey('timeHourSpinner'), { dx: 0, dy: 1000, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:scroll', byValueKey('timeMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:scroll', byValueKey('selectionAmPm'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byText('Set'))
    await driver.elementClick(byText('Set'))

    /* await driver.elementClick(byValueKey('sethabitReminder'))
    //await waitFor(2000)
    await driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    //await waitFor(2000)
    await driver.elementClick(byText('Set'))
    //await waitFor(2000) */
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    // await driver.elementClick(byValueKey('todaytoggleButton'))
    // //await waitFor(2000)
    await driver.elementClick(byValueKey('todayToggleButton'))
  }
  public async editNumericalHabitsToday() {

    await driver.execute('flutter:scrollIntoView', byValueKey('habitList'), { alignment: 1 })
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'How much distance you walked? ' + currentDate
    const valuee1 = 'Updated_How much distance you walked? ? ' + currentDate
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    assert.strictEqual(await driver.getElementText(elem), valuee)
    console.log('Success')
    await driver.execute('flutter:waitForTappable', byText(valuee))
    await driver.elementClick(byText(valuee))
    await driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await driver.elementClick(byValueKey('showHabitBottomSheet'))
    await driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    await driver.elementClick(byText('Edit habit setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
    await addtitle(valuee1)
    /*await driver.execute('flutter:waitForTappable', byText(valuee1))
    await driver.elementClick(byText(valuee1))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
  //await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
  await addtitle(valuee)*/
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))

  }
  public async deleteNumericalHabitsToday() {

    await driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await driver.elementClick(byValueKey('showHabitBottomSheet'))
    await driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet') // Screenshot_bottomsheet
    await driver.elementClick(byText('Edit habit setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    //await driver.execute('smartui.takeScreenshot=edit_habit_page')
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byText('Delete'))
    await driver.elementClick(byText('Delete'))
    await driver.elementClick(byValueKey('todayToggleButton'))
  }

  // Single_choice_habit
  public async addsinglechoicehabit1() {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'Your favotite color ' + currentDate
    const fabutton = byTooltip('increment')
    await driver.execute('flutter:waitForTappable', fabutton)
    await driver.elementClick(fabutton)

    const setuphabit = byText('Setup Habit')
    await driver.execute('flutter:waitForTappable', setuphabit)
    await driver.elementClick(setuphabit)
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitType'))
    await driver.elementClick(byValueKey('selectHabitType'))
    await driver.execute('flutter:waitForTappable', byText('Single Choice'))
    await driver.elementClick(byText('Single Choice'))
    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await driver.switchContext('NATIVE_APP')
    const el10 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')

    await el10.setValue(valuee)
    const el11 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el11.click()
    const el12 = await driver.$('~Add Option')
    await el12.click()
    const el13 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el13.click()
    await el13.setValue('white')
    const el14 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el14.click()
    const el15 = await driver.$('~Add Option')
    await el15.click()
    const el16 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el16.setValue('black')
    const el17 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el17.click()
    const el18 = await driver.$('~Add Option')
    await el18.click()
    const el19 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el19.setValue('pink')
    const el20 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el20.click()
    await driver.switchContext('FLUTTER')
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await driver.elementClick(byValueKey('selectHabitDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate')) // TodayNext SaturdayNext SundayAfter 1 week
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save')) // Today is selected
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends15 days later30 days later60 days later
    /* await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))

    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitRepeat'))
    await driver.elementClick(byValueKey('selectHabitRepeat'))
    await driver.switchContext('NATIVE_APP')
    const el29 = await driver.$('(//android.view.View[@content-desc=\'S\'])[2]')
    await el29.click()
    const el30 = await driver.$('(//android.view.View[@content-desc=\'S\'])[1]')
    await el30.click()
    await driver.switchContext('FLUTTER')
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))// if need specific days need to use alphabets and for TuesdayThursday and SaturdaySunday need appium code

    await driver.execute('flutter:waitForTappable', byValueKey('addTime'))
    await driver.elementClick(byValueKey('addTime'))
    await driver.execute('flutter:waitForTappable', byValueKey('timeHourSpinner'), 2000)
    await driver.execute('flutter:scroll', byValueKey('timeHourSpinner'), { dx: 0, dy: 1000, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('timeMinSpinner'), 2000)
    await driver.execute('flutter:scroll', byValueKey('timeMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byValueKey('selectionAmPm'), 2000)
    await driver.execute('flutter:scroll', byValueKey('selectionAmPm'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byText('Set'))
    await driver.elementClick(byText('Set'))

    /* await driver.elementClick(byValueKey('sethabitReminder'))
    //await waitFor(2000)
    await driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    //await waitFor(2000) */
    //await driver.elementClick(byText('Set'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.elementClick(byValueKey('todayToggleButton'))

  }
  public async editsinglechoiceHabitsToday() {

    await driver.execute('flutter:scrollIntoView', byValueKey('habitList'), { alignment: 1 })
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'Your favotite color ' + currentDate
    const valuee1 = 'Updated_Your favotite color ? ' + currentDate
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    assert.strictEqual(await driver.getElementText(elem), valuee)
    console.log('Success')
    await driver.execute('flutter:waitForTappable', byText(valuee))
    await driver.elementClick(byText(valuee))
    await driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await driver.elementClick(byValueKey('showHabitBottomSheet'))
    await driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    await driver.elementClick(byText('Edit habit setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
    await addtitle(valuee1)
    /*await driver.execute('flutter:waitForTappable', byText(valuee1))
    await driver.elementClick(byText(valuee1))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
  //await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
  await addtitle(valuee)*/
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))

  }
  public async deletesinglechoiceHabitsToday() {

    await driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await driver.elementClick(byValueKey('showHabitBottomSheet'))
    await driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet') // Screenshot_bottomsheet
    await driver.elementClick(byText('Edit habit setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    //await driver.execute('smartui.takeScreenshot=edit_habit_page')
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byText('Delete'))
    await driver.elementClick(byText('Delete'))
    await driver.elementClick(byValueKey('todayToggleButton'))
  }

  //Multi_choice_habit
  public async addmultichoicehabit1() {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'Food you had today' + currentDate
    const fabutton = byTooltip('increment')
    await driver.execute('flutter:waitFor', fabutton, 5000)
    await driver.elementClick(fabutton)

    const setuphabit = byText('Setup Habit')
    await driver.execute('flutter:waitForTappable', setuphabit)
    await driver.elementClick(setuphabit)
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitType'))
    await driver.elementClick(byValueKey('selectHabitType'))
    await driver.execute('flutter:waitForTappable', byText('Multi Choice'))
    await driver.elementClick(byText('Multi Choice'))

    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await driver.switchContext('NATIVE_APP')
    const el38 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el38.setValue(valuee)
    const el39 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el39.click()
    const el40 = await driver.$('~Add Option')
    await el40.click()
    const el41 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el41.setValue('Idly')
    const el42 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el42.click()
    const el43 = await driver.$('~Add Option')
    await el43.click()
    const el44 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el44.setValue('Dosa')
    const el45 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el45.click()
    const el46 = await driver.$('~Add Option')
    await el46.click()
    const el47 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el47.click()
    await el47.setValue('Roti')
    const el48 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el48.click()
    await driver.switchContext('FLUTTER')
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await driver.elementClick(byValueKey('selectHabitDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate')) // TodayNext SaturdayNext SundayAfter 1 week
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save')) // Today is selected
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends15 days later30 days later60 days later
    /* await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))

    /* await driver.execute('flutter:waitForTappable', byValueKey('addOption'))
    await driver.elementClick(byValueKey('addOption'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'Chappathi')
    await driver.execute('flutter:waitForTappable', byValueKey('addTitleDone'))
    await driver.elementClick(byValueKey('addTitleDone'))
    await driver.execute('flutter:waitForTappable', byValueKey('addOption'))
    await driver.elementClick(byValueKey('addOption'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'Dosa')
    await driver.execute('flutter:waitForTappable', byValueKey('addTitleDone'))
    await driver.elementClick(byValueKey('addTitleDone')) */

    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitRepeat'))
    await driver.elementClick(byValueKey('selectHabitRepeat'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save')) // if need specific days need to use alphabets and for TuesdayThursday and SaturdaySunday need appium code

    await driver.execute('flutter:waitForTappable', byValueKey('addTime'))
    await driver.elementClick(byValueKey('addTime'))

    await driver.execute('flutter:waitForTappable', byValueKey('timeHourSpinner'), 2000)
    await driver.execute('flutter:scroll', byValueKey('timeHourSpinner'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('timeMinSpinner'), 2000)
    await driver.execute('flutter:scroll', byValueKey('timeMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byValueKey('selectionAmPm'), 2000)
    await driver.execute('flutter:scroll', byValueKey('selectionAmPm'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await driver.execute('flutter:waitForTappable', byText('Set'))
    await driver.elementClick(byText('Set'))

    /* await driver.elementClick(byValueKey('sethabitReminder'))
    //await waitFor(2000)
    await driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    //await waitFor(2000)
    await driver.elementClick(byText('Set'))
    //await waitFor(2000) */
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.elementClick(byValueKey('todayToggleButton'))
  }
  public async editmultichoiceHabitsToday() {

    await driver.execute('flutter:scrollIntoView', byValueKey('habitList'), { alignment: 1 })
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'Food you had today' + currentDate
    const valuee1 = 'Updated_Food you had today ? ' + currentDate
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    assert.strictEqual(await driver.getElementText(elem), valuee)
    console.log('Success')
    await driver.execute('flutter:waitForTappable', byText(valuee))
    await driver.elementClick(byText(valuee))
    await driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await driver.elementClick(byValueKey('showHabitBottomSheet'))
    await driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    await driver.elementClick(byText('Edit habit setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
    await addtitle(valuee1)
    /*await driver.execute('flutter:waitForTappable', byText(valuee1))
    await driver.elementClick(byText(valuee1))
    await driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
  //await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
  await addtitle(valuee)*/
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))

  }
  public async deletemultichoiceHabitsToday() {

    await driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await driver.elementClick(byValueKey('showHabitBottomSheet'))
    await driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    //await driver.execute('smartui.takeScreenshot=habit_bottomsheet') // Screenshot_bottomsheet
    await driver.elementClick(byText('Edit habit setup'))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    //await driver.execute('smartui.takeScreenshot=edit_habit_page')
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byText('Delete'))
    await driver.elementClick(byText('Delete'))
    await driver.elementClick(byValueKey('todayToggleButton'))
  }

  public async pastTodoDateFilter() {
    await driver.execute('flutter:waitForTappable', byText('Past'))
    await driver.elementClick(byText('Past'))
    //await driver.execute('flutter:waitForTappable', byValueKey('pastTodoList'))
    //  await driver.execute('flutter:scroll', byValueKey('pastTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    /* */
    //await driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 0, dyScroll: -1000 })
    //////await waitfor(2000)
    await driver.execute('flutter:scrollIntoView', byValueKey('pastTodoList'), { alignment: 1 })
    await driver.execute('flutter:waitForTappable', byValueKey('dateFilter'))
    await driver.execute('smartui.takeScreenshot=Past_screen') // Screenshot_pastscreen
    await driver.elementClick(byValueKey('dateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Last 7 Days'))
    await driver.execute('smartui.takeScreenshot=datefilter_bottomsheet') // Screenshot_bottomsheet
    await driver.elementClick(byText('Last 7 Days'))
    //await driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 0, dyScroll: -1000 })
    //////await waitfor(2000)
    await driver.execute('flutter:waitForTappable', byValueKey('pastTodoList'))
    await driver.execute('flutter:scroll', byValueKey('pastTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('dateFilter'))
    await driver.elementClick(byValueKey('dateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Last 30 Days'))
    await driver.elementClick(byText('Last 30 Days'))
    await driver.execute('flutter:waitForTappable', byValueKey('pastTodoList'))
    await driver.execute('flutter:scroll', byValueKey('pastTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:scrollIntoView', byValueKey('pastTodoList'), { alignment: 1 })
    await driver.execute('flutter:waitForTappable', byValueKey('dateFilter'))
    await driver.elementClick(byValueKey('dateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Last 12 Months'))
    await driver.elementClick(byText('Last 12 Months'))
    await driver.execute('flutter:waitForTappable', byValueKey('pastTodoList'))
    await driver.execute('flutter:scroll', byValueKey('pastTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('dateFilter'))
    await driver.elementClick(byValueKey('dateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Custom'))
    await driver.elementClick(byText('Custom'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate')) // TodayNext SaturdayNext SundayAfter 1 week
    await driver.execute('flutter:waitForTappable', byText('Beginning'))
    await driver.elementClick(byText('Beginning'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save')) // Today is selected
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends15 days later30 days later60 days later
    /* await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await driver.execute('flutter:waitForTappable', byText('2 Days Ago'))
    await driver.elementClick(byText('2 Days Ago'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))
    await driver.execute('flutter:waitForTappable', byValueKey('pastTodoList'))
    await driver.execute('flutter:scroll', byValueKey('pastTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('dateFilter'))
    await driver.elementClick(byValueKey('dateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Until Yesterday'))
    await driver.elementClick(byText('Until Yesterday'))
  }

  // Past Todo Status Filter
  public async pastTodoStatusFilter() {
    await driver.execute('flutter:waitForTappable', byValueKey('statusFilter'))
    await driver.elementClick(byValueKey('statusFilter'))
    await driver.execute('flutter:waitForTappable', byText('Overdue'))
    await driver.execute('smartui.takeScreenshot=status_bottomsheet')
    await driver.elementClick(byText('Overdue'))
    //await driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 0, dyScroll: -1000 })
    //////await waitfor(2000)
    await driver.execute('flutter:waitForTappable', byValueKey('pastTodoList'))
    await driver.execute('flutter:scroll', byValueKey('pastTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('statusFilter'))
    await driver.elementClick(byValueKey('statusFilter'))
    await driver.execute('flutter:waitForTappable', byText('Completed'))
    await driver.elementClick(byText('Completed')) // since no cpmplete items here no list visible
    //await driver.execute('flutter:waitForTappable', byValueKey('pastTodoList'))
    //await driver.execute('flutter:scroll', byValueKey('pastTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('statusFilter'))
    await driver.elementClick(byValueKey('statusFilter'))
    await driver.execute('flutter:waitForTappable', byText('All'))
    await driver.elementClick(byText('All'))
    await driver.execute('flutter:waitForTappable', byValueKey('pastTodoList'))
    await driver.execute('flutter:scroll', byValueKey('pastTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
  }

  // Past Todo Hashtag Filter
  public async pastTodoHashtagFilter() {
    await driver.execute('flutter:waitForTappable', byValueKey('hashTagFilter'))
    await driver.elementClick(byValueKey('hashTagFilter'))
    //await driver.execute('flutter:waitForTappable', byValueKey('addSearchHashTag'))
    await driver.execute('smartui.takeScreenshot=hashtag_bottomsheet')
    await driver.execute('flutter:waitForTappable', byText('#morning'))
    await driver.elementClick(byText('#morning'))
    await driver.execute('flutter:waitForTappable', byText('#smile'))
    await driver.elementClick(byText('#smile'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
    await driver.execute('flutter:waitForTappable', byValueKey('hashTagFilter'))
    await driver.elementClick(byValueKey('hashTagFilter'))
    await driver.execute('flutter:waitForTappable', byText('#morning'))
    await driver.elementClick(byText('#morning'))
    //await driver.execute('flutter:scrollUntilVisible', byValueKey('hashTagFilterList'), { item: byText('seashore'), dxScroll: 0, dyScroll: -50 }) // vertical scroll done
    await driver.execute('flutter:waitForTappable', byText('#smile'))
    await driver.elementClick(byText('#smile'))
    await driver.execute('flutter:waitForTappable', byText('Save'))
    await driver.elementClick(byText('Save'))
  }


  // Past Note Mood Filter
  public async pastNoteMoodFilter() {
    await driver.execute('flutter:waitForTappable', byValueKey('moodFilter'))
    await driver.elementClick(byValueKey('moodFilter'))
    await driver.execute('flutter:waitForTappable', byText('Very Happy'))
    await driver.execute('smartui.takeScreenshot=moodfilter_bottomsheet')
    await driver.elementClick(byText('Very Happy'))
    await driver.execute('flutter:waitForTappable', byText('Happy'))
    await driver.elementClick(byText('Happy'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))

    //await driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 0, dyScroll: -1000 })
    //////await waitfor(2000)
    await driver.execute('flutter:waitForTappable', byValueKey('pastNoteList'))
    await driver.execute('flutter:scroll', byValueKey('pastNoteList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('moodFilter'))
    await driver.elementClick(byValueKey('moodFilter'))
    await driver.execute('flutter:waitForTappable', byText('Meh'))
    await driver.elementClick(byText('Meh'))
    await driver.execute('flutter:waitForTappable', byText('Sad'))
    await driver.elementClick(byText('Sad'))
    await driver.execute('flutter:waitForTappable', byText('Very Sad'))
    await driver.elementClick(byText('Very Sad'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))
    await driver.execute('flutter:waitForTappable', byValueKey('pastNoteList'))
    await driver.execute('flutter:scroll', byValueKey('pastNoteList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
  }

  //Past habit list filter
  public async pastHabitListFilter() {
    await driver.execute('flutter:waitForTappable', byValueKey('habitFilter'))
    await driver.elementClick(byValueKey('habitFilter'))
    await driver.execute('flutter:waitForTappable', byText('Very Happy'))
    await driver.execute('smartui.takeScreenshot=moodfilter_bottomsheet')
    await driver.elementClick(byText('Very Happy'))
    await driver.execute('flutter:waitForTappable', byText('Happy'))
    await driver.elementClick(byText('Happy'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))

    //await driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 0, dyScroll: -1000 })
    //////await waitfor(2000)
    await driver.execute('flutter:waitForTappable', byValueKey('pastNoteList'))
    await driver.execute('flutter:scroll', byValueKey('pastNoteList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('moodFilter'))
    await driver.elementClick(byValueKey('moodFilter'))
    await driver.execute('flutter:waitForTappable', byText('Meh'))
    await driver.elementClick(byText('Meh'))
    await driver.execute('flutter:waitForTappable', byText('Sad'))
    await driver.elementClick(byText('Sad'))
    await driver.execute('flutter:waitForTappable', byText('Very Sad'))
    await driver.elementClick(byText('Very Sad'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))
    await driver.execute('flutter:waitForTappable', byValueKey('pastNoteList'))
    await driver.execute('flutter:scroll', byValueKey('pastNoteList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
  }

  //Past journal list filter
  public async pastJournalListFilter() {
    await driver.execute('flutter:waitForTappable', byValueKey('moodFilter'))
    await driver.elementClick(byValueKey('moodFilter'))
    await driver.execute('flutter:waitForTappable', byText('Very Happy'))
    await driver.execute('smartui.takeScreenshot=moodfilter_bottomsheet')
    await driver.elementClick(byText('Very Happy'))
    await driver.execute('flutter:waitForTappable', byText('Happy'))
    await driver.elementClick(byText('Happy'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))

    //await driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 0, dyScroll: -1000 })
    //////await waitfor(2000)
    await driver.execute('flutter:waitForTappable', byValueKey('pastNoteList'))
    await driver.execute('flutter:scroll', byValueKey('pastNoteList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('moodFilter'))
    await driver.elementClick(byValueKey('moodFilter'))
    await driver.execute('flutter:waitForTappable', byText('Meh'))
    await driver.elementClick(byText('Meh'))
    await driver.execute('flutter:waitForTappable', byText('Sad'))
    await driver.elementClick(byText('Sad'))
    await driver.execute('flutter:waitForTappable', byText('Very Sad'))
    await driver.elementClick(byText('Very Sad'))
    await driver.execute('flutter:waitForTappable', byText('Apply'))
    await driver.elementClick(byText('Apply'))
    await driver.execute('flutter:waitForTappable', byValueKey('pastNoteList'))
    await driver.execute('flutter:scroll', byValueKey('pastNoteList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
  }


}
export default new LoginPage1();



