import { byText, byTooltip, byType, byV<PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from "assert";
import { addtitle } from "./page";
import { subDays } from "date-fns";

class YesorNoHabit {
  public async addYesOrNoHabit() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Are you happy ? " + currentDate;
    console.log(valuee);
    await driver.execute("flutter:waitForTappable", byText("Past"));
    await driver.elementClick(byText("Past"));
    await driver.execute("flutter:waitForTappable", byText("Habit"));
    await driver.elementClick(byText("Habit"));
    const fabutton = byValueKey("increment");
    await driver.execute("flutter:waitForTappable", fabutton);
    console.log("Habit " + valuee);
    await driver.elementClick(fabutton);
    console.log("Habit " + valuee);

    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_1289");//appMobile_commonUI_habit_setupAdd_yesOrNo_base_stateDefault
    await driver.elementClick(byValueKey("selectHabitDateRange"));
    await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
    await driver.execute("smartui.takeScreenshot=daterangepicker"); // Screenshot_bottomsheet
    await driver.elementClick(byValueKey("startDate")); // Today;Next Saturday;Next Sunday;After 1 week;
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE")); // Today is selected
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitRepeat"));
    await driver.elementClick(byValueKey("selectHabitRepeat"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute("smartui.takeScreenshot=overlay_repeatSelect_3045"); //appMobile_commonUI_widgets_repeat_stateRepeatForHabitAndJournalDefault
    await driver.elementClick(byText("SAVE")); // if need specific days need to use alphabets and for Tuesday;Thursday and Saturday;Sunday need appium code
    await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
    await driver.elementClick(byValueKey("addHashTag"));
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.execute("smartui.takeScreenshot=hashtag_widget"); // Screenshot_bottomsheet
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("#goodmorning"));
    await driver.elementClick(byText("#goodmorning"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    //screen_habitSetupAdd_1847 //appMobile_commonUI_habit_setupAdd_common_actionTapButtonSave
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.execute("smartui.takeScreenshot=overlay_habitSetupAddMandatoryAlert_2170");//appMobile_commonUI_habit_setupAdd_yesOrNo_base_alertPopupMandatoryFieldNotFilled_stateDefault
    await driver.elementClick(byText("GOT IT"));
    //overlay_habitSetupAddMandatoryAlert_2403//appMobile_commonUI_habit_setupAdd_yesOrNo_base_alertPopupMandatoryFieldNotFilled_actionTapButtonOk
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_2408");//appMobile_commonUI_habit_setupAdd_yesOrNo_base_stateMandatoryFieldNotFilled
    await driver.elementClick(byValueKey("addHabitTitle"));
    await driver.elementSendKeys(byValueKey("addHabitTitle"), valuee);

    //screen_habitSetupAdd_347//appMobile_commonUI_habit_setupAdd_yesOrNo_base_stateSingleLineTitleAdded
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }

  public async addYesOrNohabitSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    await driver.execute("flutter:waitForTappable", byText('Today'));
    await driver.elementClick(byText('Today'));
    const valuee = "Are you happy ? " + currentDate;
    await driver.execute("flutter:waitForTappable", byText(valuee));
    const elem = await driver.getElementText(byText(valuee));
    assert.strictEqual(elem, valuee);
  }

  public async editYesOrNoHabitsToday() {

    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Are you happy ? " + currentDate;
    const valuee1 = "Updated_Are you happy ? " + currentDate;
    const elem = byText(valuee); // here if multiple entries with same name --conflict
    assert.strictEqual(await driver.getElementText(elem), valuee);
    console.log("Success");
    await driver.execute("smartui.takeScreenshot=screen_common_1205");//appMobile_commonUI_habit_actionBlock_stateDefault
    await driver.execute("flutter:waitForTappable", byText(valuee));
    await driver.elementClick(byText(valuee));
    //screen_common_180//appMobile_commonUI_habit_actionBlock_actionTap
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    //2873_bottomSheet_actionCommon
    await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeYesOrNo_4164")//appMobile_commonUI_habit_action_yesOrNoHabit_stateDefault
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.switchContext("NATIVE_APP");
    const el8 = await driver.$("/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]/android.view.View");
    await el8.click();
    await driver.switchContext("FLUTTER");
    // screen_habitSetupEdit_2746//appMobile_commonUI_habit_setupEdit_activeHabitSetup_ruleEditAlert
    await driver.execute("flutter:waitForTappable", byText("EDIT"));
    await driver.execute("smartui.takeScreenshot=overlay_habitActionEditAlert_5848")//appMobile_commonUI_habit_setupEdit_editAlertPopup_stateDefault
    await driver.elementClick(byText("EDIT"));
    //overlay_habitActionEditAlert_3401//appMobile_commonUI_habit_setupEdit_editAlertPopup_actionTapButtonContinue
    //screen_habitSetupEdit_475//appMobile_commonUI_habit_setupEdit_activeHabitSetup_ruleEdit
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupEdit_111")//appMobile_commonUI_habit_setupEdit_activeHabitSetup_stateDefaultOfYesOrNo
    await driver.elementClick(byValueKey("addHabitTitle"));
    // await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    // await addtitle(valuee1);
    await driver.elementSendKeys(byValueKey("addHabitTitle"), valuee1);
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    //screen_habitSetupEdit_2786//appMobile_commonUI_habit_setupEdit_actionTapButtonSave
    await driver.execute("flutter:waitForTappable", byText("Yes"));
    await driver.elementClick(byText("Yes"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeYesOrNo_5057")//appMobile_commonUI_habit_action_yesOrNoHabit_stateAllFilled
    //bottomSheet_habitActionCommon_640//appMobile_commonUI_habit_action_common_stateHashtagSelectionOnEmptyActionScreen
    await driver.elementClick(byText("SAVE"));
  }

  public async editYesOrNoHabitsSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const value = "Updated_Are you happy ? " + currentDate;
    await driver.execute("flutter:waitForTappable", byText(value));
    const elem = await driver.getElementText(byText(value));
    assert.strictEqual(elem, value);
    await driver.elementClick(byText(value));
  }

  public async deleteYesOrNoHabitsToday() {
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.execute("smartui.takeScreenshot=habit_bottomsheet"); // Screenshot_bottomsheet
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    //screen_habitSetupEdit_1912 //appMobile_commonUI_habit_setupEdit_actionTapIconDelete
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.execute("smartui.takeScreenshot=overlay_habitSetupEditDelete_10");//appMobile_commonUI_habit_setupEdit_deleteConfirmationPopup_stateDefault
    await driver.elementClick(byValueKey("quaternaryButton"));
    //overlay_habitSetupEditDelete_6115//appMobile_commonUI_habit_setupEdit_deleteConfirmationPopup_actionTapButtonDelete

  }

  public async deleteHabitPast() {
    await driver.execute("flutter:waitForTappable", byText("Today"));
    await driver.elementClick(byText("Today"));
    const d = new Date();
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
    const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
    const result_sub2 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 370);
    const result_sub3 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 410);
    const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
    const pastDate1 = result_sub1.getDate() + "/" + (result_sub1.getMonth() + 1) + "/" + result_sub1.getFullYear();
    const pastDate2 = result_sub2.getDate() + "/" + (result_sub2.getMonth() + 1) + "/" + result_sub2.getFullYear();
    const pastDate3 = result_sub3.getDate() + "/" + (result_sub3.getMonth() + 1) + "/" + result_sub3.getFullYear();
    const value1 = "Are you happy ? " + pastDate;
    const value2 = "Are you happy ? " + pastDate1;
    const value3 = "Are you happy ? " + pastDate2;
    const value4 = "Are you happy ? " + pastDate3;
    await driver.execute("flutter:waitForTappable", byText(value1));
    await driver.elementClick(byText(value1));
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
    await driver.execute("flutter:waitForTappable", byText(value2));
    await driver.elementClick(byText(value2));
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
    await driver.execute("flutter:waitForTappable", byText(value3));
    await driver.elementClick(byText(value3));
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
    await driver.execute("flutter:waitForTappable", byText(value4));
    await driver.elementClick(byText(value4));
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
  }
}
export default new YesorNoHabit();
