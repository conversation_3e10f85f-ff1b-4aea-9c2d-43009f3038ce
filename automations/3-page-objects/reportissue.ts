import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
class Report {

    public async report() {
        const hamburger = byValue<PERSON>ey('hamburgerMenu')
        await driver.elementClick(hamburger);
        await driver.execute("smartui.takeScreenshot=overlay_hamburgerTray_3129");
        const reportissues = byText('Report an issue')
        await driver.elementClick(reportissues);
        await driver.execute("smartui.takeScreenshot=screen_reportAnIssue_5987");
    }

    public async reportSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Add Report'))
        const element = await driver.getElementText(byText('Add Report'))
        console.log('element value is:' + element)
        const valuee = 'Add Report'
        assert.strictEqual(element, valuee)
    }

    public async addReport() {
        const addReport = byText('Add Report')
        await driver.elementClick(addReport);
        await driver.execute("smartui.takeScreenshot=bottomSheet_reportAdd_1470");
        const reason = byText('Select a reason')
        await driver.elementClear(reason);
        const issueType = byText('Technical issue')
        await driver.elementClick(issueType);
        await driver.execute("smartui.takeScreenshot=tooltip_reportDescription_1309");
        await driver.execute("smartui.takeScreenshot=screen_reportAnIssue_1909");
        await driver.execute("smartui.takeScreenshot=screen_reportAnIssue_2313");
    }

    public async addReportSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Add report'))
        const element = await driver.getElementText(byText('Add report'))
        console.log('element value is:' + element)
        const valuee = 'Add report'
        assert.strictEqual(element, valuee)
    }

    public async addBriefIssues() {
        const briefIssues = byText('Give brief about your issue')
        await driver.elementClick(briefIssues);
        await driver.execute("smartui.takeScreenshot=screen_reportAnIssue_2313");

        await driver.execute('smartui.takeScreenshot=screen_reportAnIssue_4778')
        await driver.execute('smartui.takeScreenshot=bottomSheet_reportAdd_5065')
        await driver.execute('smartui.takeScreenshot=bottomSheet_reportAdd_370')
        const close = byValueKey('close')
        await driver.elementClick(close);
        await driver.execute('smartui.takeScreenshot=screen_reportAnIssue_2222')
        const close1 = byValueKey('close')
        await driver.elementClick(close1);
        await driver.execute('smartui.takeScreenshot=bottomSheet_reportAdd_1470')
        const closeReport = byValueKey('closeReportAnIssues')
        await driver.elementClick(closeReport);
    }

    public async addBriefIssuesSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Past'))
        const element = await driver.getElementText(byText('Past'))
        console.log('element value is:' + element)
        const valuee = 'Past'
        assert.strictEqual(element, valuee)
    }

}
export default new Report();