import { byText, byTooltip, byType, byV<PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from "assert";
import { addtitle } from "./page";

export const waitFor = async (delay: number, message?: string): Promise<any> => {
  await new Promise((resolve) => setTimeout(resolve, delay));
  if (message !== undefined) {
    console.log(message);
  }
};
class MultiChoiceHabit {
  public async addMultiChoiceHabit() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Food you had today" + currentDate;
    await driver.execute("flutter:waitForTappable", byText("Past"));
    await driver.elementClick(byText("Past"));
    await driver.execute("flutter:waitForTappable", byText("Habit"));
    await driver.elementClick(byText("Habit"));
    const fabutton = byValueKey("increment");
    await driver.execute("flutter:waitFor", fabutton, 5000);
    await driver.elementClick(fabutton);
    const setuphabit = byText("Setup Habit");
    await driver.execute("flutter:waitForTappable", setuphabit);
    await driver.elementClick(setuphabit);
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitType"));
    await driver.elementClick(byValueKey("selectHabitType"));
    await driver.execute("flutter:waitForTappable", byText("Multiple Choice"));
    await driver.elementClick(byText("Multiple Choice"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute('smartui.takeScreenshot=screen_habitSetupAdd_3270') //appMobile_commonUI_habit_setupAdd_multipleChoice_base_stateDefault
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.execute('smartui.takeScreenshot=overlay_habitSetupAddMandatoryAlert_5619') //appMobile_commonUI_habit_setupAdd_multipleChoice_base_alertPopupMandatoryFieldNotFilled_stateDefault
    await driver.elementClick(byText("GOT IT"));
    //overlay_habitSetupAddMandatoryAlert_2546//appMobile_commonUI_habit_setupAdd_multipleChoice_base_alertPopupMandatoryFieldNotFilled_actionTapButtonOk
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.elementClick(byText("GOT IT")); // code need to be corrected 
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.execute('smartui.takeScreenshot=screen_habitSetupAdd_2824')//appMobile_commonUI_habit_setupAdd_multipleChoice_base_stateMandatoryFieldNotFilled
    await driver.elementClick(byValueKey("addHabitTitle"));
    await driver.elementSendKeys(byValueKey("addHabitTitle"), valuee);
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute('smartui.takeScreenshot=screen_habitSetupAdd_2285')//appMobile_commonUI_habit_setupAdd_multipleChoice_base_stateSingleLineTitleAdded
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.execute('smartui.takeScreenshot=overlay_habitSetupAddMinOptionAlert_2220') //appMobile_commonUI_habit_setupAdd_multipleChoice_base_alertPopupMinimumLimitOfOption_stateDefault
    await driver.elementClick(byText("GOT IT"));
    await driver.elementClick(byText("Add Option"));
    const opt1 = "Idly"
    await addtitle(opt1);
    await driver.elementClick(byText("Add Option"));
    const opt2 = "Dosa"
    await addtitle(opt2);
    await driver.elementClick(byText("Add Option"));
    const opt3 = "Roti"
    await addtitle(opt3);
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
    await driver.elementClick(byValueKey("selectHabitDateRange"));
    await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
    await driver.elementClick(byValueKey("startDate")); // TodayNext SaturdayNext SundayAfter 1 week
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE")); // Today is selected
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends15 days later30 days later60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitRepeat"));
    await driver.elementClick(byValueKey("selectHabitRepeat"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE")); // if need specific days need to use alphabets and for TuesdayThursday and SaturdaySunday need appium code
    await driver.execute("flutter:waitForTappable", byValueKey("addTime"));
    await driver.elementClick(byValueKey("addTime"));
    await driver.execute("flutter:waitForTappable", byValueKey("timeHourSpinner"), 2000);
    await driver.execute("flutter:scroll", byValueKey("timeHourSpinner"), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("timeMinSpinner"), 2000);
    await driver.execute("flutter:scroll", byValueKey("timeMinSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byValueKey("selectionAmPm"), 2000);
    await driver.execute("flutter:scroll", byValueKey("selectionAmPm"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
    await driver.elementClick(byValueKey("addHashTag"));
    await driver.execute("flutter:waitForTappable", byText("#smile"));

    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("#goodmorning"));
    await driver.elementClick(byText("#goodmorning"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    /* await driver.elementClick(byValueKey('sethabitReminder'))
        //await waiitFor(2000)
        await driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
        //await waiitFor(2000)
        await driver.elementClick(byText('Set'))
        //await waiitFor(2000) */

    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute('smartui.takeScreenshot=screen_habitSetupAdd_482')//appMobile_commonUI_habit_setupAdd_multipleChoice_base_stateFilled
    await driver.elementClick(byText("SAVE"));
    // await driver.elementClick(byValueKey("todayToggleButton"));
  }
  public async addMultiChoiceHabitSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Food you had today" + currentDate;
    await driver.execute("flutter:waitForTappable", byText("Today"));
    await driver.elementClick(byText("Today"));
    await driver.execute("flutter:waitForTappable", byText(valuee));
    const elem = await driver.getElementText(byText(valuee));
    assert.strictEqual(elem, valuee);
  }

  public async editMultiChoiceHabitsToday() {
    //await driver.execute("flutter:scrollIntoView", byValueKey("habitList"), { alignment: 1 });
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Food you had today" + currentDate;
    const valuee1 = "Updated_Food you had today ? " + currentDate;
    const elem = byText(valuee); // here if multiple entries with same name --conflict
    assert.strictEqual(await driver.getElementText(elem), valuee);
    console.log("Success");
    await driver.execute("flutter:waitForTappable", byText(valuee));
    await driver.elementClick(byText(valuee));
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    //2873_bottomSheet_actionCommon
    //S-6125_bottomSheet_habitActionCommon
    await driver.execute("smartui.takeScreenshot=multichoice_initial_options");
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.switchContext("NATIVE_APP");
    const el8 = await driver.$("/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]/android.view.View");
    await el8.click();
    await driver.switchContext("FLUTTER");
    // await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    // await driver.elementClick(byValueKey('addHabitTitle'))
    await driver.execute("flutter:waitForTappable", byText("EDIT"));
    await driver.elementClick(byText("EDIT"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupEdit_5130")//appMobile_commonUI_habit_setupEdit_activeHabitSetup_stateDefaultOfMultipleChoice
    await driver.elementClick(byValueKey("addHabitTitle"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));

    await driver.elementSendKeys(byValueKey("addHabitTitle"), valuee1);
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt4 = "Idly"
    await addtitle(opt4);
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.elementClick(byText("GOT IT"));
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt_4 = "Pancake"
    await addtitle(opt_4);
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt5 = "Cake"
    await addtitle(opt5);
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt6 = "Dal"
    await addtitle(opt6);
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt7 = "Cornflakes"
    await addtitle(opt7);
    await driver.execute("flutter:scroll", byText("Cornflakes"), { dx: 0, dy: -1200, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt8 = "Appam"
    await addtitle(opt8);
    await driver.execute("flutter:scroll", byText("Cornflakes"), { dx: 0, dy: -1200, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt9 = "Tea"
    await addtitle(opt9);
    await driver.execute("flutter:scroll", byText("Cornflakes"), { dx: 0, dy: -1200, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt1 = "Rice"
    await addtitle(opt1);

    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("Idly"));
    await driver.execute("smartui.takeScreenshot=multichoice_updated_options");
    await driver.elementClick(byText("Idly"));
    await driver.execute("flutter:scroll", byText("Cornflakes"), { dx: 0, dy: -1200, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Rice"));
    await driver.elementClick(byText("Rice"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }

  public async editMultiChoiceHabitsSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const value = "Updated_Food you had today ? " + currentDate;
    await driver.execute("flutter:waitForTappable", byText(value));
    const elem = await driver.getElementText(byText(value));
    assert.strictEqual(elem, value);
    await driver.elementClick(byText(value));
  }

  public async deleteMultiChoiceHabitsToday() {
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.execute("smartui.takeScreenshot=edit_habit_page");
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byText("DELETE"));
    await driver.elementClick(byText("DELETE"));
  }
}
export default new MultiChoiceHabit();
