import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
class Gallary {

    public async gallary() {
        const hamburger = byValue<PERSON>ey('hamburgerMenu')
        await driver.elementClick(hamburger);
        const gallary = byText('Gallary')
        await driver.elementClick(gallary);
        await driver.execute('smartui.takeScreenshot=Gallary_Images')
    }
}
export default new Gallary