import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
class Account {

    public async account() {
        const hamburger = byValue<PERSON><PERSON>('hamburgerMenu')
        await driver.elementClick(hamburger);
        await driver.execute("smartui.takeScreenshot=overlay_hamburgerTray_3129");
        const account = byText('Account')
        await driver.elementClick(account);
        await driver.execute('smartui.takeScreenshot=screen_account_68')
    }

    public async accountDetailsSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Account details'))
        const element = await driver.getElementText(byText('Account details'))
        console.log('element value is:' + element)
        const valuee = 'Account details'
        assert.strictEqual(element, valuee)
    }

    public async language() {
        const language = byText('English')
        await driver.elementClick(language);
        await driver.execute('smartui.takeScreenshot=screen_account_4439')
        const close = byValue<PERSON>ey('close');
        await driver.elementClick(close);
        await driver.execute('smartui.takeScreenshot=bottomSheet_languageSelect_2444')
    }

    public async languageSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Language'))
        const element = await driver.getElementText(byText('Language'))
        console.log('element value is:' + element)
        const valuee = 'Language'
        assert.strictEqual(element, valuee)
    }

    public async manage() {
        const manage = byText('Manage')
        await driver.elementClick(manage);
        const close = byValueKey('closeSubscriptionPage');
        await driver.elementClick(close);

        await driver.execute('flutter:scroll', byText('English'), { dx: 0, dy: -150, durationMilliseconds: 1000, frequency: 80 })
    }

    public async manageSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Subscription'))
        const element = await driver.getElementText(byText('Subscription'))
        console.log('element value is:' + element)
        const valuee = 'Subscription'
        assert.strictEqual(element, valuee)
    }

    public async deleteAccount() {
        const deleteAccout = byText('Delete Account')
        await driver.elementClick(deleteAccout);
        await driver.execute('smartui.takeScreenshot=screen_account_1967')
        const continue1 = byText('CONTINUE')
        await driver.elementClick(continue1);
        await driver.execute('smartui.takeScreenshot=screen_deleteAccount_305')
        const selectReason = byText('Select a reason')
        await driver.elementClick(selectReason);
        await driver.execute('smartui.takeScreenshot=screen_deleteAccount_2701')
        const lotOfBugs = byText('Lot of bugs')
        await driver.elementClick(lotOfBugs);
        await driver.execute('smartui.takeScreenshot=bottomSheet_deleteAccountReasonSelect_3225')
        await driver.execute('smartui.takeScreenshot=bottomSheet_bottomSheet_deleteAccountReasonSelect_5690')
        await driver.execute('smartui.takeScreenshot=screen_deleteAccount_4070')

        const closeDeleteAccount = byValueKey('closeDeleteAccount')
        await driver.elementClick(closeDeleteAccount);
        const closeAccount = byValueKey('closeAccount')
        await driver.elementClick(closeAccount)
        await driver.execute('smartui.takeScreenshot=bottomSheet_deleteAccountReasonSelect_5505')
    }

    public async accountDeleteSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Past'))
        const element = await driver.getElementText(byText('Past'))
        console.log('element value is:' + element)
        const valuee = 'Past'
        assert.strictEqual(element, valuee)
    }

}
export default new Account();