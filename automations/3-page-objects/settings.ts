import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'


class Settings {
    public async settings() {
        const hamburger = byValue<PERSON>ey('hamburgerMenu')
        await driver.elementClick(hamburger);
        const settings = byText('Settings')
        await driver.elementClick(settings);
    }

    public async SettingsSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Settings'))
        const element = await driver.getElementText(byText('Settings'))
        console.log('element value is:' + element)
        const valuee = 'Settings'
        assert.strictEqual(element, valuee)
    }

    public async mevolvePin() {
        const mevolve = byText('Mevolve PIN')
        await driver.elementClick(mevolve);
        const close = byValueKey('close')
        await driver.elementClick(close);
    }

    public async mevolvePinSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Settings'))
        const element = await driver.getElementText(byText('Settings'))
        console.log('element value is:' + element)
        const valuee = 'Settings'
        assert.strictEqual(element, valuee)
    }

    public async themes() {
        const theme = byText('Theme')
        await driver.elementClick(theme);
        const systemDefault = byText('System Default');
        await driver.elementClick(systemDefault);
        const theme1 = byText('Theme')
        await driver.elementClick(theme1);
        const light = byText('Light')
        await driver.elementClick(light);
        const theme2 = byText('Theme')
        await driver.elementClick(theme2);
        const dark = byText('Dark')
        await driver.elementClick(dark);
    }

    public async themesSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Settings'))
        const element = await driver.getElementText(byText('Settings'))
        console.log('element value is:' + element)
        const valuee = 'Settings'
        assert.strictEqual(element, valuee)
    }

    public async colour() {
        const colour1 = byText('Colour')
        await driver.elementClick(colour1);
        await driver.switchContext('NATIVE_APP')
        const el161 = await driver.$('//android.view.View[@content-desc="Choose Colour"]/android.view.View[2]')
        await el161.click();
        await driver.switchContext('FLUTTER')

        const colour2 = byText('Colour')
        await driver.elementClick(colour2);
        await driver.switchContext('NATIVE_APP')
        const el162 = await driver.$('//android.view.View[@content-desc="Choose Colour"]/android.view.View[3]')
        await el162.click();
        await driver.switchContext('FLUTTER')

        const colour3 = byText('Colour')
        await driver.elementClick(colour3);
        await driver.switchContext('NATIVE_APP')
        const el163 = await driver.$('//android.view.View[@content-desc="Choose Colour"]/android.view.View[4]')
        await el163.click();
        await driver.switchContext('FLUTTER')

        const colour4 = byText('Colour')
        await driver.elementClick(colour4);
        await driver.switchContext('NATIVE_APP')
        const el164 = await driver.$('//android.view.View[@content-desc="Choose Colour"]/android.view.View[5]')
        await el164.click();
        await driver.switchContext('FLUTTER')

    }

    public async colourSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Settings'))
        const element = await driver.getElementText(byText('Settings'))
        console.log('element value is:' + element)
        const valuee = 'Settings'
        assert.strictEqual(element, valuee)
    }

    public async vibration() {
        const vibration = byText('Vibration')
        await driver.elementClick(vibration);
        const vibration1 = byText('Vibration')
        await driver.elementClick(vibration1);

        const close = byValueKey('closeSettings')
        await driver.elementClick(close);

    }

    public async vibrationSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Past'))
        const element = await driver.getElementText(byText('Past'))
        console.log('element value is:' + element)
        const valuee = 'Past'
        assert.strictEqual(element, valuee)
    }
}

export default new Settings();