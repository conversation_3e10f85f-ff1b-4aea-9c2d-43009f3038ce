import { byText, byTooltip, byType, byV<PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
class List {

    public async addList() {
        const addList = byText('Lists')
        await driver.execute('flutter:waitForTappable', addList);
        await driver.elementClick(addList);
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_2957')
        const createList = byText('Create List')
        await driver.elementClick(createList);

        // const d = new Date()
        // const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
        // const text = 'New_List 1'
        // const input = byValueKey('textFieldBottomSheet')
        // await driver.elementSendKeys(input, text)
        // const Send = byValueKey('sendButton')
        //await driver.execute('smartui.takeScreenshot=added_List')
        //await driver.elementClick(Send)

        for (let i = 1; i <= 21; i++) {
            let text = 'New_List ' + i
            const input = byValue<PERSON><PERSON>('textFieldBottomSheet')
            await driver.elementSendKeys(input, text)
            const add = byValueKey('sendButton')
            await driver.elementClick(add)
            const createList = byText('Create List')
            await driver.elementClick(createList);
        }
        await driver.switchContext('NATIVE_APP')
        const el16 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]')
        await el16.click();
        await driver.switchContext('FLUTTER')
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_6494')
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_4355')
    }

    public async addedListSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('New_List 21'))
        const element = await driver.getElementText(byText('New_List 21'))
        console.log('element value is:' + element)
        const valuee = 'New_List 21'
        assert.strictEqual(element, valuee)
    }

    public async listOrderArrange() {
        await driver.execute('flutter:scroll', byText('New_List 19'), { dx: 0, dy: 150, durationMilliseconds: 10000, frequency: 80 })
        await driver.execute('flutter:scroll', byText('New_List 17'), { dx: 0, dy: -1500, durationMilliseconds: 1000, frequency: 80 })
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_6528')
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_2613')


        await driver.execute('flutter:scroll', byText('New_List 2'), { dx: 0, dy: 150, durationMilliseconds: 1000, frequency: 80 })
        await driver.execute('flutter:scroll', byText('New_List 4'), { dx: 0, dy: 150, durationMilliseconds: 1000, frequency: 80 })
        await driver.execute('flutter:scroll', byText('New_List 7'), { dx: 0, dy: -250, durationMilliseconds: 1000, frequency: 80 })
        await driver.execute('smartui.takeScreenshot=bottomSheet_listItems_5189')
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_2948')
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_1543')

        const moveTop = byValueKey('goToTopButton')
        await driver.elementClick(moveTop);
        await driver.execute('smartui.takeScreenshot=bottomSheet_listItems_3545')
        await driver.execute('smartui.takeScreenshot=bottomSheet_listItems_1564')

        await driver.execute('flutter:waitForTappable', byText('New_List 19'));
        await driver.execute('flutter:scroll', byText('New_List 19'), { dx: -550, dy: 0, durationMilliseconds: 200, frequency: 10 })

        const editList = byValueKey('editList')
        await driver.elementClick(editList)
        const edit = 'New_List 22'
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_3659')
        const bottomSheetInput = byValueKey('textFieldBottomSheet')
        await driver.elementSendKeys(bottomSheetInput, edit)
        const clickSendButton = byValueKey('sendButton')
        await driver.elementClick(clickSendButton)


        await driver.execute('flutter:waitForTappable', byText('New_List 22'));
        await driver.execute('flutter:scroll', byText('New_List 22'), { dx: -550, dy: 0, durationMilliseconds: 200, frequency: 10 })
        const deleteList = byValueKey('deleteList')
        await driver.elementClick(deleteList);
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_82')

    }
    public async searchList() {
        let text = 'New_List 21'
        const searchButton = byValueKey('listSearchButton')
        await driver.elementClick(searchButton);
        const input = byValueKey('addSearchFilter')
        await driver.execute('smartui.takeScreenshot=bottomSheet_listItems_6024')
        await driver.elementSendKeys(input, text)
        await driver.execute('smartui.takeScreenshot=bottomSheet_listItems_4009')

        const close = byValueKey('Icon')
        await driver.elementClick(close)
    }

    public async searchListSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('New_List 21'))
        const element = await driver.getElementText(byText('New_List 21'))
        console.log('element value is:' + element)
        const valuee = 'New_List 21'
        assert.strictEqual(element, valuee)


        const tap = byText('New_List 21')
        await driver.elementClick(tap)
        await driver.execute('smartui.takeScreenshot=tab_listsMyLists_1191')

        const item = byText('Add Items')
        await driver.elementClick(item)

        for (let i = 1; i <= 2; i++) {
            let text = 'Item ' + i
            const input = byValueKey('textFieldBottomSheet')
            await driver.elementSendKeys(input, text)
            const add = byValueKey('addItem')
            await driver.elementClick(add)
        }
        await driver.switchContext('NATIVE_APP')
        const el16 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]')
        await el16.click();
        await driver.switchContext('FLUTTER')
    }

    public async addedItemsSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Item 2'))
        const element = await driver.getElementText(byText('Item 2'))
        console.log('element value is:' + element)
        const valuee = 'Item 2'
        assert.strictEqual(element, valuee)
    }

    public async checkUncheck() {
        const check = byText('Item 1')
        await driver.elementClick(check);
        const unCheck1 = byText('Item 1')
        await driver.elementClick(unCheck1);
        const check1 = byText('Item 2')
        await driver.elementClick(check1);
    }

    public async swipeDeleteItem() {
        await driver.execute('flutter:waitForTappable', byText('Item 2'));
        await driver.execute('flutter:scroll', byText('Item 2'), { dx: -550, dy: 0, durationMilliseconds: 200, frequency: 10 })

        const editItem1 = byValueKey('editItem')
        await driver.elementClick(editItem1);
        const edit = 'Item 7'
        const bottomSheetInput = byValueKey('textFieldBottomSheet')
        await driver.elementSendKeys(bottomSheetInput, edit)
        const clickSendButton = byValueKey('sendButton')
        await driver.elementClick(clickSendButton)

        await driver.execute('flutter:waitForTappable', byText('Item 1'));
        await driver.execute('flutter:scroll', byText('Item 1'), { dx: -550, dy: 0, durationMilliseconds: 200, frequency: 10 })

        const deleteItem = byValueKey('deleteItem')
        await driver.elementClick(deleteItem);
        const close = byValueKey('close')
        await driver.elementClick(close);

        const click = byText('New_List 21')
        await driver.elementClick(click)
        const addItems = byText('Add Items')
        await driver.elementClick(addItems)
    }

    public async checkUncheckScussess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Item 2'))
        const element = await driver.getElementText(byText('Item 2'))
        console.log('element value is:' + element)
        const valuee = 'Item 2'
        assert.strictEqual(element, valuee)
    }

    public async addItems() {
        for (let i = 1; i <= 21; i++) {
            let text = 'New_Item ' + i
            const input = byValueKey('textFieldBottomSheet')
            await driver.elementSendKeys(input, text)
            const add = byValueKey('addItem')
            await driver.elementClick(add)
        }
        await driver.switchContext('NATIVE_APP')
        const el16 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]')
        await el16.click();
        await driver.switchContext('FLUTTER')
    }

    public async addedItemSuccess(message?: string): Promise<void> {
        const element = await driver.getElementText(byText('New_Item 21'))
        console.log('element value is:' + element)
        const valuee = 'New_Item 21'
        assert.strictEqual(element, valuee)
    }

    public async itemsOrderRearrange() {
        // await driver.execute('flutter:scroll', byText('New_Item 18'), { dx: 0, dy: 100, durationMilliseconds: 7000, frequency: 80 })
        // await driver.execute('flutter:scroll', byText('New_Item 18'), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    }

    public async itemsReorderSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('New_Item 17'))
        const element = await driver.getElementText(byText('New_Item 17'))
        console.log('element value is:' + element)
        const valuee = 'New_Item 17'
        assert.strictEqual(element, valuee)
    }

    public async kebabMenuFunctions() {
        const kebabMenuCheckAll = byValueKey('kebabMenu')
        await driver.elementClick(kebabMenuCheckAll);

        const check = byText('Check all')
        await driver.elementClick(check);

        const tap1 = byText('New_Item 18')
        await driver.elementClick(tap1);
        const tap2 = byText('New_Item 19')
        await driver.elementClick(tap2);

        const uncheckedFilter = byText('Unchecked')
        await driver.elementClick(uncheckedFilter);
        const checkedFilter = byText('Checked')
        await driver.elementClick(checkedFilter);
        const AllFilter = byText('All')
        await driver.elementClick(AllFilter);
        const kebabMenuUncheckAll = byValueKey('kebabMenu')
        await driver.elementClick(kebabMenuUncheckAll);
        const Uncheck = byText('Uncheck all')
        await driver.elementClick(Uncheck);

        const kebabMenuUpdateList = byValueKey('kebabMenu')
        await driver.elementClick(kebabMenuUpdateList);
        const updateList = byText('Update list name')
        await driver.elementClick(updateList)
        const update = 'New_List 22'
        const bottomSheetInput = byValueKey('textFieldBottomSheet')
        await driver.elementSendKeys(bottomSheetInput, update)
        const clickSendButton = byValueKey('sendButton')
        await driver.elementClick(clickSendButton)
        const kebabMenuDelete = byValueKey('kebabMenu')
        await driver.elementClick(kebabMenuDelete);
        const kebabDelete = byText('Delete list')
        await driver.elementClick(kebabDelete);
        const deletes = byText('DELETE')
        await driver.elementClick(deletes)
        await driver.execute('smartui.takeScreenshot=overlay_listDelete_6682')
        await driver.execute('smartui.takeScreenshot=overlay_listDelete_6683')


        await driver.execute('flutter:waitForTappable', byText('New_List 20'));
        await driver.execute('flutter:scroll', byText('New_List 20'), { dx: -550, dy: 0, durationMilliseconds: 200, frequency: 10 })
        const deleteList = byValueKey('deleteList')
        await driver.elementClick(deleteList);

        for (let i = 18; i >= 1; i--) {
            const delete1 = byText('New_List ' + i)
            await driver.elementClick(delete1);
            const kebabMenu = byValueKey('kebabMenu')
            await driver.elementClick(kebabMenu)
            const deletelists = byText('Delete list')
            await driver.elementClick(deletelists);
        }
    }

    public async kebabFunctionsSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Today'))
        const element = await driver.getElementText(byText('Today'))
        console.log('element value is:' + element)
        const valuee = 'Today'
        assert.strictEqual(element, valuee)
    }

    public async toastMessage() {
        const undo = byText('UNDO')
        await driver.elementClick(undo);

        const click = byText('New_List 1')
        await driver.elementClick(click)
        const kebabMenu = byValueKey('kebabMenu')
        await driver.elementClick(kebabMenu)
        const deletes = byText('Delete list')
        await driver.elementClick(deletes)
        const dismiss = byText('New_List 1')
        await driver.elementClick(dismiss);
        const toastDismiss = byText('DISMISS')
        await driver.elementClick(toastDismiss);
    }

    public async toastMessageSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Today'))
        const element = await driver.getElementText(byText('Today'))
        console.log('element value is:' + element)
        const valuee = 'Today'
        assert.strictEqual(element, valuee)
    }
}

export default new List();