import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
import { addDatePast, addtitle } from "./page";
import addDays from 'date-fns/addDays'
import { subDays } from 'date-fns'

class OverdueTodos {

    public async addOverdueTodo() {
        await driver.execute('flutter:waitForTappable', byV<PERSON><PERSON><PERSON><PERSON>('overdueList'))
        await driver.elementClick(byValue<PERSON><PERSON>('overdueList'))
        const fabutton = byValueKey('increment');
        await driver.execute('flutter:waitForTappable', fabutton);
        await driver.execute('smartui.takeScreenshot=tab_todayOverdue_2358')
        await driver.elementClick(fabutton);
        const addtodo = byText('Add Todo');
        await driver.execute('flutter:waitForTappable', addtodo);
        await driver.elementClick(addtodo);
        const d = new Date();
        const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 10)
        const m_sub = result_sub.getMonth()
        const pastDate = result_sub.getDate() + '/' + (result_sub.getMonth() + 1) + '/' + result_sub.getFullYear();
        const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const currentDateString = d.getDate() + '/' + monthNames[d.getMonth()] + '/' + d.getFullYear();
        const pastDateString = result_sub.getDate() + '/' + monthNames[m_sub] + '/' + result_sub.getFullYear();
        const valuee = 'New OverdueTodo_1 ' + pastDate;
        await addtitle(valuee)
        await addDatePast(pastDateString, currentDateString)
        await driver.execute('flutter:waitForTappable', byText('CLOSE'))
        await driver.execute('smartui.takeScreenshot=bottomSheet_todoAction_1153')
        await driver.elementClick(byText('CLOSE'))
    }

    public async addOverdueTodoSucess() {
        const d = new Date();
        const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 10)
        const pastDate = result_sub.getDate() + '/' + (result_sub.getMonth() + 1) + '/' + result_sub.getFullYear();
        const valuee = 'New OverdueTodo_1 ' + pastDate;
        await driver.execute('flutter:waitForTappable', byText(valuee))
        const elem = await driver.getElementText(byText(valuee))
        assert.strictEqual(elem, valuee)
    }

    public async deleteOverdueTodo() {
        const d = new Date()
        const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
        const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 10)
        const d_sub = result_sub.getFullYear()
        const m_sub = result_sub.getMonth()
        const pastDate = result_sub.getDate() + '/' + (result_sub.getMonth() + 1) + '/' + result_sub.getFullYear();
        const value1 = 'New OverdueTodo_1 ' + pastDate;
        await driver.execute('flutter:waitForTappable', byText(value1))
        await driver.elementClick(byText(value1))
        //2642_tab_todayOverdue
        await driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
        await driver.elementClick(byValueKey('trashIcon'))
        await driver.execute('flutter:waitForTappable', byValueKey('quaternaryButton'))
        await driver.elementClick(byValueKey('quaternaryButton'))
    }

    public async doneOverdueTodo() {
        console.log('FAB');
        const d = new Date()
        const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 10)
        const pastDate = result_sub.getDate() + '/' + (result_sub.getMonth() + 1) + '/' + result_sub.getFullYear();
        const valuee = 'New OverdueTodo_1 ' + pastDate;
        await driver.execute('smartui.takeScreenshot=tab_todayOverdue_4796')
        await driver.execute('flutter:waitForTappable', byText(valuee))
        await driver.elementClick(byText(valuee))
        await driver.execute('flutter:waitForTappable', byValueKey('circleCheckBox'))
        await driver.elementClick(byValueKey('circleCheckBox'))
        //2249_tab_todayOverdue
        //748_toast_todoCompleted
        await driver.execute('flutter:waitForTappable', byText('CLOSE'))
        await driver.elementClick(byText('CLOSE'))
        await driver.execute('flutter:waitForTappable', byText('Past'))
        await driver.elementClick(byText('Past'))
        await driver.execute('flutter:waitForTappable', byText('Todo'))
        await driver.elementClick(byText('Todo'))
        await driver.execute('flutter:waitForTappable', byValueKey('pastTodoList'))
        await driver.execute('flutter:waitForTappable', byText(valuee))
        await driver.elementClick(byText(valuee))
        await driver.execute('flutter:waitForTappable', byValueKey('circleCheckBox'))
        await driver.elementClick(byValueKey('circleCheckBox'))
        await driver.execute('flutter:waitForTappable', byText('CLOSE'))
        await driver.elementClick(byText('CLOSE'))
        await driver.execute('flutter:waitForTappable', byText('Today'))
        await driver.elementClick(byText('Today'))
        await driver.execute('flutter:waitForTappable', byValueKey('overdueList'))
        await driver.elementClick(byValueKey('overdueList'))
    }
}
export default new OverdueTodos();



