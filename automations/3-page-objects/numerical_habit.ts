import { byText, byTooltip, byType, byV<PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from "assert";
import { addtitle } from "./page";

export const waitFor = async (delay: number, message?: string): Promise<any> => {
  await new Promise((resolve) => setTimeout(resolve, delay));
  if (message !== undefined) {
    console.log(message);
  }
};
class NumericalHabit {
  public async addNumericalHabitWithAndWithoutGoal() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Distance you walked With Goal?";
    const valuee1 = "Distance you walked Without Goal?";
    await driver.execute("flutter:waitForTappable", byText("Past"));
    await driver.elementClick(byText("Past"));
    await driver.execute("flutter:waitForTappable", byText("Habit"));
    await driver.elementClick(byText("Habit"));
    const fabutton = byValueKey("increment");
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);

    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitType"));
    await driver.elementClick(byValueKey("selectHabitType"));
    await driver.execute("flutter:waitForTappable", byText("Numerical Value"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_habitSetupAddHabitType_3325")//appMobile_commonUI_habit_setupAdd_common_habitTypeBottomSheet_stateOpen
    await driver.elementClick(byText("Numerical Value"));
    await driver.execute("flutter:waitForTappable", byValueKey("setGoal"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_1179");//appMobile_commonUI_habit_setupAdd_numericalValue_base_stateDefault
    await driver.elementClick(byValueKey("setGoal"));
    //screen_habitSetupAdd_837//appMobile_commonUI_habit_setupAdd_numericalValue_goal_actionTapGoalIcon
    //bottomSheet_goal_5393//appMobile_commonUI_habit_setupAdd_numericalValue_goal_goalTextField_stateEmpty
    await driver.elementSendKeys(byValueKey("setGoal"), "5");

    await driver.execute("smartui.takeScreenshot=bottomSheet_goal_4997");//appMobile_commonUI_habit_setupAdd_numericalValue_goal_goalTextField_stateFilled
    //bottomSheet_goal_1034//appMobile_commonUI_habit_setupAdd_numericalValue_goal_goalTextField_ruleCharacterLimit

    //bottomSheet_goal_4768//appMobile_commonUI_habit_setupAdd_numericalValue_goal_goalTextField_actionTapSendIcon
    //bottomSheet_goal_3569//appMobile_commonUI_habit_setupAdd_numericalValue_goal_goalTextField_ruleAccept
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.elementClick(byText("GOT IT"));
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.elementClick(byText("GOT IT"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_908");//appMobile_commonUI_habit_setupAdd_numericalValue_base_stateMandatoryFieldNotFilled
    await driver.elementClick(byValueKey("addHabitTitle"));
    await driver.elementSendKeys(byValueKey("addHabitTitle"), valuee);
    //bottomSheet_title_3348
    //screen_habitSetupAdd_489
    await driver.execute("flutter:waitForTappable", byValueKey("setUnit"));
    await driver.elementClick(byValueKey("setUnit"));
    await driver.elementSendKeys(byValueKey("setUnit"), "kms");
    //bottomSheet_unit_5409//appMobile_commonUI_habit_setupAdd_numericalValue_base_unitTextField_stateEmpty
    await driver.execute("smartui.takeScreenshot=bottomSheet_unit_2643");//appMobile_commonUI_habit_setupAdd_numericalValue_base_unitTextField_stateFilled
    //bottomSheet_unit_5190//appMobile_commonUI_habit_setupAdd_numericalValue_base_unitTextField_ruleCharacterLimit
    //bottomSheet_unit_3756//appMobile_commonUI_habit_setupAdd_numericalValue_base_unitTextField_actionTapSendIcon
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
    await driver.elementClick(byValueKey("selectHabitDateRange"));
    await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
    await driver.elementClick(byValueKey("startDate")); // Today;Next Saturday;Next Sunday;After 1 week;
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE")); // Today is selected
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitRepeat"));
    await driver.elementClick(byValueKey("selectHabitRepeat"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE")); // if need specific days need to use alphabets and for Tuesday;Thursday and Saturday;Sunday need appium code
    await driver.execute("flutter:waitForTappable", byValueKey("addTime"));
    await driver.elementClick(byValueKey("addTime"));
    await driver.execute("flutter:scroll", byValueKey("timeHourSpinner"), { dx: 0, dy: 1000, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:scroll", byValueKey("timeMinSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:scroll", byValueKey("selectionAmPm"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
    await driver.elementClick(byValueKey("addHashTag"));
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("#goodmorning"));
    await driver.elementClick(byText("#goodmorning"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_2300");//appMobile_commonUI_habit_setupAdd_numericalValue_base_stateUnitAdded
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);

    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitType"));
    await driver.elementClick(byValueKey("selectHabitType"));
    await driver.execute("flutter:waitForTappable", byText("Numerical Value"));
    await driver.elementClick(byText("Numerical Value"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.elementClick(byValueKey("addHabitTitle"));

    await driver.elementSendKeys(byValueKey("addHabitTitle"), valuee1);

    await driver.execute("flutter:waitForTappable", byValueKey("setUnit"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_489");//appMobile_commonUI_habit_setupAdd_numericalValue_base_stateSingleLineTitleAdded
    await driver.elementClick(byValueKey("setUnit"));
    await driver.elementSendKeys(byValueKey("setUnit"), "km");


    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
    await driver.elementClick(byValueKey("selectHabitDateRange"));
    await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
    await driver.elementClick(byValueKey("startDate")); // Today;Next Saturday;Next Sunday;After 1 week;
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE")); // Today is selected
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitRepeat"));
    await driver.elementClick(byValueKey("selectHabitRepeat"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE")); // if need specific days need to use alphabets and for Tuesday;Thursday and Saturday;Sunday need appium code
    await driver.execute("flutter:waitForTappable", byValueKey("addTime"));
    await driver.elementClick(byValueKey("addTime"));
    await driver.execute("flutter:scroll", byValueKey("timeHourSpinner"), { dx: 0, dy: 1000, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:scroll", byValueKey("timeMinSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:scroll", byValueKey("selectionAmPm"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
    await driver.elementClick(byValueKey("addHashTag"));
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    ////await driver.execute("smartui.takeScreenshot=hashtag_widget"); // Screenshot_bottomsheet
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("#goodmorning"));
    await driver.elementClick(byText("#goodmorning"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));

  }

  public async addNumericalHabitSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    await driver.execute("flutter:waitForTappable", byText("Today"));
    await driver.elementClick(byText("Today"));
    const valuee = "Distance you walked With Goal?";
    await driver.execute("flutter:waitForTappable", byText(valuee));
    const elem = await driver.getElementText(byText(valuee));
    assert.strictEqual(elem, valuee);
  }

  public async editNumericalHabitsToday() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const value = "Distance you walked Without Goal?";
    const value1 = "Updated_Distance you walked Without Goal";
    const valuee = "Distance you walked With Goal?";
    const valuee1 = "Updated_Distance you walked With Goal?";

    const elem = byText(valuee); // here if multiple entries with same name --conflict
    assert.strictEqual(await driver.getElementText(elem), valuee);
    console.log("Success");
    await driver.execute("flutter:waitForTappable", byText(valuee));
    await driver.elementClick(byText(valuee));
    await driver.execute("flutter:waitForTappable", byValueKey("addButton"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeNumericalValue_3452")//appMobile_commonUI_habit_action_numericValueHabit_stateDefaultWithGoal
    await driver.elementClick(byValueKey("addButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.execute("smartui.takeScreenshot=//bottomSheet_habitNumericalTypeValueInput_4127")//appMobile_commonUI_habit_action_numericValueHabit_numberInputField_stateEmpty
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), "5");
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_habitNumericalTypeValueInput_4299")//appMobile_commonUI_habit_action_numericValueHabit_numberInputField_stateFilled
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeNumericalValue_2315")//appMobile_commonUI_habit_action_numericValueHabit_stateFilledWithGoal
    //S-2873_bottomSheet_actionCommon //appMobile_commonUI_bottomBar_ruleIconOrder
    //S-6125_bottomSheet_habitActionCommon //appMobile_commonUI_bottomBar_stateHabitActionIconOrder
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.switchContext("NATIVE_APP");
    const el8 = await driver.$("/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]/android.view.View");
    await el8.click();
    await driver.switchContext("FLUTTER");
    await driver.execute("flutter:waitForTappable", byText("EDIT"));
    await driver.elementClick(byText("EDIT"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_screen_habitSetupEdit_3979");//appMobile_commonUI_habit_setupEdit_activeHabitSetup_stateDefaultOfNumericValue
    await driver.elementClick(byValueKey("addHabitTitle"));
    await driver.elementSendKeys(byValueKey("addHabitTitle"), valuee1);
    await driver.execute("flutter:waitForTappable", byValueKey("setGoal"));
    await driver.elementClick(byValueKey("setGoal"));
    await driver.elementSendKeys(byValueKey("setGoal"), "15");
    await driver.execute("flutter:waitForTappable", byValueKey("setUnit"));
    await driver.elementClick(byValueKey("setUnit"));
    await driver.elementSendKeys(byValueKey("setUnit"), "kilo meters");
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await waitFor(8000)
    await driver.execute("flutter:waitForTappable", byText(value));
    await driver.elementClick(byText(value));

    await driver.execute("flutter:waitForTappable", byValueKey("addButton"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeNumericalValue_4533")//appMobile_commonUI_habit_action_numericValueHabit_stateDefaultWithGoal
    await driver.elementClick(byValueKey("addButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    //bottomSheet_habitNumericalTypeValueInput_4127//appMobile_commonUI_habit_action_numericValueHabit_numberInputField_stateEmpty
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), "0");
    //bottomSheet_habitNumericalTypeValueInput_4299//appMobile_commonUI_habit_action_numericValueHabit_numberInputField_stateFilled
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    //await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeNumericalValue_2087")//appMobile_commonUI_habit_action_numericValueHabit_stateFilledWithoutGoal
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeNumericalValue_4944")//appMobile_commonUI_habit_action_numericValueHabit_stateZeroAsRespondedWithoutGoal
    //S-2873_bottomSheet_actionCommon //appMobile_commonUI_bottomBar_ruleIconOrder
    //S-6125_bottomSheet_habitActionCommon //appMobile_commonUI_bottomBar_stateHabitActionIconOrder
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.switchContext("NATIVE_APP");
    await el8.click();
    await driver.switchContext("FLUTTER");
    await driver.execute("flutter:waitForTappable", byText("EDIT"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupEdit_3979")//appMobile_commonUI_habit_setupEdit_activeHabitSetup_stateDefaultOfNumericValue
    await driver.elementClick(byText("EDIT"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.elementClick(byValueKey("addHabitTitle"));
    await driver.elementSendKeys(byValueKey("addHabitTitle"), value1);

    await driver.execute("flutter:waitForTappable", byValueKey("setUnit"));
    await driver.elementClick(byValueKey("setUnit"));

    await driver.elementSendKeys(byValueKey("setUnit"), "kilometer");

    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteNumericalEntry"));
    await driver.elementClick(byValueKey("deleteNumericalEntry"));
    await driver.execute("flutter:waitForTappable", byValueKey("addButton"));
    await driver.execute("smartui.takeScreenshot=toast_habitResponseEntryDeleted_3835")//appMobile_commonUI_habit_action_numericValueHabit_toastMessageEntryDeleted_stateDefault
    await driver.elementClick(byValueKey("addButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), "5");
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }

  public async editNumericalHabitsSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const value = "Updated_Distance you walked Without Goal";
    await driver.execute("flutter:waitForTappable", byText(value));
    const elem = await driver.getElementText(byText(value));
    assert.strictEqual(elem, value);
  }

  public async deleteNumericalHabitsToday() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const value1 = "Updated_Distance you walked Without Goal";
    const valuee1 = "Updated_Distance you walked With Goal?";
    await driver.execute("flutter:waitForTappable", byText(valuee1));
    await driver.execute("smartui.takeScreenshot=screen_common_2008")//appMobile_commonUI_habit_action_numericValueHabit_stateOnListScreenWhenGoalNotReached
    await driver.elementClick(byText(valuee1));
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
    await waitFor(3000)
    await driver.execute("flutter:waitForTappable", byText(value1));
    await waitFor(3000)
    await driver.elementClick(byText(value1));
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
  }
}

export default new NumericalHabit();
