import { byText, byTooltip, byType, byV<PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from "assert";
import { addtitle } from "./page";

export const waitFor = async (delay: number, message?: string): Promise<any> => {
  await new Promise((resolve) => setTimeout(resolve, delay));
  if (message !== undefined) {
    console.log(message);
  }
};

class SingleChoiceHabit {
  public async addSingleChoiceHabit() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Your favorite color " + currentDate;
    await driver.execute("flutter:waitForTappable", byText('Future'));
    await driver.elementClick(byText('Future'));
    await driver.execute("flutter:waitForTappable", byText('Habit Setup'));
    await driver.elementClick(byText('Habit Setup'));
    const fabutton = byValueKey("increment");
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    const setuphabit = byText("Setup Habit");
    await driver.execute("flutter:waitForTappable", setuphabit);
    await driver.elementClick(setuphabit);
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitType"));
    await driver.elementClick(byValueKey("selectHabitType"));
    await driver.execute("flutter:waitForTappable", byText("Single Choice"));
    await driver.elementClick(byText("Single Choice"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_5865"); // appMobile_commonUI_habit_setupAdd_singleChoice_base_stateDefault
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.execute("smartui.takeScreenshot=overlay_habitSetupAddMandatoryAlert_5830");//appMobile_commonUI_habit_setupAdd_singleChoice_base_mandatoryFieldNotFilledPopup_stateDefault
    await driver.elementClick(byText("GOT IT"));
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.elementClick(byText("GOT IT")); // Code need to be updated based on latest design
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_1760"); // appMobile_commonUI_habit_setupAdd_singleChoice_base_stateMandatoryFieldNotFilled
    await driver.elementClick(byValueKey("addHabitTitle"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.elementSendKeys(byValueKey("addHabitTitle"), valuee);
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.execute("smartui.takeScreenshot=overlay_habitSetupAddMinOptionAlert_1401");//appMobile_commonUI_habit_setupAdd_singleChoice_base_alertPopupMinimumLimitOfOption_stateDefault
    await driver.elementClick(byText("GOT IT"));
    //overlay_habitSetupAddMinOptionAlert_145//appMobile_commonUI_habit_setupAdd_singleChoice_base_alertPopupMinimumLimitOfOption_actionTapButtonOk
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt1 = "white"
    await addtitle(opt1);
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt2 = "black"
    await addtitle(opt2);
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt3 = "pink"
    await addtitle(opt3);
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
    await driver.elementClick(byValueKey("selectHabitDateRange"));
    await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
    await driver.elementClick(byValueKey("startDate")); // TodayNext SaturdayNext SundayAfter 1 week
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE")); // Today is selected
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends15 days later30 days later60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("selectHabitRepeat"));
    await driver.elementClick(byValueKey("selectHabitRepeat"));
    /* await driver.switchContext('NATIVE_APP')
         const el29 = await driver.$('(//android.view.View[@content-desc=\'S\'])[2]')
         await el29.click()
         const el30 = await driver.$('(//android.view.View[@content-desc=\'S\'])[1]')
         await el30.click()
         await driver.switchContext('FLUTTER')*/
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));// if need specific days need to use alphabets and for TuesdayThursday and SaturdaySunday need appium code
    await driver.execute("flutter:waitForTappable", byValueKey("addTime"));
    await driver.elementClick(byValueKey("addTime"));
    await driver.execute("flutter:waitForTappable", byValueKey("timeHourSpinner"), 2000);
    await driver.execute("flutter:scroll", byValueKey("timeHourSpinner"), { dx: 0, dy: 1000, durationMilliseconds: 500, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("timeMinSpinner"), 2000);
    await driver.execute("flutter:scroll", byValueKey("timeMinSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byValueKey("selectionAmPm"), 2000);
    await driver.execute("flutter:scroll", byValueKey("selectionAmPm"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byValueKey("setHabitReminder"));
    await driver.elementClick(byValueKey("setHabitReminder"));
    // await driver.execute("flutter:waitFor", byValueKey("reminderHourSpinner"), 3000);
    // await driver.execute("flutter:scroll", byValueKey("reminderHourSpinner"), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    // await driver.execute("flutter:waitFor", byValueKey("reminderMinSpinner"), 3000);
    // await driver.execute("flutter:scroll", byValueKey("reminderMinSpinner"), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    // await driver.execute("flutter:waitForTappable", byText("SET"));
    // await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText("Before 10m"));
    await driver.execute("smartui.takeScreenshot=overlay_repeatSelect_413"); // Screenshot_repeat
    await driver.elementClick(byText("Before 10m"));
    await driver.execute("flutter:waitForTappable", byText("Custom"));
    await driver.elementClick(byText("Custom"));
    await driver.execute("flutter:waitForTappable", byText("After"));
    await driver.execute("smartui.takeScreenshot=overlay_repeatSelect_413"); // Screenshot_repeat
    await driver.elementClick(byText("After"));
    await driver.execute("flutter:waitFor", byValueKey("reminderHourSpinner"), 3000);
    await driver.execute("flutter:scroll", byValueKey("reminderHourSpinner"), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitFor", byValueKey("reminderMinSpinner"), 3000);
    await driver.execute("flutter:scroll", byValueKey("reminderMinSpinner"), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText("CANCEL"));
    await driver.elementClick(byText("CANCEL"));
    await driver.execute("flutter:waitForTappable", byText("DISCARD"));
    await driver.elementClick(byText("DISCARD"));
    await driver.execute("flutter:waitForTappable", byText("Custom"));
    await driver.elementClick(byText("Custom"));
    await driver.execute("flutter:waitForTappable", byText("After"));
    await driver.elementClick(byText("After"));

    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.elementClick(byText("GOT IT"));
    await driver.execute("flutter:waitFor", byValueKey("reminderMinSpinner"), 3000);
    await driver.execute("flutter:scroll", byValueKey("reminderMinSpinner"), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute("smartui.takeScreenshot=overlay_repeatSelect_413"); // Screenshot_repeat
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
    await driver.elementClick(byValueKey("addHashTag"));
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("#goodmorning"));
    await driver.elementClick(byText("#goodmorning"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));

    /* await driver.elementClick(byValueKey('sethabitReminder'))
        await driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
        //await driver.elementClick(byText('Set'))*/
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_3342"); //appMobile_commonUI_habit_setupAdd_singleChoice_base_stateFilled
    await driver.elementClick(byText("SAVE"));
  }

  public async addSingleChoiceHabitSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Your favorite color " + currentDate;
    await driver.execute("flutter:waitForTappable", byText('Today'));
    await driver.elementClick(byText('Today'));
    await driver.execute("flutter:waitForTappable", byText(valuee));
    const elem = await driver.getElementText(byText(valuee));
    assert.strictEqual(elem, valuee);
  }

  public async editSingleChoiceHabitsToday() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Your favorite color " + currentDate;
    const valuee1 = "Updated_Your favorite color ? " + currentDate;
    const elem = byText(valuee); // here if multiple entries with same name --conflict
    assert.strictEqual(await driver.getElementText(elem), valuee);
    console.log("Success");
    await driver.execute("flutter:waitForTappable", byText(valuee));
    await driver.elementClick(byText(valuee));
    await driver.execute("flutter:waitForTappable", byText("white"));
    await driver.elementClick(byText("white"));
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    //S-2873_bottomSheet_actionCommon
    //S-6125_bottomSheet_habitActionCommon
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    await driver.elementClick(byText("Edit Habit Setup"));
    //screen_habitSetupEdit_2746//appMobile_commonUI_habit_setupEdit_activeHabitSetup_ruleEditAlert
    await driver.switchContext("NATIVE_APP");
    const el8 = await driver.$("/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]/android.view.View");
    await el8.click();
    await driver.switchContext("FLUTTER");
    await driver.execute("flutter:waitForTappable", byText("EDIT"));
    await driver.elementClick(byText("EDIT"));
    //screen_habitSetupEdit_475//appMobile_commonUI_habit_setupEdit_activeHabitSetup_ruleEdit
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupEdit_2659")//appMobile_commonUI_habit_setupEdit_activeHabitSetup_stateDefaultOfSingleChoice
    await driver.elementClick(byValueKey("addHabitTitle"));
    //await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    // await driver.execute('smartui.takeScreenshot=habit_bottomsheet_textfield') // Screenshot_bottomsheet
    //await addtitle(valuee1);
    await driver.elementSendKeys(byValueKey("addHabitTitle"), valuee1);
    // await driver.switchContext("NATIVE_APP");
    // const el15 = await driver.$("~Add Option");
    // await el15.click();
    // const el16 = await driver.$("/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView");
    // await el16.setValue("maroon");
    // const el17 = await driver.$("/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView");
    // await el17.click();
    // await driver.switchContext("FLUTTER");
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt4 = "grey"
    await addtitle(opt4);
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt5 = "yellow"
    await addtitle(opt5);
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt6 = "white"
    await addtitle(opt6);
    await driver.execute("flutter:waitForTappable", byText("GOT IT"));
    await driver.execute("smartui.takeScreenshot=overlay_habitSetupAddExistingOptionAlert_1540");//appMobile_commonUI_habit_setupAdd_singleChoice_base_alertPopupDuplicateOption_stateDefault
    //screen_habitSetupAdd_4577//appMobile_commonUI_habit_setupAdd_singleChoice_base_ruleDuplicateOptions
    //overlay_habitSetupAddExistingOptionAlert_1385//appMobile_commonUI_habit_setupAdd_singleChoice_base_alertPopupDuplicateOption_actionTapButtonOk
    await driver.elementClick(byText("GOT IT"));
    await driver.elementClick(byText("Add Option"));
    const opt_6 = "lavender"
    await addtitle(opt_6);
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt7 = "gold"
    await addtitle(opt7);
    await driver.execute("flutter:scroll", byText("gold"), { dx: 0, dy: -1200, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt8 = "silver"
    //screen_habitSetupAdd_5426//appMobile_commonUI_habit_setupAdd_singleChoice_base_actionTapButtonEnabledAddOption
    await addtitle(opt8);
    await driver.execute("flutter:scroll", byText("gold"), { dx: 0, dy: -1200, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt9 = "ivory"
    await addtitle(opt9);
    await driver.execute("flutter:scroll", byText("gold"), { dx: 0, dy: -1200, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.elementClick(byText("Add Option"));
    const opt10 = "maroon"
    await addtitle(opt10);
    await driver.execute("flutter:scroll", byText("gold"), { dx: 0, dy: -1200, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Add Option"));
    await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_1504");//appMobile_commonUI_habit_setupAdd_singleChoice_base_stateMaximumOption
    await driver.elementClick(byText("Add Option"));
    await driver.execute("flutter:waitForTappable", byText("DISMISS"));
    await driver.execute("smartui.takeScreenshot=toast_habitMaxOptionLimitReached_4903");//appMobile_commonUI_habit_setupAdd_singleChoice_base_toastMessageMaximumOptionLimitReached_stateDefault
    //screen_habitSetupAdd_571//appMobile_commonUI_habit_setupAdd_singleChoice_base_ruleMaximumOptionLimit
    //screen_habitSetupAdd_1187//appMobile_commonUI_habit_setupAdd_singleChoice_base_actionTapButtonDisabledAddOption
    await driver.elementClick(byText("DISMISS"));
    //toast_habitMaxOptionLimitReached_3691//appMobile_commonUI_habit_setupAdd_singleChoice_base_toastMessageMaximumOptionLimitReached_actionTapButtonDismiss
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:scroll", byText("gold"), { dx: 0, dy: -1200, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("maroon"));
    await driver.execute("smartui.takeScreenshot=habit_bottomsheet_option_update");
    await driver.elementClick(byText("maroon"));
    await driver.execute("flutter:waitForTappable", byText("pink"));
    // await driver.execute('smartui.takeScreenshot=habit_bottomsheet_option_update')
    await driver.elementClick(byText("pink"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }

  public async editSingleChoiceHabitsSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const value = "Updated_Your favorite color ? " + currentDate;
    // await driver.execute("flutter:scrollIntoView", byValueKey("habitList"), { alignment: 1 });
    await driver.execute("flutter:waitForTappable", byText(value));
    const elem = await driver.getElementText(byText(value));
    assert.strictEqual(elem, value);
    await driver.elementClick(byText(value));
  }

  public async deleteSingleChoiceHabitsToday() {
    await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
    await driver.elementClick(byValueKey("showHabitBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit Habit Setup"));
    // await driver.execute('smartui.takeScreenshot=habit_bottomsheet') // Screenshot_bottomsheet
    await driver.elementClick(byText("Edit Habit Setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.execute("smartui.takeScreenshot=edit_habit_page");
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
  }
}

export default new SingleChoiceHabit();
