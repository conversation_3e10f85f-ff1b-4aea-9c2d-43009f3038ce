import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from "assert";
import { addDateFuture, addDatePast, addEndDate, addStartDate, addtime, addtitle } from "./page";
import addDays from "date-fns/addDays";
import { subDays } from "date-fns";
export const waitFor = async (delay: number, message?: string): Promise<any> => {
    await new Promise((resolve) => setTimeout(resolve, delay));
    if (message !== undefined) {
        console.log(message);
    }
};


class Todo {
    public async addTodoToday() {
        console.log("FAB inside addtodo1");
        console.log("after login");
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "New Todo_1 " + currentDate;
        await driver.execute("flutter:waitForTappable", by<PERSON><PERSON><PERSON><PERSON><PERSON>("kebabMenu"));
        await driver.execute("smartui.takeScreenshot=tab_todayToday_3459")//appMobile_screens_today_today_tab_stateEmpty
        await driver.elementClick(byValueKey("kebabMenu"));
        await driver.execute("flutter:waitForTappable", byText("Category View"));
        await driver.execute("smartui.takeScreenshot=bottomSheet_todayKebabMenu_2067")
        await driver.elementClick(byText("Category View"));
        await driver.execute("flutter:waitForTappable", byText("View Configuration"));
        await driver.elementClick(byText("View Configuration"));
        await driver.execute("flutter:waitForTappable", byText("Description"));
        await driver.execute("smartui.takeScreenshot=bottomSheet_viewConfiguration_3083")//appMobile_screens_today_today_tab_kebabMenu_bottomsheet_viewConfigurationBottomsheet_stateDefault        
        await driver.elementClick(byText("Description"));
        await driver.execute("flutter:waitForTappable", byText("Short"));
        await driver.execute("smartui.takeScreenshot=bottomSheet_viewDescription_1768")//appMobile_screens_today_today_tab_kebabMenu_bottomsheet_viewConfigurationBottomsheet_descriptionBottomSheet_stateDefault
        await driver.elementClick(byText("Short"));
        await driver.execute("flutter:waitForTappable", byValueKey("close"));
        await driver.elementClick(byValueKey("close"));
        await driver.execute("flutter:waitForTappable", byText("My Goal"));
        await driver.elementClick(byText("My Goal"));
        // await driver.execute("flutter:waitForTappable", byText("Show Feature Labels"));
        // await driver.elementClick(byText("Show feature labels"));
        await driver.execute("flutter:waitForTappable", byValueKey("close"));
        await driver.elementClick(byValueKey("close"));
        await driver.execute("flutter:waitForTappable", byValueKey("enterGoal"));
        await driver.execute("smartui.takeScreenshot=tab_todayToday_3273")//appMobile_screens_today_today_tab_stateWithGoalPlaceholder
        //S-4874_bottomSheet_todayKebabMenu
        await driver.elementClick(byValueKey("enterGoal"));
        const goal = "Goal for this particular date is that to get maximum length text and see it " + currentDate;
        await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
        await addtitle(goal);
        const fabutton = byValueKey("increment");
        await driver.execute("smartui.takeScreenshot=tab_todayToday_154")//appMobile_screens_today_today_tab_stateLongGoalAddedWithCategoryView
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementSendKeys(byValueKey("addTitle"), valuee);
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        //S-2873_bottomSheet_actionCommon
        //S-3636_bottomSheet_todoAction
        //S-5194_bottomSheet_todoAction [with title]
        await driver.elementClick(byText("CLOSE"));
    }

    public async addTodoPast() {
        const fabutton = byValueKey("increment");
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);

        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "New Todo_1 " + currentDate;
        const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
        const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
        const result_sub2 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 360);
        const result_sub3 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 400);
        const d_sub = result_sub.getFullYear();
        const m_sub = result_sub.getMonth();
        const m_sub1 = result_sub1.getMonth();
        const m_sub2 = result_sub2.getMonth();
        const m_sub3 = result_sub3.getMonth();
        const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
        const pastDate1 = result_sub1.getDate() + "/" + (result_sub1.getMonth() + 1) + "/" + result_sub1.getFullYear();
        const pastDate2 = result_sub2.getDate() + "/" + (result_sub2.getMonth() + 1) + "/" + result_sub2.getFullYear();
        const pastDate3 = result_sub3.getDate() + "/" + (result_sub3.getMonth() + 1) + "/" + result_sub3.getFullYear();
        // const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const currentDateString = d.getDate() + "/" + monthNames[d.getMonth()] + "/" + d.getFullYear();
        const pastDateString = result_sub.getDate() + "/" + monthNames[m_sub] + "/" + result_sub.getFullYear();
        const pastDateString1 = result_sub1.getDate() + "/" + monthNames[m_sub1] + "/" + result_sub1.getFullYear();
        const pastDateString2 = result_sub2.getDate() + "/" + monthNames[m_sub2] + "/" + result_sub2.getFullYear();
        const pastDateString3 = result_sub3.getDate() + "/" + monthNames[m_sub3] + "/" + result_sub3.getFullYear();
        const result_add = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 17);
        const valuePast5days = "New Todo_1 " + pastDate;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementSendKeys(byValueKey("addTitle"), valuePast5days);

        await addDatePast(pastDateString, currentDateString);
        try {
            await driver.execute("flutter:waitFor", byValueKey("addHashTag"), 2000);
            await driver.elementClick(byValueKey("addHashTag"));
        } catch (warn) {
            await driver.execute("flutter:waitForTappable", byValueKey("overflowMenu"));
            await driver.elementClick(byValueKey("overflowMenu"));
            await driver.execute("flutter:waitForTappable", byText("Add hashtag"));
            await driver.elementClick(byText("Add hashtag"));
            console.log("qwerty");
        }
        await driver.execute("flutter:waitForTappable", byText("#smile"));
        await driver.elementClick(byText("#smile"));
        await driver.execute("flutter:waitForTappable", byText("#morning"));
        await driver.elementClick(byText("#morning"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));

        // 30 days
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);
        const valuePast30days = "New Todo_1 " + pastDate1;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementSendKeys(byValueKey("addTitle"), valuePast30days);

        await addDatePast(pastDateString1, currentDateString);
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));

        // 360 days
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);

        const valuePast360days = "New Todo_1 " + pastDate2;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementSendKeys(byValueKey("addTitle"), valuePast360days);
        await addDatePast(pastDateString2, currentDateString);
        await driver.execute("flutter:waitForTappable", byValueKey("circleCheckBox"));
        await driver.elementClick(byValueKey("circleCheckBox"));
        try {
            await driver.execute("flutter:waitFor", byValueKey("addHashTag"), 2000);
            await driver.elementClick(byValueKey("addHashTag"));
        } catch (warn) {
            await driver.execute("flutter:waitForTappable", byValueKey("overflowMenu"));
            await driver.elementClick(byValueKey("overflowMenu"));
            await driver.execute("flutter:waitForTappable", byText("Add hashtag"));
            await driver.elementClick(byText("Add hashtag"));
            console.log("qwerty");
        } await driver.execute("flutter:waitForTappable", byText("#smile"));
        await driver.elementClick(byText("#smile"));
        await driver.execute("flutter:waitForTappable", byText("#morning"));
        await driver.elementClick(byText("#morning"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));

        // 410 days
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);

        const valuePast410days = "New Todo_1 " + pastDate3;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementSendKeys(byValueKey("addTitle"), valuePast410days);
        await addDatePast(pastDateString3, currentDateString);
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));
    }

    public async addTodoFuture() {
        const fabutton = byValueKey("increment");
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);

        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "New Todo_1 " + currentDate;
        const result_addd = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
        const result_add1 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
        const result_add2 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 360);
        const result_add3 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 400);
        const d_add = result_addd.getFullYear();
        const m_add = result_addd.getMonth();
        const m_add1 = result_add1.getMonth();
        const m_add2 = result_add2.getMonth();
        const m_add3 = result_add3.getMonth();
        const futureDate = result_addd.getDate() + "/" + (result_addd.getMonth() + 1) + "/" + result_addd.getFullYear();
        const futureDate1 = result_add1.getDate() + "/" + (result_add1.getMonth() + 1) + "/" + result_add1.getFullYear();
        const futureDate2 = result_add2.getDate() + "/" + (result_add2.getMonth() + 1) + "/" + result_add2.getFullYear();
        const futureDate3 = result_add3.getDate() + "/" + (result_add3.getMonth() + 1) + "/" + result_add3.getFullYear();
        // const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const currentDateString = d.getDate() + "/" + monthNames[d.getMonth()] + "/" + d.getFullYear();
        const futureDateString = result_addd.getDate() + "/" + monthNames[m_add] + "/" + result_addd.getFullYear();
        const futureDateString1 = result_add1.getDate() + "/" + monthNames[m_add1] + "/" + result_add1.getFullYear();
        const futureDateString2 = result_add2.getDate() + "/" + monthNames[m_add2] + "/" + result_add2.getFullYear();
        const futureDateString3 = result_add3.getDate() + "/" + monthNames[m_add3] + "/" + result_add3.getFullYear();
        const result_add = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 17);
        const valueFuture5days = "New Todo_1 " + futureDate;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementSendKeys(byValueKey("addTitle"), valueFuture5days);

        await addDateFuture(futureDateString, currentDateString);
        await driver.execute("flutter:waitForTappable", byValueKey("circleCheckBox"));
        await driver.elementClick(byValueKey("circleCheckBox"));
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));
        // 30 days
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);

        const valueFuture30days = "New Todo_1 " + futureDate1;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementSendKeys(byValueKey("addTitle"), valueFuture30days);

        await addDateFuture(futureDateString1, currentDateString);
        await driver.execute("flutter:waitForTappable", byValueKey("circleCheckBox"));
        await driver.elementClick(byValueKey("circleCheckBox"));
        try {
            await driver.execute("flutter:waitFor", byValueKey("addHashTag"), 2000);
            await driver.elementClick(byValueKey("addHashTag"));
        } catch (warn) {
            await driver.execute("flutter:waitForTappable", byValueKey("overflowMenu"));
            await driver.elementClick(byValueKey("overflowMenu"));
            await driver.execute("flutter:waitForTappable", byText("Add hashtag"));
            await driver.elementClick(byText("Add hashtag"));
            console.log("qwerty");
        } await driver.execute("flutter:waitForTappable", byText("#smile"));
        await driver.elementClick(byText("#smile"));
        await driver.execute("flutter:waitForTappable", byText("#morning"));
        await driver.elementClick(byText("#morning"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));

        // 360 days
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);

        const valueFuture360days = "New Todo_1 " + futureDate2;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementSendKeys(byValueKey("addTitle"), valueFuture360days);
        await addDateFuture(futureDateString2, currentDateString);
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));

        // 410 days
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);
        const valueFuture410days = "New Todo_1 " + futureDate3;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementSendKeys(byValueKey("addTitle"), valueFuture410days);
        await addDateFuture(futureDateString3, currentDateString);
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));
        await driver.execute("flutter:waitForTappable", byText("Past"));
        await driver.elementClick(byText("Past"));
        await driver.execute("flutter:waitForTappable", byText("Future"));
        await driver.elementClick(byText("Future"));
    }

    public async addTodoSucess() {
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "New Todo_1 " + currentDate;
        await driver.execute("flutter:waitForTappable", byText(valuee));
        const elem = await driver.getElementText(byText(valuee));
        assert.strictEqual(elem, valuee);
    }

    public async editTodo() {
        console.log("FAB inside edittodo1");
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "New Todo_1 " + currentDate;
        await driver.elementClick(byText(valuee));
        const value = "Updated New Todo_1 " + currentDate;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementClick(byValueKey("addTitle"));

        await driver.elementSendKeys(byValueKey("addTitle"), value);
        // await driver.execute("flutter:waitForTappable", byValueKey("addDescriptionTile"));
        // await driver.elementClick(byValueKey("addDescriptionTile"))
        // await driver.execute("flutter:waitForTappable", byValueKey("editorKey"));
        // await driver.elementSendKeys(byValueKey("editorKey"), valuee);
        // await driver.execute("flutter:waitForTappable", byValueKey("sendDescription"))
        // await driver.elementClick(byValueKey("sendDescription"))
        await addtime();
        await driver.execute("flutter:waitForTappable", byValueKey("selectreminderTodo"));
        await driver.execute("smartui.takeScreenshot=bottomSheet_actionCommon_972");//appMobile_commonUI_widgets_reminder_stateDefaultOnActionScreen
        await driver.elementClick(byValueKey("selectreminderTodo"));

        await driver.execute("flutter:waitForTappable", byText("Before 10m"));
        await driver.execute("smartui.takeScreenshot=bottomSheet_remindMeSelect_3281"); //appMobile_commonUI_widgets_reminder_multipleReminderBottomsheet_stateDefault
        await driver.elementClick(byText("Before 10m"));
        await driver.execute("flutter:waitForTappable", byText("Custom"));
        await driver.elementClick(byText("Custom"));
        await driver.execute("flutter:waitForTappable", byText("After"));
        await driver.execute("smartui.takeScreenshot=overlay_reminderSelect_2190"); //appMobile_commonUI_widgets_reminder_multipleReminderBottomsheet_widget_stateDefault
        await driver.elementClick(byText("After"));
        await driver.execute("flutter:waitFor", byValueKey("reminderHourSpinner"), 3000);
        await driver.execute("smartui.takeScreenshot=overlay_reminderSelect_848");//appMobile_commonUI_widgets_reminder_multipleReminderBottomsheet_widget_stateSelectedOptionAfter
        // overlay_reminderSelect_6056//appMobile_commonUI_widgets_reminder_multipleReminderBottomsheet_widget_actionHourScroll
        await driver.execute("flutter:scroll", byValueKey("reminderHourSpinner"), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:waitFor", byValueKey("reminderMinSpinner"), 3000);
        //overlay_reminderSelect_1257//appMobile_commonUI_widgets_reminder_multipleReminderBottomsheet_widget_actionMinuteScroll
        await driver.execute("flutter:scroll", byValueKey("reminderMinSpinner"), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:waitForTappable", byText("CANCEL"));
        await driver.elementClick(byText("CANCEL"));
        //overlay_reminderSelect_102//appMobile_commonUI_widgets_reminder_multipleReminderBottomsheet_widget_actionTapButtonCancel
        await driver.execute("flutter:waitForTappable", byText("DISCARD"));
        await driver.execute("smartui.takeScreenshot=overlay_discard_5511");//appMobile_commonUI_widgets_reminder_multipleReminderBottomsheet_widget_discardConfirmationPopup_stateDefault
        await driver.elementClick(byText("DISCARD"));
        await driver.execute("flutter:waitForTappable", byText("Custom"));
        await driver.elementClick(byText("Custom"));
        await driver.execute("flutter:waitForTappable", byText("After"));
        await driver.elementClick(byText("After"));
        await driver.execute("flutter:waitForTappable", byText("SET"));
        await driver.elementClick(byText("SET"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.execute("smartui.takeScreenshot=bottomSheet_actionCommon_7041"); // Screenshot_repeat
        await driver.elementClick(byText("SAVE"));
        await addStartDate();
        try {
            await driver.execute("flutter:waitFor", byValueKey("repeatTodo"), 2000);
            await driver.elementClick(byValueKey("repeatTodo"));
        } catch (warn) {
            await driver.execute("flutter:waitForTappable", byValueKey("overflowMenu"));
            await driver.elementClick(byValueKey("overflowMenu"));
            await driver.execute("flutter:waitForTappable", byText("Repeat"));
            await driver.elementClick(byText("Repeat"));
        }

        // if repeat option is Monthly
        await driver.execute("flutter:waitForTappable", byText("Monthly"));
        await driver.execute("smartui.takeScreenshot=overlay_repeatSelect_413"); // appMobile_commonUI_widgets_repeat_stateDefault
        await driver.elementClick(byText("Monthly")); // repeatMonthDaySpinner
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.execute("smartui.takeScreenshot=overlay_repeatSelect_5977");//appMobile_commonUI_widgets_repeat_monthly_stateDefault
        await driver.elementClick(byText("SAVE"));

        // if repeat option is Weekly
        await driver.execute("flutter:waitForTappable", byValueKey("repeatTodo"));
        await driver.elementClick(byValueKey("repeatTodo"));
        await driver.execute("flutter:waitForTappable", byText("Weekly"));
        await driver.elementClick(byText("Weekly"));
        await driver.execute("smartui.takeScreenshot=overlay_repeatSelect_5839");//appMobile_commonUI_widgets_repeat_stateDefaultOfWeeklySelected
        await driver.switchContext("NATIVE_APP");
        const el17 = await driver.$("(//android.view.View[@content-desc=\"S\"])[2]");
        await el17.click();
        const el18 = await driver.$("(//android.view.View[@content-desc=\"S\"])[1]");
        await el18.click();
        const el19 = await driver.$("(//android.view.View[@content-desc=\"T\"])[1]");
        await el19.click();
        const el20 = await driver.$("(//android.view.View[@content-desc=\"T\"])[2]");
        await el20.click();
        await driver.switchContext("FLUTTER");
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.execute("smartui.takeScreenshot=overlay_repeatSelect_368");//appMobile_commonUI_widgets_repeat_stateDaysOnWeeklySelected
        await driver.elementClick(byText("SAVE"));

        // if repeat option is Yearly
        await driver.execute("flutter:waitForTappable", byValueKey("repeatTodo"));
        await driver.elementClick(byValueKey("repeatTodo"));
        await driver.execute("flutter:waitForTappable", byText("Yearly"));
        await driver.elementClick(byText("Yearly"));
        await driver.execute("flutter:scroll", byValueKey("yearlyMonthSpinner"), { dx: 50, dy: -100, durationMilliseconds: 200, frequency: 70 });
        await driver.execute("smartui.takeScreenshot=overlay_repeatSelect_1622"); //appMobile_commonUI_widgets_repeat_stateDefaultOfYearlySelected
        await driver.execute("flutter:scroll", byValueKey("yearlyDateSpinner"), { dx: 50, dy: -100, durationMilliseconds: 200, frequency: 20 });
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byValueKey("repeatTodo"));
        await driver.elementClick(byValueKey("repeatTodo"));
        await driver.execute("flutter:waitForTappable", byText("Daily"));
        await driver.elementClick(byText("Daily"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        try {
            await driver.execute("flutter:waitFor", byValueKey("addHashTag"), 2000);
            await driver.elementClick(byValueKey("addHashTag"));//S-2093
        } catch (warn) {
            await driver.execute("flutter:waitForTappable", byValueKey("overflowMenu"));
            await driver.elementClick(byValueKey("overflowMenu"));
            await driver.execute("flutter:waitForTappable", byText("Add hashtag"));
            await driver.elementClick(byText("Add hashtag"));//S-2093
            console.log("qwerty");
        }
        await driver.execute("flutter:waitForTappable", byValueKey("addSearchFilter"));//S-2923//S-1846
        await driver.execute("smartui.takeScreenshot=bottomSheet_hashtagSelect_1986");//appMobile_commonUI_widgets_hashtag_stateEmpty
        await driver.elementSendKeys(byValueKey("addSearchFilter"), "smile");
        await driver.execute("flutter:waitForTappable", byValueKey("addText"));
        await driver.elementClick(byValueKey("addText"));
        await driver.elementSendKeys(byValueKey("addSearchFilter"), "goodmorning");
        await driver.execute("flutter:waitForTappable", byValueKey("addText"));
        await driver.elementClick(byValueKey("addText"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.execute("smartui.takeScreenshot=bottomSheet_hashtagSelect_5429"); //need correction as per design//appMobile_commonUI_widgets_hashtag_stateDefault
        await driver.elementClick(byText("SAVE"));
        await addEndDate();
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));
    }

    public async editTodoSucess() {
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const value = "Updated New Todo_1 " + currentDate;
        await driver.execute("flutter:waitForTappable", byText(value));
        const elem = await driver.getElementText(byText(value));
        assert.strictEqual(elem, value);
    }

    public async deleteTodo() {
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const value = "Updated New Todo_1 " + currentDate;
        await driver.elementClick(byText(value));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        //S-3297_bottomSheet_todoAction
        await driver.execute("flutter:waitForTappable", byValueKey("seconadaryButton"));
        await driver.execute("smartui.takeScreenshot=overlay_todoActionDelete_1076")
        //needed S-1427 but not developed & now showing S-1076_overlay_todoActionDelete
        await driver.elementClick(byValueKey("seconadaryButton"));
        //S-2638_overlay_repeatableTodoActionDelete
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        //S-338_overlay_todoActionDelete
        //S-5999_toast_todoDeleted
    }

    public async deleteTodoPast() {
        await driver.execute("flutter:waitForTappable", byText("Todo"));
        await driver.elementClick(byText("Todo"));
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
        const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
        const result_sub2 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 360);
        const result_sub3 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 400);
        const d_sub = result_sub.getFullYear();
        const m_sub = result_sub.getMonth();
        const m_sub1 = result_sub1.getMonth();
        const m_sub2 = result_sub2.getMonth();
        const m_sub3 = result_sub3.getMonth();
        const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
        const pastDate1 = result_sub1.getDate() + "/" + (result_sub1.getMonth() + 1) + "/" + result_sub1.getFullYear();
        const pastDate2 = result_sub2.getDate() + "/" + (result_sub2.getMonth() + 1) + "/" + result_sub2.getFullYear();
        const pastDate3 = result_sub3.getDate() + "/" + (result_sub3.getMonth() + 1) + "/" + result_sub3.getFullYear();
        // const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const currentDateString = d.getDate() + "/" + monthNames[d.getMonth()] + "/" + d.getFullYear();
        const pastDateString = result_sub.getDate() + "/" + monthNames[m_sub] + "/" + result_sub.getFullYear();
        const pastDateString1 = result_sub1.getDate() + "/" + monthNames[m_sub1] + "/" + result_sub1.getFullYear();
        const pastDateString2 = result_sub2.getDate() + "/" + monthNames[m_sub2] + "/" + result_sub2.getFullYear();
        const pastDateString3 = result_sub3.getDate() + "/" + monthNames[m_sub3] + "/" + result_sub3.getFullYear();
        const value1 = "New Todo_1 " + pastDate;
        const value2 = "New Todo_1 " + pastDate1;
        const value3 = "New Todo_1 " + pastDate2;
        const value4 = "New Todo_1 " + pastDate3;
        await driver.execute("flutter:waitForTappable", byText(value1));
        await driver.elementClick(byText(value1));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        //S-1076_overlay_todoActionDelete
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText(value2));
        await driver.elementClick(byText(value2));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText(value3));
        await driver.elementClick(byText(value3));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText(value4));
        await driver.elementClick(byText(value4));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
    }

    public async deleteTodoFuture() {
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const result_addd = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
        const result_add1 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
        const result_add2 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 360);
        const result_add3 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 400);
        const d_add = result_addd.getFullYear();
        const m_add = result_addd.getMonth();
        const m_add1 = result_add1.getMonth();
        const m_add2 = result_add2.getMonth();
        const m_add3 = result_add3.getMonth();
        const futureDate = result_addd.getDate() + "/" + (result_addd.getMonth() + 1) + "/" + result_addd.getFullYear();
        const futureDate1 = result_add1.getDate() + "/" + (result_add1.getMonth() + 1) + "/" + result_add1.getFullYear();
        const futureDate2 = result_add2.getDate() + "/" + (result_add2.getMonth() + 1) + "/" + result_add2.getFullYear();
        const futureDate3 = result_add3.getDate() + "/" + (result_add3.getMonth() + 1) + "/" + result_add3.getFullYear();
        // const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
        const currentDateString = d.getDate() + "/" + monthNames[d.getMonth()] + "/" + d.getFullYear();
        const futureDateString = result_addd.getDate() + "/" + monthNames[m_add] + "/" + result_addd.getFullYear();
        const futureDateString1 = result_add1.getDate() + "/" + monthNames[m_add1] + "/" + result_add1.getFullYear();
        const futureDateString2 = result_add2.getDate() + "/" + monthNames[m_add2] + "/" + result_add2.getFullYear();
        const futureDateString3 = result_add3.getDate() + "/" + monthNames[m_add3] + "/" + result_add3.getFullYear();
        const value1 = "New Todo_1 " + futureDate;
        const value2 = "New Todo_1 " + futureDate1;
        const value3 = "New Todo_1 " + futureDate2;
        const value4 = "New Todo_1 " + futureDate3;
        await driver.execute("flutter:waitForTappable", byText('Today'));
        await driver.elementClick(byText('Today'));
        await driver.execute("flutter:waitForTappable", byText('Future'));
        await driver.elementClick(byText('Future'));
        await driver.execute("flutter:waitForTappable", byText('Todo'));
        await driver.elementClick(byText('Todo'));
        await driver.execute("flutter:waitForTappable", byText(value1));
        await driver.elementClick(byText(value1));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText(value2));
        await driver.elementClick(byText(value2));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText(value3));
        await driver.elementClick(byText(value3));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText(value4));
        await driver.elementClick(byText(value4));
        try {
            await driver.execute("flutter:waitFor", byValueKey("addHashTag"), 2000);
            await driver.elementClick(byValueKey("addHashTag"));
        } catch (warn) {
            await driver.execute("flutter:waitForTappable", byValueKey("overflowMenu"));
            await driver.elementClick(byValueKey("overflowMenu"));
            await driver.execute("flutter:waitForTappable", byText("Add hashtag"));
            await driver.elementClick(byText("Add hashtag"));
            console.log("qwerty");
        }
        await driver.execute("flutter:waitForTappable", byText("#morning"));
        await driver.execute("flutter:scroll", byText("#morning"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });// delete hashtag
        await driver.switchContext('NATIVE_APP')
        let el12 = await driver.$("//android.view.View[@content-desc=\"#morning\"]/android.widget.ImageView[2]");
        await el12.click();
        await driver.switchContext('FLUTTER')
        await driver.execute("flutter:waitForTappable", byText("DELETE"));
        await driver.elementClick(byText("DELETE"));
        await driver.execute("flutter:waitForTappable", byValueKey("close"));
        await driver.elementClick(byValueKey("close"));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
    }

    public async doneTodo() {
        console.log("FAB");
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "New Todo_1 " + currentDate;
        const elem = byText(valuee); // here if multiple entries with same name --conflict
        await driver.switchContext("NATIVE_APP");
        const el3 = await driver.$(`//android.view.View[@content-desc="${valuee}"]/android.widget.Button`);
        await el3.click();
        await driver.switchContext("FLUTTER");
        assert.strictEqual(await driver.getElementText(elem), valuee);
        console.log("Success");
    }
}
export default new Todo();
