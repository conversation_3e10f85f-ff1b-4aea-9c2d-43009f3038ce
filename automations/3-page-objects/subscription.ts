import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
class Subscription {

    public async subscription() {
        const hamburger = byValue<PERSON><PERSON>('hamburgerMenu')
        await driver.elementClick(hamburger);
        await driver.execute("smartui.takeScreenshot=overlay_hamburgerTray_3129");
        const subscription = byText('Subscription')
        await driver.elementClick(subscription);
        await driver.execute("smartui.takeScreenshot=screen_subscription_4546");
        const close = byValue<PERSON>ey('closeSubscriptionPage')
        await driver.elementClick(close);
        await driver.execute('smartui.takeScreenshot=Today_Screen')
    }

    public async subscriptionSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Today'))
        const element = await driver.getElementText(byText('Today'))
        console.log('element value is:' + element)
        const valuee = 'Today'
        assert.strictEqual(element, valuee)
    }
}
export default new Subscription();