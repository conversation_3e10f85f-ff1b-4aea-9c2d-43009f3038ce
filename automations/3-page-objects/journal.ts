import { byText, byTooltip, byType, byV<PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from "assert";
import { subDays } from "date-fns";

class Journal {
  public async addJournal() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Describe today " + currentDate;
    await driver.execute("flutter:waitForTappable", byText("Today"));
    await driver.elementClick(byText("Today"));
    const fabutton = byValueKey("increment");
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    const setupjournal = byText("Setup Journal");
    await driver.execute("flutter:waitForTappable", setupjournal);
    await driver.elementClick(setupjournal);
    await driver.execute("flutter:waitForTappable", by<PERSON><PERSON><PERSON><PERSON><PERSON>("addJournalTitle"));
    await driver.execute("smartui.takeScreenshot=screen_journalSetupAdd_1056");//appMobile_commonUI_journal_setupAdd_base_stateDefault
    await driver.elementClick(byValueKey("addJournalTitle"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), valuee);
    //bottomSheet_title_409//appMobile_commonUI_widgets_title_ruleJournalCharacterLimit
    await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
    await driver.elementClick(byValueKey("sendButton"));
    //screen_journalSetupAdd_220//appMobile_commonUI_habit_setupAdd_multipleChoice_base_alertPopupMinimumLimitOfOption_stateDefault
    await driver.execute("flutter:waitForTappable", byValueKey("selectJournalDateRange"));
    await driver.elementClick(byValueKey("selectJournalDateRange"));
    await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
    await driver.execute("smartui.takeScreenshot=journal_daterange_widget");
    await driver.elementClick(byValueKey("startDate"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("selectJournalRepeat"));
    await driver.elementClick(byValueKey("selectJournalRepeat"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    // Since conflict for S-Saturday S- Sunday T-Tuesday T-Thursday; taking code from appium inspector
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byValueKey("selectJournalTime"));
    await driver.elementClick(byValueKey("selectJournalTime"));
    await driver.execute("flutter:waitForTappable", byValueKey("timeHourSpinner"));
    // await driver.elementClick(byValueKey('toggle_time'))
    await driver.execute("flutter:scroll", byValueKey("timeHourSpinner"), { dx: 0, dy: 1000, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byValueKey("timeMinSpinner"));
    await driver.execute("flutter:scroll", byValueKey("timeMinSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byValueKey("selectionAmPm"));
    await driver.execute("flutter:scroll", byValueKey("selectionAmPm"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byValueKey("setJournalReminder"));
    await driver.elementClick(byValueKey("setJournalReminder"));
    await driver.execute("flutter:waitForTappable", byValueKey("reminderHourSpinner"));
    await driver.execute("flutter:scroll", byValueKey("reminderHourSpinner"), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byValueKey("reminderMinSpinner"));
    await driver.execute("flutter:scroll", byValueKey("reminderMinSpinner"), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
    await driver.elementClick(byValueKey("addHashTag"));
    await driver.execute("flutter:waitForTappable", byValueKey("addSearchFilter"));
    await driver.elementSendKeys(byValueKey("addSearchFilter"), "smile");
    await driver.execute("flutter:waitForTappable", byValueKey("addText"));
    await driver.elementClick(byValueKey("addText"));
    await driver.elementSendKeys(byValueKey("addSearchFilter"), "morning");
    await driver.execute("flutter:waitForTappable", byValueKey("addText"));
    await driver.elementClick(byValueKey("addText"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    // screen_journalSetupAdd_1966//appMobile_commonUI_journal_setupAdd_base_actionTapButtonSave
    //toast_journalSetupCreated_5335//appMobile_commonUI_journal_setupAdd_toastMessageSetupCreated_stateDefault
  }

  public async addJournalSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Describe today " + currentDate;
    await driver.execute("flutter:waitForTappable", byText(valuee));
    const elem = await driver.getElementText(byText(valuee));
    assert.strictEqual(elem, valuee);
  }

  public async editJournal() {
    console.log("FAB inside editjournal");
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Describe today " + currentDate;
    const elem = byText(valuee);
    await driver.execute("flutter:scrollIntoView", byValueKey("journalList"), { alignment: 1 });
    await driver.execute("smartui.takeScreenshot=screen_common_227");
    await driver.execute("flutter:waitForTappable", elem);
    assert.strictEqual(await driver.getElementText(elem), valuee);
    await driver.elementClick(elem);
    //screen_common_4022
    await driver.execute("flutter:waitForTappable", byValueKey("showJournalBottomSheet"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_journalAction_4935")
    //bottomSheet_journalAction_4551
    //S-2873_bottomSheet_actionCommon
    //S-954_bottomSheet_journalAction
    await driver.elementClick(byValueKey("showJournalBottomSheet")); // Edit journal setup;Reset journal entry
    await driver.execute("flutter:waitForTappable", byText("Edit journal setup"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_journalActionKebabmenu_1351")
    await driver.elementClick(byText("Edit journal setup"));
    //bottomSheet_journalActionKebabmenu_3994 
    await driver.switchContext("NATIVE_APP");
    const el4 = await driver.$("~Repeat Daily");
    await el4.click();
    await driver.switchContext("FLUTTER");
    await driver.execute("flutter:waitForTappable", byText("EDIT"));
    await driver.execute("smartui.takeScreenshot=overlay_journalActionEditAlert_5442")
    await driver.elementClick(byText("EDIT"));
    await driver.execute("flutter:waitForTappable", byValueKey("addJournalTitle"));
    await driver.elementClick(byValueKey("addJournalTitle"));
    await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
    await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), "updated" + valuee);
    await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
    await driver.elementClick(byValueKey("sendButton"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byValueKey("moodSelector"));
    await driver.elementClick(byValueKey("moodSelector"));
    //bottomSheet_journalAction_3181
    await driver.execute("flutter:waitForTappable", byText("Meh"));
    await driver.elementClick(byText("Meh"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
    //bottomSheet_journalAction_54
    //bottomSheet_journalAction_5531
  }

  public async editJournalSucess() {
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Describe today " + currentDate;
    const value = "updated" + valuee;
    await driver.execute("flutter:scrollIntoView", byValueKey("journalList"), { alignment: 1 });
    await driver.execute("flutter:waitForTappable", byText(value));
    const elem = await driver.getElementText(byText(value));
    assert.strictEqual(elem, value);
    await driver.elementClick(byText(value));
  }

  public async deleteJournal() {
    await driver.execute("flutter:waitForTappable", byValueKey("showJournalBottomSheet"));
    await driver.elementClick(byValueKey("showJournalBottomSheet")); // Edit journal setup;Reset journal entry
    await driver.execute("flutter:waitForTappable", byText("Edit journal setup"));
    await driver.elementClick(byText("Edit journal setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
    console.log("today_journal done");
  }

  public async deleteJournalPast() {
    // await driver.execute('flutter:waitForTappable', byText('Today'))
    // await driver.elementClick(byText('Today'))
    const d = new Date();
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
    const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
    const result_sub2 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 370);
    const result_sub3 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 410);
    const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
    const pastDate1 = result_sub1.getDate() + "/" + (result_sub1.getMonth() + 1) + "/" + result_sub1.getFullYear();
    const pastDate2 = result_sub2.getDate() + "/" + (result_sub2.getMonth() + 1) + "/" + result_sub2.getFullYear();
    const pastDate3 = result_sub3.getDate() + "/" + (result_sub3.getMonth() + 1) + "/" + result_sub3.getFullYear();
    const value1 = "Describe today " + pastDate;
    const value2 = "Describe today " + pastDate1;
    const value3 = "Describe today " + pastDate2;
    const value4 = "Describe today " + pastDate3;
    await driver.execute("flutter:waitForTappable", byText(value1));
    await driver.elementClick(byText(value1));
    await driver.execute("flutter:waitForTappable", byValueKey("showJournalBottomSheet"));
    await driver.elementClick(byValueKey("showJournalBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit journal setup"));
    await driver.elementClick(byText("Edit journal setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
    await driver.execute("flutter:waitForTappable", byText(value2));
    await driver.elementClick(byText(value2));
    await driver.execute("flutter:waitForTappable", byValueKey("showJournalBottomSheet"));
    await driver.elementClick(byValueKey("showJournalBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit journal setup"));
    await driver.elementClick(byText("Edit journal setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
    await driver.execute("flutter:waitForTappable", byText(value3));
    await driver.elementClick(byText(value3));
    await driver.execute("flutter:waitForTappable", byValueKey("showJournalBottomSheet"));
    await driver.elementClick(byValueKey("showJournalBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit journal setup"));
    await driver.elementClick(byText("Edit journal setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
    await driver.execute("flutter:waitForTappable", byText(value4));
    await driver.elementClick(byText(value4));
    await driver.execute("flutter:waitForTappable", byValueKey("showJournalBottomSheet"));
    await driver.elementClick(byValueKey("showJournalBottomSheet"));
    await driver.execute("flutter:waitForTappable", byText("Edit journal setup"));
    await driver.elementClick(byText("Edit journal setup"));
    await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
    await driver.elementClick(byValueKey("deleteButton"));
    await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
    await driver.elementClick(byValueKey("quaternaryButton"));
  }
}
export default new Journal();
