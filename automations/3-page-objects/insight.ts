import { insightDonePast } from "./page";
import { subDays } from "date-fns";
import { byText, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";

class Insight {
  public async doneTodoPast() {
    await driver.execute('flutter:waitForTappable', by<PERSON><PERSON><PERSON><PERSON><PERSON>('hamburgerMenu'))
    await driver.elementClick(by<PERSON><PERSON><PERSON><PERSON><PERSON>('hamburgerMenu'))
    await driver.execute("flutter:waitForTappable", byText("Insight"));
    await driver.elementClick(byText("Insight"));
    await driver.execute("flutter:waitForTappable", byText("Todo"));
    await driver.elementClick(byText("Todo"));
    await driver.execute("flutter:waitForTappable", by<PERSON><PERSON><PERSON><PERSON><PERSON>("insightTodo"));
    await driver.execute("smartui.takeScreenshot=insight_todo");
    await driver.execute("flutter:scroll", by<PERSON><PERSON><PERSON><PERSON><PERSON>("insightTodo"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
    const d = new Date();
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
    const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const m_sub = result_sub.getMonth();
    const m_sub1 = result_sub1.getMonth();
    const currentDateString = d.getDate() + "/" + monthNames[d.getMonth()] + "/" + d.getFullYear();
    const pastDateString = result_sub.getDate() + "/" + monthNames[m_sub] + "/" + result_sub.getFullYear();
    const pastDateString1 = result_sub1.getDate() + "/" + monthNames[m_sub1] + "/" + result_sub1.getFullYear();
    const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
    const pastDate1 = result_sub1.getDate() + "/" + (result_sub1.getMonth() + 1) + "/" + result_sub1.getFullYear();
    const valuePast5days = "New Todo_1 " + pastDate;
    const valuePast30days = "New Todo_1 " + pastDate1;
    await insightDonePast(pastDateString, currentDateString, valuePast5days, valuePast30days);
    console.log("1st insight todo done");
    await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
    await driver.elementClick(byValueKey('closeNotificationSettings'))
    await driver.execute("flutter:waitForTappable", byText("Past"));
    await driver.elementClick(byText("Past"));
    await driver.execute('flutter:waitForTappable', byValueKey('hamburgerMenu'))
    await driver.elementClick(byValueKey('hamburgerMenu'))
    await driver.execute("flutter:waitForTappable", byText("Insight"));
    await driver.elementClick(byText("Insight"));
    await insightDonePast(pastDateString1, currentDateString, valuePast5days, valuePast30days);
    await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
    await driver.elementClick(byValueKey('closeNotificationSettings'))
    console.log("2nd insight todo done");
  }

  public async doneHabitPast() {
    await driver.execute('flutter:waitForTappable', byValueKey('hamburgerMenu'))
    await driver.elementClick(byValueKey('hamburgerMenu'))
    await driver.execute("flutter:waitForTappable", byText("Insight"));
    await driver.elementClick(byText("Insight"));
    await driver.execute("flutter:waitForTappable", byText("Habit"));
    await driver.elementClick(byText("Habit"));
    await driver.execute("flutter:waitForTappable", byValueKey("insightHabit"));
    await driver.execute("smartui.takeScreenshot=insight_habit");
    await driver.execute("flutter:scroll", byValueKey("insightHabit"), { dx: 50, dy: -1500, durationMilliseconds: 1000, frequency: 80 });
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const datee = "" + d.getDate();
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
    const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
    const datee1 = "" + result_sub.getDate();
    const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
    const pastDate1 = result_sub1.getDate() + "/" + (result_sub1.getMonth() + 1) + "/" + result_sub1.getFullYear();
    const valuePast5days = "Are you happy ? " + pastDate;
    const valuePast30days = "Are you happy ? " + pastDate1;
    console.log("datee & datee1", +datee + " " + datee1);
    await driver.execute("flutter:waitForTappable", byValueKey("selectJournal"));
    await driver.elementClick(byValueKey("selectJournal"));
    await driver.execute("flutter:waitForTappable", byText(valuePast5days));
    await driver.elementClick(byText(valuePast5days));
    await driver.execute("flutter:scroll", byValueKey("insightHabit"), { dx: 50, dy: -1500, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText(datee));
    await driver.elementClick(byText(datee));
    await driver.execute("flutter:waitForTappable", byText(valuePast5days));
    await driver.elementClick(byText(valuePast5days));
    await driver.execute("flutter:waitForTappable", byText("Yes"));
    await driver.elementClick(byText("Yes"));
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    await driver.elementClick(byText("CLOSE"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
    if (result_sub.getDate() < d.getDate()) {
      console.log("in if");
      await driver.execute("flutter:waitForTappable", byText(datee1));
      await driver.elementClick(byText(datee1));
      await driver.execute("flutter:waitForTappable", byText(valuePast5days));
      await driver.elementClick(byText(valuePast5days));
      await driver.execute("flutter:waitForTappable", byText("No"));
      await driver.elementClick(byText("No"));
      await driver.execute("flutter:waitForTappable", byText("CLOSE"));
      await driver.elementClick(byText("CLOSE"));
      await driver.execute("flutter:waitForTappable", byValueKey("close"));
      await driver.elementClick(byValueKey("close"));
      await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
      await driver.elementClick(byValueKey('closeNotificationSettings'))
      await driver.execute("flutter:waitForTappable", byText("Past"));
      await driver.elementClick(byText("Past"));
    } else if (result_sub.getDate() > d.getDate()) {
      await driver.execute("flutter:waitForTappable", byValueKey("previousMonth"));
      await driver.elementClick(byValueKey("previousMonth"));
      await driver.execute("flutter:waitForTappable", byText(datee1));
      await driver.elementClick(byText(datee1));
      await driver.execute("flutter:waitForTappable", byText(valuePast5days));
      await driver.elementClick(byText(valuePast5days));
      await driver.execute("flutter:waitForTappable", byText("No"));
      await driver.elementClick(byText("No"));
      await driver.execute("flutter:waitForTappable", byText("CLOSE"));
      await driver.elementClick(byText("CLOSE"));
      await driver.execute("flutter:waitForTappable", byValueKey("close"));
      await driver.elementClick(byValueKey("close"));
      await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
      await driver.elementClick(byValueKey('closeNotificationSettings'))
      await driver.execute("flutter:waitForTappable", byText("Past"));
      await driver.elementClick(byText("Past"));
    }
  }

  public async doneJournalPast() {
    await driver.execute('flutter:waitForTappable', byValueKey('hamburgerMenu'))
    await driver.elementClick(byValueKey('hamburgerMenu'))
    await driver.execute("flutter:waitForTappable", byText("Insight"));
    await driver.elementClick(byText("Insight"));
    await driver.execute("flutter:waitForTappable", byText("Journal"));
    await driver.elementClick(byText("Journal"));
    await driver.execute("flutter:waitForTappable", byValueKey("insightJournal"));
    await driver.execute("smartui.takeScreenshot=insight_journal");
    await driver.execute("flutter:scroll", byValueKey("insightJournal"), { dx: 50, dy: -1500, durationMilliseconds: 1000, frequency: 80 });
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const datee = "" + d.getDate();
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
    const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
    const datee1 = "" + result_sub.getDate();
    const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
    const pastDate1 = result_sub1.getDate() + "/" + (result_sub1.getMonth() + 1) + "/" + result_sub1.getFullYear();
    const valuePast5days = "Describe today " + pastDate;
    const valuePast30days = "Describe today " + pastDate1;
    console.log("datee & datee1", +datee + " " + datee1);
    await driver.execute("flutter:waitForTappable", byValueKey("selectJournal"));
    await driver.elementClick(byValueKey("selectJournal"));
    await driver.execute("flutter:waitForTappable", byText(valuePast5days));
    await driver.elementClick(byText(valuePast5days));
    await driver.execute("flutter:scroll", byValueKey("insightJournal"), { dx: 50, dy: -1500, durationMilliseconds: 1000, frequency: 80 });
    await driver.execute("flutter:waitForTappable", byText(datee));
    await driver.elementClick(byText(datee));
    await driver.execute("flutter:waitForTappable", byText(valuePast5days));
    await driver.elementClick(byText(valuePast5days));
    await driver.execute("flutter:waitForTappable", byValueKey("moodSelector"));
    await driver.elementClick(byValueKey("moodSelector"));
    await driver.execute("flutter:waitForTappable", byText("Meh"));
    await driver.elementClick(byText("Meh"));
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    await driver.elementClick(byText("CLOSE"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
    if (result_sub.getDate() < d.getDate()) {
      console.log("in if");
      await driver.execute("flutter:waitForTappable", byText(datee1));
      await driver.elementClick(byText(datee1));
      await driver.execute("flutter:waitForTappable", byText(valuePast5days));
      await driver.elementClick(byText(valuePast5days));
      await driver.execute("flutter:waitForTappable", byValueKey("moodSelector"));
      await driver.elementClick(byValueKey("moodSelector"));
      await driver.execute("flutter:waitForTappable", byText("Happy"));
      await driver.elementClick(byText("Happy"));
      await driver.execute("flutter:waitForTappable", byText("CLOSE"));
      await driver.elementClick(byText("CLOSE"));
      await driver.execute("flutter:waitForTappable", byValueKey("close"));
      await driver.elementClick(byValueKey("close"));
      await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
      await driver.elementClick(byValueKey('closeNotificationSettings'))
      await driver.execute("flutter:waitForTappable", byText("Past"));
      await driver.elementClick(byText("Past"));
    } else if (result_sub.getDate() > d.getDate()) {
      await driver.execute("flutter:waitForTappable", byValueKey("previousMonth"));
      await driver.elementClick(byValueKey("previousMonth"));
      await driver.execute("flutter:waitForTappable", byText(datee1));
      await driver.elementClick(byText(datee1));
      await driver.execute("flutter:waitForTappable", byText(valuePast5days));
      await driver.elementClick(byText(valuePast5days));
      await driver.execute("flutter:waitForTappable", byValueKey("moodSelector"));
      await driver.elementClick(byValueKey("moodSelector"));
      await driver.execute("flutter:waitForTappable", byText("Very Happy"));
      await driver.elementClick(byText("Very Happy"));
      await driver.execute("flutter:waitForTappable", byText("CLOSE"));
      await driver.elementClick(byText("CLOSE"));
      await driver.execute("flutter:waitForTappable", byValueKey("close"));
      await driver.elementClick(byValueKey("close"));
      await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
      await driver.elementClick(byValueKey('closeNotificationSettings'))
      await driver.execute("flutter:waitForTappable", byText("Past"));
      await driver.elementClick(byText("Past"));
    }
  }
}

export default new Insight();
