import { byText, byTooltip, byType, byV<PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from "assert";
import { addtitle } from "./page";

export const waitFor = async (delay: number, message?: string): Promise<any> => {
    await new Promise((resolve) => setTimeout(resolve, delay));
    if (message !== undefined) {
        await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_2022");
        console.log(message);
    }
};
class TimeTypeHabit {
    public async addTimeTypeHabitWithAndWithoutGoal() {
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "Daily workouts With Goal? " + currentDate;
        const valuee1 = "Daily workouts Without Goal? " + currentDate;
        const fabutton = byValueKey("increment");
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);
        const setuphabit = byText("Setup Habit");
        await driver.execute("flutter:waitForTappable", setuphabit);
        await driver.elementClick(setuphabit);
        await driver.execute("flutter:waitForTappable", byValueKey("selectHabitType"));
        await driver.elementClick(byValueKey("selectHabitType"));
        await driver.execute("flutter:waitForTappable", byText("Time"));
        await driver.elementClick(byText("Time"));
        await driver.execute("flutter:waitForTappable", byValueKey("setGoal"));
        await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_2022");//appMobile_commonUI_habit_setupAdd_timeType_base_stateDefault
        await driver.elementClick(byValueKey("setGoal"));
        await driver.execute("flutter:waitFor", byValueKey("timerMinSpinner"), 3000);
        await driver.execute("smartui.takeScreenshot=overlay_habitSetupAddTimerSelect_1737");//appMobile_commonUI_habit_setupAdd_timeType_base_timer_stateOpen
        await driver.execute("flutter:scroll", byValueKey("timerMinSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:waitFor", byValueKey("timerSecSpinner"), 5000);
        await driver.execute("flutter:scroll", byValueKey("timerSecSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:waitForTappable", byText("SET"));
        await driver.elementClick(byText("SET"));
        await driver.execute("flutter:waitForTappable", byValueKey("vibration")); //off
        await driver.elementClick(byValueKey("vibration"));
        await driver.execute("flutter:waitForTappable", byValueKey("vibration")); //on
        await driver.execute("smartui.takeScreenshot=toast_habitTimerTypeVibrationTurnedOff_2811");//appMobile_commonUI_habit_setupAdd_timeType_base_toastVibrationTurnedOff_stateDefault
        await driver.elementClick(byValueKey("vibration"));
        await driver.execute("flutter:waitForTappable", byValueKey("sound"));
        await driver.execute("smartui.takeScreenshot=toast_habitTimerTypeVibrationTurnedOn_1571");//appMobile_commonUI_habit_setupAdd_timeType_base_toastVibrationTurnedOn_stateDefault
        await driver.elementClick(byValueKey("sound"));
        await driver.execute("flutter:waitForTappable", byText("Mevolve 3"));
        await driver.execute("smartui.takeScreenshot=bottomSheet_soundSelect_4783");//appMobile_commonUI_habit_setupAdd_timeType_base_soundSettingsBottomSheet_stateDefault
        await driver.elementClick(byText("Mevolve 3"));
        await driver.execute("flutter:waitForTappable", byText("APPLY"));
        await driver.elementClick(byText("APPLY"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("OK"));
        await driver.execute("smartui.takeScreenshot=overlay_habitSetupAddMandatoryAlert_4236");//appMobile_commonUI_habit_setupAdd_timeType_base_alertPopupMandatoryFieldNotFilled_stateDefault
        await driver.elementClick(byText("OK"));
        //overlay_habitSetupAddMandatoryAlert_3021//appMobile_commonUI_habit_setupAdd_timeType_base_alertPopupMandatoryFieldNotFilled_actionTapButtonOk
        await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
        await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_5498");//appMobile_commonUI_habit_setupAdd_timeType_base_stateMandatoryFieldNotFilled
        await driver.elementClick(byValueKey("addHabitTitle"));
        await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
        await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), valuee);
        await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
        await driver.elementClick(byValueKey("sendButton"));
        await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
        await driver.elementClick(byValueKey("selectHabitDateRange"));
        await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
        await driver.elementClick(byValueKey("startDate")); // Today;Next Saturday;Next Sunday;After 1 week;
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE")); // Today is selected
        await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
        await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("APPLY"));
        await driver.elementClick(byText("APPLY"));
        await driver.execute("flutter:waitForTappable", byValueKey("selectHabitRepeat"));
        await driver.elementClick(byValueKey("selectHabitRepeat"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE")); // if need specific days need to use alphabets and for Tuesday;Thursday and Saturday;Sunday need appium code
        await driver.execute("flutter:waitForTappable", byValueKey("addTime"));
        await driver.elementClick(byValueKey("addTime"));
        await driver.execute("flutter:scroll", byValueKey("timeHourSpinner"), { dx: 0, dy: 1000, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:scroll", byValueKey("timeMinSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:scroll", byValueKey("selectionAmPm"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:waitForTappable", byText("SET"));
        await driver.elementClick(byText("SET"));
        await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
        await driver.elementClick(byValueKey("addHashTag"));
        await driver.execute("flutter:waitForTappable", byText("#smile"));
        await driver.execute("smartui.takeScreenshot=hashtag_widget"); // Screenshot_bottomsheet
        await driver.elementClick(byText("#smile"));
        await driver.execute("flutter:waitForTappable", byText("#morning"));
        await driver.elementClick(byText("#morning"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", fabutton);
        await driver.elementClick(fabutton);
        await driver.execute("flutter:waitForTappable", setuphabit);
        await driver.elementClick(setuphabit);
        await driver.execute("flutter:waitForTappable", byValueKey("selectHabitType"));
        await driver.elementClick(byValueKey("selectHabitType"));
        await driver.execute("flutter:waitForTappable", byText("Time"));
        await driver.elementClick(byText("Time"));
        await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
        await driver.elementClick(byValueKey("addHabitTitle"));
        await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
        await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), valuee1);
        await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
        await driver.elementClick(byValueKey("sendButton"));
        await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
        await driver.elementClick(byValueKey("selectHabitDateRange"));
        await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
        await driver.elementClick(byValueKey("startDate")); // Today;Next Saturday;Next Sunday;After 1 week;
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE")); // Today is selected
        await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
        await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("APPLY"));
        await driver.elementClick(byText("APPLY"));
        await driver.execute("flutter:waitForTappable", byValueKey("selectHabitRepeat"));
        await driver.elementClick(byValueKey("selectHabitRepeat"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE")); // if need specific days need to use alphabets and for Tuesday;Thursday and Saturday;Sunday need appium code
        await driver.execute("flutter:waitForTappable", byValueKey("addTime"));
        await driver.elementClick(byValueKey("addTime"));
        await driver.execute("flutter:scroll", byValueKey("timeHourSpinner"), { dx: 0, dy: 1000, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:scroll", byValueKey("timeMinSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:scroll", byValueKey("selectionAmPm"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
        await driver.execute("flutter:waitForTappable", byText("SET"));
        await driver.elementClick(byText("SET"));
        await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
        await driver.elementClick(byValueKey("addHashTag"));
        await driver.execute("flutter:waitForTappable", byText("#smile"));
        await driver.execute("smartui.takeScreenshot=hashtag_widget"); // Screenshot_bottomsheet
        await driver.elementClick(byText("#smile"));
        await driver.execute("flutter:waitForTappable", byText("#morning"));
        await driver.elementClick(byText("#morning"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText(valuee));
        await driver.elementClick(byText(valuee));
        await driver.execute("flutter:waitForTappable", byValueKey("addAction"));
        //await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeTimer_3035");////appMobile_commonUI_habit_action_timeType_stateDefaultWithGoal
        await driver.elementClick(byValueKey("addAction"));
        await driver.execute("flutter:waitForTappable", byValueKey("close"));
        await driver.elementClick(byValueKey("close"));
        //await driver.execute("flutter:scrollIntoView", byValueKey("habitList"), { alignment: 1 });
        await driver.execute("flutter:waitForTappable", byText(valuee1));
        //await driver.execute("smartui.takeScreenshot=screen_common_5444");//appMobile_commonUI_habit_action_timeType_stateSingleTimerRunningTopBar
        await driver.elementClick(byText(valuee1));
        await driver.execute("flutter:waitForTappable", byValueKey("addAction"));
        //await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeTimer_3923");//appMobile_commonUI_habit_action_timeType_stateDefaultWithoutGoal
        await driver.elementClick(byValueKey("addAction"));
        await driver.execute("flutter:waitForTappable", byValueKey("close"));
        await driver.elementClick(byValueKey("close"));
        await driver.execute("flutter:waitForTappable", byValueKey("alertBarButton"));
        await driver.execute("smartui.takeScreenshot=screen_common_5812");//appMobile_commonUI_habit_action_timeType_stateMultipleTimersRunningTopBar
        //await driver.elementClick(byValueKey("alertBarButton"));
        //screen_common_179//appMobile_commonUI_habit_action_timeType_actionTapTopBannerIconInfo
        await driver.execute("flutter:waitForTappable", byText(valuee));
        await driver.execute("smartui.takeScreenshot=bottomSheet_habitTimerTypeTimerRunning_5663");//appMobile_commonUI_habit_action_timeType_timerRunningBottomSheet_stateDefault
        await driver.elementClick(byText(valuee));
        await driver.execute("flutter:waitForTappable", byValueKey("addAction"));
        await driver.elementClick(byValueKey("addAction"));
        await driver.execute("flutter:waitForTappable", byValueKey("showAlarmBottomSheet"));
        await driver.elementClick(byValueKey("showAlarmBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("Mevolve 4"));
        await driver.elementClick(byText("Mevolve 4"));
        await driver.execute("flutter:waitForTappable", byText("APPLY"));
        await driver.elementClick(byText("APPLY"));
        await driver.execute("flutter:waitForTappable", byValueKey("vibrationIcon"));
        await driver.elementClick(byValueKey("vibrationIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("showAlarmBottomSheet"));
        await driver.elementClick(byValueKey("showAlarmBottomSheet"));
        //bottomSheet_habitActionHabitTypeTimer_3257
        await driver.execute("flutter:waitForTappable", byText("Mevolve 2"));
        await driver.elementClick(byText("Mevolve 2"));
        await driver.execute("flutter:waitForTappable", byText("APPLY"));
        await driver.elementClick(byText("APPLY"));
        //bottomSheet_habitActionHabitTypeTimer_5474//appMobile_commonUI_habit_action_timeType_actionTapButtonFabStop
        await driver.execute("flutter:waitForTappable", byText('CLOSE'));
        await driver.elementClick(byText('CLOSE'));
        //bottomSheet_habitActionCommon_607//appMobile_commonUI_habit_action_common_actionTapButtonClose
        //bottomSheet_habitActionCommon_964//appMobile_commonUI_habit_action_common_actionTapCloseIcon
        await driver.execute("flutter:waitForTappable", byValueKey("alertBarButton"));
        await driver.elementClick(byValueKey("alertBarButton"));
        // await driver.execute("flutter:waitForTappable", byText(valuee1));
        // //bottom sheet on clicking alert bar screenshot
        // await driver.elementClick(byText(valuee1));
        // await driver.execute("flutter:waitForTappable", byValueKey("addAction"));
        // await driver.elementClick(byValueKey("addAction"));
        // await driver.execute("flutter:waitForTappable", byText('CLOSE'));
        // await driver.elementClick(byText('CLOSE'));
        await driver.execute("flutter:waitForTappable", byText('Stop'));
        await driver.elementClick(byText('Stop'));
    }

    public async addTimeTypeHabitSucess() {
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "Daily workouts With Goal? " + currentDate;
        await driver.execute("flutter:waitForTappable", byText(valuee));
        const elem = await driver.getElementText(byText(valuee));
        assert.strictEqual(elem, valuee);
    }

    public async editTimeTypeHabitsToday() {
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "Daily workouts With Goal? " + currentDate;
        const valuee1 = "Updated_Daily workouts With Goal?"
        // const elem = byText(valuee); // here if multiple entries with same name --conflict
        // assert.strictEqual(await driver.getElementText(elem), valuee);
        // console.log("Success");
        await driver.execute("flutter:waitForTappable", byText(valuee));
        await driver.execute("smartui.takeScreenshot=screen_common_5059");//appMobile_commonUI_habit_action_timeType_stateDefaultOnListScreenRespondedWithGoal
        //screen_common_2618//appMobile_commonUI_habit_action_timeType_stateDefaultOnListScreenRespondedWithoutGoal
        await driver.elementClick(byText(valuee));
        await driver.execute("flutter:waitForTappable", byValueKey("deleteTimerEntry"));
        //await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeTimer_4500");//appMobile_commonUI_habit_action_timeType_stateSoundAndVibrationTurnedOn
        await driver.elementClick(byValueKey("deleteTimerEntry"));
        //bottomSheet_habitActionHabitTypeTimer_2591//appMobile_commonUI_habit_action_timeType_actionTapInputEntryIconDelete
        await driver.execute("flutter:waitForTappable", byValueKey("addAction"));
        await driver.elementClick(byValueKey("addAction"));
        //bottomSheet_habitActionHabitTypeTimer_4802
        await driver.execute("flutter:waitForTappable", byValueKey("showAlarmBottomSheet"));
        //----await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeTimer_4425");//appMobile_commonUI_habit_action_timeType_stateTimerOn
        //toast_habitResponseEntryDeleted_2128//appMobile_commonUI_habit_action_timeType_toastMessageEntryDeleted_stateDefault
        await driver.elementClick(byValueKey("showAlarmBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("None"));
        await driver.elementClick(byText("None"));
        await driver.execute("flutter:waitForTappable", byText("APPLY"));
        await driver.elementClick(byText("APPLY"));
        await driver.execute("flutter:waitForTappable", byValueKey("vibrationIcon"));
        await driver.elementClick(byValueKey("vibrationIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("addAction"));
        //await driver.execute("smartui.takeScreenshot=bottomSheet_habitActionHabitTypeTimer_3056");//appMobile_commonUI_habit_action_timeType_stateSoundAndVibrationTurnedOff
        //toast_habitTimerTypeVibrationTurnedOn_5541//appMobile_commonUI_habit_action_timeType_toastVibrationTurnedOn_stateDefault
        await driver.elementClick(byValueKey("addAction"));
        await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
        //S-2873_bottomSheet_actionCommon
        //S-6125_bottomSheet_habitActionCommon
        await driver.elementClick(byValueKey("showHabitBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("Edit habit setup"));
        await driver.elementClick(byText("Edit habit setup"));
        await driver.switchContext("NATIVE_APP");
        const el8 = await driver.$("/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]/android.view.View");
        await el8.click();
        await driver.switchContext("FLUTTER");
        await driver.execute("flutter:waitForTappable", byText("EDIT"));
        await driver.elementClick(byText("EDIT"));
        await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
        //bottomSheet_habitActionCommon_2279//appMobile_commonUI_habit_action_timeType_ruleVibrationAndSoundSelection
        await driver.elementClick(byValueKey("addHabitTitle"));
        await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
        await addtitle(valuee1);
        await driver.execute("flutter:waitForTappable", byValueKey("vibration"));
        await driver.elementClick(byValueKey("vibration"));
        await driver.execute("flutter:waitForTappable", byValueKey("sound"));
        await driver.elementClick(byValueKey("sound"));
        await driver.execute("flutter:waitForTappable", byText("Mevolve 2"));
        await driver.elementClick(byText("Mevolve 2"));
        await driver.execute("flutter:waitForTappable", byText("APPLY"));
        await driver.elementClick(byText("APPLY"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
        await driver.elementClick(byValueKey("showHabitBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("Edit habit setup"));
        await driver.elementClick(byText("Edit habit setup"));
        await driver.switchContext("NATIVE_APP");
        await el8.click();
        await driver.switchContext("FLUTTER");
        await driver.execute("flutter:waitForTappable", byText("EDIT"));
        await driver.elementClick(byText("EDIT"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
        //await driver.execute("flutter:waitForTappable", byValueKey('titleBar'));
        //await driver.execute('flutter:scroll', byValueKey('bottomSheetTitleBar'), { dx: 50, dy: -100, durationMilliseconds: 200, frequency: 30 })
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));
    }

    public async editTimeTypeHabitsSucess() {
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const value = "Updated_Daily workouts With Goal?"
        await driver.execute("flutter:waitForTappable", byText(value));
        const elem = await driver.getElementText(byText(value));
        console.log('edittimetypesuccess')
        assert.strictEqual(elem, value);
    }

    public async deleteTimeTypeHabitToday() {
        const d = new Date();
        console.log('inside delete1')
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const value1 = "Daily workouts Without Goal? " + currentDate;
        const value = "Updated_Daily workouts With Goal?"
        await driver.execute("flutter:waitForTappable", byText(value));
        console.log('inside delete2')
        await driver.elementClick(byText(value));
        await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
        await driver.elementClick(byValueKey("showHabitBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("Edit habit setup"));
        await driver.elementClick(byText("Edit habit setup"));
        await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
        //await driver.execute("smartui.takeScreenshot=screen_habitSetupAdd_4489");//appMobile_commonUI_habit_setupAdd_timeType_base_stateGoalAddedWithSoundOnVibrationOff
        //screen_habitSetupAdd_757//appMobile_commonUI_habit_setupAdd_timeType_base_stateFilled
        await driver.elementClick(byValueKey("deleteButton"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText(value1));
        await driver.elementClick(byText(value1));
        await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
        await driver.elementClick(byValueKey("showHabitBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("Edit habit setup"));
        await driver.elementClick(byText("Edit habit setup"));
        await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
        await driver.elementClick(byValueKey("deleteButton"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
    }
}

export default new TimeTypeHabit();
