import { byText, byTooltip, byType, byV<PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { addDatePastHabit, addDatePastJournal, addtitle } from "./page";
import addDays from "date-fns/addDays";
import { subDays } from "date-fns";

class Past {
  public async pastTodoDateFilter() {
    await driver.execute("flutter:waitForTappable", byText("Past"));
    await driver.elementClick(byText("Past"));
    await driver.execute("flutter:waitForTappable", byText("Todo"));
    await driver.elementClick(byText("Todo"));
    await driver.execute("flutter:scrollIntoView", byValue<PERSON><PERSON>("pastTodoList"), { alignment: 1 });
    await driver.execute("flutter:waitForTappable", byValue<PERSON>ey("dateFilter"));
    //await driver.execute("smartui.takeScreenshot=tab_pastTodo_5493"); //appMobile_screens_past_todo_todoTab_emptyTab_stateFeatureNotUsed
    await driver.execute("smartui.takeScreenshot=tab_pastTodo_1007"); //appMobile_screens_past_todo_todoTab_stateDefault
    //tab_pastTodo_2541
    await driver.elementClick(byValueKey("dateFilter"));
    await driver.execute("flutter:waitForTappable", byText("Last 7 Days"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_dateFilter_1323"); // appMobile_commonUI_filters_dateFilter_past_bottomSheet_stateDefault
    await driver.elementClick(byText("Last 7 Days"));
    //bottomSheet_dateFilter_5360//appMobile_commonUI_filters_dateFilter_past_bottomSheet_actionTapOptionLast7Days
    await driver.execute("flutter:waitForTappable", byValueKey("pastTodoList"));
    //await driver.execute("flutter:scroll", byValueKey("pastTodoList"), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("dateFilter"));
    await driver.elementClick(byValueKey("dateFilter"));
    await driver.execute("flutter:waitForTappable", byText("Last 30 Days"));
    await driver.elementClick(byText("Last 30 Days"));
    //bottomSheet_dateFilter_4457//appMobile_commonUI_filters_dateFilter_past_bottomSheet_actionTapOptionLast30Days
    await driver.execute("flutter:waitForTappable", byValueKey("dateFilter"));
    await driver.execute("smartui.takeScreenshot=S-959_tab_pastCommon");
    await driver.elementClick(byValueKey("dateFilter"));
    //tab_pastTodo_2634//appMobile_screens_past_todo_todoTab_actionDateFilterTap
    await driver.execute("flutter:waitForTappable", byText("Last 12 Months"));
    await driver.elementClick(byText("Last 12 Months"));
    //bottomSheet_dateFilter_2819//appMobile_commonUI_filters_dateFilter_past_bottomSheet_actionTapOptionLast12Months
    await driver.execute("flutter:waitForTappable", byValueKey("pastTodoList"));
    await driver.execute("flutter:scroll", byValueKey("pastTodoList"), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 });
    await driver.execute('flutter:waitForTappable', byValueKey('dateFilter'))
    await driver.elementClick(byValueKey('dateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Custom'))
    await driver.elementClick(byText('Custom'))
    //opens - dateRangeFilter_stateNullValue_2636//appMobile_commonUI_filters_dateFilter_past_bottomSheet_actionCustomTap
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.execute('smartui.takeScreenshot=overlay_dateRangePicker_1184')//appMobile_commonUI_filters_dateFilter_past_bottomSheet_dateRangeFilter_stateNullValue
    await driver.elementClick(byValueKey('startDate')) // TodayNext SaturdayNext SundayAfter 1 week
    await driver.execute('flutter:waitForTappable', byText('Beginning'))
    //await driver.execute('smartui.takeScreenshot=past_daterange_startdatepicker')
    await driver.elementClick(byText('Beginning'))
    //S-3192_overlay_datePicker
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.elementClick(byText('SAVE')) // Today is selected
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.execute('smartui.takeScreenshot=overlay_dateRangePicker_3628')//appMobile_commonUI_filters_dateFilter_past_bottomSheet_dateRangeFilter_stateBeginningAndNullSelected
    await driver.elementClick(byValueKey('endDate')) // Never ends15 days later30 days later60 days later
    await driver.execute('flutter:waitForTappable', byText('Yesterday'))
    await driver.elementClick(byText('Yesterday'))
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.elementClick(byText('SAVE'))
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.execute('smartui.takeScreenshot=overlay_dateRangePicker_5290')//appMobile_commonUI_filters_dateFilter_past_bottomSheet_dateRangeFilter_stateBeginningAndYesterdaySelected
    await driver.elementClick(byText('APPLY'))
    await driver.execute("flutter:waitForTappable", byValueKey("dateFilter"));
    await driver.execute('smartui.takeScreenshot=bottomSheet_dateFilter_4512')//appMobile_commonUI_filters_dateFilter_past_bottomSheet_stateCustomApplied
    await driver.elementClick(byValueKey("dateFilter"));
    await driver.execute("flutter:waitForTappable", byText("Until Yesterday"));
    await driver.elementClick(byText("Until Yesterday"));
    //bottomSheet_dateFilter_1323
  }

  // Past Todo Status Filter
  public async pastTodoStatusFilter() {
    await driver.execute("flutter:waitForTappable", byText("Past"));
    await driver.elementClick(byText("Past"));
    await driver.execute("flutter:scroll", byValueKey("pastTodoFilterList"), { dx: -650, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("statusFilter"));
    //tab_pastTodo_2167//appMobile_screens_past_todo_statusFilter_button_stateDefault
    await driver.elementClick(byValueKey("statusFilter"));
    //tab_pastTodo_467
    await driver.execute("flutter:waitForTappable", byText("Overdue"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_filterTodoStatus_647");//appMobile_screens_past_todo_statusFilter_bottomSheet_stateDefault
    await driver.elementClick(byText("Overdue"));
    await driver.execute("flutter:waitForTappable", byValueKey("statusFilter"));
    await driver.elementClick(byValueKey("statusFilter"));
    //tab_pastTodo_467//appMobile_screens_past_todo_statusFilter_button_actionTap
    await driver.execute("flutter:waitForTappable", byText("Completed"));
    await driver.execute("smartui.takeScreenshot=tab_pastTodo_719")//appMobile_screens_past_todo_statusFilter_button_stateUpdated
    await driver.elementClick(byText("Completed"));
    await driver.execute("flutter:waitForTappable", byValueKey("statusFilter"));
    await driver.elementClick(byValueKey("statusFilter"));
    //tab_pastTodo_467//appMobile_screens_past_todo_statusFilter_button_actionTap
    await driver.execute("flutter:waitForTappable", byText("All"));
    await driver.elementClick(byText("All"));
    await driver.execute("flutter:waitForTappable", byValueKey("pastTodoList"));

    await driver.execute("flutter:scroll", byValueKey("pastTodoList"), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 });
  }

  // Past Todo Hashtag Filter
  public async pastTodoHashtagFilter() {
    await driver.execute("flutter:waitForTappable", byText("Past"));
    await driver.elementClick(byText("Past"));
    await driver.execute("flutter:waitForTappable", byValueKey("hashTagFilter"));
    //tab_pastCommon_2048//appMobile_commonUI_filters_hashtagFilter_button_stateDefault
    await driver.elementClick(byValueKey("hashTagFilter"));
    //tab_pastCommon_899//appMobile_commonUI_filters_hashtagFilter_button_actionTap
    await driver.execute("flutter:waitForTappable", byText("#morning"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_hashtagFilter_5779");//appMobile_commonUI_filters_hashtagFilter_bottomSheet_stateFilled
    await driver.elementClick(byText("#morning"));
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    //stateHashtagSelected_1212//appMobile_commonUI_filters_hashtagFilter_bottomSheet_actionHashtagTap
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byTooltip("Delete"));
    await driver.execute("smartui.takeScreenshot=tab_pastCommon_1050")//appMobile_commonUI_filters_hashtagFilter_button_stateMultipleHashtagsUpdated
    await driver.elementClick(byTooltip("Delete"));
    //tab_pastCommon_2601//appMobile_commonUI_filters_hashtagFilter_button_actionCloseIconTap
  }

  public async pastTodoDateStatusHashtagFilter() {
    await driver.execute("flutter:waitForTappable", byText("Todo"));
    await driver.elementClick(byText("Todo"));
    await driver.execute("flutter:scrollIntoView", byValueKey("pastTodoList"), { alignment: 1 });
    await driver.execute("flutter:waitForTappable", byValueKey("searchFilter"));
    await driver.elementClick(byValueKey("searchFilter"));
    //tab_pastTodo_3619//appMobile_screens_past_todo_todoTab_actionSearchFilterTap
    await driver.elementSendKeys(byValueKey("searchFilter"), "TODO");
    await driver.execute("flutter:waitForTappable", byValueKey("dateFilter"));
    await driver.elementClick(byValueKey("dateFilter"));
    await driver.execute("flutter:waitForTappable", byText("Last 7 Days"));
    await driver.elementClick(byText("Last 7 Days"));
    await driver.execute("flutter:scroll", byValueKey("pastTodoFilterList"), { dx: -650, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("statusFilter"));
    await driver.execute("smartui.takeScreenshot=tab_pastTodo_2167")//appMobile_screens_past_todo_statusFilter_button_stateDefault
    await driver.elementClick(byValueKey("statusFilter"));
    //tab_pastTodo_3865//appMobile_screens_past_todo_todoTab_actionStatusFilterTap
    await driver.execute("flutter:waitForTappable", byText("Completed"));
    await driver.elementClick(byText("Completed"));
    await driver.execute("flutter:scroll", byValueKey("pastTodoFilterList"), { dx: -650, dy: 0, durationMilliseconds: 200, frequency: 10 });
    //tab_pastTodo_873//appMobile_screens_past_todo_todoTab_actionSwipeLeftOrRight
    await driver.execute("flutter:waitForTappable", byValueKey("hashTagFilter"));
    await driver.elementClick(byValueKey("hashTagFilter"));
    //tab_pastTodo_2150//appMobile_screens_past_todo_todoTab_actionHashtagFilterTap
    await driver.execute("flutter:waitForTappable", byText("#morning"));
    await driver.elementClick(byText("#morning"));
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:scroll", byValueKey("pastTodoFilterList"), { dx: -1200, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Clear all"));
    //tab_pastCommon_959//appMobile_screens_past_todo_clearAll_button_stateButtonAppear
    await driver.elementClick(byText("Clear all"));
  }

  // Past habit list filter
  public async pastHabitListFilter() {
    await driver.execute("flutter:waitForTappable", byText("Past"));
    await driver.elementClick(byText("Past"));
    await driver.execute("flutter:waitForTappable", byText("Habit"));
    await driver.elementClick(byText("Habit"));
    const fabutton = byValueKey("increment");
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.execute("smartui.takeScreenshot=tab_pastHabit_5914")//appMobile_screens_past_habit_habitTab_emptyTab_stateFeatureNotUsed
    await driver.elementClick(fabutton);
    const setupHabit = byText("Setup Habit");
    await driver.execute("flutter:waitForTappable", setupHabit);
    await driver.elementClick(setupHabit);
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Are you happy ? " + currentDate;
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
    const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
    const result_sub2 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 370);
    const result_sub3 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 410);
    const d_sub = result_sub.getFullYear();
    const m_sub = result_sub.getMonth();
    const m_sub1 = result_sub1.getMonth();
    const m_sub2 = result_sub2.getMonth();
    const m_sub3 = result_sub3.getMonth();
    const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
    const pastDate1 = result_sub1.getDate() + "/" + (result_sub1.getMonth() + 1) + "/" + result_sub1.getFullYear();
    const pastDate2 = result_sub2.getDate() + "/" + (result_sub2.getMonth() + 1) + "/" + result_sub2.getFullYear();
    const pastDate3 = result_sub3.getDate() + "/" + (result_sub3.getMonth() + 1) + "/" + result_sub3.getFullYear();
    // const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const currentDateString = d.getDate() + "/" + monthNames[d.getMonth()] + "/" + d.getFullYear();
    const pastDateString = result_sub.getDate() + "/" + monthNames[m_sub] + "/" + result_sub.getFullYear();
    const pastDateString1 = result_sub1.getDate() + "/" + monthNames[m_sub1] + "/" + result_sub1.getFullYear();
    const pastDateString2 = result_sub2.getDate() + "/" + monthNames[m_sub2] + "/" + result_sub2.getFullYear();
    const pastDateString3 = result_sub3.getDate() + "/" + monthNames[m_sub3] + "/" + result_sub3.getFullYear();
    const result_add = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 17);
    const valuePast5days = "Are you happy ? " + pastDate;
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.elementClick(byValueKey("addHabitTitle"));
    await addtitle(valuePast5days);
    await addDatePastHabit(pastDateString, currentDateString);
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
    await driver.elementClick(byValueKey("addHashTag"));
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("#morning"));
    await driver.elementClick(byText("#morning"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    // 30 days
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    await driver.execute("flutter:waitForTappable", setupHabit);
    await driver.elementClick(setupHabit);
    const valuePast30days = "Are you happy ? " + pastDate1;
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.elementClick(byValueKey("addHabitTitle"));
    await addtitle(valuePast30days);
    await addDatePastHabit(pastDateString1, currentDateString);
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));

    // 370 days
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    await driver.execute("flutter:waitForTappable", setupHabit);
    await driver.elementClick(setupHabit);
    const valuePast370days = "Are you happy ? " + pastDate2;
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.elementClick(byValueKey("addHabitTitle"));
    await addtitle(valuePast370days);
    await addDatePastHabit(pastDateString2, currentDateString);
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));

    // 410 days
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    await driver.execute("flutter:waitForTappable", setupHabit);
    await driver.elementClick(setupHabit);
    const valuePast410days = "Are you happy ? " + pastDate3;
    await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
    await driver.elementClick(byValueKey("addHabitTitle"));
    await addtitle(valuePast410days);
    await addDatePastHabit(pastDateString3, currentDateString);
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byValueKey("habitFilter"));
    await driver.execute("smartui.takeScreenshot=tab_pastHabit_5515")//appMobile_screens_past_habit_habitTab_stateDefault
    //same tab_pastHabit_6130//appMobile_screens_past_habit_habitTab_stateFilled
    await driver.elementClick(byValueKey("habitFilter"));
    await driver.execute("flutter:waitForTappable", byText(valuePast370days));
    await driver.elementClick(byText(valuePast370days));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byTooltip("Delete"));
    await driver.elementClick(byTooltip("Delete"));
  }

  public async pastHabitDateStatusHashtagFilter() {
    await driver.execute("flutter:waitForTappable", byText("Habit"));
    await driver.elementClick(byText("Habit"));
    const d = new Date();
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
    const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
    const valuePast5days = "Are you happy ? " + pastDate;
    await driver.execute("flutter:waitForTappable", byValueKey("habitFilter"));
    await driver.elementClick(byValueKey("habitFilter"));
    await driver.execute("flutter:waitForTappable", byText(valuePast5days));
    await driver.elementClick(byText(valuePast5days));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:scrollIntoView", byValueKey("pastHabitList"), { alignment: 1 });
    await driver.execute("flutter:scroll", byValueKey("pastHabitFilterList"), { dx: -500, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("dateFilter"));
    //await driver.execute("smartui.takeScreenshot=Past_screen"); // Screenshot_pastscreen
    await driver.elementClick(byValueKey("dateFilter"));
    await driver.execute("flutter:waitForTappable", byText("Last 7 Days"));
    //await driver.execute("smartui.takeScreenshot=datefilter_bottomsheet"); // Screenshot_bottomsheet
    await driver.elementClick(byText("Last 7 Days"));
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("pastHabitFilterList"), { item: byValueKey("statusFilter"), dxScroll: -150, dyScroll: 0 });
    await driver.execute("flutter:scroll", byValueKey("pastHabitFilterList"), { dx: -1200, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("statusFilter"));
    await driver.elementClick(byValueKey("statusFilter"));
    await driver.execute("flutter:waitForTappable", byText("Completed"));
    await driver.elementClick(byText("Completed"));
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("pastHabitFilterList"), { item: byValueKey("hashTagFilter"), dxScroll: -150, dyScroll: 0 });
    await driver.execute("flutter:scroll", byValueKey("pastHabitFilterList"), { dx: -1000, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("hashTagFilter"));
    await driver.elementClick(byValueKey("hashTagFilter"));
    // await driver.execute('flutter:waitForTappable', byValueKey('addSearchFilter'))
    await driver.execute("flutter:waitForTappable", byText("#morning"));
    //await driver.execute("smartui.takeScreenshot=hashtag_bottomsheet");
    await driver.elementClick(byText("#morning"));
    // await driver.execute('flutter:scrollUntilVisible', byValueKey('hashTagFilterList'), { item: byText('seashore'), dxScroll: 0, dyScroll: -50 }) // vertical scroll done
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:scroll", byValueKey("pastHabitFilterList"), { dx: 1500, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("searchFilter"));
    await driver.elementClick(byValueKey("searchFilter"));
    await driver.elementSendKeys(byValueKey("searchFilter"), "HABIT");
    //await driver.execute("flutter:scroll", byValueKey("pastHabitFilterList"), { dx: -1200, dy: 0, durationMilliseconds: 200, frequency: 10 });
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("pastHabitFilterList"), { item: byText("Clear all"), dxScroll: -150, dyScroll: 0 });
    await driver.execute("flutter:scroll", byValueKey("pastHabitFilterList"), { dx: -1500, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText("Clear all"));
    await driver.elementClick(byText("Clear all"));
  }
  // Past journal list filter
  public async pastJournalListFilter() {
    await driver.execute("flutter:waitForTappable", byText("Past"));
    await driver.elementClick(byText("Past"));
    await driver.execute("flutter:waitForTappable", byText("Journal"));
    await driver.elementClick(byText("Journal"));
    const fabutton = byValueKey("increment");
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.execute("smartui.takeScreenshot=tab_pastJournal_33")
    await driver.elementClick(fabutton);
    const setupJournal = byText("Setup Journal");
    await driver.execute("flutter:waitForTappable", setupJournal);
    await driver.elementClick(setupJournal);
    const d = new Date();
    const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
    const valuee = "Describe today " + currentDate;
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
    const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30);
    const result_sub2 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 370);
    const result_sub3 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 410);
    const d_sub = result_sub.getFullYear();
    const m_sub = result_sub.getMonth();
    const m_sub1 = result_sub1.getMonth();
    const m_sub2 = result_sub2.getMonth();
    const m_sub3 = result_sub3.getMonth();
    const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
    const pastDate1 = result_sub1.getDate() + "/" + (result_sub1.getMonth() + 1) + "/" + result_sub1.getFullYear();
    const pastDate2 = result_sub2.getDate() + "/" + (result_sub2.getMonth() + 1) + "/" + result_sub2.getFullYear();
    const pastDate3 = result_sub3.getDate() + "/" + (result_sub3.getMonth() + 1) + "/" + result_sub3.getFullYear();
    // const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const currentDateString = d.getDate() + "/" + monthNames[d.getMonth()] + "/" + d.getFullYear();
    const pastDateString = result_sub.getDate() + "/" + monthNames[m_sub] + "/" + result_sub.getFullYear();
    const pastDateString1 = result_sub1.getDate() + "/" + monthNames[m_sub1] + "/" + result_sub1.getFullYear();
    const pastDateString2 = result_sub2.getDate() + "/" + monthNames[m_sub2] + "/" + result_sub2.getFullYear();
    const pastDateString3 = result_sub3.getDate() + "/" + monthNames[m_sub3] + "/" + result_sub3.getFullYear();
    const result_add = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 17);
    const valuePast5days = "Describe today " + pastDate;
    await driver.execute("flutter:waitForTappable", byValueKey("addJournalTitle"));
    await driver.elementClick(byValueKey("addJournalTitle"));
    await addtitle(valuePast5days);
    await addDatePastJournal(pastDateString, currentDateString);
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byValueKey("addHashTag"));
    await driver.elementClick(byValueKey("addHashTag"));
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("#morning"));
    await driver.elementClick(byText("#morning"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    // 30 days
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    await driver.execute("flutter:waitForTappable", setupJournal);
    await driver.elementClick(setupJournal);
    const valuePast30days = "Describe today " + pastDate1;
    await driver.execute("flutter:waitForTappable", byValueKey("addJournalTitle"));
    await driver.elementClick(byValueKey("addJournalTitle"));
    await addtitle(valuePast30days);
    await addDatePastJournal(pastDateString1, currentDateString);
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));

    // 370 days
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    await driver.execute("flutter:waitForTappable", setupJournal);
    await driver.elementClick(setupJournal);
    const valuePast370days = "Describe today " + pastDate2;
    await driver.execute("flutter:waitForTappable", byValueKey("addJournalTitle"));
    await driver.elementClick(byValueKey("addJournalTitle"));
    await addtitle(valuePast370days);
    await addDatePastJournal(pastDateString2, currentDateString);
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));

    // 410 days
    await driver.execute("flutter:waitForTappable", fabutton);
    await driver.elementClick(fabutton);
    await driver.execute("flutter:waitForTappable", setupJournal);
    await driver.elementClick(setupJournal);
    const valuePast410days = "Describe today " + pastDate3;
    await driver.execute("flutter:waitForTappable", byValueKey("addJournalTitle"));
    await driver.elementClick(byValueKey("addJournalTitle"));
    await addtitle(valuePast410days);
    await addDatePastJournal(pastDateString3, currentDateString);
    await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
    await driver.elementClick(byValueKey("endDate")); // Never ends;15 days later;30 days later;60 days later
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
    await driver.execute("flutter:waitForTappable", byValueKey("journalFilter"));
    await driver.execute("smartui.takeScreenshot=tab_pastJournal_6015")//appMobile_screens_past_journal_journalTab_stateDefault same tab_pastJournal_3339 //appMobile_screens_past_journal_journalTab_stateFilled
    await driver.elementClick(byValueKey("journalFilter"));
    await driver.execute("flutter:waitForTappable", byText(valuePast30days));
    await driver.elementClick(byText(valuePast30days));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:waitForTappable", byTooltip("Delete"));
    await driver.elementClick(byTooltip("Delete"));
  }
  public async pastJournalDateStatusHashtagFilter() {
    await driver.execute("flutter:waitForTappable", byText("Journal"));
    await driver.elementClick(byText("Journal"));
    const d = new Date();
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5);
    const pastDate = result_sub.getDate() + "/" + (result_sub.getMonth() + 1) + "/" + result_sub.getFullYear();
    const valuePast5days = "Describe today " + pastDate;
    await driver.execute("flutter:waitForTappable", byValueKey("journalFilter"));
    await driver.elementClick(byValueKey("journalFilter"));
    await driver.execute("flutter:waitForTappable", byText(valuePast5days));
    await driver.elementClick(byText(valuePast5days));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:scrollIntoView", byValueKey("pastJournalList"), { alignment: 1 });
    await driver.execute("flutter:scroll", byValueKey("pastJournalFilterList"), { dx: -600, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("dateFilter"));
    //await driver.execute("smartui.takeScreenshot=Past_screen"); // Screenshot_pastscreen
    await driver.elementClick(byValueKey("dateFilter"));
    await driver.execute("flutter:waitForTappable", byText("Last 7 Days"));
    //await driver.execute("smartui.takeScreenshot=datefilter_bottomsheet"); // Screenshot_bottomsheet
    await driver.elementClick(byText("Last 7 Days"));
    // await driver.execute("flutter:scrollUntilVisible", byValueKey("pastJournalFilterList"), { item: byValueKey("statusFilter"), dxScroll: -150, dyScroll: 0 });
    await driver.execute("flutter:scroll", byValueKey("pastJournalFilterList"), { dx: -1200, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("moodFilter"));
    await driver.elementClick(byValueKey("moodFilter"));
    await driver.execute("flutter:waitForTappable", byText("Very Happy"));
    await driver.elementClick(byText("Very Happy"));
    await driver.execute("flutter:waitForTappable", byText("Happy"));
    await driver.elementClick(byText("Happy"));
    await driver.execute("flutter:waitForTappable", byText("Very Sad"));
    await driver.elementClick(byText("Very Sad"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:scroll", byValueKey("pastJournalFilterList"), { dx: -1200, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("statusFilter"));
    await driver.elementClick(byValueKey("statusFilter"));
    await driver.execute("flutter:waitForTappable", byText("Missed"));
    await driver.elementClick(byText("Missed"));
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("pastJournalFilterList"), { item: byValueKey("hashTagFilter"), dxScroll: -150, dyScroll: 0 });
    await driver.execute("flutter:scroll", byValueKey("pastJournalFilterList"), { dx: -1000, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("hashTagFilter"));
    await driver.elementClick(byValueKey("hashTagFilter"));
    // await driver.execute('flutter:waitForTappable', byValueKey('addSearchFilter'))
    await driver.execute("flutter:waitForTappable", byText("#morning"));
    await driver.execute("smartui.takeScreenshot=hashtag_bottomsheet");
    await driver.elementClick(byText("#morning"));
    // await driver.execute('flutter:scrollUntilVisible', byValueKey('hashTagFilterList'), { item: byText('seashore'), dxScroll: 0, dyScroll: -50 }) // vertical scroll done
    await driver.execute("flutter:waitForTappable", byText("#smile"));
    await driver.elementClick(byText("#smile"));
    await driver.execute("flutter:waitForTappable", byText("APPLY"));
    await driver.elementClick(byText("APPLY"));
    await driver.execute("flutter:scroll", byValueKey("pastJournalFilterList"), { dx: 1500, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byValueKey("searchFilter"));
    await driver.elementClick(byValueKey("searchFilter"));
    await driver.elementSendKeys(byValueKey("searchFilter"), "JOURNAL");
    await driver.execute("flutter:scroll", byValueKey("pastJournalFilterList"), { dx: -1500, dy: 0, durationMilliseconds: 200, frequency: 10 });
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("pastJournalFilterList"), { item: byText("Clear all"), dxScroll: -150, dyScroll: 0 });
    await driver.execute("flutter:waitForTappable", byText("Clear all"));
    await driver.elementClick(byText("Clear all"));
  }
}
export default new Past();
