import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
import { addtitle } from "./page";
import addDays from 'date-fns/addDays'
import { subDays } from 'date-fns'

class UnscheduledTodos {

    public async addUnscheduledTodo() {
        await driver.execute('flutter:waitForTappable', by<PERSON><PERSON><PERSON><PERSON><PERSON>('unscheduledList'))
        await driver.elementClick(byValue<PERSON><PERSON>('unscheduledList'))
        const fabutton = byValueKey('increment');
        await driver.execute('flutter:waitForTappable', fabutton);
        await driver.elementClick(fabutton);
        const addtodo = byText('Add Todo');
        await driver.execute('flutter:waitForTappable', addtodo);
        await driver.elementClick(addtodo);
        const valuee = 'New UnscheduledTodo_1 '
        await driver.execute('smartui.takeScreenshot=unscheduledtodo_page') // Screenshot_todo page
        await addtitle(valuee)
        await driver.execute('flutter:waitForTappable', by<PERSON><PERSON><PERSON><PERSON><PERSON>('addDate'))
        await driver.elementClick(byValueKey('addDate'))
        await driver.execute('flutter:waitForTappable', byText('No date'))
        await driver.elementClick(byText('No date'))
        await driver.execute('flutter:waitForTappable', byText('SAVE'))
        await driver.elementClick(byText('SAVE'))
        await driver.execute('flutter:waitForTappable', byText('CLOSE'))
        await driver.elementClick(byText('CLOSE'))
        await driver.execute('flutter:waitForTappable', byText('Past'))
        await driver.elementClick(byText('Past'))
        await driver.execute('flutter:waitForTappable', byText('Today'))
        await driver.elementClick(byText('Today'))
        await driver.execute('flutter:waitForTappable', byValueKey('unscheduledList'))
        await driver.elementClick(byValueKey('unscheduledList'))
    }

    public async addUnscheduledTodoSucess() {
        const valuee = 'New UnscheduledTodo_1'
        await driver.execute('flutter:waitForTappable', byText(valuee))
        const elem = await driver.getElementText(byText(valuee))
        assert.strictEqual(elem, valuee)
        console.log('success')
    }

    public async deleteUnscheduledTodo() {
        const value1 = 'New UnscheduledTodo_1';
        await driver.switchContext('NATIVE_APP')
        let el8 = await driver.$(`~${value1}`);
        await el8.click();
        await driver.switchContext('FLUTTER')
        await driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
        await driver.elementClick(byValueKey('trashIcon'))
        await driver.execute('flutter:waitForTappable', byValueKey('quaternaryButton'))
        await driver.elementClick(byValueKey('quaternaryButton'))
    }

    public async doneUnscheduledTodo() {
        const valuee = 'New UnscheduledTodo_1 '
        const elem = byText(valuee) // here if multiple entries with same name --conflict
        await driver.execute('flutter:waitForTappable', byValueKey('circleCheckBox'))
        await driver.elementClick(byValueKey('circleCheckBox'))
        console.log('Success')
    }

    public async filterUnscheduledTodo() {
        await driver.execute('flutter:waitForTappable', byText('Completed'))
        await driver.elementClick(byText('Completed'))
        // await driver.execute('flutter:waitForTappable', byValueKey('circleCheckBox'))
        // await driver.elementClick(byValueKey('circleCheckBox'))
        await driver.execute('flutter:waitForTappable', byText('Incomplete'))
        await driver.elementClick(byText('Incomplete'))
        await driver.execute('flutter:waitForTappable', byText('All'))
        await driver.elementClick(byText('All'))
    }

}
export default new UnscheduledTodos();



