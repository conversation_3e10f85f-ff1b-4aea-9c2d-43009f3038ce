import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
import { previousDay } from "date-fns";
class Notification {

    public async notification() {
        const hamburger = byValue<PERSON><PERSON>('hamburgerMenu')
        await driver.elementClick(hamburger);
        const notification = byText('Notifications')
        await driver.elementClick(notification);
        await driver.execute('smartui.takeScreenshot=overlay_hamburgerTray_3129')
        await driver.execute('smartui.takeScreenshot=screen_notifications_1413')
    }

    public async notificationSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Notifications'))
        const element = await driver.getElementText(byText('Notifications'))
        console.log('element value is:' + element)
        const valuee = 'Notifications'
        assert.strictEqual(element, valuee)
    }

    public async muteAllDevice() {
        const muteAll = byText('Mute all Devices')
        await driver.elementClick(muteAll);
        await driver.execute('smartui.takeScreenshot=screen_notifications_3087')
    }

    public async muteAllDeviceSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('DISCARD'))
        const element = await driver.getElementText(byText('DISCARD'))
        console.log('element value is:' + element)
        const valuee = 'DISCARD'
        assert.strictEqual(element, valuee)
    }

    public async mute() {
        const mute = byText('3 Hours')
        await driver.elementClick(mute);

        const close = byValueKey('close')
        await driver.elementClick(close);

        const discard = byText('DISCARD')
        await driver.elementClick(discard);
    }


    public async muteSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Notifications'))
        const element = await driver.getElementText(byText('Notifications'))
        console.log('element value is:' + element)
        const valuee = 'Notifications'
        assert.strictEqual(element, valuee)
    }

    public async muteThisDevice() {
        const mutethisDevice = byText('Mute this Device')
        await driver.elementClick(mutethisDevice);
    }

    public async muteThisDeviceSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Notifications'))
        const element = await driver.getElementText(byText('Notifications'))
        console.log('element value is:' + element)
        const valuee = 'Notifications'
        assert.strictEqual(element, valuee)
    }

    public async mute1() {
        const mute = byText('1 Week')
        await driver.elementClick(mute);

        const close = byValueKey('close')
        await driver.elementClick(close);

        const discard = byText('DISCARD')
        await driver.elementClick(discard);
    }

    public async mute1Success(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Notifications'))
        const element = await driver.getElementText(byText('Notifications'))
        console.log('element value is:' + element)
        const valuee = 'Notifications'
        assert.strictEqual(element, valuee)
    }

    public async dailyAgenda() {
        const dailyAgenda = byText('Daily Agenda')
        await driver.elementClick(dailyAgenda);
    }

    public async dailyAgendaSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('DISCARD'))
        const element = await driver.getElementText(byText('DISCARD'))
        console.log('element value is:' + element)
        const valuee = 'DISCARD'
        assert.strictEqual(element, valuee)
    }

    public async time() {
        const time = byText('Time')
        await driver.elementClick(time);

        const set = byText('SET')
        await driver.elementClick(set);
    }

    public async timeSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Notifications'))
        const element = await driver.getElementText(byText('Notifications'))
        console.log('element value is:' + element)
        const valuee = 'Notifications'
        assert.strictEqual(element, valuee)
    }

    public async sound() {
        const sound = byText('Sound')
        await driver.elementClick(sound);

        const setSound = byText('Mevolve 5')
        await driver.elementClick(setSound);

        const save = byText('SAVE')
        await driver.elementClick(save);
    }

    public async soundSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Notifications'))
        const element = await driver.getElementText(byText('Notifications'))
        console.log('element value is:' + element)
        const valuee = 'Notifications'
        assert.strictEqual(element, valuee)
    }

    public async snoozeDuration() {
        const snoozeDuration = byText('Snooze Duration')
        await driver.elementClick(snoozeDuration);

        const minutes = byText('10m')
        await driver.elementClick(minutes);
    }

    public async snoozeDurationSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Notifications'))
        const element = await driver.getElementText(byText('Notifications'))
        console.log('element value is:' + element)
        const valuee = 'Notifications'
        assert.strictEqual(element, valuee)
    }

    public async pinnedReminder() {
        const pinnedReminder = byText('Pinned Reminder')
        await driver.elementClick(pinnedReminder);
        const pinnedReminder1 = byText('Pinned Reminder')
        await driver.elementClick(pinnedReminder1);

        const close = byValueKey('closeNotificationSettings')
        await driver.elementClick(close);
    }

    public async pinnedReminderSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Lists'))
        const element = await driver.getElementText(byText('Lists'))
        console.log('element value is:' + element)
        const valuee = 'Lists'
        assert.strictEqual(element, valuee)
    }

}
export default new Notification();