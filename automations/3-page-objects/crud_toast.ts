import { addStartDate, addtime, addtitle, insightDonePast } from "./page";
import { subDays } from "date-fns";
import { byText, byV<PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";

class ToastActions {
    public async todoToastActions() {
        await driver.execute('flutter:waitForTappable', by<PERSON><PERSON><PERSON><PERSON><PERSON>('hamburgerMenu'))
        await driver.elementClick(byV<PERSON>ue<PERSON>ey('hamburgerMenu'))
        await driver.execute("flutter:waitForTappable", byText("Insight"));
        await driver.elementClick(byText("Insight"));
        await driver.execute("flutter:waitForTappable", byText("Habit"));
        await driver.elementClick(byText("Habit"));
        await driver.execute("flutter:waitForTappable", byText("Todo"));
        await driver.elementClick(byText("Todo"));
        await driver.execute("flutter:waitForTappable", by<PERSON><PERSON><PERSON><PERSON><PERSON>("primaryButton"));
        await driver.execute("smartui.takeScreenshot=tab_insightTodo_1753");//appMobile_screens_hamburger_insight_todo_todoTab_emptyTab_stateFeatureNotUsed
        await driver.elementClick(byValueKey("primaryButton"));
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "New Todo_1 " + currentDate;
        await addtitle(valuee);
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));
        await driver.execute("flutter:waitForTappable", byText("SHOW"));
        await driver.execute("smartui.takeScreenshot=toast_todoCreated_1223");
        await driver.elementClick(byText("SHOW"));
        //toast_todoCreated_601//appMobile_commonUI_todo_addAndEditBottomsheet_toastTodoCreated_actionTapButtonView
        const value = "Updated New Todo_1 " + currentDate;
        await driver.execute("flutter:waitForTappable", byValueKey("addTitle"));
        await driver.elementClick(byValueKey("addTitle"));
        await addtitle(value);
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));
        await driver.execute("flutter:waitForTappable", byText("DISMISS"));
        await driver.execute("smartui.takeScreenshot=toast_todoEdited_5960");
        await driver.elementClick(byText("DISMISS"));
        //toast_todoEdited_5522//appMobile_commonUI_todo_addAndEditBottomsheet_toastTodoEdited_actionTapButtonDismiss
        await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
        await driver.elementClick(byValueKey('closeNotificationSettings'))
        await driver.execute("flutter:waitForTappable", byText("Today"));
        await driver.elementClick(byText("Today"));
        await driver.execute("flutter:waitForTappable", byText(value));
        await driver.elementClick(byText(value));
        await addStartDate();
        await addtime();
        await driver.execute("flutter:waitForTappable", byText("CLOSE"));
        await driver.elementClick(byText("CLOSE"));
        await driver.execute("flutter:waitForTappable", byText("SHOW"));
        await driver.elementClick(byText("SHOW"));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("seconadaryButton"));
        await driver.elementClick(byValueKey("seconadaryButton"));
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText("UNDO"));
        await driver.execute("smartui.takeScreenshot=toast_todoDeleted_5999");
        await driver.elementClick(byText("UNDO"));
        //toast_todoDeleted_5885//appMobile_commonUI_todo_addAndEditBottomsheet_toastTodoDeleted_actionTapButtonUndo
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText(value));
        await driver.elementClick(byText(value)); //need to correct
        await driver.execute("flutter:waitForTappable", byValueKey("trashIcon"));
        await driver.elementClick(byValueKey("trashIcon"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));
        await driver.execute("flutter:waitForTappable", byText("DISMISS"));
        await driver.elementClick(byText("DISMISS"));
        //toast_todoDeleted_1357//appMobile_commonUI_todo_addAndEditBottomsheet_toastTodoDeleted_actionTapButtonDismiss
    }
    public async habitToastActions() {
        await driver.execute('flutter:waitForTappable', byValueKey('hamburgerMenu'))
        await driver.elementClick(byValueKey('hamburgerMenu'))
        await driver.execute("flutter:waitForTappable", byText("Insight"));
        await driver.elementClick(byText("Insight"));
        await driver.execute("flutter:waitForTappable", byText("Habit"));
        await driver.elementClick(byText("Habit"));
        await driver.execute("flutter:waitForTappable", byValueKey("primaryButton"));
        await driver.execute("smartui.takeScreenshot=tab_insightHabit_3823");
        await driver.elementClick(byValueKey("primaryButton"));
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "Are you happy ? " + currentDate;
        await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
        await driver.elementClick(byValueKey("addHabitTitle"));
        await addtitle(valuee);
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("SHOW"));
        await driver.execute("smartui.takeScreenshot=toast_habitSetupCreated_4168");
        await driver.elementClick(byText("SHOW"));
        //toast_habitSetupCreated_667//appMobile_commonUI_habit_setupAdd_common_toastMessageSetupCreated_actionTapButtonView
        const value = "Updated Are you happy ? " + currentDate;
        await driver.switchContext("NATIVE_APP");
        const el8 = await driver.$("/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]/android.view.View");
        await el8.click();
        await driver.switchContext("FLUTTER");
        await driver.execute("flutter:waitForTappable", byText("EDIT"));
        await driver.elementClick(byText("EDIT"));
        await driver.execute("flutter:waitForTappable", byValueKey("addHabitTitle"));
        await driver.elementClick(byValueKey("addHabitTitle"));
        await addtitle(value);
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("DISMISS"));
        await driver.execute("smartui.takeScreenshot=toast_habitSetupEdited_5359");
        await driver.elementClick(byText("DISMISS"));
        //toast_habitSetupEdited_1255//appMobile_commonUI_habit_setupEdit_toastMessageHabitSetupEdited_actionTapButtonDismiss
        await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
        await driver.elementClick(byValueKey('closeNotificationSettings'))
        await driver.execute("flutter:waitForTappable", byText(value));
        await driver.elementClick(byText(value));
        await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
        await driver.elementClick(byValueKey("showHabitBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("Edit habit setup"));
        await driver.elementClick(byText("Edit habit setup"));
        await driver.switchContext("NATIVE_APP");
        await el8.click();
        await driver.switchContext("FLUTTER");
        await driver.execute("flutter:waitForTappable", byText("EDIT"));
        await driver.elementClick(byText("EDIT"));
        await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
        await driver.elementClick(byValueKey("selectHabitDateRange"));
        await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
        await driver.elementClick(byValueKey("startDate"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
        await driver.elementClick(byValueKey("endDate"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("APPLY"));
        await driver.elementClick(byText("APPLY"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("SHOW"));
        await driver.elementClick(byText("SHOW"));
        //toast_habitSetupEdited_1863//appMobile_commonUI_habit_setupEdit_toastMessageHabitSetupEdited_actionTapButtonView
        await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
        await driver.elementClick(byValueKey("deleteButton"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));

        await driver.execute("flutter:waitForTappable", byText("UNDO"));
        await driver.execute("smartui.takeScreenshot=toast_habitSetupRestored_6019");//appMobile_commonUI_habit_setupEdit_toastMessageHabitSetupRestored_stateDefaultawait driver.execute("smartui.takeScreenshot=toast_habitSetupRestored_6019");//appMobile_commonUI_habit_setupEdit_toastMessageHabitSetupRestored_stateDefault
        await driver.elementClick(byText("UNDO"));
        //toast_habitSetupDeleted_3369//appMobile_commonUI_habit_setupEdit_toastMessageHabitSetupDeleted_actionTapButtonUndo
        await driver.execute("flutter:waitForTappable", byText(value));
        await driver.elementClick(byText(value));
        await driver.execute("flutter:waitForTappable", byValueKey("showHabitBottomSheet"));
        await driver.elementClick(byValueKey("showHabitBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("Edit habit setup"));
        await driver.elementClick(byText("Edit habit setup"));
        await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
        await driver.elementClick(byValueKey("deleteButton"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));

        await driver.execute("flutter:waitForTappable", byText("DISMISS"));
        await driver.execute("smartui.takeScreenshot=toast_habitSetupDeleted_1907");//appMobile_commonUI_habit_setupEdit_toastMessageHabitSetupDeleted_stateDefault
        await driver.elementClick(byText("DISMISS"));
        //toast_habitSetupDeleted_253//appMobile_commonUI_habit_setupEdit_toastMessageHabitSetupDeleted_actionTapButtonDismiss
    }
    public async journalToastActions() {
        console.log('INSIGHT JOURNAL')
        await driver.execute('flutter:waitForTappable', byValueKey('hamburgerMenu'))
        await driver.elementClick(byValueKey('hamburgerMenu'))
        await driver.execute("flutter:waitForTappable", byText("Insight"));
        await driver.elementClick(byText("Insight"));
        await driver.execute("flutter:waitForTappable", byText("Journal"));
        await driver.elementClick(byText("Journal"));
        await driver.execute("flutter:waitForTappable", byValueKey("primaryButton"));
        await driver.execute("smartui.takeScreenshot=tab_insightJournal_2081");//appMobile_screens_hamburger_insight_journal_journalTab_emptyTab_stateFeatureNotUsed
        await driver.elementClick(byValueKey("primaryButton"));
        const d = new Date();
        const currentDate = d.getDate() + "/" + (d.getMonth() + 1) + "/" + d.getFullYear();
        const valuee = "Describe today " + currentDate;
        await driver.execute("flutter:waitForTappable", byValueKey("addJournalTitle"));
        await driver.elementClick(byValueKey("addJournalTitle"));
        await addtitle(valuee);
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("SHOW"));
        await driver.execute("smartui.takeScreenshot=toast_journalSetupCreated_5335");
        //tab_insightJournal_5260
        await driver.elementClick(byText("SHOW"));
        const value = "updated" + valuee;
        await driver.switchContext("NATIVE_APP");
        const el8 = await driver.$("/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]/android.view.View");
        await el8.click();
        await driver.switchContext("FLUTTER");
        await driver.execute("flutter:waitForTappable", byText("EDIT"));
        await driver.elementClick(byText("EDIT"));
        await driver.execute("flutter:waitForTappable", byValueKey("addJournalTitle"));
        await driver.elementClick(byValueKey("addJournalTitle"));
        await addtitle(value);
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("DISMISS"));
        await driver.execute("smartui.takeScreenshot=toast_journalSetupEdited_2464");//appMobile_commonUI_journal_setupEdit_toastMessageSetupEdited_stateDefault
        await driver.elementClick(byText("DISMISS"));
        //toast_journalSetupEdited_2653
        await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
        await driver.elementClick(byValueKey('closeNotificationSettings'))
        await driver.execute("flutter:waitForTappable", byText(value));
        await driver.elementClick(byText(value));
        await driver.execute("flutter:waitForTappable", byValueKey("showJournalBottomSheet"));
        await driver.elementClick(byValueKey("showJournalBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("Edit journal setup"));
        await driver.elementClick(byText("Edit journal setup"));
        await driver.switchContext("NATIVE_APP");
        await el8.click();
        await driver.switchContext("FLUTTER");
        await driver.execute("flutter:waitForTappable", byText("EDIT"));
        await driver.elementClick(byText("EDIT"));
        await driver.execute("flutter:waitForTappable", byValueKey("selectJournalDateRange"));
        await driver.elementClick(byValueKey("selectJournalDateRange"));
        await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
        await driver.elementClick(byValueKey("startDate"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
        await driver.elementClick(byValueKey("endDate"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("APPLY"));
        await driver.elementClick(byText("APPLY"));
        await driver.execute("flutter:waitForTappable", byText("SAVE"));
        await driver.elementClick(byText("SAVE"));
        await driver.execute("flutter:waitForTappable", byText("SHOW"));
        await driver.elementClick(byText("SHOW"));
        await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
        await driver.elementClick(byValueKey("deleteButton"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));

        await driver.execute("flutter:waitForTappable", byText("UNDO"));
        await driver.execute("smartui.takeScreenshot=toast_journalSetupRestored_2435")//appMobile_commonUI_journal_setupEdit_toastMessageSetupRestored_stateDefault
        await driver.elementClick(byText("UNDO"));
        //toast_journalSetupDeleted_1990//appMobile_commonUI_journal_setupEdit_toastMessageSetupDeleted_actionTapButtonUndo
        await driver.execute("flutter:waitForTappable", byText(value));
        await driver.elementClick(byText(value));
        await driver.execute("flutter:waitForTappable", byValueKey("showJournalBottomSheet"));
        await driver.elementClick(byValueKey("showJournalBottomSheet"));
        await driver.execute("flutter:waitForTappable", byText("Edit journal setup"));
        await driver.elementClick(byText("Edit journal setup"));
        await driver.execute("flutter:waitForTappable", byValueKey("deleteButton"));
        await driver.elementClick(byValueKey("deleteButton"));
        await driver.execute("flutter:waitForTappable", byValueKey("quaternaryButton"));
        await driver.elementClick(byValueKey("quaternaryButton"));

        await driver.execute("flutter:waitForTappable", byText("DISMISS"));
        await driver.execute("smartui.takeScreenshot=toast_journalSetupDeleted_2543")//appMobile_commonUI_journal_setupEdit_toastMessageSetupDeleted_stateDefault
        await driver.elementClick(byText("DISMISS"));
        //toast_journalSetupDeleted_2099//appMobile_commonUI_journal_setupEdit_toastMessageSetupDeleted_actionTapButtonDismiss
    }
}
export default new ToastActions();
