import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'
class SupportChat {

    public async supportchat() {
        const hamburger = byValue<PERSON>ey('hamburgerMenu')
        await driver.elementClick(hamburger);
        await driver.execute("smartui.takeScreenshot=overlay_hamburgerTray_3129");
        const supportchat = byText('Support chat')
        await driver.elementClick(supportchat);
        await driver.execute('smartui.takeScreenshot=screen_supportChat_2108');

        let text = 'Support'
        const input = byValueKey('textFieldBottomSheet')
        await driver.elementSendKeys(input, text)
        await driver.execute('smartui.takeScreenshot=screen_supportChat_2592')
        await driver.execute('smartui.takeScreenshot=screen_supportChat_4527')
        await driver.execute('smartui.takeScreenshot=screen_screen_supportChat_2253')
        await driver.execute('smartui.takeScreenshot=screen_supportChat_1102')

        const add = byVal<PERSON><PERSON><PERSON>('addItem')
        await driver.elementClick(add);
        await driver.execute('smartui.takeScreenshot=screen_supportChat_1278')
    }

    public async supportChatSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Mevolve Support'))
        const element = await driver.getElementText(byText('Mevolve Support'))
        console.log('element value is:' + element)
        const valuee = 'Mevolve Support'
        assert.strictEqual(element, valuee)
    }

    public async supportPageClose() {
        const close = byValueKey('mevolveSupportClose')
        await driver.elementClick(close);
    }

    public async supportPageCloseSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Past'))
        const element = await driver.getElementText(byText('Past'))
        console.log('element value is:' + element)
        const valuee = 'Past'
        assert.strictEqual(element, valuee)
    }

}
export default new SupportChat()
