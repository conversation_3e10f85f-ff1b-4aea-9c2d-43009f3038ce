import { byText, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";


export const addtime = async (): Promise<any> => {
  await driver.execute("flutter:waitForTappable", by<PERSON><PERSON><PERSON><PERSON><PERSON>("addTime"));
  //5946_bottomSheet_actionCommon
  //2587_bottomSheet_actionCommon
  await driver.elementClick(byValue<PERSON><PERSON>("addTime")); // toggleTime-for turnoff time
  await driver.execute("flutter:waitFor", by<PERSON><PERSON><PERSON><PERSON><PERSON>("timeHourSpinner"), 3000);
  //6148_overlay_timePicker
  //3642_overlay_timePicker
  //1740_overlay_timePicker
  //3364_overlay_timePicker
  //2663_overlay_timePicker
  //commonUI_widgets_timePicker_actionEnabledToggleBarTap - didnt added //6102_overlay_timePicker
  await driver.execute("smartui.takeScreenshot=overlay_timePicker_6148"); // Screenshot_timepicker
  ////await driver.saveScreenshot('2.png')
  await driver.execute("flutter:scroll", by<PERSON><PERSON><PERSON><PERSON><PERSON>("timeHourSpinner"), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 });
  await driver.execute("flutter:waitFor", byValueKey("timeMinSpinner"), 3000);
  //3216_overlay_timePicker
  await driver.execute("flutter:scroll", byValueKey("timeMinSpinner"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
  await driver.execute("flutter:waitFor", byValueKey("selectionAmPm"), 3000);
  //4315_overlay_timePicker
  await driver.execute("flutter:scroll", byValueKey("selectionAmPm"), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 });
  //1163_overlay_timePicker
  await driver.execute("flutter:waitForTappable", byText("CANCEL"));
  await driver.elementClick(byText("CANCEL"));
  //4760_overlay_timePicker
  await driver.execute("flutter:waitForTappable", byText("CANCEL")); // discard cancel
  await driver.elementClick(byText("CANCEL"));
  await driver.execute("flutter:waitForTappable", byText("SET"));
  await driver.elementClick(byText("SET"));
  //4370_overlay_timePicker
  console.log("add time abcd");
};

export const addStartDate = async (): Promise<any> => {
  await driver.execute("flutter:waitForTappable", byValueKey("addDate"));
  //4386_screen_common
  await driver.elementClick(byValueKey("addDate"));
  await driver.execute("flutter:waitForTappable", byText("CANCEL"));
  await driver.elementClick(byText("CANCEL"));
  //3463_overlay_datePicker
  await driver.execute("flutter:waitForTappable", byValueKey("addDate"));
  await driver.elementClick(byValueKey("addDate"));
  //4169_overlay_datePicker
  console.log("start date picker widget");
  await driver.execute("flutter:waitForTappable", byText("Tomorrow"));
  //S-5887_overlay_datePicker
  await driver.elementClick(byText("Tomorrow"));
  await driver.execute("flutter:waitForTappable", byText("After 1 Week"));
  await driver.elementClick(byText("After 1 Week"));
  await driver.execute("flutter:waitForTappable", byText("No Date"));
  //S-2362_overlay_datePicker
  await driver.elementClick(byText("No Date"));
  await driver.execute("flutter:waitForTappable", byText("Today"));
  await driver.execute("smartui.takeScreenshot=overlay_datePicker_4169"); // Screenshot_date picker
  ////await driver.saveScreenshot('3.png')
  await driver.elementClick(byText("Today"));
  await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
  //S-315_overlay_datePicker
  await driver.elementClick(byValueKey("monthAndYear"));
  console.log("monthandyear widget");
  //S-5175_overlay_yearMonthPicker
  //S-1015_overlay_yearMonthPicker[didntadded]
  // await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
  // //await driver.execute("smartui.takeScreenshot=month&year_picker"); // Screenshot_month&year picker
  ////await driver.saveScreenshot('3.png')
  // await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText("2028"), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
  // await driver.execute("flutter:waitForTappable", byText("2028"));
  // await driver.elementClick(byText("2028"));
  await driver.execute("flutter:waitForTappable", byText("Sep"));
  await driver.execute("smartui.takeScreenshot=overlay_yearMonthPicker_5175");
  ////await driver.saveScreenshot('4.png')
  await driver.elementClick(byText("Sep"));
  await driver.execute("flutter:waitForTappable", byText("SET"));
  await driver.elementClick(byText("SET"));
  //S-5657_overlay_yearMonthPicker
  await driver.execute("flutter:waitForTappable", byText("SAVE"));
  await driver.elementClick(byText("SAVE"));
  //S-2523_overlay_datePicker
  //S-1120_bottomSheet_actionCommon
  //S-603_overlay_dateRangePicker
};

export const addEndDate = async (): Promise<any> => {
  await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
  await driver.elementClick(byValueKey("selectHabitDateRange"));
  console.log("end date picker widget");
  await driver.execute("flutter:waitForTappable", byValueKey("endDate"));
  await driver.elementClick(byValueKey("endDate"));
  await driver.execute("flutter:waitForTappable", byText("15 Days Later"));
  await driver.elementClick(byText("15 Days Later"));
  await driver.execute("flutter:waitForTappable", byText("60 Days Later"));
  await driver.elementClick(byText("60 Days Later"));
  await driver.execute("flutter:waitForTappable", byText("30 Days Later"));
  await driver.execute("smartui.takeScreenshot=end_date_picker");
  ////await driver.saveScreenshot('5.png')
  await driver.elementClick(byText("30 Days Later"));
  await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
  await driver.elementClick(byValueKey("monthAndYear"));
  console.log("monthandyear widget");
  //await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
  ////await driver.execute("smartui.takeScreenshot=month&year_picker"); // Screenshot_month&year picker
  ////await driver.saveScreenshot('6.png')
  //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText("2028"), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
  //await driver.execute("flutter:waitForTappable", byText("2028"));
  //await driver.elementClick(byText("2028"));
  await driver.execute("flutter:waitForTappable", byText("Sep"));
  await driver.elementClick(byText("Sep"));
  await driver.execute("flutter:waitForTappable", byText("SET"));
  await driver.elementClick(byText("SET"));
  await driver.execute("flutter:waitForTappable", byText("SAVE"));
  await driver.elementClick(byText("SAVE"));
  await driver.execute("flutter:waitForTappable", byText("APPLY"));
  await driver.elementClick(byText("APPLY"));
};

export const addDatePast = async (pastDate: string, currentDate: string,): Promise<any> => {
  const datePast = pastDate;
  const newarr = datePast.split("/");
  const pastDay = newarr[0];
  console.log("day is " + pastDay);
  const pastMonth = newarr[1];
  console.log("month is " + pastMonth);
  const pastYear = newarr[2];
  console.log("year is " + pastYear);
  const dateCurrent = currentDate;
  const newarr1 = dateCurrent.split("/");
  const currentDay = newarr1[0];
  console.log("day is " + currentDay);
  const currentMonth = newarr1[1];
  console.log("month is " + currentMonth);
  const currentYear = newarr1[2];
  console.log("year is " + currentYear);
  await driver.execute("flutter:waitForTappable", byValueKey("addDate"));
  await driver.elementClick(byValueKey("addDate"));
  if (currentMonth == pastMonth && currentYear == pastYear) {
    console.log("date widget in  if 1");
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear == pastYear) {
    console.log("date widget in else if 2");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("smartui.takeScreenshot=month&year_picker"); // Screenshot_month&year picker
    ////await driver.saveScreenshot('8.png')
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 3");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    //await driver.execute("smartui.takeScreenshot=date_picker"); // Screenshot_date picker
    ////await driver.saveScreenshot('8.png')
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: 150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth == pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 4");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("smartui.takeScreenshot=month&year_picker"); // Screenshot_month&year picker
    ////await driver.saveScreenshot('9.png')
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: 150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    // await driver.execute('flutter:waitForTappable', byText(pastMonth))
    // await driver.elementClick(byText(pastMonth))
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }
};

export const addDatePastHabit = async (pastDate: string, currentDate: string,): Promise<any> => {
  const datePast = pastDate;
  const newarr = datePast.split("/");
  const pastDay = newarr[0];
  console.log("day is " + pastDay);
  const pastMonth = newarr[1];
  console.log("month is " + pastMonth);
  const pastYear = newarr[2];
  console.log("year is " + pastYear);
  const dateCurrent = currentDate;
  const newarr1 = dateCurrent.split("/");
  const currentDay = newarr1[0];
  console.log("day is " + currentDay);
  const currentMonth = newarr1[1];
  console.log("month is " + currentMonth);
  const currentYear = newarr1[2];
  console.log("year is " + currentYear);
  await driver.execute("flutter:waitForTappable", byValueKey("selectHabitDateRange"));
  await driver.elementClick(byValueKey("selectHabitDateRange"));
  await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
  await driver.elementClick(byValueKey("startDate"));
  if (currentMonth == pastMonth && currentYear == pastYear) {
    console.log("date widget in  if 1");
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear == pastYear) {
    console.log("date widget in else if 2");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 3");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth == pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 4");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }
};

export const addDatePastJournal = async (pastDate: string, currentDate: string,): Promise<any> => {
  const datePast = pastDate;
  const newarr = datePast.split("/");
  const pastDay = newarr[0];
  console.log("day is " + pastDay);
  const pastMonth = newarr[1];
  console.log("month is " + pastMonth);
  const pastYear = newarr[2];
  console.log("year is " + pastYear);
  const dateCurrent = currentDate;
  const newarr1 = dateCurrent.split("/");
  const currentDay = newarr1[0];
  console.log("day is " + currentDay);
  const currentMonth = newarr1[1];
  console.log("month is " + currentMonth);
  const currentYear = newarr1[2];
  console.log("year is " + currentYear);
  await driver.execute("flutter:waitForTappable", byValueKey("selectJournalDateRange"));
  await driver.elementClick(byValueKey("selectJournalDateRange"));
  await driver.execute("flutter:waitForTappable", byValueKey("startDate"));
  await driver.elementClick(byValueKey("startDate"));
  if (currentMonth == pastMonth && currentYear == pastYear) {
    console.log("date widget in  if 1");
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear == pastYear) {
    console.log("date widget in else if 2");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 3");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth == pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 4");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }
};

export const addDateFuture = async (futureDate: string, currentDate: string,): Promise<any> => {
  const datePast = futureDate;
  const newarr = datePast.split("/");
  const futureDay = newarr[0];
  console.log("day is " + futureDay);
  const futureMonth = newarr[1];
  console.log("month is " + futureMonth);
  const futureYear = newarr[2];
  console.log("year is " + futureYear);
  const dateCurrent = currentDate;
  const newarr1 = dateCurrent.split("/");
  const currentDay = newarr1[0];
  console.log("day is " + currentDay);
  const currentMonth = newarr1[1];
  console.log("month is " + currentMonth);
  const currentYear = newarr1[2];
  console.log("year is " + currentYear);
  await driver.execute("flutter:waitForTappable", byValueKey("addDate"));
  await driver.elementClick(byValueKey("addDate"));
  if (currentMonth == futureMonth && currentYear == futureYear) {
    console.log("date widget in  if 1");
    await driver.execute("flutter:waitForTappable", byText(futureDay));
    await driver.elementClick(byText(futureDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != futureMonth && currentYear == futureYear) {
    console.log("date widget in else if 2");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(futureYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(futureMonth));
    await driver.elementClick(byText(futureMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(futureDay));
    await driver.elementClick(byText(futureDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != futureMonth && currentYear != futureYear) {
    console.log("date widget in else if 3");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(futureYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(futureYear));
    await driver.elementClick(byText(futureYear));
    await driver.execute("flutter:waitForTappable", byText(futureMonth));
    await driver.elementClick(byText(futureMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(futureDay));
    await driver.elementClick(byText(futureDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth == futureMonth && currentYear != futureYear) {
    console.log("date widget in else if 4");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(futureYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:scroll", byValueKey("yearList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute("flutter:waitForTappable", byText(futureYear));
    await driver.elementClick(byText(futureYear));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(futureDay));
    await driver.elementClick(byText(futureDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }
};

export const addtitle = async (valuee: string): Promise<any> => {
  //await driver.execute("flutter:waitForTappable", byValueKey("textFieldBottomSheet"));
  await driver.elementSendKeys(byValueKey("textFieldBottomSheet"), valuee);
  await driver.execute("flutter:waitForTappable", byValueKey("sendButton"));
  await driver.elementClick(byValueKey("sendButton"));
};

export const insightDonePast = async (pastDate: string, currentDate: string, title1: string, title2: string): Promise<any> => {
  console.log("title1:-" + title1);
  console.log("title2:-" + title2);
  const datePast = pastDate;
  const newarr = datePast.split("/");
  const pastDay = newarr[0];
  console.log("day in insight is " + pastDay);
  const pastMonth = newarr[1];
  console.log("month in insight is " + pastMonth);
  const pastYear = newarr[2];
  console.log("year in insight is " + pastYear);
  const dateCurrent = currentDate;
  const newarr1 = dateCurrent.split("/");
  const currentDay = newarr1[0];
  console.log("current day is " + currentDay);
  const currentMonth = newarr1[1];
  console.log("current month is " + currentMonth);
  const currentYear = newarr1[2];
  console.log("current year is " + currentYear);
  await driver.execute("flutter:scroll", byValueKey("insightTodo"), { dx: 50, dy: -500, durationMilliseconds: 1000, frequency: 80 });
  if (currentMonth == pastMonth && currentYear == pastYear) {
    console.log("date widget in  if 1");
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    try {
      await driver.execute("flutter:waitFor", byText(title1), 2000);
      await driver.elementClick(byText(title1));
    } catch (warn) {
      console.log("qwerty");
      await driver.execute("flutter:waitFor", byText(title2), 2000);
      await driver.elementClick(byText(title2));
    }
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    await driver.elementClick(byText("CLOSE"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
  } else if (currentMonth != pastMonth && currentYear == pastYear) {
    console.log("date widget in else if 2");
    await driver.execute("flutter:waitFor", byValueKey("previousMonth"), 8000);
    //await driver.execute("flutter:waitForTappable", byValueKey("previousMonth"));
    await driver.elementClick(byValueKey("previousMonth"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    try {
      await driver.execute("flutter:waitFor", byText(title1), 2000);
      await driver.elementClick(byText(title1));
    } catch (warn) {
      console.log("qwerty");
      await driver.execute("flutter:waitFor", byText(title2), 2000);
      await driver.elementClick(byText(title2));
    }
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    await driver.elementClick(byText("CLOSE"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
    await driver.execute("flutter:waitForTappable", byValueKey("insightToday"));
    await driver.elementClick(byValueKey("insightToday"));
  } else if (currentMonth != pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 3");
    await driver.execute("flutter:waitFor", byValueKey("previousMonth"), 8000);
    //await driver.execute("flutter:waitForTappable", byValueKey("previousMonth"));
    await driver.elementClick(byValueKey("previousMonth"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    console.log("monthandyear widget");
    try {
      await driver.execute("flutter:waitFor", byText(title1), 2000);
      await driver.elementClick(byText(title1));
    } catch (warn) {
      console.log("qwerty");
      await driver.execute("flutter:waitFor", byText(title2), 2000);
      await driver.elementClick(byText(title2));
    }
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    await driver.elementClick(byText("CLOSE"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
    await driver.execute("flutter:waitForTappable", byValueKey("insightToday"));
    await driver.elementClick(byValueKey("insightToday"));
  } else if (currentMonth == pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 4");
    await driver.execute("flutter:waitFor", byValueKey("previousMonth"), 8000);
    //await driver.execute("flutter:waitForTappable", byValueKey("previousMonth"));
    await driver.elementClick(byValueKey("previousMonth"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    console.log("monthandyear widget");
    try {
      await driver.execute("flutter:waitFor", byText(title1), 2000);
      await driver.elementClick(byText(title1));
    } catch (warn) {
      console.log("qwerty");
      await driver.execute("flutter:waitFor", byText(title2), 2000);
      await driver.elementClick(byText(title2));
    }
    await driver.execute("flutter:waitForTappable", byText("CLOSE"));
    await driver.elementClick(byText("CLOSE"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.elementClick(byValueKey("close"));
    await driver.execute("flutter:waitForTappable", byValueKey("insightToday"));
    await driver.elementClick(byValueKey("insightToday"));
  }
};
export const addHabitSetup = async (pastDate: string, currentDate: string,): Promise<any> => {
  const datePast = pastDate;
  const newarr = datePast.split("/");
  const pastDay = newarr[0];
  console.log("day is " + pastDay);
  const pastMonth = newarr[1];
  console.log("month is " + pastMonth);
  const pastYear = newarr[2];
  console.log("year is " + pastYear);
  const dateCurrent = currentDate;
  const newarr1 = dateCurrent.split("/");
  const currentDay = newarr1[0];
  console.log("day is " + currentDay);
  const currentMonth = newarr1[1];
  console.log("month is " + currentMonth);
  const currentYear = newarr1[2];
  console.log("year is " + currentYear);
  if (currentMonth == pastMonth && currentYear == pastYear) {
    console.log("date widget in  if 1");
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear == pastYear) {
    console.log("date widget in else if 2");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 3");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth == pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 4");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }
};
export const addJournalSetup = async (pastDate: string, currentDate: string,): Promise<any> => {
  const datePast = pastDate;
  const newarr = datePast.split("/");
  const pastDay = newarr[0];
  console.log("day is " + pastDay);
  const pastMonth = newarr[1];
  console.log("month is " + pastMonth);
  const pastYear = newarr[2];
  console.log("year is " + pastYear);
  const dateCurrent = currentDate;
  const newarr1 = dateCurrent.split("/");
  const currentDay = newarr1[0];
  console.log("day is " + currentDay);
  const currentMonth = newarr1[1];
  console.log("month is " + currentMonth);
  const currentYear = newarr1[2];
  console.log("year is " + currentYear);
  if (currentMonth == pastMonth && currentYear == pastYear) {
    console.log("date widget in  if 1");
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear == pastYear) {
    console.log("date widget in else if 2");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth != pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 3");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText(pastMonth));
    await driver.elementClick(byText(pastMonth));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  } else if (currentMonth == pastMonth && currentYear != pastYear) {
    console.log("date widget in else if 4");
    await driver.execute("flutter:waitForTappable", byValueKey("monthAndYear"));
    await driver.elementClick(byValueKey("monthAndYear"));
    console.log("monthandyear widget");
    await driver.execute("flutter:waitFor", byValueKey("yearList"), 2000);
    //await driver.execute("flutter:scrollUntilVisible", byValueKey("yearList"), { item: byText(pastYear), dxScroll: -50, dyScroll: 0 }); // horizontal scroll done
    await driver.execute("flutter:waitForTappable", byText(pastYear));
    await driver.elementClick(byText(pastYear));
    await driver.execute("flutter:waitForTappable", byText("SET"));
    await driver.elementClick(byText("SET"));
    await driver.execute("flutter:waitForTappable", byText(pastDay));
    await driver.elementClick(byText(pastDay));
    await driver.execute("flutter:waitForTappable", byText("SAVE"));
    await driver.elementClick(byText("SAVE"));
  }
};


