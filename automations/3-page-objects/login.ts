import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from "assert";
import { addtitle } from "./page";
const loginMail = process.env.LAMBDA_AUTOMATION_TEST_EMAIL;
export const waitFor = async (delay: number, message?: string): Promise<any> => {
  await new Promise((resolve) => setTimeout(resolve, delay));
  if (message !== undefined) {
    console.log(message);
  }
};
class Login {
  public async login() {
    console.log("FAB");
    const appVersion = process.env.PLATFORM_VERSION
    if (parseInt(appVersion) > 12) {
      try {
        await driver.switchContext('NATIVE_APP');
        let el = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.Button[3]');
        await el.click();
        let el1 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.Button[2]');
        await el1.click();
        let el2 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.Button[1]');
        await el2.click();
        await driver.switchContext('FLUTTER');
      } catch (warn) {
        console.log("qwerty");
      }
    }
    else if (parseInt(appVersion) < 12) {
      await driver.switchContext('NATIVE_APP');
      let el = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.Button[3]');
      await el.click();
      await driver.switchContext('FLUTTER');
    }
    else if (parseInt(appVersion) === 12) {
      try {
        await driver.switchContext('NATIVE_APP');
        let el = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.Button[3]');
        await el.click();
        let el1 = await driver.$('/hierarchy/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.ScrollView/android.widget.LinearLayout/android.widget.LinearLayout/android.widget.LinearLayout[2]/android.widget.Button[2]');
        await el1.click();
        await driver.switchContext('FLUTTER');
      } catch (warn) {
        console.log("qwerty");
      }
    }

    await driver.execute("flutter:waitForTappable", byValueKey("versionDetails"));
    await driver.execute("smartui.takeScreenshot=screen_login_2389");
    //S-118_screen_login
    await driver.elementClick(byValueKey("versionDetails"));
    //screen_login_4581//appMobile_login_actionTapVersion
    await driver.execute("flutter:waitForTappable", byText("GOT IT"))
    await driver.execute("smartui.takeScreenshot=overlay_versionDetails_5193");//appMobile_login_versionOverlay_stateDefault
    await driver.elementClick(byText("GOT IT"))
    //overlay_versionDetails_3025//appMobile_login_versionOverlay_actionTapButtonOk
    console.log("Continue with Email");
    await driver.execute("flutter:waitForTappable", byValueKey("languageDetails"));
    await driver.elementClick(byValueKey("languageDetails"));
    await driver.execute("flutter:waitForTappable", byValueKey("close"));
    await driver.execute("smartui.takeScreenshot=bottomSheet_languageSelect_874");//appMobile_login_languageBottomSheet_stateDefault
    await driver.elementClick(byValueKey("close"));
    await driver.execute("flutter:waitForTappable", byValueKey("continueWithEmail"));
    await driver.elementClick(byValueKey("continueWithEmail"));
    //S-118_screen_login
    await addtitle(loginMail);
    await driver.execute("flutter:waitForTappable", byText("0"))
    //S-6041_screen_otpVerification
    //S-5645_screen_otpVerification
    await driver.elementClick(byText("0"));
    await driver.execute("flutter:waitForTappable", byValueKey("backspaceIcon"))
    await driver.execute("smartui.takeScreenshot=screen_otpVerification_5645");
    await driver.elementClick(byValueKey("backspaceIcon"));
    for (let i = 0; i <= 3; i++) {
      await driver.execute("flutter:waitForTappable", byValueKey("0"))
      await driver.elementClick(byValueKey("0"));
    }
    //S-544_screen_otpVerification
    await driver.execute("flutter:waitForTappable", byValueKey("closeSettings"))
    //S-1603_screen_otpVerification
    //await waitFor(8000)
    await driver.execute("smartui.takeScreenshot=screen_subscription_5396");
    //S-5396
    //await driver.elementClick(byText("Skip"));
    await driver.elementClick(byValueKey("closeSettings"));
    //S-2064
    console.log("login done");
  }
  public async loginSuccess(message?: string): Promise<void> {
    await driver.execute("flutter:waitForTappable", byText("Past"));
    const elem = await driver.getElementText(byText("Past"));
    console.log("elem value is:" + elem);
    const valuee = "Past";
    assert.strictEqual(elem, valuee);
  }
}
export default new Login();
