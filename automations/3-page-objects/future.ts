import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { addHabitSetup, addJournalSetup, addtitle } from "./page";
import addDays from 'date-fns/addDays'
import { subDays } from 'date-fns'

class Future {
  public async futureTodoDateFilter() {
    await driver.execute('flutter:waitForTappable', byText('Future'))
    await driver.elementClick(byText('Future'))
    await driver.execute('flutter:waitForTappable', byText('Todo'))
    await driver.elementClick(byText('Todo'))
    await driver.execute('flutter:waitForTappable', byV<PERSON><PERSON><PERSON><PERSON>('futureDateFilter'))
    await driver.elementClick(byValue<PERSON>ey('futureDateFilter'))
    //tab_futureTodo_5386//appMobile_commonUI_filters_dateFilter_future_button_actionTap
    await driver.execute('flutter:waitForTappable', byText('Next 7 Days'))
    await driver.execute("smartui.takeScreenshot=bottomSheet_dateFilter_1014")//appMobile_commonUI_filters_dateFilter_future_bottomSheet_stateDefault
    //tab_futureTodo_2450//appMobile_commonUI_filters_dateFilter_future_button_stateDefault
    await driver.elementClick(byText('Next 7 Days'))
    //bottomSheet_dateFilter_474//appMobile_commonUI_filters_dateFilter_future_button_actionTap
    await driver.execute('flutter:waitForTappable', byValueKey('futureTodoList'))
    await driver.execute('flutter:scroll', byValueKey('futureTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('futureDateFilter'))
    await driver.elementClick(byValueKey('futureDateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Next 30 Days'))
    await driver.elementClick(byText('Next 30 Days'))
    //tab_futureTodo_4223//appMobile_commonUI_filters_dateFilter_future_button_stateUpdated
    //bottomSheet_dateFilter_1734//appMobile_commonUI_filters_dateFilter_future_bottomSheet_actionTapOptionNext30Days
    await driver.execute('flutter:waitForTappable', byValueKey('futureTodoList'))
    await driver.execute('flutter:scroll', byValueKey('futureTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:scrollIntoView', byValueKey('futureTodoList'), { alignment: 1 })
    await driver.execute('flutter:waitForTappable', byValueKey('futureDateFilter'))
    await driver.elementClick(byValueKey('futureDateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Next 12 Months'))
    await driver.elementClick(byText('Next 12 Months'))
    //bottomSheet_dateFilter_2292//appMobile_commonUI_filters_dateFilter_future_bottomSheet_actionTapOptionNext12Months
    await driver.execute('flutter:waitForTappable', byValueKey('futureTodoList'))
    await driver.execute('flutter:scroll', byValueKey('futureTodoList'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await driver.execute('flutter:waitForTappable', byValueKey('dateFilter'))
    await driver.elementClick(byValueKey('dateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Custom'))
    await driver.elementClick(byText('Custom'))
    //bottomSheet_dateFilter_4102//appMobile_commonUI_filters_dateFilter_future_bottomSheet_actionCustomTap
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.execute('smartui.takeScreenshot=overlay_dateRangePicker_631')//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_stateNullValue
    await driver.elementClick(byValueKey('startDate'))
    //overlay_dateRangePicker_6223//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_actionStartDateBlockTap
    await driver.execute('flutter:waitForTappable', byText('After 14 Days'))
    //overlay_datePicker_1910//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_startDatePicker_ruleDefault
    await driver.execute('smartui.takeScreenshot=overlay_datePicker_4741')//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_startDatePicker_stateDefault
    await driver.elementClick(byText('After 14 Days'))
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.execute('smartui.takeScreenshot=overlay_datePicker_160')//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_startDatePicker_statePresetSelected
    await driver.elementClick(byText('SAVE'))
    //overlay_datePicker_2738//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_startDatePicker_actionSaveButtonTap
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.execute('smartui.takeScreenshot=overlay_dateRangePicker_2191')//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_stateDateAndNullSelected
    await driver.elementClick(byValueKey('endDate'))
    //overlay_datePicker_3116 //appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_endDatePicker_stateMinStart
    //overlay_datePicker_5136//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_endDatePicker_ruleMinStart
    //overlay_dateRangePicker_4395//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_actionEndDateBlockTap
    //overlay_datePicker_6120//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_endDatePicker_ruleDefault
    await driver.execute('flutter:waitForTappable', byText('Forever'))
    await driver.execute("smartui.takeScreenshot=overlay_datePicker_6200")//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_endDatePicker_stateDefault
    await driver.elementClick(byText('Forever'))
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.execute('smartui.takeScreenshot=overlay_datePicker_4352')//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_endDatePicker_stateForeverPresetSelected
    await driver.elementClick(byText('SAVE'))
    //overlay_datePicker_5305//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_endDatePicker_actionSaveButtonTap
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.execute('smartui.takeScreenshot=overlay_dateRangePicker_5173')//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_stateDateAndForeverSelected
    await driver.elementClick(byText('APPLY'))
    //overlay_dateRangePicker_1386//appMobile_commonUI_filters_dateFilter_future_bottomSheet_dateRangePicker_actionApplyButtonTap
    await driver.execute('flutter:waitForTappable', byValueKey('futureDateFilter'))
    await driver.execute("smartui.takeScreenshot=tab_futureTodo_2660")//appMobile_commonUI_filters_dateFilter_future_button_stateCustomUpdated
    await driver.elementClick(byValueKey('futureDateFilter'))
    await driver.execute('flutter:waitForTappable', byText('From Tomorrow'))
    await driver.execute("smartui.takeScreenshot=bottomSheet_dateFilter_1541")//appMobile_commonUI_filters_dateFilter_future_bottomSheet_stateCustomApplied
    await driver.elementClick(byText('From Tomorrow'))
    //6151_bottomSheet_dateFilter//appMobile_commonUI_filters_dateFilter_future_bottomSheet_actionTapOptionFromTomorrow
    await driver.execute('flutter:waitForTappable', byTooltip('Delete'))
    await driver.elementClick(byTooltip('Delete'))
    //tab_futureTodo_5741//appMobile_commonUI_filters_dateFilter_future_button_actionCloseIconTap
  }

  // Past Todo Status Filter
  public async futureTodoStatusFilter() {
    await driver.execute('flutter:waitForTappable', byValueKey('statusFilter'))
    await driver.elementClick(byValueKey('statusFilter'))
    await driver.execute('flutter:waitForTappable', byText('Overdue'))
    await driver.execute('smartui.takeScreenshot=status_bottomsheet')
    await driver.elementClick(byText('Overdue'))
    await driver.execute('flutter:waitForTappable', byValueKey('statusFilter'))
    await driver.elementClick(byValueKey('statusFilter'))
    await driver.execute('flutter:waitForTappable', byText('Completed'))
    await driver.elementClick(byText('Completed')) // since no cpmplete items here no list visible
    await driver.execute('flutter:waitForTappable', byValueKey('statusFilter'))
    await driver.elementClick(byValueKey('statusFilter'))
    await driver.execute('flutter:waitForTappable', byText('All'))
    await driver.elementClick(byText('All'))
  }

  // Past Todo Hashtag Filter
  public async futureTodoHashtagFilter() {
    await driver.execute('flutter:waitForTappable', byText('Past'))
    await driver.elementClick(byText('Past'))
    await driver.execute('flutter:waitForTappable', byValueKey('hashTagFilter'))
    await driver.elementClick(byValueKey('hashTagFilter'))
    await driver.execute('flutter:waitForTappable', byText('#morning'))
    await driver.elementClick(byText('#morning'))
    await driver.execute('flutter:waitForTappable', byText('#smile'))
    await driver.elementClick(byText('#smile'))
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.elementClick(byText('APPLY'))
    await driver.execute('flutter:waitForTappable', byTooltip('Delete'))
    await driver.elementClick(byTooltip('Delete'))
    const fabutton = byValueKey('increment');
    await driver.execute('flutter:waitForTappable', fabutton);
    await driver.elementClick(fabutton);
    const addtodo = byText('Add Todo');
    await driver.execute('flutter:waitForTappable', addtodo);
    await driver.elementClick(addtodo);
    await driver.execute('flutter:waitForTappable', byValueKey('addHashTag'))
    await driver.elementClick(byValueKey('addHashTag'))
    await driver.execute('flutter:waitForTappable', byText('#smile'))
    await driver.execute('flutter:scroll', byText('#smile'), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 })// delete hashtag
    await driver.execute('flutter:waitForTappable', byText('DELETE'))
    await driver.elementClick(byText('DELETE'))
    await driver.execute('flutter:waitForTappable', byText('#morning'))
    await driver.execute('flutter:scroll', byText('#morning'), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 })// delete hashtag
    await driver.execute('flutter:waitForTappable', byText('DELETE'))
    await driver.elementClick(byText('DELETE'))
    await driver.execute('flutter:waitForTappable', byValueKey('close'))
    await driver.elementClick(byValueKey('close'))
    await driver.execute('flutter:waitForTappable', byValueKey('CLOSE'))
    await driver.elementClick(byValueKey('CLOSE'))
  }

  public async futureTodoDateStatusHashtagFilter() {
    await driver.execute('flutter:scrollIntoView', byValueKey('futureTodoList'), { alignment: 1 })
    await driver.execute('flutter:waitForTappable', byValueKey('futureDateFilter'))
    //await driver.execute('smartui.takeScreenshot=Past_screen') // Screenshot_pastscreen
    await driver.elementClick(byValueKey('futureDateFilter'))
    await driver.execute('flutter:waitForTappable', byText('Next 30 Days'))
    await driver.execute('smartui.takeScreenshot=datefilter_bottomsheet') // Screenshot_bottomsheet
    await driver.elementClick(byText('Next 7 Days'))
    await driver.execute('flutter:waitForTappable', byValueKey('hashTagFilter'))
    await driver.elementClick(byValueKey('hashTagFilter'))
    await driver.execute('flutter:waitForTappable', byText('#morning'))
    await driver.elementClick(byText('#morning'))
    await driver.execute("flutter:scroll", byValueKey("hashTagFilterList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute('flutter:waitForTappable', byText('#smile'))
    await driver.elementClick(byText('#smile'))
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.elementClick(byText('APPLY'))
    await driver.execute("flutter:scroll", byValueKey("futureFilterList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute('flutter:waitForTappable', byValueKey('statusFilter'))
    await driver.elementClick(byValueKey('statusFilter'))
    await driver.execute('flutter:waitForTappable', byText('Completed'))
    await driver.elementClick(byText('Completed'))
    await driver.execute("flutter:scroll", byValueKey("futureFilterList"), { dx: -150, dy: 0, durationMilliseconds: 200, frequency: 10 });
    await driver.execute('flutter:waitForTappable', byText('Clear all'))
    await driver.elementClick(byText('Clear all'))
  }

  //Here need to check whether added habits falling under correct section ; upcoming/active/completed
  public async futureHabitList() {
    await driver.execute('flutter:waitForTappable', byText('Habit Setup'))
    await driver.elementClick(byText('Habit Setup'))
    const fabutton = byValueKey('increment');
    await driver.execute('flutter:waitForTappable', fabutton);
    await driver.elementClick(fabutton);
    const setupHabit = byText('Setup Habit');
    await driver.execute('flutter:waitForTappable', setupHabit);
    await driver.elementClick(setupHabit);
    const d = new Date();
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear();
    const result_add = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5)// Active habit end date
    const result_add1 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30)// upcoming habit start date
    const result_add2 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 370)// upcoming habit end date
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5)// Active habit start date
    const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30)// completed habit end date
    const result_sub2 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 370) // completed habit start date 
    console.log("1st group" + result_add + "," + result_add1 + "," + result_add2)
    console.log("2nd group" + result_sub + "," + result_sub1 + "," + result_sub2)
    const m_add = result_add.getMonth()
    const m_add1 = result_add1.getMonth()
    const m_add2 = result_add2.getMonth()
    const futureDate = result_add.getDate() + '/' + (result_add.getMonth() + 1) + '/' + result_add.getFullYear();
    const futureDate1 = result_add1.getDate() + '/' + (result_add1.getMonth() + 1) + '/' + result_add1.getFullYear();
    const futureDate2 = result_add2.getDate() + '/' + (result_add2.getMonth() + 2) + '/' + result_add2.getFullYear();
    const pastDate = result_sub.getDate() + '/' + (result_sub.getMonth() + 1) + '/' + result_sub.getFullYear();
    const pastDate1 = result_sub1.getDate() + '/' + (result_sub1.getMonth() + 1) + '/' + result_sub1.getFullYear();
    const pastDate2 = result_sub2.getDate() + '/' + (result_sub2.getMonth() + 1) + '/' + result_sub2.getFullYear();
    const m_sub = result_sub.getMonth()
    const m_sub1 = result_sub1.getMonth()
    const m_sub2 = result_sub2.getMonth()
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const currentDateString = d.getDate() + '/' + monthNames[d.getMonth()] + '/' + d.getFullYear();
    const futureDateString = result_add.getDate() + '/' + monthNames[m_add] + '/' + result_add.getFullYear();
    const futureDateString1 = result_add1.getDate() + '/' + monthNames[m_add1] + '/' + result_add1.getFullYear();
    const futureDateString2 = result_add2.getDate() + '/' + monthNames[m_add2] + '/' + result_add2.getFullYear();
    //const futureDateString3 = result_add3.getDate() + '/' + monthNames[m_add3] + '/' + result_add3.getFullYear();
    const pastDateString = result_sub.getDate() + '/' + monthNames[m_sub] + '/' + result_sub.getFullYear();
    const pastDateString1 = result_sub1.getDate() + '/' + monthNames[m_sub1] + '/' + result_sub1.getFullYear();
    const pastDateString2 = result_sub2.getDate() + '/' + monthNames[m_sub2] + '/' + result_sub2.getFullYear();
    const valueActiveHabit = 'active_habit' + pastDate + '-' + futureDate;
    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await addtitle(valueActiveHabit)
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await driver.elementClick(byValueKey('selectHabitDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate'))
    await addHabitSetup(pastDateString, currentDateString)
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate'))
    await addHabitSetup(futureDateString, currentDateString)
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.elementClick(byText('APPLY'))
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.elementClick(byText('SAVE'))
    //30 days
    await driver.execute('flutter:waitForTappable', fabutton);
    await driver.elementClick(fabutton);
    await driver.execute('flutter:waitForTappable', setupHabit);
    await driver.elementClick(setupHabit);
    const valueCompletedHabit = 'completed_habit' + pastDate2 + '-' + pastDate1;
    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await addtitle(valueCompletedHabit)
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await driver.elementClick(byValueKey('selectHabitDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate'))
    await addHabitSetup(pastDateString2, currentDateString)
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends;15 days later;30 days later;60 days later
    /*await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await addHabitSetup(pastDateString1, currentDateString)
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.elementClick(byText('APPLY'))
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.elementClick(byText('SAVE'))

    //370 days
    await driver.execute('flutter:waitForTappable', fabutton);
    await driver.elementClick(fabutton);
    await driver.execute('flutter:waitForTappable', setupHabit);
    await driver.elementClick(setupHabit);
    const valueUpcomingHabit = 'upcoming_habit' + futureDate1 + '-' + futureDate2;
    await driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await driver.elementClick(byValueKey('addHabitTitle'))
    await addtitle(valueUpcomingHabit)
    await driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await driver.elementClick(byValueKey('selectHabitDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate'))
    await addHabitSetup(futureDateString1, currentDateString)
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends;15 days later;30 days later;60 days later
    /*await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await addHabitSetup(futureDateString2, currentDateString)
    console.log('futureDateString2', +futureDate2)
    console.log('futureDateString1', +futureDate1)
    console.log('futureDateString', +futureDate)
    console.log('pastDateString2', +pastDate2)
    console.log('pastDateString1', +pastDate1)
    console.log('pastDateString', +pastDate)
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.elementClick(byText('APPLY'))
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.elementClick(byText('SAVE'))

    //deleting added habit setups from future screens
    await driver.execute('flutter:waitForTappable', byText(valueUpcomingHabit))
    await driver.execute("smartui.takeScreenshot=tab_futureHabitSetup_5534")//appMobile_commonUI_habit_setupBlock_stateDefault
    await driver.elementClick(byText(valueUpcomingHabit))
    //tab_futureHabitSetup_1133//appMobile_commonUI_habit_setupBlock_actionTap
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byValueKey('quaternaryButton'))
    await driver.elementClick(byValueKey('quaternaryButton'))
    await driver.execute('flutter:waitForTappable', byText(valueCompletedHabit))
    await driver.elementClick(byText(valueCompletedHabit))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byValueKey('quaternaryButton'))
    await driver.elementClick(byValueKey('quaternaryButton'))
    await driver.execute('flutter:waitForTappable', byText(valueActiveHabit))
    await driver.elementClick(byText(valueActiveHabit))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    await driver.execute("smartui.takeScreenshot=screen_habitSetupEdit_111")//appMobile_commonUI_habit_setupEdit_activeHabitSetup_stateDefaultOfYesOrNo
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byValueKey('quaternaryButton'))
    await driver.elementClick(byValueKey('quaternaryButton'))
  }

  public async futureJournalList() {
    await driver.execute('flutter:waitForTappable', byText('Journal Setup'))
    await driver.elementClick(byText('Journal Setup'))
    const fabutton = byValueKey('increment');
    await driver.execute('flutter:waitForTappable', fabutton);
    await driver.elementClick(fabutton);
    const setupJournal = byText('Setup Journal');
    await driver.execute('flutter:waitForTappable', setupJournal);
    await driver.elementClick(setupJournal);
    const d = new Date();
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear();
    const result_add = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5)// Active habit end date
    const result_add1 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30)// upcoming habit start date
    const result_add2 = addDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 370)// upcoming habit end date
    const result_sub = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 5)// Active habit start date
    const result_sub1 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 30)// completed habit end date
    const result_sub2 = subDays(new Date(d.getFullYear(), d.getMonth(), d.getDate()), 370) // completed habit start date 
    console.log("1st group" + result_add + "," + result_add1 + "," + result_add2)
    console.log("2nd group" + result_sub + "," + result_sub1 + "," + result_sub2)
    const m_add = result_add.getMonth()
    const m_add1 = result_add1.getMonth()
    const m_add2 = result_add2.getMonth()
    const futureDate = result_add.getDate() + '/' + (result_add.getMonth() + 1) + '/' + result_add.getFullYear();
    const futureDate1 = result_add1.getDate() + '/' + (result_add1.getMonth() + 1) + '/' + result_add1.getFullYear();
    const futureDate2 = result_add2.getDate() + '/' + (result_add2.getMonth() + 2) + '/' + result_add2.getFullYear();
    const pastDate = result_sub.getDate() + '/' + (result_sub.getMonth() + 1) + '/' + result_sub.getFullYear();
    const pastDate1 = result_sub1.getDate() + '/' + (result_sub1.getMonth() + 1) + '/' + result_sub1.getFullYear();
    const pastDate2 = result_sub2.getDate() + '/' + (result_sub2.getMonth() + 1) + '/' + result_sub2.getFullYear();
    const m_sub = result_sub.getMonth()
    const m_sub1 = result_sub1.getMonth()
    const m_sub2 = result_sub2.getMonth()
    const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const currentDateString = d.getDate() + '/' + monthNames[d.getMonth()] + '/' + d.getFullYear();
    const futureDateString = result_add.getDate() + '/' + monthNames[m_add] + '/' + result_add.getFullYear();
    const futureDateString1 = result_add1.getDate() + '/' + monthNames[m_add1] + '/' + result_add1.getFullYear();
    const futureDateString2 = result_add2.getDate() + '/' + monthNames[m_add2] + '/' + result_add2.getFullYear();
    const pastDateString = result_sub.getDate() + '/' + monthNames[m_sub] + '/' + result_sub.getFullYear();
    const pastDateString1 = result_sub1.getDate() + '/' + monthNames[m_sub1] + '/' + result_sub1.getFullYear();
    const pastDateString2 = result_sub2.getDate() + '/' + monthNames[m_sub2] + '/' + result_sub2.getFullYear();
    const valueActiveJournal = 'active_journal: ' + pastDate + '-' + futureDate;
    await driver.execute('flutter:waitForTappable', byValueKey('addJournalTitle'))
    await driver.elementClick(byValueKey('addJournalTitle'))
    await addtitle(valueActiveJournal)
    await driver.execute('flutter:waitForTappable', byValueKey('selectJournalDateRange'))
    await driver.elementClick(byValueKey('selectJournalDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate'))
    await addJournalSetup(pastDateString, currentDateString)
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends;15 days later;30 days later;60 days later
    /*await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await addJournalSetup(futureDateString, currentDateString)
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.elementClick(byText('APPLY'))
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.elementClick(byText('SAVE'))
    //30 days
    await driver.execute('flutter:waitForTappable', fabutton);
    await driver.elementClick(fabutton);
    await driver.execute('flutter:waitForTappable', setupJournal);
    await driver.elementClick(setupJournal);
    const valueCompletedJournal = 'completed_journal:-' + pastDate2 + '-' + pastDate1;
    await driver.execute('flutter:waitForTappable', byValueKey('addJournalTitle'))
    await driver.elementClick(byValueKey('addJournalTitle'))
    await addtitle(valueCompletedJournal)
    await driver.execute('flutter:waitForTappable', byValueKey('selectJournalDateRange'))
    await driver.elementClick(byValueKey('selectJournalDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate'))
    await addJournalSetup(pastDateString2, currentDateString)
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends;15 days later;30 days later;60 days later
    /*await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await addJournalSetup(pastDateString1, currentDateString)
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.elementClick(byText('APPLY'))
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.elementClick(byText('SAVE'))

    //370 days
    await driver.execute('flutter:waitForTappable', fabutton);
    await driver.elementClick(fabutton);
    await driver.execute('flutter:waitForTappable', setupJournal);
    await driver.elementClick(setupJournal);
    const valueUpcomingJournal = 'upcoming_journal: ' + futureDate1 + '-' + futureDate2;
    await driver.execute('flutter:waitForTappable', byValueKey('addJournalTitle'))
    await driver.elementClick(byValueKey('addJournalTitle'))
    await addtitle(valueUpcomingJournal)
    await driver.execute('flutter:waitForTappable', byValueKey('selectJournalDateRange'))
    await driver.elementClick(byValueKey('selectJournalDateRange'))
    await driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await driver.elementClick(byValueKey('startDate'))
    await addJournalSetup(futureDateString1, currentDateString)
    await driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await driver.elementClick(byValueKey('endDate')) // Never ends;15 days later;30 days later;60 days later
    /*await driver.execute('flutter:waitForTappable', byText('Never ends'))
    await driver.elementClick(byText('Never ends')) */
    await addJournalSetup(futureDateString2, currentDateString)
    console.log('futureDateString2', +futureDate2)
    console.log('futureDateString1', +futureDate1)
    console.log('futureDateString', +futureDate)
    console.log('pastDateString2', +pastDate2)
    console.log('pastDateString1', +pastDate1)
    console.log('pastDateString', +pastDate)
    await driver.execute('flutter:waitForTappable', byText('APPLY'))
    await driver.elementClick(byText('APPLY'))
    await driver.execute('flutter:waitForTappable', byText('SAVE'))
    await driver.elementClick(byText('SAVE'))

    //deleting added habit setups from future screens
    await driver.execute('flutter:waitForTappable', byText(valueUpcomingJournal))
    await driver.elementClick(byText(valueUpcomingJournal))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byValueKey('quaternaryButton'))
    await driver.elementClick(byValueKey('quaternaryButton'))
    await driver.execute('flutter:waitForTappable', byText(valueCompletedJournal))
    await driver.elementClick(byText(valueCompletedJournal))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byValueKey('quaternaryButton'))
    await driver.elementClick(byValueKey('quaternaryButton'))
    await driver.execute('flutter:waitForTappable', byText(valueActiveJournal))
    await driver.elementClick(byText(valueActiveJournal))
    await driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))
    await driver.elementClick(byValueKey('deleteButton'))
    await driver.execute('flutter:waitForTappable', byValueKey('quaternaryButton'))
    await driver.elementClick(byValueKey('quaternaryButton'))
  }

}
export default new Future();



