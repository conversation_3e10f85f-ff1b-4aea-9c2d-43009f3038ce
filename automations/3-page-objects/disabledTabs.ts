import { byText, byTooltip, byType, by<PERSON><PERSON><PERSON><PERSON><PERSON> } from "appium-flutter-finder";
import { strict as assert } from 'assert'

class DisabledTabs {
    public async mevolveFeatures() {
        await driver.execute('flutter:waitForTappable', by<PERSON><PERSON><PERSON><PERSON><PERSON>('hamburgerMenu'))
        await driver.elementClick(by<PERSON><PERSON><PERSON><PERSON><PERSON>('hamburgerMenu'))
        await driver.execute('flutter:waitForTappable', byText('Settings'))
        await driver.execute("smartui.takeScreenshot=overlay_hamburgerTray_3129");//appMobile_screens_hamburger_stateDefaultWithoutUnreadNotification
        await driver.elementClick(byText('Settings'))
        await driver.execute('flutter:waitForTappable', byText('Todo'))
        await driver.elementClick(byText('Todo'))
        await driver.execute('flutter:waitForTappable', byText('Habit'))
        await driver.elementClick(byText('Habit'))
        await driver.execute('flutter:waitForTappable', byText('Journal'))
        await driver.elementClick(byText('Journal'))
        await driver.execute('flutter:waitForTappable', byText('Note'))
        await driver.elementClick(byText('Note'))
        await driver.execute('flutter:waitForTappable', byText('OK'))
        await driver.execute("smartui.takeScreenshot=overlay_featureMinimumAlert_6124");//appMobile_screens_hamburger_settings_features_MinimumFeatureNeededPopup_stateDefault
        await driver.elementClick(byText('OK'))
        //overlay_featureMinimumAlert_3992//appMobile_screens_hamburger_settings_features_MinimumFeatureNeededPopup_actionTapButtonOk
        await driver.execute('flutter:waitForTappable', byValueKey('closeSettings'))
        await driver.elementClick(byValueKey('closeSettings'))

        await driver.execute('flutter:waitForTappable', byValueKey('todayDisabledIcon'))
        await driver.elementClick(byValueKey('todayDisabledIcon'))
        //screen_settings_5255//appMobile_screens_today_common_ruleWhenFeatureDisabled
        await driver.execute('flutter:waitForTappable', byText('Todo'))
        await driver.execute("smartui.takeScreenshot=tab_disabledFeatures_5897");//appMobile_screens_today_common_disabledTab_stateDefault
        await driver.elementClick(byText('Todo'))
        //tab_disabledFeatures_2290//appMobile_screens_today_common_disabledTab_actionTapDisabledToggleBar
        await driver.execute('flutter:waitForTappable', byText('Future'))
        await driver.elementClick(byText('Future'))
        await driver.execute('flutter:waitForTappable', byValueKey('DisabledIcon'))
        await driver.elementClick(byValueKey('DisabledIcon'))
        await driver.execute('flutter:waitForTappable', byText('Habit'))
        await driver.execute("smartui.takeScreenshot=tab_disabledFeatures_5752");//appMobile_screens_future_common_disabledTab_stateDefault
        //tab_disabledFeatures_2675//appMobile_screens_future_common_ruleWhenFeatureDisabled
        await driver.elementClick(byText('Habit'))
        //tab_disabledFeatures_2480//appMobile_screens_future_common_disabledTab_actionDisabledToggleBarTap
        await driver.execute('flutter:waitForTappable', byText('Past'))
        await driver.elementClick(byText('Past'))
        await driver.execute('flutter:waitForTappable', byValueKey('DisabledIcon'))
        await driver.elementClick(byValueKey('DisabledIcon'))
        await driver.execute('flutter:waitForTappable', byText('Journal'))
        await driver.execute("smartui.takeScreenshot=tab_disabledFeatures_5795");//appMobile_screens_past_common_disabledTab_stateDefault
        //screen_settings_1639//appMobile_screens_past_common_ruleWhenFeatureDisabled
        await driver.elementClick(byText('Journal'))
        //tab_disabledFeatures_502//appMobile_screens_past_common_disabledTab_actionDisabledToggleBarTap


        // await driver.execute('flutter:waitForTappable', byValueKey('hamburgerMenu'))
        // await driver.elementClick(byValueKey('hamburgerMenu'))
        // await driver.execute('flutter:waitForTappable', byText('Settings'))
        // await driver.elementClick(byText('Settings'))
        // await driver.execute('flutter:waitForTappable', byText('Note'))
        // await driver.elementClick(byText('Note'))
        // await driver.execute('flutter:waitForTappable', byValueKey('closeSettings'))
        // await driver.elementClick(byValueKey('closeSettings'))
        // await driver.execute('flutter:waitForTappable', byValueKey('hamburgerMenu'))
        // await driver.elementClick(byValueKey('hamburgerMenu'))
        // await driver.execute('flutter:waitForTappable', byText('Insight'))
        // await driver.elementClick(byText('Insight'))
        // await driver.execute('flutter:waitForTappable', byValueKey('DisabledIcon'))
        // await driver.elementClick(byValueKey('DisabledIcon'))
        // await driver.execute('flutter:waitForTappable', byText('Note'))
        // await driver.execute("smartui.takeScreenshot=tab_disabledFeatures_2347")//appMobile_screens_hamburger_insight_common_disabledTab_stateDefault
        // //screen_settings_2788//appMobile_screens_hamburger_insight_common_ruleWhenFeatureDisabled
        // //tab_disabledFeatures_6135//appMobile_screens_hamburger_insight_common_disabledTab_actionDisabledToggleBarTap
        // await driver.elementClick(byText('Note'))
        // await driver.execute('flutter:waitForTappable', byValueKey('closeNotificationSettings'))
        // await driver.elementClick(byValueKey('closeNotificationSettings'))
        await driver.execute('flutter:waitForTappable', byText('Today'))
        await driver.elementClick(byText('Today'))
    }


    public async settingsSuccess(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Settings'))
        const elem = await driver.getElementText(byText('Settings'))
        console.log('elem value is:' + elem)
        const valuee = 'Settings'
        assert.strictEqual(elem, valuee)
    }

    public async settingsSuccessMevolveLock(message?: string): Promise<void> {
        await driver.execute('flutter:waitForTappable', byText('Settings'))
        const elem = await driver.getElementText(byText('Settings'))
        console.log('elem value is:' + elem)
        const valuee = 'Settings'
        assert.strictEqual(elem, valuee)
        await driver.execute('flutter:waitForTappable', byValueKey('closeSettings'))
        await driver.elementClick(byValueKey('closeSettings'))
    }

}

export default new DisabledTabs();