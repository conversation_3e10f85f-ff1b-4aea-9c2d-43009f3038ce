/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'
import { Key } from 'webdriverio'

export class FutureNoteFilter {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async futureNoteFilter (): Promise<void> {
    // await this.driver.elementClick(byText('Past'))
    await this.driver.elementClick(byText('Note'))
    await waitFor(5000)

    await this.driver.elementClick(byValueKey('datefilter'))
    await waitFor(2000)
    await this.driver.elementClick(byText('Next 7 Days'))

    await waitFor(2500)
    await this.driver.execute('flutter:scrollUntilVisible', byVal<PERSON><PERSON><PERSON>('future_note_filter'), { item: byValue<PERSON>ey('clearall'), dxScroll: -200, dyScroll: 0 }) // horizontal scroll done
    await waitFor(2500)
    await this.driver.elementClick(byValueKey('clearall'))
    await waitFor(5000)

    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('futurenotelist'), { item: byText('Mevolve@123'), dxScroll: 0, dyScroll: -1000 })
    await this.driver.elementClick(byText('Mevolve@123'))
    console.log('CLEAR ALL')
  }
}
