/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'

export class PastNoteFilter {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async pastNoteFilter (): Promise<void> {
    const NULL = '\u0000'
    await this.driver.elementClick(byText('Past'))
    await this.driver.elementClick(byText('Note'))
    await waitFor(5000)
    await this.driver.execute('flutter:scrollUntilVisible', byValue<PERSON>ey('pastpagefilters_note'), { item: byV<PERSON><PERSON><PERSON><PERSON>('saerchtext'), dxScroll: -150, dyScroll: 0 }) // horizontal scroll done
    await this.driver.elementClick(byValue<PERSON>ey('saerchtext'))
    const element = byValue<PERSON>ey('searchtext')
    await waitFor(5000)
    await this.driver.execute('flutter:setTextEntryEmulation', false)
    await this.driver.switchContext('NATIVE_APP')
    const el1 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText')
    await el1.setValue('abcd')
    await this.driver.pressKeyCode(66)

    await this.driver.switchContext('FLUTTER')
    // await this.driver.elementSendKeys(byValueKey('searchtext'), 'abc')
    await waitFor(5000)

    // await this.driver.pressKeyCode(66)
    // const adb = await ADB.createADB()
    // await adb.shell(['input', 'keyevent', 'ENTER'])
    // console.log(('KEY>BACKSPACE' + Key.Backspace))
    // await this.driver.elementSendKeys(byValueKey('searchtext'), 'abcd' + (Key.Backspace))

    // const actions = this.driver.actions()

    // send keys to the element
    // await actions.sendKeys(element, 'Lorem ipsum dolor sit amet').perform()

    // delete the last character
    // await actions.keyDown(Key.BACK_SPACE).keyUp(Key.BACK_SPACE).perform()
    // await this.driver.elementSendKeys(byValueKey('searchtext'), 'Lorem ipsum dolor sit amet ')
    // await this.driver.elementSendKeys(byValueKey('searchtext'), (Key.Backspace))
    // await this.driver.elementSendKeys(byValueKey('searchtext'), (Key.Return))
    // await this.driver.elementSendKeys(byValueKey('searchtext'), )
    /* await this.driver.execute('flutter:setTextEntryEmulation', false)
    await this.driver.execute('flutter:getVMInfo')
    await this.driver.execute('flutter:getIsolate')
    // await this.driver.elementSendKeys(byValueKey('searchtext'), 'Search')
    await waitFor(2000)
    // await this.driver.switchContext('NATIVE_APP')
    // await this.driver.pressKeyCode(10)
    // await this.driver.keys(Key.Enter)
    // await this.driver.execute("document.activeElement.dispatchEvent(new KeyboardEvent('keydown', {'key': 'Enter'}));")
    // await this.driver.keys('\uE007')
    await waitFor(2000)
    // const textField = byValueKey('searchtext')
    // await this.driver.elementSendKeys(byValueKey('searchtext'), 'Key.RETURN')
    console.log('KEY PRESSED')
    await this.driver.execute('flutter:setTextEntryEmulation', true) */
    // await this.driver.switchContext('FLUTTER')
    await this.driver.elementClick(byValueKey('dateselector'))
    await waitFor(2000)
    await this.driver.elementClick(byText('Last 7 Days'))

    await waitFor(2500)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('pastpagefilters_note'), { item: byText('Clear All'), dxScroll: -200, dyScroll: 0 }) // horizontal scroll done
    await waitFor(2500)
    await this.driver.elementClick(byText('Clear All'))
    await waitFor(3000)
    await this.driver.elementClick(byText('Clear'))
    await waitFor(3000)

    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 0, dyScroll: -1000 })
    await this.driver.elementClick(byText('poiuy'))
    console.log('CLEAR ALL')
  }
}
