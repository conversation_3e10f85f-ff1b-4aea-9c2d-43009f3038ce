/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */

import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates, @typescript-eslint/no-unused-vars
import { byValueKey, byType, byText, byTooltip } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'

export class NotesToday {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async testNotesToday (): Promise<void> {
    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
    const d = new Date()
    const currentMonth = months[d.getMonth()]
    console.log(currentMonth)
    const currentYear = new Date().getFullYear()
    console.log(currentYear)
    const valuee1 = currentMonth + ' ' + currentYear
    console.log(valuee1)
    await waitFor(1000)
    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    // Add Todo
    const valuee = 'New Note_1 ' + currentDate
    const fabutton = byTooltip('increment')
    // await this.driver.execute('flutter:waitFor', fabutton, 5000)
    await this.driver.execute('flutter:waitForTappable', fabutton)
    console.log('Note1')
    await this.driver.elementClick(fabutton)

    const addNote = byText('Add Note')
    // await this.driver.execute('flutter:waitFor', addNote, 10000)
    await this.driver.execute('flutter:waitForTappable', addNote)
    console.log('Note2')
    await this.driver.elementClick(addNote)
    // await this.driver.execute('flutter:waitFor', byValueKey('addNoteTitle'), 10000)
    await this.driver.execute('flutter:waitForTappable', byValueKey('addNoteTitle'))
    console.log('Note3')
    await this.driver.elementClick(byValueKey('addNoteTitle'))
    // await this.driver.execute('flutter:waitFor', byValueKey('textFieldBottomSheet'), 10000)
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    console.log('Note4')
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), valuee)
    await this.driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    // await this.driver.execute('flutter:waitFor', byValueKey('addTitleDone'), 10000)

    console.log('Note5')
    await this.driver.elementClick(byValueKey('sendButton'))
    // await this.driver.execute('flutter:waitFor', byText('Save'), 10000)
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    console.log('Note6')
    await this.driver.elementClick(byText('Save'))
    // await this.driver.execute('flutter:waitFor', byValueKey('habitList'), 10000)
    console.log('Note7')
    // Edit Notes
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('toDoLists'), { item: byText('Notes'), dxScroll: 0, dyScroll: -400 })
    await this.driver.execute('flutter:scrollIntoView', byValueKey('noteList'), { alignment: 0.5 })

    console.log('Note8')
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    assert.strictEqual(await this.driver.getElementText(elem), valuee)
    console.log('Success')
    console.log('Note9')
    await this.driver.execute('flutter:waitForTappable', byText(valuee))
    console.log('Note10')
    await this.driver.elementClick(byText(valuee))
    console.log('Note11')
    await this.driver.execute('flutter:waitForTappable', byValueKey('moodSelector'))
    await this.driver.elementClick(byValueKey('moodSelector'))
    console.log('Note12')
    await this.driver.execute('flutter:waitForTappable', byText('Happy'))
    await this.driver.elementClick(byText('Happy'))
    console.log('Note13')
    await this.driver.execute('flutter:waitForTappable', byValueKey('addNoteTitle'))
    await this.driver.elementClick(byValueKey('addNoteTitle'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'Updated ' + valuee)
    await this.driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await this.driver.elementClick(byValueKey('sendButton'))
    console.log('Note14')
    /* await this.driver.execute('flutter:waitForTappable', byValueKey('addImages'))
    await this.driver.elementClick(byValueKey('addImages'))

    await this.driver.switchContext('NATIVE_APP')
    const el10 = await this.driver.$('//android.widget.FrameLayout[@content-desc=\'Photo taken on 19 Apr 2023, 6:26:44 am\']/androidx.cardview.widget.CardView/android.widget.FrameLayout/android.widget.ImageView')
    await el10.click()
    const el11 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout[2]/android.widget.Button[2]')
    await el11.click()
    const el12 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.view.View[3]/android.widget.ImageView[1]')
    await el12.click()
    const el13 = await this.driver.$('//android.widget.FrameLayout[@content-desc=\'Photo taken on 18 Apr 2023, 6:39:25 am\']/androidx.cardview.widget.CardView/android.widget.FrameLayout/android.widget.FrameLayout')
    await el13.click()
    const el14 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.view.ViewGroup/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout[2]/android.widget.Button[2]')
    await el14.click()

    await this.driver.switchContext('FLUTTER') */
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    console.log('Note16')
    const value1 = 'Updated ' + valuee
    // Edit Notes

    console.log('Note17')
    await this.driver.execute('flutter:scrollIntoView', byValueKey('noteList'), { alignment: 0.5 })
    console.log('Note18')
    // const elem = byText(valuee) // here if multiple entries with same name --conflict
    // assert.strictEqual(await this.driver.getElementText(elem), valuee)
    // console.log('Success')
    await this.driver.execute('flutter:waitForTappable', byText(value1))
    console.log('Note19')
    await this.driver.elementClick(byText(value1))
    console.log('Note20')
    // await this.driver.execute('flutter:waitFor', byValueKey('trashIcon'), 8000)
    await this.driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
    await this.driver.elementClick(byValueKey('trashIcon'))
    await this.driver.execute('flutter:waitForTappable', byText('Cancel'))
    await this.driver.elementClick(byText('Cancel'))

    // await this.driver.execute('flutter:waitFor', byValueKey('trashIcon'), 8000)
    await this.driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
    await this.driver.elementClick(byValueKey('trashIcon'))
    await this.driver.execute('flutter:waitForTappable', byText('Delete'))
    await this.driver.elementClick(byText('Delete'))
    await this.driver.elementClick(byValueKey('todayToggleButton'))
    console.log('today_notes done')
    await waitFor(3000)
  }
}

// Working OK
// selectreminderTodoreminderhrSpinnerreminderminSpinnertogRemindersadddescriptionTilequillEditorbolditalicsunderline
// bulletStylenumberingStylesendDescriptionclosetitleBaraddHashtagaddorsearchHashtagaddTextaddImagesmoodSelector
// addmoreImages
