/* eslint-disable @typescript-eslint/no-misused-promises */
import { remote } from 'webdriverio'
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates, @typescript-eslint/no-unused-vars
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
// eslint-disable-next-line @typescript-eslint/no-unused-vars, import/no-duplicates
// import { byText } from 'appium-flutter-finder'
// eslint-disable-next-line import/no-duplicates
// import { byTooltip } from 'appium-flutter-finder'
// eslint-disable-next-line import/no-duplicates
// import { byType } from 'appium-flutter-finder'
// import fs from 'fs'

const osSpecificOps = {
  platformName: 'Android',
  'appium:deviceName': 'RZ8T50EC7NW',
  // @todo support non-unix style path
  // 'appium:app': 'D:/projects/RealtimeInnovations/test_appium/build/app/outputs/flutter-apk/app-debug.apk'
  'appium:app': 'E:/appium/app-profile.apk'
}

const opts = {
  port: 4723,
  capabilities: {
    ...osSpecificOps,
    // 'appium:path': '/wd/hub',
    'appium:automationName': 'Flutter',
    'appium:retryBackoffTime': 500
  }
}

void (async () => {
  const driver = await remote(opts)
  const tf = byValueKey('loginForm_emailInput_textField')
  const tf1 = byValueKey('loginForm_passwordInput_textField')
  const hidebtn = byValueKey('eye') // to reveal password
  const hidebtn1 = byValueKey('eye1') // to hide back password
  console.log('EMAIL1')
  await driver.elementClick(tf)
  console.log('EMAIL2')

  await driver.elementSendKeys(tf, '<EMAIL>')
  await driver.elementClick(tf1)
  await driver.elementSendKeys(tf1, 'Mevolve@123')
  await driver.elementClick(hidebtn)

  await driver.elementClick(hidebtn1)
  const logbut = byValueKey('loginForm_continue_raisedButton')
  await driver.elementClick(logbut)
  await driver.elementClick(byText('Insight'))

  setTimeout(async () => {
    await driver.elementClick(byText('Habit'))

    setTimeout(async () => {
      await driver.elementClick(byValueKey('selectdaterange_habit'))
      await driver.elementClick(byValueKey('close'))
      setTimeout(async () => {
        await driver.elementClick(byText('Show in Number'))
        setTimeout(async () => {
          await driver.elementClick(byValueKey('selecthabits'))
          setTimeout(async () => {
            await driver.elementClick(byValueKey('close'))
            console.log('seelct habit')
            // eslint-disable-next-line @typescript-eslint/no-misused-promises
            setTimeout(async () => {
              // await driver.elementClick(byText('Show in Percentage'))
              console.log('All DONE')
              void driver.execute('flutter:scrollIntoView', byValueKey('insight_habit_scroll'), { alignment: 0.8 })
            }, 6000)
          }, 6000)
        }, 6000)
      }, 6000)
    }, 6000)
  }, 8000)
})()
