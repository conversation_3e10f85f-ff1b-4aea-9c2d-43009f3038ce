/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'
import { Key } from 'webdriverio'

export class FutureTodoFilter {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async futureTodoFilter (): Promise<void> {
    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
    const d = new Date()
    const currentMonth = months[d.getMonth()]
    console.log(currentMonth)
    const currentYear = new Date().getFullYear()
    console.log(currentYear)
    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    // Add Note
    const valuee = 'New Todo ' + currentDate
    await waitFor(2000)
    const fabutton = byTooltip('Increment')
    await this.driver.elementClick(fabutton)
    const addtodo = byText('Add Todo')
    await waitFor(1000)
    await this.driver.elementClick(addtodo)
    await waitFor(1000)
    await this.driver.elementClick(byValueKey('addtodotitle'))
    await waitFor(2000)
    const enttitle = byValueKey('textfield_todo')
    await this.driver.elementSendKeys(enttitle, valuee)
    await waitFor(1000)
    await this.driver.elementClick(byValueKey('addtitledone'))
    await waitFor(2000)
    await this.driver.elementClick(byValueKey('adddate'))
    await this.driver.elementClick(byText('OK'))
    await waitFor(1000)

    await this.driver.elementClick(byText('Future'))
    await this.driver.elementClick(byText('Todo'))
    await waitFor(5000)

    /* await this.driver.elementClick(byValueKey('saerchtext'))
    const element = byValueKey('searchtext')
    console.log(('KEY>BACKSPACE' + Key.Backspace))
    await this.driver.elementSendKeys(byValueKey('searchtext'), 'abcd' + (Key.Backspace))
    console.log((Key.Backspace)) */
    // const actions = this.driver.actions()

    // send keys to the element
    // await actions.sendKeys(element, 'Lorem ipsum dolor sit amet').perform()

    // delete the last character
    // await actions.keyDown(Key.BACK_SPACE).keyUp(Key.BACK_SPACE).perform()
    // await this.driver.elementSendKeys(byValueKey('searchtext'), 'Lorem ipsum dolor sit amet ')
    // await this.driver.elementSendKeys(byValueKey('searchtext'), (Key.Backspace))
    // await this.driver.elementSendKeys(byValueKey('searchtext'), (Key.Return))
    // await this.driver.elementSendKeys(byValueKey('searchtext'), )
    /* await this.driver.execute('flutter:setTextEntryEmulation', false)
    await this.driver.execute('flutter:getVMInfo')
    await this.driver.execute('flutter:getIsolate')
    // await this.driver.elementSendKeys(byValueKey('searchtext'), 'Search')
    await waitFor(2000)
    // await this.driver.switchContext('NATIVE_APP')
    // await this.driver.pressKeyCode(10)
    // await this.driver.keys(Key.Enter)
    // await this.driver.execute("document.activeElement.dispatchEvent(new KeyboardEvent('keydown', {'key': 'Enter'}));")
    // await this.driver.keys('\uE007')
    await waitFor(2000)
    // const textField = byValueKey('searchtext')
    // await this.driver.elementSendKeys(byValueKey('searchtext'), 'Key.RETURN')
    console.log('KEY PRESSED')
    await this.driver.execute('flutter:setTextEntryEmulation', true) */
    // await this.driver.switchContext('FLUTTER')
    await this.driver.elementClick(byValueKey('dateselector'))
    await waitFor(2000)
    await this.driver.elementClick(byText('Last 7 Days'))

    await waitFor(2500)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('pastpagefilters_note'), { item: byValueKey('clearall'), dxScroll: -200, dyScroll: 0 }) // horizontal scroll done
    await waitFor(2500)
    await this.driver.elementClick(byValueKey('clearall'))
    await waitFor(3000)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('futuretodolist'), { item: byText(valuee), dxScroll: -150, dyScroll: 0 }) // horizontal scroll done
    await waitFor(2000)
    await this.driver.elementClick(byValueKey('deleteentry'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Delete'))
    console.log('CLEAR ALL')
  }
}
