/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */

import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates, @typescript-eslint/no-unused-vars
import { byValueKey, byType, byText, byTooltip } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'

export class TodoToday {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async testTodoToday (): Promise<void> {
    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
    const d = new Date()
    const currentMonth = months[d.getMonth()]
    console.log(currentMonth)
    const currentYear = new Date().getFullYear()
    console.log(currentYear)
    const valuee1 = currentMonth + ' ' + currentYear
    console.log(valuee1)

    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    // Add Todo
    const valuee = 'New Todo_1 ' + currentDate
    const fabutton = byTooltip('increment')
    await this.driver.execute('flutter:waitForTappable', fabutton)
    await this.driver.elementClick(fabutton)

    const addtodo = byText('Add Todo')
    await this.driver.execute('flutter:waitForTappable', addtodo)
    await this.driver.elementClick(addtodo)
    await this.driver.execute('flutter:waitForTappable', byValueKey('addTitle'))
    await this.driver.elementClick(byValueKey('addTitle'))

    // await this.driver.execute('flutter:setTextEntryEmulation', false)
    // await waitFor(2000)
    // await this.driver.pressKeyCode(4)
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), valuee)
    await this.driver.execute('flutter:waitForTappable', byValueKey('sendButton'))

    await this.driver.elementClick(byValueKey('sendButton'))

    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))

    // Edit Todo
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('toDoLists'), { item: byText('Todos'), dxScroll: 0, dyScroll: 500 })
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    await this.driver.switchContext('NATIVE_APP')
    const el3 = await this.driver.$(`//android.view.View[@content-desc="${valuee}"]/android.view.View`)
    await el3.click()
    await this.driver.switchContext('FLUTTER')
    assert.strictEqual(await this.driver.getElementText(elem), valuee)
    console.log('Success')
    await this.driver.elementClick(byText(valuee))
    await this.driver.execute('flutter:waitForTappable', byValueKey('addTime'))
    await this.driver.elementClick(byValueKey('addTime')) // toggleTime-for turnoff time
    await this.driver.execute('flutter:waitFor', byValueKey('timeHourSpinner'), 2000)
    await this.driver.execute('flutter:scroll', byValueKey('timeHourSpinner'), { dx: 0, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitFor', byValueKey('timeMinSpinner'), 2000)
    await this.driver.execute('flutter:scroll', byValueKey('timeMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitFor', byValueKey('selectionAmPm'), 2000)
    await this.driver.execute('flutter:scroll', byValueKey('selectionAmPm'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set'))
    /* await this.driver.execute('flutter:waitForTappable', byValueKey('selectReminderToDo'))
    await this.driver.elementClick(byValueKey('selectReminder')) // toggleTime-for turnoff time.reminderHourSpinner
    await this.driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set')) */

    await this.driver.execute('flutter:waitForTappable', byValueKey('addDate'))
    await this.driver.elementClick(byValueKey('addDate'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('monthAndYear'))
    await this.driver.elementClick(byValueKey('monthAndYear'))
    await this.driver.execute('flutter:waitFor', byValueKey('yearList'), 2000)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('yearList'), { item: byText('2028'), dxScroll: -50, dyScroll: 0 }) // horizontal scroll done
    await this.driver.execute('flutter:waitForTappable', byText('2028'))
    await this.driver.elementClick(byText('2028'))
    await this.driver.execute('flutter:waitForTappable', byText('Sep'))
    await this.driver.elementClick(byText('Sep'))
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set'))
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save')) // here not selected any date in sep 2028, so saving with today's date
    await this.driver.execute('flutter:waitForTappable', byValueKey('repeatTodo'))
    await this.driver.elementClick(byValueKey('repeatTodo'))

    // if repeat option is Weekly
    /* await this.driver.elementClick(byText('Weekly'))
    await waitFor(1000)
    await this.driver.switchContext('NATIVE_APP')
    const el17 = await this.driver.$('(//android.view.View[@content-desc="S"])[2]')
    await el17.click()
    const el18 = await this.driver.$('(//android.view.View[@content-desc="S"])[1]')
    await el18.click()
    const el19 = await this.driver.$('(//android.view.View[@content-desc="T"])[1]')
    await el19.click()
    const el20 = await this.driver.$('(//android.view.View[@content-desc="T"])[2]')
    await el20.click()
    await this.driver.switchContext('FLUTTER')
    await waitFor(2000) */
    // if repeat option is Yearly
    /* await this.driver.elementClick(byText('Yearly'))
    await waitFor(1500)
    await this.driver.execute('flutter:scroll', byValueKey('yearlyMonthSpinner'), { dx: 50, dy: -100, durationMilliseconds: 200, frequency: 70 })
    await waitFor(1500)
    await this.driver.execute('flutter:scroll', byValueKey('yearlyDateSpinner'), { dx: 50, dy: -100, durationMilliseconds: 200, frequency: 20 })
    await waitFor(1500) */
    // if repeat option is Monthly
    await this.driver.execute('flutter:waitForTappable', byText('Monthly'))
    await this.driver.elementClick(byText('Monthly')) // repeatMonthDaySpinner
    await this.driver.execute('flutter:waitForTappable', byValueKey('daySpinner'))
    await this.driver.execute('flutter:scroll', byValueKey('daySpinner'), { dx: 50, dy: -100, durationMilliseconds: 200, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
    await this.driver.elementClick(byValueKey('trashIcon'))
    await this.driver.execute('flutter:waitForTappable', byText('Cancel'))
    await this.driver.elementClick(byText('Cancel'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('trashIcon'))
    await this.driver.elementClick(byValueKey('trashIcon'))
    await this.driver.execute('flutter:waitForTappable', byText('Delete'))
    await this.driver.elementClick(byText('Delete'))

    console.log('today_todo done')
    await waitFor(2000)
  }
}

// Working OK
// selectreminderTodo;reminderhrSpinner;reminderminSpinner;togReminders;addDescriptionTile;quillEditor;bold;italics;underline;
// bulletStyle;numberingStyle;sendDescription;closetitleBar;addHashTag;addSearchHashTag;addText;addImages;moodSelector;
// addMoreImages
