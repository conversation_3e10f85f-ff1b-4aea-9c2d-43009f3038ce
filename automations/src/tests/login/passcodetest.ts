/* eslint-disable @typescript-eslint/no-unused-vars */
import { byText, byV<PERSON><PERSON><PERSON><PERSON> } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'

export class TestPasscodeFlow {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async passcodeTestFlow (): Promise<void> {
    await this.driver.elementClick(byText('5'))
    await waitFor(1000)
    await this.driver.elementClick(byText('5'))
    await waitFor(1000)
    await this.driver.elementClick(byText('5'))
    await waitFor(1000)
    await this.driver.elementClick(byText('5'))
    await waitFor(1000)
  }
}
