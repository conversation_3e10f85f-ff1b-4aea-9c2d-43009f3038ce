/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-unused-vars */
import { byText, byValue<PERSON><PERSON> } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'

export class TestLoginFlow {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async testEmailPasswordLogin (): Promise<void> {
    console.log('Continue with google')
    await this.driver.elementClick(byValue<PERSON>ey('continueWithGoogle'))
    try {
      await this.driver.execute('flutter:waitFor', byText('Past'), 8000)
      console.log('qwerty')
    } catch (warn) {
      const currentcontext = await this.driver.getContext()
      await this.driver.switchContext('NATIVE_APP')
      const el6 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.LinearLayout/android.support.v7.widget.RecyclerView/android.widget.LinearLayout[1]/android.widget.LinearLayout')
      await el6.click()
      await this.driver.switchContext('FLUTTER')
      await waitFor(5000)
    }
    await this.driver.elementClick(byValueKey('todayToggleButton'))

    console.log('login done')

    // Add assert statements to verify test cases.
  }

  async testForgotPassword (): Promise<void> {
    // TODO
  }
}
