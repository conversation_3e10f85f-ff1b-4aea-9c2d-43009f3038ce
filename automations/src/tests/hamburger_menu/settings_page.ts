/* eslint-disable @typescript-eslint/no-misused-promises */
import { byText, byV<PERSON><PERSON><PERSON><PERSON> } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'

export class SettingsPage {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async testSettingsPage (): Promise<void> {
    await waitFor(1000)
    await this.driver.elementClick(byValue<PERSON>ey('hmenu1'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Settings'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Todo'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Habit'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Journal'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Note')) // Check for warning message
    await waitFor(1000)
    await this.driver.elementClick(byText('Todo'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Habit'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Journal'))

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    await waitFor(1000)
    await this.driver.elementClick(byText('Dark mode'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Dark mode'))
    await this.driver.elementClick(byValueKey('close_settings'))

  // await this.driver.deleteSession()
  } // other than theme color all done
}
