/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../../common/utils.js'

export class PastHabitFilter {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async pastHabitFilter (): Promise<void> {
    await this.driver.elementClick(byText('Past'))
    await this.driver.elementClick(byText('Habit'))
    await waitFor(2000)
    await this.driver.elementClick(byValueKey('dateselector'))
    await waitFor(5000)
    await this.driver.elementClick(byText('Last 7 Days'))
    await waitFor(5000)
    await this.driver.elementClick(byV<PERSON><PERSON><PERSON><PERSON>('habitselector'))

    await waitFor(5000)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('selecthabit_past'), { item: byText('New Habit'), dxScroll: 0, dyScroll: -760 })
    // await this.driver.execute('flutter:scrollIntoView', byValueKey('selecthabit_past'), { alignment: 8 })
    await waitFor(2000)
    await this.driver.elementClick(byText('New Habit'))
    // await this.driver.elementClick(byText('Last 7 days'))
    // await this.driver.elementClick(byTooltip('delete'))
    // await this.driver.elementClick(byValueKey('close'))
    /* await this.driver.elementClick(byValueKey('hashtagselector'))
    await this.driver.elementClick(byValueKey('closeinpast'))

    await this.driver.elementClick(byValueKey('moodselector'))
    await this.driver.elementClick(byText('Meh'))
    await this.driver.elementClick(byText('Apply')) */
    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    await waitFor(6000)
    await this.driver.elementClick(byValueKey('resetbutton'))
    await waitFor(5000)

    await this.driver.elementClick(byText('New Habit'))
    await this.driver.elementClick(byText('Apply'))
    await waitFor(5000)
    // const elem = byValueKey('pastpagefilters_habit')
    // await this.driver.execute('flutter:scrollIntoView', elem, { alignment: 1 })
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('pastpagefilters_habit'), { item: byText('Clear All'), dxScroll: -200, dyScroll: 0 }) // horizontal scroll done
    await waitFor(3000)
    await this.driver.elementClick(byText('Clear All'))
    await waitFor(3000)
    await this.driver.elementClick(byText('Clear'))
    await waitFor(6000)
    await this.driver.elementClick(byText('Note')) // key: const ValueKey('searchtext'),
    await waitFor(6000)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 0, dyScroll: -1000 })
    // await this.driver.execute('flutter:scrollIntoView', byValueKey('pasttodo_listview'), { alignment: 1 })
    await this.driver.elementClick(byText('poiuy'))
    // await this.driver.elementClick(byValueKey('statusselector'))
    //  await this.driver.elementClick(byText('Clear All'))
    await waitFor(2000)
    await this.driver.elementClick(byText('Cancel'))
    await waitFor(2000)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 100, dyScroll: 0 })
    await this.driver.elementClick(byText('Today'))
    await waitFor(2000)
    console.log('CLEAR ALL')
  }
}
