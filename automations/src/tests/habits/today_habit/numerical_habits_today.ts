/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../../common/utils.js'

export class NumericalHabitsToday {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async testNumericalHabitsToday (): Promise<void> {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'How much distance you walked? ' + currentDate
    await waitFor(3000)
    const fabutton = byTooltip('increment')
    await this.driver.execute('flutter:waitForTappable', fabutton)
    await this.driver.elementClick(fabutton)

    const setuphabit = byText('Setup Habit')
    await this.driver.execute('flutter:waitForTappable', setuphabit)
    await this.driver.elementClick(setuphabit)
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectHabitType'))
    await this.driver.elementClick(byValueKey('selectHabitType'))
    await this.driver.execute('flutter:waitForTappable', byText('Numerical Value'))
    await this.driver.elementClick(byText('Numerical Value'))

    await this.driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await this.driver.elementClick(byValueKey('addHabitTitle'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), valuee)
    await this.driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await this.driver.elementClick(byValueKey('sendButton'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('setGoal'))
    await this.driver.elementClick(byValueKey('setGoal'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), '5')
    await this.driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await this.driver.elementClick(byValueKey('sendButton'))

    await this.driver.execute('flutter:waitForTappable', byValueKey('setUnit'))
    await this.driver.elementClick(byValueKey('setUnit'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'kms')
    await this.driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await this.driver.elementClick(byValueKey('sendButton'))

    await this.driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await this.driver.elementClick(byValueKey('selectHabitDateRange'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await this.driver.elementClick(byValueKey('startDate')) // Today;Next Saturday;Next Sunday;After 1 week;
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save')) // Today is selected
    await this.driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await this.driver.elementClick(byValueKey('endDate')) // Never ends;15 days later;30 days later;60 days later
    /* await this.driver.execute('flutter:waitForTappable', byText('Never ends'))
    await this.driver.elementClick(byText('Never ends')) */
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectHabitRepeat'))
    await this.driver.elementClick(byValueKey('selectHabitRepeat'))
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save')) // if need specific days need to use alphabets and for Tuesday;Thursday and Saturday;Sunday need appium code
    await this.driver.execute('flutter:waitForTappable', byValueKey('addTime'))
    await this.driver.elementClick(byValueKey('addTime'))

    await this.driver.execute('flutter:scroll', byValueKey('timeHourSpinner'), { dx: 0, dy: 1000, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:scroll', byValueKey('timeMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:scroll', byValueKey('selectionAmPm'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set'))

    /* await this.driver.elementClick(byValueKey('sethabitReminder'))
    await waitFor(2000)
    await this.driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await waitFor(2000)
    await this.driver.elementClick(byText('Set'))
    await waitFor(2000) */
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    // await this.driver.elementClick(byValueKey('todaytoggleButton'))
    // await waitFor(2000)
    await this.driver.elementClick(byValueKey('todayToggleButton'))
    await this.driver.execute('flutter:scrollIntoView', byValueKey('habitList'), { alignment: 1 })

    const elem = byText(valuee) // here if multiple entries with same name --conflict
    assert.strictEqual(await this.driver.getElementText(elem), valuee)
    console.log('Success')
    await this.driver.execute('flutter:waitForTappable', byText(valuee))
    await this.driver.elementClick(byText(valuee))
    await this.driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await this.driver.elementClick(byValueKey('showHabitBottomSheet'))
    await this.driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    await this.driver.elementClick(byText('Edit habit setup'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))

    await this.driver.elementClick(byValueKey('deleteButton'))
    await this.driver.execute('flutter:waitForTappable', byText('Delete'))
    await this.driver.elementClick(byText('Delete'))
    await waitFor(5000)
    await this.driver.elementClick(byValueKey('todayToggleButton'))
    // await this.driver.elementClick(byValueKey('todayToggleButton'))
  }
}
// habitList;todoList;journalList;noteList;todayList;overdueList;unscheduledList;status
