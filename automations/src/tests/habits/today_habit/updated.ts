// key: const <PERSON><PERSON><PERSON>('selecthabittype')key: const <PERSON><PERSON><PERSON>('addhabittitle'), const <PERSON><PERSON><PERSON>('textfield_bottomsheet'),   key: const <PERSON><PERSON><PERSON>('addtitledone'),key: const <PERSON><PERSON><PERSON>('selecthabitdaterange'),key: const <PERSON><PERSON><PERSON>('startdate'),Today;Next Saturday;Next Sunday;After 1 week;key: const <PERSON><PERSON><PERSON>('enddate'),Never ends;15 days later;30 days later;60 days laterkey: const <PERSON><PERSON><PERSON>('selecthabitrepeat'),key: const <PERSON><PERSON><PERSON>('selecthabittime'),key: const <PERSON><PERSON><PERSON>('sethabitreminder'),key: const <PERSON><PERSON><PERSON>('addhashtagtodo'),

// key: const <PERSON><PERSON><PERSON>('addhabittitle'),key: const <PERSON><PERSON><PERSON>('setgoal'),key: const <PERSON><PERSON><PERSON>('adddetails'),key: const <PERSON><PERSON><PERSON>('senddetails'),key: const <PERSON><PERSON><PERSON>('setunit'),key: const <PERSON><PERSON><PERSON>('adddetails'), key: const <PERSON><PERSON><PERSON>('senddetails'),
// key: const <PERSON><PERSON><PERSON>('addoptions'),+ key: const <PERSON><PERSON><PERSON>('adddetails'),key: const <PERSON><PERSON><PERSON>('senddetails'),
// key: const <PERSON><PERSON><PERSON>('deletebutton')key: const <PERSON><PERSON>ey('closebutton'),key: const ValueKey('selectjournaloption'),key: const ValueKey('closetitlebar'),
