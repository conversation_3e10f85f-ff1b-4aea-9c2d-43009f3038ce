/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../../common/utils.js'

export class MultiChoiceHabitsToday {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async testMultiChoiceHabitsToday (): Promise<void> {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'Food you had today' + currentDate
    await waitFor(3000)
    const fabutton = byTooltip('increment')
    await this.driver.execute('flutter:waitFor', fabutton, 5000)
    await this.driver.elementClick(fabutton)

    const setuphabit = byText('Setup Habit')
    await this.driver.execute('flutter:waitForTappable', setuphabit)
    await this.driver.elementClick(setuphabit)
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectHabitType'))
    await this.driver.elementClick(byValueKey('selectHabitType'))
    await this.driver.execute('flutter:waitForTappable', byText('Multi Choice'))
    await this.driver.elementClick(byText('Multi Choice'))

    await this.driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await this.driver.elementClick(byValueKey('addHabitTitle'))
    await this.driver.switchContext('NATIVE_APP')
    const el38 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el38.setValue(valuee)
    const el39 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el39.click()
    const el40 = await this.driver.$('~Add Option')
    await el40.click()
    const el41 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el41.setValue('Option1')
    const el42 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el42.click()
    const el43 = await this.driver.$('~Add Option')
    await el43.click()
    const el44 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el44.setValue('Option2')
    const el45 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el45.click()
    const el46 = await this.driver.$('~Add Option')
    await el46.click()
    const el47 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el47.click()
    await el47.setValue('Option3')
    const el48 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el48.click()
    await this.driver.switchContext('FLUTTER')
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await this.driver.elementClick(byValueKey('selectHabitDateRange'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await this.driver.elementClick(byValueKey('startDate')) // TodayNext SaturdayNext SundayAfter 1 week
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save')) // Today is selected
    await this.driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await this.driver.elementClick(byValueKey('endDate')) // Never ends15 days later30 days later60 days later
    /* await this.driver.execute('flutter:waitForTappable', byText('Never ends'))
    await this.driver.elementClick(byText('Never ends')) */
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set'))

    /* await this.driver.execute('flutter:waitForTappable', byValueKey('addOption'))
    await this.driver.elementClick(byValueKey('addOption'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'Chappathi')
    await this.driver.execute('flutter:waitForTappable', byValueKey('addTitleDone'))
    await this.driver.elementClick(byValueKey('addTitleDone'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('addOption'))
    await this.driver.elementClick(byValueKey('addOption'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'Dosa')
    await this.driver.execute('flutter:waitForTappable', byValueKey('addTitleDone'))
    await this.driver.elementClick(byValueKey('addTitleDone')) */

    await this.driver.execute('flutter:waitForTappable', byValueKey('selectHabitRepeat'))
    await this.driver.elementClick(byValueKey('selectHabitRepeat'))
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save')) // if need specific days need to use alphabets and for TuesdayThursday and SaturdaySunday need appium code

    await this.driver.execute('flutter:waitForTappable', byValueKey('addTime'))
    await this.driver.elementClick(byValueKey('addTime'))

    await this.driver.execute('flutter:waitForTappable', byValueKey('timeHourSpinner'), 2000)
    await this.driver.execute('flutter:scroll', byValueKey('timeHourSpinner'), { dx: 0, dy: -100, durationMilliseconds: 500, frequency: 10 })
    await this.driver.execute('flutter:waitForTappable', byValueKey('timeMinSpinner'), 2000)
    await this.driver.execute('flutter:scroll', byValueKey('timeMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectionAmPm'), 2000)
    await this.driver.execute('flutter:scroll', byValueKey('selectionAmPm'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set'))

    /* await this.driver.elementClick(byValueKey('sethabitReminder'))
    await waitFor(2000)
    await this.driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await waitFor(2000)
    await this.driver.elementClick(byText('Set'))
    await waitFor(2000) */
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.elementClick(byValueKey('todayToggleButton'))
    await waitFor(2000)
    // await this.driver.execute('flutter:scrollIntoView', byValueKey('habitList'), { alignment: 1 })
    await this.driver.elementClick(byValueKey('todayToggleButton'))
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    assert.strictEqual(await this.driver.getElementText(elem), valuee)
    console.log('Success')
    await this.driver.execute('flutter:waitForTappable', byText(valuee))
    await this.driver.elementClick(byText(valuee))
    await this.driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await this.driver.elementClick(byValueKey('showHabitBottomSheet'))
    await this.driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    await this.driver.elementClick(byText('Edit habit setup'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))

    await this.driver.elementClick(byValueKey('deleteButton'))
    await this.driver.execute('flutter:waitForTappable', byText('Delete'))
    await this.driver.elementClick(byText('Delete'))
    // await this.driver.elementClick(byValueKey('todayToggleButton'))
    // await this.driver.elementClick(byValueKey('todayToggleButton'))
    await waitFor(3000)
  }
}
// habitListtodoListjournalListnoteListtodayListoverdueListunscheduledListstatus
