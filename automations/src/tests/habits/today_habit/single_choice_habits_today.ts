/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../../common/utils.js'

export class SingleChoiceHabitsToday {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async testSingleChoiceHabitsToday (): Promise<void> {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'Your favotite color ' + currentDate
    await waitFor(3000)
    const fabutton = byTooltip('increment')
    await this.driver.execute('flutter:waitForTappable', fabutton)
    await this.driver.elementClick(fabutton)

    const setuphabit = byText('Setup Habit')
    await this.driver.execute('flutter:waitForTappable', setuphabit)
    await this.driver.elementClick(setuphabit)
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectHabitType'))
    await this.driver.elementClick(byValueKey('selectHabitType'))
    await this.driver.execute('flutter:waitForTappable', byText('Single Choice'))
    await this.driver.elementClick(byText('Single Choice'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('addHabitTitle'))
    await this.driver.elementClick(byValueKey('addHabitTitle'))
    await this.driver.switchContext('NATIVE_APP')
    const el10 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')

    await el10.setValue(valuee)
    const el11 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el11.click()
    const el12 = await this.driver.$('~Add Option')
    await el12.click()
    const el13 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el13.click()
    await el13.setValue('Opt1')
    const el14 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el14.click()
    const el15 = await this.driver.$('~Add Option')
    await el15.click()
    const el16 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el16.setValue('Opt2')
    const el17 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el17.click()
    const el18 = await this.driver.$('~Add Option')
    await el18.click()
    const el19 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.ImageView')
    await el19.setValue('Opt3')
    const el20 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[1]/android.view.View/android.widget.EditText/android.widget.ImageView')
    await el20.click()
    await this.driver.switchContext('FLUTTER')
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectHabitDateRange'))
    await this.driver.elementClick(byValueKey('selectHabitDateRange'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await this.driver.elementClick(byValueKey('startDate')) // TodayNext SaturdayNext SundayAfter 1 week
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save')) // Today is selected
    await this.driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await this.driver.elementClick(byValueKey('endDate')) // Never ends15 days later30 days later60 days later
    /* await this.driver.execute('flutter:waitForTappable', byText('Never ends'))
    await this.driver.elementClick(byText('Never ends')) */
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set'))

    await this.driver.execute('flutter:waitForTappable', byValueKey('selectHabitRepeat'))
    await this.driver.elementClick(byValueKey('selectHabitRepeat'))
    await this.driver.switchContext('NATIVE_APP')
    const el29 = await this.driver.$('(//android.view.View[@content-desc=\'S\'])[2]')
    await el29.click()
    const el30 = await this.driver.$('(//android.view.View[@content-desc=\'S\'])[1]')
    await el30.click()
    await this.driver.switchContext('FLUTTER')
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))// if need specific days need to use alphabets and for TuesdayThursday and SaturdaySunday need appium code

    await this.driver.execute('flutter:waitForTappable', byValueKey('addTime'))
    await this.driver.elementClick(byValueKey('addTime'))
    /* await this.driver.execute('flutter:waitForTappable', byValueKey('timeHourSpinner'), 2000)
    await this.driver.execute('flutter:scroll', byValueKey('timeHourSpinner'), { dx: 0, dy: 1000, durationMilliseconds: 500, frequency: 10 })
    await this.driver.execute('flutter:waitForTappable', byValueKey('timeMinSpinner'), 2000)
    await this.driver.execute('flutter:scroll', byValueKey('timeMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectionAmPm'), 2000)
    await this.driver.execute('flutter:scroll', byValueKey('selectionAmPm'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 }) */
    /* await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set')) */

    /* await this.driver.elementClick(byValueKey('sethabitReminder'))
    await waitFor(2000)
    await this.driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await waitFor(2000) */
    await this.driver.elementClick(byText('Set'))
    await waitFor(2000)
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.elementClick(byValueKey('todayToggleButton'))
    await waitFor(2000)
    // await this.driver.execute('flutter:scrollIntoView', byValueKey('habitList'), { alignment: 1 })
    // await this.driver.elementClick(byValueKey('todayToggleButton'))
    // await this.driver.execute('flutter:waitForTappable', byText('Future'))
    // await this.driver.elementClick(byText('Future'))
    // await this.driver.execute('flutter:waitForTappable', byText('Habit Setup'))
    // await this.driver.elementClick(byText('Habit Setup'))
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    assert.strictEqual(await this.driver.getElementText(elem), valuee)
    console.log('Success')
    await this.driver.execute('flutter:waitForTappable', byText(valuee))
    await this.driver.elementClick(byText(valuee))
    await this.driver.execute('flutter:waitForTappable', byValueKey('showHabitBottomSheet'))
    await this.driver.elementClick(byValueKey('showHabitBottomSheet'))
    await this.driver.execute('flutter:waitForTappable', byText('Edit habit setup'))
    await this.driver.elementClick(byText('Edit habit setup'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))

    await this.driver.elementClick(byValueKey('deleteButton'))
    await this.driver.execute('flutter:waitForTappable', byText('Delete'))
    await this.driver.elementClick(byText('Delete'))
    // await this.driver.elementClick(byValueKey('todayToggleButton'))
    // await this.driver.elementClick(byValueKey('todayToggleButton'))
    await waitFor(3000)
  }
}
// habitListtodoListjournalListnoteListtodayListoverdueListunscheduledListstatus
