/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../../common/utils.js'

export class FutureHabitPage {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async FutureHabitPage (): Promise<void> {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    // const valuee = 'Your favotite color ' + currentDate
    const valuee = 'Are you happy today? ' + currentDate
    await waitFor(2000)
    const fabutton = byTooltip('Increment')
    await this.driver.elementClick(fabutton)
    const setuphabit = byText('Setup Habit')
    await this.driver.elementClick(setuphabit)
    /* const num = byText('Yes or No')
    await this.driver.elementClick(num)
    const singch = byText('Single Choice')
    await this.driver.elementClick(singch) */
    await this.driver.elementSendKeys(byType('TextField'), valuee)
    // const hab_qstion=byText('Habit question *')
    /* const habqstion = byValueKey('habitquestion')
    console.log('HABIT QUESTION')
    await this.driver.elementClick(habqstion)
    await this.driver.elementSendKeys(habqstion, valuee)
    const options = byValueKey('options')
    console.log('Single Choice HABIT')

    await this.driver.elementSendKeys(options, 'White')
    await this.driver.elementClick(byValueKey('addchoice'))
    await this.driver.elementSendKeys(options, 'Black')
    await this.driver.elementClick(byValueKey('addchoice'))
    await this.driver.elementSendKeys(options, 'Pink')
    await this.driver.elementClick(byValueKey('addchoice')) */

    await this.driver.elementClick(byText('Start Date'))
    // Entering date in text field
    await this.driver.elementClick(byTooltip('Switch to input'))
    await this.driver.elementClick(byTooltip('Switch to calendar'))
    await this.driver.elementClick(byTooltip('Switch to input'))
    await this.driver.elementClear(byType('TextField'))
    await this.driver.elementSendKeys(byType('TextField'), '8/30/2023')
    // await this.driver.elementClear(byType('TextField'))
    // await this.driver.elementSendKeys(byType('TextField'),'2/23/2023')
    // By changing month and selecting a date
    /* await this.driver.elementClick(byTooltip('Previous month'))
      await this.driver.elementClick(byTooltip('Next month'))
      await this.driver.elementClick(byTooltip('Next month'))

      await this.driver.elementClick(byText('February 2023'))   // label given as Select year instead of tooltip
      await this.driver.elementClick(byText('2025'))
      await this.driver.elementClick(byText('24')) */
    await this.driver.elementClick(byText('OK'))
    await this.driver.elementClick(byText('End Date'))
    await this.driver.elementClick(byTooltip('Switch to input'))
    await this.driver.elementClear(byType('TextField'))
    await this.driver.elementSendKeys(byType('TextField'), '5/30/2024')
    await this.driver.elementClick(byText('OK'))
    await this.driver.elementClick(byText('No Time'))
    await this.driver.elementClick(byValueKey('toggle_time'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.elementClick(byText('No Reminder'))
    await this.driver.elementClick(byValueKey('tog_reminder'))
    await this.driver.elementClick(byText('Save'))

    await this.driver.elementClick(byText('Done'))
    await this.driver.elementClick(byText('Future'))
    await this.driver.elementClick(byText('Habit Setup'))
    await waitFor(10000)

    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('futurehabitlist'), { item: byText(valuee), dxScroll: 0, dyScroll: -1500 })
    const elem = byText(valuee)
    assert.strictEqual(await this.driver.getElementText(elem), valuee)
    console.log('Success')

    // await this.driver.execute('flutter:scrollIntoView', byValueKey('selecthabit_past'), { alignment: 8 })
    await waitFor(2000)
    await this.driver.elementClick(byText(valuee))
    await waitFor(2000)
    await this.driver.elementClick(byValueKey('deleteentry'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Delete'))
    console.log('future_habits done')

    console.log('CLEAR ALL')
  }
}
