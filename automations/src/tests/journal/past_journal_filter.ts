/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'

export class PastJournalFilter {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async pastJournalFilter (): Promise<void> {
    await this.driver.elementClick(byText('Past'))
    await this.driver.elementClick(byText('Journal'))
    await waitFor(2000)
    await this.driver.elementClick(byValueKey('dateselector'))
    await waitFor(5000)
    await this.driver.elementClick(byText('Last 7 Days'))
    await waitFor(5000)
    await this.driver.elementClick(by<PERSON><PERSON><PERSON><PERSON><PERSON>('journalselector'))

    await waitFor(5000)
    await this.driver.execute('flutter:scrollUntilVisible', byValue<PERSON>ey('selectjournal_past'), { item: byText('jrfuj'), dxScroll: 0, dyScroll: -600 })

    await waitFor(2000)
    await this.driver.elementClick(byText('jrfuj'))

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    await waitFor(6000)
    await this.driver.elementClick(byValueKey('resetbutton_journal'))
    await waitFor(5000)

    await this.driver.elementClick(byText('jrfuj'))
    await this.driver.elementClick(byText('Apply'))
    await waitFor(5000)
    await this.driver.elementClick(byValueKey('moodselector'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Meh'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Apply'))

    await waitFor(5000)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('pastpagefilters_journal'), { item: byValueKey('statusselector'), dxScroll: -150, dyScroll: 0 }) // horizontal scroll done
    await waitFor(5000)
    await this.driver.elementClick(byValueKey('statusselector'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Pending'))
    await waitFor(5000)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('pastpagefilters_journal'), { item: byText('Clear All'), dxScroll: -200, dyScroll: 0 }) // horizontal scroll done
    await waitFor(3000)
    await this.driver.elementClick(byText('Clear All'))
    await waitFor(3000)
    await this.driver.elementClick(byText('Clear'))
    await waitFor(6000)
    await this.driver.elementClick(byText('Note')) // key: const ValueKey('searchtext'),
    await waitFor(6000)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 0, dyScroll: -1000 })
    // await this.driver.execute('flutter:scrollIntoView', byValueKey('pasttodo_listview'), { alignment: 1 })
    await this.driver.elementClick(byText('poiuy'))
    await waitFor(2000)
    await this.driver.elementClick(byText('Cancel'))
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('past_note'), { item: byText('poiuy'), dxScroll: 100, dyScroll: 0 })
    await this.driver.elementClick(byText('Today'))
    // await this.driver.elementClick(byValueKey('statusselector'))
    //  await this.driver.elementClick(byText('Clear All'))
    console.log('CLEAR ALL')
  }
}
