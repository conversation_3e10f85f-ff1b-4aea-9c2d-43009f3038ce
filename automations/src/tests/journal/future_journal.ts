/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'

export class FutureJournalPage {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async futureJournalPage (): Promise<void> {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'New Journal_1 ' + currentDate
    await waitFor(2000)
    const fabutton = byTooltip('Increment')
    await this.driver.elementClick(fabutton)
    const setupjournal = byText('Setup Journal')
    await this.driver.elementClick(setupjournal)
    await this.driver.elementSendKeys(byValueKey('journal'), valuee)
    await this.driver.elementClick(byText('Start Date'))
    // Entering date in text field
    await this.driver.elementClick(byTooltip('Switch to input'))
    await this.driver.elementClick(byTooltip('Switch to calendar'))
    await this.driver.elementClick(byTooltip('Switch to input'))
    await this.driver.elementClear(byType('TextField'))
    await this.driver.elementSendKeys(byType('TextField'), '8/30/2023')

    await this.driver.elementClick(byText('OK'))
    await this.driver.elementClick(byText('End Date'))
    await this.driver.elementClick(byTooltip('Switch to input'))
    await this.driver.elementClear(byType('TextField'))
    await this.driver.elementSendKeys(byType('TextField'), '5/30/2024')
    await this.driver.elementClick(byText('OK'))
    await this.driver.elementClick(byText('No Time'))
    await this.driver.elementClick(byValueKey('toggle_time'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.elementClick(byText('No Reminder'))
    await this.driver.elementClick(byValueKey('tog_reminder'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.elementClick(byText('Done'))

    await waitFor(2000)
    await this.driver.elementClick(byText('Future'))
    await this.driver.elementClick(byText('Journal Setup'))
    await waitFor(2000)
    await waitFor(10000)

    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('futurejournallist'), { item: byText(valuee), dxScroll: 0, dyScroll: -500 })
    const elem = byText(valuee)
    assert.strictEqual(await this.driver.getElementText(elem), valuee)
    console.log('Success')

    // await this.driver.execute('flutter:scrollIntoView', byValueKey('selecthabit_past'), { alignment: 8 })
    await waitFor(2000)
    await this.driver.elementClick(byText(valuee))
    await waitFor(2000)
    await this.driver.elementClick(byValueKey('deleteentry'))
    await waitFor(1000)
    await this.driver.elementClick(byText('Delete'))
    console.log('future_habits done')

    console.log('CLEAR ALL')

    // eslint-disable-next-line @typescript-eslint/no-misused-promises
    console.log('CLEAR ALL')
  }
}
