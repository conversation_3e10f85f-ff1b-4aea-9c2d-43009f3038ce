/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */
import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates
import { byValueKey, byTooltip, byType, byText } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'

export class JournalToday {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async testJournalToday (): Promise<void> {
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()

    const valuee = 'Describe today ' + currentDate
    const fabutton = byTooltip('increment')
    await this.driver.execute('flutter:waitForTappable', fabutton)
    await this.driver.elementClick(fabutton)
    const setupjournal = byText('Setup Journal')
    await this.driver.execute('flutter:waitForTappable', setupjournal)
    await this.driver.elementClick(setupjournal)

    await this.driver.execute('flutter:waitForTappable', byValueKey('addJournalTitle'))
    // await this.driver.execute('flutter:waitFor', byValueKey('addjournalTitle'), 8000)
    await this.driver.elementClick(byValueKey('addJournalTitle'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), valuee)
    await this.driver.execute('flutter:waitForTappable', byValueKey('sendButton'))

    await this.driver.elementClick(byValueKey('sendButton'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectJournalDateRange'))

    await this.driver.elementClick(byValueKey('selectJournalDateRange'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('startDate'))
    await this.driver.elementClick(byValueKey('startDate'))
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('endDate'))
    await this.driver.elementClick(byValueKey('endDate'))
    /* await this.driver.execute('flutter:waitForTappable', byText('Never ends'))
    await this.driver.elementClick(byText('Never ends')) */
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectJournalRepeat'))
    await this.driver.elementClick(byValueKey('selectJournalRepeat'))
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    // Since conflict for S-Saturday S- Sunday T-Tuesday T-Thursday; taking code from appium inspector
    await this.driver.elementClick(byText('Save'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectJournalTime'))
    await this.driver.elementClick(byValueKey('selectJournalTime'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('timeHourSpinner'))
    // await this.driver.elementClick(byValueKey('toggle_time'))
    await this.driver.execute('flutter:scroll', byValueKey('timeHourSpinner'), { dx: 0, dy: 1000, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byValueKey('timeMinSpinner'))
    await this.driver.execute('flutter:scroll', byValueKey('timeMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byValueKey('selectionAmPm'))
    await this.driver.execute('flutter:scroll', byValueKey('selectionAmPm'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set'))
    /* await this.driver.execute('flutter:waitForTappable', byValueKey('setjournalReminder'))
    await this.driver.elementClick(byValueKey('setjournalReminder'))
    await this.driver.execute('flutter:scroll', byValueKey('reminderMinSpinner'), { dx: 50, dy: -100, durationMilliseconds: 1000, frequency: 80 })
    await this.driver.execute('flutter:waitForTappable', byText('Set'))
    await this.driver.elementClick(byText('Set')) */

    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    // await this.driver.elementClick(byValueKey('tog_reminder'))
    await this.driver.elementClick(byText('Save'))
    // journalList
    // await waitFor(10000)
    await this.driver.elementClick(byValueKey('todayToggleButton'))
    const elem = byText(valuee)
    await this.driver.execute('flutter:scrollUntilVisible', byValueKey('toDoLists'), { item: byText('Journals'), dxScroll: 0, dyScroll: 500 })
    await this.driver.execute('flutter:scrollIntoView', byValueKey('journalList'), { alignment: 1 })
    // await this.driver.execute('flutter:scrollUntilTapable', byValueKey('journalList'), { item: byText(valuee), dxScroll: 0, dyScroll: -400 })
    //  const elem = byText(valuee)
    await this.driver.execute('flutter:waitForTappable', elem)
    assert.strictEqual(await this.driver.getElementText(elem), valuee)
    await this.driver.elementClick(elem)
    await this.driver.execute('flutter:waitForTappable', byValueKey('showJournalBottomSheet'))
    await this.driver.elementClick(byValueKey('showJournalBottomSheet')) // Edit journal setup;Reset journal entry
    await this.driver.execute('flutter:waitForTappable', byText('Edit journal setup'))
    await this.driver.elementClick(byText('Edit journal setup'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('addJournalTitle'))
    await this.driver.elementClick(byValueKey('addJournalTitle'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('textFieldBottomSheet'))
    await this.driver.elementSendKeys(byValueKey('textFieldBottomSheet'), 'updated' + valuee)
    await this.driver.execute('flutter:waitForTappable', byValueKey('sendButton'))
    await this.driver.elementClick(byValueKey('sendButton'))
    await this.driver.execute('flutter:waitForTappable', byText('Save'))
    await this.driver.elementClick(byText('Save'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('showJournalBottomSheet'))
    await this.driver.elementClick(byValueKey('showJournalBottomSheet')) // Edit journal setup;Reset journal entry
    await this.driver.execute('flutter:waitForTappable', byText('Edit journal setup'))
    await this.driver.elementClick(byText('Edit journal setup'))
    await this.driver.execute('flutter:waitForTappable', byValueKey('deleteButton'))

    await this.driver.elementClick(byValueKey('deleteButton'))
    await this.driver.execute('flutter:waitForTappable', byText('Delete'))
    await this.driver.elementClick(byText('Delete'))
    await this.driver.elementClick(byValueKey('todayToggleButton'))
    await this.driver.elementClick(byValueKey('todayToggleButton'))
    console.log('today_journal done')
    await waitFor(5000)
  }
}
