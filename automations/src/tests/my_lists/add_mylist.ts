/* eslint-disable @typescript-eslint/restrict-plus-operands */
/* eslint-disable @typescript-eslint/no-misused-promises */

import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates, @typescript-eslint/no-unused-vars
import { byValueKey, byType, byText, byTooltip } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'
// import { waitFor } from '../common/utils.js'

export class MyLists {
  driver: WebdriverIO.Browser
  constructor (driver: WebdriverIO.Browser) {
    this.driver = driver
  }

  async testMyList (): Promise<void> {
    const months = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
    const d = new Date()
    const currentMonth = months[d.getMonth()]
    console.log(currentMonth)
    const currentYear = new Date().getFullYear()
    console.log(currentYear)
    // eslint-disable-next-line @typescript-eslint/restrict-plus-operands
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const valuee = 'New List ' + currentDate
    await this.driver.elementClick(byText('My Lists'))
    await waitFor(5000)
    await this.driver.elementClick(byValueKey('closelistsheet'))
    await waitFor(3000)
    await this.driver.elementClick(byText('My Lists'))
    await waitFor(3000)
    await this.driver.execute('flutter:scrollIntoView', byValueKey('listentries'), { alignment: 1 })
    await waitFor(3000)
    await this.driver.elementClick(byText('Add List')) // key: const ValueKey('addlist') //key: const ValueKey('entertext'),key: const ValueKey('addlist'),
    await this.driver.elementSendKeys(byValueKey('entertext'), valuee) // key: const ValueKey('listentries'),
    await waitFor(1500)
    await this.driver.elementClick(byValueKey('addlist')) // key: const ValueKey('listitementries'),kebab menu:key: const ValueKey('selectoption'),
    await waitFor(1500)

    let i = 10
    for (i = 1; i <= 5; i++) {
      await this.driver.elementClick(byText('Add items'))
      await waitFor(1500)
      await this.driver.elementSendKeys(byValueKey('entertext'), 'item' + i) // key: const ValueKey('listentries'),
      await waitFor(1500)
      await this.driver.elementClick(byValueKey('addlist')) // key: const ValueKey('listitementries'),kebab menu:key: const ValueKey('selectoption'),
      await waitFor(1000)
    }
    await this.driver.elementClick(byValueKey('closeitemsheet'))
    await waitFor(3000)
    await this.driver.execute('flutter:scrollIntoView', byValueKey('listentries'), { alignment: 0 })

    await waitFor(1200)
    const elem = byText(valuee) // here if multiple entries with same name --conflict
    assert.strictEqual(await this.driver.getElementText(elem), valuee)
    console.log('Success')
    await waitFor(1200)
    await this.driver.elementClick(byText(valuee))
    await waitFor(1200)
    await this.driver.elementClick(byValueKey('selectoption'))
    await waitFor(1200)
    await this.driver.elementClick(byText('Update list name'))
    await waitFor(1200)
    await this.driver.elementSendKeys(byValueKey('entertext'), 'Updated ' + valuee)
    await waitFor(1200)
    await this.driver.elementClick(byValueKey('addlist'))
    await waitFor(1200)
    await this.driver.elementClick(byValueKey('selectoption'))
    await waitFor(1200)
    await this.driver.elementClick(byText('Check all'))
    await waitFor(1200)
    await this.driver.elementClick(byValueKey('selectoption'))
    await waitFor(1200)
    await this.driver.elementClick(byText('Uncheck all'))
    await waitFor(1200)
    await this.driver.elementClick(byValueKey('selectoption'))
    await waitFor(1200)
    await this.driver.elementClick(byText('Delete list'))
    await waitFor(1200)
    await this.driver.elementClick(byText('Delete'))
    await waitFor(1200)
    await this.driver.elementClick(byValueKey('closelistsheet'))
  }
}
