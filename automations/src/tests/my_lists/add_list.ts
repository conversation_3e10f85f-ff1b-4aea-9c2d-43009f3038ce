import { strict as assert } from 'assert'
// eslint-disable-next-line import/no-duplicates, @typescript-eslint/no-unused-vars
import { byValueKey, byType, byText, byTooltip, bySemanticsLabel } from 'appium-flutter-finder'
import { waitFor } from '../common/utils.js'
import { Key } from 'webdriverio'
export class Lists {
  driver: WebdriverIO.Browser
  constructor(driver: WebdriverIO.Browser) {
    this.driver = driver
  }
  async addingLists(): Promise<void> {

    const list = byText('Lists')
    await this.driver.elementClick(list)
    await waitFor(1000)
    const createList = byText('Create List')
    await this.driver.elementClick(createList)
    await waitFor(1000)
    const d = new Date()
    const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
    const text = 'New_List 1'
    const input = byValueKey('textFieldBottomSheet')
    await this.driver.elementSendKeys(input, text)
    await waitFor(1000)
    const Send = byValueKey('sendButton')
    await this.driver.elementClick(Send)
    await waitFor(1000)
    const tap = byText('New_List 1')
    await this.driver.elementClick(tap)
    await waitFor(1000)

    const item = byText('Add Items')
    await this.driver.elementClick(item)
    await waitFor(1000)

    for (let i = 1; i <= 5; i++) {
      let text = 'New_Item ' + i
      const input = byValueKey('textFieldBottomSheet')
      await this.driver.elementSendKeys(input, text)
      await waitFor(1000)
      const add = byValueKey('addItem')
      await this.driver.elementClick(add)
      await waitFor(1000)
    }
    await this.driver.switchContext('NATIVE_APP')
    const el16 = await this.driver.$('/hierarchy/android.widget.FrameLayout/android.widget.LinearLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.widget.FrameLayout/android.view.View/android.view.View/android.view.View/android.view.View/android.view.View[2]')
    await el16.click();
    await this.driver.switchContext('FLUTTER')

    const kebabMenuArray = ['Check all', 'Uncheck all', 'Update list name', 'Delete list'];
    for (let i = 0; i < kebabMenuArray.length; i++) {
      const kebabMenu = byValueKey('kebabMenu')
      await this.driver.elementClick(kebabMenu)
      await waitFor(1000)
      const kebabMenuItem = byText(kebabMenuArray[i])
      await this.driver.elementClick(kebabMenuItem)
      await waitFor(1000)

      if (kebabMenuArray[i] === 'Update list name') {
        const newList = 'New_List_7'
        const bottomSheetInput = byValueKey('textFieldBottomSheet')
        await this.driver.elementSendKeys(bottomSheetInput, newList)
        await waitFor(1000)
        const clickSendButton = byValueKey('sendButton')
        await this.driver.elementClick(clickSendButton)
        await waitFor(1000)
      }
    }
    const deleteList = byText('Delete')
    await this.driver.elementClick(deleteList)
    await waitFor(1000)
  }
}