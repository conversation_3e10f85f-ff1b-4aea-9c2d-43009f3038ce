/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable @typescript-eslint/no-misused-promises */
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { remote } from 'webdriverio'
import { deviceName, testFilePath } from './configuration.js'
import { TestLoginFlow } from './tests/login/test_login.js'
import { JournalToday } from './tests/journal/today_journal.js'
import { NotesToday } from './tests/notes/today_notes.js'
import { YesorNoHabitsToday } from './tests/habits/today_habit/yes_or_no_habits_today.js'
import { NumericalHabitsToday } from './tests/habits/today_habit/numerical_habits_today.js'
import { SingleChoiceHabitsToday } from './tests/habits/today_habit/single_choice_habits_today.js'
import { MultiChoiceHabitsToday } from './tests/habits/today_habit/multi_choice_habits_today.js'
import { TodoToday } from './tests/todo/today_todo.js'
import { Lists } from './tests/my_lists/add_list.js'

 const opts = {
  hostname: 'mobile-hub.lambdatest.com',
  port: 80,
  path: '/wd/hub',
  user: 'anu.rachel',
  key: process.env.LAMBDA_AUTOMATION_KEY,
  capabilities: {
    platformName: 'android',
    isRealMobile: true,
    deviceName: 'Galaxy S22 Ultra 5G',
    version: '11',
    realDevice: true,
    appiumVersion: '2.0',
    visual: true,
    automationName: 'Flutter',
    devicelog: true,
    app: process.env.LAMBDA_APK_PATH,
    playStoreLogin: { email: process.env.LAMBDA_AUTOMATION_TEST_EMAIL, password: process.env.LAMBDA_AUTOMATION_TEST_PASSWORD }

  }
}
void (async () => {
  const driver = await remote(opts)

  // Test cases
  const loginTest = new TestLoginFlow(driver)
  await loginTest.testEmailPasswordLogin()
  const todotoday = new TodoToday(driver)
  await todotoday.testTodoToday()
  const notestoday = new NotesToday(driver)
  await notestoday.testNotesToday()
  const journaltoday = new JournalToday(driver)
  await journaltoday.testJournalToday()
  const yesornohabitstoday = new YesorNoHabitsToday(driver)
  await yesornohabitstoday.testYesorNoHabitsToday()
  const numericalhabitstoday = new NumericalHabitsToday(driver)
  await numericalhabitstoday.testNumericalHabitsToday()

  const singlechoicehabitstoday = new SingleChoiceHabitsToday(driver)
  await singlechoicehabitstoday.testSingleChoiceHabitsToday()
  const multichoicehabitstoday = new MultiChoiceHabitsToday(driver)
  await multichoicehabitstoday.testMultiChoiceHabitsToday()
  const mylists = new Lists(driver)
  await mylists.addingLists()
  /* const passcodetest = new TestPasscodeFlow(driver)
  await passcodetest.passcodeTestFlow()
  const settingspage = new SettingsPage(driver)
  await settingspage.testSettingsPage()
  const pasthabitfilter = new PastHabitFilter(driver)
  await pasthabitfilter.pastHabitFilter()
  const pastjournalfilter = new PastJournalFilter(driver)
  await pastjournalfilter.pastJournalFilter()
  const pastnotefilter = new PastNoteFilter(driver)
  await pastnotefilter.pastNoteFilter()
  const futurehabitpage = new FutureHabitPage(driver)
  await futurehabitpage.FutureHabitPage()
  const futurenotefilter = new FutureNoteFilter(driver)
  await futurenotefilter.futureNoteFilter()
  const futuretodofilter = new FutureTodoFilter(driver)
  await futuretodofilter.futureTodoFilter()
  const futurejournalpage = new FutureJournalPage(driver)
  await futurejournalpage.futureJournalPage() */

  await driver.deleteSession()
})()
