import sgMail from '@sendgrid/mail';
import { Client } from "@sendgrid/client";
import * as fs from 'fs';
import * as fs1 from 'fs-extra';
import * as path from 'path';
import allure from 'allure-commandline'
import fetch from 'node-fetch';
import dotenv from 'dotenv';

class SendMaill {
    public readJSONFile(): any {
        try {
            const tmpFolderPath = '../automations/.tmp/json/'
            const files = fs.readdirSync(tmpFolderPath);
            const jsonFiles = files.filter(file => file.endsWith('.json'));
            if (jsonFiles.length > 0) {
                const reportFileName = jsonFiles[0]; // Assuming there's only one JSON report file
                const tmpFolderPath1 = '../automations/.tmp/json/' + reportFileName;
                const fileData = fs.readFileSync(tmpFolderPath1, 'utf-8');
                const jsonData = JSON.parse(fileData);
                return jsonData;
            }
        } catch (error) {
        }
    }

    public async sendMail() {
        console.log(process.env.SENDGRID_API_KEY)
        const currentEnvironment = process.env.CURRENT_ENVIRONMENT
        if (process.env.SENDGRID_API_KEY == undefined)
            return
        const folderPath = '../automations/allure-results';
        const jsonFiles: string[] = [];
        let firstStartValue
        let lastStopValue
        let test_result = 'fail'
        function readJsonFile(filePath: string): Promise<any> {
            return new Promise((resolve, reject) => {
                fs.readFile(filePath, 'utf8', (err, data) => {
                    if (err) {
                        reject(err);
                    } else {
                        const jsonData = JSON.parse(data);
                        resolve(jsonData);
                    }
                });
            });
        }

        function findContainerJsonFile(): Promise<string> {
            return new Promise((resolve, reject) => {
                fs.readdir(folderPath, (err, files) => {
                    if (err) {
                        reject(err);
                    } else {
                        const containerFile = files.find((file) => file.endsWith('-container.json'));
                        if (containerFile) {
                            resolve(containerFile);
                        } else {
                            reject(new Error('Container JSON file not found in the folder.'));
                        }
                    }
                });
            });
        }

        // Retrieve files ending with "attachment.png"
        const attachmentFiles: string[] = [];
        fs.readdirSync(folderPath).forEach((file) => {
            if (file.endsWith('attachment.png')) {
                attachmentFiles.push(path.join(folderPath, file));
            }
        });

        function readResultJsonFiles(resultJsonFiles: string[]): Promise<any[]> {
            const resultPromises = resultJsonFiles.map((resultFile) => {
                const resultFilePath = path.join(folderPath, `${resultFile}-result.json`);
                return readJsonFile(resultFilePath);
            });

            return Promise.all(resultPromises);
        }

        const processJsonPromise = new Promise<void>(async (resolve, reject) => {
            await findContainerJsonFile()
                .then((containerJsonFile: string) => {
                    const containerFilePath = path.join(folderPath, containerJsonFile);
                    return readJsonFile(containerFilePath)
                        .then((containerJson: any) => containerJson.children)
                        .catch((err) => {
                            throw new Error(`Error reading container JSON: ${err}`);
                        });
                })
                .then((resultJsonFiles: string[]) => {
                    return readResultJsonFiles(resultJsonFiles);
                })
                .then((resultJsonData: any[]) => {
                    // Process and display each result JSON object
                    resultJsonData.forEach((resultJsonObj, index) => {
                    });
                    // Get start value from the first JSON object
                    const firstJsonObject = resultJsonData[0];
                    firstStartValue = firstJsonObject.start;
                    // Get stop value from the last JSON object
                    const lastJsonObject = resultJsonData[resultJsonData.length - 1];
                    lastStopValue = lastJsonObject.stop;
                    resolve();
                })
                .catch((err) => {
                    console.error('Error reading JSON files:', err);
                });
        });
        await processJsonPromise;
        sgMail.setClient(new Client());
        sgMail.setApiKey(process.env.SENDGRID_API_KEY)
        var jsonString = this.readJSONFile();
        var passedCount = 0;
        var failedCount = 0;
        var skippedCount = 0;
        const elements = jsonString[0].elements;
        // Loop through each element (scenario) in the elements array
        const failedScenarios: { scenarioName: string; stepName: string; errorMessage: string }[] = [];
        for (const element of elements) {
            const steps = element.steps;
            for (const step of steps) {
                if (step.result.status === "failed") {
                    const scenarioName = element.name;
                    const stepName = step.name;
                    const errorMessage = step.result.error_message || "No error message available";
                    failedScenarios.push({ scenarioName, stepName, errorMessage });
                }
            }
        }

        let failed_scenario_count = 0
        for (const scenario of jsonString[0].elements) {
            let hasFailedStep = false;
            for (const step of scenario.steps) {
                const resultStatus = step.result.status;
                if (resultStatus === "failed") {
                    hasFailedStep = true;
                    if (hasFailedStep && ["After", "Before", "Given", "When", "Then"].some((keyword) =>
                        step.keyword.includes(keyword)
                    )
                    ) {
                        failed_scenario_count++;
                    }
                    break;
                }
            }
        }
        let scenarioCount = 0;
        for (const element of elements) {
            if (element.keyword === "Scenario") {
                scenarioCount++;
            }
        }
        for (const element of elements) {
            // Access the steps array for each element
            const steps = element.steps;
            // Loop through each step in the steps array
            for (const step of steps) {
                const { status } = step.result;
                // Count the number of passed and failed steps
                if (status === "passed") {
                    passedCount++;
                } else if (status === "failed") {
                    failedCount++;
                }
                else if (status === "skipped") {
                    skippedCount++;
                }
            }
        }
        const totalScenarios = scenarioCount;
        const passedScenarios = totalScenarios - failed_scenario_count;
        const timestamp1 = firstStartValue;
        const timestamp2 = lastStopValue;
        const date1 = new Date(timestamp1);
        const date2 = new Date(timestamp2);
        const hours = date1.getHours();
        const minutes = date1.getMinutes();
        const seconds = date1.getSeconds();
        const hours1 = date2.getHours();
        const minutes1 = date2.getMinutes();
        const seconds1 = date2.getSeconds();
        const formattedDate = `${date1.getDate()}-${date1.getMonth() + 1}-${date1.getFullYear()}`;// Months are zero-based, so we add 1
        // Format hours in 12-hour format with AM/PM
        const period = hours >= 12 ? 'PM' : 'AM';
        const formattedHours = hours % 12 || 12;
        const formattedTime = `${formattedHours}: ${minutes.toString().padStart(2, '0')
            }:${seconds.toString().padStart(2, '0')} ${period} `;
        const formattedHours1 = hours1 % 12 || 12;
        const formattedTime1 = `${formattedHours1}:${minutes1.toString().padStart(2, '0')}:${seconds1.toString().padStart(2, '0')} ${period} `;
        let subject = '[' + currentEnvironment + ': ' + ']Automation results(' + passedScenarios + ' / ' + totalScenarios + ')'
        let htmlbody =
            `<body>
        <center>
          <h1>MEVOLVE AUTOMATION TEST RESULT</h1>
        </center>
        <b>Date:</b>${formattedDate}<br>
        <b>Start Time:</b>${formattedTime}<br>
        <b>End Time:</b>${formattedTime1}<br>
        <b>Total Scenarios:</b>${totalScenarios}<br>
        <b>Passed Scenarios:</b>${passedScenarios}<br>
        <b>Failed Scenarios:</b>${failed_scenario_count}<br>`

        if (failed_scenario_count > 0) {
            htmlbody += `<h2>Failed Scenarios:</h2>`;

            // Include the failed scenarios in the HTML body
            for (const failedScenario of failedScenarios) {
                htmlbody += `<b>Scenario:</b>${failedScenario.scenarioName}<br>`;
                htmlbody += `<b>Failed Step:</b>${failedScenario.stepName}<br>`;
                htmlbody += `<b>Failed Reason:</b>${failedScenario.errorMessage}<br>`;
                htmlbody += "-------------------------<br>";
            }
        }

        htmlbody += `<center><a href='https://mevolve-automation.web.app/'><h2> Cucumber HTML Reporter</h2></a></center><br><b></b></body>`;
        const msg: sgMail.MailDataRequired = {
            to: '<EMAIL>',
            from: '<EMAIL>',
            subject: subject,
            text: 'Here is a mail for you!',
            html: htmlbody,
            attachments: [],
            trackingSettings: {
                clickTracking: {
                    enable: false
                }
            }
        };
        attachmentFiles.forEach((file) => {
            const fileContent = fs.readFileSync(file);
            msg.attachments.push({
                content: fileContent.toString('base64'),
                filename: path.basename(file),
                type: 'image/png',
                disposition: 'attachment',
            });
        });
        console.log('to:', msg.to);
        console.log('from:', msg.from);
        console.log('subject:', msg.subject);
        console.log('text:', msg.text);
        console.log('html:', msg.html);

        if (failed_scenario_count > 0) { test_result = 'Fail' }
        else if (failed_scenario_count === 0) { test_result = 'Pass' }

        //await this.uploadReports(totalScenarios, passedScenarios, failed_scenario_count, test_result);
        await sgMail.send(msg).then(() => {
            console.log('Email sent');
        })
            .catch((error: Error) => {
                console.error('error is:' + error);
            });
        console.log('after sending email')
    }

    public async getVaValues() {
        const folderPath = '../automations/allure-results';
        let test_result = 'fail'
        function readJsonFile(filePath: string): Promise<any> {
            return new Promise((resolve, reject) => {
                fs.readFile(filePath, 'utf8', (err, data) => {
                    if (err) {
                        reject(err);
                    } else {
                        const jsonData = JSON.parse(data);
                        resolve(jsonData);
                    }
                });
            });
        }

        function findContainerJsonFile(): Promise<string> {
            return new Promise((resolve, reject) => {
                fs.readdir(folderPath, (err, files) => {
                    if (err) {
                        reject(err);
                    } else {
                        const containerFile = files.find((file) => file.endsWith('-container.json'));
                        if (containerFile) {
                            resolve(containerFile);
                        } else {
                            reject(new Error('Container JSON file not found in the folder.'));
                        }
                    }
                });
            });
        }

        // Retrieve files ending with "attachment.png"
        const attachmentFiles: string[] = [];
        fs.readdirSync(folderPath).forEach((file) => {
            if (file.endsWith('attachment.png')) {
                attachmentFiles.push(path.join(folderPath, file));
            }
        });

        function readResultJsonFiles(resultJsonFiles: string[]): Promise<any[]> {
            const resultPromises = resultJsonFiles.map((resultFile) => {
                const resultFilePath = path.join(folderPath, `${resultFile}-result.json`);
                return readJsonFile(resultFilePath);
            });

            return Promise.all(resultPromises);
        }

        const processJsonPromise = new Promise<void>(async (resolve, reject) => {
            await findContainerJsonFile()
                .then((containerJsonFile: string) => {
                    const containerFilePath = path.join(folderPath, containerJsonFile);
                    return readJsonFile(containerFilePath)
                        .then((containerJson: any) => containerJson.children)
                        .catch((err) => {
                            throw new Error(`Error reading container JSON: ${err}`);
                        });
                })
                .then((resultJsonFiles: string[]) => {
                    return readResultJsonFiles(resultJsonFiles);
                })
                .then((resultJsonData: any[]) => {
                    // Process and display each result JSON object
                    resultJsonData.forEach((resultJsonObj, index) => {
                    });
                    resolve();
                })
                .catch((err) => {
                    console.error('Error reading JSON files:', err);
                });
        });
        await processJsonPromise;
        var jsonString = this.readJSONFile();
        var passedCount = 0;
        var failedCount = 0;
        var skippedCount = 0;
        const elements = jsonString[0].elements;
        // Loop through each element (scenario) in the elements array
        const failedScenarios: { scenarioName: string; stepName: string; errorMessage: string }[] = [];
        for (const element of elements) {
            const steps = element.steps;
            for (const step of steps) {
                if (step.result.status === "failed") {
                    const scenarioName = element.name;
                    const stepName = step.name;
                    const errorMessage = step.result.error_message || "No error message available";
                    failedScenarios.push({ scenarioName, stepName, errorMessage });
                }
            }
        }

        let failed_scenario_count = 0
        for (const scenario of jsonString[0].elements) {
            let hasFailedStep = false;
            for (const step of scenario.steps) {
                const resultStatus = step.result.status;
                if (resultStatus === "failed") {
                    hasFailedStep = true;
                    if (hasFailedStep && ["After", "Before", "Given", "When", "Then"].some((keyword) =>
                        step.keyword.includes(keyword)
                    )
                    ) {
                        failed_scenario_count++;
                    }
                    break;
                }
            }
        }
        let scenarioCount = 0;
        for (const element of elements) {
            if (element.keyword === "Scenario") {
                scenarioCount++;
            }
        }
        for (const element of elements) {
            // Access the steps array for each element
            const steps = element.steps;
            // Loop through each step in the steps array
            for (const step of steps) {
                const { status } = step.result;
                // Count the number of passed and failed steps
                if (status === "passed") {
                    passedCount++;
                } else if (status === "failed") {
                    failedCount++;
                }
                else if (status === "skipped") {
                    skippedCount++;
                }
            }
        }
        const totalScenarios = scenarioCount;
        const passedScenarios = totalScenarios - failed_scenario_count;
        if (failed_scenario_count > 0) { test_result = 'Fail' }
        else if (failed_scenario_count === 0) { test_result = 'Pass' }
        return { totalScenarios, passedScenarios, failed_scenario_count, test_result }

    }

    public deleteAllureReportFolder() {
        const allureReportPath = '../automations/allure-report'; // Replace with the actual path to the allure-results folder
        try {
            fs1.emptyDirSync(allureReportPath);
        } catch (error) {
        }
    }

    public async allureReportGenerator() {
        const reportError = new Error('Could not generate Allure report');
        const generation = allure(['generate', 'allure-results', '--clean']);
        await new Promise<void>(async (resolve, reject) => {
            const generationTimeout = setTimeout(() => reject(reportError), 5000);
            await generation.on('exit', function (exitCode) {
                clearTimeout(generationTimeout);
                if (exitCode !== 0) {
                    return reject(reportError);
                }
                resolve();
            });
        });
    }
}

export default new SendMaill()
export const sendMailInstance = new SendMaill();