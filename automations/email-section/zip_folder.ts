import * as fs from 'fs';
import * as path from 'path';
import JS<PERSON><PERSON> from 'jszip';

class ZipUtility {
  public async createZipFromFolder(folderPath: string, outputPath: string): Promise<void> {
    // Initialize a new instance of JSZip
    const zip = new JSZip();

    // Function to add a file to the zip
    const addFileToZip = async (filePath: string, relativePath: string) => {
      const fileData = await fs.promises.readFile(filePath);
      zip.file(relativePath, fileData);
    };

    // Function to recursively process a folder and add its contents to the zip
    const processFolder = async (folder: string, relativePath: string) => {
      const files = await fs.promises.readdir(folder);
      for (const file of files) {
        const filePath = path.join(folder, file);
        const fileStats = await fs.promises.stat(filePath);
        if (fileStats.isFile()) {
          await addFileToZip(filePath, path.join(relativePath, file));
        } else if (fileStats.isDirectory()) {
          await processFolder(filePath, path.join(relativePath, file));
        }
      }
    };

    // Start processing the folder
    await processFolder(folderPath, '');

    // Generate the zip file
    const zipData = await zip.generateAsync({ type: 'nodebuffer' });

    // Write the zip file to the specified output path
    await fs.promises.writeFile(outputPath, zipData);
  }

  public async zipToBase64(zipPath: string): Promise<string> {
    // Read the zip file
    const zipData = await fs.promises.readFile(zipPath);

    // Convert the zip file data to base64
    const base64Data = zipData.toString('base64');
    return base64Data;
  }
}

// Usage: Export an instance of ZipUtility
export default new ZipUtility;
