import * as path from "path";
import { generate } from 'multiple-cucumber-html-reporter'
import { sendMailInstance } from './email-section/email_report';
import SendMaill from './email-section/email_report';
import fs1 from 'fs';
import fetch from 'node-fetch';
import dotenv from 'dotenv';
import ZipUtility from "./email-section/zip_folder"
const allureReportFolder = '../automations/allure-report'
const outputZipPath = '../automations/allure-report-zip.zip';
dotenv.config();
const envConfig = dotenv.parse(fs1.readFileSync('.env'));
const password = process.env.LAMBDA_AUTOMATION_KEY
const username = "anu.rachel"
const credentials = btoa(`${username}:${password}`);
const currentEnvironment = process.env.CURRENT_ENVIRONMENT
const appTheme = envConfig.APP_THEME
const themeColor = envConfig.THEME_COLOR
const devicename = envConfig.DEVICE
const platformVersion = envConfig.PLATFORM_VERSION
const testMail = process.env.LAMBDA_AUTOMATION_TEST_EMAIL
const screenSize = envConfig.SCREEN_SIZE
console.log("Hello config");
const d = new Date()
const currentDate = d.getDate() + '/' + (d.getMonth() + 1) + '/' + d.getFullYear()
const timestamp1 = d.toLocaleString().replace(', ', ',').replace(/(\d+:\d+:\d+)\s+(am|pm)/i, '$1$2');
const timestamp = timestamp1.replace(/ /g, '');
console.log(timestamp)
const qa = 'qa_daily_' + appTheme + '_' + themeColor + '_' + timestamp
const staging = 'staging_daily_' + screenSize + '_' + appTheme + '_' + themeColor + '_' + timestamp
const dev = 'dev1_local_daily_' + screenSize + '_' + appTheme + '_' + themeColor + '_' + timestamp
const production = 'production_daily_' + screenSize + '_' + appTheme + '_' + themeColor + '_' + timestamp
const other = 'other_daily_' + screenSize + '_' + appTheme + '_' + themeColor + '_' + timestamp
export function getBuild() {
  const qa = 'qa_daily_' + appTheme + '_' + themeColor + '_' + timestamp
  const staging = 'staging_daily_' + screenSize + '_' + appTheme + '_' + themeColor + '_' + timestamp
  const dev = 'dev1_local_daily_' + screenSize + '_' + appTheme + '_' + themeColor + '_' + timestamp
  const production = 'production_daily_' + screenSize + '_' + appTheme + '_' + themeColor + '_' + timestamp
  const other = 'other_daily_' + screenSize + '_' + appTheme + '_' + themeColor + '_' + timestamp
}
function getSmartUIBuild(currentEnvironment: string) {
  switch (currentEnvironment) {
    case 'qa':
      return qa;
    case 'staging':
      return staging;
    case 'dev':
      return dev;
    case 'production':
      return production;
    default:
      return other;
  }
}

function getSmartUIProject(currentEnvironment) {
  switch (currentEnvironment) {
    case 'qa':
      return devicename + '_qa_' + screenSize + '_' + appTheme + '_' + themeColor
    case 'staging':
      return devicename + '_staging_' + screenSize + '_' + appTheme + '_' + themeColor
    case 'dev':
      return devicename + '_dev_local_' + screenSize + '_' + appTheme + '_' + themeColor
    case 'production':
      return devicename + '_production_' + screenSize + '_' + appTheme + '_' + themeColor
    default:
      return devicename + '_other_' + screenSize + '_' + appTheme + '_' + themeColor
  }
}

export const config = {
  user: username,
  key: process.env.LAMBDA_AUTOMATION_KEY,
  path: "/wd/hub",
  hostname: "mobile-hub.lambdatest.com",
  port: 80,
  specs: ["../1-features/past.feature"], //path of your test script
  exclude: [],
  maxInstances: 10,
  capabilities: [
    {
      'smartUI.build': getSmartUIBuild(currentEnvironment),
      'smartUI.project': getSmartUIProject(currentEnvironment),
      'smartUI.baseline': false,
      build: 'Mevolve_test',
      project: 'Mevolve application',
      name: 'Mevolve test - ' + devicename + " " + screenSize + " " + appTheme + " " + themeColor + " " + currentEnvironment,
      appiumVersion: '2.0-stable',
      automationName: 'Flutter',
      isRealMobile: true,
      deviceName: devicename, platformVersion: platformVersion,
      platformName: "android",
      app: process.env.LAMBDA_APK_PATH,
    },
  ],

  logLevel: "info",
  coloredLogs: true,
  screenshotPath: "./errorShots/",
  baseUrl: "",
  connectionRetryCount: 0,
  idleTimeout: '200000',
  framework: "cucumber",
  cucumberOpts: {
    backtrace: true,
    requireModule: [],
    failAmbiguousDefinitions: false,
    failFast: false,
    ignoreUndefinedDefinitions: false,
    name: [],
    profile: [],
    require: [path.join(__dirname, "2-step-definitions", "steps2.js")],
    tags: "not @skipScenario",
    snippetSyntax: undefined,
    snippets: true,
    source: true,
    strict: false,
    tagsInTitle: false,
    timeout: 60000,
    retry: 0,
  },

  reporters: ['spec', ['allure', {
    outputDir: 'allure-results',
    disableWebdriverStepsReporting: true,
    disableWebdriverScreenshotsReporting: true,
    useCucumberStepReporter: true,
    addConsoleLogs: true
  }], 'cucumberjs-json'],
  reporterOptions: {
    outputDir: './Results'
  },

  // =====
  // Hooks
  // =====
  // WebdriverIO provides several hooks you can use to interfere with the test process in order to enhance
  // it and to build services around it. You can either apply a single function or an array of
  // methods to it. If one of them returns with a promise, WebdriverIO will wait until that promise got
  // resolved to continue.
  /**
   * Gets executed once before all workers get launched.
   * @param {Object} config wdio configuration object
   * @param {Array.<Object>} capabilities list of capabilities details
   */
  onPrepare: async () => {
    // Remove the `.tmp/` folder that holds the json and report files
    if (fs1.existsSync('.tmp/')) {
      fs1.rmSync('.tmp/', { recursive: true });
    }

    // Remove the 'allure-results/' folder
    if (fs1.existsSync('allure-results/')) {
      fs1.rmSync('allure-results/', { recursive: true });
    }
    await SendMaill.deleteAllureReportFolder()
    await fetch('https://europe-west1-mevolve-' + currentEnvironment + '.cloudfunctions.net/me-automations-deleteData?account=' + process.env.LAMBDA_AUTOMATION_TEST_EMAIL)
    await fetchUser(currentEnvironment)
      .then(() => {
        console.log('Data fetched successfully.');
      })
      .catch((error) => {
        console.error('Error fetching data:', error);
      });
    async function fetchUser(currentEnvironment) {
      let url;
      url = 'https://europe-west1-mevolve-' + currentEnvironment + '.cloudfunctions.net/me-automations-initUserData?account=' + process.env.LAMBDA_AUTOMATION_TEST_EMAIL + '&themeColor=' + themeColor + '&appTheme=' + appTheme;
      await fetch(url);
      console.log(testMail + " " + themeColor + " " + appTheme)
    }
  },

  // onPrepare: function (config, capabilities) {
  // },
  /**
   * Gets executed before a worker process is spawned and can be used to initialise specific service
   * for that worker as well as modify runtime environments in an async fashion.
   * @param  {String} cid      capability id (e.g 0-0)
   * @param  {[type]} caps     object containing capabilities for session that will be spawn in the worker
   * @param  {[type]} specs    specs to be run in the worker process
   * @param  {[type]} args     object that will be merged with the main configuration once worker is initialised
   * @param  {[type]} execArgv list of string arguments passed to the worker process
   */
  // onWorkerStart: function (cid, caps, specs, args, execArgv) {
  // },
  /**
   * Gets executed just before initialising the webdriver session and test framework. It allows you
   * to manipulate configurations depending on the capability or spec.
   * @param {Object} config wdio configuration object
   * @param {Array.<Object>} capabilities list of capabilities details
   * @param {Array.<String>} specs List of spec file paths that are to be run
   */
  // beforeSession: function (config, capabilities, specs) {
  // },
  /**
   * Gets executed before test execution begins. At this point you can access to all global
   * variables like `browser`. It is the perfect place to define custom commands.
   * @param {Array.<Object>} capabilities list of capabilities details
   * @param {Array.<String>} specs        List of spec file paths that are to be run
   * @param {Object}         browser      instance of created browser/device session
   */
  // before: function (capabilities, specs) {
  // },
  /**
   * Runs before a WebdriverIO command gets executed.
   * @param {String} commandName hook command name
   * @param {Array} args arguments that command would receive
   */
  // beforeCommand: function (commandName, args) {
  // },
  /**
   * Hook that gets executed before the suite starts
   * @param {Object} suite suite details
   */

  afterStep: async function (step, scenario, { error, duration, passed }, context) {

    if (error && !passed) {
      await driver.takeScreenshot();
      await driver.execute("lambda-hook: {\"action\": \"setTestStatus\",\"arguments\": {\"status\":\"failed\", \"remark\":\"This is a sample remark for tests \"}}");
    }
    else if (error === 'Error: function timed out, ensure the promise resolves within 60000 milliseconds') {
      await driver.deleteSession()
    }
    else if (passed) {
      await driver.execute("lambda-hook: {\"action\": \"setTestStatus\",\"arguments\": {\"status\":\"passed\", \"remark\":\"This is a sample remark for tests \"}}");
    }
  },

  /**
   * Hook that gets executed after the suite has ended
   * @param {Object} suite suite details
   */
  // afterSuite: function (suite) {
  // },
  /**
   * Runs after a WebdriverIO command gets executed
   * @param {String} commandName hook command name
   * @param {Array} args arguments that command would receive
   * @param {Number} result 0 - command success, 1 - command error
   * @param {Object} error error object if any
   */
  // afterCommand: function (commandName, args, result, error) {
  // },
  /**
   * Gets executed after all tests are done. You still have access to all global variables from
   * the test.
   * @param {Number} result 0 - test pass, 1 - test fail
   * @param {Array.<Object>} capabilities list of capabilities details
   * @param {Array.<String>} specs List of spec file paths that ran
   */
  // after: function (result, capabilities, specs) {
  // },
  /**
   * Gets executed right after terminating the webdriver session.
   * @param {Object} config wdio configuration object
   * @param {Array.<Object>} capabilities list of capabilities details
   * @param {Array.<String>} specs List of spec file paths that ran
   */
  // afterSession: function (config, capabilities, specs) {
  // },
  /**
   * Gets executed after all workers got shut down and the process is about to exit. An error
   * thrown in the onComplete hook will result in the test run failing.
   * @param {Object} exitCode 0 - success, 1 - fail
   * @param {Object} config wdio configuration object
   * @param {Array.<Object>} capabilities list of capabilities details
   * @param {<Object>} results object containing test results
   */
  onComplete: async () => {
    // Generate the report when it all tests are done
    await driver.deleteSession()
    generate({
      //   // Required
      //   // This part needs to be the same path where you store the JSON files
      jsonDir: '.tmp/json/',
      reportPath: '.tmp/report/',
      //   // for more options see https://github.com/wswebcreation/multiple-cucumber-html-reporter%23options
    });

    await fetch('https://europe-west1-mevolve-' + currentEnvironment + '.cloudfunctions.net/me-automations-deleteData?account=' + process.env.LAMBDA_AUTOMATION_TEST_EMAIL)

    async function retrieveValuesFromSendMaill() {
      try {
        const vaValues = await sendMailInstance.getVaValues();
        let { screenShotPath, projectName, buildName } = await makeAPICall()
        await uploadReports(vaValues.totalScenarios, vaValues.passedScenarios, vaValues.failed_scenario_count, vaValues.test_result, screenShotPath, projectName, buildName);
      } catch (error) {
        console.error('Error retrieving values:', error);
      }
    }
    await SendMaill.allureReportGenerator();
    await retrieveValuesFromSendMaill();
    await SendMaill.sendMail()
  },
  /**
 * Gets executed when a refresh happens.
 * @param {String} oldSessionId session ID of the old session
 * @param {String} newSessionId session ID of the new session
 */
  //onReload: function(oldSessionId, newSessionId) {
  //}
};
export async function makeAPICall(): Promise<{ screenShotPath: string, buildName: string, projectName: string } | null> {

  var projectToken1;
  var projectToken = projectToken1;
  if (process.env.CURRENT_ENVIRONMENT === 'staging' && envConfig.DEVICE === 'Galaxy S23 Ultra') {
    projectToken = '887730%231d801803-90e4-4463-9974-4224382f1979%23Galaxy%20S23%20Ultra_staging_6.43*3.07*0.35_in_dark_blue'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + staging;
    console.log('dev value is ' + dev)
    console.log('url in screenshot' + url)
    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });
      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
        } = data.data;
        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=1d801803-90e4-4463-9974-4224382f1979&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      }
      else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }
  else if (process.env.CURRENT_ENVIRONMENT === 'staging' && envConfig.DEVICE === 'Galaxy S10') {
    projectToken = '887730%23bb8a3929-040b-4cc8-af00-4b99a557c525%23Galaxy%20S10_staging_5.90*2.77*0.31_in_light_blue'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + staging;
    console.log('url in screenshot' + url)
    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
        } = data.data;

        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=bb8a3929-040b-4cc8-af00-4b99a557c525&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      } else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }
  else if (process.env.CURRENT_ENVIRONMENT === 'staging' && envConfig.DEVICE === 'Pixel 6 Pro') {
    projectToken = '887730%23a9a80d02-0066-45c1-bc77-4f168694aeb2%23Pixel%206%20Pro_staging_6.45*2.99*0.35_in_light_green'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + staging;
    console.log('url in screenshot' + url)
    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
        } = data.data;
        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=a9a80d02-0066-45c1-bc77-4f168694aeb2&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      }
      else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }
  else if (process.env.CURRENT_ENVIRONMENT === 'staging' && envConfig.DEVICE === 'Pixel 5') {
    projectToken = '887730%23349e42ae-c5b3-4d62-a6a7-b2e21229b07f%23Pixel%205_staging_5.70*2.77*0.31_in_dark_green'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + staging;
    console.log('url in screenshot' + url)
    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
        } = data.data;

        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=349e42ae-c5b3-4d62-a6a7-b2e21229b07f&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      }
      else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }
  else if (process.env.CURRENT_ENVIRONMENT === 'qa' && envConfig.DEVICE === 'Galaxy S23 Ultra') {
    projectToken = '887730%23c7077d64-843f-417f-8321-b147530b0cd3%23Galaxy%20S23%20Ultra_qa_6.43*3.07*0.35_in_dark_blue'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + qa;
    console.log('url in screenshot' + url)

    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
        } = data.data;

        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=c7077d64-843f-417f-8321-b147530b0cd3&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      } else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }

  else if (process.env.CURRENT_ENVIRONMENT === 'qa' && envConfig.DEVICE === 'Galaxy S10') {
    projectToken = '887730%23a423cec9-67e3-4b20-a2e1-7a7bcedd88bc%23Galaxy%20S10_qa_5.90*2.77*0.31_in_light_blue'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + qa;
    console.log('dev value is ' + dev)
    console.log('url in screenshot' + url)
    const response = await fetch(url);
    const data = await response.json();
    const {
      buildId,
      buildName,
      projectName,
    } = data.data;
    const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=a423cec9-67e3-4b20-a2e1-7a7bcedd88bc&buildid=' + buildId + '&new=true&isBaselineBuild=false'
    console.log('screenshotpath' + screenShotPath)
    return { screenShotPath, buildName, projectName };
  }

  else if (process.env.CURRENT_ENVIRONMENT === 'qa' && envConfig.DEVICE === 'Pixel 6 Pro') {
    projectToken = '887730%23ca2f4f0b-1588-4ce0-a1ff-130e7bab838b%23Pixel%206%20Pro_qa_6.45*2.99*0.35_in_light_green'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + qa;
    console.log('dev value is ' + dev)
    console.log('url in screenshot' + url)

    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
        } = data.data;

        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=ca2f4f0b-1588-4ce0-a1ff-130e7bab838b&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      }
      else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }
  else if (process.env.CURRENT_ENVIRONMENT === 'qa' && envConfig.DEVICE === 'Pixel 5') {
    projectToken = '887730%233103dbd7-89b3-41b3-831b-d2a8943ad316%23Pixel%205_qa_5.70*2.77*0.31_in_dark_green '
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + qa;
    console.log('qa value is ' + qa)
    console.log('url in screenshot' + url)

    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
        } = data.data;

        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=3103dbd7-89b3-41b3-831b-d2a8943ad316&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      }
      else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }

  else if (process.env.CURRENT_ENVIRONMENT === 'dev' && envConfig.DEVICE === 'Galaxy S23 Ultra') {
    projectToken = '887730%23291815f3-436e-4413-8af7-ff81457a2d40%23Galaxy%20S23%20Ultra_dev_local_6.43*3.07*0.35_in_dark_blue'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + dev;
    console.log('dev value is ' + dev)
    console.log('url in screenshot' + url)

    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
        } = data.data;

        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=5909a714-dd4e-4aea-a100-56e7e692c627&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      }
      else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }
  else if (process.env.CURRENT_ENVIRONMENT === 'dev' && envConfig.DEVICE === 'Galaxy S10') {
    projectToken = '887730%232f3c06c8-b9af-4454-b3ed-c4a18f195093%23Galaxy%20S10_dev_local_5.90*2.77*0.31_in_light_blue'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + dev;
    console.log('dev value is ' + dev)
    console.log('url in screenshot' + url)
    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
        } = data.data;

        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=2f3c06c8-b9af-4454-b3ed-c4a18f195093&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      }

      else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }
  else if (process.env.CURRENT_ENVIRONMENT === 'dev' && envConfig.DEVICE === 'Pixel 6 Pro') {
    projectToken = '887730%2371b57121-5c39-49ef-bff9-f33321f2a9fb%23Pixel%206%20Pro_dev_local_6.45*2.99*0.35_in_light_green'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + dev;
    console.log('dev value is ' + dev)
    console.log('url in screenshot' + url)

    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
          buildStatus,
          message,
          status
        } = data.data;

        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=71b57121-5c39-49ef-bff9-f33321f2a9fb&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      }
      else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }
  else if (process.env.CURRENT_ENVIRONMENT === 'dev' && envConfig.DEVICE === 'Pixel 5') {
    projectToken = '887730%239821391c-fdd8-4d61-aea7-33bf979172ac%23Pixel%205_dev_local_5.70*2.77*0.31_in_dark_green'
    const url = 'https://api.lambdatest.com/automation/smart-ui/build/status?projectToken=' + projectToken + '&buildName=' + dev;
    console.log('dev value is ' + dev)
    console.log('url in screenshot' + url)

    try {
      const response = await fetch(url, {
        method: 'GET', // or 'POST', 'PUT', etc.
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Basic ${credentials}`,
        },
      });

      if (!response.ok) {
        throw new Error(`API request failed with status: ${response.status}`);
      }
      const data = await response.json();
      if (data && data.data) {
        const {
          buildId,
          buildName,
          projectName,
          buildStatus,
          message,
          status
        } = data.data;
        const screenShotPath = 'https://smartui.lambdatest.com/testdetail?projectid=9821391c-fdd8-4d61-aea7-33bf979172ac&buildid=' + buildId + '&new=true&isBaselineBuild=false'
        console.log('screenshotpath' + screenShotPath)
        return { screenShotPath, buildName, projectName };
      }
      else {
        console.error('Invalid response format or missing data property.');
        // Handle the error or return a default value
      }
    } catch (error) {
      console.error('Error during API request:', error.message);
      // Handle the error or return a default value
    }
  }
}

export async function uploadReports(totalScenarios: number, passedScenarios: number, failed_scenario_count: number, test_result: string, screenshotPath: String, projectName: String, buildName: String) {

  var base64String;
  await ZipUtility.createZipFromFolder(allureReportFolder, outputZipPath)
    .then(() => {
      console.log('Zip created successfully.');
      console.log(outputZipPath)
      return ZipUtility.zipToBase64(outputZipPath);
    })
    .then((base64Zip: string) => {
      base64String = base64Zip;
      console.log('base64String is generated')
    })
    .catch((error) => console.error('Error:', error));

  let url = 'https://europe-west1-mevolve-automation.cloudfunctions.net/uploadResults';
  const requestBody = {
    "environment": process.env.CURRENT_ENVIRONMENT,
    "triggeredBy": "vijay.kumar",
    "deviceName": envConfig.DEVICE,
    "deviceOs": "android",
    "totalTestCases": totalScenarios,
    "totalTestCasesPassed": passedScenarios,
    "totalTestCasesFailed": failed_scenario_count,
    "reportZipFileBase64": base64String,
    "finalResult": test_result,
    "theme": envConfig.APP_THEME,
    "screenShotPath": screenshotPath,
    "color": envConfig.THEME_COLOR,
    "resolution": envConfig.RESOLUTION,
    "screenSize": envConfig.SCREEN_SIZE,
    "projectName": projectName,
    "buildName": buildName
  };
  const body = JSON.stringify(requestBody);
  await fetch(url, {
    method: "POST",
    body: body, // Convert to JSON string
  })
    .then((response) => {
      console.log("Response status:", response.status);
      return response.text();  // Retrieve the response text
    })
    .catch((error) => {
      console.error("Error:", error);
    });
}
