import { Given, When, Then } from "@wdio/cucumber-framework";
import { remote } from "webdriverio";
import { config } from "../wdio.conf";
import { byVal<PERSON><PERSON><PERSON>, byText, byTooltip } from "appium-flutter-finder";
import { strict as assert } from 'assert'
import { setDriver } from "./steps";
import Past from "../3-page-objects/past";
import Future from "../3-page-objects/future";
import Login from "../3-page-objects/login";
import Todo from "../3-page-objects/todo";
import Notes from "../3-page-objects/note";
import Journal from "../3-page-objects/journal";
import YesorNoHabit from "../3-page-objects/yes_or_no_habit";
import NumericalHabit from "../3-page-objects/numerical_habit";
import TimeTypeHabit from "../3-page-objects/time_type_habit";
import SingleChoiceHabit from "../3-page-objects/single_choice_habit";
import MultiChoiceHabit from "../3-page-objects/multi_choice_habit";
import Settings from "../3-page-objects/settings";
import DisabledTabs from "../3-page-objects/disabledTabs";
import Insight from "../3-page-objects/insight";
import ToastActions from "../3-page-objects/crud_toast";
import OverdueTodos from "../3-page-objects/overdue_todos";
import UnscheduledTodos from "../3-page-objects/unscheduled_todos";
import List from "../3-page-objects/list";
import Account from "../3-page-objects/accounts";
import Notification from "../3-page-objects/notification";
import Subscription from "../3-page-objects/subscription";
import Report from "../3-page-objects/reportissue";
import Logout from "../3-page-objects/logout";
import Gallary from "../3-page-objects/gallary";
import SupportChat from "../3-page-objects/supportchat";
import settings from "../3-page-objects/settings";


Given(/^I am on the login screen$/, () => {
  console.log('Login screen');
});
When(/^I login with using an email$/, async () => {
  console.log('FAB');
  await Login.login();
});

Then(/^I should land on Today screen$/, async () => {
  //await Login.loginSuccess();
  console.log("Login done and now on today page ")
});

Given(/^I am on the today screen to check disabled tab views$/, () => {
  console.log("Today screen");
});
When(/^I disabled some features and go to respective tabs$/, async () => {
  await DisabledTabs.mevolveFeatures();
});
Then(/^I should see the disabled and enabled tab status$/, async () => {
  console.log("Add todo done");
});

Given(/^I am on the today screen to add todo$/, () => {
  console.log("Today screen");
});
When(/^I click on FAB and select 'Add Todo' and add data with Today's date and clicked Save$/, async () => {
  await Todo.addTodoToday();
});
Then(/^I should see added todo on Today screen$/, async () => {
  await Todo.addTodoSucess();
  console.log("Add todo done");
});

Given(/^I am on the today screen with an added TODO$/, () => {
  console.log("Today screen");
});
When(/^I click on the radio button present with that TODO$/, async () => {
  await Todo.doneTodo();
  console.log('Success')
});
Then(/^I should see that TODO should be turned to Done or checked state$/, () => {
  console.log("Todo Checked");
});

Given(/^I am on the today screen with an added TODO to be edited$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular todo and after doing changes and saving$/, async () => {
  await Todo.editTodo();
});
Then(/^I should see edited todo on Today screen$/, async () => {
  await Todo.editTodoSucess();
  console.log("Edit todo done");
});

Given(/^I am on the today screen with an added TODO to be deleted$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular todo and after performing delete action$/, async () => {
  await Todo.deleteTodo();
});
Then(/^I should not see that todo in today screen$/, async () => {
  await Login.loginSuccess();
  console.log("Todo deleted");
});

Given(/^I am on the today screen to add notes$/, () => {
  console.log("Today screen");
});
When(/^I click on FAB and select 'Add Note' and add data with Today's date and clicked Save$/, async () => {
  // await Notes.deleteNote();
  await Notes.addNote();
  await Notes.NoteFilter();
});
Then(/^I should see added note on Today screen$/, async () => {
  await Notes.addNoteSucess()
  console.log("Add note done");
});

Given(/^I am on the today screen with an added Note$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular note and after doing changes and saving$/, async () => {
  await Notes.editNote();
});
Then(/^I should see edited note on Today screen$/, async () => {
  await Notes.editNoteSucess();
  console.log("Edit note done");
});
Given(/^I am on the today screen with an added NOTE to be deleted$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular note and after performing delete action$/, async () => {
  await Notes.deleteNote();
});
Then(/^I should not see that note in today screen$/, async () => {
  await Login.loginSuccess();
  console.log("Note deleted");
});

Given(/^I am on the today screen to add journal$/, () => {
  console.log("Today screen");
});
When(/^I click on FAB and select 'Add Journal' and add data with Today's date and clicked Save$/, async () => {
  await Journal.addJournal();
});
Then(/^I should see added journal on Today screen$/, async () => {
  await Journal.addJournalSucess()
  console.log("Add journal done");
});

Given(/^I am on the today screen with an added Journal$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular journal and after doing changes and saving$/, async () => {
  await Journal.editJournal();
});
Then(/^I should see edited journal on Today screen$/, async () => {
  await Journal.editJournalSucess()
  console.log("Edit journal done");
});

Given(/^I am on the today screen with an added journal to be deleted$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular journal and after performing delete action$/, async () => {
  await Journal.deleteJournal();
});
Then(/^I should not see that journal in today screen$/, async () => {
  await Login.loginSuccess();
  console.log("Journal deleted");
});

Given(/^I am on the today screen to add 'Yes or No' habits$/, () => {
  console.log('Today screen');
});
When(/^I click on FAB and select 'Setup Habit' and add a 'Yes or No' habit with Today's date and clicked Save$/, async () => {
  console.log('FAB');
  await YesorNoHabit.addYesOrNoHabit();
});
Then(/^I should see added 'Yes or No' habit on Today screen$/, async () => {
  await YesorNoHabit.addYesOrNohabitSucess()
  console.log("today page");
});

Given(/^I am on the today screen with an added 'Yes or No' habit$/, () => {
  console.log('Today screen with added yes or no habit');
});
When(/^I click on that particular 'Yes or No' habit and after doing changes and saving$/, async () => {
  console.log('FAB');
  await YesorNoHabit.editYesOrNoHabitsToday();
});
Then(/^I should see edited 'Yes or No' habit on Today screen$/, async () => {
  await YesorNoHabit.editYesOrNoHabitsSucess()
  console.log("Yes or No habit edited");
});

Given(/^I am on the today screen with an added 'Yes or No' habit to be deleted$/, () => {
  console.log('Today screen with added yes or no habit');
});
When(/^I click on that particular 'Yes or No' habit and after performing delete action$/, async () => {
  await YesorNoHabit.deleteYesOrNoHabitsToday();
});
Then(/^I should not see that 'Yes or No' habit in today screen$/, async () => {
  //await Login.loginSuccess();
  console.log("Yes or No habit deleted");
});

Given(/^I am on the today screen to add 'Numerical' habits$/, () => {
  console.log('Today screen');
});
When(/^I click on FAB and select 'Setup Habit' and add a 'Numerical' habit with Today's date and clicked Save$/, async () => {
  console.log('FAB');
  await NumericalHabit.addNumericalHabitWithAndWithoutGoal();
});

Then(/^I should see added 'Numerical' habit on Today screen$/, async () => {
  await NumericalHabit.addNumericalHabitSucess()
  console.log("today page");
});

Given(/^I am on the today screen with an added 'Numerical' habit$/, () => {
  console.log('Today screen with added Numerical habit');
});
When(/^I edit that 'Numerical' habit and after performing actions$/, async () => {
  console.log('FAB');
  await NumericalHabit.editNumericalHabitsToday();
});
Then(/^I should see edited 'Numerical' habit on Today screen$/, async () => {
  await NumericalHabit.editNumericalHabitsSucess()
  console.log("Numerical habit edited");
});

Given(/^I am on the today screen with an added 'Numerical' habit to be deleted$/, () => {
  console.log('Today screen with added Numerical habit');
});
When(/^I click on that particular 'Numerical' habit and after performing delete action$/, async () => {
  await NumericalHabit.deleteNumericalHabitsToday();
});
Then(/^I should not see that 'Numerical' habit in today screen$/, async () => {
  await Login.loginSuccess();
  console.log("Numerical habit deleted");
});

Given(/^I am on the today screen to add 'TimeType' habits$/, () => {
  console.log('Today screen');
});
When(/^I click on FAB and select 'Setup Habit' and add a 'TimeType' habit with Today's date and clicked Save$/, async () => {
  console.log('FAB');
  await TimeTypeHabit.addTimeTypeHabitWithAndWithoutGoal();
});

Then(/^I should see added 'TimeType' habit on Today screen$/, async () => {
  //await TimeTypeHabit.addTimeTypeHabitSucess()
  console.log("today page");
});

Given(/^I am on the today screen with an added 'TimeType' habit$/, () => {
  console.log('Today screen with added TimeType habit');
});
When(/^I edit that 'TimeType' habit and after performing actions$/, async () => {
  console.log('FAB');
  await TimeTypeHabit.editTimeTypeHabitsToday();
});
Then(/^I should see edited 'TimeType' habit on Today screen$/, async () => {
  //await TimeTypeHabit.editTimeTypeHabitsSucess()
  console.log("TimeType habit edited");
});

Given(/^I am on the today screen with an added 'TimeType' habit to be deleted$/, () => {
  console.log('Today screen with added TimeType habit');
});
When(/^I click on that particular 'TimeType' habit and after performing delete action$/, async () => {
  await TimeTypeHabit.deleteTimeTypeHabitToday();
});
Then(/^I should not see that 'TimeType' habit in today screen$/, async () => {
  await Login.loginSuccess();
  console.log("TimeType habit deleted");
});

Given(/^I am on the today screen to add 'Single Choice' habits$/, () => {
  console.log('Login screen');
});
When(/^I click on FAB and select 'Setup Habit' and add a 'Single Choice' habit with Today's date and clicked Save$/, async () => {
  await SingleChoiceHabit.addSingleChoiceHabit();
});
Then(/^I should see added 'Single Choice' habit on Today screen$/, async () => {
  await SingleChoiceHabit.addSingleChoiceHabitSucess()
  console.log("Single choice habit added");
});

Given(/^I am on the today screen with an added 'Single Choice' habit$/, () => {
  console.log("Today screen");
});
When(/^I edit that 'Single Choice' habit and after performing actions$/, async () => {
  await SingleChoiceHabit.editSingleChoiceHabitsToday();
});
Then(/^I should see edited 'Single Choice' habit on Today screen$/, async () => {
  await SingleChoiceHabit.editSingleChoiceHabitsSucess()
  console.log("Single choice habit edited");
});

Given(/^I am on the today screen with an added 'Single Choice' habit to be deleted$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular 'Single Choice' habit and after performing delete action$/, async () => {
  await SingleChoiceHabit.deleteSingleChoiceHabitsToday();
  console.log('Success')
});
Then(/^I should not see that 'Single Choice' habit in today screen$/, async () => {
  await Login.loginSuccess();
  console.log("Single choice habit deleted");
});

Given(/^I am on the today screen to add 'Multi Choice' habits$/, () => {
  console.log('Today screen');
});
When(/^I click on FAB and select 'Setup Habit' and add a 'Multi Choice' habit with Today's date and clicked Save$/, async () => {
  console.log('FAB');
  await MultiChoiceHabit.addMultiChoiceHabit();
});
Then(/^I should see added 'Multi Choice' habit on Today screen$/, async () => {
  await MultiChoiceHabit.addMultiChoiceHabitSucess()
  console.log("Multiple choice habit added");
});

Given(/^I am on the today screen with an added 'Multi Choice' habit$/, () => {
  console.log("Today screen");
});
When(/^I edit that 'Multi Choice' habit and after performing actions$/, async () => {
  await MultiChoiceHabit.editMultiChoiceHabitsToday();
});
Then(/^I should see edited 'Multi Choice' habit on Today screen$/, async () => {
  await MultiChoiceHabit.editMultiChoiceHabitsSucess()
  console.log("Multiple choice habit edited");
});

Given(/^I am on the today screen with an added 'Multi Choice' habit to be deleted$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular 'Multi Choice' habit and after performing delete action$/, async () => {
  await MultiChoiceHabit.deleteMultiChoiceHabitsToday();
  console.log('Success')
});
Then(/^I should not see that 'Multi Choice' habit in today screen$/, async () => {
  await Login.loginSuccess();
  console.log("Multiple choice habit deleted");
});

Given(/^I am on the past screen$/, () => {
  console.log("Past screen");
});
When(/^I select different date filters$/, async () => {
  await Todo.addTodoPast();
  await Past.pastTodoDateFilter();
  console.log('Success')
});
Then(/^I should see added todo list based on selected date filter$/, async () => {
  await Login.loginSuccess();
  console.log("Date filtering done");
});

Given(/^I am on the insight screen$/, () => {
  console.log("Future screen");
});
When(/^I select a todo and mark it as done$/, async () => {
  await Insight.doneTodoPast();
  console.log('Success')
});
Then(/^I should see that todo as completed in insight page$/, async () => {
  await Login.loginSuccess();
  console.log("Insight todo done");
});

Given(/^I am on the past screen1$/, () => {
  console.log("Past screen");
});
When(/^I select different status filters$/, async () => {
  await Past.pastTodoStatusFilter();
  console.log('Success')
});
Then(/^I should see added todo list based on selected status filter$/, async () => {
  await Login.loginSuccess();
  console.log("Status filtering done");
});

Given(/^I am on the past screen2$/, () => {
  console.log("Past screen");
});
When(/^I select different hashtag filters$/, async () => {
  await Past.pastTodoHashtagFilter();

  console.log('Success')
});
Then(/^I should see added todo list based on selected hashtag filter$/, async () => {
  await Login.loginSuccess();
  console.log("Hashtag filtering done");
});

Given(/^I am on the past screen with notes$/, () => {
  console.log("Past screen");
});
When(/^I select different mood filters$/, async () => {
  // await Past.pastNoteMoodFilter();
  console.log('Success')
});
Then(/^I should see added note list based on selected mood filter$/, async () => {
  await Login.loginSuccess();
  console.log("Mood filtering done");
});

Given(/^I am on the past screen with habits$/, () => {
  console.log("Past screen");
});
When(/^I select different habits from filters$/, async () => {
  await Past.pastHabitListFilter();
  console.log('Success')
});
Then(/^I should see selected habit actions only$/, async () => {
  await Login.loginSuccess();
  console.log("Habit filtering done");
});

Given(/^I am on the insight screen with habits$/, () => {
  console.log("Insight screen with habits");
});
When(/^I select a habit and mark it as done for some particular days$/, async () => {
  await Insight.doneHabitPast();

  console.log('Success')
});
Then(/^I should see that habit action as completed in insight page for those particular days$/, async () => {
  await Login.loginSuccess();
  console.log("Insight Habit done");
});

Given(/^I am on the past screen with journals$/, () => {
  console.log("Past screen");
});
When(/^I select different journals from filters$/, async () => {
  await Past.pastJournalListFilter();
  console.log('Success')
});
Then(/^I should see selected journal actions only$/, async () => {

  await Login.loginSuccess();
  console.log("Journal filtering done");
});

Given(/^I am on the insight screen with journals$/, () => {
  console.log("Insight screen with Journals");
});
When(/^I select a journal and edit some details for some particular days$/, async () => {
  await Insight.doneJournalPast();

  console.log('Success')
});
Then(/^I should see that journal in insight page for those particular days$/, async () => {
  await Login.loginSuccess();
  console.log("Insight Journal done");
});

Given(/^I am on the past screen with todo,habits,journals,notes$/, () => {
  console.log("Past screen");
});
When(/^I select different combinations from filters for every list$/, async () => {
  await Past.pastTodoDateStatusHashtagFilter()
  await Past.pastHabitDateStatusHashtagFilter()
  await Past.pastJournalDateStatusHashtagFilter()
  console.log('Success')
});
Then(/^I should see list result based on selected filter combinations only$/, async () => {
  await Todo.deleteTodoPast();
  await YesorNoHabit.deleteHabitPast()
  await Journal.deleteJournalPast()
  await Login.loginSuccess();
  console.log("Journal filtering done");
});

Given(/^I am on the hamburger menu1$/, () => {
  console.log("Hamburger Menu");
});
When(/^I click on the settings$/, async () => {
  await Settings.settings()
  console.log("Settings Pages")
});
Then(/^I should see the settings Page$/, async () => {
  await Settings.SettingsSuccess()
  console.log("Settings page opened");
});

Given(/^I am n the setting screen$/, () => {
  console.log("Settings Screen");
});
When(/^I click on mevolve pin$/, async () => {
  await settings.mevolvePin()
  console.log("Mevolve Pin");
});
Then(/^I should see the entering new pin bottom sheet$/, async () => {
  await settings.mevolvePinSuccess()
  console.log("Mevolve Pin Set");
});

Given(/^I am on the settings screen$/, () => {
  console.log("Themes");
});
When(/^I click on the theme$/, async () => {
  await settings.themes()
  console.log("Themes");
});
Then(/^I see light and dark themes$/, async () => {
  await settings.themesSuccess()
  console.log("Theme Sucess");
});

Given(/^I am on the setting screen1$/, () => {
  console.log("Colour");
});
When(/^I click on the colours$/, async () => {
  await settings.colour()
  console.log("Colours");
});
Then(/^I should see the choose colour popup$/, async () => {
  //await settings.colourSuccess()
  console.log("Multiple Colours");
});

Given(/^I am on the settings screen1$/, () => {
  console.log("Vibration");
});
When(/^I click on the vibration$/, async () => {
  await settings.vibration()
  console.log("Vibration Tap");
});
Then(/^I should see the turned on toggle button$/, async () => {
  await settings.vibrationSuccess()
  console.log("Vibration Toggle on");
});



Given(/^I am on the futute page with habit setup$/, () => {
  console.log("Future page");
});
When(/^I add habits with different date range$/, async () => {
  await Future.futureHabitList();
  console.log('Success')
});
Then(/^I should see added habits under correct upcomimg,active,completed sections correctly$/, async () => {
  await Login.loginSuccess();
  console.log("Habit setup done");
});

Given(/^I am on the futute page with journal setup$/, () => {
  console.log("Future page");
});
When(/^I add journals with different date range$/, async () => {
  await Future.futureJournalList()
  console.log('Success')
});
Then(/^I should see added journals under correct upcomimg,active,completed sections correctly$/, async () => {
  await Login.loginSuccess();
  console.log("Deleted Data till now");
});

Given(/^I am on the insight screen with no data$/, () => {
  console.log("Today screen");
});
When(/^I add,edit,delete any todo,notes,habit and journals$/, async () => {
  await ToastActions.todoToastActions();
  await ToastActions.habitToastActions();
  await ToastActions.journalToastActions();
});
Then(/^I should see corresponding toast messages$/, async () => {
  await Login.loginSuccess();
  console.log("UnscheduledTodo deleted");
});

Given(/^I am on the today screen to add an overdue todo$/, () => {
  console.log("Today screen");
});
When(/^I click on FAB and select 'Add Todo' and add data as some past date and clicked Save$/, async () => {
  await OverdueTodos.addOverdueTodo();
});
Then(/^I should see added todo on Overdue tab of Today screen$/, async () => {
  await OverdueTodos.addOverdueTodoSucess();
  console.log("Add overdue todo done");
});

Given(/^I am on the Overdue tab on Today screen with an added overdue TODO$/, () => {
  console.log("Today screen");
});
When(/^I check or uncheck on the radio button present with that overdue TODO$/, async () => {
  await OverdueTodos.doneOverdueTodo();
  console.log('Success')
});
Then(/^I should see that overdue todo either in past if checked and in overdue tab if unchecked$/, async () => {
  await Login.loginSuccess();
  console.log("Overdue Todo Checked/unchecked");
});

Given(/^I am on the Overdue tab on today screen with an added overdue TODO to be deleted$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular overdue todo and after performing delete action$/, async () => {
  await OverdueTodos.deleteOverdueTodo();
});
Then(/^I should not see that overdue todo in Overdue tab on today screen and on past screen$/, async () => {
  await Login.loginSuccess();
  console.log("OverdueTodo deleted");
});

Given(/^I am on the today screen to add an unscheduled todo$/, () => {
  console.log("Today screen");
});
When(/^I click on FAB and select 'Add Todo' and add data as no date and clicked Save$/, async () => {
  await UnscheduledTodos.addUnscheduledTodo();
});
Then(/^I should see added todo on unscheduled tab of Today screen$/, async () => {
  await UnscheduledTodos.addUnscheduledTodoSucess();
  console.log("Add unscheduled todo done");
});

Given(/^I am on the unscheduled tab on Today screen with an added unscheduled TODO$/, () => {
  console.log("Today screen");
});
When(/^I check or uncheck on the radio button present with that unscheduled TODO and perform filter based on status$/, async () => {
  await UnscheduledTodos.doneUnscheduledTodo();
  await UnscheduledTodos.filterUnscheduledTodo();
  console.log('Success')
});
Then(/^I should see that unscheduled todo in unscheduled tab itself based on action either under 'todo' or 'completed' status$/, async () => {
  await Login.loginSuccess();
  console.log("Unscheduled Todo Checked/unchecked");
});

Given(/^I am on the unscheduled tab on today screen with an added unscheduled TODO to be deleted$/, () => {
  console.log("Today screen");
});
When(/^I click on that particular unscheduled todo and after performing delete action$/, async () => {
  await UnscheduledTodos.deleteUnscheduledTodo();
});
Then(/^I should not see that unscheduled todo in unscheduled tab on today screen$/, async () => {
  await Login.loginSuccess();
  console.log("UnscheduledTodo deleted");
});

Given(/^I am on the List screen$/, () => {
  console.log("Today screen");
});
When(/^I create new list$/, async () => {
  await List.addList();
  console.log("Click List screen");
});
Then(/^I should see the created list$/, async () => {
  await List.addedListSuccess();
  console.log("In List screen");
});

Given(/^I am on the added list page$/, () => {
  console.log("List Rearranged");
});
When(/^I drag list from list page$/, async () => {
  await List.listOrderArrange();
  console.log("List ordered")
})
Then(/^I should see the rearranged lists$/, async () => {
  //await List.addedItemsSuccess();
  console.log("List Rearranged")
})

Given(/^I am on the search text field$/, () => {
  console.log("Searching List");
})
When(/^I pass the lists inputs$/, async () => {
  await List.searchList();
  console.log("Searched List");
})
Then(/^I should see the passed list$/, async () => {
  await List.searchListSuccess();
  console.log("List Searched");
});

Given(/^I am on the added item screen$/, () => {
  console.log("Item Checked");
});
When(/^I check and uncheck the items$/, async () => {
  await List.checkUncheck();
  console.log("Items Checked")
});
Then(/^I should see the checked and unchecked items$/, async () => {
  await List.checkUncheckScussess();
  console.log("Items Check Uncheck")
});

Given(/^I am on the added items screen$/, () => {
  console.log("Swiped Item");
});
When(/^I swipe left side in items$/, async () => {
  await List.swipeDeleteItem();
  console.log("Item Deleted")
});
Then(/^I should see only not deleted item$/, () => {
  console.log("Item Deleted")
});

Given(/^I am on item page$/, () => {
  console.log("Added Items");
});
When(/^I add items in the list$/, async () => {
  await List.addItems();
  console.log("Added Items");
});
Then(/^I should see the created items$/, async () => {
  await List.addedItemSuccess
  console.log("Bottom sheet");
});

Given(/^I am on the added item page$/, () => {
  console.log("Item rearranged");
});
When(/^I drag item from items list$/, async () => {
  await List.itemsOrderRearrange();
  console.log("Item Rerange");
});
Then(/^I should see the rearraged items$/, async () => {
  await List.itemsReorderSuccess();
  console.log("Item Reordered")
});

Given(/^I am on bottom sheet$/, () => {
  console.log("Delete");
});
When(/^I tap on check all,uncheck all, update and delete$/, async () => {
  await List.kebabMenuFunctions();
  console.log("Delete list");
});
Then(/^I should see check all,uncheck all, update and delete$/, async () => {
  //await List.kebabFunctionsSuccess();
  console.log("My list");
});

Given(/^I am on toast message screen$/, () => {
  console.log("Toast Message");
});
When(/^I tap on undo button$/, async () => {
  await List.toastMessage();
  console.log("Toast Message Undo");
});
Then(/^I should see the deleted list again$/, async () => {
  await List.toastMessageSuccess();
  console.log("My Lists");
})

// Given(/^I am on the settings page with Features option$/, () => {
//   console.log("Settings page");
// });
// When(/^I change the features options$/, async () => {
//   await Settings.mevolveFeatures();
//   console.log('Success')
// });
// Then(/^I should see the enabled or disabled features$/, async () => {
//   await Settings.settingsSuccess();
//   console.log("mevolveFeatures done");
// });

// Given(/^I am on the settings page with theme option$/, () => {
//   console.log("Settings page");
// });
// When(/^I change theme from bottomsheet$/, async () => {
//   await Settings.mevolveTheme()
// });
// Then(/^I should see the application with updated theme option$/, async () => {
//   await Settings.settingsSuccess();
//   console.log("mevolveTheme done");
// });

// Given(/^I am on the settings page with color option$/, () => {
//   console.log("Settings page");
// });
// When(/^I change color from the list$/, async () => {
//   await Settings.mevolveColor();
// });
// Then(/^I should see the application with updated color option$/, async () => {
//   await Settings.settingsSuccess();
//   console.log("mevolveColor done");
// });

// Given(/^I am on the settings page with vibration option$/, () => {
//   console.log("Settings page");
// });
// When(/^I enable or disable vibration$/, async () => {
//   await Settings.mevolveVibration();
// });
// Then(/^I should see the application with updated vibration option$/, async () => {
//   //await Login.loginSuccess();
//   console.log("mevolveVibration done");
// });

Given(/^I am on the hamburgerr menu$/, () => {
  console.log("Notification");
});
When(/^I click on notifications$/, async () => {
  await Notification.notification();
  console.log("Notification Settings");
});
Then(/^I should see notifications page$/, async () => {
  await Notification.notificationSuccess();
  console.log("Notification Settings Screen");
});

Given(/^I am on the notificaion settings page$/, () => {
  console.log("Mute");
});
When(/^I click on the mute all devices$/, async () => {
  await Notification.muteAllDevice();
  console.log("Mute All Devices");
});
Then(/^mute bottom sheet appears$/, async () => {
  //await Notification.muteAllDeviceSuccess();
  console.log("Mute All Devices Success");
});

Given(/^I am on the mute bottom sheet$/, () => {
  console.log("Mute for");
});
When(/^I select on the mute timings$/, async () => {
  await Notification.mute();
  console.log("Mute for bottomsheet");
});
Then(/^I should see the mute timing selected$/, async () => {
  await Notification.muteSuccess();
  console.log("Mute for bottomsheet Success");
});

Given(/^I am on the notification settings page1$/, () => {
  console.log("Notification Settings");
});
When(/^I click on mute this device$/, async () => {
  await Notification.muteThisDevice();
  console.log("Mute This Devices");
});
Then(/^mute bottom sheets appears$/, async () => {
  //await Notification.muteThisDeviceSuccess();
  console.log("Mute This Device Success");
});

Given(/^I am on the mutes bottom sheet$/, () => {
  console.log("Bottomsheet");
})
When(/^I select on the mute timing$/, async () => {
  await Notification.mute1();
  console.log("Selected Mute");
});
Then(/^I should see the mute timings selected$/, async () => {
  await Notification.mute1Success();
  console.log("Selected Mute Success");
});

Given(/^I am on the notification settings screen$/, () => {
  console.log("Notification Settings")
});
When(/^ I tap on the daily agenda$/, async () => {
  await Notification.dailyAgenda();
  console.log("Daily Agenda Toggle Turned on");
});
Then(/^I should see the daily agenda toggle button oned$/, async () => {
  await Notification.dailyAgendaSuccess();
  console.log("Daily Agenda");
});

Given(/^I am on the notification settings pages$/, () => {
  console.log("Time");
});
When(/^I tap on the time$/, async () => {
  await Notification.time();
  console.log("Set Time");
});
Then(/^I should see the timer widget$/, async () => {
  await Notification.timeSuccess();
  console.log("Timer Widget");
});

Given(/^I am on the notification settings pages1$/, () => {
  console.log("Notification settings screen");
});
When(/^I tap on the sound$/, async () => {
  await Notification.sound();
  console.log("Notification Settings Sounds");
});
Then(/^I should see the available sounds$/, async () => {
  await Notification.soundSuccess();
  console.log("Different Sound");
});

Given(/^I am on the notification page$/, () => {
  console.log("Notofication Settings");
});
When(/^I click on snooze duration$/, async () => {
  await Notification.snoozeDuration();
  console.log("Notification Snooze");
});
Then(/^I should see the snooze duration timings$/, async () => {
  await Notification.snoozeDurationSuccess();
  console.log("Discard Dialouge Snooze");
});

Given(/^I am on the notification settings page$/, () => {
  console.log("Notification Page");
});
When(/^I tap on the pinned reminder$/, async () => {
  await Notification.pinnedReminder();
  console.log("Toggle Button On");
});
Then(/^I should see the pinned reminder toggle button on$/, async () => {
  await Notification.pinnedReminderSuccess();
  console.log("Toggle button off");
});

Given(/^I am on the hamburger screen now$/, () => {
  console.log("Hamburger")
});
When(/^I click on subscription$/, async () => {
  await Subscription.subscription();
  console.log("Subscription")
});
Then(/^I should see subscription page$/, () => {
  console.log("Subscription page");
});

Given(/^I am on the hamburger3 screen$/, () => {
  console.log("Support")
});
When(/^I click on support chat$/, async () => {
  await SupportChat.supportchat();
  console.log("Support Chat Pages")
});
Then(/^I should see the mevolve support pages$/, async () => {
  await SupportChat.supportChatSuccess();
  console.log("Mevolve Support Chat");
});

Given(/^I am on the opened support page$/, () => {
  console.log("Support Page")
});
When(/^I click on close icon$/, async () => {
  await SupportChat.supportPageClose();
  console.log("Support Chat Pages")
});
Then(/^I should see the Today main page$/, async () => {
  await SupportChat.supportPageCloseSuccess();
  console.log("Mevolve Support Chat");
});

Given(/^I am on the account scession$/, () => {
  console.log("Account");
});
When(/^I tap on the account$/, async () => {
  await Account.account();
  console.log("Account Details");
});
Then(/^I should see account details$/, async () => {
  await Account.accountDetailsSuccess();
  console.log("Account Details page");
});

Given(/^I am on the account details$/, () => {
  console.log("Languages");
});
When(/^I tap on the languages$/, async () => {
  await Account.language();
  console.log("Multiple Languages");
});
Then(/^I should see the multiple languages$/, async () => {
  await Account.languageSuccess();
  console.log("Languages bottom sheet");
});

Given(/^I am on the account details pages$/, () => {
  console.log("Manage Button");
});
When(/^I tap on the manage button$/, async () => {
  await Account.manage();
  console.log("Manage Clicked");
});
Then(/^I should see subscription page$/, async () => {
  await Account.manageSuccess();
  console.log("Manage success");
});

Given(/^I am on the account details page$/, () => {
  console.log("Delete Account");
});
When(/^I tap on delete account button$/, async () => {
  await Account.deleteAccount();
  console.log("Delete Account");
});
Then(/^I should see delete account page$/, async () => {
  await Account.accountDeleteSuccess();
  console.log("Delete account page");
});

Given(/^I am on the hamburger1 menu$/, () => {
  console.log("Hamburger");
});
When(/^I click on report an issue$/, async () => {
  await Report.report();
  console.log("Report an issue");
});
Then(/^I should see report an issue page$/, async () => {
  await Report.reportSuccess();
});

Given(/^I am on the report screen$/, () => {
  console.log("Add Report");
});
When(/^I click on the reason$/, async () => {
  await Report.addReport();
  console.log("Reason Bottom Sheet");
});
Then(/^I should see the selected reason$/, async () => {
  await Report.addReportSuccess();
  console.log("Reason Selected");
});

Given(/^I am on the add report screen$/, () => {
  console.log("Add brief reports")
});
When(/^I click on brief issues$/, async () => {
  await Report.addBriefIssues();
  console.log("Breief Issues Add");
});
Then(/^I should see the today pages$/, async () => {
  await Report.addBriefIssuesSuccess();
  console.log("Today Screen Shown");
});

Given(/^I am on the hamburger2 menu$/, () => {
  console.log("Gallary Tap");
});
When(/^I click on gallary$/, async () => {
  await Gallary.gallary();
});
Then(/^I should see the gallary photos$/, async () => {
  console.log("Gallary Images");
});

Given(/^I am on the hamburger menu$/, () => {
  console.log("Hamburger Menu");
});
When(/^I click on the logout$/, async () => {
  await Logout.logout();
  console.log("Logout Dialouge Box");
});
Then(/^I should see login page$/, async () => {
  await Logout.logoutSuccess();
  console.log("Login Screen");
});