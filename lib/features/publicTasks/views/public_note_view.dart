import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/data/enums/document_type.dart';
import 'package:mevolve/data/enums/share_type.dart';
import 'package:mevolve/data/models/attachment_info.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/note.dart';
import 'package:mevolve/data/models/user/user_resources.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_cubit.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/login/view/login_page.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/notes/cubit/my_notes/notes_operations.dart';
import 'package:mevolve/features/notes/sharing/note_collaborate_sheet.dart';
import 'package:mevolve/features/publicTasks/handle_public_link.dart';
import 'package:mevolve/features/publicTasks/public_data_service.dart';
import 'package:mevolve/features/publicTasks/widgets/rotating_sync_icon.dart';
import 'package:mevolve/features/widgets/me_attachment_widget.dart';
import 'package:mevolve/features/widgets/me_description_editor.dart';
import 'package:mevolve/features/widgets/me_doc_bottom_bar/me_doc_bottom_bar.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_mood_widget.dart';
import 'package:mevolve/features/widgets/me_title_bar.dart';
import 'package:mevolve/features/widgets/me_title_widget.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';
import 'package:mevolve/utilities/tagged_surface.dart';
import 'package:mevolve/utilities/task_component_handler.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';

class PublicNoteView extends StatefulWidget {
  const PublicNoteView({
    super.key,
    required this.note,
    this.uid,
  });

  final Note note;
  final String? uid;

  @override
  State<PublicNoteView> createState() => _PublicNoteViewState();
}

class _PublicNoteViewState extends State<PublicNoteView> {
  late TextEditingController textEditingController;
  final Log _log = MeLogger.getLogger(LogTags.analytics);

  // Refresh functionality
  bool _isRefreshing = false;
  late Note _currentNote;
  late String _noteId;

  @override
  void initState() {
    super.initState();

    // Initialize refresh state first
    _currentNote = widget.note;
    _noteId = widget.note.publicId ?? '';

    textEditingController = TextEditingController(text: _currentNote.title);

    // Send analytics screen view event
    EventFormation()
        .sendPublicScreenViewedEvent(trackAction: TrackAction.publicNote);
  }

  void save() async {
    // Send analytics save event
    EventFormation()
        .sendPublicScreenSaveEvent(trackAction: TrackAction.publicNote);

    if (authenticatedGlobalContext == null) {
      _log.e('homeNavKey.currentContext is null');
      await showLoginFormDialog(context);
      return;
    }
    final UserResources? userResources = authenticatedGlobalContext!
        .read<UserResourcesCubit>()
        .state
        .userResources;
    String? uid = widget.uid ?? userResources?.uid;
    final bool isMyNote = userResources?.uid == _currentNote.id;
    if (isMyNote) {
      Navigator.of(context).pop();
      return;
    }
    // Public tab functionality removed - always clone instead of save
    if (userResources == null) {
      _log.e('userResources is null');
    }
    if (uid == null) {
      _log.e('uid is null');
    }
    if (uid != null) {
      // Clone as private note
      AppBloc appBloc = authenticatedGlobalContext!.read<AppBloc>();
      NotesOperations notesOperations =
          authenticatedGlobalContext!.read<NotesOperations>();

      if (appBloc.state.user != null) {
        // Show loading dialog while cloning
        UtilityMethods.showMeLoaderDialog(context);

        // Clone the note as private
        await notesOperations.cloneOthersPublicNote(
          note: _currentNote,
          ownerEmail: appBloc.state.userData!.userInfo.email,
          ownerName: appBloc.state.userData!.userInfo.name,
        );

        // Close loading dialog and public note view
        await Future.delayed(const Duration(milliseconds: 500));
        if (!mounted) return;
        Navigator.of(context).pop(); // Close loader
        Navigator.of(context).pop(); // Close public note view
      }
    }
  }

  /// Refresh the note data
  Future<void> _refreshNote() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      // Check internet first
      if (!await HandlePublicLink.checkInternetConnection(context)) {
        return;
      }

      if (_noteId.isEmpty) {
        if (mounted) {
          await HandlePublicLink.showLoadFailedDialog(
            context,
            onRetry: () => _refreshNote(),
          );
        }
        return;
      }

      // Refresh the data
      final refreshedNote = await PublicDataService().getCompleteNote(_noteId);

      if (refreshedNote != null && mounted) {
        setState(() {
          _currentNote = refreshedNote;
          // Update the title controller with refreshed data
          textEditingController.text = refreshedNote.title;
        });
      } else {
        if (mounted) {
          await HandlePublicLink.showLoadFailedDialog(
            context,
            onRetry: () => _refreshNote(),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        await HandlePublicLink.showLoadFailedDialog(
          context,
          onRetry: () => _refreshNote(),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final UserResources? userResources = authenticatedGlobalContext
        ?.read<UserResourcesCubit>()
        .state
        .userResources;
    return Scaffold(
      body: SafeArea(
        child: MeDescriptionEditor(
          autoFocus: false,
          descriptionTapRegionGroupId: '_textFieldsTapRegionGroupId',
          isDisabled: true,
          isDisabledOnTap: null,
          isDisabledForMembers: true,
          backgroundColor: colorScheme.color5,
          description: _currentNote.description,
          isBottomSheetEditor: false,
          maxTextLength: 2000,
          placeHolder: MeTranslations.instance.screen_common_description.text,
          onDescriptionChanged: (String? description) {},
          appBar: PreferredSize(
            preferredSize: const Size.fromHeight(
              SizeConstants.appBarSecondaryHeight,
            ),
            child: MeTitleBar(
              isBorderNonCurved: true,
              title: MeTranslations.instance.screen_common_note,
              subtitle: _currentNote.noteUpdatedAt == null
                  ? _currentNote.localUpdatedAt.formattedDayDate()
                  : _currentNote.noteUpdatedAt!.formattedDayDate(),
              titleButton: Row(
                children: [
                  // Refresh button with rotating animation
                  Visibility(
                    visible: false,
                    child: MeIconButton(
                      isCircularIconButton: true,
                      iconPath: _isRefreshing ? null : Assets.svg.icResync.path,
                      icon: _isRefreshing
                          ? RotatingSyncIcon(
                              iconColor: colorScheme.color8,
                              iconSize: const SizedBox(
                                width:
                                    SizeConstants.iconButtonIconContainerSize,
                                height:
                                    SizeConstants.iconButtonIconContainerSize,
                              ),
                            )
                          : null,
                      iconColor: colorScheme.color12,
                      iconSize: const SizedBox(
                        height: 14,
                        width: 14,
                      ),
                      iconContainerSize: const SizedBox(
                        width: SizeConstants.iconButtonIconContainerSize,
                        height: SizeConstants.iconButtonIconContainerSize,
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 12,
                      ),
                      onPressed: () => _refreshNote(),
                    ),
                  ),
                  MeIconButton(
                    key: const ValueKey(
                      'collaborateBtn',
                    ),
                    isCircularIconButton: true,
                    iconPath: Assets.svg.triPeople.path,
                    buttonColor: colorScheme.color9,
                    iconColor: colorScheme.color12,
                    iconSize: const SizedBox(
                      height: 14,
                      width: 14,
                    ),
                    iconContainerSize: const SizedBox(
                      width: SizeConstants.iconButtonIconContainerSize,
                      height: SizeConstants.iconButtonIconContainerSize,
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 12,
                    ),
                    onPressed: () async {
                      showMeScrollableModalBottomSheet(
                        bottomSheetLevel: 1,
                        useSafeArea: true,
                        builder: (_, __) {
                          return NoteCollaborateSheetNonOwner(
                            noteId: _currentNote.id,
                            type: ShareType.publicTask,
                            note: _currentNote,
                          );
                        },
                      );
                    },
                  ),
                ],
              ),
              onCloseClick: () {
                Navigator.of(context).maybePop();
              },
            ),
          ),
          bottomNavigationBar: (
            Widget? descriptionStt,
            Widget? descriptionRichText,
            bool isDescriptionRichTextOn,
            bool isDescriptionSttListening,
          ) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (_currentNote.attachments.isNotEmpty)
                  PreventEdit(
                    prevent: false,
                    child: MeAttachmentWidget(
                      isDisabled: false,
                      isNew: true,
                      isPublic: true,
                      attachments: _currentNote.attachments,
                      allowDelete: false,
                      onAttachmentChanged: (List<MeAttachmentInfo>? data) {},
                      taskTitle: MeString(_currentNote.title),
                      taskId: _currentNote.id,
                      //? MeString
                      encryptionData: null,
                      featureType: AttachmentFeatureType.note,
                      ownerUid: _currentNote.uid,
                    ),
                  ),
                TaggedChild(
                  tag: '_textFieldsTapRegionGroupId',
                  child: MeDocBottomBar(
                    taskTitle: _currentNote.title,
                    disableUserTag: true,
                    preventTap: () {},
                    featureType: AttachmentFeatureType.note,
                    bottomSheetLevel: 1,
                    docType: DocumentType.note,
                    widgetList: const [],
                    isSaveEnabled: () {
                      final bool isNoteOwner =
                          widget.note.uid == userResources?.uid;

                      final bool shouldEnable = !isNoteOwner;

                      return shouldEnable;
                    }(),
                    saveButtonTitle:
                        MeTranslations.instance.screen_noteAction_saveNote,
                    description: _currentNote.description,
                    onDescriptionChanged: (String? data) {},
                    onTagsChanged: (List<String>? data) {},
                    tags: const [],
                    attachments: _currentNote.attachments,
                    onAttachmentsChanged: (List<MeAttachmentInfo>? data) {},
                    emotion: _currentNote.emotion,
                    onEmotionChanged: (data) {},
                    saveFunction: () {
                      save();
                    },
                    descriptionStt: descriptionStt,
                    descriptionRichText: descriptionRichText,
                    isDescriptionRichTextOn: isDescriptionRichTextOn,
                    isDescriptionSttListening: isDescriptionSttListening,
                    titleStt: null,
                    isTitleSttListening: false,
                  ),
                ),
              ],
            );
          },
          bodyAboveDescriptionWidgets: [
            const SizedBox(height: 8),
            PreventEdit(
              prevent: true,
              child: TaggedChild(
                tag: '_textFieldsTapRegionGroupId',
                child: MeTitleWidget(
                  key: const ValueKey('addNoteTitle'),
                  textEditingController: textEditingController,
                  isDisabledTask: false,
                  docType: DocumentType.note,
                  title: MeString(_currentNote.title),
                  isSaveEnabled: true,
                  hintText: MeTranslations
                      .instance.bottomSheet_noteAction_titlePlaceholder,
                  inputHintText: MeTranslations
                      .instance.bottomSheet_noteAction_titlePlaceholder,
                  onTitleChanged: (data) {},
                  titleStyle: MeFontStyle.A8,
                  hintTextStyle: MeFontStyle.A7,
                  onSaveChanged: (data) {},
                  showIcon: false,
                  keyboardType: TextInputType.text,
                  onInPlaceTextFieldFocusChange: (bool value) {},
                  textInputMaxLength: 100,
                  isInPlaceTextField: true,
                ),
              ),
            ),
          ],
          bodyBelowDescriptionTextShouldTakeMaxHeight: true,
          bodyBelowDescriptionText: const [],
          bodyBelowDescriptionWidgets: [
            if (_currentNote.emotion != null)
              KeyboardVisibilityBuilder(
                builder: (context, isKeyboardVisible) {
                  if (isKeyboardVisible) {
                    return const SizedBox.shrink();
                  }
                  return PreventEdit(
                    prevent: true,
                    child: AnimatedScale(
                      duration: const Duration(milliseconds: 300),
                      scale: _currentNote.emotion != null ? 1 : 0,
                      child: Align(
                        alignment: Alignment.centerLeft,
                        child: MeActionEmojiWidget(
                          selectedEmoji: _currentNote.emotion ?? 5,
                          onEmojiSelected: (val) {},
                        ),
                      ),
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}
