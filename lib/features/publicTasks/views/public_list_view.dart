import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/constants/animation_constants.dart';
import 'package:mevolve/constants/app_strings.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/list_item_add_on_type.dart';
import 'package:mevolve/data/enums/share_type.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/list_item.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/user/user_resources.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_cubit.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/login/view/login_page.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/lists/cubit/my_list/list_operations.dart';
import 'package:mevolve/features/lists/widgets/colaborate_sheet/list_collaborate_sheet.dart';
import 'package:mevolve/features/lists/widgets/list_filter_chip.dart';
import 'package:mevolve/features/publicTasks/handle_public_link.dart';
import 'package:mevolve/features/publicTasks/public_data_service.dart';
import 'package:mevolve/features/publicTasks/widgets/rotating_sync_icon.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/empty_state_widget.dart';
import 'package:mevolve/features/widgets/filter_widgets/filter_widget_wrapper.dart';
import 'package:mevolve/features/widgets/filter_widgets/search_filter_input_chip.dart';
import 'package:mevolve/features/widgets/go_to_top_button.dart';
import 'package:mevolve/features/widgets/horizontal_scroll_row.dart';
import 'package:mevolve/features/widgets/me_button.dart';
import 'package:mevolve/features/widgets/me_checkbox.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_list_tile.dart';
import 'package:mevolve/features/widgets/me_scrollbar.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/task_component_handler.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';

class PublicListView extends StatefulWidget {
  const PublicListView({
    super.key,
    required this.listData,
    this.uid,
  });

  final ListData listData;
  final String? uid;

  @override
  State<PublicListView> createState() => _PublicListViewState();
}

class _PublicListViewState extends State<PublicListView>
    with SingleTickerProviderStateMixin {
  bool showFilterRow = false;
  bool isFilterApplied = false;
  final ScrollController _scrollController = ScrollController();
  late AnimationController _animationController;
  List<ListItem> listItems = [];

  // Refresh functionality
  bool _isRefreshing = false;
  late ListData _currentListData;
  late String _listId;

  @override
  void initState() {
    super.initState();

    // Initialize refresh state
    _currentListData = widget.listData;
    _listId = widget.listData.publicId ?? '';
    listItems = widget.listData.validListItems;

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: AnimationConstants.hideBottomBarOnScrollDuration,
      ),
      value: 1,
    );
    updateFilterState();

    // Send analytics screen view event
    EventFormation()
        .sendPublicScreenViewedEvent(trackAction: TrackAction.publicList);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void filter() {
    List<ListItem> filteredList = _currentListData.validListItems;
    if (_searchController.text.isNotEmpty) {
      filteredList = filteredList.where((element) {
        return element.item
            .toLowerCase()
            .contains(_searchController.text.toLowerCase());
      }).toList();
    }
    if (isCompleteSelected != null) {
      if (isCompleteSelected == true) {
        filteredList = filteredList.where((element) => element.done).toList();
      } else {
        filteredList = filteredList.where((element) => !element.done).toList();
      }
    }
    listItems = filteredList;
    updateFilterState();
    setState(() {});
  }

  void updateFilterState() {
    int visibleTasksLength = listItems.length;
    bool isEmptyState = visibleTasksLength == 0 && !isFilterApplied;
    // Hide filter when there are no items and no filter is applied
    if (isEmptyState && showFilterRow) {
      showFilterRow = false;
    }
    // Also hide filter row if original list is empty (not just filtered result)
    if (_currentListData.validListItems.isEmpty &&
        !isFilterApplied &&
        showFilterRow) {
      showFilterRow = false;
    }
  }

  void save() async {
    // Send analytics save event
    EventFormation()
        .sendPublicScreenSaveEvent(trackAction: TrackAction.publicList);

    //homeNavKey.currentContext == null means user is not logged in
    if (authenticatedGlobalContext == null) {
      await showLoginFormDialog(context);
      return;
    }

    final UserResources? userResources = authenticatedGlobalContext!
        .read<UserResourcesCubit>()
        .state
        .userResources;
    final bool isMyList = userResources?.uid == _currentListData.uid;
    if (isMyList) {
      Navigator.of(context).pop();
      return;
    }

    // Clone as private list
    String? uid = widget.uid ?? userResources?.uid;
    AppBloc appBloc = authenticatedGlobalContext!.read<AppBloc>();
    ListOperations listCubit =
        authenticatedGlobalContext!.read<ListOperations>();

    if (uid != null && userResources != null && appBloc.state.user != null) {
      // Show loading dialog while cloning
      UtilityMethods.showMeLoaderDialog(context);

      // Clone the list as private
      await listCubit.cloneOthersPublicList(
        list: _currentListData,
        ownerEmail: appBloc.state.userData!.userInfo.email,
        ownerName: appBloc.state.userData!.userInfo.name,
      );

      // adding a delay to show the loader dialog
      // for a while before closing it
      // so that the list would be saved first and then we can navigate to the page
      await Future.delayed(const Duration(milliseconds: 500));
      if (!mounted) return;
      Navigator.of(context).pop(); // Close loader
      Navigator.of(context).pop(); // Close public list view
    }
  }

  final TextEditingController _searchController = TextEditingController();
  bool? isCompleteSelected;

  /// Refresh the list data
  Future<void> _refreshList() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      // Check internet first
      if (!await HandlePublicLink.checkInternetConnection(context)) {
        return;
      }

      if (_listId.isEmpty) {
        if (mounted) {
          await HandlePublicLink.showLoadFailedDialog(
            context,
            onRetry: () => _refreshList(),
          );
        }
        return;
      }

      // Refresh the data
      final refreshedList = await PublicDataService().getCompleteList(_listId);

      if (refreshedList != null && mounted) {
        setState(() {
          _currentListData = refreshedList;
          listItems = refreshedList.validListItems;
          // Reset filters on refresh
          isCompleteSelected = null;
          _searchController.clear();
          isFilterApplied = false;
          showFilterRow = false;
        });
        // Update filter state after refresh
        updateFilterState();
      } else {
        if (mounted) {
          await HandlePublicLink.showLoadFailedDialog(
            context,
            onRetry: () => _refreshList(),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        await HandlePublicLink.showLoadFailedDialog(
          context,
          onRetry: () => _refreshList(),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final UserResources? userResources = authenticatedGlobalContext
        ?.read<UserResourcesCubit>()
        .state
        .userResources;

    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            DialogAndBottomSheetAppBar(
              showRoundedTop: false,
              showCloseButton: true,
              widgetTitle: MeText(
                text: MeString.fromVariable(_currentListData.title),
                meFontStyle: MeFontStyle.A12,
              ),
              suffixActions: [
                Row(
                  children: [
                    // Refresh button with rotating animation
                    Visibility(
                      visible: false,
                      child: MeIconButton(
                        isCircularIconButton: true,
                        iconPath:
                            _isRefreshing ? null : Assets.svg.icResync.path,
                        icon: _isRefreshing
                            ? RotatingSyncIcon(
                                iconColor: colorScheme.color8,
                                iconSize: const SizedBox(
                                  width:
                                      SizeConstants.iconButtonIconContainerSize,
                                  height:
                                      SizeConstants.iconButtonIconContainerSize,
                                ),
                              )
                            : null,
                        iconColor: colorScheme.color12,
                        iconSize: const SizedBox(
                          height: 14,
                          width: 14,
                        ),
                        iconContainerSize: const SizedBox(
                          width: SizeConstants.iconButtonIconContainerSize,
                          height: SizeConstants.iconButtonIconContainerSize,
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 12,
                        ),
                        onPressed: () => _refreshList(),
                      ),
                    ),
                    MeIconButton(
                      key: const ValueKey(
                        'collaborateBtn',
                      ),
                      isCircularIconButton: true,
                      iconPath: Assets.svg.triPeople.path,
                      buttonColor: colorScheme.color9,
                      iconColor: colorScheme.color12,
                      iconSize: const SizedBox(
                        height: 14,
                        width: 14,
                      ),
                      iconContainerSize: const SizedBox(
                        width: SizeConstants.iconButtonIconContainerSize,
                        height: SizeConstants.iconButtonIconContainerSize,
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 12,
                      ),
                      onPressed: () async {
                        showMeScrollableModalBottomSheet(
                          bottomSheetLevel: 1,
                          useSafeArea: true,
                          builder: (_, __) {
                            return ListCollaborateSheetNonOwner(
                              listId: _currentListData.id,
                              type: ShareType.publicTask,
                              list: _currentListData,
                            );
                          },
                        );
                      },
                    ),
                    // Hide filter button when list is empty and no filter is applied
                    listItems.isEmpty && !isFilterApplied
                        ? const SizedBox.shrink()
                        : MeIconButton(
                            key: const ValueKey(
                              'filterBtn',
                            ),
                            isCircularIconButton: true,
                            iconPath: showFilterRow
                                ? Assets.svg.filterRowVisible.path
                                : Assets.svg.filterRowHidden.path,
                            buttonColor: colorScheme.color9,
                            iconColor: colorScheme.color12,
                            iconContainerSize: const SizedBox(
                              width: SizeConstants.iconButtonIconContainerSize,
                              height: SizeConstants.iconButtonIconContainerSize,
                            ),
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 12,
                            ),
                            onPressed: () {
                              if (isFilterApplied && showFilterRow) {
                                showDialog<bool>(
                                  useRootNavigator: false,
                                  context: context,
                                  builder: (
                                    BuildContext childContext,
                                  ) {
                                    return MeDialog(
                                      tertiaryText: MeTranslations.instance
                                          .overlay_filterApplied_proceed,
                                      secondaryText: MeTranslations
                                          .instance.screen_common_buttonCancel,
                                      title: MeTranslations
                                          .instance.overlay_filterApplied_title,
                                      description: MeTranslations.instance
                                          .overlay_filterApplied_content,
                                      showBottomDivider: false,
                                      secondaryOnTap: () {
                                        Navigator.of(
                                          childContext,
                                        ).pop();
                                      },
                                      tertiaryOnTap: () {
                                        isFilterApplied = false;
                                        showFilterRow = false;
                                        setState(() {});
                                        Navigator.of(
                                          childContext,
                                        ).pop();
                                      },
                                    );
                                  },
                                );
                                return;
                              } else {
                                // Check if filter can be shown before toggling
                                int visibleTasksLength = listItems.length;
                                bool isEmptyState =
                                    visibleTasksLength == 0 && !isFilterApplied;
                                if (!isEmptyState) {
                                  showFilterRow = !showFilterRow;
                                  setState(() {});
                                }
                              }
                            },
                          ),
                  ],
                ),
              ],
            ),
            showFilterRow
                ? FilterWidgetWrapper(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Expanded(
                          child: HorizontalScrollRow(
                            spacing: 8,
                            scrollController: _scrollController,
                            horizontalPadding: 16,
                            key: const ValueKey('myListFilter'),
                            data: [
                              SearchFilterInputChip(
                                searchQuery: _searchController.text,
                                onSearchQueryChanged: (value) {
                                  String? val = value;
                                  if (val == null) {
                                    listItems = _currentListData.validListItems;
                                    _searchController.clear();
                                  }
                                  _searchController.text = value ?? '';
                                  isFilterApplied =
                                      (value?.isNotEmpty ?? false) ||
                                          isCompleteSelected != null;
                                  filter();
                                },
                              ),
                              ListFilterChip(
                                title: MeTranslations
                                    .instance.bottomSheet_listItems_unchecked,
                                isSelected: isCompleteSelected == false,
                                onPressed: () {
                                  setState(() {
                                    if (isCompleteSelected == false) {
                                      isCompleteSelected = null;
                                    } else {
                                      isCompleteSelected = false;
                                    }
                                    isFilterApplied =
                                        _searchController.text.isNotEmpty ||
                                            isCompleteSelected != null;
                                    filter();
                                  });
                                },
                              ),
                              ListFilterChip(
                                title: MeTranslations
                                    .instance.bottomSheet_listItems_checked,
                                isSelected: isCompleteSelected == true,
                                onPressed: () {
                                  setState(() {
                                    if (isCompleteSelected == true) {
                                      isCompleteSelected = null;
                                    } else {
                                      isCompleteSelected = true;
                                    }
                                    isFilterApplied =
                                        _searchController.text.isNotEmpty ||
                                            isCompleteSelected != null;
                                    filter();
                                  });
                                },
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  )
                : const SizedBox.shrink(),
            Expanded(
              child: listItems.isEmpty
                  ? Center(
                      child: EmptyStateWidget(
                        imagePath: AppStrings.listOfItemsEmptyIllustration(
                          colorScheme: colorScheme,
                        ),
                        imageTextStyle: MeFontStyle.C8,
                        imageText: MeTranslations
                            .instance.bottomSheet_listItems_emptyScreenContent,
                      ),
                    )
                  : NotificationListener<UserScrollNotification>(
                      onNotification: (notification) {
                        if (notification.direction == ScrollDirection.forward) {
                          _animationController.forward();
                        }
                        if (notification.direction == ScrollDirection.reverse) {
                          _animationController.reverse();
                        }
                        if (notification.metrics.pixels ==
                            notification.metrics.maxScrollExtent) {
                          _animationController.forward();
                        }
                        return true;
                      },
                      child: Stack(
                        children: [
                          MeScrollbar(
                            controller: _scrollController,
                            length: listItems.length,
                            child: ListView.builder(
                              controller: _scrollController,
                              itemCount: listItems.length,
                              itemBuilder: (context, index) {
                                ListItem listItem = listItems[index];
                                return Material(
                                  key: UniqueKey(),
                                  child: PreventEdit(
                                    prevent: true,
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        MeListTile(
                                          title: MeText(
                                            text: MeString.fromVariable(
                                              listItem.item,
                                            ),
                                            meFontStyle: MeFontStyle.D8,
                                            textOverflow: TextOverflow.visible,
                                          ),
                                          subtitle: listItem.description !=
                                                      null &&
                                                  listItem
                                                      .description!.isNotEmpty
                                              ? MeText(
                                                  text: MeString.fromVariable(
                                                    listItem.description!,
                                                  ),
                                                  meFontStyle: MeFontStyle.F7,
                                                  textOverflow:
                                                      TextOverflow.visible,
                                                )
                                              : null,
                                          trailing: _currentListData
                                                      .addOnType ==
                                                  ListItemAddOnType.none
                                              ? const SizedBox.shrink()
                                              : _currentListData.addOnType ==
                                                      ListItemAddOnType
                                                          .customText
                                                  ? MeText(
                                                      text:
                                                          MeString.fromVariable(
                                                        listItem.customText
                                                                    ?.isEmpty ??
                                                                true
                                                            ? '---'
                                                            : listItem
                                                                .customText!,
                                                      ),
                                                      meFontStyle:
                                                          MeFontStyle.D7,
                                                      textOverflow:
                                                          TextOverflow.visible,
                                                    )
                                                  : SizedBox(
                                                      child: MeCheckBox(
                                                        value: listItem.done,
                                                        isDisabled: false,
                                                        isDue: false,
                                                        borderWidth: SizeConstants
                                                            .checkBoxBorderWidth,
                                                        // Removed to match rest of the list checkbox
                                                        // checkboxUncheckedBorderColor:
                                                        //     colorScheme.color7,
                                                        // checkboxUncheckedColor:
                                                        //     colorScheme.color3,
                                                      ),
                                                    ),
                                        ),
                                        Divider(
                                          height: 1,
                                          thickness: 1,
                                          color: context.meColorScheme.color6,
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            ),
                          ),
                          GoToTopButton(
                            scrollController: _scrollController,
                          ),
                        ],
                      ),
                    ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(
        child: Container(
          color: colorScheme.color5,
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          child: Row(
            children: [
              const Spacer(),
              MeButton(
                color: null,
                disabledColor: null,
                fontStyle: (_currentListData.uid == userResources?.uid
                    ? MeFontStyle.C4
                    : MeFontStyle.C35),
                title: MeTranslations.instance.screen_listItem_saveList,
                buttonType: ButtonType.text,
                onPressed: (_currentListData.uid == userResources?.uid
                    ? null
                    : () {
                        save();
                      }),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
