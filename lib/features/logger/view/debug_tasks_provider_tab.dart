import 'dart:convert';
import 'package:mevolve/data/models/calendar_event_setup.dart';
import 'package:mevolve/data/models/habit/habit_setup.dart';
import 'package:mevolve/data/models/journal_setup.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/money_tracker/money_tracker_transaction.dart';
import 'package:mevolve/data/models/note.dart';
import 'package:mevolve/data/models/todo.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/features/tasks_providers/lists/main_lists_cubit.dart';
import 'package:mevolve/features/tasks_providers/money_tracker/money_trackers_cubit.dart';
import 'package:mevolve/features/tasks_providers/notes/main_notes_cubit.dart';
import 'package:universal_io/io.dart';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/past/cubit/past_habit_cubit/past_habits_actions_cubit.dart';
import 'package:mevolve/features/past/cubit/past_journal_cubit/past_journals_actions_cubit.dart';
import 'package:mevolve/features/past/cubit/past_todo_cubit/past_todos_actions_cubit.dart';
import 'package:mevolve/features/tasks_providers/calendar_integrations/calendar_integrations_cubit.dart';
import 'package:mevolve/features/tasks_providers/calendar_integrations/calendar_events_entries_cubit.dart';
import 'package:mevolve/features/tasks_providers/habits/habits_cubit.dart';
import 'package:mevolve/features/tasks_providers/habits/habits_entries_cubit.dart';
import 'package:mevolve/features/tasks_providers/journals/journals_cubit.dart';
import 'package:mevolve/features/tasks_providers/journals/journals_entries_cubit.dart';
import 'package:mevolve/features/tasks_providers/todos/todos_cubit.dart';
import 'package:mevolve/features/tasks_providers/todos/todos_entries_cubit.dart';
import 'package:mevolve/features/today/cubit/overdue_todo_cubit/overdue_todos_actions_cubit.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/list_option_sheet.dart'
    show getSharePositionWithFallback;

class DebugTasksProviderTab extends StatelessWidget {
  const DebugTasksProviderTab({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        const MeText(
          text: MeString('Tasks Providers'),
          meFontStyle: MeFontStyle.A8,
        ),
        ListTile(
          title: const MeText(
            text: MeString('TodosCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<TodosCubit, TodosState>(
            builder: (context, state) {
              // Get scheduled todos by subtracting unscheduled todos from total
              final scheduledTodosCount = state.taskIdsToTasks.keys.length -
                  state.unscheduledTodos.length;

              // Filter todos that have a non-empty repeat
              final repeatTodosCount = state.taskIdsToTasks.values
                  .where((todo) => todo.repeat.isNotEmpty)
                  .length;

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeText(
                    text: MeString(
                      'Total Todos in Cubit: ${state.taskIdsToTasks.keys.length}\n'
                      'Scheduled Todos in Cubit: $scheduledTodosCount\n'
                      'Unscheduled Todos in Cubit: ${state.unscheduledTodos.length}\n'
                      'Repeat Todos in Cubit: $repeatTodosCount',
                    ),
                    meFontStyle: MeFontStyle.I8,
                  ),
                  FutureBuilder<List<Todo>>(
                    future: DatabaseRepository().getAllTodos(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const MeText(
                          text: MeString('Loading leftover todos...'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasError) {
                        return const MeText(
                          text: MeString('Error loading leftover todos'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasData) {
                        final todos = snapshot.data ?? [];
                        final existingIds = state.taskIdsToTasks.keys;
                        final newSetups = todos
                            .where((todo) => !existingIds.contains(todo.id))
                            .toList();

                        if (newSetups.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        return MeText(
                          text: MeString(
                            'Todos found in DB but not in Cubit: ${newSetups.length}\n'
                            'Note: This may not be 0 even if the app works correctly.',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else {
                        return const MeText(
                          text: MeString('No leftover todos found'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      }
                    },
                  ),
                ],
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('TodosCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<TodosCubit, TodosState>(
            builder: (context, state) {
              return MeText(
                text: MeString(
                  'Total Todos in Cubit: ${state.taskIdsToTasks.keys.length}\nScheduled Todos in Cubit: ${state.taskIdsToTasks.keys.length - state.unscheduledTodos.length}\nUnscheduled Todos in Cubit: ${state.unscheduledTodos.length}',
                ),
                meFontStyle: MeFontStyle.I8,
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('TodosEntriesCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<TodosEntriesCubit, TodosEntriesState>(
            builder: (context, state) {
              return MeText(
                text: MeString(
                  'Todos entries in Cubit: ${state.dateToTasksIDMap.values.fold(0, (sum, list) => sum + list.length)}',
                ),
                meFontStyle: MeFontStyle.I8,
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('HabitsCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<HabitsCubit, HabitsState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeText(
                    text: MeString(
                      'Total Habit Setups in Cubit: ${state.setupsIDToASetup.keys.length}\n'
                      'Total Habits Actions in Cubit: ${state.actionsIDToActions.keys.length}',
                    ),
                    meFontStyle: MeFontStyle.I8,
                  ),
                  FutureBuilder<List<HabitSetup>>(
                    future: DatabaseRepository().getAllHabitSetups(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const MeText(
                          text: MeString('Loading leftover habit setups...'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasError) {
                        return const MeText(
                          text: MeString('Error loading leftover habit setups'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasData) {
                        final habitSetups = snapshot.data ?? [];
                        final existingIds = state.setupsIDToASetup.keys;
                        final newSetups = habitSetups
                            .where((setup) => !existingIds.contains(setup.id))
                            .toList();

                        if (newSetups.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        // Dev note: This will not be 0 if any task in DB is not suitable for top level cubit to pick.
                        // Can happen if a task is perma deleted but not removed from DB.
                        return MeText(
                          text: MeString(
                            'Habits Setups found in DB but not in Cubit: ${newSetups.length}\n'
                            'Note: This may not be 0 even if the app works correctly.',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else {
                        return const MeText(
                          text: MeString('No leftover habit setups found'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      }
                    },
                  ),
                ],
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('HabitSetupsEntriesCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle:
              BlocBuilder<HabitSetupsEntriesCubit, HabitSetupsEntriesState>(
            builder: (context, state) {
              return InkWell(
                onTap: () async {
                  File file = await writeMapToFile(state.dateToTasksIDMap);

                  // Share the file
                  if (!context.mounted) return;
                  final shareRect = getSharePositionWithFallback(context);
                  await Share.shareXFiles(
                    [XFile(file.path)],
                    text: 'Here is the map content',
                    sharePositionOrigin: shareRect,
                  );
                },
                child: MeText(
                  text: MeString(
                    'Habits entries in Cubit: ${state.dateToTasksIDMap.values.fold(0, (sum, list) => sum + list.length)}',
                  ),
                  meFontStyle: MeFontStyle.I8,
                ),
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('JournalsCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<JournalsCubit, JournalsState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeText(
                    text: MeString(
                      'Total Journal Setups in Cubit: ${state.setupsIDToASetup.keys.length}\n'
                      'Total Journals Actions in Cubit: ${state.actionsIDToActions.keys.length}',
                    ),
                    meFontStyle: MeFontStyle.I8,
                  ),
                  FutureBuilder<List<JournalSetup>>(
                    future: DatabaseRepository().getAllJournalSetups(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const MeText(
                          text: MeString('Loading leftover journal setups...'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasError) {
                        return const MeText(
                          text:
                              MeString('Error loading leftover journal setups'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasData) {
                        final journalSetups = snapshot.data ?? [];
                        final existingIds = state.setupsIDToASetup.keys;
                        final newSetups = journalSetups
                            .where((setup) => !existingIds.contains(setup.id))
                            .toList();

                        if (newSetups.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        // Dev note: This will not be 0 if any task in DB is not suitable for top level cubit to pick.
                        // Can happen if a task is perma deleted but not removed from DB.
                        return MeText(
                          text: MeString(
                            'Journal Setups found in DB but not in Cubit: ${newSetups.length}\n'
                            'Note: This may not be 0 even if the app works correctly.',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else {
                        return const MeText(
                          text: MeString('No leftover journal setups found'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      }
                    },
                  ),
                ],
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('JournalSetupsEntriesCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle:
              BlocBuilder<JournalSetupsEntriesCubit, JournalSetupsEntriesState>(
            builder: (context, state) {
              return InkWell(
                onTap: () async {
                  File file = await writeMapToFile(state.dateToTasksIDMap);

                  // Share the file
                  if (!context.mounted) return;
                  final shareRect = getSharePositionWithFallback(context);
                  await Share.shareXFiles(
                    [XFile(file.path)],
                    text: 'Here is the map content',
                    sharePositionOrigin: shareRect,
                  );
                },
                child: MeText(
                  text: MeString(
                    'Journals entries in Cubit: ${state.dateToTasksIDMap.values.fold(0, (sum, list) => sum + list.length)}',
                  ),
                  meFontStyle: MeFontStyle.I8,
                ),
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('CalendarEventsCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle:
              BlocBuilder<CalendarIntegrationsCubit, CalendarIntegrationsState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeText(
                    text: MeString(
                      'Total CalendarEvents in Cubit: ${state.setupsIDToASetup.keys.length}\n'
                      'Total Journals Actions in Cubit: ${state.actionsIDToActions.keys.length}',
                    ),
                    meFontStyle: MeFontStyle.I8,
                  ),
                  FutureBuilder<List<CalendarEventSetup>>(
                    future: DatabaseRepository().getAllCalendarEventSetups(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const MeText(
                          text: MeString('Loading leftover calendar events...'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasError) {
                        return const MeText(
                          text: MeString(
                            'Error loading leftover calendar events',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasData) {
                        final calendarEvents = snapshot.data ?? [];
                        final existingIds = state.setupsIDToASetup.keys;
                        final leftoverEvents = calendarEvents
                            .where((event) => !existingIds.contains(event.id))
                            .toList();

                        if (leftoverEvents.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        return MeText(
                          text: MeString(
                            'CalendarEvents found in DB but not in Cubit: ${leftoverEvents.length}\n'
                            'Note: This may not be 0 even if the app works correctly.',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else {
                        return const MeText(
                          text: MeString('No leftover calendar events found'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      }
                    },
                  ),
                ],
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('CalendarEventsEntriesCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<CalendarEventsEntriesCubit,
              CalendarEventsEntriesState>(
            builder: (context, state) {
              return InkWell(
                onTap: () async {
                  File file = await writeMapToFile(state.dateToTasksIDMap);

                  // Share the file
                  if (!context.mounted) return;
                  final shareRect = getSharePositionWithFallback(context);
                  await Share.shareXFiles(
                    [XFile(file.path)],
                    text: 'Here is the map content',
                    sharePositionOrigin: shareRect,
                  );
                },
                child: MeText(
                  text: MeString(
                    'CalendarEvents entries in Cubit: ${state.dateToTasksIDMap.values.fold(0, (sum, list) => sum + list.length)}',
                  ),
                  meFontStyle: MeFontStyle.I8,
                ),
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('NotesCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<MainNotesCubit, MainNotesState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeText(
                    text: MeString(
                      'Total Notes in Cubit: ${state.taskIdsToTasks.keys.length}',
                    ),
                    meFontStyle: MeFontStyle.I8,
                  ),
                  FutureBuilder<List<Note>>(
                    future: DatabaseRepository().getAllNotes(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const MeText(
                          text: MeString('Loading leftover notes...'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasError) {
                        return const MeText(
                          text: MeString('Error loading leftover notes'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasData) {
                        final notes = snapshot.data ?? [];
                        final existingIds = state.taskIdsToTasks.keys;
                        final leftoverNotes = notes
                            .where((note) => !existingIds.contains(note.id))
                            .toList();

                        if (leftoverNotes.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        return MeText(
                          text: MeString(
                            'Notes found in DB but not in Cubit: ${leftoverNotes.length}\n'
                            'Note: This may not be 0 even if the app works correctly.',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else {
                        return const MeText(
                          text: MeString('No leftover notes found'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      }
                    },
                  ),
                ],
              );
            },
          ),
        ),

        ListTile(
          title: const MeText(
            text: MeString('ListsCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<MainListsCubit, MainListsState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeText(
                    text: MeString(
                      'Total Lists in Cubit: ${state.taskIdsToTasks.keys.length}',
                    ),
                    meFontStyle: MeFontStyle.I8,
                  ),
                  FutureBuilder<List<ListData>>(
                    future: DatabaseRepository().getAllLists(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const MeText(
                          text: MeString('Loading leftover lists...'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasError) {
                        return const MeText(
                          text: MeString('Error loading leftover lists'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasData) {
                        final lists = snapshot.data ?? [];
                        final existingIds = state.taskIdsToTasks.keys;
                        final leftoverLists = lists
                            .where((list) => !existingIds.contains(list.id))
                            .toList();

                        if (leftoverLists.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        return MeText(
                          text: MeString(
                            'Lists found in DB but not in Cubit: ${leftoverLists.length}\n'
                            'Note: This may not be 0 even if the app works correctly.',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else {
                        return const MeText(
                          text: MeString('No leftover lists found'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      }
                    },
                  ),
                ],
              );
            },
          ),
        ),

        ListTile(
          title: const MeText(
            text: MeString('MoneyTrackerCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<MoneyTrackersCubit, MoneyTrackersState>(
            builder: (context, state) {
              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  MeText(
                    text: MeString(
                      'Total MoneyTracker in Cubit: ${state.actionsIDToActions.keys.length}',
                    ),
                    meFontStyle: MeFontStyle.I8,
                  ),
                  FutureBuilder<List<MoneyTrackerTransaction>>(
                    future:
                        DatabaseRepository().getAllMoneyTrackerTransactions(),
                    builder: (context, snapshot) {
                      if (snapshot.connectionState == ConnectionState.waiting) {
                        return const MeText(
                          text: MeString(
                            'Loading leftover money tracker items...',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasError) {
                        return const MeText(
                          text: MeString(
                            'Error loading leftover money tracker items',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else if (snapshot.hasData) {
                        final moneyTrackers = snapshot.data ?? [];
                        final existingIds = state.actionsIDToActions.keys;
                        final leftoverMoneyTrackers = moneyTrackers
                            .where(
                              (tracker) => !existingIds.contains(tracker.id),
                            )
                            .toList();

                        if (leftoverMoneyTrackers.isEmpty) {
                          return const SizedBox.shrink();
                        }

                        return MeText(
                          text: MeString(
                            'MoneyTracker items found in DB but not in Cubit: ${leftoverMoneyTrackers.length}\n'
                            'Note: This may not be 0 even if the app works correctly.',
                          ),
                          meFontStyle: MeFontStyle.I8,
                        );
                      } else {
                        return const MeText(
                          text:
                              MeString('No leftover money tracker items found'),
                          meFontStyle: MeFontStyle.I8,
                        );
                      }
                    },
                  ),
                ],
              );
            },
          ),
        ),
        const MeText(
          text: MeString(
            'The total values may ideally match with Drift SQL tables',
          ),
          meFontStyle: MeFontStyle.I8,
        ),
        const MeText(
          text: MeString('Screen Level Tasks Providers'),
          meFontStyle: MeFontStyle.A8,
        ),
        ListTile(
          title: const MeText(
            text: MeString('PastTodosActionsCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<PastTodosActionsCubit, PastTodosActionsState>(
            builder: (context, state) {
              return MeText(
                text: MeString(
                  'Unfiltered Past Todos in Cubit: ${state.unFilteredDateToPastTasksIdMap.values.fold(0, (sum, list) => sum + list.length)}\nFiltered Past Todos in Cubit: ${state.filteredDateToPastTasksIdMap.values.fold(0, (sum, list) => sum + list.length)}',
                ),
                meFontStyle: MeFontStyle.I8,
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('PastHabitsActionsCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle: BlocBuilder<PastHabitsActionsCubit, PastHabitsActionsState>(
            builder: (context, state) {
              return MeText(
                text: MeString(
                  'Unfiltered Past Habits in Cubit: ${state.unFilteredDateToPastTasksIdMap.values.fold(0, (sum, list) => sum + list.length)}\nFiltered Past Todos in Cubit: ${state.filteredDateToPastTasksIdMap.values.fold(0, (sum, list) => sum + list.length)}',
                ),
                meFontStyle: MeFontStyle.I8,
              );
            },
          ),
        ),
        ListTile(
          title: const MeText(
            text: MeString('PastJournalsActionsCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle:
              BlocBuilder<PastJournalsActionsCubit, PastJournalsActionsState>(
            builder: (context, state) {
              return MeText(
                text: MeString(
                  'Unfiltered Past Journals in Cubit: ${state.unFilteredDateToPastTasksIdMap.values.fold(0, (sum, list) => sum + list.length)}\nFiltered Past Todos in Cubit: ${state.filteredDateToPastTasksIdMap.values.fold(0, (sum, list) => sum + list.length)}',
                ),
                meFontStyle: MeFontStyle.I8,
              );
            },
          ),
        ),
        // ListTile(
        //   title: const MeText(
        //     text: MeString('TodayActionsCubit'),
        //     meFontStyle: MeFontStyle.A8,
        //   ),
        //   subtitle: BlocBuilder<TodayActionsCubit, TodayActionsState>(
        //     builder: (context, state) {
        //       return MeText(
        //         text: MeString(
        //           'Today Todos in Cubit: ${state.dateToTodayTodosIdMap.values.fold(0, (sum, list) => sum + list.length)}\nToday Habits in Cubit: ${state.dateToTodayHabitsIdMap.values.fold(0, (sum, list) => sum + list.length)}\nToday Journals in Cubit: ${state.dateToTodayJournalsIdMap.values.fold(0, (sum, list) => sum + list.length)}',
        //         ),
        //         meFontStyle: MeFontStyle.I8,
        //       );
        //     },
        //   ),
        // ),
        ListTile(
          title: const MeText(
            text: MeString('OverdueTodosActionsCubit'),
            meFontStyle: MeFontStyle.A8,
          ),
          subtitle:
              BlocBuilder<OverdueTodosActionsCubit, OverdueTodosActionsState>(
            builder: (context, state) {
              return MeText(
                text: MeString(
                  'Overdue Todos in Cubit: ${state.filteredDateToOverdueTasksIdMap.values.fold(0, (sum, list) => sum + list.length)}',
                ),
                meFontStyle: MeFontStyle.I8,
              );
            },
          ),
        ),
      ],
    );
  }
}

Future<String> get _localPath async {
  final directory = await getApplicationDocumentsDirectory();
  return directory.path;
}

Future<File> get _localFile async {
  final path = await _localPath;
  return File('$path/my_map.json');
}

Future<File> writeMapToFile(Map<DateTime, Set<String>> map) async {
  final jsonStr = jsonEncode(
    map.map((key, value) => MapEntry(key.toString(), value.toList())),
  );

  final file = await _localFile;

  return await file.writeAsString(jsonStr);
}
