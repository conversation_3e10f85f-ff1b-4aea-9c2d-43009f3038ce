import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/logger/cubit/log_cubit.dart';
import 'package:mevolve/features/logger/cubit/log_state.dart';
import 'package:mevolve/features/logger/view/cached_files_tab.dart';
import 'package:mevolve/features/logger/view/debug_images_tab.dart';
import 'package:mevolve/features/logger/view/debug_settings_tab.dart';
import 'package:mevolve/features/logger/view/debug_tasks_provider_tab.dart';
import 'package:mevolve/features/logger/view/firestore_debug_tab.dart';
import 'package:mevolve/features/logger/view/notification_debug_tab.dart';
import 'package:mevolve/features/logger/view/widgets/log_filter_widget.dart';
import 'package:mevolve/features/logger/view/widgets/log_list_row_item.dart';
import 'package:mevolve/features/logger/view/widgets/sqflite_debug_tab.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/main.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/logger/log_tags.dart';
import 'package:mevolve/utilities/logger/me_logger.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:scrollable_positioned_list/scrollable_positioned_list.dart';
import 'package:share_plus/share_plus.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/list_option_sheet.dart'
    show getSharePositionWithFallback;

class LogScreen extends StatefulWidget {
  const LogScreen({super.key});

  static Widget page() => BlocProvider(
        create: (context) => LogCubit(
          const LogState(logItems: [], selectedTags: []),
          databaseRepository: context.read<DatabaseRepository>(),
        ),
        child: const LogScreen(),
      );

  @override
  State<LogScreen> createState() => _LogScreenState();
}

class _LogScreenState extends State<LogScreen> with TickerProviderStateMixin {
  final ItemScrollController itemScrollController = ItemScrollController();
  late final TabController tabController;
  int? selectedIndex;

  List<Tab> tabs = [
    const Tab(text: 'Logs'),
    if (debugSettingsEnabled) const Tab(text: 'Settings'),
    const Tab(text: 'Drift SQL'),
    const Tab(text: 'Notifications'),
    const Tab(text: 'Analytics'),
    const Tab(text: 'Firestore'),
    const Tab(text: 'Cached Files'),
    if (debugSettingsEnabled) const Tab(text: 'All Images'),
    if (debugSettingsEnabled) const Tab(text: 'Tasks Providers'),
  ];

  @override
  void initState() {
    super.initState();
    tabController = TabController(length: tabs.length, vsync: this);
    tabController.addListener(_onTabChanged);
    Future.delayed(Duration.zero, () {
      if (!mounted) return;
      context.read<LogCubit>().fetchLogs();
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Scaffold(
      backgroundColor: Colors.transparent,
      appBar: AppBar(
        leading: MeIconButton(
          onPressed: () => Navigator.maybePop(context),
          icon: const Icon(Icons.arrow_back),
          padding: const EdgeInsets.symmetric(horizontal: 12),
        ),
        actions: [
          MeIconButton(
            onPressed: () {
              UtilityMethods.clearDebugLogsTimestamp = null;
              context.read<LogCubit>().fetchLogs();
            },
            icon: const Icon(Icons.refresh),
            padding: const EdgeInsets.symmetric(horizontal: 12),
          ),
          const SizedBox(width: 4),
          MeIconButton(
            onPressed: () {
              setState(() {
                UtilityMethods.clearDebugLogsTimestamp = DateTime.now();
              });
            },
            icon: const Icon(Icons.clear_all),
            padding: const EdgeInsets.symmetric(horizontal: 12),
          ),
          PopupMenuButton<String>(
            onSelected: handleClick,
            iconColor: colorScheme.color12,
            itemBuilder: (BuildContext context) {
              return {'Share logs'}.map((String choice) {
                return PopupMenuItem<String>(
                  value: choice,
                  child: MeText(
                    text: MeString(choice),
                    meFontStyle: MeFontStyle.C8,
                  ),
                );
              }).toList();
            },
          ),
        ],
        title:
            const MeText(text: MeString('Debug'), meFontStyle: MeFontStyle.A12),
        bottom: TabBar(
          isScrollable: true,
          tabAlignment: TabAlignment.start,
          controller: tabController,
          tabs: tabs,
        ),
      ),
      body: TabBarView(
        controller: tabController,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const LogsFilterWidget(),
              Expanded(
                child: BlocBuilder<LogCubit, LogState>(
                  builder: (context, state) {
                    final logList = context.read<LogCubit>().getFilteredLogs(
                          logTags: state.selectedTags,
                          debugLogLevels: state.selectedLogLevels,
                        );
                    return ScrollablePositionedList.builder(
                      itemBuilder: (context, index) {
                        final item = logList[index];
                        return LogListItem(
                          item,
                          isSelected: index == selectedIndex,
                        );
                      },
                      itemCount: logList.length,
                      itemScrollController: itemScrollController,
                    );
                  },
                ),
              ),
            ],
          ),
          if (debugSettingsEnabled) const SettingsTab(),
          const SqfliteDebugTab(),
          NotificationDebugTab(tabController, (index) {
            selectedIndex = index;
          }),
          SafeArea(
            child: BlocBuilder<LogCubit, LogState>(
              builder: (context, state) {
                final logList = context
                    .read<LogCubit>()
                    .getFilteredLogs(logTags: [LogTags.analytics]);
                return ListView.builder(
                  itemBuilder: (context, index) {
                    return LogListItem(logList[index]);
                  },
                  itemCount: logList.length,
                );
              },
            ),
          ),
          FirestoreDebugTab(tabController, (index) {
            selectedIndex = index;
          }),
          const CachedFilesTab(),
          if (debugSettingsEnabled) const DebugAppImagesTab(),
          if (debugSettingsEnabled) const DebugTasksProviderTab(),
        ],
      ),
    );
  }

  void _onTabChanged() {
    if (tabController.index == 0 && selectedIndex != null) {
      itemScrollController.scrollTo(
        index: selectedIndex!,
        duration: const Duration(seconds: 1),
        curve: Curves.easeInOutCubic,
      );
    } else {
      selectedIndex = null;
    }
  }
}

Future<void> handleClick(String value) async {
  switch (value) {
    case 'Share logs':
      {
        String? filePath = await MeLogger.getLogDumpFilePath();
        if (filePath != null) {
          final shareRect =
              getSharePositionWithFallback(authenticatedGlobalContext);
          await Share.shareXFiles(
            [XFile(filePath)],
            text: 'Mevolve Logs',
            sharePositionOrigin: shareRect,
          );
        }
      }
      break;
  }
}
