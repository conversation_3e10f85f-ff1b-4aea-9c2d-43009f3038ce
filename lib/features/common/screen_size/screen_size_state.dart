import 'package:flutter/material.dart';
import 'package:mevolve/constants/release_config.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';

class ScreenSizeState {
  // Private constructor
  ScreenSizeState._internal();

  // Singleton instance
  static final ScreenSizeState _instance = ScreenSizeState._internal();

  // Getter to access the instance
  static ScreenSizeState get instance => _instance;

  double _width = 0;
  double _height = 0;
  EdgeInsets _safeAreaPadding = EdgeInsets.zero;

  double get width => _width;

  double get height => _height;

  EdgeInsets get safeAreaPadding => _safeAreaPadding;

  void updateSize(double width, double height, EdgeInsets safeAreaPadding) {
    _width = width;
    _height = height;
    _safeAreaPadding = safeAreaPadding;
  }

  bool _isCollapsed = true;
  double _scrollOffset = 0.0;

  // ValueNotifier for macOS drawer visibility to trigger rebuilds
  final ValueNotifier<bool> _macOSDrawerVisibilityNotifier =
      ValueNotifier<bool>(true);

  void updateDrawerState({bool? isCollapsed, double? scrollOffset}) {
    if (isCollapsed != null) {
      _isCollapsed = isCollapsed;
    }
    if (scrollOffset != null) {
      _scrollOffset = scrollOffset;
    }
  }

  void updateMacOSDrawerVisibility(bool isVisible) {
    _macOSDrawerVisibilityNotifier.value = isVisible;
  }

  void toggleMacOSDrawerVisibility() {
    _macOSDrawerVisibilityNotifier.value =
        !_macOSDrawerVisibilityNotifier.value;
  }

  bool get isCollapsed => _isCollapsed;

  double get scrollOffset => _scrollOffset;

  bool get isMacOSDrawerVisible => _macOSDrawerVisibilityNotifier.value;

  ValueNotifier<bool> get macOSDrawerVisibilityNotifier =>
      _macOSDrawerVisibilityNotifier;

  static const double _bigScreenBreakPoint = 750;

  bool get isBigScreen => _width > _bigScreenBreakPoint;

  double get subtractFromWidthToGetMinimumScreenWidth => isBigScreen
      ? (collapsedDrawerWidth * 2) + _safeAreaPadding.horizontal
      : 0;

  double get screenMaxWidth =>
      (_width - subtractFromWidthToGetMinimumScreenWidth) < _bigScreenBreakPoint
          ? (_width - subtractFromWidthToGetMinimumScreenWidth)
          : _bigScreenBreakPoint;

  // macOS main content area max width
  double get macOSMainContentMaxWidth => MePlatform.isMacOS
      ? SizeConstants.macOSMainContentMaxWidth
      : screenMaxWidth;

  double get drawerWidth => isBigScreen ? 190 : _width * (2 / 3);

  double get collapsedDrawerWidth => 60;

  EdgeInsets get featuredWidgetPadding => EdgeInsets.only(
        left: _width > _bigScreenBreakPoint ? 24 : 16,
        right: _width > _bigScreenBreakPoint ? 24 : 16,
        top: 12,
        bottom: 12,
      );

  EdgeInsets get tabLabelPadding => EdgeInsets.only(
        left: _width > _bigScreenBreakPoint ? 55 : 24,
        right: _width > _bigScreenBreakPoint ? 55 : 24,
        top: 0,
        bottom: 0,
      );

  EdgeInsets get docBottomBarPadding => EdgeInsets.only(
        left: _width > _bigScreenBreakPoint ? 8 : 0,
        right: _width > _bigScreenBreakPoint ? 8 : 0,
        top: 0,
        bottom: 0,
      );

  EdgeInsets get appBarPadding => EdgeInsets.only(
        left: _width > _bigScreenBreakPoint ? 16 : 16,
        right: _width > _bigScreenBreakPoint ? 16 : 16,
        top: 0,
        bottom: 0,
      );

  EdgeInsets getDialogInsetPadding(DialogLevel level) {
    // Use macOS-specific widths for macOS platform
    if (MePlatform.isMacOS) {
      final double maxWidth = SizeConstants.getMacOSDialogMaxWidth(level);
      final double minPadding = SizeConstants.dialogMinPaddings[level]!;

      // Calculate the padding based on _width and the selected values.
      return EdgeInsets.symmetric(
        horizontal: _width > (maxWidth + (minPadding * 2))
            ? (_width - maxWidth) / 2
            : minPadding,
      );
    }

    // For non-macOS platforms, use the existing logic
    final double maxWidth = SizeConstants.dialogMaxWidths[level]!;
    final double minPadding = SizeConstants.dialogMinPaddings[level]!;

    // Calculate the padding based on _width and the selected values.
    return EdgeInsets.symmetric(
      horizontal: _width > (maxWidth + (minPadding * 2))
          ? (_width - maxWidth) / 2
          : minPadding,
    );
  }

  double get modalBarrierOpacity =>
      (ReleaseConfig.instance.modalBarrierOpacity / 100);

  double get loaderModalBarrierOpacity => (90 / 100);

  double get drawerHeaderHeight => isBigScreen
      ? _isCollapsed
          ? 56
          : SizeConstants.appBarHeightWithBottomTabs +
              SizeConstants.tabBarHeight
      : 113;

  double get drawerHeaderLogoExpandedHeight => isBigScreen ? 30 : 45;

  double get drawerHeaderLogoCollapsedHeight => isBigScreen ? 18 : 50;
}
