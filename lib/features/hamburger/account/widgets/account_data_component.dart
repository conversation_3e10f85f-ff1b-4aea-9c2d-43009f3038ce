import 'dart:async';
import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/user/data_backup_info.dart';
import 'package:mevolve/data/providers/firebase_functions.dart';
import 'package:mevolve/data/providers/firebase_storage.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/hamburger/account/widgets/manage_data_widgets/import_progress_widget.dart';
import 'package:mevolve/features/hamburger/account/widgets/manage_data_widgets/upload_complete_dialog_widget.dart';
import 'package:mevolve/features/hamburger/subscription/effective_subscription_cubit/effective_subscription_cubit.dart';
import 'package:mevolve/features/hamburger/subscription/helpers/subscription_helper.dart';
import 'package:mevolve/features/hamburger/widgets/activity_label.dart';
import 'package:mevolve/features/hamburger/widgets/drawer_component_header.dart';
import 'package:mevolve/features/hamburger/widgets/drawer_list_tile_button.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/show_me_platform_dialog.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/connectivity/internet_connectivity_service.dart';
import 'package:mevolve/utilities/encryption/me_encryption.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:path/path.dart' as p;
import 'package:universal_io/io.dart';

class AccountDataComponent extends StatelessWidget {
  const AccountDataComponent({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        DrawerComponentHeader(
          headerText: MeTranslations.instance.screen_account_sectionDataTitle,
        ),
        BlocBuilder<AppBloc, AppState>(
          buildWhen: (previous, current) =>
              (previous.userData?.dataBackupInfo !=
                  current.userData?.dataBackupInfo) &&
              current.userData != null,
          builder: (context, state) {
            final userData = state.userData!;
            return StreamBuilder(
              stream: Stream.periodic(
                const Duration(seconds: 1),
                (int count) => true,
              ),
              builder: (context, snapshot) {
                bool isBackupDataButtonEnabled =
                    userData.dataBackupInfo.import.status !=
                        BackupTaskStatus.inProgress;
                bool isDeleteButtonEnabled =
                    !(userData.dataBackupInfo.export.status ==
                            BackupTaskStatus.inProgress ||
                        userData.dataBackupInfo.import.status ==
                            BackupTaskStatus.inProgress);
                bool isImportDataButtonEnabled =
                    userData.dataBackupInfo.export.status !=
                        BackupTaskStatus.inProgress;
                return Column(
                  children: [
                    DrawerSettingsListTile(
                      title: MeTranslations.instance.screen_account_backupData,
                      leading: MeIcon(
                        iconPath: Assets.svg.accountScreenImportDataIc.path,
                        iconColor: !isBackupDataButtonEnabled
                            ? context.meColorScheme.color7
                            : context.meColorScheme.color35,
                        iconContainerSize:
                            const SizedBox(width: 24, height: 24),
                        padding: EdgeInsets.zero,
                      ),
                      trailing: userData.dataBackupInfo.export.status ==
                              BackupTaskStatus.inProgress
                          ? ActivityLabelWidget(
                              label: MeTranslations
                                  .instance.screen_account_inProgress,
                            )
                          : const SizedBox(),
                      subTitleWidget: userData.dataBackupInfo.export
                                  .lastUpdatedAtInString !=
                              null
                          ? MeText(
                              text: userData
                                  .dataBackupInfo.export.lastUpdatedAtInString!,
                              meFontStyle: MeFontStyle.F7,
                            )
                          : null,
                      onTap: !isBackupDataButtonEnabled
                          ? null
                          : () async {
                              bool result = await InternetConnectivityService
                                  .instance.hasInternet;
                              if (!context.mounted) return;
                              UtilityMethods.showOfflineDialogOrPerformAction(
                                internetActiveFunction: () =>
                                    BackupImportDialogs.showBackupDataDialog(
                                  context,
                                ),
                                internetConnectionState: result == true
                                    ? InternetConnectionState.connected
                                    : InternetConnectionState.disconnected,
                                context: context,
                              );
                            },
                      enabled: isBackupDataButtonEnabled,
                    ),
                    DrawerSettingsListTile(
                      title: MeTranslations.instance.screen_account_deleteData,
                      leading: MeIcon(
                        iconPath: Assets.svg.accountScreenDeleteDataIc.path,
                        iconColor: !isDeleteButtonEnabled
                            ? context.meColorScheme.color7
                            : context.meColorScheme.color35,
                        iconContainerSize:
                            const SizedBox(width: 24, height: 24),
                        padding: EdgeInsets.zero,
                      ),
                      onTap: () async {
                        bool result = await InternetConnectivityService
                            .instance.hasInternet;
                        if (!context.mounted) return;
                        UtilityMethods.showOfflineDialogOrPerformAction(
                          internetActiveFunction: () =>
                              BackupImportDialogs.showDeleteConfirmationDialog(
                            context,
                          ),
                          internetConnectionState: result == true
                              ? InternetConnectionState.connected
                              : InternetConnectionState.disconnected,
                          context: context,
                        );
                      },
                      enabled: isDeleteButtonEnabled,
                    ),
                    DrawerSettingsListTile(
                      title: MeTranslations.instance.screen_common_importData,
                      leading: MeIcon(
                        iconPath: Assets.svg.accountScreenBackupDataIc.path,
                        iconColor: !isImportDataButtonEnabled
                            ? context.meColorScheme.color7
                            : context.meColorScheme.color35,
                        iconContainerSize:
                            const SizedBox(width: 24, height: 24),
                        padding: EdgeInsets.zero,
                      ),
                      trailing: userData.dataBackupInfo.import.status ==
                              BackupTaskStatus.inProgress
                          ? ActivityLabelWidget(
                              label: MeTranslations
                                  .instance.screen_account_inProgress,
                            )
                          : const SizedBox(),
                      onTap: () async {
                        bool result = await InternetConnectivityService
                            .instance.hasInternet;
                        if (!context.mounted) return;
                        UtilityMethods.showOfflineDialogOrPerformAction(
                          internetActiveFunction: () {
                            final isProUser = context
                                .read<EffectiveSubscriptionCubit>()
                                .state
                                .eMayCreateButCanSeeProFeatures;
                            if (!isProUser) {
                              SubscriptionHelper.showFeatureLockedDialog(
                                context,
                              );
                            } else {
                              BackupImportDialogs
                                  .showImportDataConfirmationDialog(context);
                            }
                          },
                          internetConnectionState: result == true
                              ? InternetConnectionState.connected
                              : InternetConnectionState.disconnected,
                          context: context,
                        );
                      },
                      enabled: isImportDataButtonEnabled,
                    ),
                  ],
                );
              },
            );
          },
        ),
        const SizedBox(height: 4),
      ],
    );
  }
}

class BackupImportDialogs {
  static Future<void> showBackupDataDialog(BuildContext context) async {
    final userData = context.read<AppBloc>().state.userData!;
    return showMePlatformDialog(
      useRootNavigator: false,
      context: context,
      title: MeTranslations.instance.overlay_backupDataConfirmation_title,
      description:
          MeTranslations.instance.overlay_backupDataConfirmation_content1,
      customContentAfterDescription: Column(
        children: [
          MeText(
            text: MeString.fromVariable(userData.userInfo.email),
            meFontStyle: MeFontStyle.F8,
            maxLines: 2,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 14),
          MeText(
            text:
                MeTranslations.instance.overlay_backupDataConfirmation_content3,
            meFontStyle: MeFontStyle.F7,
            maxLines: 2,
          ),
        ],
      ),
      primaryText: MeTranslations.instance.overlay_filterApplied_proceed,
      primaryOnTap: () async {
        // Send analytics data action event for export
        final DateTime startTime = DateTime.now();

        Navigator.pop(context);
        showMeSnackbar(
          MeTranslations.instance.toast_backupDataRequestSent_content,
        );
        final appBloc = context.read<AppBloc>();
        appBloc.updateUser(
          user: userData.copyWith(
            dataBackupInfo: userData.dataBackupInfo.copyWith(
              export: DataBackupStat(
                BackupTaskStatus.inProgress,
                DateTime.now(),
              ),
            ),
          ),
        );
        final userSecret =
            await MeEncryption().getUserSecret(appBloc.state.userData!.uid);
        await FirebaseFunctionsRepository().createAccountBackup(
          uid: userData.uid,
          userSecret: userSecret,
        );

        // Send analytics data action event for export completion
        EventFormation().sendDataActionEvent(
          trackAction: TrackAction.dataExport,
          startTime: startTime,
          endTime: DateTime.now(),
        );
      },
      secondaryText: MeTranslations.instance.screen_common_buttonCancel,
      secondaryOnTap: () => Navigator.pop(context),
    );
  }

  static Future<void> showDeleteConfirmationDialog(BuildContext context) async {
    return showMePlatformDialog(
      useRootNavigator: false,
      context: context,
      title: MeTranslations.instance.overlay_deleteDataConfirmation_title,
      description:
          MeTranslations.instance.overlay_deleteDataConfirmation_content,
      primaryText:
          MeTranslations.instance.overlay_deleteDataConfirmation_backupData,
      primaryOnTap: () {
        Navigator.pop(context);
        showBackupDataDialog(context);
      },
      secondaryText: MeTranslations.instance.screen_common_buttonCancel,
      secondaryOnTap: () => Navigator.pop(context),
      tertiaryText:
          MeTranslations.instance.overlay_deleteDataConfirmation_deleteData,
      tertiaryOnTap: () async {
        Navigator.pop(context);
        showFinalDeleteConfirmationWarning(context);
      },
    );
  }

  static Future<void> showImportDataConfirmationDialog(
    BuildContext context,
  ) async {
    return showMePlatformDialog(
      useRootNavigator: false,
      context: context,
      title: MeTranslations.instance.overlay_importDataConfirmation_title,
      description:
          MeTranslations.instance.overlay_importDataConfirmation_content,
      primaryText:
          MeTranslations.instance.overlay_importDataConfirmation_import,
      primaryOnTap: () async {
        // Send analytics data action event for import
        final DateTime startTime = DateTime.now();

        Navigator.pop(context);
        await BackupImportService.importFile(context, startTime);
      },
      secondaryText: MeTranslations.instance.screen_common_buttonCancel,
      secondaryOnTap: () => Navigator.pop(context),
    );
  }

  static Future<void> showImportFileSizeBigWarningDialog(
    BuildContext context,
  ) async {
    return showMePlatformDialog(
      useRootNavigator: false,
      context: context,
      title: MeTranslations.instance.overlay_importFileTooLarge_title,
      description: MeTranslations.instance.overlay_importFileTooLarge_content,
      primaryText: MeTranslations.instance.screen_common_ok,
      primaryOnTap: () => Navigator.pop(context),
    );
  }

  static Future<void> showTimeoutDialog(BuildContext context) async {
    return showMePlatformDialog(
      useRootNavigator: false,
      context: context,
      title: MeTranslations.instance.overlay_importTimeout_title,
      description: MeTranslations.instance.overlay_importTimeout_content,
      primaryText: MeTranslations.instance.screen_common_ok,
      primaryOnTap: () => Navigator.pop(context),
    );
  }

  static Future<void> showFileErrorDialog(BuildContext context) async {
    return showMePlatformDialog(
      useRootNavigator: false,
      context: context,
      title: MeTranslations.instance.overlay_fileError_title,
      description: MeTranslations.instance.overlay_fileError_content,
      primaryText: MeTranslations.instance.screen_common_ok,
      primaryOnTap: () => Navigator.pop(context),
    );
  }

  static Future<void> showInvalidFileDialog(BuildContext context) async {
    return showMePlatformDialog(
      useRootNavigator: false,
      context: context,
      title: MeTranslations.instance.overlay_invalidImportFileFormat_title,
      description:
          MeTranslations.instance.overlay_invalidImportFileFormat_content,
      showBottomDivider: false,
      primaryText: MeTranslations.instance.screen_common_ok,
      primaryOnTap: () => Navigator.pop(context),
    );
  }

  static Future<void> showFinalDeleteConfirmationWarning(
    BuildContext context,
  ) async {
    return showMePlatformDialog(
      useRootNavigator: false,
      context: context,
      title: MeTranslations.instance.overlay_deleteDataAlert_title,
      description: MeTranslations.instance.overlay_deleteDataAlert_content,
      showBottomDivider: false,
      primaryText: MeTranslations.instance.overlay_deleteDataAlert_deleteData,
      primaryOnTap: () async {
        // Send analytics data action event for delete
        final DateTime startTime = DateTime.now();

        Navigator.pop(context);
        UtilityMethods.showMeLoaderDialog(context);
        await context.read<AppBloc>().deleteAllUserData(context);
        if (context.mounted) {
          Navigator.of(context).pop();
        }

        // Send analytics data action event for delete completion
        EventFormation().sendDataActionEvent(
          trackAction: TrackAction.dataDelete,
          startTime: startTime,
          endTime: DateTime.now(),
        );
      },
      secondaryText: MeTranslations.instance.screen_common_buttonCancel,
      secondaryOnTap: () => Navigator.pop(context),
    );
  }
}

class BackupImportService {
  static Future<void> importFile(
    BuildContext context, [
    DateTime? startTime,
  ]) async {
    final DateTime actionStartTime = startTime ?? DateTime.now();
    int filePickerTimeout = 60;
    int timeSpentInProcessing = 0;
    FilePickerStatus? pickerStatus;
    bool isTimedOut = false;

    UtilityMethods.showMeLoaderDialog(context);

    try {
      Timer timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
        if (pickerStatus == FilePickerStatus.picking &&
            timeSpentInProcessing > filePickerTimeout) {
          timer.cancel();
          Navigator.pop(context);
          isTimedOut = true;
          BackupImportDialogs.showTimeoutDialog(context);
        } else {
          timeSpentInProcessing++;
        }
      });

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['zip'],
        onFileLoading: (status) {
          pickerStatus = status;
        },
      );

      timer.cancel();
      if (isTimedOut) return;
      if (!context.mounted) return;
      Navigator.pop(context);

      if (result != null) {
        final extension = p.extension(result.files.single.path!);
        if (extension == '.zip') {
          File file = File(result.files.single.path!);
          int fileSize = file.lengthSync();
          if (fileSize >= (1000 * 1000 * 1000)) {
            if (context.mounted) {
              BackupImportDialogs.showImportFileSizeBigWarningDialog(context);
            }
          } else {
            if (context.mounted) {
              _uploadFile(context, file, actionStartTime);
            }
          }
        } else {
          if (context.mounted) {
            BackupImportDialogs.showInvalidFileDialog(context);
          }
        }
      }
    } catch (error) {
      Navigator.pop(context);
      final Log log = MeLogger.getLogger(LogTags.dataBackup);
      BackupImportDialogs.showFileErrorDialog(context);
      log.e('error in importing file - ${error.toString()}');
    }
  }

  static Future<void> _uploadFile(
    BuildContext context,
    File file,
    DateTime startTime,
  ) async {
    final userData = context.read<AppBloc>().state.user!;
    final encPvtKey =
        await MeEncryption().getEncPvtKeyWithKms(userData.uid, userData.email!);
    if (encPvtKey == null) {
      MeLogger.getLogger(LogTags.dataBackup).e('Keys are null during upload');
      return;
    }

    final uploadTask = await FirebaseStorageRepository().uploadBackupFile(
      filePath: file.path,
      userId: userData.uid,
      userEncPvtKey: encPvtKey,
    );

    if (!context.mounted) return;
    bool? result = await showDialog<bool?>(
      useRootNavigator: false,
      context: context,
      barrierDismissible: false,
      builder: (BuildContext childContext) {
        return MeDialog(
          title: MeTranslations.instance.overlay_dataUploadingProcess_title,
          centerTitleAndDescription: true,
          customContentAfterDescription: ImportProgressWidget(uploadTask!),
          secondaryText: MeTranslations.instance.screen_common_buttonCancel,
          secondaryOnTap: () {
            try {
              uploadTask.cancel();
            } catch (e) {
              MeLogger.getLogger(LogTags.dataBackup)
                  .e('error in cancelling task');
            }
            Navigator.pop(context);
          },
        );
      },
    );

    if (!context.mounted) return;
    if (result ?? false) {
      // Send analytics data action event for import completion
      EventFormation().sendDataActionEvent(
        trackAction: TrackAction.dataImport,
        startTime: startTime,
        endTime: DateTime.now(),
      );

      showDialog(
        useRootNavigator: false,
        context: context,
        barrierDismissible: false,
        builder: (BuildContext childContext) {
          return MeDialog(
            title: MeTranslations.instance.overlay_dataUploadingProcess_title,
            centerTitleAndDescription: true,
            customContentAfterDescription: const UploadCompleteDialogWidget(),
          );
        },
      );
    }
  }
}
