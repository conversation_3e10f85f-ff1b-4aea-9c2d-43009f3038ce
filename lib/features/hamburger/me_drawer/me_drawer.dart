import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/constants/app_strings.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/release_config_cubit.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/hamburger/me_drawer/components/me_drawer_features_component.dart';
import 'package:mevolve/features/hamburger/me_drawer/components/me_drawer_home_component.dart';
import 'package:mevolve/features/hamburger/me_drawer/components/me_drawer_settings_component.dart';
import 'package:mevolve/features/hamburger/me_drawer/components/me_drawer_support_component.dart';
import 'package:mevolve/features/hamburger/me_drawer/components/me_drawer_update_component.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_scrollbar.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/non_modal_dialog.dart';

class MeDrawer extends StatefulWidget {
  const MeDrawer({
    Key? key,
    this.overlayDrawer = true,
    required this.selectedPage,
    required this.onItemSelected,
  }) : super(key: key);

  final bool overlayDrawer;

  final String selectedPage;
  final ValueChanged<String> onItemSelected;

  @override
  State<MeDrawer> createState() => _MeDrawerState();
}

class _MeDrawerState extends State<MeDrawer> {
  late ScrollController _scrollController;
  bool isInvisibleOverlayOpen = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController(
      initialScrollOffset: ScreenSizeState.instance.scrollOffset,
    );

    _scrollController.addListener(() {
      if (_scrollController.hasClients) {
        ScreenSizeState.instance.updateDrawerState(
          scrollOffset: _scrollController.offset,
        );
      }
    });
  }

  @override
  void dispose() {
    if (_scrollController.hasClients) {
      ScreenSizeState.instance.updateDrawerState(
        scrollOffset: _scrollController.offset,
      );
    }
    _scrollController.dispose();
    super.dispose();
  }

  void toggleDrawer([bool? shouldCollapse]) {
    bool collapse = shouldCollapse ?? !ScreenSizeState.instance.isCollapsed;

    if (mounted) {
      setState(() {
        ScreenSizeState.instance.updateDrawerState(
          isCollapsed: collapse,
        );
      });
    }

    if (ScreenSizeState.instance.isBigScreen &&
        !collapse &&
        !MePlatform.isHardwareInputDeviceAvailable) {
      invisibleOverlay(context);
    } else if (isInvisibleOverlayOpen &&
        collapse &&
        !MePlatform.isHardwareInputDeviceAvailable) {
      Navigator.maybePop(context);
    }
  }

  void toggleMacOSDrawerVisibility() {
    if (MePlatform.isMacOS && ScreenSizeState.instance.isBigScreen) {
      setState(() {
        ScreenSizeState.instance.updateMacOSDrawerVisibility(
          !ScreenSizeState.instance.isMacOSDrawerVisible,
        );
      });
    }
  }

  Future<void> invisibleOverlay(BuildContext context) {
    isInvisibleOverlayOpen = true;
    return showNonModalDialog<void>(
      useRootNavigator: false,
      context: authenticatedGlobalContext ?? context,
      barrierDismissible: false,
      builder: (BuildContext childContext) {
        return const SizedBox.shrink();
      },
    ).whenComplete(() {
      // Avoid auto-collapse on route change
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final bool isMacOSBigScreen =
        MePlatform.isMacOS && ScreenSizeState.instance.isBigScreen;

    final bool isExpanded = isMacOSBigScreen
        ? true
        : (!ScreenSizeState.instance.isCollapsed || widget.overlayDrawer);

    return MePlatformDrawer(
      overlayDrawer: widget.overlayDrawer,
      isCollapsed: ScreenSizeState.instance.isCollapsed,
      onDrawerCollapseState: toggleDrawer,
      child: Drawer(
        backgroundColor: ScreenSizeState.instance.isBigScreen
            ? colorScheme.color6
            : colorScheme.color5,
        width: ScreenSizeState.instance.drawerWidth,
        child: Column(
          children: [
            BlocBuilder<AppBloc, AppState>(
              buildWhen: (previous, current) =>
                  !listEquals(
                    previous.userMetadata?.userSegments,
                    current.userMetadata?.userSegments,
                  ) ||
                  previous.currentRCUpdatedAt != current.currentRCUpdatedAt,
              builder: (context, state) {
                return GestureDetector(
                  onLongPress: () {
                    final debugEnabled = context
                        .read<ReleaseConfigCubit>()
                        .isDebugScreenAvailable();
                    if (debugEnabled || kDebugMode) {
                      if (widget.overlayDrawer) {
                        Navigator.pop(context);
                      }
                      context.mePush(logPage);
                    }
                  },
                  onTap: () {
                    if (!MePlatform.isHardwareInputDeviceAvailable) {
                      toggleDrawer();
                    }
                  },
                  child: SizedBox(
                    height: ScreenSizeState.instance.drawerHeaderHeight,
                    width: double.maxFinite,
                    child: DrawerHeader(
                      decoration: BoxDecoration(
                        color: MePlatform.isMacOS
                            ? colorScheme.color6
                            : colorScheme.color9,
                      ),
                      margin: EdgeInsets.zero,
                      child: _buildDrawerHeaderContent(isExpanded, colorScheme),
                    ),
                  ),
                );
              },
            ),
            Flexible(
              child: Stack(
                children: [
                  MeScrollbar(
                    controller: widget.overlayDrawer ? null : _scrollController,
                    isDisabled: ScreenSizeState.instance.isCollapsed
                        ? true
                        : widget.overlayDrawer,
                    child: ListView(
                      padding: EdgeInsets.only(
                        top: 24,
                        bottom: MediaQuery.of(context).padding.bottom + 16,
                      ),
                      physics: const ClampingScrollPhysics(),
                      controller:
                          widget.overlayDrawer ? null : _scrollController,
                      children: [
                        MeDrawerUpdateComponent(
                          overlayDrawer: widget.overlayDrawer,
                          selectedPage: widget.selectedPage,
                          onItemSelected: widget.onItemSelected,
                          showTitle: isExpanded,
                        ),
                        if (ScreenSizeState.instance.isBigScreen)
                          MeDrawerHomeComponent(
                            overlayDrawer: widget.overlayDrawer,
                            selectedPage: widget.selectedPage,
                            onItemSelected: widget.onItemSelected,
                            showTitle: isExpanded,
                          ),
                        MeDrawerFeaturesComponent(
                          overlayDrawer: widget.overlayDrawer,
                          selectedPage: widget.selectedPage,
                          onItemSelected: widget.onItemSelected,
                          showTitle: isExpanded,
                        ),
                        MeDrawerSettingsComponent(
                          overlayDrawer: widget.overlayDrawer,
                          selectedPage: widget.selectedPage,
                          onItemSelected: widget.onItemSelected,
                          showTitle: isExpanded,
                        ),
                        MeDrawerSupportComponent(
                          overlayDrawer: widget.overlayDrawer,
                          selectedPage: widget.selectedPage,
                          onItemSelected: widget.onItemSelected,
                          showTitle: isExpanded,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDrawerHeaderContent(bool isExpanded, MeColorScheme colorScheme) {
    if (MePlatform.isMacOS &&
        ScreenSizeState.instance.isBigScreen &&
        isExpanded &&
        !widget.overlayDrawer) {
      return Row(
        children: [
          MeIcon(
            iconString:
                AppStrings.appHorizontalLogoMac(colorScheme: colorScheme),
            iconColor: colorScheme.color12,
            iconSize: SizedBox(
              height: ScreenSizeState.instance.drawerHeaderLogoExpandedHeight,
            ),
            fit: BoxFit.contain,
          ),
          const Spacer(),
          GestureDetector(
            onTap: toggleMacOSDrawerVisibility,
            child: Container(
              padding: const EdgeInsets.all(8),
              child: MeIcon(
                iconPath: Assets.svg.hamburgerIcon.path,
                iconColor: colorScheme.color8,
                iconSize: const SizedBox(width: 19, height: 11.43),
                fit: BoxFit.contain,
              ),
            ),
          ),
        ],
      );
    } else {
      return Center(
        child: isExpanded
            ? MeIcon(
                iconString:
                    AppStrings.appHorizontalLogo(colorScheme: colorScheme),
                iconColor: colorScheme.color12,
                iconSize: SizedBox(
                  height:
                      ScreenSizeState.instance.drawerHeaderLogoExpandedHeight,
                ),
                fit: BoxFit.contain,
              )
            : !MePlatform.isHardwareInputDeviceAvailable
                ? MeIcon(
                    iconPath: Assets.svg.hamburgerIcon.path,
                    iconColor: colorScheme.color12,
                    fit: BoxFit.contain,
                  )
                : MeIcon(
                    iconPath: Assets.svg.me.path,
                    iconColor: colorScheme.color12,
                    iconSize: SizedBox(
                      height: ScreenSizeState
                          .instance.drawerHeaderLogoCollapsedHeight,
                    ),
                    fit: BoxFit.contain,
                  ),
      );
    }
  }
}

class MePlatformDrawer extends StatelessWidget {
  const MePlatformDrawer({
    super.key,
    required this.overlayDrawer,
    required this.isCollapsed,
    required this.child,
    required this.onDrawerCollapseState,
  });

  final bool overlayDrawer;
  final bool isCollapsed;
  final Widget child;
  final ValueChanged<bool>? onDrawerCollapseState;

  @override
  Widget build(BuildContext context) {
    // For macOS, disable hover behavior and use complete hide/show
    final bool isMacOSBigScreen =
        MePlatform.isMacOS && ScreenSizeState.instance.isBigScreen;

    return overlayDrawer
        ? child
        : isMacOSBigScreen
            ? // For macOS, show/hide drawer based on visibility state
            ValueListenableBuilder<bool>(
                valueListenable:
                    ScreenSizeState.instance.macOSDrawerVisibilityNotifier,
                builder: (context, isMacOSDrawerVisible, child) {
                  return isMacOSDrawerVisible
                      ? SizedBox(
                          width: ScreenSizeState.instance.drawerWidth,
                          child: this.child,
                        )
                      : // Show hamburger menu at far left when drawer is hidden
                      Align(
                          alignment: Alignment.topLeft,
                          child: SizedBox(
                            width: 60,
                            height: 60,
                            child: Center(
                              child: MeIconButton(
                                key: const ValueKey('macOSShowDrawerButton'),
                                isCircularIconButton: true,
                                onPressed: () {
                                  ScreenSizeState.instance
                                      .updateMacOSDrawerVisibility(true);
                                },
                                iconPath: Assets.svg.hamburgerIcon.path,
                                iconColor: Theme.of(context)
                                    .extension<MeColorScheme>()!
                                    .color8,
                                padding: const EdgeInsets.all(8),
                                iconContainerSize: const SizedBox(
                                  width: 40,
                                  height: 40,
                                ),
                                iconSize: const SizedBox(
                                  width: 19,
                                  height: 11.43,
                                ),
                              ),
                            ),
                          ),
                        );
                },
              )
            : // For other platforms, use existing hover behavior
            MouseRegion(
                onEnter: (event) {
                  // Expand on hover
                  onDrawerCollapseState?.call(false);
                },
                onExit: (event) {
                  // Collapse on hover exit
                  onDrawerCollapseState?.call(true);
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 150),
                      width: isCollapsed
                          ? ScreenSizeState.instance.collapsedDrawerWidth
                          : ScreenSizeState.instance.drawerWidth,
                      child: child,
                    ),
                    if (!MePlatform.isHardwareInputDeviceAvailable &&
                        !isCollapsed) ...[
                      Expanded(
                        child: Listener(
                          onPointerDown: (event) {
                            onDrawerCollapseState?.call(true);
                          },
                          child: Container(
                            color: isCollapsed
                                ? Colors.transparent
                                : Theme.of(context)
                                    .bottomSheetTheme
                                    .modalBarrierColor,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
              );
  }
}
