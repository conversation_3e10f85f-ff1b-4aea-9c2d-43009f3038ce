import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/widgets/me_calendar/model/me_preset_model.dart';
import 'package:mevolve/features/widgets/me_calendar/model/me_range_calendar_result.dart';
import 'package:mevolve/features/widgets/me_calendar_new/cubit/me_preset_cubit.dart';
import 'package:mevolve/features/widgets/me_calendar_new/month_year_picker.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_presets_grid_view.dart';
import 'package:mevolve/features/widgets/me_primary_button.dart';
import 'package:mevolve/features/widgets/me_secondary_button.dart';
import 'package:mevolve/features/widgets/show_me_discard_dialog.dart';
import 'package:mevolve/features/widgets/time_picker_widget.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/text_scale_factor_checker.dart';
import 'package:mevolve/utilities/utility_methods.dart';

const Duration _monthScrollDuration = Duration(milliseconds: 200);
const double _dayPickerRowHeight = 42.0;
const int _maxDayPickerRowCount = 6; // A 31 day month that starts on Saturday.
const double _maxDayPickerHeight =
    _dayPickerRowHeight * (_maxDayPickerRowCount + 1);

Future<MeCalendarResult?> showMeTimeCalendarDialog({
  required BuildContext context,
  required DateTime initialDate,
  required DateTime firstDate,
  required DateTime lastDate,
  required TimeOfDay selectedTime,
  DateTime? selectedDate,
  Color? barrierColor,
  List<MePresetModel>? presetList,
  MePresetModel? selectedPreset,
  bool? showInvalidTimeDialog,
  required bool isTmzDependent,
  required bool isDateTimeSet,
}) async {
  return await showDialog(
    context: context,
    useRootNavigator: false,
    barrierColor: barrierColor,
    builder: (_) => Dialog(
      insetPadding:
          ScreenSizeState.instance.getDialogInsetPadding(DialogLevel.level1),
      elevation: 0,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.all(Radius.circular(16)),
      ),
      child: MeCalendar(
        isTmzDependent: isTmzDependent,
        initialDate: initialDate,
        firstDate: firstDate,
        selectedDate: selectedDate,
        lastDate: lastDate,
        presetList: presetList ?? [],
        selectedPreset: selectedPreset,
        selectedTime: selectedTime,
        showInvalidTimeDialog: showInvalidTimeDialog,
        monthPickerInsetPadding:
            ScreenSizeState.instance.getDialogInsetPadding(DialogLevel.level2),
        yearPickerInsetPadding:
            ScreenSizeState.instance.getDialogInsetPadding(DialogLevel.level2),
        isDateTimeSet: isDateTimeSet,
      ),
    ),
  );
}

class MeCalendar extends StatelessWidget {
  const MeCalendar({
    Key? key,
    required this.initialDate,
    required this.firstDate,
    required this.lastDate,
    this.selectedDate,
    this.presetList = const [],
    this.selectedPreset,
    this.selectedTime,
    this.showInvalidTimeDialog,
    required this.monthPickerInsetPadding,
    required this.yearPickerInsetPadding,
    required this.isTmzDependent,
    required this.isDateTimeSet,
  }) : super(key: key);

  /// The initially shown month when the calendar view is created.
  final DateTime initialDate;

  /// The earliest date that the user is permitted to select.
  final DateTime firstDate;

  /// The selected date. If this is non-null, it will be highlighted in the calendar.
  final DateTime? selectedDate;

  /// The latest date that the user is permitted to select.
  final DateTime lastDate;

  /// The list of presets to show in the calendar
  /// defaults to empty list
  final List<MePresetModel> presetList;

  /// The preset to be selected. If this is non-null then the preset is highlighted in the
  /// preset GridView in the calendar. Make sure the selected preset is present in the
  /// [presetList].
  final MePresetModel? selectedPreset;
  final TimeOfDay? selectedTime;
  final bool? showInvalidTimeDialog;
  final EdgeInsets? monthPickerInsetPadding;
  final EdgeInsets? yearPickerInsetPadding;
  final bool isTmzDependent;
  final bool isDateTimeSet;

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => MePresetCubit(selectedPreset),
      child: _MeCalendar(
        initialDate: initialDate,
        firstDate: firstDate,
        lastDate: lastDate,
        selectedDate: selectedDate,
        presetList: presetList,
        selectedTime: selectedTime,
        showInvalidTimeDialog: showInvalidTimeDialog,
        monthPickerInsetPadding: monthPickerInsetPadding,
        yearPickerInsetPadding: yearPickerInsetPadding,
        isTmzDependent: isTmzDependent,
        isDateTimeSet: isDateTimeSet,
      ),
    );
  }
}

class _MeCalendar extends StatefulWidget {
  const _MeCalendar({
    Key? key,
    required this.initialDate,
    required this.firstDate,
    required this.lastDate,
    this.selectedDate,
    this.presetList = const [],
    required this.selectedTime,
    this.showInvalidTimeDialog,
    required this.monthPickerInsetPadding,
    required this.yearPickerInsetPadding,
    required this.isTmzDependent,
    required this.isDateTimeSet,
  }) : super(key: key);
  final DateTime initialDate;
  final DateTime firstDate;
  final DateTime? selectedDate;
  final DateTime lastDate;
  final List<MePresetModel> presetList;
  final TimeOfDay? selectedTime;
  final bool? showInvalidTimeDialog;
  final EdgeInsets? monthPickerInsetPadding;
  final EdgeInsets? yearPickerInsetPadding;
  final bool isTmzDependent;
  final bool isDateTimeSet;

  @override
  State<_MeCalendar> createState() => _MeCalendarState();
}

class _MeCalendarState extends State<_MeCalendar> {
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  bool? _isTmzDependent;

  bool selectionMade = false;

  @override
  void initState() {
    super.initState();
    _selectedDate = widget.selectedDate;
    _selectedTime = widget.selectedTime;
    _isTmzDependent = widget.isTmzDependent;
  }

  bool _isValidDate(DateTime? date) {
    if (date == null) {
      return true;
    }
    return date.isAfterOrEqualTo(widget.firstDate.onlyDate()) &&
        date.isBeforeOrEqualTo(widget.lastDate);
  }

  void _onWillPop(bool value, dynamic result) async {
    if (!value) {
      DateTime selectedDateTime = DateTime(
        _selectedDate!.year,
        _selectedDate!.month,
        _selectedDate!.day,
        _selectedTime!.hour,
        _selectedTime!.minute,
      );
      if (selectionMade == false) {
        Navigator.pop(context);
        return;
      }
      if (selectedDateTime == widget.selectedDate && mounted) {
        Navigator.pop(context);
        return;
      }
      final DiscardDialogActionType res = await showMeDiscardDialogV2(
        context,
        allowActionType: {
          DiscardDialogActionType.discard,
          DiscardDialogActionType.save,
        },
        insetPadding:
            ScreenSizeState.instance.getDialogInsetPadding(DialogLevel.level2),
      );
      if (res == DiscardDialogActionType.discard && mounted) {
        Navigator.pop(context);
      } else if (res == DiscardDialogActionType.save && mounted) {
        Navigator.pop(
          context,
          MeCalendarResult(
            date: DateTime(
              _selectedDate!.year,
              _selectedDate!.month,
              _selectedDate!.day,
              _selectedTime!.hour,
              _selectedTime!.minute,
            ),
            preset: context.read<MePresetCubit>().state,
            isTmzDependent: _isTmzDependent,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Container(
      decoration: BoxDecoration(
        color: colorScheme.color5,
        borderRadius: const BorderRadius.all(Radius.circular(16)),
      ),
      child: _datePicker(context, colorScheme),
    );
  }

  Widget _datePicker(BuildContext context, MeColorScheme colorScheme) {
    DateTime selectedDateTime = DateTime(
      _selectedDate!.year,
      _selectedDate!.month,
      _selectedDate!.day,
      _selectedTime!.hour,
      _selectedTime!.minute,
    );
    return PopScope(
      canPop: selectedDateTime == widget.selectedDate,
      onPopInvokedWithResult: _onWillPop,
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            BlocBuilder<MePresetCubit, MePresetModel?>(
              builder: (context, state) {
                return MePresetsGridView(
                  crossAxisCount:
                      TextFactorChecker.isTextScaleFactorLarge(context)
                          ? 1
                          : null,
                  childAspectRatio:
                      TextFactorChecker.isTextScaleFactorLarge(context)
                          ? 8.5
                          : null,
                  key: ValueKey(state),
                  presets: widget.presetList,
                  disabledPresets: widget.presetList
                      .where(
                        (element) => !_isValidDate(
                          element.calculatedDateTime(),
                        ),
                      )
                      .toList(),
                  selectedIndex:
                      state == null ? null : widget.presetList.indexOf(state),
                  padding: const EdgeInsets.only(
                    bottom: 12,
                  ),
                  onPresetSelected: (selectedIndex) {
                    final preset = widget.presetList[selectedIndex];
                    context.read<MePresetCubit>().updatePreset(preset);
                  },
                );
              },
            ),
            BlocBuilder<MePresetCubit, MePresetModel?>(
              builder: (context, state) {
                return _MonthPicker(
                  monthPickerInsetPadding: widget.monthPickerInsetPadding,
                  yearPickerInsetPadding: widget.yearPickerInsetPadding,
                  firstDate: widget.firstDate,
                  lastDate: widget.lastDate,
                  initialDate: widget.initialDate,
                  selectedDate: _selectedDate ?? state?.calculatedDateTime(),
                  onDateSelected: (value) {
                    setState(() {
                      _selectedDate = value;
                      selectionMade = true;
                    });
                    // if the selected date is in preset then
                    // check if that date is in preset to rebuild the preset gridview.

                    if (value != null) {
                      context.read<MePresetCubit>().checkIfDateIsInPreset(
                            selectedDate: value,
                            presets: widget.presetList,
                          );
                    }
                  },
                  isDateTimeSet: widget.isDateTimeSet,
                );
              },
            ),
            const SizedBox(height: 16),
            GestureDetector(
              onTap: () async {
                (TimeOfDay, bool)? timePickerRes =
                    await showDialog<(TimeOfDay, bool)>(
                  useRootNavigator: false,
                  barrierDismissible: true,
                  context: context,
                  builder: (childContext) => TimePickerWidget(
                    showDeleteOnEdit: false,
                    step: 1,
                    isTimeSet: true,
                    allowTmzAffectedTypeUpdate: true,
                    isTmzAffected: widget.isTmzDependent,
                    selectedTime: _selectedTime ?? TimeOfDay.now(),
                    showInvalidTimeDialog:
                        widget.showInvalidTimeDialog ?? false,
                    selectedDateTime: _selectedDate,
                    insetPadding: ScreenSizeState.instance
                        .getDialogInsetPadding(DialogLevel.level2),
                    discardInnerPadding: ScreenSizeState.instance
                        .getDialogInsetPadding(DialogLevel.level3),
                  ),
                );

                TimeOfDay? pickedTime = timePickerRes?.$1;
                if (pickedTime != null && mounted) {
                  setState(() {
                    _selectedTime = pickedTime;
                    _isTmzDependent = timePickerRes?.$2;
                    selectionMade = true;
                  });
                }
              },
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 16,
                      horizontal: 16,
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Assets.svg.clockIcon.svg(
                          height: 20,
                          colorFilter: ColorFilter.mode(
                            colorScheme.color35,
                            BlendMode.srcIn,
                          ),
                        ),
                        const SizedBox(width: 8),
                        MeText(
                          key: const ValueKey('selectedtime'),
                          text: _selectedTime?.formattedTime() ??
                              MeTranslations.instance.screen_common_time,
                          meFontStyle: MeFontStyle.D8,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Divider(
              height: 1,
              thickness: 1,
              color: colorScheme.color6,
            ),
            _footer(colorScheme),
          ],
        ),
      ),
    );
  }

  Widget _footer(colorScheme) {
    return OverflowBar(
      alignment: MainAxisAlignment.end,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(12, 14, 12, 14),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              MeSecondaryButton(
                title: MeTranslations.instance.screen_common_buttonCancel,
                onPressed: () {
                  _onWillPop(false, null);
                },
              ),
              const SizedBox(
                width: 8,
              ),
              BlocBuilder<MePresetCubit, MePresetModel?>(
                builder: (context, state) {
                  return MePrimaryButton(
                    title: MeTranslations.instance.screen_common_save,
                    onPressed: _selectedDate == null && state == null
                        ? null
                        : () {
                            // selected time is before now
                            if (DateTime(
                                  _selectedDate!.year,
                                  _selectedDate!.month,
                                  _selectedDate!.day,
                                  _selectedTime!.hour,
                                  _selectedTime!.minute,
                                ).isBefore(DateTime.now()) &&
                                (widget.showInvalidTimeDialog != null &&
                                    widget.showInvalidTimeDialog!)) {
                              showDialog(
                                context: context,
                                builder: (context) => MeDialog(
                                  title: MeTranslations.instance
                                      .overlay_invalidTimeSelected_title,
                                  description: MeTranslations.instance
                                      .overlay_invalidTimeSelected_content,
                                  insetPadding: ScreenSizeState.instance
                                      .getDialogInsetPadding(
                                    DialogLevel.level2,
                                  ),
                                  primaryOnTap: () {
                                    Navigator.pop(context);
                                  },
                                  primaryText:
                                      MeTranslations.instance.screen_common_ok,
                                ),
                              );
                              return;
                            }

                            Navigator.pop(
                              context,
                              MeCalendarResult(
                                date: DateTime(
                                  _selectedDate!.year,
                                  _selectedDate!.month,
                                  _selectedDate!.day,
                                  _selectedTime!.hour,
                                  _selectedTime!.minute,
                                ),
                                preset: state,
                                isTmzDependent: _isTmzDependent,
                              ),
                            );
                          },
                  );
                },
              ),
            ],
          ),
        ),
      ],
    );
  }
}

class _MonthPicker extends StatefulWidget {
  _MonthPicker({
    Key? key,
    required DateTime firstDate,
    required DateTime lastDate,
    required DateTime initialDate,
    DateTime? selectedDate,
    required this.onDateSelected,
    required this.monthPickerInsetPadding,
    required this.yearPickerInsetPadding,
    required this.isDateTimeSet,
  })  : initialDate = DateUtils.dateOnly(initialDate),
        firstDate = DateUtils.dateOnly(firstDate),
        lastDate = DateUtils.dateOnly(lastDate),
        selectedDate =
            selectedDate != null ? DateUtils.dateOnly(selectedDate) : null,
        super(key: key);

  final DateTime firstDate;
  final DateTime lastDate;
  final DateTime initialDate;
  final DateTime? selectedDate;

  final ValueChanged<DateTime?> onDateSelected;
  final EdgeInsets? monthPickerInsetPadding;
  final EdgeInsets? yearPickerInsetPadding;
  final bool isDateTimeSet;

  @override
  State<_MonthPicker> createState() => _MonthPickerState();
}

class _MonthPickerState extends State<_MonthPicker> {
  late final PageController _pageController;
  late DateTime _currentMonth;

  DateTime? _selectedDate;

  @override
  void initState() {
    super.initState();
    _currentMonth = DateTime(widget.initialDate.year, widget.initialDate.month);
    _selectedDate = widget.selectedDate;
    _pageController = PageController(
      initialPage: DateUtils.monthDelta(widget.firstDate, _currentMonth),
    );
  }

  void _handleMonthPageChanged(int monthPage) {
    setState(() {
      final DateTime monthDate =
          DateUtils.addMonthsToMonthDate(widget.firstDate, monthPage);
      if (!DateUtils.isSameMonth(_currentMonth, monthDate)) {
        _currentMonth = DateTime(monthDate.year, monthDate.month);
      }
    });
  }

  /// Navigate to the given month.
  void _showMonth(DateTime month, {bool jump = false}) {
    final int monthPage = DateUtils.monthDelta(widget.firstDate, month);
    if (jump) {
      _pageController.jumpToPage(monthPage);
    } else {
      _pageController.animateToPage(
        monthPage,
        duration: _monthScrollDuration,
        curve: Curves.ease,
      );
    }
  }

  void _handleDayChanged(DateTime? value) {
    setState(() {
      _selectedDate = value;
    });
    widget.onDateSelected(value);
  }

  Widget _buildItems(BuildContext context, int index) {
    final DateTime month =
        DateUtils.addMonthsToMonthDate(widget.firstDate, index);
    return _DayPicker(
      key: ValueKey<DateTime>(month),
      selectedDate: _selectedDate,
      onChanged: _handleDayChanged,
      firstDate: widget.firstDate,
      lastDate: widget.lastDate,
      displayedMonth: month,
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return BlocListener<MePresetCubit, MePresetModel?>(
      listener: (context, state) {
        if (state != null) _handlePresetChanged(state);
      },
      child: Column(
        children: [
          Container(
            margin: const EdgeInsets.only(
              top: 12,
              bottom: 14,
              left: 16,
              right: 16,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                // month
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () async {
                      final res = await showMonthPicker(
                        context: context,
                        insetPadding: widget.monthPickerInsetPadding,
                        initialDate: _currentMonth,
                        firstDate: widget.firstDate,
                        lastDate: widget.lastDate,
                        barrierColor:
                            colorScheme.color13.withValues(alpha: 0.7),
                        isMultiSelect: false,
                      );
                      setState(() {
                        if (res != null) {
                          _showMonth(res, jump: true);
                          if (_selectedDate != null) {
                            int lastDayOfMonth =
                                DateUtils.getDaysInMonth(res.year, res.month);
                            int newDay =
                                math.min(_selectedDate!.day, lastDayOfMonth);
                            _selectedDate = DateTime(
                              _selectedDate!.year,
                              res.month,
                              newDay,
                            );
                            widget.onDateSelected(_selectedDate);
                          }
                        }
                      });
                    },
                    child: MeText(
                      text: getShortMonthFromInt(_currentMonth.month),
                      meFontStyle: MeFontStyle.A35,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () async {
                      final res = await showYearPicker(
                        context: context,
                        insetPadding: widget.yearPickerInsetPadding,
                        initialDate: _currentMonth,
                        firstDate: widget.firstDate,
                        lastDate: widget.lastDate,
                        barrierColor:
                            colorScheme.color13.withValues(alpha: 0.7),
                        isMultiSelect: false,
                      );
                      setState(() {
                        if (res != null) {
                          _showMonth(res, jump: true);
                          if (_selectedDate != null) {
                            int lastDayOfMonth =
                                DateUtils.getDaysInMonth(res.year, res.month);
                            int newDay =
                                math.min(_selectedDate!.day, lastDayOfMonth);
                            _selectedDate = DateTime(
                              res.year,
                              _selectedDate!.month,
                              newDay,
                            );
                            widget.onDateSelected(_selectedDate);
                          }
                        }
                      });
                    },
                    child: MeText(
                      text: MeString(
                        DateFormat('yyyy').format(_currentMonth),
                      ),
                      meFontStyle: MeFontStyle.A35,
                    ),
                  ),
                ),
                const Spacer(),
                if (widget.isDateTimeSet)
                  MeIconButton(
                    onPressed: () async {
                      final bool pop = await showDialog<bool>(
                            useRootNavigator: false,
                            context: context,
                            builder: (BuildContext childContext) {
                              return MeDialog(
                                insetPadding: ScreenSizeState.instance
                                    .getDialogInsetPadding(DialogLevel.level2),
                                quaternaryText: MeTranslations
                                    .instance.screen_common_delete,
                                secondaryText: MeTranslations
                                    .instance.screen_common_buttonCancel,
                                title: MeTranslations
                                    .instance.overlay_deleteDateAndTime_title,
                                description: MeTranslations
                                    .instance.overlay_deleteDateAndTime_content,
                                showBottomDivider: false,
                                secondaryOnTap: () {
                                  Navigator.pop(childContext, false);
                                },
                                quaternaryOnTap: () async {
                                  Navigator.pop(childContext, true);
                                },
                              );
                            },
                          ) ??
                          false;
                      if (pop == true && context.mounted) {
                        Navigator.pop(
                          context,
                          MeCalendarResult(
                            isDeleteOptionSelected: true,
                            date: _selectedDate,
                          ),
                        );
                      }
                    },
                    iconPath: Assets.svg.deleteIcon.path,
                    iconSize: const SizedBox(height: 18, width: 18),
                    iconColor: colorScheme.color11,
                  ),
              ],
            ),
          ),
          SizedBox(
            height: _maxDayPickerHeight,
            child: PageView.builder(
              controller: _pageController,
              itemCount:
                  DateUtils.monthDelta(widget.firstDate, widget.lastDate) + 1,
              onPageChanged: _handleMonthPageChanged,
              itemBuilder: _buildItems,
            ),
          ),
        ],
      ),
    );
  }

  void _handlePresetChanged(MePresetModel preset) {
    _handleDayChanged(preset.calculatedDateTime());
    if (preset.calculatedDateTime() != null) {
      _showMonth(preset.calculatedDateTime()!);
    }
  }
}

class _DayPicker extends StatefulWidget {
  const _DayPicker({
    Key? key,
    required this.selectedDate,
    required this.firstDate,
    required this.lastDate,
    required this.displayedMonth,
    required this.onChanged,
  }) : super(key: key);
  final DateTime? selectedDate;
  final DateTime firstDate;
  final DateTime lastDate;
  final DateTime displayedMonth;
  final ValueChanged<DateTime> onChanged;

  @override
  State<_DayPicker> createState() => _DayPickerState();
}

class _DayPickerState extends State<_DayPicker> {
  List<Widget> _dayHeaders() {
    final List<Widget> result = <Widget>[];

    for (int i = 0; i < 7; i++) {
      final String weekday = getShortDayFromInt(i).text;
      result.add(
        ExcludeSemantics(
          child: Center(
            child: MeText(
              text: MeString(
                TextFactorChecker.isTextScaleFactorLarge(context)
                    ? weekday[0]
                    : weekday,
              ),
              meFontStyle: MeFontStyle.F8,
            ),
          ),
        ),
      );
    }
    return result;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final MaterialLocalizations localizations =
        MaterialLocalizations.of(context);
    final int year = widget.displayedMonth.year;
    final int month = widget.displayedMonth.month;
    final int daysInMonth = DateUtils.getDaysInMonth(year, month);
    final int dayOffset = DateUtils.firstDayOffset(year, month, localizations);
    final List<Widget> dayItems = _dayHeaders();

    int day = -dayOffset;
    while (day < daysInMonth) {
      day++;
      if (day < 1) {
        const dayWidget = SizedBox.shrink();
        dayItems.add(dayWidget);
      } else if (day > daysInMonth) {
        Widget dayWidget = Center(
          child: MeText(
            text: MeString(
              NumberFormat.decimalPattern().format(day - daysInMonth),
            ),
            meFontStyle: MeFontStyle.F10,
          ),
        );
        dayItems.add(dayWidget);
      } else {
        final DateTime dayToBuild = DateTime(year, month, day);
        final DateTime currentDate = DateTime.now();
        final bool isToday = DateUtils.isSameDay(currentDate, dayToBuild);
        final bool isDisabled = dayToBuild.isAfter(widget.lastDate) ||
            dayToBuild.isBefore(widget.firstDate);
        final bool isSelectedDay =
            DateUtils.isSameDay(widget.selectedDate, dayToBuild);
        // final bool isToday =
        //     DateUtils.isSameDay(widget.currentDate, dayToBuild);

        BoxDecoration? decoration;
        MeFontStyle fontStyle = MeFontStyle.F8;
        if (isSelectedDay) {
          // The selected day gets a circle background highlight, and a
          // contrasting text color.
          fontStyle = MeFontStyle.F12;
          decoration = BoxDecoration(
            color: colorScheme.color1,
            shape: BoxShape.circle,
          );
        } else if (isToday) {
          // The current day gets a different text color and a circle stroke
          // border.

          fontStyle = !isDisabled ? MeFontStyle.F1 : MeFontStyle.F10;
          decoration = BoxDecoration(
            border: Border.all(
              color: !isDisabled ? colorScheme.color1 : colorScheme.color10,
            ),
            shape: BoxShape.circle,
          );
        } else if (isDisabled) {
          fontStyle = MeFontStyle.F10;
        }
        Widget dayWidget = Container(
          padding: const EdgeInsets.all(8),
          child: Container(
            decoration: decoration,
            child: Center(
              child: Text(
                localizations.formatDecimal(day),
                style: MeTextTheme.getMeFontStyle(
                  fontStyle: fontStyle,
                  context: context,
                ),
              ),
            ),
          ),
        );

        if (isDisabled) {
          dayWidget = ExcludeSemantics(
            child: dayWidget,
          );
        } else {
          dayWidget = InkResponse(
            onTap: () {
              widget.onChanged(dayToBuild);
            },
            radius: _dayPickerRowHeight / 2 + 4,
            child: Semantics(
              // We want the day of month to be spoken first irrespective of the
              // locale-specific preferences or TextDirection. This is because
              // an accessibility user is more likely to be interested in the
              // day of month before the rest of the date, as they are looking
              // for the day of month. To do that we prepend day of month to the
              // formatted full date.
              label:
                  '${localizations.formatDecimal(day)}, ${localizations.formatFullDate(dayToBuild)}',
              selected: isSelectedDay,
              excludeSemantics: true,
              child: dayWidget,
            ),
          );
        }

        dayItems.add(dayWidget);
      }
    }

    return GridView.custom(
      gridDelegate: _dayPickerGridDelegate,
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      physics: const NeverScrollableScrollPhysics(),
      childrenDelegate: SliverChildListDelegate(
        dayItems,
        addRepaintBoundaries: false,
      ),
    );
  }
}

class DayPickerGridDelegate extends SliverGridDelegate {
  const DayPickerGridDelegate();

  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    const int columnCount = DateTime.daysPerWeek;
    final double tileWidth = constraints.crossAxisExtent / columnCount;
    final double tileHeight = math.min(
      _dayPickerRowHeight,
      constraints.viewportMainAxisExtent / (_maxDayPickerRowCount + 1),
    );
    return SliverGridRegularTileLayout(
      childCrossAxisExtent: tileWidth,
      childMainAxisExtent: tileHeight,
      crossAxisCount: columnCount,
      crossAxisStride: tileWidth,
      mainAxisStride: tileHeight,
      reverseCrossAxis: axisDirectionIsReversed(constraints.crossAxisDirection),
    );
  }

  @override
  bool shouldRelayout(DayPickerGridDelegate oldDelegate) => false;
}

const DayPickerGridDelegate _dayPickerGridDelegate = DayPickerGridDelegate();
