import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/custom/me_date_time.dart';
import 'package:mevolve/data/models/daily_agenda_notif.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/user/view_settings.dart';
import 'package:mevolve/data/providers/shared_prefs.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/hamburger/notifications/widgets/date_time_select_bottomsheet.dart';
import 'package:mevolve/features/hamburger/notifications/widgets/snooze_bottomsheet.dart';
import 'package:mevolve/features/hamburger/notifications/widgets/sound_bottomsheet.dart';
import 'package:mevolve/features/hamburger/settings/widgets/settings_widgets/settings_list_tile.dart';
import 'package:mevolve/features/hamburger/widgets/drawer_component_header.dart';
import 'package:mevolve/features/hamburger/widgets/drawer_list_switch_button.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/features/widgets/time_picker_widget.dart';
import 'package:mevolve/features/widgets/toggle_tile.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/nullable.dart';
import 'package:mevolve/utilities/utility_methods.dart';

class SettingsMobileNotificationComponent extends StatefulWidget {
  const SettingsMobileNotificationComponent({
    super.key,
  });

  @override
  State<SettingsMobileNotificationComponent> createState() =>
      _SettingsMobileNotificationComponentState();
}

class _SettingsMobileNotificationComponentState
    extends State<SettingsMobileNotificationComponent> {
  late Timer _timer;

  @override
  void initState() {
    super.initState();

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      ViewSettings viewSettings = context.read<AppBloc>().state.viewSettings!;
      DateTime? muteThisDevice =
          parseDate(context.read<SharedPreferencesClient>().muteThisDevice());
      if (muteThisDevice != null &&
          DateTime.now().isAfterOrEqualTo(muteThisDevice)) {
        context.read<SharedPreferencesClient>().disableMuteThisDevice();
        setState(() {});
      }
      if (viewSettings.notificationSettings.tmzSafeMuteAllDeviceUtil != null &&
          DateTime.now().isAfterOrEqualTo(
            viewSettings.notificationSettings.tmzSafeMuteAllDeviceUtil!,
          )) {
        context.read<AppBloc>().updateViewSettings(
              viewSettings:
                  context.read<AppBloc>().state.viewSettings!.copyWith(
                        notificationSettings: context
                            .read<AppBloc>()
                            .state
                            .viewSettings!
                            .notificationSettings
                            .copyWith(
                              muteAllDevice: const Nullable(null),
                              muteAllDeviceStartAt: const Nullable(null),
                            ),
                      ),
            );
      }
    });
  }

  void refreshScreen() {
    setState(() {});
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  TimeOfDay getDailyAgendaMobileTime(
    ViewSettings viewSettings, {
    bool val = false,
  }) {
    int? hour;
    int? minute;
    final MeDateTime? mobileDailyAgendaTime =
        viewSettings.notificationSettings.mobileDailyAgendaNotificationTime;
    bool isTmzAffected =
        viewSettings.notificationSettings.isDailyAgendaTmzDependent;
    DateTime? tmzSafeDateTime = UtilityMethods.getDateTimeForTask(
      mobileDailyAgendaTime,
      true,
      !(true && isTmzAffected),
    );
    if (val) {
      hour = 7;
      minute = 0;
    } else if (mobileDailyAgendaTime != null) {
      final TimeOfDay timeOfDay = tmzSafeDateTime != null
          ? tmzSafeDateTime.toTimeOfDay
          : mobileDailyAgendaTime.toTimeOfDay();
      hour = timeOfDay.hour;
      hour = timeOfDay.hour;
      minute = timeOfDay.minute;
    } else {
      hour = 7;
      minute = 0;
    }
    return TimeOfDay(
      hour: hour,
      minute: minute,
    );
  }

  Future<bool> hasNotificationPermission() async {
    final bool currentPermissionValue = MePlatform.isWeb || MePlatform.isWindows
        ? true
        : await UtilityMethods.isNotificationAllowed();
    if (currentPermissionValue == false) {
      UtilityMethods.manageAppNotificationPermission();
      return false;
    }
    return true;
  }

  @override
  Widget build(BuildContext context) {
    // Only disable for platforms that don't support notifications
    // macOS has full notification support, so it should not be disabled
    bool disableGeneral = MePlatform.isWeb || MePlatform.isWindows;
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: BlocBuilder<AppBloc, AppState>(
        builder: (_, state) {
          final viewSettings = state.viewSettings;
          final muteAllDevice =
              viewSettings!.notificationSettings.tmzSafeMuteAllDeviceUtil;
          final DateTime? muteThisDevice = parseDate(
            context.read<SharedPreferencesClient>().muteThisDevice(),
          );
          final bool canShow = muteAllDevice == null ? true : false;
          final bool canShowMuteThisDevice =
              muteThisDevice == null ? true : false;
          // Decide if we need to show the notification permission dialog.
          return Column(
            children: [
              DrawerComponentHeader(
                headerText: MeTranslations
                    .instance.screen_notifications_sectionTitleGeneral,
                isDisabled: disableGeneral,
              ),
              Wrap(
                runSpacing: 1,
                children: [
                  Visibility(
                    visible: canShow && canShowMuteThisDevice,
                    child: SettingsListTile(
                      isDisabled: disableGeneral,
                      onTap: () async {
                        bool permission = await hasNotificationPermission();
                        if (!permission) return;
                        showMeScrollableModalBottomSheet(
                          bottomSheetLevel: 1,
                          builder: (_, setShowDiscardDialogFlagValue) =>
                              SoundBottomSheet(
                            setShowDiscardDialogFlagValue:
                                setShowDiscardDialogFlagValue,
                          ),
                        );
                      },
                      dense: true,
                      title: MeTranslations.instance.screen_common_sound,
                      trailing: viewSettings
                          .notificationSettings.mobileSoundType
                          .toMeString(),
                    ),
                  ),
                  Visibility(
                    visible: canShow && canShowMuteThisDevice,
                    child: SettingsListTile(
                      isDisabled: disableGeneral,
                      onTap: () async {
                        bool permission = await hasNotificationPermission();
                        if (!permission) return;
                        showMeScrollableModalBottomSheet(
                          bottomSheetLevel: 1,
                          builder: (_, __) => const SnoozeBottomSheet(),
                        );
                      },
                      dense: true,
                      title: MeTranslations.instance
                          .screen_notifications_mobileNotificationsSnoozeDuration,
                      trailing: viewSettings
                          .notificationSettings.mobileSnoozeType
                          .toMeString(),
                    ),
                  ),
                  Visibility(
                    visible: canShow && canShowMuteThisDevice,
                    child: ToggleTile(
                      isDisabled: disableGeneral,
                      listTileCrossAxisAlignment: CrossAxisAlignment.start,
                      title: MeTranslations.instance
                          .screen_notifications_mobileNotificationsPinReminder,
                      subtitle: MeTranslations.instance
                          .screen_notifications_mobileNotificationsPinReminderContent,
                      value: viewSettings.notificationSettings.pinReminder,
                      onToggleTap: () async {
                        bool permission = await hasNotificationPermission();
                        if (!permission) return;
                        if (context.mounted) {
                          context.read<AppBloc>().updateViewSettings(
                                viewSettings: viewSettings.copyWith(
                                  notificationSettings: viewSettings
                                      .notificationSettings
                                      .copyWith(
                                    pinReminder: !viewSettings
                                        .notificationSettings.pinReminder,
                                  ),
                                ),
                              );
                        }
                      },
                    ),
                  ),
                  DrawerListSwitchButton(
                    dense: true,
                    isDisabled: disableGeneral,
                    title: MeTranslations.instance
                        .screen_notifications_mobileNotificationsMuteAllDevice,
                    subtitle: viewSettings.notificationSettings
                                .tmzSafeMuteAllDeviceUtil ==
                            null
                        ? null
                        : viewSettings.notificationSettings
                                    .tmzSafeMuteAllDeviceUtil!.year ==
                                9999
                            ? MeTranslations.instance
                                .bottomSheet_notificationsMuteDuration_muteForAlways
                            : MeTranslations.instance
                                .screen_notifications_mobileNotificationsMuteDeviceInfo(
                                time: viewSettings.notificationSettings
                                    .tmzSafeMuteAllDeviceUtil!
                                    .formattedTime()
                                    .text,
                                date: viewSettings.notificationSettings
                                    .tmzSafeMuteAllDeviceUtil!
                                    .formattedDate()
                                    .text,
                              ),
                    value: viewSettings.notificationSettings
                                .tmzSafeMuteAllDeviceUtil ==
                            null
                        ? false
                        : true,
                    onSubtitleTap: () {
                      showMeScrollableModalBottomSheet(
                        bottomSheetLevel: 1,
                        builder: (_, setShowDiscardDialogFlagValue) =>
                            DateTimeSelectBottomSheet(
                          startAt: viewSettings
                              .notificationSettings.tmzSafeMuteAllDeviceStartAt,
                          endAt: viewSettings
                              .notificationSettings.tmzSafeMuteAllDeviceUtil,
                          isMuteAllDevice: true,
                          refresh: refreshScreen,
                          customPresetSelected: viewSettings
                              .notificationSettings.muteAllCustomPresetSelected,
                          setShowDiscardDialogFlagValue:
                              setShowDiscardDialogFlagValue,
                        ),
                      );
                    },
                    onChanged: (value) async {
                      if (muteAllDevice == null) {
                        showMeScrollableModalBottomSheet(
                          bottomSheetLevel: 1,
                          builder: (_, setShowDiscardDialogFlagValue) =>
                              DateTimeSelectBottomSheet(
                            startAt: viewSettings.notificationSettings
                                .tmzSafeMuteAllDeviceStartAt,
                            endAt: viewSettings
                                .notificationSettings.tmzSafeMuteAllDeviceUtil,
                            isMuteAllDevice: true,
                            refresh: refreshScreen,
                            customPresetSelected: viewSettings
                                    .notificationSettings
                                    .muteAllCustomPresetSelected &&
                                viewSettings.notificationSettings
                                        .tmzSafeMuteAllDeviceUtil !=
                                    null,
                            setShowDiscardDialogFlagValue:
                                setShowDiscardDialogFlagValue,
                          ),
                        );
                      } else {
                        if (context.mounted) {
                          context.read<AppBloc>().updateViewSettings(
                                viewSettings: viewSettings.copyWith(
                                  notificationSettings: viewSettings
                                      .notificationSettings
                                      .copyWith(
                                    muteAllDevice: const Nullable(null),
                                    muteAllDeviceStartAt: const Nullable(null),
                                    muteAllCustomPresetSelected: false,
                                  ),
                                ),
                              );
                        }
                      }
                    },
                  ),
                  Visibility(
                    visible: canShow,
                    child: DrawerListSwitchButton(
                      dense: true,
                      isDisabled: disableGeneral,
                      title: MeTranslations.instance
                          .screen_notifications_mobileNotificationsMuteThisDevice,
                      subtitle: muteThisDevice == null
                          ? null
                          : muteThisDevice.year == 9999
                              ? MeTranslations.instance
                                  .bottomSheet_notificationsMuteDuration_muteForAlways
                              : MeTranslations.instance
                                  .screen_notifications_mobileNotificationsMuteDeviceInfo(
                                  time: muteThisDevice.formattedTime().text,
                                  date: muteThisDevice.formattedDate().text,
                                ),
                      value: muteThisDevice == null ? false : true,
                      onSubtitleTap: () {
                        showMeScrollableModalBottomSheet(
                          bottomSheetLevel: 1,
                          builder: (_, setShowDiscardDialogFlagValue) =>
                              DateTimeSelectBottomSheet(
                            startAt: parseDate(
                              context
                                  .read<SharedPreferencesClient>()
                                  .muteThisDeviceStartAt(),
                            ),
                            endAt: muteThisDevice,
                            refresh: refreshScreen,
                            customPresetSelected: context
                                .read<SharedPreferencesClient>()
                                .isMuteThisDeviceCustomPreset(),
                            setShowDiscardDialogFlagValue:
                                setShowDiscardDialogFlagValue,
                          ),
                        );
                      },
                      onChanged: (value) async {
                        if (muteThisDevice == null) {
                          showMeScrollableModalBottomSheet(
                            bottomSheetLevel: 1,
                            builder: (_, setShowDiscardDialogFlagValue) =>
                                DateTimeSelectBottomSheet(
                              startAt: parseDate(
                                context
                                    .read<SharedPreferencesClient>()
                                    .muteThisDeviceStartAt(),
                              ),
                              endAt: muteThisDevice,
                              refresh: refreshScreen,
                              customPresetSelected: context
                                  .read<SharedPreferencesClient>()
                                  .isMuteThisDeviceCustomPreset(),
                              setShowDiscardDialogFlagValue:
                                  setShowDiscardDialogFlagValue,
                            ),
                          );
                        } else {
                          if (context.mounted) {
                            await context
                                .read<SharedPreferencesClient>()
                                .disableMuteThisDevice();
                          }
                        }
                        setState(() {});
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4),
              Visibility(
                visible: canShow && canShowMuteThisDevice,
                child: DrawerComponentHeader(
                  headerText: MeTranslations
                      .instance.screen_notifications_sectionTitleDailyAgenda,
                  isDisabled: false,
                ),
              ),
              Visibility(
                visible: canShow && canShowMuteThisDevice,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    DrawerListSwitchButton(
                      isDisabled: false,
                      title: MeTranslations
                          .instance.screen_notifications_dailyAgenda,
                      value: viewSettings.notificationSettings
                          .isDailyAgendaMobileNotificationEnabled,
                      dense: viewSettings.notificationSettings
                          .isDailyAgendaMobileNotificationEnabled,
                      onChanged: (value) async {
                        bool permission = await hasNotificationPermission();
                        if (!permission) return;
                        if (context.mounted) {
                          context.read<AppBloc>().updateViewSettings(
                                viewSettings: viewSettings.copyWith(
                                  notificationSettings: viewSettings
                                      .notificationSettings
                                      .copyWith(
                                    isDailyAgendaMobileNotificationEnabled:
                                        value,
                                    mobileDailyAgendaNotificationTime: value
                                        ? MeDateTime.fromDate(
                                            DateTime(
                                              DateTime.now().year,
                                              DateTime.now().month,
                                              DateTime.now().day,
                                              getDailyAgendaMobileTime(
                                                viewSettings,
                                                val: true,
                                              ).hour,
                                              getDailyAgendaMobileTime(
                                                viewSettings,
                                                val: true,
                                              ).minute,
                                            ),
                                          )
                                        : MeDateTime.fromDate(
                                            DateTime(
                                              DateTime.now().year,
                                              DateTime.now().month,
                                              DateTime.now().day,
                                              7,
                                              0,
                                            ),
                                          ),
                                  ),
                                ),
                              );
                          if (!value && context.mounted) {
                            DailyAgendaNotif? dailyAgendaNotif = await context
                                .read<DatabaseRepository>()
                                .getDailyAgendaNotif();
                            if (dailyAgendaNotif != null && context.mounted) {
                              context
                                  .read<SharedPreferencesClient>()
                                  .removeDailyAgendaSyncAt();
                              await context
                                  .read<DatabaseRepository>()
                                  .addOrUpdateDailyAgendaNotif(
                                    dailyAgendaNotif: dailyAgendaNotif.copyWith(
                                      dailyAgendaDismissedUntil: '',
                                      dailyAgendaLastNotifiedAt:
                                          const Nullable(null),
                                    ),
                                  );
                            }
                          }
                        }
                      },
                    ),
                    if (viewSettings.notificationSettings
                        .isDailyAgendaMobileNotificationEnabled) ...[
                      const SizedBox(height: 1),
                      SettingsListTile(
                        dense: true,
                        onTap: () async {
                          bool permission = await hasNotificationPermission();
                          if (!permission) return;
                          if (context.mounted) {
                            (TimeOfDay?, bool?)? pickedTime = await showDialog(
                              useRootNavigator: false,
                              barrierDismissible: true,
                              context: context,
                              builder: (childContext) => TimePickerWidget(
                                showDeleteOnEdit: false,
                                step: 5,
                                isTimeSet: true,
                                isTmzAffected: viewSettings.notificationSettings
                                    .isDailyAgendaTmzDependent,
                                selectedTime: getDailyAgendaMobileTime(
                                  viewSettings,
                                ),
                                insetPadding: ScreenSizeState.instance
                                    .getDialogInsetPadding(DialogLevel.level1),
                              ),
                            );
                            if (pickedTime == null) return;
                            if (pickedTime.$1 != null && context.mounted) {
                              await context.read<AppBloc>().updateViewSettings(
                                    viewSettings: viewSettings.copyWith(
                                      notificationSettings: viewSettings
                                          .notificationSettings
                                          .copyWith(
                                        mobileDailyAgendaNotificationTime:
                                            MeDateTime.fromDate(
                                          DateTime(
                                            DateTime.now().year,
                                            DateTime.now().month,
                                            DateTime.now().day,
                                            pickedTime.$1!.hour,
                                            pickedTime.$1!.minute,
                                          ),
                                        ),
                                        isDailyAgendaTmzDependent:
                                            pickedTime.$2,
                                      ),
                                    ),
                                  );
                            }
                          }
                        },
                        title: MeTranslations
                            .instance.screen_notifications_receiveDailyAgenda,
                        trailing: MeString(
                          getDailyAgendaMobileTime(viewSettings)
                              .format(context),
                        ), //? MeString
                      ),
                    ],
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }
}
