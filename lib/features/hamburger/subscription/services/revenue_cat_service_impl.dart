import 'dart:async';
import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:mevolve/constants/app_config.dart';
import 'package:mevolve/data/enums/environment_type.dart';
import 'package:mevolve/data/enums/purchase/subscription_state.dart';
import 'package:mevolve/data/enums/purchase/subscription_type.dart';
import 'package:mevolve/data/providers/shared_prefs.dart';
import 'package:mevolve/features/hamburger/subscription/models/purchase_result.dart';
import 'package:mevolve/features/hamburger/subscription/models/subscription_product.dart';
import 'package:mevolve/features/hamburger/subscription/models/subscription_status.dart';
import 'package:mevolve/features/hamburger/subscription/purchase_cubit/in_app_purchase_state.dart';
import 'package:mevolve/features/hamburger/subscription/services/purchase_service.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:url_launcher/url_launcher.dart';

/// RevenueCat implementation of PurchaseService for iOS and Android
class RevenueCatService implements PurchaseService {
  RevenueCatService() : _log = MeLogger.getLogger(LogTags.payment);

  // ============================================================================
  // Dependencies & State
  // ============================================================================

  final Log _log;
  late CustomerInfo _customerInfo;
  final StreamController<SubscriptionStatus> _statusController =
      StreamController<SubscriptionStatus>.broadcast();

  /// Temporary flag to bypass RevenueCat's automatic status updates
  /// TODO: Investigate why RevenueCat returns inconsistent subscription data:
  /// - Sometimes returns empty/free status when user has active subscription
  /// - Purchase info listener can trigger unwanted status changes
  /// - syncPurchases() fixes it but risks subscription transfers
  /// Enable this flag to prevent RevenueCat from overwriting valid subscription data
  static const bool _bypassRevenueCatAutoUpdates = true;

  // ============================================================================
  // Initialization & Configuration
  // ============================================================================

  @override
  Future<void> initialize(String userId, String userEmail) async {
    _log.i('Initializing RevenueCat SDK');

    await Purchases.setLogLevel(kDebugMode ? LogLevel.error : LogLevel.info);

    final purchasesConfiguration = MePlatform.isAndroid || MePlatform.isIOS || MePlatform.isMacOS
        ? PurchasesConfiguration(_getRevenueCatApiKey())
        : null;

    if (purchasesConfiguration != null) {
      await Purchases.configure(purchasesConfiguration);

      // Remove any existing listener after SDK is configured
      try {
        Purchases.removeCustomerInfoUpdateListener(_purchaseInfoListener);
      } catch (e) {
        _log.d('No existing listener to remove: $e');
      }

      await Purchases.logIn(userId);

      // Only add listener if not bypassing auto updates
      if (!_bypassRevenueCatAutoUpdates) {
        Purchases.addCustomerInfoUpdateListener(_purchaseInfoListener);
      } else {
        _log.d('Bypassing RevenueCat auto updates - listener not added');
      }

      await _fetchCustomerInfo();
    }
  }

  @override
  Future<void> logout() async {
    _log.d('Logging out from RevenueCat SDK');

    try {
      Purchases.removeCustomerInfoUpdateListener(_purchaseInfoListener);
    } catch (e) {
      _log.d('Error removing listener during logout: $e');
    }

    try {
      await Purchases.logOut();
    } catch (e) {
      _log.e('Error during RevenueCat logout: $e');
      // Continue with cleanup even if logout fails
    }

    // Clear cached customer info
    try {
      _customerInfo = CustomerInfo.fromJson({});
    } catch (e) {
      _log.d('Intentionally clearing customer forcefully');
    }
  }

  // ============================================================================
  // Product Management
  // ============================================================================

  @override
  Future<List<SubscriptionProduct>> getProducts() async {
    _log.d('Fetching offerings');
    try {
      // Try to load saved plans first
      final savedPlansJson =
          SharedPreferencesClient.instance.getUserSubscriptionPlans();
      if (savedPlansJson != null) {
        List<dynamic> jsonData = jsonDecode(savedPlansJson);
        List<Package> packageList =
            jsonData.map((item) => Package.fromJson(item)).toList();
        return _convertPackagesToProducts(packageList);
      }

      // Fetch from RevenueCat
      final offerings = await Purchases.getOfferings();
      if (offerings.current != null &&
          offerings.current!.availablePackages.isNotEmpty) {
        await SharedPreferencesClient.instance.setUserSubscriptionPlans(
          jsonEncode(offerings.current!.availablePackages),
        );
        return _convertPackagesToProducts(offerings.current!.availablePackages);
      }

      return [];
    } on PlatformException catch (e) {
      _log.e('Error loading current offerings: ${e.message}');
      return [];
    }
  }

  // ============================================================================
  // Subscription Status Management
  // ============================================================================

  @override
  Future<SubscriptionStatus> getSubscriptionStatus() async {
    await _fetchCustomerInfo();
    return _convertCustomerInfoToStatus(_customerInfo);
  }

  String? _currentSubIdForAndroidPurchaseFlow(
    String? providedSubscriptionId,
  ) {
    // Normalize provided subscription ID by replacing special characters
    String? normalizedProvidedId = providedSubscriptionId
        ?.replaceAll('-', ' ')
        .replaceAll('_', ' ')
        .replaceAll(':', ' ');
    if (normalizedProvidedId != null) {
      // Split by spaces and the use do lower case and then add to set
      Set<String> providedIdsSet =
          normalizedProvidedId.toLowerCase().split(' ').toSet();
      // Join with _ to normalize
      normalizedProvidedId = providedIdsSet.join('_');
    }

    String? firstActiveSubscriptionId;
    for (int i = 0; i < _customerInfo.activeSubscriptions.length; i++) {
      // Normalize each subscription ID by replacing special characters
      String normalizedSubscription = _customerInfo.activeSubscriptions[i]
          .replaceAll('-', ' ')
          .replaceAll('_', ' ')
          .replaceAll(':', ' ');
      // Split by spaces and then use lower case and then add to set
      Set<String> subscriptionIdsSet =
          normalizedSubscription.toLowerCase().split(' ').toSet();
      // Join with _ to normalize
      normalizedSubscription = subscriptionIdsSet.join('_');
      if (i == 0) {
        // Store the first active subscription ID for fallback
        firstActiveSubscriptionId = normalizedSubscription;
      }
      if (normalizedProvidedId != null &&
          normalizedSubscription.contains(normalizedProvidedId)) {
        return normalizedSubscription;
      }
    }

    if (firstActiveSubscriptionId != null) {
      _log.w(
        'Provided subscription ID not found in active subscriptions. Using first active subscription as fallback: $firstActiveSubscriptionId',
      );
    } else {
      _log.w(
        'No active subscriptions found. Returning null as fallback.',
      );
    }
    return firstActiveSubscriptionId;
  }

  // ============================================================================
  // Purchase Operations
  // ============================================================================

  @override
  Future<PurchaseResult> makePurchase(
    String productId,
    MeUserEntitlement selectedEntitlement, {
    String? currentActiveProductId,
  }) async {
    _log.i(
      'Starting RevenueCat purchase for product: $productId, entitlement: $selectedEntitlement',
    );
    _log.i('Provided currentActiveProductId: $currentActiveProductId');

    // For Android:
    // If currentActiveProductId is provided then look for currentActiveProductId
    // inside _customerInfo.activeSubscriptions. If it doesn't exist then try to
    // use first active subscription as fallback if not empty else use null.
    String? activeSubscriptionToUse = _currentSubIdForAndroidPurchaseFlow(
      currentActiveProductId,
    );
    _log.i(
      'Android purchase using active product id as: $activeSubscriptionToUse for flow',
    );

    try {
      final offerings = await Purchases.getOfferings();
      final package = _findPackageById(offerings, productId);

      if (package == null) {
        _log.e('Package not found for productId: $productId');
        return PurchaseResult.failure(
          error: 'Product not found: $productId',
          subscriptionStatus: await getSubscriptionStatus(),
        );
      }

      _log.i('Found package.identifier: ${package.identifier}');

      CustomerInfo customerInfo;

      if (MePlatform.isAndroid && activeSubscriptionToUse != null) {
        // Handle Android product changes
        customerInfo = await _handleAndroidSubscriptionChange(
          package,
          selectedEntitlement,
          activeSubscriptionToUse,
        );
      } else {
        // Handle iOS or new purchases
        customerInfo = await Purchases.purchasePackage(package);
      }

      // Sync and refresh
      await syncPurchases();
      _customerInfo = await Purchases.getCustomerInfo();

      final subscriptionStatus = _convertCustomerInfoToStatus(customerInfo);
      _statusController.add(subscriptionStatus);

      return PurchaseResult.success(
        subscriptionStatus: subscriptionStatus,
        transactionId: customerInfo.activeSubscriptions.isNotEmpty
            ? customerInfo.activeSubscriptions.first
            : null,
      );
    } on PlatformException catch (e) {
      final errorCode = PurchasesErrorHelper.getErrorCode(e);
      if (errorCode == PurchasesErrorCode.purchaseCancelledError) {
        return PurchaseResult.cancelled(
          subscriptionStatus: await getSubscriptionStatus(),
        );
      } else {
        return PurchaseResult.failure(
          error: e.message ?? 'Unknown error',
          subscriptionStatus: await getSubscriptionStatus(),
        );
      }
    } catch (error) {
      return PurchaseResult.failure(
        error: error.toString(),
        subscriptionStatus: await getSubscriptionStatus(),
      );
    }
  }

  /// Handles Android subscription changes with appropriate proration modes
  /// This method is specifically for Android when user has an active subscription
  Future<CustomerInfo> _handleAndroidSubscriptionChange(
    Package package,
    MeUserEntitlement selectedEntitlement,
    String activeSubscription,
  ) async {
    _log.i(
      'Purchase going from subscription: $activeSubscription to new package: ${package.identifier}',
    );

    // Check if it's the same base product
    final currentBaseProductId = activeSubscription.split('_').first;
    final newBaseProductId = package.identifier.split('_').first;

    _log.i(
      'Base product comparison - current: $currentBaseProductId, new: $newBaseProductId',
    );

    if (currentBaseProductId == newBaseProductId) {
      // Same product - don't use GoogleProductChangeInfo
      _log.i('Same product detected, proceeding without product change info');
      return await Purchases.purchasePackage(package);
    }

    // Different product - handle the change with appropriate proration
    final currentEntitlement = MeUserEntitlement.parseServerEntitlement(
      activeSubscription,
    );

    // Determine if it's an upgrade (moving to higher tier)
    final isUpgrade = selectedEntitlement.index > currentEntitlement.index;

    // Determine the proration mode
    GoogleProrationMode prorationMode;

    // Check if it's a billing period change (yearly <-> monthly)
    final currentType = _getCurrentSubscriptionType();
    final newType = _parseSubscriptionTypeFromPackageId(package.identifier);

    if (isUpgrade) {
      // Tier upgrade (basic -> plus, plus -> pro, etc.)
      if (currentType == SubscriptionType.yearly &&
          newType == SubscriptionType.monthly) {
        prorationMode = GoogleProrationMode.immediateWithTimeProration;
        _log.i(
          'Using IMMEDIATE_WITH_TIME_PRORATION for yearly to monthly tier upgrade',
        );
      } else {
        prorationMode = GoogleProrationMode.immediateAndChargeProratedPrice;
        _log.i(
          'Using IMMEDIATE_AND_CHARGE_PRORATED_PRICE for standard tier upgrade',
        );
      }
    } else {
      // Downgrade to lower tier with same billing period
      prorationMode = GoogleProrationMode.deferred;
      _log.i('Using DEFERRED for tier downgrade');
    }

    // All Android subscription changes need GoogleProductChangeInfo
    final oldSku = currentBaseProductId;
    _log.i(
      'Different base product - using GoogleProductChangeInfo: oldSku=$oldSku, prorationMode=$prorationMode',
    );

    return await Purchases.purchasePackage(
      package,
      googleProductChangeInfo: GoogleProductChangeInfo(
        oldSku,
        prorationMode: prorationMode,
      ),
    );
  }

  // ============================================================================
  // Restore & Sync Operations
  // ============================================================================

  @override
  Future<MeUserEntitlement> restorePurchases() async {
    _log.d('Restoring purchase');

    // Get current subscription status before restore
    final currentStatus = _statusController.hasListener
        ? _convertCustomerInfoToStatus(_customerInfo)
        : null;

    _customerInfo = await Purchases.restorePurchases();
    final restoredStatus = _convertCustomerInfoToStatus(_customerInfo);

    // Only apply restore if current subscription is free/expired OR restore found an active subscription
    if (_shouldApplyRestoredStatus(currentStatus, restoredStatus)) {
      _log.i(
        'Applying restored subscription status: ${restoredStatus.entitlement}',
      );
      _statusController.add(restoredStatus);
      return restoredStatus.entitlement;
    } else {
      _log.i(
        'Keeping current subscription, restore returned free/expired status',
      );
      _log.d(
        'Current: ${currentStatus?.entitlement}, Restored: ${restoredStatus.entitlement}',
      );
      return currentStatus?.entitlement ?? MeUserEntitlement.free;
    }
  }

  /// May lead to unintended subscription transfers of account by syncing purchases to revenue cat
  /// Also, no need for our case so not using it.
  @override
  Future<MeUserEntitlement> syncPurchases() async {
    _log.d('Syncing purchases');
    await Purchases.syncPurchases();
    _customerInfo = await Purchases.getCustomerInfo();
    final status = _convertCustomerInfoToStatus(_customerInfo);
    _statusController.add(status);
    return status.entitlement;
  }

  @override
  Future<void> refreshSubscriptionStatus(
    SubscriptionStatus storedStatus,
  ) async {
    _log.d('Refreshing customer info');
    await _fetchCustomerInfo();
  }

  // ============================================================================
  // Subscription Management & Utilities
  // ============================================================================

  @override
  Stream<SubscriptionStatus> subscriptionStatusStream() {
    return _statusController.stream;
  }

  @override
  bool isPlatformCorrectForPurchase(SubscriptionStatus currentStatus) {
    return currentStatus.subscriptionState != SubscriptionState.subscribed ||
        ((MePlatform.isAndroid && currentStatus.storeType == 'play_store') ||
            (MePlatform.isIOS && currentStatus.storeType == 'app_store'));
  }

  @override
  Future<String?> getSubscriptionManagementUrl() async {
    if (MePlatform.isAndroid) {
      return 'https://play.google.com/store/account/subscriptions';
    } else if (MePlatform.isIOS) {
      return 'https://apps.apple.com/account/subscriptions';
    }
    return null;
  }

  @override
  Future<bool> cancelSubscription() async {
    final url = await getSubscriptionManagementUrl();
    if (url != null) {
      final uri = Uri.parse(url);
      return await launchUrl(uri);
    }
    return false;
  }

  @override
  Future<void> dispose() async {
    try {
      Purchases.removeCustomerInfoUpdateListener(_purchaseInfoListener);
    } catch (e) {
      _log.d('Error removing listener during dispose: $e');
    }
    await logout();
    await _statusController.close();
  }

  @override
  Future<SubscriptionStatus?> verifyStoredSubscription(
    SubscriptionStatus storedStatus,
  ) async {
    if (_bypassRevenueCatAutoUpdates) {
      _log.d('Skipping subscription verification as per configuration');
      return storedStatus;
    }

    _log.d('Verifying stored subscription');

    // Check if platform is correct for verification
    if (!isPlatformCorrectForPurchase(storedStatus)) {
      _log.d('Platform mismatch - cannot verify subscription on this platform');
      return null;
    }

    try {
      // Get current status from RevenueCat
      await _fetchCustomerInfo();
      final currentStatus = _convertCustomerInfoToStatus(_customerInfo);

      _log.d('RevenueCat status: ${currentStatus.entitlement}');
      _log.d('Stored status: ${storedStatus.entitlement}');

      // Return the verified status from RevenueCat
      return currentStatus;
    } catch (e) {
      _log.e('Error verifying subscription: $e');
      return null;
    }
  }

  // ============================================================================
  // Private Helper Methods
  // ============================================================================

  Future<void> _fetchCustomerInfo() async {
    _log.d('Fetching customer info');
    _customerInfo = await Purchases.getCustomerInfo();
  }

  Future<void> _purchaseInfoListener(CustomerInfo purchaserInfo) async {
    _log.i('Received a purchase info event - fetching fresh customer info');

    try {
      // Don't trust the CustomerInfo passed to the listener as it might be stale
      // or incorrect. Always fetch fresh data from RevenueCat servers to ensure
      // we have the most accurate subscription information.
      await _fetchCustomerInfo();
      final status = _convertCustomerInfoToStatus(_customerInfo);

      // Check if platform is correct before broadcasting status updates
      // This prevents overwriting subscription data when on wrong platform
      // Each platform data is stored separately in Revenue cat using different api keys
      // So it's important otherwise we might overwrite subscription data when launching app on different platform and this
      // listener is triggered due to user having expired sub on other platform
      if (!isPlatformCorrectForPurchase(status)) {
        _log.d(
          'Platform mismatch detected in purchase listener - ignoring status update',
        );
        return;
      }

      _statusController.add(status);
    } catch (e) {
      _log.e('Error fetching fresh customer info in listener: $e');
      // Don't broadcast potentially incorrect data on error
    }
  }

  // ============================================================================
  // Data Conversion & Processing
  // ============================================================================

  /// Sorts entitlements by expiration date in descending order (latest first)
  void _sortEntitlementsByExpirationDate(List<EntitlementInfo> entitlements) {
    entitlements.sort((a, b) {
      final dateA = DateTime.parse(a.expirationDate!);
      final dateB = DateTime.parse(b.expirationDate!);
      return dateB.compareTo(dateA);
    });
  }

  /// Extracts subscription fields from EntitlementInfo for status object
  Map<String, dynamic> _extractSubscriptionFields(
    EntitlementInfo entitlement,
    CustomerInfo customerInfo,
  ) {
    final expirationDate = DateTime.parse(entitlement.expirationDate!);
    final isExpired = expirationDate.millisecondsSinceEpoch <
        DateTime.now().millisecondsSinceEpoch;

    return {
      'entitlement': isExpired
          ? MeUserEntitlement.free
          : MeUserEntitlement.values.byName(entitlement.identifier),
      'subscriptionState': isExpired
          ? SubscriptionState.subscriptionExpired
          : SubscriptionState.subscribed,
      'subscriptionStartDate': DateTime.parse(entitlement.latestPurchaseDate),
      'subscriptionExpDate': expirationDate,
      'storeType':
          entitlement.store == Store.playStore ? 'play_store' : 'app_store',
      'productId': _getProductId(
        entitlement.productIdentifier,
        customerInfo.allPurchasedProductIdentifiers,
      ),
      'subscriptionType': _getProductSubscriptionPeriod(entitlement),
      'unsubscribedAt': entitlement.unsubscribeDetectedAt != null
          ? DateTime.parse(entitlement.unsubscribeDetectedAt!)
          : null,
    };
  }

  List<SubscriptionProduct> _convertPackagesToProducts(List<Package> packages) {
    return packages.map((package) {
      final entitlement = _parseEntitlementFromPackageId(package.identifier);
      final subscriptionType =
          _parseSubscriptionTypeFromPackageId(package.identifier);

      return SubscriptionProduct(
        id: package.identifier,
        entitlement: entitlement,
        subscriptionType: subscriptionType,
        price: package.storeProduct.price,
        currencyCode: package.storeProduct.currencyCode,
        priceString: package.storeProduct.priceString,
        title: package.storeProduct.title,
        description: package.storeProduct.description,
      );
    }).toList();
  }

  SubscriptionStatus _convertCustomerInfoToStatus(CustomerInfo customerInfo) {
    final sortedEntitlementList = customerInfo.entitlements.all.values.toList();
    if (sortedEntitlementList.isNotEmpty) {
      _sortEntitlementsByExpirationDate(sortedEntitlementList);
    }

    var sortedActiveEntitlementList =
        customerInfo.entitlements.active.values.toList();
    if (sortedActiveEntitlementList.isNotEmpty) {
      // Smart subscription prioritization:
      // 1. First try to find active subscriptions from current platform
      // 2. If none found, fall back to active subscriptions from other platforms
      // This allows users to restore cross-platform subscriptions while still
      // prioritizing platform-specific ones when available.
      final currentPlatformStore =
          MePlatform.isAndroid ? Store.playStore : Store.appStore;

      // Separate subscriptions by platform
      final currentPlatformSubs = sortedActiveEntitlementList
          .where((entitlement) => entitlement.store == currentPlatformStore)
          .toList();
      final otherPlatformSubs = sortedActiveEntitlementList
          .where((entitlement) => entitlement.store != currentPlatformStore)
          .toList();

      // Sort both lists by expiration date (descending - latest first)
      if (currentPlatformSubs.isNotEmpty) {
        _sortEntitlementsByExpirationDate(currentPlatformSubs);
        // Use current platform subscription
        sortedActiveEntitlementList = currentPlatformSubs;
      } else if (otherPlatformSubs.isNotEmpty) {
        // No current platform subscription, use other platform
        _sortEntitlementsByExpirationDate(otherPlatformSubs);
        sortedActiveEntitlementList = otherPlatformSubs;
        _log.d(
          'Using cross-platform subscription: ${otherPlatformSubs.first.identifier} from ${otherPlatformSubs.first.store}',
        );
      }
      // If both empty, sortedActiveEntitlementList remains empty
    }

    MeUserEntitlement entitlement = MeUserEntitlement.free;
    SubscriptionState subscriptionState = SubscriptionState.none;
    DateTime? subscriptionStartDate;
    DateTime? subscriptionExpDate;
    DateTime? unsubscribedAt;
    String? storeType;
    String? productId;
    SubscriptionType subscriptionType = SubscriptionType.custom;

    if (sortedActiveEntitlementList.isNotEmpty) {
      final premiumEntitlement = sortedActiveEntitlementList.first;
      final fields =
          _extractSubscriptionFields(premiumEntitlement, customerInfo);

      entitlement = fields['entitlement'] as MeUserEntitlement;
      subscriptionState = fields['subscriptionState'] as SubscriptionState;
      subscriptionStartDate = fields['subscriptionStartDate'] as DateTime?;
      subscriptionExpDate = fields['subscriptionExpDate'] as DateTime?;
      storeType = fields['storeType'] as String?;
      productId = fields['productId'] as String?;
      subscriptionType = fields['subscriptionType'] as SubscriptionType;
      unsubscribedAt = fields['unsubscribedAt'] as DateTime?;
    } else if (sortedEntitlementList.isNotEmpty) {
      final premiumEntitlement = sortedEntitlementList.first;
      final fields =
          _extractSubscriptionFields(premiumEntitlement, customerInfo);

      // For expired subscriptions, override entitlement and state
      entitlement = MeUserEntitlement.free;
      subscriptionState = SubscriptionState.subscriptionExpired;
      subscriptionStartDate = fields['subscriptionStartDate'] as DateTime?;
      subscriptionExpDate = fields['subscriptionExpDate'] as DateTime?;
      storeType = fields['storeType'] as String?;
      productId = fields['productId'] as String?;
      subscriptionType = fields['subscriptionType'] as SubscriptionType;
      unsubscribedAt = fields['unsubscribedAt'] as DateTime?;
    }

    return SubscriptionStatus(
      entitlement: entitlement,
      subscriptionState: subscriptionState,
      subscriptionType: subscriptionType,
      subscriptionStartDate: subscriptionStartDate,
      subscriptionExpDate: subscriptionExpDate,
      unsubscribedAt: unsubscribedAt,
      storeType: storeType,
      productId: productId,
      activeSubscriptions: customerInfo.activeSubscriptions,
      allPurchasedProductIds: customerInfo.allPurchasedProductIdentifiers,
    );
  }

  MeUserEntitlement _parseEntitlementFromPackageId(String packageId) {
    if (packageId.contains('basic')) {
      return MeUserEntitlement.basic;
    } else if (packageId.contains('pro')) {
      return MeUserEntitlement.pro;
    } else if (packageId.contains('plus')) {
      return MeUserEntitlement.plus;
    }
    return MeUserEntitlement.free;
  }

  SubscriptionType _parseSubscriptionTypeFromPackageId(String packageId) {
    if (packageId.contains('monthly')) {
      return SubscriptionType.monthly;
    } else if (packageId.contains('yearly')) {
      return SubscriptionType.yearly;
    }
    return SubscriptionType.custom;
  }

  Package? _findPackageById(Offerings offerings, String productId) {
    if (offerings.current == null) return null;

    try {
      return offerings.current!.availablePackages.firstWhere(
        (package) => package.identifier == productId,
      );
    } catch (e) {
      return null;
    }
  }

  String _getProductId(
    String productIdentifier,
    List<String> allPurchasedProductIdentifiers,
  ) {
    final productIdentifierList = allPurchasedProductIdentifiers
        .where((element) => element.contains('$productIdentifier:'))
        .toList();
    if (productIdentifierList.isNotEmpty) {
      return productIdentifierList.first.split(':').last;
    }
    return productIdentifier;
  }

  SubscriptionType _getProductSubscriptionPeriod(
    EntitlementInfo premiumEntitlement,
  ) {
    return (premiumEntitlement.productPlanIdentifier?.contains('yearly') ??
                false) ||
            (premiumEntitlement.productIdentifier.contains('yearly'))
        ? SubscriptionType.yearly
        : SubscriptionType.monthly;
  }

  SubscriptionType _getCurrentSubscriptionType() {
    if (_customerInfo.entitlements.active.isNotEmpty) {
      final activeEntitlement = _customerInfo.entitlements.active.values.first;
      return _getProductSubscriptionPeriod(activeEntitlement);
    }
    return SubscriptionType.custom;
  }

  /// Determine if restored status should be applied over current status
  /// Simple rule: Don't overwrite active subscription with free/expired restore result
  bool _shouldApplyRestoredStatus(
    SubscriptionStatus? currentStatus,
    SubscriptionStatus restoredStatus,
  ) {
    // If no current status, always apply restored status
    if (currentStatus == null) {
      _log.d('No current status, applying restored status');
      return true;
    }

    // If current subscription is free or expired, always apply restored status
    if (currentStatus.subscriptionState == SubscriptionState.none ||
        currentStatus.subscriptionState ==
            SubscriptionState.subscriptionExpired ||
        currentStatus.entitlement == MeUserEntitlement.free) {
      _log.d('Current subscription is free/expired, applying restored status');
      return true;
    }

    // If restored subscription is active, apply it (even if it's a downgrade)
    if (restoredStatus.subscriptionState == SubscriptionState.subscribed) {
      _log.d('Restored subscription is active, applying');
      return true;
    }

    // Don't overwrite active subscription with free/expired restore result
    _log.d(
      'Not overwriting active subscription with free/expired restore result',
    );
    return false;
  }

  // ============================================================================
  // Configuration & API Keys
  // ============================================================================

  static String _getRevenueCatApiKey() {
    final isAndroid = MePlatform.isAndroid;
    switch (AppConfig.instance.environmentType) {
      case EnvironmentType.dev:
        return isAndroid
            ? 'goog_vQOFLDbKxhUWHaRWavbZQkAuOfT'
            : 'appl_sirCbISXnNjMndrxEPOAZdCMxro';
      case EnvironmentType.qa:
        return isAndroid
            ? 'goog_bGpgSPrQdDTpBpteDGPWAnPLVIE'
            : 'appl_SMPfaaSSvlQpiipSkipseLRqBlr';
      case EnvironmentType.staging:
        return isAndroid
            ? 'goog_PAqyrQpHpELAODTkZKMYaUduxOh'
            : 'appl_DojLIOmLFPMJrwfxnGkmzHiUvKF';
      case EnvironmentType.prod:
        return isAndroid
            ? 'goog_ALBuDXZFOQkaSOSudwBXyDCUgDP'
            : 'appl_uoHysIvHeOlfcAXBlmRLFNXWARX';
      case EnvironmentType.hotfix:
        return isAndroid
            ? 'goog_ENeJPYWCgnqQXqPXItwEsICmIHO'
            : 'appl_eKtioEvtHHUwcRaGvPCRajliDDN';
    }
  }
}
