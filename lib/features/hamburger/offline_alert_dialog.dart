import 'package:flutter/material.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/widgets/show_me_platform_dialog.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';

/// Shows an alert dialog to inform user that they are offline.
Future<void> offlineAlertDialog({
  required BuildContext context,
  bool hideDescription = false,
  MeString? customDescription,
}) async {
  await showMePlatformDialog<bool>(
    context: context,
    insetPadding:
        ScreenSizeState.instance.getDialogInsetPadding(DialogLevel.level1),
    title: MeTranslations.instance.overlay_noInternetConnection_title,
    bottomSheetLevel:
        MePlatform.isMacOS ? SizeConstants.level4BottomSheet : null,
    titleFontStyle: MeFontStyle.A8,
    description: hideDescription
        ? null
        : customDescription ??
            MeTranslations.instance.overlay_noInternetConnection_content,
    primaryText: MeTranslations.instance.screen_common_ok,
    primaryOnTap: () {
      Navigator.pop(context, true);
    },
  );
}
