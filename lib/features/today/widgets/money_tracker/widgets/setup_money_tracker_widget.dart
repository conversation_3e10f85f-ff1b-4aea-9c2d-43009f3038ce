import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';

import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/add_ons_type.dart';
import 'package:mevolve/data/enums/common_sharing_model.dart';
import 'package:mevolve/data/enums/doc_widget_type.dart';
import 'package:mevolve/data/enums/document_type.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/enums/money_tracker_status.dart';
import 'package:mevolve/data/enums/share_type.dart';
import 'package:mevolve/data/models/attachment_info.dart';
import 'package:mevolve/data/models/in_app_notification.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/money_tracker/money_tracker_setups.dart';
import 'package:mevolve/data/models/user/user.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_cubit.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/data/models/list_screens_models/task_lists.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/app/widgets/styles/me_text_icon.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/hamburger/trash/drawer_trash_page.dart';
import 'package:mevolve/features/insight/enums/insight_feature.dart';
import 'package:mevolve/features/insight/view/insight_page.dart';
import 'package:mevolve/features/lists/functions/collaboration_notification.dart';
import 'package:mevolve/features/tasks_providers/money_tracker/money_trackers_cubit.dart';
import 'package:mevolve/features/today/widgets/common_widgets/add_feature_app_bar.dart';
import 'package:mevolve/features/today/widgets/money_tracker/cubit/money_tracker_cubit.dart';
import 'package:mevolve/features/today/widgets/money_tracker/sharing/money_tracker_collaborate_sheet.dart';
import 'package:mevolve/features/welcome_and_guide/welcome_screen.dart';
import 'package:mevolve/features/widgets/attach_widget_to_keyboard.dart';
import 'package:mevolve/features/widgets/custom_fab_location.dart';
import 'package:mevolve/features/widgets/feature_widget.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/me_doc_bottom_bar/me_doc_bottom_bar.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_tags_list_widget.dart';
import 'package:mevolve/features/widgets/me_text_input.dart';
import 'package:mevolve/features/widgets/me_title_bottom_bar.dart';
import 'package:mevolve/features/widgets/me_title_widget.dart';
import 'package:mevolve/features/widgets/show_me_discard_dialog.dart';
import 'package:mevolve/features/widgets/show_me_edit_dialog.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';
import 'package:mevolve/utilities/me_menu_anchor.dart';
import 'package:mevolve/utilities/nullable.dart';
import 'package:mevolve/utilities/sharing_utility/sharing_firebase_functions.dart';
import 'package:mevolve/utilities/tagged_surface.dart';
import 'package:mevolve/utilities/task_component_handler.dart';
import 'package:mevolve/utilities/utility_methods.dart';

class SetupMoneyTrackerWidget extends StatefulWidget {
  const SetupMoneyTrackerWidget._({
    this.moneyTrackerSetupId,
    required this.isFuture,
    this.cloneMoneyTrackerSetup,
    this.restored,
    required this.isInsightOpen,
  });

  static Widget open({
    String? moneyTrackerSetupId,
    bool isFuture = false,
    MoneyTrackerSetup? cloneMoneyTrackerSetup,
    bool? restored,
    bool isInsightOpen = false,
  }) =>
      WidgetTracker(
        trackAction: TrackAction.moneyTrackerSetup,
        child: MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: authenticatedGlobalContext!.read<MoneyTrackersCubit>(),
            ),
            BlocProvider.value(
              value: authenticatedGlobalContext!.read<AppBloc>(),
            ),
          ],
          child: SetupMoneyTrackerWidget._(
            moneyTrackerSetupId: moneyTrackerSetupId,
            isFuture: isFuture,
            cloneMoneyTrackerSetup: cloneMoneyTrackerSetup,
            restored: restored,
            isInsightOpen: isInsightOpen,
          ),
        ),
      );

  final String? moneyTrackerSetupId;
  final bool isFuture;
  final MoneyTrackerSetup? cloneMoneyTrackerSetup;
  final bool? restored;
  final bool isInsightOpen;

  @override
  State<SetupMoneyTrackerWidget> createState() =>
      _SetupMoneyTrackerWidgetState();
}

class _SetupMoneyTrackerWidgetState extends State<SetupMoneyTrackerWidget> {
  late bool isTrashItem;
  late bool isDisabledTask;
  DateTime _selectedStartDate = DateTime.now();
  DateTime? _selectedEndDate;
  String _selectedCurrency = 'USD';

  MoneyTrackerStatus? get _moneyTrackerStatus =>
      widget.moneyTrackerSetupId == null || _isClone
          ? null
          : MoneyTrackerStatus.active;
  bool _isChangeMade = false;
  bool _allowEditing = false;
  bool _isEditing = false;
  String _title = '';

  // bool _isCurrencyFocused = false;
  List<String> _tags = [];
  bool _isValidSetup = true;

  late final String _uid;

  late List<String> _notDeletedTags;
  late MoneyTrackerSetup _defaultMoneyTrackerSetup;
  late MoneyTrackerSetup _currentMoneyTrackerSetup;

  late bool _isClone;

  final int _titleMaxLength = 120;
  bool _isTitleFocused = false;
  bool isTitleSttListening = false;

  final TextEditingController _textEditingController = TextEditingController();
  final TextEditingController _currencyTextEditingController =
      TextEditingController();

  bool get preventEditing => _isEditing && !_allowEditing;

  GlobalKey repeatWidgetGlobalKey = GlobalKey();
  GlobalKey dateRangePickerGlobalKey = GlobalKey();

  final _keyboardVisibilityController = KeyboardVisibilityController();
  late StreamSubscription<bool> _keyboardSubscription;
  final bool isShared = false;
  TaskUserType viewerType = TaskUserType.owner;

  @override
  void initState() {
    _uid = context.read<AppBloc>().state.user!.uid;
    updateState();
    _keyboardListener();

    if (widget.moneyTrackerSetupId != null) {
      // Send read event to analytics.
      EventFormation().sendFeatureCrudActivityEvent(
        actionType: ActionType.read,
        trackAction: TrackAction.moneyTrackerSetup,
        sharingStatus: isShared ? ShareStatus.shared : ShareStatus.private,
        sharedCount:
            _currentMoneyTrackerSetup.members.memberHashedEmails.length,
        shareMethod: null,
        userRole: _currentMoneyTrackerSetup.members.getRoleForAnalytics,
      );
    }
    super.initState();
  }

  void updateState() {
    _isClone = widget.cloneMoneyTrackerSetup != null;
    if (widget.isFuture && widget.moneyTrackerSetupId == null) {
      _selectedStartDate = _selectedStartDate.add(const Duration(days: 1));
    }
    _selectedCurrency = context
            .read<AppBloc>()
            .state
            .viewSettings
            ?.moneyTrackerSettings
            .config
            ?.currency ??
        'USD';
    if (widget.moneyTrackerSetupId == null) {
      _isEditing = false;
      _defaultMoneyTrackerSetup = _getMoneyTrackerSetupForSave(_isEditing);
    }

    if (_isClone) {
      _isEditing = false;
      _defaultMoneyTrackerSetup = widget.cloneMoneyTrackerSetup!;
    }

    if (widget.moneyTrackerSetupId != null) {
      _defaultMoneyTrackerSetup = context
          .read<MoneyTrackersCubit>()
          .state
          .setupsIDToASetup[widget.moneyTrackerSetupId]!;
      _isEditing = !_isClone;
      _allowEditing = _isClone;
    }
    isTrashItem = _defaultMoneyTrackerSetup.deletedAt != null;
    isDisabledTask = isTrashItem;
    _title = _defaultMoneyTrackerSetup.title;
    _selectedCurrency = widget.moneyTrackerSetupId == null
        ? context
                .read<AppBloc>()
                .state
                .viewSettings
                ?.moneyTrackerSettings
                .config
                ?.currency ??
            'USD'
        : _defaultMoneyTrackerSetup.currency;
    _selectedStartDate = _defaultMoneyTrackerSetup.tmzSafeStartAt;
    _tags = _defaultMoneyTrackerSetup.tags.toList();
    _textEditingController.text = _title;
    _currencyTextEditingController.text = _selectedCurrency;

    _updateMoneyTrackerSetup();
    checkAndUpdateUserType();
  }

  void checkAndUpdateUserType() {
    if (_currentMoneyTrackerSetup.uid == _uid) {
      viewerType = TaskUserType.owner;
    } else {
      String? currentUserHashedEmail = getCurrentUserHashedEmail;
      if (currentUserHashedEmail != null) {
        if (_currentMoneyTrackerSetup.members.memberHashedEmails
            .contains(currentUserHashedEmail)) {
          MemberRole role = _currentMoneyTrackerSetup
              .members.membersConfig[getCurrentUserHashedEmail]!.role;
          if (role == MemberRole.editor) {
            viewerType = TaskUserType.editor;
          } else {
            viewerType = TaskUserType.viewer;
          }
        } else {
          viewerType = TaskUserType.viewer;
        }
      } else {
        viewerType = TaskUserType.viewer;
      }
    }
  }

  @override
  void dispose() {
    _keyboardSubscription.cancel();
    _textEditingController.dispose();
    _currencyTextEditingController.dispose();
    super.dispose();
  }

  void _keyboardListener() {
    _keyboardSubscription =
        _keyboardVisibilityController.onChange.listen((bool visible) async {
      if (!visible && mounted) {
        removeKeyboardFocus(context);
        _updateMoneyTrackerSetup();
      }
    });
  }

  // Function used to update the current habit setup state.
  void _updateMoneyTrackerSetup() {
    _currentMoneyTrackerSetup = _getMoneyTrackerSetupForSave(_isEditing);
    _isChangeMade = checkIfMoneyTrackerSetupIsDifferent();
    setState(() {});
  }

  bool checkIfMoneyTrackerSetupIsDifferent() {
    return _currentMoneyTrackerSetup.title != _defaultMoneyTrackerSetup.title ||
        _currentMoneyTrackerSetup.currency !=
            _defaultMoneyTrackerSetup.currency ||
        !listEquals(
          getTags(_currentMoneyTrackerSetup.tags),
          getTags(_defaultMoneyTrackerSetup.tags),
        );
  }

  void _validateSave() {
    // Reset setup valid status.
    setState(() {
      _isValidSetup = true;
    });
    MeString dialogTitle;
    MeString dialogContent;
    void mandatoryFieldDialog({
      required MeString dialogTitle,
      required MeString dialogContent,
    }) {
      if (!_isValidSetup) {
        showDialog(
          context: context,
          builder: (ctx) {
            return MeDialog(
              title: dialogTitle,
              description: dialogContent,
              primaryText: MeTranslations.instance.screen_common_ok,
              primaryOnTap: () {
                Navigator.of(ctx).pop();
              },
            );
          },
        );
      }
    }

    if (_title.trim().isEmpty || _selectedCurrency.isEmpty) {
      // title is mandatory to proceed
      dialogTitle =
          MeTranslations.instance.overlay_moneyTrackerSetupMandatoryAlert_title;
      dialogContent = MeTranslations
          .instance.overlay_moneyTrackerSetupMandatoryAlert_content;

      setState(() {
        _isValidSetup = false;
      });
      mandatoryFieldDialog(
        dialogTitle: dialogTitle,
        dialogContent: dialogContent,
      );
    }
  }

  MoneyTrackerSetup _getMoneyTrackerSetupForSave(bool isEditing) {
    if (isEditing) {
      return _defaultMoneyTrackerSetup.copyWith(
        title: _title,
        currency: _selectedCurrency,
        tags: _tags,
      );
    } else {
      final UserData userData = context.read<AppBloc>().state.userData!;
      return MoneyTrackerSetup(
        localUpdatedAt: DateTime.now(),
        title: _title,
        createdAt: DateTime.now(),
        uid: _uid,
        tags: _tags,
        currency: _selectedCurrency,
        ownerEmail: userData.userInfo.email,
        ownerName: userData.userInfo.name,
      );
    }
  }

  Future<void> _saveMoneyTrackerSetup(
    void Function()? callAfterSetupCloseButBeforeSave,
    bool showSnackBar,
    bool shouldPopAfterDone,
  ) async {
    _updateMoneyTrackerSetup();
    // If task is valid, proceed to save.
    if (_isValidSetup) {
      // MeString? snackbarText;
      if (shouldPopAfterDone)
      // Close task.
      {
        Navigator.pop(context, true);
      }
      // A function we can optionally call after the setup is closed but before
      // the setup is saved.
      callAfterSetupCloseButBeforeSave?.call();
      // We have added this delay so there is no lag in opening the insight
      // bottomSheet when changes are saved and at the sametime the sheet is being opened.
      await Future.delayed(const Duration(milliseconds: 500));

      if (authenticatedGlobalContext == null) {
        final Log log = MeLogger.getLogger(LogTags.moneyTracker);
        log.e(
          'homeNavKey.currentContext is null in _saveMoneyTrackerSetup in SetupMoneyTrackerWidget',
        );
      }

      // Save.
      authenticatedGlobalContext
          ?.read<MoneyTrackerCubit>()
          .addOrUpdateMoneySetup(
            moneyTrackerSetup: _currentMoneyTrackerSetup,
          );

      if (showSnackBar) {
        // It's only a edit if it's a old task which updated.
        if (widget.moneyTrackerSetupId != null && _isChangeMade) {
          // snackbarText =
          //     MeTranslations.instance.;
        }
        // It's only a create if it's a new task and changes were made or a clone
        // task.
        else if (_isChangeMade || _isClone) {
          // snackbarText =
          //     MeTranslations.instance.;
        }

        // if (snackbarText == null) return;
        // // Show snackbar.
        // showMeSnackbar(snackbarText, MeTranslations.instance.screen_common_show,
        //     () async {
        //   scaffoldMessengerKey.currentState?.removeCurrentSnackBar();

        //   await homeNavKey.currentState?.push<bool>(
        //     buildPageRoute(
        //       child: (_) => SetupMoneyTrackerWidget.open(
        //         moneyTrackerSetupId: _currentMoneyTrackerSetup.id,
        //         isFuture: widget.isFuture,
        //       ),
        //     ),
        //   );
        // });
      }
    }
  }

  void _validateAndSave({
    void Function()? callAfterSetupCloseButBeforeSave,
    bool showSnackBar = true,
    bool shouldPopAfterDone = true,
  }) async {
    _validateSave();
    await _saveMoneyTrackerSetup(
      callAfterSetupCloseButBeforeSave,
      showSnackBar,
      shouldPopAfterDone,
    );
    if (!mounted) return;
  }

  // Gives hashtags that are not deleted and removes duplicating tags.
  List<String> getTags(
    List<String> allTags, [
    bool removeDeleted = true,
  ]) {
    if (context.mounted) {
      Set<String> tags = {};
      for (var tag in allTags) {
        String? tagName = context
            .read<UserResourcesCubit>()
            .state
            .userResources!
            .getTagById(tag);
        if (removeDeleted) {
          if (tagName != null) {
            tags.add(tag);
          }
        } else {
          tags.add(tag);
        }
      }
      return tags.toList();
    }
    return allTags.toList();
  }

  void _updateUneditedValue({required MoneyTrackerSetup moneyTrackerSetup}) {
    if (_currentMoneyTrackerSetup.deletedAt != moneyTrackerSetup.deletedAt &&
        moneyTrackerSetup.deletedAt != null &&
        context.mounted) {
      // Used in case a delete happened from another device.
      TaskComponentHandler.showMeDeleteDialog(
        context: context,
        docType: DocumentType.moneyTrackerSetup,
        title: MeTranslations.instance.overlay_moneyTrackerSetupDelete_title,
        description:
            MeTranslations.instance.overlay_moneyTrackerSetupDelete_content,
        moneyTrackerSetup: _currentMoneyTrackerSetup,
        isNewTask: widget.moneyTrackerSetupId == null,
        isChangeMade: _isChangeMade,
        handleOnlyAfterDelete: true,
      );
    }

    if (_currentMoneyTrackerSetup.title == _defaultMoneyTrackerSetup.title) {
      _title = moneyTrackerSetup.title;
      _textEditingController.text = _title;
    }

    if (listEquals(
      getTags(_currentMoneyTrackerSetup.tags),
      getTags(_defaultMoneyTrackerSetup.tags),
    )) {
      _tags = moneyTrackerSetup.tags.toList();
    }

    if (isSameDateTime(
      _currentMoneyTrackerSetup.tmzSafeStartAt,
      _defaultMoneyTrackerSetup.tmzSafeStartAt,
    )) {
      _selectedStartDate = moneyTrackerSetup.tmzSafeStartAt;
    }

    if (_currentMoneyTrackerSetup.currency ==
        _defaultMoneyTrackerSetup.currency) {
      _selectedCurrency = moneyTrackerSetup.currency;
      _currencyTextEditingController.text = _selectedCurrency;
    }

    _currentMoneyTrackerSetup = _currentMoneyTrackerSetup.copyWith(
      deletedAt: Nullable(moneyTrackerSetup.deletedAt),
    );

    isTrashItem = moneyTrackerSetup.deletedAt != null;

    isDisabledTask = isTrashItem;

    _defaultMoneyTrackerSetup = moneyTrackerSetup;
    _updateMoneyTrackerSetup();
  }

  final String _textFieldsTapRegionGroupId = 'setup_moneyTracker_textfield';

  // Don't show discard dialog based on save status as save will be enabled on fresh setup also.
  bool get showDiscardDialog => _isClone || _isChangeMade;

  // Save is always enabled for new setup or if changes are made on existing setups.
  bool get isSaveEnabled => widget.moneyTrackerSetupId == null || _isChangeMade;

  var fabLocationForSTT = CustomStandardFabLocation.endFloat(
    floatingActionButtonXMargin: 20,
    floatingActionButtonYMargin: 72,
  );

  var fabLocationForNonSTT = CustomStandardFabLocation.endFloat(
    floatingActionButtonXMargin: 20,
    floatingActionButtonYMargin: 16,
  );

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final userData = context.select(
      (AppBloc bloc) => bloc.state.userData!,
    );
    _notDeletedTags = getTags(_tags);

    bool canUseSpeechToText = context.select(
      (AppBloc bloc) =>
          bloc.state.viewSettings?.appSettings.canUseSpeechToText ?? false,
    );
    return TaggedSurface(
      tag: _textFieldsTapRegionGroupId,
      onTapOutside: () {
        removeKeyboardFocus(context);
      },
      onTapInside: () {},
      child: SafeArea(
        child: PopScope(
          canPop: false,
          onPopInvokedWithResult: (didPop, result) async {
            if (MenuControllerManager.instance.isAnyMenuOpen) {
              MenuControllerManager.instance.closeAllMenus();
              return;
            }
            if (!didPop) {
              if (showDiscardDialog) {
                final DiscardDialogActionType val = await showMeDiscardDialogV2(
                  context,
                  title: widget.moneyTrackerSetupId == null
                      ? MeTranslations
                          .instance.overlay_moneyTrackerDiscard_title
                      : null,
                  allowActionType: {
                    DiscardDialogActionType.save,
                    DiscardDialogActionType.discard,
                  },
                  insetPadding: ScreenSizeState.instance
                      .getDialogInsetPadding(DialogLevel.level1),
                );
                if (context.mounted && val == DiscardDialogActionType.save) {
                  isSaveEnabled
                      ? _validateAndSave(
                          callAfterSetupCloseButBeforeSave: () {
                            context.meGo(manageAddOnsPage);
                          },
                          showSnackBar: false,
                        )
                      : Navigator.of(context).pop();
                }
                if (context.mounted && val == DiscardDialogActionType.discard) {
                  Navigator.of(context).pop();
                }
              } else {
                Navigator.of(context).pop();
              }
            }
          },
          child: Scaffold(
            backgroundColor: colorScheme.color5,
            resizeToAvoidBottomInset: true,
            appBar: AddFeatureAppBar(
              isUserOwner: viewerType == TaskUserType.owner,
              isEditing: _isEditing,
              title: isTrashItem
                  ? MeTranslations.instance.screen_common_moneyTracker
                  : _isEditing
                      ? MeTranslations
                          .instance.dropdown_journalActionKebabMenu_editSetup
                      : MeTranslations.instance.screen_common_setup,
              secondaryButtonWidget: isTrashItem ||
                      widget.moneyTrackerSetupId == null ||
                      _isClone
                  ? null
                  : KebabMenuAnchor(
                      moneyTrackerStatus: _moneyTrackerStatus,
                      isOwner: viewerType == TaskUserType.owner,
                      showInsightBtn: !(viewerType != TaskUserType.owner ||
                          widget.isInsightOpen ||
                          isDisabledTask ||
                          context.read<AppBloc>().isItemHidden(
                                _defaultMoneyTrackerSetup.id,
                                TaskType.moneyTracker,
                              ) ||
                          widget.moneyTrackerSetupId == null ||
                          _isClone),
                      showCollabBtn: !(isTrashItem ||
                          widget.moneyTrackerSetupId == null ||
                          _isClone ||
                          _defaultMoneyTrackerSetup.uid != _uid ||
                          isDisabledTask),
                      collaboratorsCount: _currentMoneyTrackerSetup
                          .members.memberHashedEmails.length,
                      kebabButtonOnTap: (controller) async {
                        if (controller.isOpen) {
                          controller.close();
                        } else {
                          controller.open();
                        }
                      },
                      onTapInfoSetup: () {
                        showMeScrollableModalBottomSheet(
                          bottomSheetLevel: 2,
                          builder: (_, __) {
                            return const FeatureInfoSheet(
                              type: WelcomeFeatureType.moneyTracker,
                            );
                          },
                        );
                      },
                      // onTapPauseSetup: (!_defaultMoneyTrackerSetup
                      //             .isPaused &&
                      //         isSaveEnabled)
                      //     ? null
                      //     : () {
                      //         bool isPaused =
                      //             context.read<AppBloc>().isItemHidden(_defaultMoneyTrackerSetup.id, TaskType.moneyTracker);
                      //         // Navigator.pop(context);
                      //         showDialog(
                      //           context: context,
                      //           builder: (childContext) {
                      //             return MeDialog(
                      //               insetPadding: ScreenSizeState.instance
                      //                   .getDialogInsetPadding(
                      //                 DialogLevel.level1,
                      //               ),
                      //               title: !isPaused
                      //                   ? MeTranslations.instance
                      //                       .
                      //                   : MeTranslations.instance
                      //                       .,
                      //               description: !isPaused
                      //                   ? MeTranslations.instance
                      //                       .
                      //                   : MeTranslations.instance
                      //                       .,
                      //               primaryOnTap: () {
                      //                 context
                      //                     .read<MoneyTrackerCubit>()
                      //                     .addOrUpdateMoneySetup(
                      //                       moneyTrackerSetup:
                      //                           _defaultMoneyTrackerSetup
                      //                               .copyWith(
                      //                         isPaused: false,
                      //                       ),
                      //                     );
                      //                 Navigator.pop(
                      //                   childContext,
                      //                 );
                      //               },
                      //               primaryText: !isPaused
                      //                   ? null
                      //                   : MeTranslations.instance
                      //                       .,
                      //               secondaryOnTap: () {
                      //                 Navigator.pop(
                      //                   childContext,
                      //                 );
                      //               },
                      //               secondaryText: MeTranslations.instance
                      //                   .screen_common_buttonCancel,
                      //               tertiaryOnTap: isPaused
                      //                   ? null
                      //                   : () {
                      //                       context
                      //                           .read<MoneyTrackerCubit>()
                      //                           .addOrUpdateMoneySetup(
                      //                             moneyTrackerSetup:
                      //                                 _defaultMoneyTrackerSetup
                      //                                     .copyWith(
                      //                               isPaused: true,
                      //                             ),
                      //                           );
                      //                       Navigator.pop(
                      //                         childContext,
                      //                       );
                      //                     },
                      //               tertiaryText: isPaused
                      //                   ? null
                      //                   : MeTranslations.instance
                      //                       .,
                      //             );
                      //           },
                      //         );
                      //       },
                      onTapDeleteSetup: () async {
                        bool hasCollaborators = _currentMoneyTrackerSetup
                            .members.memberHashedEmails.isNotEmpty;
                        if (hasCollaborators) {
                          final InternetConnectionState
                              internetConnectionState = context
                                  .read<AppBloc>()
                                  .state
                                  .internetConnectionState;
                          bool isOffline = context
                                  .read<AppBloc>()
                                  .state
                                  .internetConnectionState ==
                              InternetConnectionState.disconnected;
                          if (isOffline) {
                            UtilityMethods.showOfflineDialogOrPerformAction(
                              internetActiveFunction: () {},
                              internetConnectionState: internetConnectionState,
                              context: context,
                            );
                            return;
                          }
                        }
                        await TaskComponentHandler.showMeDeleteDialog(
                          context: context,
                          docType: DocumentType.moneyTrackerSetup,
                          title: MeTranslations
                              .instance.overlay_moneyTrackerSetupDelete_title,
                          description: MeTranslations
                              .instance.overlay_moneyTrackerSetupDelete_content,
                          moneyTrackerSetup: _currentMoneyTrackerSetup,
                          isNewTask: widget.moneyTrackerSetupId == null,
                          isChangeMade: _isChangeMade,
                          handleOnlyDelete: true,
                        );
                        // if (context.mounted) {
                        //   Navigator.pop(context);
                        // }
                      },
                      onTapInsights: () {
                        void openInsight() {
                          showMeScrollableModalBottomSheet(
                            bottomSheetLevel: SizeConstants.level2BottomSheet,
                            builder: (_, __) {
                              return InsightPage(
                                insightFeature: InsightFeature.moneyTracker,
                                id: _defaultMoneyTrackerSetup.id,
                                parentSheetLevel:
                                    SizeConstants.level2BottomSheet - 1,
                              );
                            },
                          );
                        }

                        // Show dialog here if unsaved progress on setup screen
                        // as we will close setup screen before opening insight screen.
                        if (_isChangeMade) {
                          showMeUnsavedChangesDialogV2(
                            context,
                            title: MeTranslations.instance
                                .overlay_saveOrDiscardConfirmation_title,
                            description: MeTranslations
                                .instance.screen_common_discardContent,
                            onCancel: () {},
                            onProceedAnyway: () {
                              if (context.mounted) {
                                updateState();
                                openInsight();
                              }
                            },
                            onSaveAndProceed: () {
                              if (context.mounted) {
                                _validateAndSave(
                                  callAfterSetupCloseButBeforeSave: openInsight,
                                  showSnackBar: false,
                                  shouldPopAfterDone: false,
                                );
                              }
                            },
                          );
                        } else {
                          if (context.mounted) {
                            openInsight();
                          }
                        }
                      },
                      onTapCollaborate: () async {
                        showMeScrollableModalBottomSheet(
                          bottomSheetLevel: 2,
                          builder: (_, __) {
                            return MoneyTrackerCollaborateSheet(
                              setupId: _defaultMoneyTrackerSetup.id,
                            );
                          },
                        );
                      },
                      onTapLeave: () async {
                        bool? canLeave = await showDialog(
                          context: context,
                          builder: (context) => MeDialog(
                            title: MeTranslations
                                .instance.overlay_leaveMoneyTracker_title,
                            description: MeTranslations
                                .instance.overlay_leaveMoneyTracker_content,
                            tertiaryOnTap: () => Navigator.pop(
                              context,
                              true,
                            ),
                            tertiaryText: MeTranslations
                                .instance.screen_common_sharingLeave,
                            secondaryOnTap: () => Navigator.pop(
                              context,
                              false,
                            ),
                            secondaryText: MeTranslations
                                .instance.screen_common_buttonCancel,
                          ),
                        );
                        if (canLeave != true) return;
                        if (!context.mounted) return;
                        String uid =
                            context.read<AppBloc>().state.userData!.uid;
                        UtilityMethods.showMeLoaderDialog(
                          context,
                        );
                        bool? res =
                            await SharingFirebaseFunctions().leaveOrBlockTask(
                          _defaultMoneyTrackerSetup.id,
                          uid,
                          FirebaseDocCollectionType.moneyTrackerSetups,
                          MemberStatus.left,
                        );
                        if (!context.mounted) return;
                        SendNotificationList().notifyOwnerUserLeft(
                          title: _defaultMoneyTrackerSetup.title,
                          taskType: InAppNotificationTaskType.moneyTracker,
                          taskId: _defaultMoneyTrackerSetup.id,
                          operationBy: context
                              .read<AppBloc>()
                              .state
                              .userData!
                              .userInfo
                              .name,
                          uid: _defaultMoneyTrackerSetup.uid,
                          ownerHashedEmail: hashData(
                            _defaultMoneyTrackerSetup.ownerEmail,
                          ),
                        );
                        if (res && context.mounted) {
                          Navigator.pop(context);
                        }
                        if (context.mounted) {
                          Navigator.pop(context);
                        }
                      },
                      onTapBlock: () async {
                        bool? canBlock = await showDialog(
                          context: context,
                          builder: (context) => MeDialog(
                            title: MeTranslations
                                .instance.overlay_blockMoneyTracker_title,
                            description: MeTranslations
                                .instance.overlay_blockMoneyTracker_content,
                            quaternaryOnTap: () => Navigator.pop(
                              context,
                              true,
                            ),
                            quaternaryText: MeTranslations
                                .instance.screen_common_sharingBlock,
                            secondaryOnTap: () => Navigator.pop(
                              context,
                              false,
                            ),
                            secondaryText: MeTranslations
                                .instance.screen_common_buttonCancel,
                          ),
                        );
                        if (canBlock != true) return;
                        if (!context.mounted) return;
                        String uid =
                            context.read<AppBloc>().state.userData!.uid;
                        UtilityMethods.showMeLoaderDialog(
                          context,
                        );
                        bool? res =
                            await SharingFirebaseFunctions().leaveOrBlockTask(
                          _defaultMoneyTrackerSetup.id,
                          uid,
                          FirebaseDocCollectionType.moneyTrackerSetups,
                          MemberStatus.blocked,
                        );
                        if (!context.mounted) return;
                        SendNotificationList().notifyOwnerUserBlocked(
                          title: _defaultMoneyTrackerSetup.title,
                          taskType: InAppNotificationTaskType.moneyTracker,
                          taskId: _defaultMoneyTrackerSetup.id,
                          operationBy: context
                              .read<AppBloc>()
                              .state
                              .userData!
                              .userInfo
                              .name,
                          uid: _defaultMoneyTrackerSetup.uid,
                          ownerHashedEmail: hashData(
                            _defaultMoneyTrackerSetup.ownerEmail,
                          ),
                        );
                        if (res && context.mounted) {
                          Navigator.pop(context);
                        }
                        if (context.mounted) {
                          Navigator.pop(context);
                        }
                      },
                    ),
              tertiaryButtonIconPath: isDisabledTask ||
                      widget.moneyTrackerSetupId == null ||
                      _currentMoneyTrackerSetup
                          .members.memberHashedEmails.isEmpty
                  ? null
                  : Assets.svg.people.path,
              tertiaryButtonFunction: isDisabledTask ||
                      widget.moneyTrackerSetupId == null ||
                      _currentMoneyTrackerSetup
                          .members.memberHashedEmails.isEmpty
                  ? null
                  : () async {
                      showMeScrollableModalBottomSheet(
                        bottomSheetLevel: 2,
                        builder: (_, __) {
                          return MoneyTrackerCollaborateSheet(
                            setupId: _defaultMoneyTrackerSetup.id,
                          );
                        },
                      );
                    },
              // customWidget: viewerType != TaskUserType.owner
              //     ? Container(
              //         padding: const EdgeInsets.only(
              //           left: 12,
              //           right: 12,
              //         ),
              //         child: OwnerInitialAvatar(
              //           isLarge: true,
              //           name: _defaultMoneyTrackerSetup.ownerName,
              //           ownerId: _defaultMoneyTrackerSetup.uid,
              //           email: _defaultMoneyTrackerSetup.ownerEmail,
              //         ),
              //       )
              //     : null,
              closeButtonFunction: showDiscardDialog
                  ? () {
                      Navigator.maybePop(context);
                    }
                  : null,
            ),
            bottomSheet:
                _isTitleFocused && !MePlatform.isHardwareInputDeviceAvailable
                    ? TaggedChild(
                        tag: _textFieldsTapRegionGroupId,
                        child: TitleBottomBar(
                          controller: _textEditingController,
                          maxLength: _titleMaxLength,
                          onTitleChanged: (data) {
                            _title = data;
                            _updateMoneyTrackerSetup();
                          },
                        ),
                      )
                    : null,
            bottomNavigationBar: isTrashItem
                ? RemoveRestoreBottomBar(
                    onRemove: () => onRemove(
                      context: context,
                      docType: DocumentType.moneyTrackerSetup,
                      moneyTrackerSetup: _defaultMoneyTrackerSetup,
                    ),
                    onRestore: () => onRestore(
                      context: context,
                      docType: DocumentType.moneyTrackerSetup,
                      moneyTrackerSetup: _defaultMoneyTrackerSetup,
                    ),
                  )
                : MeDocBottomBar(
                    taskTitle: _title,
                    featureType: AttachmentFeatureType.moneyTracker,
                    bottomSheetLevel: SizeConstants.level2BottomSheet,
                    docType: DocumentType.moneyTrackerSetup,
                    isDisabledTask: isDisabledTask,
                    widgetList: _defaultMoneyTrackerSetup.uid != _uid
                        ? []
                        : const [DocWidgetType.hashtag],
                    isSaveEnabled: true,
                    saveButtonTitle: isSaveEnabled
                        ? MeTranslations.instance.screen_common_save
                        : MeTranslations.instance.screen_common_close,
                    selectedStartDate: _selectedStartDate,
                    selectedEndDate: _selectedEndDate,
                    reminder: const [],
                    onTagsChanged: (List<String>? data) {
                      _tags = data ?? _tags;
                      _updateMoneyTrackerSetup();
                    },
                    tags: _notDeletedTags,
                    saveFunction: () {
                      isSaveEnabled
                          ? _validateAndSave(
                              callAfterSetupCloseButBeforeSave: () {
                                context.meGo(manageAddOnsPage);
                              },
                              showSnackBar: false,
                            )
                          : Navigator.of(context).pop();
                    },
                    titleStt: _isTitleFocused &&
                            MePlatform.isHardwareInputDeviceAvailable
                        ? TaggedChild(
                            tag: _textFieldsTapRegionGroupId,
                            child: SpeechToTextBtnForTitle(
                              controller: _textEditingController,
                              maxLength: _titleMaxLength,
                              onTitleChanged: (data) {
                                _title = data;
                                _updateMoneyTrackerSetup();
                              },
                              isListening: (bool value) {
                                setState(() {
                                  isTitleSttListening = value;
                                });
                              },
                            ),
                          )
                        : null,
                    isTitleSttListening: isTitleSttListening,
                  ),
            body: PreventEdit(
              prevent: isDisabledTask,
              child: Stack(
                children: [
                  if (widget.moneyTrackerSetupId != null)
                    BlocListener<MoneyTrackersCubit, MoneyTrackersState>(
                      listener: (context, state) {
                        MoneyTrackerSetup? moneyTrackerSetup =
                            state.setupsIDToASetup[widget.moneyTrackerSetupId];
                        if (moneyTrackerSetup != null) {
                          _updateUneditedValue(
                            moneyTrackerSetup: moneyTrackerSetup,
                          );
                        }
                      },
                      child: const SizedBox.shrink(),
                    ),
                  SingleChildScrollView(
                    child: Container(
                      color: Colors.transparent,
                      child: Column(
                        children: [
                          const SizedBox(height: 8),
                          Material(
                            color: colorScheme.color5,
                            child: InkWell(
                              key: const ValueKey('openMoneyTrackerInfo'),
                              splashFactory: InkSparkle.splashFactory,
                              onTap: () {
                                showMeScrollableModalBottomSheet(
                                  bottomSheetLevel: 2,
                                  builder: (_, __) {
                                    return const FeatureInfoSheet(
                                      type: WelcomeFeatureType.moneyTracker,
                                    );
                                  },
                                );
                              },
                              child: Padding(
                                padding: ScreenSizeState
                                    .instance.featuredWidgetPadding,
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Align(
                                      alignment: Alignment.topLeft,
                                      child: MeIcon(
                                        iconPath: Assets.svg.habitTypeIcon.path,
                                        iconColor: isDisabledTask
                                            ? colorScheme.color7
                                            : colorScheme.color35,
                                        iconContainerSize: const SizedBox(
                                          width: 18,
                                          height: 18,
                                        ),
                                        padding: EdgeInsets.only(
                                          right: ScreenSizeState.instance
                                                  .featuredWidgetPadding.left -
                                              4,
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: MeText(
                                        text: AddOnsType.moneyTracker
                                            .toMeString(),
                                        textOverflow: null,
                                        meFontStyle: isDisabledTask
                                            ? MeFontStyle.D7
                                            : MeFontStyle.D8,
                                      ),
                                    ),
                                    if (!isTrashItem &&
                                        (context.read<AppBloc>().isItemHidden(
                                                  _defaultMoneyTrackerSetup.id,
                                                  TaskType.moneyTracker,
                                                ) ||
                                            _moneyTrackerStatus != null))
                                      Container(
                                        decoration: BoxDecoration(
                                          border: Border.all(
                                            color: !context
                                                        .read<AppBloc>()
                                                        .isItemHidden(
                                                          _defaultMoneyTrackerSetup
                                                              .id,
                                                          TaskType.moneyTracker,
                                                        ) &&
                                                    (_moneyTrackerStatus ==
                                                        MoneyTrackerStatus
                                                            .active)
                                                ? colorScheme.color35
                                                : colorScheme.color7,
                                            width: 1,
                                          ),
                                          borderRadius: BorderRadius.circular(
                                            4,
                                          ),
                                        ),
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 2.0,
                                            horizontal: 6.0,
                                          ),
                                          child: MeText(
                                            text: _moneyTrackerStatus!
                                                .toMeString(),
                                            meFontStyle: !context
                                                        .read<AppBloc>()
                                                        .isItemHidden(
                                                          _defaultMoneyTrackerSetup
                                                              .id,
                                                          TaskType.moneyTracker,
                                                        ) &&
                                                    (_moneyTrackerStatus ==
                                                        MoneyTrackerStatus
                                                            .active)
                                                ? MeFontStyle.N35
                                                : MeFontStyle.N7,
                                          ),
                                        ),
                                      ),
                                    if (widget.moneyTrackerSetupId == null ||
                                        _isClone)
                                      MeIcon(
                                        iconPath: Assets.svg.infoIcon.path,
                                        iconColor: colorScheme.color2,
                                        iconSize: const SizedBox(
                                          width: 18,
                                          height: 18,
                                        ),
                                        iconContainerSize: const SizedBox(
                                          width: 24,
                                          height: 24,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ),
                          TaggedChild(
                            tag: _textFieldsTapRegionGroupId,
                            child: PreventEdit(
                              prevent: viewerType != TaskUserType.owner,
                              onTapDuringPrevent: () =>
                                  UtilityMethods.showAccessDeniedPopup(
                                context: context,
                              ),
                              child: MeTitleWidget(
                                key: const ValueKey('addMoneyTrackerTitle'),
                                isDisabledTask: isDisabledTask,
                                textEditingController: _textEditingController,
                                docType: DocumentType.moneyTrackerSetup,
                                title: MeString(_title),
                                hintText: MeTranslations.instance
                                    .bottomSheet_moneyTrackerAction_titlePlaceholder,
                                inputHintText: MeTranslations.instance
                                    .bottomSheet_moneyTrackerAction_titlePlaceholder,
                                isSaveEnabled: isSaveEnabled,
                                onTitleChanged: (data) {
                                  _title = data;
                                  _updateMoneyTrackerSetup();
                                },
                                onSaveChanged: (data) {},
                                showIcon: true,
                                iconColor: _isValidSetup || _title.isNotEmpty
                                    ? colorScheme.color35
                                    : colorScheme.color11,
                                onInPlaceTextFieldFocusChange: (bool value) {
                                  setState(() {
                                    _isTitleFocused = value;
                                  });
                                },
                                textInputMaxLength: _titleMaxLength,
                                isInPlaceTextField: true,
                                keyboardType: TextInputType.text,
                                titleStyle: MeFontStyle.C8,
                                hintTextStyle: MeFontStyle.C7,
                              ),
                            ),
                          ),
                          TaggedChild(
                            tag: _textFieldsTapRegionGroupId,
                            child: PreventEdit(
                              prevent: viewerType != TaskUserType.owner,
                              onTapDuringPrevent: () =>
                                  UtilityMethods.showAccessDeniedPopup(
                                context: context,
                              ),
                              child: FeatureWidget(
                                key: const ValueKey('setCurrency'),
                                iconColor: isDisabledTask
                                    ? colorScheme.color7
                                    : _isValidSetup ||
                                            _selectedCurrency.isNotEmpty
                                        ? colorScheme.color35
                                        : colorScheme.color11,
                                titleStyle: isDisabledTask
                                    ? MeFontStyle.D7
                                    : (_selectedCurrency.isNotEmpty
                                        ? MeFontStyle.D8
                                        : MeFontStyle.D7),
                                iconPath: Assets.svg.moneyTrackerIcon.path,
                                title: _selectedCurrency.isNotEmpty
                                    ? MeString.fromVariable(_selectedCurrency)
                                    : MeTranslations.instance
                                        .bottomSheet_moneyTrackerSettings_enterCurrencyPlaceholder,
                                //? MeString
                                titleWidget: true,
                                isInPlaceTextField: true,

                                inPlaceTextFieldWidget: MeInplaceTextInput(
                                  inputTextStyle:
                                      isDisabledTask ? MeFontStyle.D7 : null,
                                  maxLength: 5,
                                  autoFocus: false,
                                  initialValue: MeString(
                                    _defaultMoneyTrackerSetup.currency,
                                  ),
                                  textEditingController:
                                      _currencyTextEditingController,
                                  hintText: MeTranslations.instance
                                      .bottomSheet_moneyTrackerSettings_enterCurrencyPlaceholder,
                                  onChanged: (value) {
                                    _selectedCurrency = value;
                                    _updateMoneyTrackerSetup();
                                  },
                                  onFocusChange: (value) {
                                    _selectedCurrency =
                                        _selectedCurrency.trim();
                                    _updateMoneyTrackerSetup();
                                    // setState(() {
                                    //   _isCurrencyFocused = value;
                                    // });
                                  },
                                ),
                              ),
                            ),
                          ),
                          if (_notDeletedTags.isNotEmpty)
                            MeTagsListWidget(
                              bottomSheetLevel: SizeConstants.level2BottomSheet,
                              isDisabledTask: isDisabledTask,
                              tags: _notDeletedTags,
                              onTagsChanged: (tags) {
                                _tags = tags;
                                _updateMoneyTrackerSetup();
                              },
                              userData: userData,
                              docType: DocumentType.moneyTrackerSetup,
                              moneyTrackerSetup: _defaultMoneyTrackerSetup,
                            ),
                          if (isTrashItem)
                            TrashDeletedAtText(
                              deletedAt: _defaultMoneyTrackerSetup.deletedAt,
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            floatingActionButtonAnimator: NoScaling(),
            floatingActionButtonLocation:
                canUseSpeechToText ? fabLocationForSTT : fabLocationForNonSTT,
            floatingActionButton: _isTitleFocused
                ? TextInputCountOverlay(
                    rawText: _title,
                    maxLength: _titleMaxLength,
                  )
                : const SizedBox.shrink(),
          ),
        ),
      ),
    );
  }
}

class KebabMenuAnchor extends StatefulWidget {
  const KebabMenuAnchor({
    Key? key,
    this.moneyTrackerStatus,
    required this.isOwner,
    this.showInsightBtn = false,
    this.showCollabBtn = false,
    this.collaboratorsCount = 0,
    this.onTapInfoSetup,
    this.onTapPauseSetup,
    this.onTapDeleteSetup,
    this.onTapInsights,
    this.onTapCollaborate,
    this.onTapLeave,
    this.onTapBlock,
    required this.kebabButtonOnTap,
  }) : super(key: key);

  final MoneyTrackerStatus? moneyTrackerStatus;
  final bool isOwner;
  final bool showInsightBtn;
  final bool showCollabBtn;
  final int collaboratorsCount;
  final void Function()? onTapInfoSetup;
  final void Function()? onTapPauseSetup;
  final void Function()? onTapDeleteSetup;
  final void Function()? onTapInsights;
  final void Function()? onTapCollaborate;
  final void Function()? onTapLeave;
  final void Function()? onTapBlock;
  final void Function(CustomMenuController controller) kebabButtonOnTap;

  @override
  State<KebabMenuAnchor> createState() => _KebabMenuAnchorState();
}

class _KebabMenuAnchorState extends State<KebabMenuAnchor> {
  final CustomMenuController controller = CustomMenuController();
  final double menuItemHeight = 44.0;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    // Build menu items
    final menuItems = _buildMenuItems(colorScheme);
    final totalHeightOfMenu =
        menuItemHeight * menuItems.length + menuItems.length - 1;

    return CustomMenuAnchor(
      controller: controller,
      alignmentOffset: const Offset(-112, -10),
      style: _buildMenuStyle(colorScheme, totalHeightOfMenu),
      builder: (
        BuildContext context,
        CustomMenuController controller,
        Widget? child,
      ) {
        return MeIconButton(
          onPressed: () => widget.kebabButtonOnTap(controller),
          iconPath: Assets.svg.moreVertical.path,
          buttonColor: colorScheme.color9,
          iconColor: colorScheme.color12,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        );
      },
      menuChildren: [
        const SizedBox(width: 150),
        ...menuItems.asMap().entries.map((entry) {
          final isLast = entry.key == menuItems.length - 1;
          return _wrapWithDivider(entry.value, colorScheme, isLast);
        }),
      ],
    );
  }

  List<Widget> _buildMenuItems(MeColorScheme colorScheme) {
    final menuItems = <Widget>[];

    // Info menu item
    menuItems.add(
      _buildMenuItem(
        text: MeTranslations.instance.dropdown_moneyTrackerSetupKebabMenu_info,
        iconPath: Assets.svg.infoIcon.path,
        iconColor: colorScheme.color35,
        onTap: widget.onTapInfoSetup,
        padding: 14.5,
      ),
    );

    // Insights menu item
    if (widget.isOwner && widget.showInsightBtn) {
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.screen_common_dropdownInsight,
          iconPath: Assets.svg.insightBottomIcon.path,
          iconColor: colorScheme.color35,
          onTap: widget.onTapInsights,
          padding: 14.5,
        ),
      );
    }

    // Collaborate menu item
    if (widget.isOwner && widget.showCollabBtn) {
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.screen_common_collaborate,
          iconPath: Assets.svg.people.path,
          iconColor: colorScheme.color35,
          onTap: widget.onTapCollaborate,
          padding: 14.5,
          // Display collaborator count if available
          trailing: Container(
            width: 18,
            height: 18,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorScheme.color10,
              border: Border.all(
                color: colorScheme.color1,
                width: 0.5,
              ),
            ),
            child: Center(
              child: widget.collaboratorsCount != 0
                  ? MeText(
                      text: MeString.fromVariable(
                        widget.collaboratorsCount.toString(),
                      ),
                      meFontStyle: MeFontStyle.I35,
                    )
                  : MeIcon(
                      iconPath: Assets.svg.plusIcon.path,
                      iconColor: colorScheme.color35,
                      iconSize: const SizedBox(height: 8, width: 8),
                    ),
            ),
          ),
        ),
      );
    }

    if (!widget.isOwner) {
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.screen_common_leave,
          iconPath: Assets.svg.leaveIcon.path,
          iconColor: colorScheme.color14,
          onTap: widget.onTapLeave,
          padding: 14.5,
        ),
      );
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.screen_common_block,
          iconPath: Assets.svg.blockIcon.path,
          iconColor: colorScheme.color11,
          onTap: widget.onTapBlock,
          padding: 14.5,
        ),
      );
    }

    // Delete menu item
    if (widget.isOwner) {
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.screen_common_setupOrActionDelete,
          iconPath: Assets.svg.deleteIcon.path,
          iconColor: colorScheme.color11,
          onTap: widget.onTapDeleteSetup,
          padding: 14.5,
        ),
      );
    }

    return menuItems;
  }

  Widget _buildMenuItem({
    required MeString text,
    required String iconPath,
    required Color iconColor,
    Widget? trailing,
    required void Function()? onTap,
    required double padding,
  }) {
    return SizedBox(
      height: menuItemHeight,
      child: InkWell(
        splashFactory: InkSparkle.splashFactory,
        onTap: () {
          controller.close();
          onTap?.call();
        },
        child: Padding(
          padding: EdgeInsets.all(padding),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              MeTextIcon(
                text: text,
                meFontStyle: MeFontStyle.F8,
                iconPath: iconPath,
                iconColor: iconColor,
                iconContainerSize: const SizedBox(width: 18, height: 18),
                verticallyCenterTitleText: true,
              ),
              if (trailing != null) ...[
                trailing,
              ],
            ],
          ),
        ),
      ),
    );
  }

  MenuStyle _buildMenuStyle(MeColorScheme colorScheme, double totalHeight) {
    return MenuStyle(
      alignment: Alignment.bottomLeft,
      backgroundColor:
          WidgetStateProperty.resolveWith((_) => colorScheme.color5),
      padding: WidgetStateProperty.all(
        const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
      ),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: colorScheme.color6, width: 2.0),
        ),
      ),
      maximumSize: WidgetStateProperty.all(Size(200, totalHeight)),
    );
  }

  Widget _wrapWithDivider(Widget item, MeColorScheme colorScheme, bool isLast) {
    return Column(
      children: [
        item,
        if (!isLast)
          Divider(color: colorScheme.color6, height: 1, thickness: 1),
      ],
    );
  }
}
