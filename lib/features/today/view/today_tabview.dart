import 'dart:async';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/constants/animation_constants.dart';
import 'package:mevolve/constants/app_strings.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/today_view_type.dart';
import 'package:mevolve/data/models/list_screens_models/calendar_event_lists_new.dart';
import 'package:mevolve/data/models/list_screens_models/habit_lists_new.dart';
import 'package:mevolve/data/models/list_screens_models/journal_lists_new.dart';
import 'package:mevolve/data/models/list_screens_models/task_lists.dart';
import 'package:mevolve/data/models/list_screens_models/todo_lists.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/tasks_providers/calendar_integrations/calendar_events_entries_cubit.dart';
import 'package:mevolve/features/tasks_providers/calendar_integrations/calendar_integrations_cubit.dart';
import 'package:mevolve/features/tasks_providers/habits/habits_cubit.dart';
import 'package:mevolve/features/tasks_providers/habits/habits_entries_cubit.dart';
import 'package:mevolve/features/tasks_providers/journals/journals_cubit.dart';
import 'package:mevolve/features/tasks_providers/journals/journals_entries_cubit.dart';
import 'package:mevolve/features/tasks_providers/money_tracker/money_trackers_cubit.dart';
import 'package:mevolve/features/tasks_providers/todos/todos_cubit.dart';
import 'package:mevolve/features/tasks_providers/todos/todos_entries_cubit.dart';
import 'package:mevolve/features/today/cubit/screens_cubit/screens_status_cubit.dart';
import 'package:mevolve/features/today/cubit/today_tab_cubit/today_cubit.dart';
import 'package:mevolve/features/today/cubit/today_tab_cubit/today_entries_cubit.dart';
import 'package:mevolve/features/today/cubit/today_tab_cubit/today_entries_filter_cubit.dart';
import 'package:mevolve/features/today/view/calendar_view.dart';
import 'package:mevolve/features/today/view/today_categorical_view.dart';
import 'package:mevolve/features/today/view/today_chronological_view.dart';
import 'package:mevolve/features/widgets/animated_list.dart';
import 'package:mevolve/features/widgets/empty_state_widget.dart';
import 'package:mevolve/features/widgets/loaders.dart';
import 'package:mevolve/features/widgets/me_banner.dart';
import 'package:mevolve/features/widgets/me_text_input.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';

class TodayTabView extends StatefulWidget {
  const TodayTabView({
    Key? key,
  }) : super(key: key);

  @override
  State<TodayTabView> createState() => _TodayTabViewState();
}

class _TodayTabViewState extends State<TodayTabView>
    with AutomaticKeepAliveClientMixin {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    super.build(context);
    return BlocBuilder<AppBloc, AppState>(
      buildWhen: (previous, current) =>
          previous.viewSettings != current.viewSettings,
      builder: (context, state) {
        bool? showUserGoal = state.viewSettings?.featureSettings.showUserGoal;
        String? userGoal = state.viewSettings?.featureSettings.userGoal;
        TodayViewType? viewType = state.viewSettings?.featureSettings.viewType;
        bool? showCalendarView =
            state.viewSettings?.featureSettings.showCalendarView;
        return BlocBuilder<TodayCubit, TodayState>(
          builder: (context, state) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                if (showUserGoal == true)
                  GestureDetector(
                    key: const ValueKey('enterGoal'),
                    child: MeMaterialBanner(
                      text: userGoal != null
                          ? MeString(userGoal)
                          : MeTranslations.instance.tab_todayToday_yourGoal,
                      centerText: true,
                      fontStyle: MeFontStyle.H35,
                      tileColor: colorScheme.color5,
                      padding: const EdgeInsets.symmetric(
                        vertical: 10.0,
                        horizontal: 16.0,
                      ),
                    ),
                    onTap: () async {
                      int maxLength = 80;
                      MeString hintText = MeTranslations
                          .instance.bottomSheet_yourGoal_placeholder;

                      Future<bool?>? onFieldSubmitted(value) {
                        if (value.trim().isNotEmpty) {
                          context.read<TodaySettingsCubit>().setUserGoal(
                                value.trim(),
                              );
                        }
                        return null;
                      }

                      if (ScreenSizeState.instance.isBigScreen) {
                        await showMeTextInputDialog<Duration>(
                          context,
                          dialogTitle: MeTranslations
                              .instance.bottomSheet_yourGoal_placeholder,
                          maxLength: maxLength,
                          initialValue:
                              userGoal != null ? MeString(userGoal) : null,
                          hintText: hintText,
                          onFieldSubmitted: onFieldSubmitted,
                          useRootNavigator: true,
                        );
                      } else {
                        await showMeScrollableModalBottomSheet(
                          bottomSheetLevel: SizeConstants.level1BottomSheet,
                          shape: const RoundedRectangleBorder(),
                          backgroundColor: Colors.transparent,
                          builder: (context, setShowDiscardDialogFlagValue) {
                            return MeTextInput(
                              title: MeTranslations
                                  .instance.bottomSheet_yourGoal_titleTopBar,
                              keyboardType: TextInputType.text,
                              initialValue:
                                  userGoal != null ? MeString(userGoal) : null,
                              maxLength: maxLength,
                              hintText: hintText,
                              suffixIconPath: Assets.svg.icSend.path,
                              inputFormatters: [
                                FilteringTextInputFormatter.deny(RegExp(r'\n')),
                              ],
                              onFieldSubmitted: onFieldSubmitted,
                              setShowDiscardDialogFlagValue:
                                  setShowDiscardDialogFlagValue,
                              isV2DiscardDialog: true,
                              v2DiscardDialogLevel: DialogLevel.level1,
                              allowMultiLine: true,
                              removeFieldFocusOnKeyboardPop: true,
                            );
                          },
                        );
                      }
                    },
                  ),
                if (showUserGoal == true)
                  Divider(
                    height: 2,
                    thickness: 2,
                    color: colorScheme.color6,
                  ),
                Expanded(
                  child: Center(
                    child: Builder(
                      builder: (context) {
                        if (state is TodayLoading && state.isFirstFetch) {
                          return const CustomCircularLoader();
                        }

                        return TodayPageView(
                          child: TodayTabCommonView(
                            viewType: viewType,
                            showCalendarView: showCalendarView ?? false,
                          ),
                        );
                      },
                    ),
                  ),
                ),
              ],
            );
          },
        );
      },
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class TodayPageView extends StatefulWidget {
  const TodayPageView({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  State<TodayPageView> createState() => _TodayPageViewState();
}

class _TodayPageViewState extends State<TodayPageView> {
  DateTime get _initialDateLocalDate =>
      context.read<TodayEntriesFilterCubit>().state.initialDateLocalDate;
  late DateTime _minTasksDateLocalDate =
      context.read<TodayEntriesFilterCubit>().state.minTasksDateLocalDate;
  late DateTime _maxTasksDateLocalDate =
      context.read<TodayEntriesFilterCubit>().state.maxTasksDateLocalDate;
  late DateTime _currentTasksDateLocalDate = _initialDateLocalDate;
  late int initialPage = getInitialPage;
  late final PageController _controller =
      PageController(initialPage: initialPage);
  bool _isJumping = false;

  Future<void> _jumpToDate(
    DateTime date, [
    bool useAnimation = true,
  ]) async {
    final bool shouldJump = _initialDateLocalDate
            .add(Duration(days: _controller.page!.round() - initialPage)) !=
        date;
    if (shouldJump) {
      final int daysDifference = date.difference(_initialDateLocalDate).inDays;
      final int targetPage = initialPage + daysDifference;
      if (!useAnimation) {
        _controller.jumpToPage(targetPage);
      } else {
        await _controller.animateToPage(
          targetPage,
          duration: AnimationConstants.todayPageViewAnimationDuration,
          curve: Curves.easeInOut,
        );
      }
    }
  }

  late double currentPageValue = initialPage.toDouble();

  @override
  void initState() {
    super.initState();
    context.read<TodayEntriesFilterCubit>().resetFilter();
    _controller.addListener(() {
      setState(() {
        currentPageValue = _controller.page ?? currentPageValue;
      });
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    scrollToPx = null;
    super.dispose();
  }

  int get getInitialPage {
    return _initialDateLocalDate.difference(_minTasksDateLocalDate).inDays < 0
        ? 0
        : _initialDateLocalDate.difference(_minTasksDateLocalDate).inDays;
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<TodayEntriesFilterCubit, TodayEntriesFilterState>(
      listener: (BuildContext context, TodayEntriesFilterState state) {
        // if max min dates are updated, update the initial page
        if (state.minTasksDateLocalDate != _minTasksDateLocalDate ||
            state.maxTasksDateLocalDate != _maxTasksDateLocalDate) {
          setState(() {
            _minTasksDateLocalDate = state.minTasksDateLocalDate;
            _maxTasksDateLocalDate = state.maxTasksDateLocalDate;
            initialPage = getInitialPage;
          });
        }
        _isJumping = true;
        _jumpToDate(
          state.currentTasksDateLocalDate,
          state.currentTasksDateLocalDate != _currentTasksDateLocalDate,
        ).then((_) {
          _isJumping = false;
        });
        _currentTasksDateLocalDate = state.currentTasksDateLocalDate;
      },
      builder: (context, state) {
        return PageView.builder(
          itemCount:
              _maxTasksDateLocalDate.difference(_minTasksDateLocalDate).inDays +
                  1,
          controller: _controller,
          scrollDirection: Axis.horizontal,
          onPageChanged: (int page) {
            if (!_isJumping) {
              final currentDate =
                  _initialDateLocalDate.add(Duration(days: page - initialPage));
              context.read<TodayEntriesFilterCubit>().updateFilter(
                    currentDate,
                  );
            }
          },
          itemBuilder: (BuildContext context, int itemIndex) {
            // Calculate the current day based on the itemIndex
            int cubitDay = itemIndex - initialPage;

            DateTime cubitDateTime =
                _initialDateLocalDate.add(Duration(days: cubitDay));

            return MultiBlocProvider(
              providers: [
                BlocProvider(
                  create: (context) => TodayActionsCubit(
                    context.read<TodosCubit>(),
                    context.read<TodosEntriesCubit>(),
                    context.read<HabitsCubit>(),
                    context.read<HabitSetupsEntriesCubit>(),
                    context.read<JournalsCubit>(),
                    context.read<JournalSetupsEntriesCubit>(),
                    context.read<CalendarIntegrationsCubit>(),
                    context.read<CalendarEventsEntriesCubit>(),
                    context.read<AppBloc>(),
                    cubitDateTime,
                  )..initializeTodayActions(context),
                ),
              ],
              child: Builder(
                builder: (context) {
                  /// IMP as we have to update the TodayActionsCubit with the current date
                  /// because it may have changed since we passed it while its creation.
                  /// TodayActionsCubit is destroyed only when it's index is destroyed not when
                  /// date related things or other things change.
                  context.read<TodayActionsCubit>().updateCurrentTasksDate(
                        cubitDateTime,
                      );
                  return AnimationWrapper(
                    animationType: AnimationType.scale,
                    currentPageValue: currentPageValue,
                    itemIndex: itemIndex,
                    child: widget.child,
                  );
                },
              ),
            );
          },
        );
      },
    );
  }
}

class TodayTabCommonView extends StatefulWidget {
  const TodayTabCommonView({
    super.key,
    this.viewType,
    required this.showCalendarView,
  });

  final TodayViewType? viewType;
  final bool showCalendarView;

  @override
  State<TodayTabCommonView> createState() => _TodayTabCommonViewState();
}

class _TodayTabCommonViewState extends State<TodayTabCommonView> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();
    context.read<TodayActionsCubit>().attachScrollController(
          _scrollController,
          context,
        );
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    bool showTodoFeature = context.select(
          (AppBloc cubit) =>
              cubit.state.viewSettings?.featureSettings.showTodoFeature,
        ) ??
        false;

    bool showCalendarFeature = context.select(
          (ScreensStatusCubit cubit) => cubit.state.showTodayCalendars,
        ) ??
        false;

    bool showMoneyTrackerFeature = context.select(
          (ScreensStatusCubit cubit) => cubit.state.showTodayMoneyTrackers,
        ) ??
        false;

    return BlocBuilder<TodayActionsCubit, TodayActionsState>(
      builder: (context, state) {
        if (state is TodayActionsStateInitial) {
          return const CustomCircularLoader();
        }

        List<DateTasks> visibleTasks = state.dateToTodayTasksGrouping;
        List<DateTodos> todosForDates = state.dateToTodayTodosGrouping;
        List<DateHabits> habitsForDates = state.dateToTodayHabitsGrouping;
        List<DateJournals> journalsForDates = state.dateToTodayJournalsGrouping;
        List<DateCalendarEvents> calendarEventsForDates =
            state.dateToTodayCalendarEventsGrouping;

        bool isRefreshing = state is TodayActionsStateRefreshing;
        bool isToday = state.cubitDateTime != null
            ? state.cubitDateTime!.isDateSameForUser(DateTime.now())
            : false;

        List<DateTasks> filteredVisibleTasks = [];
        final todayMoneyTransactions = context.select(
          (MoneyTrackersCubit cubit) => cubit.state.actionsIDToActions.values
              .toList()
              .where(
                (item) =>
                    item.deletedAt == null &&
                    item.tmzSafeTransactionDate
                        .isDateSameForUser(state.cubitDateTime) &&
                    cubit.state.setupsIDToASetup[item.setupId] != null &&
                    cubit.state.setupsIDToASetup[item.setupId]!.deletedAt ==
                        null,
              )
              .toList(),
        );

        void updateEvents() {
          filteredVisibleTasks = filterTasks(
            visibleTasks,
            showTodoFeature,
            showCalendarFeature,
            showMoneyTrackerFeature,
          );
        }

        updateEvents();

        bool notShowingMoneyTrackerOnToday =
            todayMoneyTransactions.isEmpty || !showMoneyTrackerFeature;
        if (filteredVisibleTasks.isEmpty &&
            notShowingMoneyTrackerOnToday &&
            !widget.showCalendarView) {
          return Center(
            child: EmptyStateWidget(
              imagePath: AppStrings.todoTabEmptyIllustration(
                colorScheme: colorScheme,
              ),
              imageText:
                  MeTranslations.instance.tab_todayToday_emptyScreenContent,
            ),
          );
        }

        List<CalendarViewEvent> events = widget.showCalendarView
            ? getEventsFromTasksList(
                context,
                filteredVisibleTasks,
                colorScheme,
                state.cubitDateTime!,
              )
            : [];

        return MultiBlocListener(
          listeners: [
            BlocListener<TodosCubit, TodosState>(
              listener: (_, __) => refreshTodayOnTaskUpdatesEfficiently(),
            ),
            BlocListener<HabitsCubit, HabitsState>(
              listener: (_, __) => refreshTodayOnTaskUpdatesEfficiently(),
            ),
            BlocListener<JournalsCubit, JournalsState>(
              listener: (_, __) => refreshTodayOnTaskUpdatesEfficiently(),
            ),
            BlocListener<MoneyTrackersCubit, MoneyTrackersState>(
              listener: (_, __) => refreshTodayOnTaskUpdatesEfficiently(),
            ),
            BlocListener<CalendarIntegrationsCubit, CalendarIntegrationsState>(
              listener: (_, __) => refreshTodayOnTaskUpdatesEfficiently(),
            ),
          ],
          child: widget.showCalendarView
              ? CalendarView(
                  events: events,
                  isToday: isToday,
                )
              : widget.viewType == TodayViewType.category
                  ? TodayCategoricalView(
                      scrollController: _scrollController,
                      visibleTasks: filteredVisibleTasks,
                      todosForDates: todosForDates,
                      habitsForDates: habitsForDates,
                      journalsForDates: journalsForDates,
                      calendarEventsForDates: calendarEventsForDates,
                      isRefreshing: isRefreshing,
                      dateTime: state.cubitDateTime!,
                    )
                  : TodayChronologicalView(
                      scrollController: _scrollController,
                      visibleTasks: filteredVisibleTasks,
                      isRefreshing: isRefreshing,
                      dateTime: state.cubitDateTime!,
                    ),
        );
      },
    );
  }

  void refreshTodayOnTaskUpdatesEfficiently() {
    EasyDebounce.debounce(
      '_todayViewRefresh',
      const Duration(milliseconds: 300),
      () {
        if (mounted) {
          setState(() {});
        }
      },
    );
  }
}
