import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/constants/animation_constants.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/deeplink/deeplink_action.dart';
import 'package:mevolve/features/deeplink/deeplink_executor.dart';
import 'package:mevolve/features/today/cubit/today_tab_cubit/today_entries_filter_cubit.dart';
import 'package:mevolve/features/today/cubit/unscheduled_todo_cubit/unscheduled_todo_cubit.dart';
import 'package:mevolve/features/today/view/overdue_todo_tabview.dart';
import 'package:mevolve/features/today/view/today_tabview.dart';
import 'package:mevolve/features/widgets/custom_app_bar.dart';
import 'package:mevolve/features/widgets/feature_options_tab_bar.dart';
import 'package:mevolve/features/widgets/me_button.dart';
import 'package:mevolve/features/widgets/me_calendar_new/me_calendar.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_primary_button.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';

class TodayPage extends StatelessWidget {
  const TodayPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    bool showTodoFeature = context.select(
          (AppBloc cubit) =>
              cubit.state.viewSettings?.featureSettings.showTodoFeature,
        ) ??
        false;
    return WidgetTracker(
      trackAction: TrackAction.today,
      child: Column(
        children: [
          SizedBox(
            height: SizeConstants.tabBarHeight,
            width: MediaQuery.of(context).size.width,
            child: Container(
              color: ScreenSizeState.instance.isBigScreen
                  ? colorScheme.color5
                  : colorScheme.color9,
              child:
                  BlocBuilder<TodayEntriesFilterCubit, TodayEntriesFilterState>(
                builder: (context, state) {
                  bool isOnFutureScreen = state.currentTasksDateLocalDate
                      .isDateAfterForUser(DateTime.now());
                  bool isOnPastScreen = state.currentTasksDateLocalDate
                      .isDateBeforeForUser(DateTime.now());
                  return Stack(
                    children: [
                      Align(
                        alignment: Alignment.centerLeft,
                        child: Padding(
                          padding: EdgeInsets.only(
                            left:
                                ScreenSizeState.instance.isBigScreen ? 12 : 16,
                          ),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              if (ScreenSizeState.instance.isBigScreen) ...[
                                MeIconButton(
                                  key: const ValueKey('previousDay'),
                                  iconPath: Assets.svg.leftArrow.path,
                                  iconColor:
                                      ScreenSizeState.instance.isBigScreen
                                          ? colorScheme.color2
                                          : colorScheme.color12,
                                  iconSize: const SizedBox(height: 12),
                                  onPressed: () {
                                    context
                                        .read<TodayEntriesFilterCubit>()
                                        .previousDay();
                                  },
                                ),
                                const SizedBox(width: 8),
                              ],
                              TodayDateChangeGesture(
                                child: InkWell(
                                  onTap: () async {
                                    final meCalendarResult =
                                        await showMeCalendarDialog(
                                      context:
                                          authenticatedGlobalContext ?? context,
                                      insetPadding: ScreenSizeState.instance
                                          .getDialogInsetPadding(
                                        DialogLevel.level2,
                                      ),
                                      monthPickerInsetPadding: ScreenSizeState
                                          .instance
                                          .getDialogInsetPadding(
                                        DialogLevel.level3,
                                      ),
                                      yearPickerInsetPadding: ScreenSizeState
                                          .instance
                                          .getDialogInsetPadding(
                                        DialogLevel.level3,
                                      ),
                                      initialDate: context
                                          .read<TodayEntriesFilterCubit>()
                                          .state
                                          .currentTasksDateLocalDate,
                                      firstDate: context
                                          .read<TodayEntriesFilterCubit>()
                                          .state
                                          .minTasksDateLocalDate,
                                      lastDate: context
                                          .read<TodayEntriesFilterCubit>()
                                          .state
                                          .maxTasksDateLocalDate,
                                      selectedDate: context
                                          .read<TodayEntriesFilterCubit>()
                                          .state
                                          .currentTasksDateLocalDate,
                                    );
                                    if (meCalendarResult != null &&
                                        meCalendarResult.date != null &&
                                        context.mounted) {
                                      context
                                          .read<TodayEntriesFilterCubit>()
                                          .updateFilter(
                                            meCalendarResult.date!,
                                          );
                                    }
                                  },
                                  child: DayTitleSwitcher(
                                    childWidth: 130,
                                    center: true,
                                    child: MeText(
                                      text: state.currentTasksDateLocalDate
                                          .formattedDayDate(
                                        showSpecialDate: false,
                                      ),
                                      textAlign:
                                          ScreenSizeState.instance.isBigScreen
                                              ? TextAlign.center
                                              : TextAlign.left,
                                      meFontStyle:
                                          ScreenSizeState.instance.isBigScreen
                                              ? MeFontStyle.L2
                                              : MeFontStyle.L12,
                                    ),
                                  ),
                                ),
                              ),
                              if (ScreenSizeState.instance.isBigScreen) ...[
                                const SizedBox(width: 8),
                                MeIconButton(
                                  key: const ValueKey('nextDay'),
                                  iconPath: Assets.svg.rightArrow.path,
                                  iconColor:
                                      ScreenSizeState.instance.isBigScreen
                                          ? colorScheme.color2
                                          : colorScheme.color12,
                                  iconSize: const SizedBox(height: 12),
                                  onPressed: () {
                                    context
                                        .read<TodayEntriesFilterCubit>()
                                        .nextDay();
                                  },
                                ),
                              ],
                            ],
                          ),
                        ),
                      ),
                      Align(
                        alignment: Alignment.centerRight,
                        child: Padding(
                          padding: const EdgeInsets.only(right: 16.0),
                          child: isOnFutureScreen || isOnPastScreen
                              ? MePrimaryButton(
                                  buttonType: ButtonType.outlinedIcon,
                                  iconAtLeft: false,
                                  iconPath: isOnFutureScreen
                                      ? Assets.svg.todayLeftArrow.path
                                      : isOnPastScreen
                                          ? Assets.svg.todayRightArrow.path
                                          : null,
                                  onPressed: () {
                                    context
                                        .read<TodayEntriesFilterCubit>()
                                        .resetFilter();
                                  },
                                  title: MeTranslations
                                      .instance.screen_common_today,
                                  fontStyle:
                                      ScreenSizeState.instance.isBigScreen
                                          ? MeFontStyle.F2
                                          : MeFontStyle.F12,
                                  height: 24,
                                  color: ScreenSizeState.instance.isBigScreen
                                      ? colorScheme.color2
                                      : colorScheme.color12,
                                  outlineColor:
                                      ScreenSizeState.instance.isBigScreen
                                          ? colorScheme.color2
                                          : colorScheme.color12,
                                )
                              : Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    if (showTodoFeature)
                                      OverdueTabIndicator(
                                        isSelected: true,
                                        onTap: () => DeeplinkExecutor
                                            .executeDeeplinkAction(
                                          context,
                                          const DeeplinkAction(
                                            sheetName: todayOverdueTab,
                                          ),
                                        ),
                                      ),
                                    if (showTodoFeature)
                                      BlocBuilder<UnscheduledTodoCubit,
                                          UnscheduledTodoState>(
                                        builder: (context, state) {
                                          return Padding(
                                            padding: EdgeInsets.only(
                                              left: state.todos.isNotEmpty
                                                  ? 17.0
                                                  : 0,
                                            ),
                                            child: TabFilterBadge(
                                              show: context
                                                  .read<UnscheduledTodoCubit>()
                                                  .state
                                                  .isFilterApplied,
                                              offset: const Offset(6, 3),
                                              child: TabTitleWithCount(
                                                iconPath: Assets
                                                    .svg.unscheduledIcon.path,
                                                count: state.pendingTodoCount,
                                                showRegardlessOfCount:
                                                    state.todos.isNotEmpty,
                                                isSelected: true,
                                                onTap: () => DeeplinkExecutor
                                                    .executeDeeplinkAction(
                                                  context,
                                                  const DeeplinkAction(
                                                    sheetName:
                                                        todayUnscheduledTab,
                                                  ),
                                                ),
                                              ),
                                            ),
                                          );
                                        },
                                      ),
                                  ],
                                ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                border: ScreenSizeState.instance.isBigScreen
                    ? Border(
                        top: BorderSide(
                          color: colorScheme.color6,
                          width: 2,
                        ),
                      )
                    : null,
              ),
              child: const TodayTabView(),
            ),
          ),
        ],
      ),
    );
  }
}

class TabTitleWithCount extends StatelessWidget {
  const TabTitleWithCount({
    Key? key,
    required this.iconPath,
    required this.count,
    this.showRegardlessOfCount = false,
    required this.isSelected,
    this.onTap,
    this.allowVisibilityTillLoading = false,
  }) : super(key: key);
  final String iconPath;
  final int count;
  final bool showRegardlessOfCount;
  final bool isSelected;
  final void Function()? onTap;

  /// Set this true to allow count less than 0 which is used for loading indication.
  final bool allowVisibilityTillLoading;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return (allowVisibilityTillLoading
            ? !showRegardlessOfCount && count == 0
            : !showRegardlessOfCount && count <= 0)
        ? const SizedBox.shrink()
        : InkWell(
            onTap: onTap,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                MeIcon(
                  iconPath: iconPath,
                  iconColor: isSelected
                      ? ScreenSizeState.instance.isBigScreen
                          ? colorScheme.color2
                          : colorScheme.color12
                      : colorScheme.color27,
                  iconSize: const SizedBox(
                    height: 18,
                    width: 18,
                  ),
                ),
                AnimatedCrossFade(
                  duration: const Duration(
                    milliseconds: AnimationConstants.listFadeDuration,
                  ),
                  alignment: Alignment.center,
                  crossFadeState: count > 0
                      ? CrossFadeState.showSecond
                      : CrossFadeState.showFirst,
                  firstChild: const SizedBox.shrink(),
                  secondChild: Container(
                    margin: const EdgeInsets.only(left: 8.0),
                    constraints: null,
                    decoration: null,
                    child: Center(
                      child: Padding(
                        padding: EdgeInsets.zero,
                        child: MeText(
                          text: MeString(
                            count > 99 ? '99+' : count.toString(),
                          ),
                          meFontStyle: isSelected
                              ? ScreenSizeState.instance.isBigScreen
                                  ? MeFontStyle.I2
                                  : MeFontStyle.I12
                              : MeFontStyle.I27,
                          textOverflow: TextOverflow.visible,
                          textAlign: TextAlign.center,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
  }
}

class TodayDateChangeGesture extends StatelessWidget {
  const TodayDateChangeGesture({
    super.key,
    required this.child,
  });

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onHorizontalDragEnd: (details) {
        if (details.velocity.pixelsPerSecond.dx > 0) {
          // Swipe Right - Previous Day
          context.read<TodayEntriesFilterCubit>().previousDay();
        } else if (details.velocity.pixelsPerSecond.dx < 0) {
          // Swipe Left - Next Day
          context.read<TodayEntriesFilterCubit>().nextDay();
        }
      },
      child: child,
    );
  }
}
