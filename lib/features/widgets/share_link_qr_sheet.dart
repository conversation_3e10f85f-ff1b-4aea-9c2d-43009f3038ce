import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gal/gal.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';

import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/common_sharing_model.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/enums/theme_type.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';

import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/lists/functions/list_to_image.dart';
import 'package:mevolve/features/lists/functions/list_to_text.dart';
import 'package:mevolve/features/lists/widgets/colaborate_sheet/shareable_image_widget.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_list_tile.dart';
import 'package:mevolve/features/widgets/me_qr_image_view.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/list_option_sheet.dart'
    show getSharePositionWithFallback;

class ShareQrAndLinkSheet extends StatelessWidget {
  const ShareQrAndLinkSheet({
    super.key,
    required this.args,
    required this.shareableLink,
    required this.sharerUserRole,
    required this.shareCount,
  });

  final ShareLinkQrData args;
  final String shareableLink;
  final MemberRole sharerUserRole;
  final int shareCount;

  @override
  Widget build(BuildContext context) {
    ScreenshotController screenshotController = ScreenshotController();

    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final ThemeType currentTheme = context.select(
      (TodaySettingsCubit cubit) => cubit.state.appThemeMode,
    );
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            SizeConstants.bottomSheetBorderRadius,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          DialogAndBottomSheetAppBar(
            title:
                MeTranslations.instance.bottomSheet_shareQrAndLink_titleTopBar,
          ),
          ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              Container(
                padding: const EdgeInsets.only(
                  top: 16,
                  left: 16,
                  right: 16,
                  bottom: 16,
                ),
                color: colorScheme.color5,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (args.collection !=
                        FirebaseDocCollectionType.publicUsers)
                      MeText(
                        text: MeString.fromVariable(args.title),
                        meFontStyle: MeFontStyle.C8,
                      ),
                    if (args.collection == FirebaseDocCollectionType.lists) ...[
                      const SizedBox(height: 8),
                      MeText(
                        text: args.collection == FirebaseDocCollectionType.lists
                            ? args.description == null ||
                                    args.description!.isEmpty
                                ? MeTranslations
                                    .instance.screen_common_description
                                : MeString.fromVariable(args.description!)
                            : MeString.empty,
                        meFontStyle: args.description == null ||
                                args.description!.isEmpty
                            ? MeFontStyle.F7
                            : MeFontStyle.F8,
                      ),
                    ],
                    MeQrImageView(
                      size: 150,
                      link: shareableLink,
                      colorScheme: colorScheme,
                      currentTheme: currentTheme,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 1),
              MeListTile(
                title: MeText(
                  text: MeString.fromVariable(shareableLink),
                  meFontStyle: MeFontStyle.D8,
                ),
                trailing: MeIconButton(
                  iconPath: Assets.svg.copyIcon.path,
                  iconColor: colorScheme.color35,
                  iconSize: const SizedBox(height: 20, width: 20),
                  iconContainerSize: const SizedBox(height: 24, width: 24),
                  onPressed: () async {
                    await Clipboard.setData(
                      ClipboardData(
                        text: shareableLink,
                      ),
                    );
                    if (context.mounted) {
                      showCustomMessageOverlay(
                        context,
                        MeTranslations.instance.toast_linkCopied_content,
                      );
                    }

                    // Send analytics event for share by link
                    EventFormation().sendFeatureSharedEvent(
                      trackAction:
                          args.collection == FirebaseDocCollectionType.lists
                              ? TrackAction.myListsItem
                              : TrackAction.noteAction,
                      sharingStatus: ShareStatus.public,
                      sharedCount: shareCount,
                      shareMethod: ShareMethod.link,
                      userRole: UserRoleOfSharer.getUserRoleByMemberRole(
                        sharerUserRole,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(height: 1),
              MeListTile(
                title: MeText(
                  text: MeTranslations
                      .instance.bottomSheet_shareQrAndLink_download,
                  meFontStyle: MeFontStyle.D8,
                ),
                trailing: MeIconButton(
                  iconPath: Assets.svg.icDownload.path,
                  iconColor: colorScheme.color35,
                  iconSize: const SizedBox(height: 16.7, width: 15),
                  iconContainerSize: const SizedBox(height: 24, width: 24),
                  onPressed: () async {
                    // Send analytics event for share by link
                    EventFormation().sendFeatureSharedEvent(
                      trackAction:
                          args.collection == FirebaseDocCollectionType.lists
                              ? TrackAction.myListsItem
                              : TrackAction.noteAction,
                      sharingStatus: ShareStatus.public,
                      sharedCount: shareCount,
                      shareMethod: ShareMethod.qrcode,
                      userRole: UserRoleOfSharer.getUserRoleByMemberRole(
                        sharerUserRole,
                      ),
                    );

                    UtilityMethods.showMeLoaderDialog(context);
                    screenshotController
                        .captureFromWidget(
                      DownloadableQrImageRaw(
                        shareableLink: shareableLink,
                        args: args,
                        colorScheme: colorScheme,
                        currentTheme: currentTheme,
                        isNote:
                            args.collection == FirebaseDocCollectionType.notes,
                      ),
                    )
                        .then((value) async {
                      if (!context.mounted) return;
                      Navigator.of(context).pop();
                      String path = await imagePathFromUint8List(
                        args.id,
                        context,
                        value,
                      );
                      await Gal.putImage(
                        path,
                        album: 'Mevolve',
                      );
                      if (context.mounted) {
                        showCustomMessageOverlay(
                          context,
                          MeTranslations.instance.toast_qrDownload_content,
                          actionText:
                              MeTranslations.instance.screen_imagePreview_view,
                          actionFunction: () {
                            OpenFile.open(path);
                          },
                        );
                      }
                    });
                  },
                ),
              ),
              const SizedBox(height: 1),
              MeListTile(
                title: MeText(
                  text: MeTranslations.instance.screen_common_share,
                  meFontStyle: MeFontStyle.D8,
                ),
                trailing: Builder(
                  builder: (buttonContext) => MeIconButton(
                    iconPath: Assets.svg.icShare.path,
                    iconColor: colorScheme.color35,
                    iconSize: const SizedBox(height: 16.7, width: 15),
                    iconContainerSize: const SizedBox(height: 24, width: 24),
                    onPressed: () async {
                      // Send analytics event for share by link
                      EventFormation().sendFeatureSharedEvent(
                        trackAction:
                            args.collection == FirebaseDocCollectionType.lists
                                ? TrackAction.myListsItem
                                : TrackAction.noteAction,
                        sharingStatus: ShareStatus.public,
                        sharedCount: shareCount,
                        shareMethod: ShareMethod.qrcode,
                        userRole: UserRoleOfSharer.getUserRoleByMemberRole(
                          sharerUserRole,
                        ),
                      );

                      if (args.collection ==
                          FirebaseDocCollectionType.publicUsers) {
                        final shareRect =
                            getSharePositionWithFallback(buttonContext);
                        await Share.share(
                          listQrLinkText(args),
                          sharePositionOrigin: shareRect,
                        );
                        return;
                      }
                      UtilityMethods.showMeLoaderDialog(context);
                      screenshotController
                          .captureFromWidget(
                        QrImageWidget(
                          shareableLink: shareableLink,
                          colorScheme: colorScheme,
                          currentTheme: currentTheme,
                        ),
                      )
                          .then((value) async {
                        if (!context.mounted) return;
                        Navigator.of(context).pop();
                        String path = await imagePathFromUint8List(
                          args.id,
                          context,
                          value,
                        );
                        if (!buttonContext.mounted) return;
                        final shareRect =
                            getSharePositionWithFallback(buttonContext);
                        await Share.shareXFiles(
                          [XFile(path)],
                          text: listQrLinkText(args),
                          sharePositionOrigin: shareRect,
                        );
                      });
                    },
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class ShareLinkQrData {
  ShareLinkQrData({
    required this.link,
    required this.title,
    this.description,
    required this.id,
    required this.ownerName,
    required this.collection,
    this.secondaryName,
  });

  final String link;
  final String title;
  final String? description;
  final String id;
  final String ownerName;
  final String? secondaryName;
  final FirebaseDocCollectionType collection;
}
