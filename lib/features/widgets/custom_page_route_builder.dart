import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/hamburger/me_drawer/me_drawer.dart';
import 'package:mevolve/features/today/cubit/today_tab_cubit/today_entries_filter_cubit.dart';
import 'package:mevolve/utilities/tagged_surface.dart';

class CustomPageRouteBuilder extends StatelessWidget {
  const CustomPageRouteBuilder({
    super.key,
    required this.child,
    this.selectedPage,
  });

  final Widget child;
  final String? selectedPage;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final bool noDrawerPage = selectedPage == emailOtp ||
        selectedPage == passcodePage ||
        selectedPage == dataMigrationScreen ||
        selectedPage == encryptionPasswordPage ||
        selectedPage == updateInfoScreen;
    final bool showDrawer = ScreenSizeState.instance.isBigScreen &&
        selectedPage != null &&
        !noDrawerPage;
    final bool isTransparencyAllowed = !noDrawerPage;

    final bool usingTranslucentBackground =
        ScreenSizeState.instance.isBigScreen &&
            !showDrawer &&
            isTransparencyAllowed;

    final String tagOfScreen = 'screenTag${child.key}';
    return Builder(
      builder: (context) {
        // Don't use scaffold here as nested scaffold will cause issues with
        // snackbars positioning. When using scaffoldMessengerKey it will only
        // respect the parent scaffold context.
        return TaggedSurface(
          // This is used to pop on tapping the translucent area of pages.
          isEnabled: usingTranslucentBackground,
          tag: tagOfScreen,
          onTapOutside: () {
            // Use mePopModal to respect WillPopScope/PopScope conditions in child
            // widgets as this transparent should act as modal.
            context.mePopModal();
          },
          onTapInside: () {},
          child: Container(
            // Scaffold background color.
            color: usingTranslucentBackground
                ? colorScheme.color13.withValues(alpha: 0.7)
                : ScreenSizeState.instance.isBigScreen
                    ? colorScheme.color5
                    : colorScheme.color6,
            // Min width provided by flutter to the Container is max screen width.
            // So, to apply constraints to the child, we need to wrap it in Center.
            // Read more here: https://stackoverflow.com/a/59928192
            child: Stack(
              children: [
                Row(
                  children: [
                    if (showDrawer)
                      SizedBox(
                        width: ScreenSizeState.instance.collapsedDrawerWidth,
                      ),
                    Expanded(
                      child: Center(
                        child: Container(
                          constraints: BoxConstraints(
                            maxWidth: ScreenSizeState.instance.screenMaxWidth,
                          ),
                          color: colorScheme.color6,
                          child: TaggedChild(
                            isEnabled: usingTranslucentBackground,
                            dontStealFocus: false,
                            tag: tagOfScreen,
                            child: child,
                          ),
                        ),
                      ),
                    ),
                    if (showDrawer)
                      SizedBox(
                        width: ScreenSizeState.instance.collapsedDrawerWidth,
                      ),
                  ],
                ),
                if (showDrawer)
                  MeDrawer(
                    overlayDrawer: false,
                    selectedPage: selectedPage!,
                    onItemSelected: (String page) {
                      context.read<TodayEntriesFilterCubit>().resetFilter();
                      context.meGo('$homePage/$page');
                    },
                  ),
              ],
            ),
          ),
        );
      },
    );
  }
}
