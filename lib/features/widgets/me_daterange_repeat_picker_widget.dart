import 'package:flutter/material.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/document_type.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/widgets/feature_widget.dart';
import 'package:mevolve/features/widgets/me_calendar/model/me_preset_model.dart';
import 'package:mevolve/features/widgets/me_calendar/model/me_range_calendar_result.dart';
import 'package:mevolve/features/widgets/me_calendar_new/me_range_repeat_calendar.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/repeat/repeat_utility_core.dart';

typedef Date = void Function(MeRangeCalendarResult data);

class MeDateRangeRepeatPickerWidget extends StatefulWidget {
  const MeDateRangeRepeatPickerWidget({
    Key? key,
    required this.currentStartDate,
    required this.onDateChanged,
    required this.type,
    this.currentEndDate,
    this.isStartDateEditable = true,
    this.isDateEditable = true,
    required this.repeatData,
    required this.forceRepeat,
    this.isDisabledTask = false,
    this.showDelete = false,
    required this.isTimeSet,
  }) : super(key: key);

  final DateTime? currentStartDate;
  final DateTime? currentEndDate;
  final Date onDateChanged;
  final DocumentType type;
  final bool isStartDateEditable;
  final bool isDateEditable;
  final List<String>? repeatData;
  final bool forceRepeat;
  final bool shouldPopOnApply = false;
  final bool isDisabledTask;
  final bool showDelete;
  final bool isTimeSet;

  @override
  State<MeDateRangeRepeatPickerWidget> createState() =>
      _MeDateRangeRepeatPickerWidgetState();
}

class _MeDateRangeRepeatPickerWidgetState
    extends State<MeDateRangeRepeatPickerWidget> {
  late DateTime? _selectedStartDate;
  DateTime? _selectedEndDate;
  MeRangeCalendarResult? meCalendarResult;

  @override
  void initState() {
    _selectedStartDate = widget.currentStartDate ?? DateTime.now().onlyDate();
    _selectedEndDate = widget.currentEndDate;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _selectedStartDate = widget.currentStartDate ?? _selectedStartDate;
    _selectedEndDate = widget.currentEndDate;
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    Color? iconColor = widget.isDisabledTask ? colorScheme.color7 : null;

    List<String>? repeatData =
        widget.repeatData == null || widget.repeatData!.isEmpty
            ? null
            : widget.repeatData;
    if (repeatData != null && repeatData[0].contains('WKST=SU')) {
      repeatData[0] = repeatData[0].replaceAll('WKST=SU', 'WKST=MO');
    }
    RepeatUntilOrOccurrenceType repeatUntilOrOccurrenceType =
        RepeatUntilOrOccurrenceType.fromRRuleString(repeatData?[0]);

    int repeatOccurrence =
        RepeatUntilOrOccurrenceType.getOccurrenceFromRRuleString(
      repeatData?[0],
    );

    RepeatType repeatType = RepeatType.fromRRuleString(repeatData?[0]);

    bool onlyShowDatePickerText =
        repeatType == RepeatType.off || widget.currentStartDate == null;

    MeString configRepeatOccurrenceText = repeatUntilOrOccurrenceType ==
            RepeatUntilOrOccurrenceType.repeatOccurrence
        ? repeatOccurrence > 1
            ? MeTranslations.instance.screen_common_occurrenceCount(
                occurenceValue: repeatOccurrence.toString(),
              )
            : MeString(
                '$repeatOccurrence ${MeTranslations.instance.bottomSheet_repeatEndOptions_occurrence.text}',
              )
        : widget.currentEndDate != null && repeatData != null
            ? getNumberOfDaysRepeatsAreValid(
                      repeatData,
                      widget.currentStartDate!,
                      widget.currentEndDate,
                    ) >
                    1
                ? MeTranslations.instance.screen_common_daysCount(
                    daysCount: getNumberOfDaysRepeatsAreValid(
                      repeatData,
                      widget.currentStartDate!,
                      widget.currentEndDate,
                    ).toString(),
                  )
                : MeTranslations.instance
                    .overlay_dateRangePicker_previewSameDateToDateSelect
            : MeTranslations.instance
                .bottomSheet_actionCommon_previewRepeatDaysForTodayToNeverEnds;

    MeString? subtitle() {
      if (widget.currentStartDate == null) {
        return null;
      } else {
        if (repeatType == RepeatType.off) {
          return MeTranslations.instance.bottomSheet_actionCommon_noRepeat;
        } else {
          String repeatPlusOccurrence =
              getRepeatDescriptionFromRRule(repeatData?[0], false, true);

          if (!onlyShowDatePickerText) {
            repeatPlusOccurrence += ' • ${configRepeatOccurrenceText.text}';
          }
          return MeString(repeatPlusOccurrence);
        }
      }
    }

    MeString getFormattedDate() {
      if (onlyShowDatePickerText) {
        final currentStartDate = widget.currentStartDate;
        final isTodo = widget.type == DocumentType.todo;
        final isMoneyTracker =
            widget.type == DocumentType.moneyTrackerTransaction;
        final today = DateTime.now();
        final tomorrow = today.add(const Duration(days: 1));

        if (currentStartDate == null) {
          return MeTranslations.instance.overlay_datePicker_noDate;
        }

        if (currentStartDate.isDateSameForUser(today)) {
          if (isTodo) {
            return MeTranslations.instance.bottomSheet_todoAction_dateToday;
          }
          return MeTranslations.instance.screen_common_today;
        }

        if (currentStartDate.isDateSameForUser(tomorrow) && isTodo) {
          return MeTranslations.instance.bottomSheet_actionCommon_calendarDue(
            dateValue: MeTranslations.instance.overlay_datePicker_tomorrow.text,
          );
        }

        if (isMoneyTracker) {
          return currentStartDate.formattedDayDate();
        }

        return MeTranslations.instance.bottomSheet_actionCommon_calendarDue(
          dateValue: currentStartDate.isDateSameForUser(today)
              ? MeTranslations.instance.screen_common_today.toString()
              : currentStartDate.formattedDayDate().text,
        );
      }

      String startDateString =
          _selectedStartDate.isDateSameForUser(DateTime.now())
              ? MeTranslations.instance.screen_common_today.text
              : _selectedStartDate.isDateSameForUser(
                  DateTime.now().add(const Duration(days: 1)),
                )
                  ? MeTranslations.instance.overlay_datePicker_tomorrow.text
                  : _selectedStartDate!.formattedDate().text;
      if (repeatUntilOrOccurrenceType ==
          RepeatUntilOrOccurrenceType.repeatOccurrence) {
        return MeTranslations.instance
            .overlay_dateRangePicker_previewDateToNeverEndsSelect(
          startDate: startDateString,
        );
      }

      String endDateString = _selectedEndDate == null
          ? MeTranslations.instance.overlay_datePicker_neverEnds.text
          : _selectedEndDate!.formattedDate().text;
      return MeTranslations.instance.screen_common_dateRangeFormat(
        endDate: endDateString,
        startDate: startDateString,
      );
    }

    bool isDateTimeOverdue = widget.type != DocumentType.calendar &&
        repeatType == RepeatType.off &&
        _selectedStartDate != null &&
        (widget.isTimeSet
            ? _selectedStartDate!.isBefore(DateTime.now())
            : _selectedStartDate!.isDateBeforeForUser(DateTime.now()));

    MeFontStyle? titleStyle = widget.isDisabledTask
        ? MeFontStyle.D7
        : (isDateTimeOverdue ? MeFontStyle.D11 : MeFontStyle.D8);

    return FeatureWidget(
      iconColor: iconColor,
      titleStyle: titleStyle,
      iconContainerHeight: 19,
      iconContainerWidth: 18,
      onTap: !widget.isDateEditable
          ? null
          : () async {
              List<MePresetModel> startDatePresets = [
                MePresetModel(
                  title: MeTranslations.instance.screen_common_today,
                  intDateFromNow: 0,
                ),
              ];
              List<MePresetModel> endDatePresets = [
                MePresetModel(
                  title: MeTranslations.instance.overlay_datePicker_neverEnds,
                  intDateFromNow: null,
                ),
                MePresetModel(
                  title: MeTranslations.instance
                      .overlay_datePicker_endDatePreset(dateValue: '15'),
                  intDateFromNow: 14,
                ),
                MePresetModel(
                  title: MeTranslations.instance
                      .overlay_datePicker_endDatePreset(dateValue: '30'),
                  intDateFromNow: 29,
                ),
                MePresetModel(
                  title: MeTranslations.instance
                      .overlay_datePicker_endDatePreset(dateValue: '60'),
                  intDateFromNow: 59,
                ),
              ];

              void onRes() {
                if (meCalendarResult != null) {
                  final pickedDate = meCalendarResult!.startDate;

                  if (pickedDate != null) {
                    _selectedStartDate = DateTime(
                      pickedDate.year,
                      pickedDate.month,
                      pickedDate.day,
                      _selectedStartDate?.hour ?? 0,
                      _selectedStartDate?.minute ?? 0,
                      _selectedStartDate?.second ?? 0,
                      _selectedStartDate?.millisecond ?? 0,
                    );
                    _selectedEndDate = meCalendarResult!.endDate?.onlyDate();
                  } else {
                    _selectedStartDate = null;
                    _selectedEndDate = null;
                  }
                  widget.onDateChanged(meCalendarResult!);
                  setState(() {
                    _selectedStartDate;
                    _selectedEndDate;
                  });
                }
              }

              meCalendarResult = await showMeRangeRepeatCalendarDialog(
                context,
                startDate: _selectedStartDate,
                endDate: _selectedEndDate,
                firstDate: DateTime(2000),
                lastDate: DateTime(2100),
                startDatePresetList: startDatePresets,
                endDatePresetList: endDatePresets,
                calculateEndDatePresetFromStartDate: true,
                startDatePreset: meCalendarResult?.startDatePreset ??
                    (_selectedStartDate != null &&
                            _selectedStartDate!.isSameDate(DateTime.now())
                        ? MePresetModel(
                            title: MeTranslations.instance.screen_common_today,
                            intDateFromNow: 0,
                          )
                        : null),
                endDatePreset: meCalendarResult?.endDatePreset ??
                    (_selectedEndDate == null
                        ? MePresetModel(
                            title: MeTranslations
                                .instance.overlay_datePicker_neverEnds,
                            intDateFromNow: null,
                          )
                        : null),
                isStartDateEditable: widget.isStartDateEditable,
                shouldPopAndReturnResOnApply: widget.shouldPopOnApply,
                resultOnApply: (result) {
                  meCalendarResult = result;
                  onRes();
                },
                repeatData: repeatData,
                forceRepeat: widget.forceRepeat,
                showDelete: widget.showDelete,
                isConfigSet: widget.currentStartDate != null,
                bottomSheetLevel: SizeConstants.level2BottomSheet,
              );

              if (widget.shouldPopOnApply) {
                onRes();
              }
            },
      iconPath: Assets.svg.calendarIcon.path,
      subtitle: subtitle(),
      title: getFormattedDate(),
      trailingWidget: null,
    );
  }
}
