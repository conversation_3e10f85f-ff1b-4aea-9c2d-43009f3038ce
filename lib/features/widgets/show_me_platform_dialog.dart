import 'package:flutter/material.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';

/// Generic function to show platform-specific dialogs
/// Uses bottom sheet on macOS, dialog on other platforms
Future<T?> showMePlatformDialog<T>({
  required BuildContext context,
  MeString? title,
  MeFontStyle? titleFontStyle,
  MeString? description,
  MeString? primaryText,
  VoidCallback? primaryOnTap,
  MeString? secondaryText,
  VoidCallback? secondaryOnTap,
  MeString? tertiaryText,
  VoidCallback? tertiaryOnTap,
  MeString? quaternaryText,
  VoidCallback? quaternaryOnTap,
  Widget? customContentAboveTitle,
  Widget? customContentAfterDescription,
  bool primaryButtonAtEnd = false,
  bool centerTitleAndDescription = false,
  bool showBottomDivider = false,
  EdgeInsets? padding,
  EdgeInsets? customContentAfterDescriptionPadding,
  EdgeInsets? insetPadding,
  bool useRootNavigator = false,
  double? bottomSheetLevel,
  bool barrierDismissible = true,
}) {
  if (MePlatform.isMacOS) {
    return showMeScrollableModalBottomSheet<T>(
      bottomSheetLevel: bottomSheetLevel ?? SizeConstants.level3BottomSheet,
      useSafeArea: true,
      isRootSheet: useRootNavigator,
      builder: (BuildContext childContext, _) {
        return MeBottomSheetDialogContent(
          title: title,
          titleFontStyle: titleFontStyle ?? MeFontStyle.A8,
          description: description,
          primaryText: primaryText,
          primaryOnTap: primaryOnTap,
          secondaryText: secondaryText,
          secondaryOnTap: secondaryOnTap,
          tertiaryText: tertiaryText,
          tertiaryOnTap: tertiaryOnTap,
          quaternaryText: quaternaryText,
          quaternaryOnTap: quaternaryOnTap,
          customContentAboveTitle: customContentAboveTitle,
          customContentAfterDescription: customContentAfterDescription,
          primaryButtonAtEnd: primaryButtonAtEnd,
          centerTitleAndDescription: centerTitleAndDescription,
          showBottomDivider: showBottomDivider,
          padding: padding,
          customContentAfterDescriptionPadding:
              customContentAfterDescriptionPadding,
        );
      },
    );
  } else {
    return showDialog<T>(
      useRootNavigator: useRootNavigator,
      context: context,
      barrierDismissible: barrierDismissible,
      builder: (BuildContext childContext) {
        return MeDialog(
          title: title,
          titleFontStyle: titleFontStyle ?? MeFontStyle.A8,
          description: description,
          primaryText: primaryText,
          primaryOnTap: primaryOnTap,
          secondaryText: secondaryText,
          secondaryOnTap: secondaryOnTap,
          tertiaryText: tertiaryText,
          tertiaryOnTap: tertiaryOnTap,
          quaternaryText: quaternaryText,
          quaternaryOnTap: quaternaryOnTap,
          customContentAboveTitle: customContentAboveTitle,
          customContentAfterDescription: customContentAfterDescription,
          primaryButtonAtEnd: primaryButtonAtEnd,
          centerTitleAndDescription: centerTitleAndDescription,
          showBottomDivider: showBottomDivider,
          padding: padding,
          customContentAfterDescriptionPadding:
              customContentAfterDescriptionPadding,
          insetPadding: insetPadding,
        );
      },
    );
  }
}

/// Custom platform-specific dialog function that works exactly like showDialog
/// but automatically chooses the appropriate platform implementation.
///
/// **Platform Behavior:**
/// - **macOS**: Uses `showMeScrollableModalBottomSheet` for consistent modal design
/// - **Other platforms**: Uses standard `showDialog`
///
/// **Usage Example:**
/// ```dart
/// showMePlatformCustomDialog<bool>(
///   context: context,
///   builder: (context) => AlertDialog(
///     title: Text('My Dialog'),
///     content: Text('Dialog content'),
///     actions: [
///       TextButton(
///         onPressed: () => Navigator.pop(context, false),
///         child: Text('Cancel'),
///       ),
///       TextButton(
///         onPressed: () => Navigator.pop(context, true),
///         child: Text('OK'),
///       ),
///     ],
///   ),
/// );
/// ```
///
/// **Parameters:**
/// - All standard `showDialog` parameters are supported
/// - Additional macOS-specific parameters: `bottomSheetLevel`, `enableDrag`
Future<T?> showMePlatformCustomDialog<T>({
  required BuildContext context,
  required Widget Function(BuildContext) builder,
  bool barrierDismissible = true,
  Color? barrierColor,
  String? barrierLabel,
  bool useSafeArea = true,
  bool useRootNavigator = true,
  RouteSettings? routeSettings,
  Offset? anchorPoint,
  TraversalEdgeBehavior? traversalEdgeBehavior,
  // macOS-specific parameters
  double? bottomSheetLevel,
  bool enableDrag = true,
}) {
  if (MePlatform.isMacOS) {
    // For macOS, use showMeScrollableModalBottomSheet
    return showMeScrollableModalBottomSheet<T>(
      bottomSheetLevel: bottomSheetLevel ?? SizeConstants.level3BottomSheet,
      useSafeArea: useSafeArea,
      isRootSheet: useRootNavigator,
      enableDrag: enableDrag,
      builder: (BuildContext childContext, _) {
        // Call the provided builder with the child context
        return builder(childContext);
      },
    );
  } else {
    // For other platforms, use standard showDialog
    return showDialog<T>(
      context: context,
      builder: builder,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor,
      barrierLabel: barrierLabel,
      useSafeArea: useSafeArea,
      useRootNavigator: useRootNavigator,
      routeSettings: routeSettings,
      anchorPoint: anchorPoint,
      traversalEdgeBehavior: traversalEdgeBehavior,
    );
  }
}
