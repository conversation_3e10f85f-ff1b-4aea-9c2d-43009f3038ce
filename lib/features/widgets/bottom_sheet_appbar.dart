import 'package:flutter/material.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';

class DialogAndBottomSheetAppBar extends StatelessWidget
    implements PreferredSizeWidget {
  const DialogAndBottomSheetAppBar({
    Key? key,
    this.title,
    this.suffixActions,
    this.onTap,
    this.showCloseButton = true,
    this.showRoundedTop = true,
    this.widgetTitle,
    this.backgroundColor,
  })  : assert(
          title != null || widgetTitle != null,
          'Either title or widgetTitle must be provided',
        ),
        super(key: key);

  final MeString? title;
  final List<Widget>? suffixActions;
  final VoidCallback? onTap;
  final bool showCloseButton;
  final bool showRoundedTop;
  final Widget? widgetTitle;
  final Color? backgroundColor;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return AppBar(
      key: const ValueKey('bottomSheetTitleBar'),
      backgroundColor: backgroundColor ?? colorScheme.color9,
      automaticallyImplyLeading: false,
      toolbarHeight: SizeConstants.appBarSecondaryHeight,
      shape: showRoundedTop
          ? const RoundedRectangleBorder(
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(SizeConstants.bottomSheetBorderRadius),
              ),
            )
          : null,
      title: title != null
          ? MeText(text: title!, meFontStyle: MeFontStyle.A12)
          : widgetTitle,
      actions: [
        if (suffixActions != null) ...[
          ...suffixActions!,
        ],
        (showCloseButton)
            ? MeIconButton(
                key: const ValueKey('close'),
                iconPath: Assets.svg.closeIcon.path,
                onPressed: onTap ?? () => Navigator.pop(context),
                buttonColor: colorScheme.color9,
                iconColor: colorScheme.color12,
                padding: const EdgeInsets.symmetric(horizontal: 12),
              )
            : const SizedBox.shrink(),
        const SizedBox(width: 4),
      ],
    );
  }

  @override
  Size get preferredSize =>
      const Size.fromHeight(SizeConstants.appBarSecondaryHeight);
}
