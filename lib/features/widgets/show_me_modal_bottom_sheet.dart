import 'package:flutter/material.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/view/app_view.dart';
import 'package:mevolve/features/common/modal_context_manager.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/widgets/me_custom_bottomsheet.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';

final Log _log = MeLogger.getLogger(LogTags.bottomSheet);

Future<T?> showMeScrollableModalBottomSheet<T>({
  ShapeBorder? shape,
  bool useSafeArea = false,
  double? bottomSheetLevel,
  BoxConstraints? constraints,
  Color? backgroundColor,
  BuildContext? widgetContext,
  // Set true only if you want the bottom sheet to be shown above root widgets
  // also. Like timers running bar.
  // Note: On set true the context will be from the root navigator which won't
  // have access other blocs under the widget tree.
  bool isRootSheet = false,
  bool enableDrag = true,
  required MeBottomSheetWidgetBuilder builder,
}) async {
  // Use dynamic level detection if bottomSheetLevel is not provided
  final effectiveBottomSheetLevel = bottomSheetLevel ?? ModalContextManager.getCurrentModalLevel();

  double bottomSheetMinTopMargin =
      SizeConstants.levelBottomSheetMinTopMargin(effectiveBottomSheetLevel);

  // Get context from the global navigator key.
  BuildContext? context = isRootSheet
      ? rootNavKey.currentContext
      : widgetContext ??
          authenticatedGlobalContext ??
          rootNavKey.currentContext;

  // Return and log error if context is null.
  if (context == null) {
    _log.e('homeNavKey.currentContext and rootNavKey.currentContext is null');
    return null;
  }

  // Return and log error if constraints is null and we can't determine level
  if (constraints == null && effectiveBottomSheetLevel == 0) {
    _log.e('Unable to determine modal level and constraints not provided.');
    return null;
  }

  // Remove and clear any open snackbars.
  scaffoldMessengerKey.currentState
    ?..removeCurrentSnackBar()
    ..clearSnackBars();

  double statusBarHeight =
      MediaQueryData.fromView(View.of(context)).padding.top;

  // If constraints are null, calculate them based on the effective level
  constraints ??= BoxConstraints(
    maxHeight: MediaQuery.of(context).size.height -
        statusBarHeight -
        bottomSheetMinTopMargin,
  );

  double screenMaxWidth = ScreenSizeState.instance.screenMaxWidth;

  constraints = constraints.copyWith(
    maxWidth: screenMaxWidth,
  );

  // Show the customised modal bottom sheet.
  final res = await showCustomMaterialModalBottomSheet(
    clipBehavior: Clip.antiAlias,
    context: context,
    backgroundColor: MePlatform.isMacOS ? null : Colors.transparent,
    isDismissible: true,
    enableDrag: enableDrag,
    useSafeArea: useSafeArea,
    constraints: MePlatform.isMacOS ? null : constraints,
    duration: const Duration(milliseconds: 200),
    shape: shape,
    bottomSheetLevel: effectiveBottomSheetLevel,
    builder: (
      BuildContext context,
      SetShowDiscardDialogFlagValue setShowDiscardDialogFlagValue,
    ) {
      if (MePlatform.isMacOS) {
        // On macOS, return the content directly as the container is handled by the modal builder
        return builder(context, setShowDiscardDialogFlagValue);
      } else {
        // On other platforms, use the existing container approach
        return Container(
          color: backgroundColor ?? Theme.of(context).scaffoldBackgroundColor,
          child: MePlatform.isIOS
              ? SafeArea(child: builder(context, setShowDiscardDialogFlagValue))
              : builder(context, setShowDiscardDialogFlagValue),
        );
      }
    },
  );

  return res;
}
