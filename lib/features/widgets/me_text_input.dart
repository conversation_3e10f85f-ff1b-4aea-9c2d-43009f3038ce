import 'dart:async';
import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:flutter_svg/svg.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/highlight_text_controller.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/widgets/attach_widget_to_keyboard.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/mac_os/me_adaptive_custom_dialog.dart';
import 'package:mevolve/features/widgets/me_custom_bottomsheet.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_text_input_validator_snackbar.dart';
import 'package:mevolve/features/widgets/me_title_bar.dart';
import 'package:mevolve/features/widgets/show_me_discard_dialog.dart';
import 'package:mevolve/features/widgets/show_me_platform_dialog.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/utility_methods.dart';

class MeTextInput extends StatefulWidget {
  const MeTextInput({
    Key? key,
    this.hintText,
    this.validator,
    this.title,
    this.onChanged,
    this.onFieldSubmitted,
    // Using url as default keyboard type because it disables the sticker and
    // gif button on the keyboard.
    this.keyboardType = TextInputType.url,
    this.suffixIconPath,
    this.textEditingController,
    this.maxLength,
    this.validationRegExp,
    this.textCapitalization = TextCapitalization.sentences,
    this.autoFocus = true,
    this.suffixWidget,
    this.prefixWidget,
    this.initialValue,
    this.showDiscardDialog = true,
    this.inputFormatters,
    this.addBottomPaddingOnKeyboardOpen = true,
    this.shouldPopOnDonePressedWhenEmpty = true,
    this.isKeyboardPop = true,
    this.allowEmptyTextSubmit = false,
    this.allowNewLineText = true,
    this.textInputAction,
    this.borderRadius,
    this.contentPadding = const EdgeInsets.symmetric(
      horizontal: 16,
      vertical: 16,
    ),
    this.isDense = false,
    this.isInPlaceInput = false,
    this.onFocusChange,
    this.onCloseClick,
    this.inputTextStyle,
    this.hintTextStyle,
    this.backgroundColor,
    this.cursorColor,
    this.focusNode,
    this.hideCloseButton = false,
    this.enableButtonIfTextNotEmpty = false,
    this.canHaveCapitalLetters = true,
    this.showMandatoryDialog = false,
    this.clearTextFieldOnSubmit = false,
    this.allowKBPop = true,
    this.useKeyboardAttachable = false,
    this.isControlPop = false,
    this.widgetBelowTextField,
    this.setShowDiscardDialogFlagValue,
    this.isV2DiscardDialog = false,
    this.v2DiscardDialogAllowPopOnSave = false,
    this.v2DiscardDialogLevel = DialogLevel.level2,
    this.autoCorrect = false,
    this.obscureText = false,
    this.obscuringCharacter = '*',
    this.textAlign = TextAlign.start,
    this.normalDiscardDialogLevel = DialogLevel.level2,
    this.titleButtonWidget,
    this.allowMultiLine = false,
    this.removeFieldFocusOnKeyboardPop = false,
    this.onTapOutside,
    this.showDeleteButton = false,
    this.onDelete,
    this.deleteDialogConfig,
  })  : assert(
          maxLength == null || maxLength > 0,
          'maxLength must be greater than 0 or null',
        ),
        super(key: key);

  final SetShowDiscardDialogFlagValue? setShowDiscardDialogFlagValue;

  final MeString? hintText;

  final MeString? initialValue;

  final MeString? title;

  final bool allowMultiLine;

  final FormFieldValidator<String>? validator;

  final ValueChanged<String>? onChanged;
  final bool isV2DiscardDialog;
  final bool v2DiscardDialogAllowPopOnSave;
  final DialogLevel v2DiscardDialogLevel;
  final DialogLevel normalDiscardDialogLevel;

  final Widget Function(
    BuildContext context,
    VoidCallback onSubmit,
  )? widgetBelowTextField;

  /// The returned value is used to determine if the text field should be cleared
  /// or not. If the value is false, the text field will not be cleared and if
  /// the value is true, the text field will be cleared and if the value is null
  /// then we won't do anything.
  final Future<bool?>? Function(String)? onFieldSubmitted;

  final TextInputType? keyboardType;

  final String? suffixIconPath;

  final Widget? suffixWidget;

  final Widget? prefixWidget;

  final TextEditingController? textEditingController;

  final int? maxLength;

  final String? validationRegExp;

  final TextCapitalization textCapitalization;

  final bool autoFocus;

  final bool showDiscardDialog;

  final List<TextInputFormatter>? inputFormatters;

  final bool addBottomPaddingOnKeyboardOpen;

  final bool shouldPopOnDonePressedWhenEmpty;

  /// Should we pop after onFieldSubmitted is called.
  final bool isKeyboardPop;

  final bool allowEmptyTextSubmit;

  final bool allowNewLineText;

  final TextInputAction? textInputAction;

  final BorderRadiusGeometry? borderRadius;
  final VoidCallback? onCloseClick;

  final EdgeInsetsGeometry contentPadding;

  final bool isDense;

  final bool isInPlaceInput;

  final void Function(bool)? onFocusChange;

  final MeFontStyle? inputTextStyle;

  final MeFontStyle? hintTextStyle;

  final Color? backgroundColor;

  final Color? cursorColor;

  final FocusNode? focusNode;

  final bool hideCloseButton;
  final bool enableButtonIfTextNotEmpty;
  final bool showMandatoryDialog;

  final bool canHaveCapitalLetters;

  final bool clearTextFieldOnSubmit;

  final bool allowKBPop;
  final bool useKeyboardAttachable;

  final bool isControlPop;
  final bool autoCorrect;
  final Widget? titleButtonWidget;
  final bool obscureText;
  final String obscuringCharacter;

  final TextAlign textAlign;

  final void Function(PointerDownEvent)? onTapOutside;

  final bool removeFieldFocusOnKeyboardPop;
  final bool showDeleteButton;
  final Function? onDelete;
  final DeleteDialogConfig? deleteDialogConfig;

  @override
  State<MeTextInput> createState() => _MeTextInputState();
}

class _MeTextInputState extends State<MeTextInput> {
  final _formKey = GlobalKey<FormState>();

  late final TextEditingController _textEditingController;
  StreamSubscription<bool>? _keyboardSubscription;
  final ValueNotifier<bool> isStringValidNotifier = ValueNotifier(false);
  final _keyboardVisibilityController = KeyboardVisibilityController();

  String? _errorText;
  bool _showError = false;
  bool _isWillPop = false;
  late final FocusNode _focusNode = widget.focusNode ?? FocusNode();
  MeString _currentText = MeString.empty;
  bool markTextRed = false;
  bool _allowKBPop = true;
  bool _isChangeMade = false;
  bool _isShowingDelete = false;

  @override
  void initState() {
    _textEditingController = widget.textEditingController ??
        TextEditingController(
          text: widget.initialValue == null
              ? MeString.empty.text
              : widget.initialValue!.text,
        );
    _currentText = widget.initialValue ?? MeString.empty;
    _allowKBPop = widget.allowKBPop;
    _keyboardListener();
    _textEditingController.addListener(_onTextChangeListener);
    super.initState();
  }

  void _onTextChangeListener() {
    // Check if text exceeds maxLength and trim if necessary
    if (widget.maxLength != null) {
      final text = _textEditingController.text;
      final int charactersTyped = UtilityMethods.getCharactersCount(text);

      if (charactersTyped > widget.maxLength!) {
        // Trim text to maxLength using characters to properly handle emojis
        final trimmedText = text.characters.take(widget.maxLength!).toString();

        // Update text without triggering this listener again
        _textEditingController.removeListener(_onTextChangeListener);
        _textEditingController.text = trimmedText;
        _textEditingController.selection = TextSelection(
          baseOffset: trimmedText.length,
          extentOffset: trimmedText.length,
        );
        _textEditingController.addListener(_onTextChangeListener);
      }
    }

    final finalText = _textEditingController.text.trim();
    isStringValidNotifier.value = widget.enableButtonIfTextNotEmpty
        ? finalText.isNotEmpty
        : (widget.allowEmptyTextSubmit || finalText.isNotEmpty) &&
            (widget.validationRegExp == null ||
                RegExp(widget.validationRegExp!).hasMatch(finalText)) &&
            (widget.initialValue != null
                ? widget.initialValue!.text != finalText
                : true);
    if ((finalText.length - _currentText.text.length) > 1) {
      if ((finalText.contains('.gif') || finalText.contains('http')) &&
          widget.keyboardType != TextInputType.url) {
        _textEditingController.text = _currentText.text;
        _textEditingController.selection = TextSelection(
          baseOffset: _textEditingController.text.length,
          extentOffset: _textEditingController.text.length,
        );
      } else {
        _currentText = MeString(_textEditingController.text);
      }
    } else {
      _currentText = MeString(_textEditingController.text);
    }
    if (_textEditingController.text.isEmpty && markTextRed) {
      setState(() {
        markTextRed = false;
      });
    }
    _isChangeMade = _textEditingController.text != widget.initialValue?.text;
    if (widget.setShowDiscardDialogFlagValue != null &&
        widget.showDiscardDialog) {
      widget.setShowDiscardDialogFlagValue!(_isChangeMade);
    }
  }

  void _keyboardListener() {
    // Used to avoid multiple pops if multiple text fields are there in the same screen.
    // Only acting for focused text field.
    bool oldHasFocus = _focusNode.hasFocus;
    _keyboardSubscription?.cancel();
    _keyboardSubscription =
        _keyboardVisibilityController.onChange.listen((bool visible) async {
      // Delay is added to let widget to be disposed if keyboard is popped due to
      // widget sudden closure like swipe down bottom sheet.
      // In future find a way to detect swipe down in progress to prevent this delay and pop from happening.
      await Future.delayed(const Duration(milliseconds: 350));
      // This _isWillPop is used to prevent the pop by keyboard visibility
      // change is already popping the keyboard.
      bool hasFocus = _focusNode.hasFocus;
      if (!visible) {
        // As keyboard is removed we can remove focus from focused text fields.
        if (oldHasFocus && widget.removeFieldFocusOnKeyboardPop) {
          _focusNode.unfocus();
          hasFocus = false;
        }

        // This is required here to show the discard dialog as soon as the
        // keyboard gets popped. So that the user don't have to press back again
        // to get the discard dialog.
        // This also closes the bottom-sheet as soon as the keyboard closes
        // preventing the need to press back again. These are the requirements provided.

        // If in place text input there is nothing after keyboard closes so don't
        // pop.
        if (!_isWillPop && mounted && oldHasFocus != hasFocus) {
          if (!widget.isInPlaceInput) {
            if (_allowKBPop) {
              Navigator.of(context).maybePop();
            }
            _allowKBPop = true;
          }
        }
      }
      oldHasFocus = hasFocus;
    });
  }

  Future<bool> onWillPop() async {
    _isWillPop = true;
    FocusScope.of(context).unfocus();
    if (widget.showMandatoryDialog) {
      showDialog<bool>(
        context: context,
        builder: (context) {
          return MeDialog(
            insetPadding: ScreenSizeState.instance
                .getDialogInsetPadding(DialogLevel.level2),
            title: MeTranslations
                .instance.overlay_loginEmailUsernameMandatoryAlert_title,
            description: MeTranslations
                .instance.overlay_loginEmailUsernameMandatoryAlert_content,
            titleFontStyle: MeFontStyle.C8,
            primaryText: MeTranslations.instance.screen_common_ok,
            primaryOnTap: () {
              Navigator.pop(context, true);
            },
          );
        },
      );
      return false;
    }
    if (_isShowingDelete) return false;
    if (!widget.showDiscardDialog) return true;
    if (_textEditingController.text.isEmpty) return true;
    if (_textEditingController.text != widget.initialValue?.text) {
      if (widget.isV2DiscardDialog) {
        DiscardDialogActionType type = await showMeDiscardDialogV2(
          context,
          insetPadding: ScreenSizeState.instance
              .getDialogInsetPadding(widget.v2DiscardDialogLevel),
          title:
              MeTranslations.instance.overlay_saveOrDiscardConfirmation_title,
          allowActionType: {
            DiscardDialogActionType.discard,
            DiscardDialogActionType.save,
          },
        );
        if (type == DiscardDialogActionType.save) {
          _onFieldSubmitted(
            _textEditingController.text,
            widget.isKeyboardPop,
          );
          if (!widget.v2DiscardDialogAllowPopOnSave) {
            return false;
          }
        }
        final pop = type == DiscardDialogActionType.discard ||
            type == DiscardDialogActionType.save;
        _isWillPop = pop;
        if (mounted && !pop) {
          FocusScope.of(context).requestFocus(_focusNode);
          _isWillPop = false;
        } else {
          _textEditingController.clear();
        }
        return pop;
      }
      final pop = await showMeDiscardDialog(
            context,
            insetPadding: ScreenSizeState.instance
                .getDialogInsetPadding(widget.normalDiscardDialogLevel),
          ) ??
          false;
      _isWillPop = pop;
      if (mounted && !pop) {
        FocusScope.of(context).requestFocus(_focusNode);
        _isWillPop = false;
      } else {
        _textEditingController.clear();
      }
      return pop;
    }
    return true;
  }

  @override
  void dispose() {
    _textEditingController.removeListener(_onTextChangeListener);
    if (widget.textEditingController == null) _textEditingController.dispose();
    _keyboardSubscription?.cancel();
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant MeTextInput oldWidget) {
    if (widget.initialValue != oldWidget.initialValue) {
      _textEditingController.value =
          widget.textEditingController?.value.copyWith(
                text: widget.textEditingController?.text,
              ) ??
              _textEditingController.value.copyWith(
                text: widget.initialValue == null
                    ? MeString.empty.text
                    : widget.initialValue!.text,
              );
    }

    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final inputTextStyle = widget.inputTextStyle != null
        ? widget.inputTextStyle!
        : markTextRed
            ? MeFontStyle.D11
            : _textEditingController.text.isEmpty
                ? MeFontStyle.D7
                : MeFontStyle.D8;
    if (_textEditingController is HighlightedTextFieldController) {
      (_textEditingController as HighlightedTextFieldController)
          .unHighlightedTextStyle = inputTextStyle;
    }
    return PopScope(
      canPop: widget.isInPlaceInput,
      onPopInvokedWithResult: (value, result) async {
        if (!widget.isInPlaceInput &&
            !value &&
            await onWillPop() &&
            context.mounted) {
          Navigator.pop(context);
        }
      },
      child: AttachWidgetToKeyBoard(
        attach: widget.addBottomPaddingOnKeyboardOpen,
        useKeyboardAttachable: widget.useKeyboardAttachable,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (widget.title != null)
                MeTitleBar(
                  title: widget.title!,
                  titleButton: widget.showDeleteButton &&
                          widget.deleteDialogConfig != null &&
                          widget.onDelete != null
                      ? MeIconButton(
                          padding: const EdgeInsets.all(12),
                          onPressed: () async {
                            setState(() {
                              _isShowingDelete = true;
                            });
                            bool? canDelete = await showDialog(
                              context: context,
                              builder: (context) {
                                return MeDialog(
                                  title: widget.deleteDialogConfig!.title,
                                  description:
                                      widget.deleteDialogConfig!.description,
                                  tertiaryText: MeTranslations
                                      .instance.screen_common_delete,
                                  secondaryText: MeTranslations
                                      .instance.screen_common_buttonCancel,
                                  tertiaryOnTap: () {
                                    Navigator.of(context).pop(true);
                                  },
                                  secondaryOnTap: () {
                                    Navigator.of(context).pop(false);
                                  },
                                );
                              },
                            );
                            setState(() {
                              _isShowingDelete = false;
                            });
                            if (canDelete == true && context.mounted) {
                              if (widget.onDelete != null) {
                                widget.onDelete!.call();
                              }
                            }
                          },
                          iconPath: Assets.svg.deleteIcon.path,
                          iconColor: colorScheme.color12,
                          iconSize: const SizedBox(height: 15),
                        )
                      : widget.titleButtonWidget,
                  onCloseClick: widget.hideCloseButton
                      ? null
                      : () {
                          if (widget.onCloseClick != null) {
                            widget.onCloseClick!();
                          } else {
                            // Don't pop instead give responsibility to the PopScope
                            // to pop or not.
                            Navigator.of(context).maybePop();
                          }
                        },
                ),
              if (widget.validator != null)
                MeTextInputValidatorSnackbar(
                  snackbarText: _errorText != null
                      ? MeString(_errorText!)
                      : MeString.empty, //? MeString
                  actionText: MeTranslations.instance.screen_common_close,
                  opacity: _showError ? 1 : 0,
                  onTap: () {},
                ),
              Container(
                decoration: BoxDecoration(
                  color: widget.backgroundColor ?? colorScheme.color5,
                  borderRadius: widget.borderRadius,
                ),
                child: Center(
                  child: FocusScope(
                    child: Focus(
                      onFocusChange: widget.onFocusChange,
                      child: ValueListenableBuilder(
                        valueListenable: isStringValidNotifier,
                        builder: (context, isStringValid, child) {
                          return TextFormField(
                            key: const ValueKey('textFieldBottomSheet'),
                            onTapOutside: widget.onTapOutside,
                            autocorrect: widget.autoCorrect,
                            obscureText: widget.obscureText,
                            obscuringCharacter: widget.obscuringCharacter,
                            textAlign: widget.textAlign,
                            onChanged: (String value) {
                              String newValue = widget.canHaveCapitalLetters
                                  ? value
                                  : value.toLowerCase();
                              _textEditingController.value =
                                  _textEditingController.value.copyWith(
                                text: newValue,
                              );
                              if (widget.onChanged != null) {
                                if (widget.allowNewLineText) {
                                  widget.onChanged!(newValue);
                                } else {
                                  widget.onChanged!(
                                    newValue.replaceAll('\n', ''),
                                  );
                                }
                              }
                            },
                            inputFormatters: widget.inputFormatters,
                            cursorColor:
                                widget.cursorColor ?? colorScheme.color1,
                            maxLines: widget.allowMultiLine ? null : 1,
                            style: widget.inputTextStyle != null
                                ? MeTextTheme.getMeFontStyle(
                                    fontStyle: widget.inputTextStyle!,
                                    context: context,
                                  )
                                : markTextRed
                                    ? MeTextTheme.getMeFontStyle(
                                        fontStyle: MeFontStyle.D11,
                                        height: 20,
                                        context: context,
                                      )
                                    : _textEditingController.text.isEmpty
                                        ? MeTextTheme.getMeFontStyle(
                                            fontStyle: MeFontStyle.D7,
                                            context: context,
                                          )
                                        : MeTextTheme.getMeFontStyle(
                                            fontStyle: MeFontStyle.D8,
                                            context: context,
                                          ),

                            onEditingComplete: () => _onFieldSubmitted(
                              _textEditingController.text,
                              widget.isKeyboardPop,
                            ),
                            textCapitalization: widget.textCapitalization,
                            enableSuggestions: false,
                            // enableInteractiveSelection: false,
                            keyboardType: widget.keyboardType,
                            controller: _textEditingController,
                            maxLength: widget.maxLength,
                            autofocus: widget.autoFocus,
                            focusNode: _focusNode,
                            textInputAction: widget.textInputAction,
                            // enableSuggestions: false,
                            // cursorHeight: 18,
                            decoration: InputDecoration(
                              border: InputBorder.none,
                              hintText: widget.hintText!.text,
                              counterText: '',
                              errorStyle: MeTextTheme.getMeFontStyle(
                                fontStyle: MeFontStyle.I14,
                                context: context,
                              ),
                              hintStyle: MeTextTheme.getMeFontStyle(
                                fontStyle:
                                    widget.hintTextStyle ?? MeFontStyle.D7,
                                context: context,
                              ),
                              prefixIcon: widget.prefixWidget,
                              prefixIconConstraints: const BoxConstraints(),
                              contentPadding: widget.contentPadding,
                              isDense: widget.isDense,
                              suffixIconConstraints: const BoxConstraints(
                                minHeight: 14,
                                minWidth: 14,
                              ),
                              suffixIcon: (widget.suffixIconPath != null)
                                  ? ValueListenableBuilder(
                                      valueListenable: _textEditingController,
                                      builder: (context, value, child) {
                                        final finalText = value.text;
                                        int charactersTyped =
                                            UtilityMethods.getCharactersCount(
                                          finalText,
                                        );
                                        // Ensure characterLeft is never negative
                                        final characterLeft = math.max(
                                          0,
                                          (widget.maxLength ?? 0) -
                                              charactersTyped,
                                        );
                                        bool isCounterVisible =
                                            UtilityMethods.showMaxLimitLeft(
                                          charactersTyped,
                                          (widget.maxLength ?? 0),
                                        ).$1;
                                        return Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            if (isCounterVisible)
                                              MeText(
                                                text: MeString(
                                                  characterLeft.toString(),
                                                ),
                                                meFontStyle: characterLeft <= 0
                                                    ? MeFontStyle.I14
                                                    : MeFontStyle.I7,
                                              ),
                                            if (widget.suffixIconPath != null)
                                              InkWell(
                                                key: const ValueKey(
                                                  'sendButton',
                                                ),
                                                onTap: widget
                                                        .enableButtonIfTextNotEmpty
                                                    ? () {
                                                        bool isValid = (widget
                                                                    .allowEmptyTextSubmit ||
                                                                value.text
                                                                    .trim()
                                                                    .isNotEmpty) &&
                                                            (widget.validationRegExp ==
                                                                    null ||
                                                                RegExp(
                                                                  widget
                                                                      .validationRegExp!,
                                                                ).hasMatch(
                                                                  value.text,
                                                                ));
                                                        if (isValid) {
                                                          _onFieldSubmitted(
                                                            _textEditingController
                                                                .text,
                                                            widget
                                                                .isKeyboardPop,
                                                          );
                                                        } else {
                                                          setState(() {
                                                            markTextRed = true;
                                                          });
                                                        }
                                                      }
                                                    : isStringValid
                                                        ? () =>
                                                            _onFieldSubmitted(
                                                              _textEditingController
                                                                  .text,
                                                              widget
                                                                  .isKeyboardPop,
                                                            )
                                                        : null,
                                                child: Padding(
                                                  key: const ValueKey(
                                                    'addItem',
                                                  ),
                                                  padding:
                                                      const EdgeInsets.only(
                                                    left: 16.0,
                                                    right: 19,
                                                  ),
                                                  child: SvgPicture.asset(
                                                    widget.suffixIconPath!,
                                                    width: 18,
                                                    colorFilter:
                                                        ColorFilter.mode(
                                                      isStringValid
                                                          ? colorScheme.color35
                                                          : colorScheme.color10,
                                                      BlendMode.srcIn,
                                                    ),
                                                  ),
                                                ),
                                              ),
                                          ],
                                        );
                                      },
                                    )
                                  : null,
                              suffixIconColor:
                                  isStringValid || widget.allowEmptyTextSubmit
                                      ? colorScheme.color35
                                      : colorScheme.color10,
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                ),
              ),
              if (widget.widgetBelowTextField != null)
                widget.widgetBelowTextField!(
                  context,
                  () {
                    _onFieldSubmitted(
                      _textEditingController.text,
                      widget.isKeyboardPop,
                    );
                  },
                ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _onFieldSubmitted(
    String value,
    bool isPop,
  ) async {
    _isWillPop = true;
    if (widget.isControlPop) {
      _allowKBPop = false;
    }
    String finalValue = value.trim();
    if (!widget.allowEmptyTextSubmit &&
        finalValue.isEmpty &&
        widget.shouldPopOnDonePressedWhenEmpty) {
      if (widget.isInPlaceInput) {
        _focusNode.unfocus();
      } else if (!widget.isControlPop) {
        Navigator.of(context).pop();
      }
      return;
    }
    if (widget.validator != null) {
      final res = widget.validator!(finalValue);
      if (res != null) {
        _errorText = res;
        _showError = true;
        setState(() {});
        Future.delayed(const Duration(seconds: 2), () {
          _showError = false;
          _isWillPop = false;
          setState(() {});
        });
        return;
      }
    }

    if (!widget.allowNewLineText) {
      finalValue = finalValue.replaceAll('\n', '');
    }

    bool? shouldClearTextField =
        (await widget.onFieldSubmitted?.call(finalValue)) ??
            widget.clearTextFieldOnSubmit;
    if (widget.isInPlaceInput) {
      _focusNode.unfocus();
    } else if (mounted && isPop && !widget.isControlPop) {
      Navigator.pop(context, finalValue);
    }

    if (shouldClearTextField) {
      _textEditingController.clear();
    }
    _isWillPop = false;
  }
}

class MeTextInputDialog extends StatelessWidget {
  const MeTextInputDialog({
    super.key,
    this.dialogTitle,
    this.hintText,
    this.validator,
    this.title,
    this.onChanged,
    this.onFieldSubmitted,
    // Using url as default keyboard type because it disables the sticker and
    // gif button on the keyboard.
    this.keyboardType = TextInputType.url,
    this.suffixIconPath,
    this.textEditingController,
    this.maxLength,
    this.validationRegExp,
    this.textCapitalization = TextCapitalization.sentences,
    this.autoFocus = true,
    this.suffixWidget,
    this.prefixWidget,
    this.initialValue,
    this.showDiscardDialog = true,
    this.inputFormatters,
    this.addBottomPaddingOnKeyboardOpen = true,
    this.shouldPopOnDonePressedWhenEmpty = true,
    this.isKeyboardPop = true,
    this.allowEmptyTextSubmit = false,
    this.allowNewLineText = true,
    this.textInputAction,
    this.focusNode,
    this.canHaveCapitalLetters = true,
    this.clearTextFieldOnSubmit = false,
    this.isControlPop = false,
    this.allowKBPop = true,
    this.isInPlaceInput = true,
    this.showMandatoryDialog = false,
  });

  final MeString? dialogTitle;
  final MeString? hintText;
  final MeString? initialValue;
  final MeString? title;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final Future<bool?>? Function(String)? onFieldSubmitted;
  final TextInputType? keyboardType;
  final String? suffixIconPath;
  final Widget? suffixWidget;
  final Widget? prefixWidget;
  final TextEditingController? textEditingController;
  final int? maxLength;
  final String? validationRegExp;
  final TextCapitalization textCapitalization;
  final bool autoFocus;
  final bool showDiscardDialog;
  final List<TextInputFormatter>? inputFormatters;
  final bool addBottomPaddingOnKeyboardOpen;
  final bool shouldPopOnDonePressedWhenEmpty;
  final bool isKeyboardPop;
  final bool allowEmptyTextSubmit;
  final bool allowNewLineText;
  final TextInputAction? textInputAction;
  final FocusNode? focusNode;
  final bool canHaveCapitalLetters;
  final bool clearTextFieldOnSubmit;
  final bool isControlPop;
  final bool allowKBPop;
  final bool isInPlaceInput;
  final bool showMandatoryDialog;

  @override
  Widget build(BuildContext context) {
    return MeAdaptiveCustomDialog(
      insetPadding:
          ScreenSizeState.instance.getDialogInsetPadding(DialogLevel.level1),
      child: Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(16)),
        ),
        clipBehavior: Clip.hardEdge,
        child: ListView(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          children: [
            DialogAndBottomSheetAppBar(
              title: dialogTitle ?? MeString.empty,
              onTap: () {
                Navigator.maybePop(context);
              },
            ),
            MeTextInput(
              hintText: hintText ?? MeString.empty,
              title: title,
              validator: validator,
              onChanged: onChanged,
              onFieldSubmitted: onFieldSubmitted,
              keyboardType: keyboardType,
              suffixIconPath: suffixIconPath,
              textEditingController: textEditingController,
              maxLength: maxLength,
              validationRegExp: validationRegExp,
              textCapitalization: textCapitalization,
              autoFocus: autoFocus,
              suffixWidget: suffixWidget,
              prefixWidget: prefixWidget,
              initialValue: initialValue,
              showDiscardDialog: showDiscardDialog,
              inputFormatters: inputFormatters,
              addBottomPaddingOnKeyboardOpen: addBottomPaddingOnKeyboardOpen,
              shouldPopOnDonePressedWhenEmpty: shouldPopOnDonePressedWhenEmpty,
              isKeyboardPop: isKeyboardPop,
              allowEmptyTextSubmit: allowEmptyTextSubmit,
              allowNewLineText: allowNewLineText,
              textInputAction: textInputAction,
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
              focusNode: focusNode,
              clearTextFieldOnSubmit: clearTextFieldOnSubmit,
              isControlPop: isControlPop,
              allowKBPop: allowKBPop,
              isInPlaceInput: isInPlaceInput,
              showMandatoryDialog: showMandatoryDialog,
            ),
          ],
        ),
      ),
    );
  }
}

Future<T?> showMeTextInputDialog<T>(
  BuildContext context, {
  MeString? dialogTitle,
  MeString? initialValue,
  MeString? hintText,
  TextInputType? keyboardType,
  Future<bool?>? Function(String)? onFieldSubmitted,
  String? Function(String?)? validator,
  int? maxLength,
  bool allowEmptyTextSubmit = false,
  bool allowNewLineText = true,
  String? validationRegExp,
  TextCapitalization textCapitalization = TextCapitalization.sentences,
  bool isKeyboardPop = true,
  bool showDiscardDialog = true,
  String? suffixIconPath,
  TextEditingController? textEditingController,
  TextInputAction? textInputAction,
  List<TextInputFormatter>? inputFormatters,
  bool canHaveCapitalLetters = true,
  bool clearTextFieldOnSubmit = false,
  bool isControlPop = false,
  bool allowKBPop = true,
  bool autoFocus = true,
  bool barrierDismissible = true,
  bool isInPlaceInput = false,
  bool showMandatoryDialog = false,
  bool shouldPopOnDonePressedWhenEmpty = true,
  bool useRootNavigator = false,
}) async {
  final res = await showMePlatformDialog(
    useRootNavigator: useRootNavigator,
    barrierDismissible: barrierDismissible,
    context: context,
    customContentAfterDescription: MeTextInputDialog(
      allowEmptyTextSubmit: allowEmptyTextSubmit,
      allowNewLineText: allowNewLineText,
      dialogTitle: dialogTitle,
      initialValue: initialValue,
      maxLength: maxLength,
      hintText: hintText ?? MeString.empty,
      suffixIconPath: suffixIconPath ?? Assets.svg.icSend.path,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters ??
          [
            FilteringTextInputFormatter.deny(RegExp(r'\n')),
          ],
      onFieldSubmitted: onFieldSubmitted,
      validationRegExp: validationRegExp,
      textCapitalization: textCapitalization,
      isKeyboardPop: isKeyboardPop,
      showDiscardDialog: showDiscardDialog,
      textEditingController: textEditingController,
      textInputAction: textInputAction,
      validator: validator,
      canHaveCapitalLetters: canHaveCapitalLetters,
      clearTextFieldOnSubmit: clearTextFieldOnSubmit,
      isControlPop: isControlPop,
      allowKBPop: allowKBPop,
      autoFocus: autoFocus,
      isInPlaceInput: isInPlaceInput,
      showMandatoryDialog: showMandatoryDialog,
      shouldPopOnDonePressedWhenEmpty: shouldPopOnDonePressedWhenEmpty,
    ),
  );

  return res;
}

class MeInplaceTextInput extends StatelessWidget {
  const MeInplaceTextInput({
    super.key,
    this.dialogTitle,
    this.hintText,
    this.validator,
    this.title,
    this.onChanged,
    this.onFieldSubmitted,
    // Using url as default keyboard type because it disables the sticker and
    // gif button on the keyboard.
    this.keyboardType = TextInputType.url,
    this.suffixIconPath,
    this.textEditingController,
    this.maxLength,
    this.validationRegExp,
    this.textCapitalization = TextCapitalization.sentences,
    this.autoFocus = true,
    this.suffixWidget,
    this.prefixWidget,
    this.initialValue,
    this.showDiscardDialog = true,
    this.inputFormatters,
    this.addBottomPaddingOnKeyboardOpen = true,
    this.shouldPopOnDonePressedWhenEmpty = true,
    this.isKeyboardPop = true,
    this.allowEmptyTextSubmit = false,
    this.allowNewLineText = true,
    this.textInputAction,
    this.onFocusChange,
    this.inputTextStyle,
    this.hintTextStyle,
    this.backgroundColor,
    this.cursorColor,
    this.focusNode,
    this.useKeyboardAttachable = false,
    this.autoCorrect = false,
    this.onTapOutside,
  });

  final MeString? dialogTitle;
  final MeString? hintText;
  final MeString? initialValue;
  final MeString? title;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final Future<bool?>? Function(String)? onFieldSubmitted;
  final TextInputType? keyboardType;
  final String? suffixIconPath;
  final Widget? suffixWidget;
  final Widget? prefixWidget;
  final TextEditingController? textEditingController;
  final int? maxLength;
  final String? validationRegExp;
  final TextCapitalization textCapitalization;
  final bool autoFocus;
  final bool showDiscardDialog;
  final List<TextInputFormatter>? inputFormatters;
  final bool addBottomPaddingOnKeyboardOpen;
  final bool shouldPopOnDonePressedWhenEmpty;
  final bool isKeyboardPop;
  final bool allowEmptyTextSubmit;
  final bool allowNewLineText;
  final TextInputAction? textInputAction;
  final void Function(bool)? onFocusChange;
  final MeFontStyle? inputTextStyle;
  final MeFontStyle? hintTextStyle;
  final Color? backgroundColor;
  final Color? cursorColor;
  final FocusNode? focusNode;
  final bool useKeyboardAttachable;
  final bool autoCorrect;
  final void Function(PointerDownEvent)? onTapOutside;

  @override
  Widget build(BuildContext context) {
    return MeTextInput(
      hintText: hintText,
      title: title,
      autoCorrect: autoCorrect,
      validator: validator,
      onChanged: onChanged,
      onFieldSubmitted: onFieldSubmitted,
      keyboardType: keyboardType,
      suffixIconPath: suffixIconPath,
      textEditingController: textEditingController,
      maxLength: maxLength,
      validationRegExp: validationRegExp,
      textCapitalization: textCapitalization,
      autoFocus: autoFocus,
      suffixWidget: suffixWidget,
      prefixWidget: prefixWidget,
      initialValue: initialValue,
      showDiscardDialog: showDiscardDialog,
      inputFormatters: inputFormatters,
      addBottomPaddingOnKeyboardOpen: addBottomPaddingOnKeyboardOpen,
      shouldPopOnDonePressedWhenEmpty: shouldPopOnDonePressedWhenEmpty,
      isKeyboardPop: isKeyboardPop,
      allowEmptyTextSubmit: allowEmptyTextSubmit,
      allowNewLineText: allowNewLineText,
      textInputAction: textInputAction,
      hintTextStyle: hintTextStyle,
      isDense: true,
      contentPadding: EdgeInsets.zero,
      onFocusChange: onFocusChange,
      isInPlaceInput: true,
      inputTextStyle: inputTextStyle,
      backgroundColor: backgroundColor,
      cursorColor: cursorColor,
      focusNode: focusNode,
      useKeyboardAttachable: useKeyboardAttachable,
      allowMultiLine: true,
      onTapOutside: onTapOutside,
    );
  }
}

class TextInputCountOverlay extends StatelessWidget {
  const TextInputCountOverlay({
    super.key,
    required this.rawText,
    required this.maxLength,
  });

  final String rawText;
  final int maxLength;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    int charactersTyped = UtilityMethods.getCharactersCount(
      rawText,
    );

    // Ensure charLeft is never negative
    int charLeft = math.max(0, maxLength - charactersTyped);
    (bool, double) show = UtilityMethods.showMaxLimitLeft(
      charactersTyped,
      maxLength,
    );

    return show.$1
        ? SizedBox(
            height: 32,
            width: 32,
            child: FloatingActionButton(
              elevation: 0,
              onPressed: null,
              backgroundColor: colorScheme.color6,
              child: Stack(
                children: [
                  Align(
                    alignment: Alignment.center,
                    child: MeText(
                      text: MeString(charLeft.toString()),
                      meFontStyle: MeFontStyle.I7,
                    ),
                  ),
                  Builder(
                    builder: (context) {
                      // print('showingFrom: $showingFrom');
                      // print('charLeft: $charLeft');
                      // print('maxLength: $maxLength');
                      // int maxCharOnOverlay = (maxLength - showingFrom);
                      // int charDoneOnOverlay =
                      //     // maxCharOnOverlay -
                      //     (charactersTyped - showingFrom);

                      // print('maxCharOnOverlay: $maxCharOnOverlay');
                      // print('charDoneOnOverlay: $charDoneOnOverlay');
                      // print('charactersTyped: $charactersTyped');
                      return CircularProgressIndicator(
                        value: show.$1 ? show.$2 : 0,
                        color: charLeft > 0
                            ? colorScheme.color35
                            : colorScheme.color14,
                        strokeWidth: 2,
                      );
                    },
                  ),
                ],
              ),
            ),
          )
        : const SizedBox.shrink();
  }
}

class DeleteDialogConfig {
  DeleteDialogConfig({
    required this.title,
    required this.description,
  });

  MeString title;
  MeString description;
}
