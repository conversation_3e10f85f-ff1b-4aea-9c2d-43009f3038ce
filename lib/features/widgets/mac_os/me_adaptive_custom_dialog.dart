import 'package:flutter/material.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';

/// A platform-aware dialog widget that conditionally wraps its content in a [Dialog]
/// based on the current platform.
///
/// 🔹 On **non-macOS platforms**, this behaves exactly like <PERSON><PERSON><PERSON>'s built-in [Dialog].
/// 🔹 On **macOS**, it **does not** wrap the content in a [Dialog], allowing
///     greater platform-specific control over layout, especially for use cases
///     like custom sidebar layouts where the `Dialog`'s behavior may be undesirable.
///
/// This is useful when you want to maintain platform-specific UI behavior
/// without duplicating dialog layout logic.
class MeAdaptiveCustomDialog extends StatelessWidget {
  const MeAdaptiveCustomDialog({
    super.key,
    required this.child,
    this.insetPadding,
    this.backgroundColor,
    this.elevation,
    this.shape,
    this.alignment,
    this.clipBehavior,
  });

  /// The main content of the dialog.
  final Widget child;

  /// The amount of space by which to inset the dialog.
  final EdgeInsets? insetPadding;

  /// The background color of the dialog.
  final Color? backgroundColor;

  /// The z-coordinate at which to place this dialog.
  final double? elevation;

  /// The shape of the dialog.
  final ShapeBorder? shape;

  /// Alignment of the dialog within its parent.
  final AlignmentGeometry? alignment;

  /// How to clip the content within the dialog.
  final Clip? clipBehavior;

  @override
  Widget build(BuildContext context) {
    // On macOS, return only the child (no Dialog wrapper)
    if (MePlatform.isMacOS) {
      return Container(
        color: backgroundColor,
        child: child,
      );
    }

    // On other platforms, wrap content in a standard Dialog
    return Dialog(
      insetPadding: insetPadding,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
      alignment: alignment,
      clipBehavior: clipBehavior,
      child: child,
    );
  }
}
