import 'dart:math' as math;

import 'package:audio_waveforms/audio_waveforms.dart' as aw;
import 'package:firebase_cached_image/firebase_cached_image.dart';
import 'package:flutter/material.dart';
import 'package:gal/gal.dart';
import 'package:just_audio/just_audio.dart';
import 'package:media_kit/media_kit.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/attachment_info.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/view/app_view.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/hamburger/gallery/widget/image_tile.dart';
import 'package:mevolve/features/hamburger/offline_alert_dialog.dart';
import 'package:mevolve/features/widgets/me_attachment_viewer/attachment_utility.dart';
import 'package:mevolve/features/widgets/me_attachment_viewer/widgets/attachment_info_sheet.dart';
import 'package:mevolve/features/widgets/me_attachment_viewer/widgets/audio_viewer_widget.dart';
import 'package:mevolve/features/widgets/me_attachment_viewer/widgets/image_viewer_widget.dart';
import 'package:mevolve/features/widgets/me_attachment_viewer/widgets/video_and_document_viewer_widget.dart';
import 'package:mevolve/features/widgets/me_banner.dart';
import 'package:mevolve/features/widgets/me_button.dart';
import 'package:mevolve/features/widgets/me_cached_storage_image.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/me_doc_bottom_bar/video_play_component.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/show_me_discard_dialog.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/features/widgets/waveform_animation_widget.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/me_app_colors.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/connectivity/internet_connectivity_service.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/encryption/me_encryption.dart';
import 'package:mevolve/utilities/logger/log_tags.dart';
import 'package:mevolve/utilities/logger/me_logger.dart';
import 'package:mevolve/utilities/nullable.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:rxdart/rxdart.dart';
import 'package:share_plus/share_plus.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/list_option_sheet.dart'
    show getSharePositionWithFallback;
import 'package:universal_io/io.dart';

class MeAttachmentViewer extends StatefulWidget {
  const MeAttachmentViewer({
    super.key,
    this.taskTitle,
    this.attachmentInfoList = const [],
    this.currentIndex = 0,
    this.isSupportMedia = false,
    this.isSupportPreview = false,
    this.encryptionData,
    this.showOptions = true,
    this.allowDelete = true,
    this.allowRestore = false,
    this.onAttachmentsUpdated,
    required this.featureType,
    this.taskId,
    this.dueDate,
    this.actionId,
    this.fromImageTile = false,
    this.isCameraPreview = false,
    required this.ownerUid,
    this.isNotThroughActionScreen = false,
    this.isPublic = false,
  }) : assert(
          (isSupportMedia == false && encryptionData != null) ||
              (isSupportMedia == true && encryptionData == null) ||
              (isPublic == true && encryptionData == null) ||
              (isPublic == false && encryptionData != null),
        );

  final List<MeAttachmentInfo> attachmentInfoList;
  final int currentIndex;
  final MeString? taskTitle;
  final bool isSupportMedia;
  final bool isSupportPreview;
  final EncryptionData? encryptionData;
  final bool showOptions;
  final bool allowDelete;
  final bool allowRestore;
  final Function(List<MeAttachmentInfo>)? onAttachmentsUpdated;
  final AttachmentFeatureType featureType;
  final String? taskId;
  final DateTime? dueDate;
  final String? actionId;
  final bool fromImageTile;
  final bool isCameraPreview;
  final String ownerUid;
  final bool isNotThroughActionScreen;
  final bool isPublic;

  @override
  State<MeAttachmentViewer> createState() => _MeAttachmentViewerState();
}

class _MeAttachmentViewerState extends State<MeAttachmentViewer>
    with TickerProviderStateMixin {
  // Controllers and state
  static final _log = MeLogger.getLogger(LogTags.attachment);
  final ValueNotifier<int> _currentIndexNotifier = ValueNotifier<int>(0);
  final ValueNotifier<bool> _showOverlay = ValueNotifier<bool>(false);
  late PageController _controller;
  AnimationController? _animationController;
  late Animation<Offset> _tweenTop;
  late Animation<Offset> _tweenBottom;

  List<MeAttachmentInfo> get activeAttachments {
    return widget.attachmentInfoList
        .where((attachment) => attachment.isValidAttachmentToShow)
        .toList();
  }

  // Media playback related state
  final ValueNotifier<aw.PlayerController?> _audioPlayer =
      ValueNotifier<aw.PlayerController?>(null);
  Player? _videoPlayer;
  final ValueNotifier<AudioWaveFormType> audioPlayerState =
      ValueNotifier<AudioWaveFormType>(AudioWaveFormType.stop);

  // Image and video related state
  final Map<int, ImageProvider> _cachedImages = {};
  late List<int> _rotation;
  int _rotationDegrees = 0;
  int? videoHeight;
  int? videoWidth;
  bool isViewing = false;

  // Flags and counters
  bool _isPreloaded = false;
  int audioDuration = 0;

  // Map where key is attachment ID and value is a map containing index and attachment
  static final Map<int, Map<String, dynamic>> _deletedAttachmentsMap = {};

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _setupAnimations();
    _initializeAttachments();
    _setupInitialOverlayState();
  }

  @override
  void didUpdateWidget(covariant MeAttachmentViewer oldWidget) {
    setState(() {});
    super.didUpdateWidget(oldWidget);
  }

  void _initializeControllers() {
    MeAttachmentInfo? currentItem =
        widget.attachmentInfoList[widget.currentIndex];
    final indexForPageView =
        activeAttachments.indexWhere((item) => item.id == currentItem.id) == -1
            ? 0
            : activeAttachments.indexWhere((item) => item.id == currentItem.id);
    _currentIndexNotifier.value = indexForPageView;

    _controller = PageController(initialPage: _currentIndexNotifier.value);
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
  }

  void _setupAnimations() {
    _tweenTop =
        Tween<Offset>(begin: const Offset(0, -1), end: const Offset(0, 0))
            .animate(
      CurvedAnimation(
        parent: _animationController ?? AnimationController(vsync: this),
        curve: Curves.easeOut,
      ),
    );
    _tweenBottom =
        Tween<Offset>(begin: const Offset(0, 1), end: const Offset(0, 0))
            .animate(
      CurvedAnimation(
        parent: _animationController ?? AnimationController(vsync: this),
        curve: Curves.easeOut,
      ),
    );
  }

  void _initializeAttachments() {
    _rotation = List<int>.filled(activeAttachments.length, 0);
  }

  void _setupInitialOverlayState() {
    if (!widget.isSupportMedia ||
        activeAttachments.any((item) => item.fileType == FileType.txt) ||
        (MePlatform.isMacOS &&
            activeAttachments.any((item) => item.fileType == FileType.audio))) {
      _animationController?.forward();
      _showOverlay.value = true;
    } else {
      _animationController?.reverse();
      _showOverlay.value = false;
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_isPreloaded) {
      _preloadImages();
      _isPreloaded = true;
    }
  }

  @override
  void dispose() {
    _cachedImages.clear();
    _animationController?.dispose();
    _animationController = null;
    _videoPlayer?.dispose();
    super.dispose();
  }

  Future<void> _preloadImages() async {
    for (var attachment in activeAttachments) {
      if (attachment.fileType == FileType.image) {
        String url = attachment.getAttachmentUrl(
          AttachmentSizeType.optimized,
          uid: widget.ownerUid,
          isSupportMedia: widget.isSupportMedia,
          featureType: widget.featureType,
          docId: widget.actionId ?? widget.taskId,
        );
        final imageProvider = await _getImageProvider(
          url,
          attachment.status,
          isPublic: widget.isPublic,
        );
        _cachedImages[attachment.id!] = imageProvider;
      }
    }
  }

  void updateOverlay() {
    if (!_showOverlay.value) {
      _animationController?.forward();
    } else {
      _animationController?.reverse();
    }
    _showOverlay.value = !_showOverlay.value;
  }

  Future<ImageProvider> _getImageProvider(
    String url,
    AttachmentUploadStatus status, {
    bool isPublic = false,
  }) async {
    return getMeCachedStorageImageProvider(
      context: context,
      url: url,
      storageType: status,
      encryptionData: widget.encryptionData,
      isSupportMedia: widget.isSupportMedia,
      isPublic: isPublic,
    );
  }

  Future<ImageProvider> _getOrCreateImageProvider(
    MeAttachmentInfo attachmentInfo,
    String url,
  ) async {
    if (_cachedImages.containsKey(attachmentInfo.id!)) {
      return _cachedImages[attachmentInfo.id!]!;
    }
    final future = await _getImageProvider(
      url,
      attachmentInfo.status,
      isPublic: widget.isPublic,
    );
    _cachedImages[attachmentInfo.id!] = future;
    return future;
  }

  void _onRotateImage(int index, int updatedRotation) {
    setState(() {
      _rotation[index] = updatedRotation;
    });
  }

  void _onRotateVideo(int updatedRotationDegrees) {
    setState(() {
      _rotationDegrees = updatedRotationDegrees;
    });
  }

  void _onShareAttachment(int index) async {
    if (widget.isCameraPreview) {
      final String? file = activeAttachments[index].localFilePath;
      if (file != null) {
        final shareRect = getSharePositionWithFallback(context);
        await Share.shareXFiles([XFile(file)], sharePositionOrigin: shareRect);
      }
      return;
    }

    MeAttachmentInfo attachment = activeAttachments[index];
    if (attachment.status == AttachmentUploadStatus.cloud) {
      if (!await UtilityMethods.isAttachmentAvailableOffline(
        attachment: attachment,
        context: context,
        allowImageCheck: true,
      )) {
        return;
      }
    }

    if (!mounted) return;

    UtilityMethods.showMeLoaderDialog(context);

    final attachmentInfo = activeAttachments[index];
    final FileType type = attachmentInfo.fileType;
    final url = attachmentInfo.getAttachmentUrl(
      type != FileType.image
          ? AttachmentSizeType.original
          : AttachmentSizeType.optimized,
      uid: widget.ownerUid,
      isSupportMedia: widget.isSupportMedia,
      featureType: widget.featureType,
      docId: widget.actionId ?? widget.taskId,
    );

    bool isOffline = !(await InternetConnectivityService.instance.hasInternet);

    if (type == FileType.image) {
      String? file;
      try {
        if (isOffline) {
          file = attachmentInfo.status == AttachmentUploadStatus.temporary ||
                  attachmentInfo.status == AttachmentUploadStatus.local
              ? attachmentInfo.localFilePath!
              : await FirebaseCacheManager()
                  .getSingleFile(FirebaseUrl(url))
                  .timeout(const Duration(seconds: 5));
        } else {
          file = attachmentInfo.status == AttachmentUploadStatus.temporary ||
                  attachmentInfo.status == AttachmentUploadStatus.local
              ? attachmentInfo.localFilePath!
              : await FirebaseCacheManager().getSingleFile(FirebaseUrl(url));
        }
      } catch (e) {
        if (isOffline && mounted) {
          offlineAlertDialog(context: context);
        }
        if (!mounted) return;
        Navigator.of(context).pop();
      }

      if (file == null) return;

      if (!mounted) return;
      final shareRect = getSharePositionWithFallback(context);
      await Share.shareXFiles([XFile(file)], sharePositionOrigin: shareRect);
    } else {
      String? currentUserUid = getCurrentUserUid();
      final File file = widget.encryptionData?.dek == null ||
              attachmentInfo.status == AttachmentUploadStatus.temporary ||
              attachmentInfo.status == AttachmentUploadStatus.local
          ? File(url)
          : await AttachmentUtility.decryptAttachment(
              widget.encryptionData!.dek!,
              url,
              attachmentInfo,
              type,
              currentUserUid,
            );

      if (!mounted) return;
      final shareRect = getSharePositionWithFallback(context);
      await Share.shareXFiles(
        [XFile(file.path)],
        sharePositionOrigin: shareRect,
      );
    }

    if (!mounted) return;
    Navigator.of(context).pop();
  }

  void _onDownloadAttachment(int index) async {
    if (widget.isCameraPreview) {
      final String? file = activeAttachments[index].localFilePath;
      final bool isImage = activeAttachments[index].fileType == FileType.image;
      final bool isVideo = activeAttachments[index].fileType == FileType.video;

      if (file != null) {
        _onDownloadCameraPreview(file, isImage, isVideo);
      }
      return;
    }

    MeAttachmentInfo attachment = activeAttachments[index];
    if (attachment.status == AttachmentUploadStatus.cloud) {
      if (!await UtilityMethods.isAttachmentAvailableOffline(
        attachment: attachment,
        context: context,
        allowImageCheck: true,
      )) {
        return;
      }
    }

    if (!mounted) return;

    UtilityMethods.showMeLoaderDialog(context);

    final attachmentInfo = activeAttachments[index];
    final FileType type = attachmentInfo.fileType;

    final url = attachmentInfo.getAttachmentUrl(
      type == FileType.image
          ? AttachmentSizeType.optimized
          : AttachmentSizeType.original,
      uid: widget.ownerUid,
      isSupportMedia: widget.isSupportMedia,
      featureType: widget.featureType,
      docId: widget.actionId ?? widget.taskId,
    );

    // macOS-specific download handling
    if (MePlatform.isMacOS) {
      await _downloadToMacOSDownloads(attachmentInfo, url, type);
      if (!mounted) return;
      Navigator.of(context).pop();
      return;
    }

    if (type == FileType.image) {
      if (MePlatform.isWeb) {
        if (attachmentInfo.status == AttachmentUploadStatus.temporary ||
            attachmentInfo.status == AttachmentUploadStatus.local) {
          final fileName = '${attachmentInfo.id}.${attachmentInfo.format}';
          AttachmentUtility.downloadWebDecryptedFile(
            attachmentInfo.localFilePath!,
            fileName,
          );
        } else {
          await AttachmentUtility.viewFileWeb(
            context: context,
            attachment: attachmentInfo,
            key: widget.encryptionData!.dek!,
            fileUrl: url,
            shouldDownload: true,
            shouldOpenOnceDone: false,
          );
        }
      } else {
        bool isLocal = attachmentInfo.status == AttachmentUploadStatus.local ||
            attachmentInfo.status == AttachmentUploadStatus.temporary;
        bool isOffline =
            !(await InternetConnectivityService.instance.hasInternet);
        String? file;

        try {
          if (isOffline) {
            file = isLocal
                ? attachmentInfo.localFilePath!
                : await FirebaseCacheManager()
                    .getSingleFile(FirebaseUrl(url))
                    .timeout(const Duration(seconds: 5));
          } else {
            file = isLocal
                ? attachmentInfo.localFilePath!
                : await FirebaseCacheManager().getSingleFile(FirebaseUrl(url));
          }
        } catch (e) {
          if (isOffline && mounted) {
            offlineAlertDialog(context: context);
          }
          if (!mounted) return;
          Navigator.of(context).pop();
        }

        if (file == null) return;

        try {
          _log.d(
            'DOWNLOAD_IMAGE_MOBILE: Starting Gal.putImage for file: $file',
          );
          await Gal.putImage(file, album: 'Mevolve');
          _log.d('DOWNLOAD_IMAGE_MOBILE: Gal.putImage completed successfully');
        } catch (e) {
          _log.e('DOWNLOAD_IMAGE_MOBILE: Gal.putImage failed: $e');
          if (!mounted) return;
          Navigator.of(context).pop();
          return;
        }

        if (context.mounted) {
          showMeSnackbar(
            MeTranslations.instance.toast_imageDownloaded_content,
            MeTranslations.instance.screen_imagePreview_view,
            () {
              OpenFile.open(file);
            },
          );
        }
      }
    } else {
      // Download logic for other file types
      // This would be the complex download logic from your original file
      if (MePlatform.isWeb) {
        if (!mounted) return;

        if (attachmentInfo.status == AttachmentUploadStatus.temporary ||
            attachmentInfo.status == AttachmentUploadStatus.local) {
          final fileName = '${attachmentInfo.id}.${attachmentInfo.format}';
          AttachmentUtility.downloadWebDecryptedFile(
            attachmentInfo.localFilePath!,
            fileName,
          );
        } else {
          await AttachmentUtility.viewFileWeb(
            context: context,
            attachment: attachmentInfo,
            key: widget.encryptionData!.dek!,
            fileUrl: url,
            shouldDownload: true,
            shouldOpenOnceDone: false,
          );
        }
      } else {
        String? currentUserUid = getCurrentUserUid();

        final File file = widget.encryptionData?.dek == null ||
                attachmentInfo.status == AttachmentUploadStatus.temporary ||
                attachmentInfo.status == AttachmentUploadStatus.local ||
                attachmentInfo.fileType == FileType.txt
            ? File(url)
            : await AttachmentUtility.decryptAttachment(
                widget.encryptionData!.dek!,
                url,
                attachmentInfo,
                type,
                currentUserUid,
              );

        if (type == FileType.video) {
          await Gal.putVideo(file.path, album: 'Mevolve');

          if (context.mounted) {
            showMeSnackbar(
              MeTranslations.instance.toast_videoDownloaded_content,
              MeTranslations.instance.screen_imagePreview_view,
              () {
                OpenFile.open(file.path);
              },
            );
          }
        } else if (type == FileType.audio) {
          Directory downloadsDirectory = MePlatform.isAndroid
              ? Directory('/storage/emulated/0/Download/')
              : await getApplicationDocumentsDirectory();

          await file.copy(
            '${downloadsDirectory.path}/${attachmentInfo.originalFileName}',
          );

          showMeSnackbar(
            MeTranslations.instance.toast_audioDownloaded_content,
            MeTranslations.instance.screen_imagePreview_view,
            type == FileType.txt
                ? null
                : () {
                    OpenFile.open(file.path);
                  },
            type == FileType.txt ? 16 : 8,
          );
        } else {
          Directory downloadsDirectory = MePlatform.isAndroid
              ? Directory('/storage/emulated/0/Download/')
              : await getTemporaryDirectory();

          final String filePath =
              '${downloadsDirectory.path}/${attachmentInfo.fileType == FileType.txt ? 'LOG_${attachmentInfo.id}.txt' : attachmentInfo.originalFileName}';

          String path =
              await FirebaseCacheManager().getSingleFile(FirebaseUrl(url));
          File file = File(path);
          await file.copy(filePath);

          if (context.mounted) {
            showMeSnackbar(
              type == FileType.audio
                  ? MeTranslations.instance.toast_audioDownloaded_content
                  : type == FileType.txt
                      ? MeTranslations.instance.toast_logDownloaded_content
                      : MeTranslations
                          .instance.toast_documentDownloaded_content,
              MeTranslations.instance.screen_imagePreview_view,
              () {
                OpenFile.open(filePath);
              },
              type == FileType.txt ? 16 : 8,
            );
          }
        }
      }
    }

    if (!mounted) return;
    Navigator.of(context).pop();
  }

  /// Handle macOS downloads to Downloads folder
  Future<void> _downloadToMacOSDownloads(
    MeAttachmentInfo attachmentInfo,
    String url,
    FileType type,
  ) async {
    try {
      // Get user's actual Downloads directory
      final String homeDir = Platform.environment['HOME'] ?? '';
      late final String downloadsPath;

      if (homeDir.isNotEmpty) {
        downloadsPath = '$homeDir/Downloads';
      } else {
        // Fallback to app documents if HOME is not available
        Directory appDocuments = await getApplicationDocumentsDirectory();
        downloadsPath = '${appDocuments.path}/Downloads';
      }

      final downloadsDir = Directory(downloadsPath);
      if (!await downloadsDir.exists()) {
        await downloadsDir.create(recursive: true);
      }

      final fileName = attachmentInfo.originalFileName ??
          '${attachmentInfo.id}.${attachmentInfo.format}';

      String? sourceFilePath;

      // Get source file
      if (attachmentInfo.status == AttachmentUploadStatus.local ||
          attachmentInfo.status == AttachmentUploadStatus.temporary) {
        sourceFilePath = attachmentInfo.localFilePath;
      } else {
        bool isOffline =
            !(await InternetConnectivityService.instance.hasInternet);
        if (isOffline) {
          sourceFilePath = await FirebaseCacheManager()
              .getSingleFile(FirebaseUrl(url))
              .timeout(const Duration(seconds: 5));
        } else {
          sourceFilePath =
              await FirebaseCacheManager().getSingleFile(FirebaseUrl(url));
        }
      }

      if (sourceFilePath == null) return;

      // Handle decryption if needed
      File sourceFile;
      if (widget.encryptionData?.dek != null &&
          attachmentInfo.status != AttachmentUploadStatus.temporary &&
          attachmentInfo.status != AttachmentUploadStatus.local &&
          attachmentInfo.fileType != FileType.txt) {
        String? currentUserUid = getCurrentUserUid();
        // Use original URL for decryption, not the cached file path
        sourceFile = await AttachmentUtility.decryptAttachment(
          widget.encryptionData!.dek!,
          url, // Use original URL instead of sourceFilePath
          attachmentInfo,
          type,
          currentUserUid,
        );
      } else {
        sourceFile = File(sourceFilePath);
      }

      // Copy to Downloads
      final String finalFileName =
          type == FileType.txt ? 'LOG_${attachmentInfo.id}.txt' : fileName;
      await sourceFile.copy('$downloadsPath/$finalFileName');

      _log.d(
        'DOWNLOAD_MACOS: File copied to Downloads folder: $downloadsPath/$finalFileName',
      );

      // Show success message
      if (context.mounted) {
        MeString message;
        switch (type) {
          case FileType.image:
            message = MeTranslations.instance.toast_imageDownloaded_content;
            break;
          case FileType.video:
            message = MeTranslations.instance.toast_videoDownloaded_content;
            break;
          case FileType.audio:
            message = MeTranslations.instance.toast_audioDownloaded_content;
            break;
          case FileType.txt:
            message = MeTranslations.instance.toast_logDownloaded_content;
            break;
          default:
            message = MeTranslations.instance.toast_documentDownloaded_content;
        }

        showMeSnackbar(
          message,
          MeTranslations.instance.screen_imagePreview_view,
          () {
            OpenFile.open('$downloadsPath/$finalFileName');
          },
          type == FileType.txt ? 16 : 8,
        );
      }
    } catch (e) {
      _log.e('DOWNLOAD_MACOS: Failed: $e');
      if (e.toString().contains('timeout') && mounted) {
        offlineAlertDialog(context: context);
      }
    }
  }

  void _onDownloadCameraPreview(
    String filePath,
    bool isImage,
    bool isVideo,
  ) async {
    if (isImage) {
      try {
        _log.d(
          'DOWNLOAD_IMAGE_MOBILE: Starting Gal.putImage for file: $filePath',
        );
        await Gal.putImage(filePath, album: 'Mevolve');
        _log.d('DOWNLOAD_IMAGE_MOBILE: Gal.putImage completed successfully');
      } catch (e) {
        _log.e('DOWNLOAD_IMAGE_MOBILE: Gal.putImage failed: $e');
        if (!mounted) return;
        Navigator.of(context).pop();
        return;
      }
    }

    if (isVideo) {
      await Gal.putVideo(filePath, album: 'Mevolve');
    }

    if (context.mounted) {
      showMeSnackbar(
        isImage
            ? MeTranslations.instance.toast_imageDownloaded_content
            : MeTranslations.instance.toast_videoDownloaded_content,
        MeTranslations.instance.screen_imagePreview_view,
        () {
          OpenFile.open(filePath);
        },
      );
    }
  }

  void _onUpdateVideoDimensions(int? height, int? width) {
    if (height != null) {
      videoHeight = height;
    }
    if (width != null) {
      videoWidth = width;
    }
    // Mark as needing rebuild only after the current build is complete
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) setState(() {});
    });
  }

  Future<bool> prepareVideo() async {
    return VideoPreparationService.prepareVideo(
      context: context,
      attachment: activeAttachments[_currentIndexNotifier.value],
      ownerUid: widget.ownerUid,
      featureType: widget.featureType,
      actionId: widget.actionId,
      taskId: widget.taskId,
      encryptionData: widget.encryptionData,
      videoPlayer: _videoPlayer,
      onVideoPlayerSet: (player) {
        setState(() {
          _videoPlayer = player;
        });
      },
    );
  }

  void _updateAttachments(List<MeAttachmentInfo> updatedList, int pageIndex) {
    if (_sendUpdatedAttachments(updatedList)) {
      _controller
          .animateToPage(
        pageIndex,
        duration: const Duration(milliseconds: 200),
        curve: Curves.bounceInOut,
      )
          .then((_) {
        setState(() {});
      });
    }
  }

  bool _sendUpdatedAttachments(List<MeAttachmentInfo> updatedList) {
    widget.onAttachmentsUpdated?.call(updatedList.toList());

    List<MeAttachmentInfo> activeAttachmentsAfterUpdate = updatedList
        .where((attachment) => attachment.deletedAt == null)
        .toList();

    if (activeAttachmentsAfterUpdate.isEmpty) {
      Navigator.pop(context);
      // Don't continue.
      return false;
    } else if (!_controller.hasClients) {
      // Don't continue.
      return false;
    } else {
      return true;
    }
  }

  void _deleteAttachment(BuildContext context, final int index) {
    AttachmentDeletionManager.markAttachmentAsDeleted(
      attachment: activeAttachments[index],
      onAttachmentsChanged: (updatedList) {
        _stopMediaPlayers();
        _updateAttachments(
          updatedList,
          index < updatedList.length ? index : updatedList.length - 1,
        );
      },
      attachments: widget.attachmentInfoList,
      deletedAttachmentsMap: _deletedAttachmentsMap,
      taskTitle: widget.taskTitle,
      showSnackbar: widget.allowRestore,
    );
  }

  void _stopMediaPlayers() {
    // Stop audio player
    if (_audioPlayer.value?.playerState == aw.PlayerState.playing) {
      _audioPlayer.value?.stopPlayer();
    }
    audioPlayerState.value = AudioWaveFormType.stop;
    // Stop video player
    _videoPlayer?.seek(Duration.zero);
    _videoPlayer?.stop();
  }

  @override
  Widget build(BuildContext context) {
    if (activeAttachments.isEmpty) return const SizedBox.shrink();
    final int count = activeAttachments.length;
    final bool isTaskValidEntry = getIfTaskValidEntry(
      widget.featureType,
      context,
      widget.taskId,
      widget.dueDate,
    );
    return _buildMainComponent(count, isTaskValidEntry);
  }

  Widget _buildMainComponent(int count, bool isTaskValidEntry) {
    return AttachmentViewerPopScope(
      isCameraPreview: widget.isCameraPreview,
      onPop: () {
        Navigator.of(context).pop();
      },
      onCameraPreviewDiscard: (shouldDiscard) {
        if (shouldDiscard == true && mounted) {
          Navigator.of(context).pop(false);
        }
      },
      child: AttachmentViewerLayout(
        isBigScreen: ScreenSizeState.instance.isBigScreen,
        count: count,
        currentIndexNotifier: _currentIndexNotifier,
        onBackPressed: () {
          _controller.previousPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        },
        onForwardPressed: () {
          _controller.nextPage(
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeInOut,
          );
        },
        onClose: () {
          Navigator.of(context).pop();
        },
        mainContent: _buildMainContent(isTaskValidEntry, count),
      ),
    );
  }

  Widget _buildMainContent(bool isTaskValidEntry, int count) {
    return Container(
      constraints: BoxConstraints(
        maxWidth: ScreenSizeState.instance.screenMaxWidth,
      ),
      color: MeAppColors.instance.darkColorScheme.color6,
      child: Scaffold(
        backgroundColor: MeAppColors.instance.darkColorScheme.color6,
        resizeToAvoidBottomInset: false,
        body: SafeArea(
          child: Stack(
            children: [
              AttachmentViewerContent(
                attachmentFileList: activeAttachments,
                controller: _controller,
                currentIndexNotifier: _currentIndexNotifier,
                rotation: _rotation,
                ownerUid: widget.ownerUid,
                isSupportMedia: widget.isSupportMedia,
                featureType: widget.featureType,
                actionId: widget.actionId,
                taskId: widget.taskId,
                encryptionData: widget.encryptionData,
                isViewing: isViewing,
                videoPlayer: _videoPlayer,
                rotationDegrees: _rotationDegrees,
                animationController: _animationController,
                onControllerReady: (controller) {
                  _videoPlayer = controller;
                  // Mark as needing rebuild only after the current build is complete
                  WidgetsBinding.instance.addPostFrameCallback((_) {
                    if (mounted) setState(() {});
                  });
                },
                prepareVideo: prepareVideo,
                updateOverlay: updateOverlay,
                showOverlay: _showOverlay,
                getOrCreateImageProvider: _getOrCreateImageProvider,
                audioPlayer: _audioPlayer,
                onVideoStateChanged: ({
                  int? rotationDegrees,
                  int? videoHeight,
                  int? videoWidth,
                }) {
                  setState(() {
                    if (rotationDegrees != null) {
                      _rotationDegrees = rotationDegrees;
                    }
                    if (videoHeight != null) {
                      this.videoHeight = videoHeight;
                    }
                    if (videoWidth != null) {
                      this.videoWidth = videoWidth;
                    }
                  });
                },
              ),
              TopOverlays(
                colorScheme: MeAppColors.instance.darkColorScheme,
                isTaskValidEntry: isTaskValidEntry,
                currentIndexNotifier: _currentIndexNotifier,
                tweenTop: _tweenTop,
                tweenBottom: _tweenBottom,
                activeAttachments: activeAttachments,
                rotation: _rotation,
                rotationDegrees: _rotationDegrees,
                videoHeight: videoHeight,
                videoWidth: videoWidth,
                audioPlayer: _audioPlayer,
                videoPlayer: _videoPlayer,
                audioPlayerState: audioPlayerState,
                ownerUid: widget.ownerUid,
                encryptionData: widget.encryptionData,
                taskId: widget.taskId,
                actionId: widget.actionId,
                taskTitle: widget.taskTitle,
                isSupportMedia: widget.isSupportMedia,
                isSupportPreview: widget.isSupportPreview,
                allowDelete: widget.allowDelete,
                showOptions: widget.showOptions,
                isCameraPreview: widget.isCameraPreview,
                isNotThroughActionScreen: widget.isNotThroughActionScreen,
                featureType: widget.featureType,
                onAttachmentUpdate: (updatedAttachment) {
                  final updatedList = widget.attachmentInfoList.toList();
                  final index = updatedList.indexWhere(
                    (attachment) => attachment.id == updatedAttachment.id,
                  );
                  if (index != -1) {
                    updatedList[index] = updatedAttachment;
                  }
                  _updateAttachments(updatedList, _currentIndexNotifier.value);
                },
                onDeleteAttachment: _deleteAttachment,
                onRotateImage: _onRotateImage,
                onRotateVideo: _onRotateVideo,
                onShareAttachment: _onShareAttachment,
                onDownloadAttachment: _onDownloadAttachment,
                onDownloadCameraPreview: _onDownloadCameraPreview,
                onUpdateVideoDimensions: _onUpdateVideoDimensions,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// For the main layout
class AttachmentViewerLayout extends StatelessWidget {
  const AttachmentViewerLayout({
    Key? key,
    required this.isBigScreen,
    required this.count,
    required this.currentIndexNotifier,
    required this.onBackPressed,
    required this.onForwardPressed,
    required this.onClose,
    required this.mainContent,
  }) : super(key: key);
  final bool isBigScreen;
  final int count;
  final ValueNotifier<int> currentIndexNotifier;
  final VoidCallback onBackPressed;
  final VoidCallback onForwardPressed;
  final VoidCallback onClose;
  final Widget mainContent;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: MeAppColors.instance.darkColorScheme.color13.withValues(
        alpha: 0.7,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (isBigScreen)
            Expanded(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: onClose,
                child: Align(
                  alignment: Alignment.center,
                  child: ValueListenableBuilder<int>(
                    valueListenable: currentIndexNotifier,
                    builder: (context, currentIndex, child) {
                      return BackAndForwardButton(
                        onPressed: currentIndex > 0 ? onBackPressed : null,
                        isBack: true,
                      );
                    },
                  ),
                ),
              ),
            ),
          Center(child: mainContent),
          if (isBigScreen)
            Expanded(
              child: GestureDetector(
                behavior: HitTestBehavior.opaque,
                onTap: onClose,
                child: ValueListenableBuilder<int>(
                  valueListenable: currentIndexNotifier,
                  builder: (context, currentIndex, child) {
                    return Align(
                      alignment: Alignment.center,
                      child: BackAndForwardButton(
                        onPressed:
                            currentIndex < count - 1 ? onForwardPressed : null,
                        isBack: false,
                      ),
                    );
                  },
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class AttachmentViewerContent extends StatelessWidget {
  const AttachmentViewerContent({
    Key? key,
    required this.attachmentFileList,
    required this.controller,
    required this.currentIndexNotifier,
    required this.rotation,
    required this.ownerUid,
    this.isSupportMedia = false,
    required this.featureType,
    this.actionId,
    this.taskId,
    this.encryptionData,
    this.isViewing = false,
    this.videoPlayer,
    this.rotationDegrees = 0,
    this.animationController,
    required this.onControllerReady,
    required this.prepareVideo,
    required this.updateOverlay,
    required this.showOverlay,
    required this.getOrCreateImageProvider,
    required this.audioPlayer,
    required this.onVideoStateChanged,
  }) : super(key: key);
  final List<MeAttachmentInfo> attachmentFileList;
  final PageController controller;
  final ValueNotifier<int> currentIndexNotifier;
  final List<int> rotation;
  final String ownerUid;
  final bool isSupportMedia;
  final AttachmentFeatureType featureType;
  final String? actionId;
  final String? taskId;
  final dynamic encryptionData;
  final bool isViewing;
  final dynamic videoPlayer;
  final int rotationDegrees;
  final AnimationController? animationController;
  final dynamic Function(Player) onControllerReady;
  final Function prepareVideo;
  final Function() updateOverlay;
  final ValueNotifier<bool> showOverlay;
  final Future<ImageProvider<Object>> Function(MeAttachmentInfo, String)
      getOrCreateImageProvider;
  final ValueNotifier<dynamic> audioPlayer;
  final Function({int? rotationDegrees, int? videoHeight, int? videoWidth})
      onVideoStateChanged;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return GestureDetector(
      onTap: updateOverlay,
      child: _buildAttachmentsPageView(colorScheme),
    );
  }

  Widget _buildAttachmentsPageView(MeColorScheme colorScheme) {
    return PageView.builder(
      controller: controller,
      onPageChanged: (value) async {
        currentIndexNotifier.value = value;
        // Stop any active media players
        videoPlayer?.stop();
        videoPlayer?.seek(Duration.zero);
      },
      itemCount: attachmentFileList.length,
      itemBuilder: (context, index) {
        final MeAttachmentInfo attachmentInfo = attachmentFileList[index];

        return _buildAttachmentItem(attachmentInfo, index, colorScheme);
      },
    );
  }

  Widget _buildAttachmentItem(
    MeAttachmentInfo attachmentInfo,
    int index,
    MeColorScheme colorScheme,
  ) {
    switch (attachmentInfo.fileType) {
      case FileType.image:
        return Builder(
          builder: (_) {
            String url = attachmentInfo.getAttachmentUrl(
              AttachmentSizeType.optimized,
              uid: ownerUid,
              isSupportMedia: isSupportMedia,
              featureType: featureType,
              docId: actionId ?? taskId,
            );
            return ImageViewerWidget(
              attachmentInfo: attachmentInfo,
              url: url,
              getOrCreateImageProvider: getOrCreateImageProvider,
              initialRotation: rotation[index],
              isViewing: isViewing,
            );
          },
        );

      case FileType.txt:
        return Center(
          child: Assets.svg.icLogThumbnail.svg(
            height: 100,
            fit: BoxFit.fitHeight,
            colorFilter: ColorFilter.mode(
              colorScheme.color35,
              BlendMode.srcIn,
            ),
          ),
        );

      case FileType.audio:
        return AudioViewerWidget(
          key: ValueKey(attachmentInfo.id),
          attachment: attachmentInfo,
          showOverlay: showOverlay,
          ownerUid: ownerUid,
          featureType: featureType,
          taskId: taskId,
          actionId: actionId,
          encryptionData: encryptionData,
          onControllerInitialised: (controller) {
            WidgetsBinding.instance.addPostFrameCallback((_) {
              audioPlayer.value = controller;
            });
          },
        );

      case FileType.video:
        return Transform.rotate(
          angle: rotationDegrees * math.pi / 180,
          child: VideoPlayerPlayer(
            animationController: animationController,
            attachment: attachmentInfo,
            onControllerReady: onControllerReady,
            prepareVideo: prepareVideo,
            showOverlay: showOverlay,
            player: videoPlayer,
            onVideoChanged: () {
              // Pass state changes to parent widget
              onVideoStateChanged(
                rotationDegrees: 0,
                videoHeight: null,
                videoWidth: null,
              );
            },
          ),
        );

      case FileType.document:
        return DocumentViewerWidget(
          fileType: FileType.document,
          attachment: attachmentInfo,
          encryptionData: encryptionData,
          featureType: featureType,
          showOverlay: showOverlay,
          docId: actionId ?? taskId,
          ownerId: ownerUid,
        );
    }
  }
}

class TopOverlays extends StatelessWidget {
  const TopOverlays({
    Key? key,
    required this.colorScheme,
    required this.isTaskValidEntry,
    required this.currentIndexNotifier,
    required this.tweenTop,
    required this.tweenBottom,
    required this.activeAttachments,
    required this.rotation,
    required this.rotationDegrees,
    required this.videoHeight,
    required this.videoWidth,
    required this.audioPlayer,
    required this.audioPlayerState,
    required this.videoPlayer,
    required this.ownerUid,
    required this.encryptionData,
    required this.taskId,
    required this.actionId,
    required this.taskTitle,
    required this.isSupportMedia,
    required this.isSupportPreview,
    required this.allowDelete,
    required this.showOptions,
    required this.isCameraPreview,
    required this.isNotThroughActionScreen,
    required this.featureType,
    required this.onAttachmentUpdate,
    required this.onDeleteAttachment,
    this.iconHeight = 20,
    this.iconWidth = 20,
    this.iconHorizontalPadding = 12,
    this.iconVerticalPadding = 16,
    this.onRotateImage,
    this.onRotateVideo,
    this.onShareAttachment,
    this.onDownloadAttachment,
    this.onDownloadCameraPreview,
    this.onUpdateVideoDimensions,
  }) : super(key: key);
  final MeColorScheme colorScheme;
  final bool isTaskValidEntry;
  final ValueNotifier<int> currentIndexNotifier;
  final Animation<Offset> tweenTop;
  final Animation<Offset> tweenBottom;
  final List<MeAttachmentInfo> activeAttachments;
  final List<int> rotation;
  final int rotationDegrees;
  final int? videoHeight;
  final int? videoWidth;
  final ValueNotifier<aw.PlayerController?> audioPlayer;
  final ValueNotifier<AudioWaveFormType> audioPlayerState;
  final Player? videoPlayer;
  final String? ownerUid;
  final EncryptionData? encryptionData;
  final String? taskId;
  final String? actionId;
  final MeString? taskTitle;
  final bool isSupportMedia;
  final bool isSupportPreview;
  final bool allowDelete;
  final bool showOptions;
  final bool isCameraPreview;
  final bool isNotThroughActionScreen;
  final AttachmentFeatureType? featureType;
  final Function(MeAttachmentInfo)? onAttachmentUpdate;
  final Function(BuildContext, int) onDeleteAttachment;

  // Icon dimensions
  final double iconHeight;
  final double iconWidth;
  final double iconHorizontalPadding;
  final double iconVerticalPadding;

  final Function(int, int)? onRotateImage;
  final Function(int)? onRotateVideo;
  final Function(int)? onShareAttachment;
  final Function(int)? onDownloadAttachment;
  final Function(String, bool, bool)? onDownloadCameraPreview;
  final Function(int?, int?)? onUpdateVideoDimensions;

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<int>(
      valueListenable: currentIndexNotifier,
      builder: (context, currentIndex, child) {
        if (currentIndexNotifier.value >= activeAttachments.length) {
          return const SizedBox.shrink();
        }
        final FileType type = currentIndex == activeAttachments.length
            ? activeAttachments[currentIndex - 1].fileType
            : activeAttachments[currentIndex].fileType;
        return Stack(
          children: [
            // TOP OVERLAYS
            SlideTransition(
              position: tweenTop,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  taskTitle != null && taskTitle != MeString.empty
                      ? Container(
                          width: MediaQuery.of(context).size.width,
                          padding: EdgeInsets.only(
                            left: 16,
                            right: 16,
                            top: 16,
                            bottom: activeAttachments[currentIndex].fileType ==
                                    FileType.txt
                                ? 16
                                : 0,
                          ),
                          decoration: BoxDecoration(
                            color: MeAppColors.instance.darkColorScheme.color13
                                .withValues(alpha: 0.7),
                          ),
                          child: Row(
                            children: [
                              if (activeAttachments[currentIndex].fileType ==
                                  FileType.txt) ...[
                                Row(
                                  children: [
                                    MeIcon(
                                      iconPath: Assets.svg.icLogIcon.path,
                                      iconColor: colorScheme.color1,
                                      iconSize: const SizedBox(
                                        width: 14,
                                        height: 18,
                                      ),
                                      iconContainerSize: const SizedBox(
                                        width: 30,
                                        height: 30,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 8,
                                    ),
                                    MeText(
                                      text: MeString.fromVariable(
                                        'LOG_${activeAttachments[currentIndex].id}.txt',
                                      ),
                                      meFontStyle: MeFontStyle.C12,
                                      textOverflow: TextOverflow.ellipsis,
                                    ),
                                  ],
                                ),
                              ],
                              if (taskTitle != null &&
                                  taskTitle != MeString.empty)
                                Expanded(
                                  child: MeText(
                                    text: taskTitle!,
                                    meFontStyle: MeFontStyle.C12,
                                    textOverflow: TextOverflow.ellipsis,
                                  ),
                                )
                              else
                                const SizedBox.shrink(),
                              const Spacer(),
                              if (activeAttachments[currentIndex].fileType !=
                                      FileType.txt &&
                                  !isCameraPreview)
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: MeAppColors
                                        .instance.darkColorScheme.color3,
                                    borderRadius: BorderRadius.circular(
                                      8,
                                    ),
                                  ),
                                  child: MeText(
                                    text: MeString(
                                      '${currentIndex + 1} / ${activeAttachments.length}',
                                    ),
                                    meFontStyle: MeFontStyle.I8D,
                                  ),
                                ),
                              const SizedBox(width: 24),
                              MeIconButton(
                                iconPath: Assets.svg.closeIcon.path,
                                iconColor: MeAppColors
                                    .instance.darkColorScheme.color12,
                                iconSize: const SizedBox(
                                  height: 13,
                                  width: 13,
                                ),
                                iconContainerSize: const SizedBox(
                                  height: 24,
                                  width: 24,
                                ),
                                onPressed: () async {
                                  if (isCameraPreview) {
                                    bool? shouldDiscard =
                                        await showMeDiscardDialog(
                                      context,
                                      title: MeTranslations.instance
                                          .overlay_attachmentDiscard_title,
                                      insetPadding: ScreenSizeState.instance
                                          .getDialogInsetPadding(
                                        DialogLevel.level1,
                                      ),
                                      text: MeTranslations.instance
                                          .overlay_attachmentDiscard_content,
                                    );
                                    if (shouldDiscard == true &&
                                        context.mounted) {
                                      Navigator.of(context).pop(false);
                                    }
                                  } else {
                                    Navigator.of(context).pop();
                                  }
                                },
                              ),
                            ],
                          ),
                        )
                      : Container(
                          width: MediaQuery.of(context).size.width,
                          padding: const EdgeInsets.only(
                            left: 16,
                            right: 16,
                            bottom: 16,
                            top: 16,
                          ),
                          decoration: BoxDecoration(
                            color: MeAppColors.instance.darkColorScheme.color13
                                .withValues(alpha: 0.7),
                          ),
                          child: AttachmentInfoComponent(
                            attachmentInfo: activeAttachments[currentIndex],
                            currentIndex: currentIndex,
                            totalAttachments: activeAttachments.length,
                            isTaskValidEntry: isTaskValidEntry,
                            showCloseIcon: true,
                            isSupportMedia: isSupportMedia,
                            allowDelete: allowDelete,
                            taskId: taskId,
                            actionId: actionId,
                            taskTitle: taskTitle?.text,
                            isCameraPreview: isCameraPreview,
                            videoHeight: videoHeight,
                            videoWidth: videoWidth,
                            onNameChange: (val) {
                              final updatedAttachment =
                                  activeAttachments[currentIndex].copyWith(
                                originalFileName: val,
                              );

                              onAttachmentUpdate?.call(updatedAttachment);
                            },
                          ),
                        ),
                  if (activeAttachments[currentIndex].fileType !=
                          FileType.txt &&
                      isCameraPreview == false &&
                      (taskTitle != null && taskTitle != MeString.empty))
                    Container(
                      width: MediaQuery.of(context).size.width,
                      padding: const EdgeInsets.only(
                        left: 16,
                        right: 16,
                        bottom: 16,
                        top: 16,
                      ),
                      decoration: BoxDecoration(
                        color: MeAppColors.instance.darkColorScheme.color13
                            .withValues(alpha: 0.7),
                      ),
                      child: AttachmentInfoComponent(
                        attachmentInfo: activeAttachments[currentIndex],
                        currentIndex: currentIndex,
                        totalAttachments: activeAttachments.length,
                        isTaskValidEntry: isTaskValidEntry,
                        showCloseIcon: false,
                        isSupportMedia: isSupportMedia,
                        allowDelete: allowDelete,
                        taskId: taskId,
                        actionId: actionId,
                        taskTitle: taskTitle?.text,
                        isCameraPreview: isCameraPreview,
                        videoHeight: videoHeight,
                        videoWidth: videoWidth,
                        onNameChange: (val) {
                          final updatedAttachment =
                              activeAttachments[currentIndex].copyWith(
                            originalFileName: val,
                          );

                          onAttachmentUpdate?.call(updatedAttachment);
                        },
                      ),
                    ),
                  if (!isTaskValidEntry)
                    MeMaterialBanner(
                      text: MeTranslations
                          .instance.bottomSheet_actionCommon_invalidEntry,
                      fontStyle: MeFontStyle.F14,
                      tileColor: MeAppColors.instance.darkColorScheme.color28,
                      centerText: true,
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                      ),
                    ),
                ],
              ),
            ),
            !showOptions
                ? const SizedBox.shrink()
                : SlideTransition(
                    position: tweenBottom,
                    child: Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        decoration: BoxDecoration(
                          color: MeAppColors.instance.darkColorScheme.color13
                              .withValues(alpha: 0.7),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            if (type == FileType.audio && !MePlatform.isMacOS)
                              ValueListenableBuilder<aw.PlayerController?>(
                                valueListenable: audioPlayer,
                                builder: (
                                  context,
                                  player,
                                  child,
                                ) {
                                  return NativeAudioProgressWidget(
                                    audioPlayer: player,
                                    currentIndex: currentIndex,
                                    attachmentFileList: activeAttachments,
                                    audioPlayerState: audioPlayerState,
                                  );
                                },
                              ),
                            if (type == FileType.video)
                              VideoProgressWidget(
                                videoPlayer: videoPlayer,
                                currentIndex: currentIndex,
                                attachmentFileList: activeAttachments,
                                updateVideoDimensions: (
                                  height,
                                  width,
                                ) {
                                  onUpdateVideoDimensions?.call(height, width);
                                },
                              ),
                            Row(
                              children: [
                                // Share button
                                if (!isSupportPreview &&
                                    !MePlatform.isWeb &&
                                    type != FileType.txt)
                                  Builder(
                                    builder: (buttonContext) => MeIconButton(
                                      isCircularIconButton: false,
                                      iconPath: Assets.svg.icShare.path,
                                      iconColor: MeAppColors
                                          .instance.darkColorScheme.color12,
                                      iconSize: SizedBox(
                                        height: iconHeight,
                                        width: iconWidth,
                                      ),
                                      iconContainerSize: const SizedBox(
                                        height: 24,
                                        width: 24,
                                      ),
                                      padding: EdgeInsets.symmetric(
                                        horizontal: iconHorizontalPadding,
                                        vertical: iconVerticalPadding,
                                      ),
                                      onPressed: () async {
                                        if (isCameraPreview) {
                                          final String? file =
                                              activeAttachments[currentIndex]
                                                  .localFilePath;
                                          if (file != null) {
                                            final shareRect =
                                                getSharePositionWithFallback(
                                              buttonContext,
                                            );
                                            await Share.shareXFiles(
                                              [XFile(file)],
                                              sharePositionOrigin: shareRect,
                                            );
                                          }
                                          return;
                                        }

                                        onShareAttachment?.call(currentIndex);
                                      },
                                    ),
                                  ),
                                // Rotate button for images
                                if (type == FileType.image)
                                  MeIconButton(
                                    isCircularIconButton: false,
                                    iconPath: Assets.svg.icRotate.path,
                                    iconColor: MeAppColors
                                        .instance.darkColorScheme.color12,
                                    iconSize: SizedBox(
                                      height: iconHeight,
                                      width: iconWidth,
                                    ),
                                    iconContainerSize: const SizedBox(
                                      height: 24,
                                      width: 24,
                                    ),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: iconHorizontalPadding,
                                      vertical: iconVerticalPadding,
                                    ),
                                    onPressed: () {
                                      final updatedRotation =
                                          (rotation[currentIndex] + 1) % 4;
                                      onRotateImage?.call(
                                        currentIndex,
                                        updatedRotation,
                                      );
                                    },
                                  ),
                                // Download button
                                if (!isSupportPreview)
                                  MeIconButton(
                                    isCircularIconButton: false,
                                    iconPath: Assets.svg.icDownload.path,
                                    iconColor: MeAppColors
                                        .instance.darkColorScheme.color12,
                                    iconSize: SizedBox(
                                      height: iconHeight,
                                      width: iconWidth,
                                    ),
                                    iconContainerSize: const SizedBox(
                                      height: 24,
                                      width: 24,
                                    ),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: iconHorizontalPadding,
                                      vertical: iconVerticalPadding,
                                    ),
                                    onPressed: () async {
                                      if (isCameraPreview) {
                                        final String? file =
                                            activeAttachments[currentIndex]
                                                .localFilePath;
                                        final bool isImage =
                                            activeAttachments[currentIndex]
                                                    .fileType ==
                                                FileType.image;
                                        final bool isVideo =
                                            activeAttachments[currentIndex]
                                                    .fileType ==
                                                FileType.video;

                                        if (file != null) {
                                          onDownloadCameraPreview?.call(
                                            file,
                                            isImage,
                                            isVideo,
                                          );
                                        }
                                        return;
                                      }

                                      onDownloadAttachment?.call(currentIndex);
                                    },
                                  ),
                                // Rotate button for videos
                                if (type == FileType.video)
                                  MeIconButton(
                                    isCircularIconButton: false,
                                    iconPath: Assets.svg.icRotate.path,
                                    iconColor: MeAppColors
                                        .instance.darkColorScheme.color12,
                                    iconSize: SizedBox(
                                      height: iconHeight,
                                      width: iconWidth,
                                    ),
                                    iconContainerSize: const SizedBox(
                                      height: 24,
                                      width: 24,
                                    ),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: iconHorizontalPadding,
                                      vertical: iconVerticalPadding,
                                    ),
                                    onPressed: () {
                                      final updatedRotationDegrees =
                                          (rotationDegrees + 90) % 360;
                                      onRotateVideo
                                          ?.call(updatedRotationDegrees);
                                    },
                                  ),
                                // Delete button
                                if (allowDelete)
                                  MeIconButton(
                                    isCircularIconButton: false,
                                    iconPath: Assets.svg.deleteIcon.path,
                                    iconColor: MeAppColors
                                        .instance.darkColorScheme.color12,
                                    iconSize: SizedBox(
                                      height: iconHeight,
                                      width: iconWidth,
                                    ),
                                    iconContainerSize: const SizedBox(
                                      height: 24,
                                      width: 24,
                                    ),
                                    padding: EdgeInsets.symmetric(
                                      horizontal: iconHorizontalPadding,
                                      vertical: iconVerticalPadding,
                                    ),
                                    onPressed: () async {
                                      // Delete confirmation dialog
                                      bool? shouldDelete =
                                          isNotThroughActionScreen == false
                                              ? true
                                              : await showDialog(
                                                  context: context,
                                                  builder: (
                                                    BuildContext ctx,
                                                  ) {
                                                    return MeDialog(
                                                      title: MeTranslations
                                                          .instance
                                                          .overlay_deleteAttachment_deletetitle,
                                                      description: MeTranslations
                                                          .instance
                                                          .overlay_deleteAttachment_deletecontent,
                                                      quaternaryText: MeTranslations
                                                          .instance
                                                          .screen_common_delete,
                                                      secondaryText: MeTranslations
                                                          .instance
                                                          .screen_common_buttonCancel,
                                                      quaternaryOnTap: () {
                                                        Navigator.pop(
                                                          ctx,
                                                          true,
                                                        );
                                                      },
                                                      secondaryOnTap: () {
                                                        Navigator.pop(
                                                          ctx,
                                                          false,
                                                        );
                                                      },
                                                    );
                                                  },
                                                );
                                      if (shouldDelete == null ||
                                          !shouldDelete) {
                                        return;
                                      }
                                      if (!context.mounted) {
                                        return;
                                      }
                                      if (activeAttachments.isNotEmpty) {
                                        onDeleteAttachment(
                                          context,
                                          currentIndex,
                                        );
                                      }
                                    },
                                  ),
                                const Spacer(),
                                // Save button for camera preview
                                if (isCameraPreview)
                                  MeButton(
                                    color: colorScheme.color35,
                                    disabledColor: colorScheme.color35,
                                    fontStyle: MeFontStyle.C35,
                                    buttonType: ButtonType.text,
                                    title: MeTranslations
                                        .instance.screen_common_save,
                                    onPressed: () {
                                      Navigator.of(context).pop(true);
                                    },
                                  ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
          ],
        );
      },
    );
  }
}

class AttachmentInfoComponent extends StatelessWidget {
  const AttachmentInfoComponent({
    Key? key,
    required this.attachmentInfo,
    required this.currentIndex,
    required this.totalAttachments,
    required this.isTaskValidEntry,
    this.showCloseIcon = false,
    this.isSupportMedia = true,
    this.allowDelete = false,
    this.taskId,
    this.actionId,
    this.taskTitle,
    this.isCameraPreview = false,
    this.videoHeight,
    this.videoWidth,
    this.onNameChange,
  }) : super(key: key);

  final MeAttachmentInfo attachmentInfo;
  final int currentIndex;
  final int totalAttachments;
  final bool isTaskValidEntry;
  final bool showCloseIcon;
  final bool isSupportMedia;
  final bool allowDelete;
  final String? taskId;
  final String? actionId;
  final String? taskTitle;
  final bool isCameraPreview;
  final int? videoHeight;
  final int? videoWidth;
  final Function(String)? onNameChange;

  @override
  Widget build(BuildContext context) {
    FileType type = attachmentInfo.fileType;
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (attachmentInfo.originalFileName != null)
          Expanded(
            child: Row(
              children: [
                MeIcon(
                  iconPath: type == FileType.audio
                      ? Assets.svg.audioIcon.path
                      : type == FileType.video
                          ? Assets.svg.camRecordingIcon.path
                          : type == FileType.document || type == FileType.txt
                              ? Assets.svg.documentIcon.path
                              : Assets.svg.imageGallery.path,
                  iconColor: MeAppColors.instance.darkColorScheme.color1,
                  iconSize: const SizedBox(
                    height: 17,
                    width: 17,
                  ),
                  iconContainerSize: const SizedBox(
                    height: 24,
                    width: 24,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: MeText(
                    text: MeString.fromVariable(
                      attachmentInfo.originalFileName!,
                    ),
                    meFontStyle: MeFontStyle.C12,
                    textOverflow: TextOverflow.ellipsis,
                    lineThrough: !isTaskValidEntry,
                  ),
                ),
              ],
            ),
          )
        else
          const SizedBox.shrink(),
        if (totalAttachments > 1 &&
            (taskTitle == null || (taskTitle != null && taskTitle!.isEmpty)))
          Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
              vertical: 2,
            ),
            decoration: BoxDecoration(
              color: MeAppColors.instance.darkColorScheme.color3,
              borderRadius: BorderRadius.circular(
                8,
              ),
            ),
            child: MeText(
              text: MeString(
                '${currentIndex + 1} / $totalAttachments',
              ),
              meFontStyle: MeFontStyle.I8D,
            ),
          ),
        if (!isSupportMedia)
          Container(
            margin: const EdgeInsets.only(
              left: 16,
            ),
            child: MeIconButton(
              iconPath: Assets.svg.infoIcon.path,
              iconColor: MeAppColors.instance.darkColorScheme.color12,
              iconSize: const SizedBox(
                height: 18,
                width: 18,
              ),
              iconContainerSize: const SizedBox(
                height: 24,
                width: 24,
              ),
              onPressed: () {
                showMeScrollableModalBottomSheet(
                  bottomSheetLevel: 1,
                  builder: (_, __) {
                    return AttachmentInfoSheet(
                      canDelete: allowDelete,
                      info: attachmentInfo,
                      videoHeight: videoHeight,
                      videoWidth: videoWidth,
                      onNameChange: (val) {
                        if (onNameChange != null) {
                          onNameChange!(val);
                        }
                      },
                    );
                  },
                );
              },
            ),
          ),
        if (showCloseIcon) ...[
          const SizedBox(width: 16),
          MeIconButton(
            iconPath: Assets.svg.closeIcon.path,
            iconColor: MeAppColors.instance.darkColorScheme.color12,
            iconSize: const SizedBox(
              height: 13,
              width: 13,
            ),
            iconContainerSize: const SizedBox(
              height: 24,
              width: 24,
            ),
            onPressed: () async {
              if (isCameraPreview) {
                bool? shouldDiscard = await showMeDiscardDialog(
                  context,
                  title:
                      MeTranslations.instance.overlay_attachmentDiscard_title,
                  insetPadding: ScreenSizeState.instance.getDialogInsetPadding(
                    DialogLevel.level1,
                  ),
                  text:
                      MeTranslations.instance.overlay_attachmentDiscard_content,
                );
                if (shouldDiscard == true && context.mounted) {
                  Navigator.of(context).pop(false);
                }
              } else {
                Navigator.of(context).pop();
              }
            },
          ),
        ],
      ],
    );
  }
}

// For PopScope handling
class AttachmentViewerPopScope extends StatelessWidget {
  const AttachmentViewerPopScope({
    Key? key,
    required this.isCameraPreview,
    required this.onPop,
    required this.onCameraPreviewDiscard,
    required this.child,
  }) : super(key: key);
  final bool isCameraPreview;
  final VoidCallback onPop;
  final Function(bool?) onCameraPreviewDiscard;
  final Widget child;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) async {
        if (!didPop) {
          if (isCameraPreview) {
            bool? shouldDiscard = await showMeDiscardDialog(
              context,
              title: MeTranslations.instance.overlay_attachmentDiscard_title,
              insetPadding: ScreenSizeState.instance
                  .getDialogInsetPadding(DialogLevel.level1),
              text: MeTranslations.instance.overlay_attachmentDiscard_content,
            );
            onCameraPreviewDiscard(shouldDiscard);
          } else {
            onPop();
          }
        }
      },
      child: child,
    );
  }
}

// For video preparation
class VideoPreparationService {
  static Future<bool> prepareVideo({
    required BuildContext context,
    required MeAttachmentInfo attachment,
    required String ownerUid,
    required AttachmentFeatureType featureType,
    required String? actionId,
    required String? taskId,
    required EncryptionData? encryptionData,
    required Player? videoPlayer,
    required Function(Player) onVideoPlayerSet,
  }) async {
    if (attachment.status == AttachmentUploadStatus.cloud) {
      if (!await UtilityMethods.isAttachmentAvailableOffline(
        attachment: attachment,
        context: context,
      )) {
        return false;
      }
    }

    if (!context.mounted) return false;
    UtilityMethods.showMeLoaderDialog(context);

    String videoUrl = attachment.getAttachmentUrl(
      AttachmentSizeType.original,
      uid: ownerUid,
      isSupportMedia: false,
      featureType: featureType,
      docId: actionId ?? taskId,
    );

    try {
      if (MePlatform.isWeb) {
        return await _prepareWebVideo(
          context: context,
          attachment: attachment,
          videoUrl: videoUrl,
          encryptionData: encryptionData,
          videoPlayer: videoPlayer,
          onVideoPlayerSet: onVideoPlayerSet,
        );
      } else {
        return await _prepareNativeVideo(
          context: context,
          attachment: attachment,
          videoUrl: videoUrl,
          encryptionData: encryptionData,
          videoPlayer: videoPlayer,
          onVideoPlayerSet: onVideoPlayerSet,
        );
      }
    } catch (e) {
      if (!context.mounted) return false;
      Navigator.of(context).pop();
      return false;
    }
  }

  static Future<bool> _prepareWebVideo({
    required BuildContext context,
    required MeAttachmentInfo attachment,
    required String videoUrl,
    required EncryptionData? encryptionData,
    required Player? videoPlayer,
    required Function(Player) onVideoPlayerSet,
  }) async {
    String? url = attachment.status == AttachmentUploadStatus.local ||
            attachment.status == AttachmentUploadStatus.temporary
        ? videoUrl
        : await AttachmentUtility.viewFileWeb(
            context: context,
            attachment: attachment,
            key: encryptionData!.dek!,
            fileUrl: videoUrl,
            shouldOpenOnceDone: false,
          );

    if (url == null && context.mounted) {
      Navigator.of(context).pop();
      return false;
    }

    await videoPlayer?.stop();
    await videoPlayer?.open(Media(url!), play: false);

    if (!context.mounted) return false;
    Navigator.of(context).pop();
    return true;
  }

  static Future<bool> _prepareNativeVideo({
    required BuildContext context,
    required MeAttachmentInfo attachment,
    required String videoUrl,
    required EncryptionData? encryptionData,
    required Player? videoPlayer,
    required Function(Player) onVideoPlayerSet,
  }) async {
    String finalUrl = getMeFileUrl(videoUrl, attachment.status);
    String? currentUserUid = getCurrentUserUid();

    File file = attachment.status == AttachmentUploadStatus.local ||
            attachment.status == AttachmentUploadStatus.temporary
        ? File(finalUrl)
        : await AttachmentUtility.decryptAttachment(
            encryptionData?.dek,
            finalUrl,
            attachment,
            attachment.fileType,
            currentUserUid,
          );

    await videoPlayer?.stop();
    await videoPlayer?.open(Media(file.path), play: false);

    if (!context.mounted) return false;
    Navigator.of(context).pop();
    return true;
  }
}

/// A utility class to handle attachment deletion and restoration operations
/// consistently across the application.
///
/// # Key Features
///
/// 1. **Deletion Management**:
///    - Stores deleted attachments with their original index and full state
///    - Handles deletion differently for temporary vs cloud attachments
///    - Provides snackbar notifications with undo functionality
///
/// 2. **Restoration Logic**:
///    - Attempts to restore at the original position
///    - Preserves all original properties including timestamps
///    - Handles edge cases when list structure changes between deletion and undo
///
/// 3. **Unified Interface**:
///    - Works with MeAttachmentWidget and MeAttachmentViewer
///    - Maintains consistent behavior across different UI components
///
class AttachmentDeletionManager {
  /// Marks an attachment as deleted while preserving its original state and position
  ///
  /// @param attachment The attachment to delete
  /// @param attachments The full list of attachments
  /// @param onAttachmentsChanged Callback to notify parent about the changes
  /// @param taskTitle Optional task title for snackbar messages
  /// @param showSnackbar Whether to show a snackbar with undo option
  static void markAttachmentAsDeleted({
    required MeAttachmentInfo attachment,
    required List<MeAttachmentInfo> attachments,
    required Function(List<MeAttachmentInfo>) onAttachmentsChanged,
    required Map<int, Map<String, dynamic>> deletedAttachmentsMap,
    MeString? taskTitle,
    bool showSnackbar = true,
  }) {
    debugPrint('Deleting attachment: ${attachment.id}');

    // Find the original attachment and its index in the main list
    int originalIndex = -1;
    MeAttachmentInfo? originalAttachment;

    for (int i = 0; i < attachments.length; i++) {
      if (attachments[i].id == attachment.id) {
        originalIndex = i;
        originalAttachment = attachments[i];
        break;
      }
    }

    if (originalAttachment == null) return;

    // Store the original index and attachment in the map
    deletedAttachmentsMap[attachment.id!] = {
      'index': originalIndex,
      'attachment': originalAttachment,
    };

    // Create a copy of the attachments list
    List<MeAttachmentInfo> updatedAttachments = List.from(attachments);

    // Check if the attachment is temporary
    if (originalAttachment.status == AttachmentUploadStatus.temporary) {
      // For temporary attachments, completely remove from the list
      updatedAttachments.removeWhere(
        (element) => element.id == attachment.id,
      );
    } else {
      // For non-temporary attachments, mark as deleted (original behavior)
      final index = updatedAttachments.indexWhere(
        (element) => element.id == attachment.id,
      );

      if (index != -1) {
        updatedAttachments[index] = attachment.copyWith(
          deletedAt: Nullable(DateTime.now()),
        );
      }
    }

    // Notify parent about the changes
    onAttachmentsChanged(updatedAttachments);

    // Show undo snackbar if requested
    if (showSnackbar && taskTitle != null) {
      _showUndoSnackbar(
        deletedAttachment: attachment,
        taskTitle: taskTitle,
        onUndo: () => restoreDeletedAttachment(
          attachment: attachment,
          attachments: updatedAttachments,
          onAttachmentsChanged: onAttachmentsChanged,
          deletedAttachmentsMap: deletedAttachmentsMap,
        ),
      );
    }
  }

  /// Restores a previously deleted attachment
  ///
  /// @param attachment The attachment to restore
  /// @param attachments The current list of attachments
  /// @param onAttachmentsChanged Callback to notify parent about the changes
  static void restoreDeletedAttachment({
    required MeAttachmentInfo attachment,
    required List<MeAttachmentInfo> attachments,
    required Function(List<MeAttachmentInfo>) onAttachmentsChanged,
    required Map<int, Map<String, dynamic>> deletedAttachmentsMap,
  }) {
    debugPrint('Restoring attachment: ${attachment.id}');

    // Remove any existing snackbar
    scaffoldMessengerKey.currentState?.removeCurrentSnackBar();

    // Check if we have this attachment in our deleted map
    final deletedInfo = deletedAttachmentsMap[attachment.id];
    if (deletedInfo == null) return;

    // Create a copy of the attachments list
    List<MeAttachmentInfo> updatedAttachments = List.from(attachments);

    final originalAttachment = deletedInfo['attachment'] as MeAttachmentInfo;
    final originalIndex = deletedInfo['index'] as int;

    // Edge case: We might have an outdated widget.attachments if the widget
    // was unmounted due to not having any attachments. Check if the attachment
    // is already in the list, and if so, remove it to avoid duplicates.
    updatedAttachments.removeWhere(
      (element) => element.id == attachment.id,
    );

    //The original attachment will have deletedAt set null so lets make it non null
    MeAttachmentInfo restoredAttachment = originalAttachment.copyWith(
      deletedAt: Nullable(DateTime.now()), // Clear the deletedAt
    );
    // Now lets make it null again as it was restored from deletion.
    // This process will update the createdAt to now
    restoredAttachment = restoredAttachment.copyWith(
      deletedAt: const Nullable(null), // Clear the deletedAt
    );

    // Add it back at the original index or at the end if index is out of bounds
    if (originalIndex < updatedAttachments.length) {
      updatedAttachments.insert(originalIndex, restoredAttachment);
    } else {
      updatedAttachments.add(restoredAttachment);
    }

    // Remove from deleted map
    deletedAttachmentsMap.remove(attachment.id);

    // Notify parent about the changes
    onAttachmentsChanged(updatedAttachments);
  }

  /// Shows a snackbar with undo option when an attachment is deleted
  ///
  /// @param deletedAttachment The deleted attachment
  /// @param taskTitle The title of the task
  /// @param onUndo Callback when undo button is pressed
  static void _showUndoSnackbar({
    required MeAttachmentInfo deletedAttachment,
    required MeString taskTitle,
    required VoidCallback onUndo,
  }) {
    final String taskTitleText = taskTitle.text;
    MeString message;

    // Set message based on file type
    switch (deletedAttachment.fileType) {
      case FileType.image:
        message = MeTranslations.instance
            .toast_imageDeleted_contentNew(taskTitle: taskTitleText);
        break;
      case FileType.video:
        message = MeTranslations.instance
            .toast_videoDeleted_content(fileName: taskTitleText);
        break;
      case FileType.audio:
        message = MeTranslations.instance
            .toast_audioDeleted_content(taskTitle: taskTitleText);
        break;
      default:
        message = MeTranslations.instance
            .toast_documentDeleted_content(taskTitle: taskTitleText);
    }

    showMeSnackbar(
      message,
      MeTranslations.instance.screen_common_undo,
      onUndo,
    );
  }
}

class VideoProgressWidget extends StatelessWidget {
  const VideoProgressWidget({
    super.key,
    required Player? videoPlayer,
    required int currentIndex,
    required List<MeAttachmentInfo> attachmentFileList,
    required this.updateVideoDimensions,
  })  : _videoPlayer = videoPlayer,
        _currentIndex = currentIndex,
        _attachmentFileList = attachmentFileList;

  final Player? _videoPlayer;
  final int _currentIndex;
  final List<MeAttachmentInfo> _attachmentFileList;
  final Function(int?, int?) updateVideoDimensions;

  @override
  Widget build(BuildContext context) {
    if (_videoPlayer == null) {
      MeAttachmentInfo attachmentInfo =
          _currentIndex >= _attachmentFileList.length
              ? _attachmentFileList[_currentIndex - 1]
              : _attachmentFileList[_currentIndex];
      final int? durationFromMetadata = attachmentInfo.metadata != null
          ? attachmentInfo.metadata!['duration']
          : null;
      final totalDuration = durationFromMetadata != null
          ? Duration(
              milliseconds: durationFromMetadata,
            )
          : Duration.zero;
      return Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ),
        child: Row(
          children: [
            MeText(
              text: MeString.fromVariable(
                '00:00',
              ),
              meFontStyle: MeFontStyle.F12,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: SliderTheme(
                data: SliderTheme.of(
                  context,
                ).copyWith(
                  thumbColor: Colors.transparent,
                  trackShape: EdgeToEdgeTrackShape(),
                  thumbShape: const RoundSliderThumbShape(
                    enabledThumbRadius: 3.0,
                  ),
                ),
                child: Slider(
                  value: 0,
                  min: 0,
                  max: 1,
                  activeColor: MeAppColors.instance.darkColorScheme.color12,
                  inactiveColor: MeAppColors.instance.darkColorScheme.color7,
                  onChanged: (double value) {
                    _videoPlayer?.seek(
                      Duration(
                        seconds: value.toInt(),
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(width: 12),
            MeText(
              text: MeString.fromVariable(
                '${totalDuration.inMinutes.toString().padLeft(2, '0')}:${(totalDuration.inSeconds % 60).toString().padLeft(2, '0')}',
              ),
              meFontStyle: MeFontStyle.F12,
            ),
          ],
        ),
      );
    }
    return StreamBuilder(
      stream: _videoPlayer!.stream.position,
      builder: (context, snapshot) {
        final position = snapshot.data ?? Duration.zero;
        bool isCompleted = _videoPlayer?.state.completed ?? false;
        if (isCompleted) {
          _videoPlayer?.seek(
            const Duration(seconds: 0),
          );
          _videoPlayer?.pause();
        }
        MeAttachmentInfo attachmentInfo =
            _currentIndex >= _attachmentFileList.length
                ? _attachmentFileList[_currentIndex - 1]
                : _attachmentFileList[_currentIndex];
        final int? durationFromMetadata = attachmentInfo.metadata != null
            ? attachmentInfo.metadata!['duration']
            : null;
        final totalDuration = durationFromMetadata != null
            ? Duration(
                milliseconds: durationFromMetadata,
              )
            : _videoPlayer?.state.duration ?? Duration.zero;
        if (attachmentInfo.metadata == null ||
            attachmentInfo.metadata?['height'] == null ||
            attachmentInfo.metadata?['width'] == null) {
          updateVideoDimensions(
            _videoPlayer?.state.height,
            _videoPlayer?.state.width,
          );
        }
        updateVideoDimensions(
          _videoPlayer?.state.height,
          _videoPlayer?.state.width,
        );
        return Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Row(
            children: [
              MeText(
                text: MeString.fromVariable(
                  '${position.inMinutes.toString().padLeft(2, '0')}:${(position.inSeconds % 60).toString().padLeft(2, '0')}',
                ),
                meFontStyle: MeFontStyle.F12,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(
                    context,
                  ).copyWith(
                    thumbColor: Colors.transparent,
                    trackShape: EdgeToEdgeTrackShape(),
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 3.0,
                    ),
                  ),
                  child: Slider(
                    value: _videoPlayer?.state.duration == Duration.zero
                        ? 0
                        : position.inSeconds.toDouble(),
                    min: 0,
                    max: _videoPlayer?.state.duration == Duration.zero
                        ? 0
                        : totalDuration.inSeconds.toDouble(),
                    activeColor: MeAppColors.instance.darkColorScheme.color12,
                    inactiveColor: MeAppColors.instance.darkColorScheme.color7,
                    onChanged: (double value) {
                      _videoPlayer!.seek(
                        Duration(
                          seconds: value.toInt(),
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              MeText(
                text: MeString.fromVariable(
                  '${totalDuration.inMinutes.toString().padLeft(2, '0')}:${(totalDuration.inSeconds % 60).toString().padLeft(2, '0')}',
                ),
                meFontStyle: MeFontStyle.F12,
              ),
            ],
          ),
        );
      },
    );
  }
}

class NativeAudioProgressWidget extends StatelessWidget {
  const NativeAudioProgressWidget({
    super.key,
    aw.PlayerController? audioPlayer,
    required int currentIndex,
    required List<MeAttachmentInfo> attachmentFileList,
    required this.audioPlayerState,
  })  : _audioPlayer = audioPlayer,
        _currentIndex = currentIndex,
        _attachmentFileList = attachmentFileList;

  final aw.PlayerController? _audioPlayer;
  final int _currentIndex;
  final List<MeAttachmentInfo> _attachmentFileList;
  final ValueNotifier<AudioWaveFormType> audioPlayerState;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<int>(
      stream: _audioPlayer?.onCurrentDurationChanged,
      builder: (context, snapshot) {
        int intPosition = snapshot.data ?? 0;
        Duration position = Duration(
          milliseconds: _audioPlayer == null
              ? 0
              : _audioPlayer!.playerState.isStopped
                  ? 0
                  : intPosition,
        );
        MeAttachmentInfo attachmentInfo =
            _currentIndex >= _attachmentFileList.length
                ? _attachmentFileList[_currentIndex - 1]
                : _attachmentFileList[_currentIndex];
        final double? durationFromMetadata = attachmentInfo.metadata != null
            ? attachmentInfo.metadata!['duration']?.toDouble()
            : null;
        final totalDuration = durationFromMetadata != null
            ? Duration(
                milliseconds: durationFromMetadata.toInt(),
              )
            : Duration(
                milliseconds:
                    _audioPlayer == null ? 0 : _audioPlayer!.maxDuration,
              );
        return Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Row(
            children: [
              MeText(
                text: MeString.fromVariable(
                  '${position.inMinutes.toString().padLeft(2, '0')}:${(position.inSeconds % 60).toString().padLeft(2, '0')}',
                ),
                meFontStyle: MeFontStyle.F12,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(
                    context,
                  ).copyWith(
                    trackShape: EdgeToEdgeTrackShape(),
                    thumbColor: Colors.transparent,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 3.0,
                    ),
                  ),
                  child: Slider(
                    value: position.inSeconds.toDouble(),
                    min: 0,
                    max: totalDuration.inSeconds.toDouble(),
                    activeColor: MeAppColors.instance.darkColorScheme.color12,
                    inactiveColor: MeAppColors.instance.darkColorScheme.color7,
                    onChanged: (
                      double value,
                    ) async {
                      if (_audioPlayer == null) return;
                      _audioPlayer!.seekTo(
                        Duration(
                          seconds: value.toInt(),
                        ).inMilliseconds,
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 12),
              MeText(
                text: MeString.fromVariable(
                  '${totalDuration.inMinutes.toString().padLeft(2, '0')}:${(totalDuration.inSeconds % 60).toString().padLeft(2, '0')}',
                ),
                meFontStyle: MeFontStyle.F12,
              ),
            ],
          ),
        );
      },
    );
  }
}

class WebAudioProgressWidget extends StatelessWidget {
  const WebAudioProgressWidget({
    super.key,
    required AudioPlayer webAudioPlayer,
    required int currentIndex,
    required List<MeAttachmentInfo> attachmentFileList,
    required this.colorScheme,
  })  : _webAudioPlayer = webAudioPlayer,
        _currentIndex = currentIndex,
        _attachmentFileList = attachmentFileList;

  final AudioPlayer _webAudioPlayer;
  final int _currentIndex;
  final List<MeAttachmentInfo> _attachmentFileList;
  final MeColorScheme colorScheme;

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<Duration>(
      stream: _webAudioPlayer.positionStream,
      builder: (context, snapshot) {
        final position = snapshot.data ?? Duration.zero;
        MeAttachmentInfo attachmentInfo =
            _currentIndex >= _attachmentFileList.length
                ? _attachmentFileList[_currentIndex - 1]
                : _attachmentFileList[_currentIndex];
        final double? durationFromMetadata = attachmentInfo.metadata != null
            ? attachmentInfo.metadata!['duration']?.toDouble()
            : null;
        final totalDuration = durationFromMetadata != null
            ? Duration(
                milliseconds: durationFromMetadata.toInt(),
              )
            : _webAudioPlayer.duration ?? Duration.zero;
        return Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Row(
            children: [
              MeText(
                text: MeString.fromVariable(
                  '${position.inMinutes.toString().padLeft(2, '0')}:${(position.inSeconds % 60).toString().padLeft(2, '0')}',
                ),
                meFontStyle: MeFontStyle.F12,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: SliderTheme(
                  data: SliderTheme.of(
                    context,
                  ).copyWith(
                    thumbColor: Colors.transparent,
                    thumbShape: const RoundSliderThumbShape(
                      enabledThumbRadius: 3.0,
                    ),
                  ),
                  child: Slider(
                    value: position.inSeconds.toDouble(),
                    min: 0,
                    max: totalDuration.inSeconds.toDouble(),
                    activeColor: colorScheme.color12,
                    inactiveColor: colorScheme.color7,
                    onChanged: (double value) {
                      _webAudioPlayer.seek(
                        Duration(
                          seconds: value.toInt(),
                        ),
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(width: 8),
              MeText(
                text: MeString.fromVariable(
                  '${totalDuration.inMinutes.toString().padLeft(2, '0')}:${(totalDuration.inSeconds % 60).toString().padLeft(2, '0')}',
                ),
                meFontStyle: MeFontStyle.F12,
              ),
            ],
          ),
        );
      },
    );
  }
}

class BackAndForwardButton extends StatelessWidget {
  const BackAndForwardButton({
    super.key,
    this.onPressed,
    required this.isBack,
  });

  final void Function()? onPressed;
  final bool isBack;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return onPressed == null
        ? const SizedBox.shrink()
        : Container(
            decoration: BoxDecoration(
              color: colorScheme.color19,
              borderRadius: BorderRadius.circular(4),
            ),
            child: MeIconButton(
              isCircularIconButton: false,
              iconPath: isBack
                  ? Assets.svg.leftArrow.path
                  : Assets.svg.rightArrow.path,
              iconColor: colorScheme.color12,
              buttonColor: colorScheme.color19,
              iconSize: const SizedBox(
                height: 13,
                width: 7.9,
              ),
              iconContainerSize: const SizedBox(
                height: 24,
                width: 24,
              ),
              onPressed: onPressed,
            ),
          );
  }
}

class EdgeToEdgeTrackShape extends RoundedRectSliderTrackShape {
  // Override getPreferredRect to adjust the track's dimensions
  @override
  Rect getPreferredRect({
    required RenderBox parentBox,
    Offset offset = Offset.zero,
    required SliderThemeData sliderTheme,
    bool isEnabled = false,
    bool isDiscrete = false,
  }) {
    final double trackHeight = sliderTheme.trackHeight ?? 2.0;
    final double trackWidth = parentBox.size.width;
    final double trackTop =
        offset.dy + (parentBox.size.height - trackHeight) / 2;
    return Rect.fromLTWH(offset.dx, trackTop, trackWidth, trackHeight);
  }
}

/// StreamBuilder wrapper for MeAttachmentViewer that converts stream to list
///
/// This wrapper handles stream-to-list conversion at a higher level, keeping
/// MeAttachmentViewer simple with its original List-based API. Use this when
/// you have a stream of attachment data that needs to be displayed.
/// ## Stream Communication Pattern
///
/// This widget uses a BehaviorSubject to keep attachment data synchronized between
/// this parent widget and the MeAttachmentViewer that opens via Navigator.push.
///
/// **Problem:** When MeAttachmentViewer opens in a new navigation context, it can't
/// receive updates if the parent's attachment list changes.
///
/// **Solution:** Global stream manager for cross-navigation communication
/// ```
/// 1. Parent calls MeAttachmentViewerStreamManager.updateStreamForTask(taskId, attachments)
/// 2. Parent gets stream via MeAttachmentViewerStreamManager.getStreamForTask(taskId)
/// 3. Parent passes stream to MeAttachmentViewer via Navigator.push
/// 4. Viewer listens to stream and updates UI when new data arrives
/// 5. When parent's attachments change, updateStreamForTask() emits to global stream
/// 6. Viewer automatically receives update and syncs its UI
/// 7. BehaviorSubject ensures new subscribers get the last emitted value immediately
/// ```
///
/// **Data Flow:**
/// - Parent → Child: Global stream provides real-time attachment updates
/// - Child → Parent: onAttachmentsUpdated callback handles user actions (delete, etc.)
///
/// **Memory Management:** MeAttachmentViewerStreamManager handles automatic disposal.
class MeAttachmentViewerWithStream extends StatefulWidget {
  const MeAttachmentViewerWithStream({
    super.key,
    required this.attachmentInfoStream,
    this.taskTitle,
    this.currentIndex = 0,
    this.isSupportMedia = false,
    this.isSupportPreview = false,
    this.encryptionData,
    this.showOptions = true,
    this.allowDelete = true,
    this.allowRestore = false,
    this.onAttachmentsUpdated,
    required this.featureType,
    this.taskId,
    this.dueDate,
    this.actionId,
    this.fromImageTile = false,
    this.isCameraPreview = false,
    required this.ownerUid,
    this.isNotThroughActionScreen = false,
    this.isPublic = false,
  });

  final Stream<List<MeAttachmentInfo>> attachmentInfoStream;
  final int currentIndex;
  final MeString? taskTitle;
  final bool isSupportMedia;
  final bool isSupportPreview;
  final EncryptionData? encryptionData;
  final bool showOptions;
  final bool allowDelete;
  final bool allowRestore;
  final Function(List<MeAttachmentInfo>)? onAttachmentsUpdated;
  final AttachmentFeatureType featureType;
  final String? taskId;
  final DateTime? dueDate;
  final String? actionId;
  final bool fromImageTile;
  final bool isCameraPreview;
  final String ownerUid;
  final bool isNotThroughActionScreen;
  final bool isPublic;

  @override
  State<MeAttachmentViewerWithStream> createState() =>
      _MeAttachmentViewerWithStreamState();
}

class _MeAttachmentViewerWithStreamState
    extends State<MeAttachmentViewerWithStream> {
  static final _log = MeLogger.getLogger(LogTags.attachment);

  @override
  void dispose() {
    // Dispose the stream when the viewer is closed
    if (widget.taskId != null) {
      MeAttachmentViewerStreamManager.disposeStreamForTask(widget.taskId!);
      _log.d('Disposed stream for task: ${widget.taskId}');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<MeAttachmentInfo>>(
      stream: widget.attachmentInfoStream,
      builder: (context, snapshot) {
        if (snapshot.hasData) {
          final data = snapshot.data!;

          if (data.isNotEmpty) {
            return MeAttachmentViewer(
              attachmentInfoList: data,
              taskTitle: widget.taskTitle,
              currentIndex: widget.currentIndex,
              isSupportMedia: widget.isSupportMedia,
              isSupportPreview: widget.isSupportPreview,
              encryptionData: widget.encryptionData,
              showOptions: widget.showOptions,
              allowDelete: widget.allowDelete,
              allowRestore: widget.allowRestore,
              onAttachmentsUpdated: widget.onAttachmentsUpdated,
              featureType: widget.featureType,
              taskId: widget.taskId,
              dueDate: widget.dueDate,
              actionId: widget.actionId,
              fromImageTile: widget.fromImageTile,
              isCameraPreview: widget.isCameraPreview,
              ownerUid: widget.ownerUid,
              isNotThroughActionScreen: widget.isNotThroughActionScreen,
              isPublic: widget.isPublic,
            );
          }
        }

        // Show empty state while waiting for data
        return const SizedBox.shrink();
      },
    );
  }
}

/// Wrapper widget that automatically disposes stream when viewer closes
class AttachmentViewerWithStreamDisposal extends StatefulWidget {
  const AttachmentViewerWithStreamDisposal({
    super.key,
    required this.streamReproducibleId,
    required this.child,
  });

  final String streamReproducibleId;
  final Widget child;

  @override
  State<AttachmentViewerWithStreamDisposal> createState() =>
      _AttachmentViewerWithStreamDisposalState();
}

class _AttachmentViewerWithStreamDisposalState
    extends State<AttachmentViewerWithStreamDisposal> {
  @override
  void dispose() {
    // Dispose the stream when the viewer is closed
    MeAttachmentViewerStreamManager.disposeStreamForTask(
      widget.streamReproducibleId,
    );
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

/// Global stream manager for attachment viewers to survive widget reordering
class MeAttachmentViewerStreamManager {
  // Private constructor to prevent instantiation
  MeAttachmentViewerStreamManager._();

  static final Map<String, BehaviorSubject<List<MeAttachmentInfo>>> _streams =
      {};

  /// Get or create a stream for a specific task
  static BehaviorSubject<List<MeAttachmentInfo>> getStreamForTask(
    String streamReproducibleId, {
    List<MeAttachmentInfo>? initialData,
  }) {
    final existingStream = _streams[streamReproducibleId];
    if (existingStream != null && !existingStream.isClosed) {
      debugPrint(
        '[AttachmentStreamManager] Reusing existing stream for task: $streamReproducibleId',
      );
      return existingStream;
    } else {
      // Clean up any closed stream
      if (existingStream != null && existingStream.isClosed) {
        _streams.remove(streamReproducibleId);
      }

      debugPrint(
        '[AttachmentStreamManager] Creating new stream for task: $streamReproducibleId with initial data: ${initialData?.length ?? 0} attachments',
      );
      // ignore: close_sinks
      final newStream = BehaviorSubject<List<MeAttachmentInfo>>();
      _streams[streamReproducibleId] = newStream;

      // Seed the stream with initial data if provided
      if (initialData != null) {
        newStream.add(initialData);
        debugPrint(
          '[AttachmentStreamManager] Seeded new stream for task: $streamReproducibleId with ${initialData.length} attachments',
        );
      }
    }
    return _streams[streamReproducibleId]!;
  }

  /// Update stream with new data
  static void updateStreamForTask(
    String streamReproducibleId,
    List<MeAttachmentInfo> attachments,
  ) {
    // ignore: close_sinks
    final stream = _streams[streamReproducibleId];
    if (stream != null && !stream.isClosed) {
      stream.add(attachments);
      debugPrint(
        '[AttachmentStreamManager] Updated stream for task: $streamReproducibleId with ${attachments.length} attachments',
      );
    } else {
      debugPrint(
        '[AttachmentStreamManager] Cannot update closed/missing stream for task: $streamReproducibleId',
      );
    }
  }

  /// Dispose stream for a specific task (called when viewer closes)
  static void disposeStreamForTask(String streamReproducibleId) {
    final stream = _streams.remove(streamReproducibleId);
    if (stream != null && !stream.isClosed) {
      stream.close();
      debugPrint(
        '[AttachmentStreamManager] Disposed stream for task: $streamReproducibleId',
      );
    }
  }

  /// Check if stream exists for task
  static bool hasStreamForTask(String streamReproducibleId) {
    // ignore: close_sinks
    final stream = _streams[streamReproducibleId];
    return stream != null && !stream.isClosed;
  }

  /// Get current stream count (for debugging)
  static int get activeStreamCount => _streams.length;

  /// Clean up all streams (for emergency cleanup)
  static void disposeAllStreams() {
    debugPrint(
      '[AttachmentStreamManager] Disposing all streams (${_streams.length} total)',
    );
    for (final stream in _streams.values) {
      if (!stream.isClosed) {
        stream.close();
      }
    }
    _streams.clear();
  }
}
