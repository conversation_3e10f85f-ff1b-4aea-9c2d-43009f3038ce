import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/data/models/note.dart';
import 'package:mevolve/data/models/user/removed_doc_info.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/tasks_providers/common.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';
import 'package:mevolve/features/tasks_providers/resilient_stream_merger.dart';

part 'main_notes_state.dart';

class MainNotesCubit extends Cubit<MainNotesState> {
  MainNotesCubit(this._databaseRepository, this._appBloc)
      : super(const MainNotesStateInitial());

  final DatabaseRepository _databaseRepository;
  final AppBloc _appBloc;

  StreamSubscription? _noteSubscription;
  Map<String, Note> oldNotes = {};
  Map<String, Note> oldIncompatibleNotes = {};

  final Log log = MeLogger.getLogger(LogTags.notes);

  void initialize({required String uid}) {
    _fetchNote(uid: uid, limit: AppConstant.maxLimit);
  }

  Future<void> fetchAllAndUpdate() async {
    oldNotes = {};
    oldIncompatibleNotes = {};

    // Fetch all at once.
    final initialNotes = await _databaseRepository.getAllNotes();

    // Update state.
    updateState(
      notes: initialNotes,
      fullRefreshUpdate: true,
    );
  }

  late AppState oldAppBlocState;

  void _updateOldValuesUsedForComparison({
    dynamic value,
  }) {
    oldAppBlocState = value is AppState ? value : _appBloc.state;
  }

  void _fetchNote({
    required String uid,
    required int limit,
  }) async {
    _updateOldValuesUsedForComparison();

    fetchAllAndUpdate();

    await _noteSubscription?.cancel();

    _noteSubscription = ResilientStreamMerger.merge<dynamic>([
      _appBloc.stream.where(
        (event) =>
            event.removedDocsIDs.notes.length !=
            oldAppBlocState.removedDocsIDs.notes.length,
      ),
      _databaseRepository.notesUpdateStream(
        uid: uid,
      ),
    ]).listen(
      (value) {
        bool isDeletedDocsChanged = value is AppState &&
            value.removedDocsIDs.notes.length !=
                oldAppBlocState.removedDocsIDs.notes.length;
        bool isNotesChanged = value is List<Note>;
        if (isNotesChanged) {
          updateState(
            notes: value,
          );
        } else if (isDeletedDocsChanged) {
          updateState(
            notes: [],
          );
        }
      },
    );
  }

  void updateState({
    required List<Note> notes,
    bool fullRefreshUpdate = false,
  }) {
    // ----------------- Separate compatible and incompatible items early - start with existing collections -----------------
    Map<String, Note> incompatibleNotes = Map.from(oldIncompatibleNotes);
    final uid = _appBloc.state.userData?.uid;

    // Process notes: separate compatible from incompatible
    if (uid != null) {
      for (Note note in notes) {
        if (note.isDataVersionInCompatible) {
          incompatibleNotes[note.id] = note;
        } else {
          incompatibleNotes.remove(note.id);
        }
      }
      // Filter out incompatible notes from main processing
      notes.removeWhere(
        (note) => note.isDataVersionInCompatible,
      );
    }
    // ----------------- Separate compatible and incompatible items early - end with existing collections -----------------

    Map<String, Note> onlyNewTasks =
        notes.asMap().map((key, value) => MapEntry(value.id, value));
    (Map<UpdateType, Set<Note>>, Map<String, Note>) categorizedRes =
        categorizeNewTasks(
      oldNotes,
      onlyNewTasks,
      oldAppBlocState.removedDocsIDs.notes.toSet(),
    );

    Map<UpdateType, Set<Note>> categorizedTasks = categorizedRes.$1;
    Map<String, Note> taskIdsToTasks = categorizedRes.$2;

    // Check if incompatible notes have changed
    bool isIncompatibleNotesChanged = !const DeepCollectionEquality().equals(
      incompatibleNotes,
      oldIncompatibleNotes,
    );

    if (isIncompatibleNotesChanged) {
      //Remove all incompatible notes from the main collection
      taskIdsToTasks.removeWhere(
        (key, value) => incompatibleNotes.containsKey(key),
      );
      fullRefreshUpdate = true;
    }

    // isChanged should be true if we new items are there or if items were empty and now also empty.
    // We follow same scheme in all other top level cubits as lower level cubits won't emit
    // until these top level cubits are in initial state and will indicate loading.
    // So, if the cubit never emits due to no items, then those loader will show infinitely.
    // Also include incompatible notes changes in the condition.
    bool isNotesChanged = categorizedTasks.isNotEmpty ||
        isIncompatibleNotesChanged ||
        (oldNotes.isEmpty && state is MainNotesStateInitial);

    // Only update if notes are changed.
    if (isNotesChanged) {
      Map<DateTime, List<Note>> dateBased = taskIdsToTasks.values
          .map((note) {
            DateTime date = note.noteUpdatedAt?.onlyDate() ??
                note.localUpdatedAt.onlyDate();
            return date;
          })
          .toSet()
          .toList()
          .asMap()
          .map((key, value) {
            final note = taskIdsToTasks.values.where((note) {
              DateTime date = note.noteUpdatedAt?.onlyDate() ??
                  note.localUpdatedAt.onlyDate();
              return date == value;
            }).toList();
            return MapEntry(value, note);
          });
      emit(
        MainNotesStateLoaded(
          incompatibleTaskIdsToTasks: incompatibleNotes,
          taskIdsToTasks: taskIdsToTasks,
          dateToTasks: dateBased,
          updatedTasks: fullRefreshUpdate
              ? {UpdateType.fullRefreshUpdate: {}}
              : categorizedTasks,
        ),
      );
      oldNotes = Map.from(taskIdsToTasks);
      oldIncompatibleNotes = incompatibleNotes;
    }
  }

  @override
  Future<void> close() async {
    await _noteSubscription?.cancel();
    return super.close();
  }
}

/// Categorizes new tasks into different types of updates, unscheduled tasks, and
/// new all tasks map.
(Map<UpdateType, Set<Note>>, Map<String, Note>) categorizeNewTasks(
  Map<String, Note> allPastTasks,
  Map<String, Note> onlyNewTasks,
  Set<IdInfo>? removedDocsIDsInfo,
) {
  Map<UpdateType, Set<Note>> categorizedTasks = {};
  Map<String, Note> allTasks = Map.from(allPastTasks);

  void updateDataForPermaDelete(Note task) {
    if (allTasks.containsKey(task.id)) {
      categorizedTasks.putIfAbsent(UpdateType.permaDelete, () => {}).add(task);
      allTasks.remove(task.id);
    }
  }

  // Check for removed, updated, and inserted tasks
  for (var newTaskID in onlyNewTasks.keys) {
    Note newTask = onlyNewTasks[newTaskID]!;

    if (newTask.permaDeletedAt != null ||
        DocInfo.canConsiderDocForRemove(
              newTask,
              removedDocsIDsInfo?.toList(),
            ) ==
            true) {
      updateDataForPermaDelete(newTask);
      continue;
    }

    allTasks[newTaskID] = newTask;

    if (!allPastTasks.containsKey(newTaskID)) {
      // Inserted task
      categorizedTasks.putIfAbsent(UpdateType.insert, () => {}).add(newTask);
    } else {
      // Updated task
      Note oldTask = allPastTasks[newTaskID]!;
      if (newTask.deletedAt != oldTask.deletedAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingBasicFiltering, () => {})
            .add(newTask);
      } else {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingSpecificFiltering, () => {})
            .add(newTask);
      }
    }
  }

  // Check for removed tasks from removedDocsIDs.
  for (var removedDocIDInfo in removedDocsIDsInfo ?? {}) {
    Note? removedTask = allTasks[removedDocIDInfo.docId];
    if (removedTask != null &&
        DocInfo.canConsiderDocForRemoveFromInfo(
          removedTask,
          removedDocIDInfo,
        )) {
      updateDataForPermaDelete(removedTask);
    }
  }

  return (categorizedTasks, allTasks);
}
