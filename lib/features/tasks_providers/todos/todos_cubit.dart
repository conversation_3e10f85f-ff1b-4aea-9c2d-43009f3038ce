import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/data/models/todo.dart';
import 'package:mevolve/data/models/user/removed_doc_info.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/data/repositories/home_widget_and_deep_link/home_widget.utility.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_cubit.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_state.dart';
import 'package:mevolve/features/tasks_providers/common.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';
import 'package:mevolve/features/tasks_providers/resilient_stream_merger.dart';

part 'todos_state.dart';

class TodosCubit extends Cubit<TodosState> {
  TodosCubit(
    this._databaseRepository,
    this._appBloc,
    this._userResourcesCubit,
  ) : super(const TodosStateInitial());

  final DatabaseRepository _databaseRepository;
  final AppBloc _appBloc;
  final UserResourcesCubit _userResourcesCubit;

  StreamSubscription? _todoSubscription;
  Map<String, Todo> oldTodos = {};
  Set<String> oldUnscheduledTodos = {};
  Map<String, Todo> oldIncompatibleTodos = {};

  final Log log = MeLogger.getLogger(LogTags.todos);

  void initialize({required String uid}) {
    _fetchTodo(uid: uid, limit: AppConstant.maxLimit);
  }

  Future<void> fetchAllAndUpdate() async {
    if (_appBloc.state.userData == null) return;

    // Check if userResources is loaded or not. If not then do nothing.
    // As we will have to do fetch all anyways again later when it does load.
    if (_userResourcesCubit.state is UserResourcesStateInitial) return;

    oldTodos = {};
    oldUnscheduledTodos = {};
    oldIncompatibleTodos = {};

    // Fetch all at once.
    final initialTodos = await _databaseRepository.getAllTodos();

    // Update state.
    updateState(
      todos: initialTodos,
      fullRefreshUpdate: true,
    );
  }

  late AppState oldAppBlocState;
  late UserResourcesState oldUserResourcesState;

  void _updateOldValuesUsedForComparison({
    dynamic value,
  }) {
    oldAppBlocState = value is AppState ? value : _appBloc.state;
    oldUserResourcesState =
        value is UserResourcesState ? value : _userResourcesCubit.state;
  }

  void _fetchTodo({
    required String uid,
    required int limit,
  }) async {
    _updateOldValuesUsedForComparison();

    fetchAllAndUpdate();

    await _todoSubscription?.cancel();

    _todoSubscription = ResilientStreamMerger.merge<dynamic>([
      // Listen for timezone changes and discard objects cached values as they are
      // no longer valid and the repeats are also not valid.
      _appBloc.stream.where(
        (event) =>
            event.appDateTimeState.deviceTimezone !=
            oldAppBlocState.appDateTimeState.deviceTimezone,
      ),
      _appBloc.stream.where(
        (event) =>
            event.removedDocsIDs.todos.length !=
            oldAppBlocState.removedDocsIDs.todos.length,
      ),
      _databaseRepository.todosUpdateStream(
        uid: uid,
      ),
      _userResourcesCubit.stream.where(
        (event) =>
            event.userResources?.shared.sharedTodos.length !=
            oldUserResourcesState.userResources?.shared.sharedTodos.length,
      ),
    ]).listen(
      (value) {
        if (_appBloc.state.userData == null) return;
        bool isDeviceTimezoneChanged = value is AppState &&
            (value.appDateTimeState.deviceTimezone !=
                oldAppBlocState.appDateTimeState.deviceTimezone);
        bool isDeletedDocsChanged = value is AppState &&
            value.removedDocsIDs.todos.length !=
                oldAppBlocState.removedDocsIDs.todos.length;
        bool isSharedTodosChanged = value is UserResourcesState &&
            value.userResources?.shared.sharedTodos.length !=
                oldUserResourcesState.userResources?.shared.sharedTodos.length;
        bool isAllTasksRepeatsReComputeRequired = isDeviceTimezoneChanged;
        bool isTodosChanged = value is List<Todo>;
        _updateOldValuesUsedForComparison(
          value: value,
        );
        if (isTodosChanged) {
          updateState(
            todos: value,
          );
        } else if (isSharedTodosChanged) {
          fetchAllAndUpdate();
        } else if (isAllTasksRepeatsReComputeRequired) {
          fetchAllAndUpdate();
        } else if (isDeletedDocsChanged) {
          updateState(
            todos: [],
          );
        }
      },
    );
  }

  void updateState({
    required List<Todo> todos,
    bool fullRefreshUpdate = false,
  }) {
    // we make sure that if it is sharedTodos then the userResources
    // should have the ids of those todos
    List<String> sharedTodos =
        _userResourcesCubit.state.userResources?.shared.sharedTodos ?? [];
    todos.removeWhere(
      (todo) =>
          todo.uid != _appBloc.state.userData!.uid &&
          !sharedTodos.contains(
            todo.id,
          ),
    );

    // ----------------- Separate compatible and incompatible items early - start with existing collections -----------------
    Map<String, Todo> incompatibleTodos = Map.from(oldIncompatibleTodos);

    // Process todos: separate compatible from incompatible
    for (Todo todo in todos) {
      if (todo.isDataVersionInCompatible) {
        incompatibleTodos[todo.id] = todo;
      } else {
        incompatibleTodos.remove(todo.id);
      }
    }
    // Filter out incompatible todos from main processing
    todos.removeWhere(
      (todo) => todo.isDataVersionInCompatible,
    );

    // ----------------- Separate compatible and incompatible items early - end with existing collections -----------------
    Map<String, Todo> onlyNewTasks =
        todos.asMap().map((key, value) => MapEntry(value.id, value));
    (
      Map<UpdateType, Set<Todo>>,
      Set<String>,
      Map<String, Todo>
    ) categorizedRes = categorizeNewTasks(
      oldTodos,
      oldUnscheduledTodos,
      onlyNewTasks,
      oldAppBlocState.removedDocsIDs.todos.toSet(),
    );

    Map<UpdateType, Set<Todo>> categorizedTasks = categorizedRes.$1;
    Set<String> unscheduledTodos = categorizedRes.$2;
    Map<String, Todo> taskIdsToTasks = categorizedRes.$3;

    // Check if incompatible todos have changed
    bool isIncompatibleTodosChanged = !const DeepCollectionEquality().equals(
      incompatibleTodos,
      oldIncompatibleTodos,
    );

    if (isIncompatibleTodosChanged) {
      //Remove all incompatible todos from the main collection
      taskIdsToTasks.removeWhere(
        (key, value) => incompatibleTodos.containsKey(key),
      );
      fullRefreshUpdate = true;
    }

    // isChanged should be true if we new items are there or if items were empty and now also empty.
    // We follow same scheme in all other top level cubits as lower level cubits won't emit
    // until these top level cubits are in initial state and will indicate loading.
    // So, if the cubit never emits due to no items, then those loader will show infinitely.
    // Also include incompatible todos changes in the condition.
    bool isTodosChanged = categorizedTasks.isNotEmpty ||
        isIncompatibleTodosChanged ||
        (oldTodos.isEmpty && state is TodosStateInitial);
    if (fullRefreshUpdate) {
      isTodosChanged = true;
    }

    // Only update if todos are changed.
    if (isTodosChanged) {
      final homeWidgetUtility = HomeWidgetUtility(
        databaseRepository: _databaseRepository,
      );
      homeWidgetUtility.updateHomeWidget();

      emit(
        TodosStateLoaded(
          incompatibleTaskIdsToTasks: incompatibleTodos,
          taskIdsToTasks: taskIdsToTasks,
          unscheduledTodos: unscheduledTodos,
          updatedTodos: fullRefreshUpdate
              ? {UpdateType.fullRefreshUpdate: {}}
              : categorizedTasks,
        ),
      );
      oldTodos = Map.from(taskIdsToTasks);
      oldUnscheduledTodos = Set.from(unscheduledTodos);
      oldIncompatibleTodos = Map.from(incompatibleTodos);
    }
  }

  @override
  Future<void> close() async {
    await _todoSubscription?.cancel();
    return super.close();
  }
}

/// Categorizes new tasks into different types of updates, unscheduled tasks, and
/// new all tasks map.
(Map<UpdateType, Set<Todo>>, Set<String>, Map<String, Todo>) categorizeNewTasks(
  Map<String, Todo> allPastTasks,
  Set<String> allPastUnscheduledTodos,
  Map<String, Todo> onlyNewTasks,
  Set<IdInfo>? removedDocsIDsInfo,
) {
  Map<UpdateType, Set<Todo>> categorizedTasks = {};
  Set<String> unscheduledTodos = Set.from(allPastUnscheduledTodos);
  Map<String, Todo> allTasks = Map.from(allPastTasks);

  void updateDataForPermaDelete(Todo task) {
    if (allTasks.containsKey(task.id)) {
      categorizedTasks.putIfAbsent(UpdateType.permaDelete, () => {}).add(task);
      allTasks.remove(task.id);
      unscheduledTodos.remove(task.id);
    }
  }

  // Check for removed, updated, and inserted tasks
  for (var newTaskID in onlyNewTasks.keys) {
    Todo newTask = onlyNewTasks[newTaskID]!;

    if (newTask.permaDeletedAt != null ||
        DocInfo.canConsiderDocForRemove(
              newTask,
              removedDocsIDsInfo?.toList(),
            ) ==
            true) {
      updateDataForPermaDelete(newTask);
      continue;
    }

    allTasks[newTaskID] = newTask;

    if (newTask.startAt == null) {
      unscheduledTodos.add(newTask.id);
    } else {
      // Remove it from unscheduled list as it may have been there before.
      unscheduledTodos.remove(newTask.id);
    }

    if (!allPastTasks.containsKey(newTaskID)) {
      // Inserted task
      categorizedTasks.putIfAbsent(UpdateType.insert, () => {}).add(newTask);
    } else {
      // Updated task
      Todo oldTask = allPastTasks[newTaskID]!;
      if (!(const DeepCollectionEquality().equals(
        newTask.repeat,
        oldTask.repeat,
      ))) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingRepeats, () => {})
            .add(newTask);
      } else if (newTask.startAt != oldTask.startAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingRepeats, () => {})
            .add(newTask);
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingBasicFiltering, () => {})
            .add(newTask);
      } else if (newTask.endAt != oldTask.endAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingRepeats, () => {})
            .add(newTask);
      } else if (newTask.deletedAt != oldTask.deletedAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingBasicFiltering, () => {})
            .add(newTask);
      } else {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingSpecificFiltering, () => {})
            .add(newTask);
      }
    }
  }

  // Check for removed tasks from removedDocsIDs.
  for (IdInfo removedDocIDInfo in removedDocsIDsInfo ?? {}) {
    Todo? removedTask = allTasks[removedDocIDInfo.docId];
    if (removedTask != null &&
        DocInfo.canConsiderDocForRemoveFromInfo(
          removedTask,
          removedDocIDInfo,
        )) {
      updateDataForPermaDelete(removedTask);
    }
  }

  return (categorizedTasks, unscheduledTodos, allTasks);
}
