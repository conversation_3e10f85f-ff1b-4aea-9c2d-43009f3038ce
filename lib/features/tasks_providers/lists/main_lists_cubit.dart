import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/user/removed_doc_info.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/tasks_providers/common.dart';
import 'package:mevolve/utilities/logger/log.dart';
import 'package:mevolve/utilities/logger/log_tags.dart';
import 'package:mevolve/utilities/logger/me_logger.dart';
import 'package:mevolve/features/tasks_providers/resilient_stream_merger.dart';

part 'main_list_state.dart';

class MainListsCubit extends Cubit<MainListsState> {
  MainListsCubit(this._databaseRepository, this._appBloc)
      : super(const MainListsStateInitial());

  final DatabaseRepository _databaseRepository;
  final AppBloc _appBloc;

  Map<String, ListData> oldLists = {};
  Map<String, ListData> oldIncompatibleLists = {};
  StreamSubscription? _listsSubscription;

  final Log log = MeLogger.getLogger(LogTags.lists);

  void initialize({required String uid}) {
    _fetchLists(uid: uid, limit: AppConstant.maxLimit);
  }

  Future<void> fetchAllAndUpdate() async {
    oldLists = {};
    oldIncompatibleLists = {};

    // Fetch all at once.
    final initialLists = await _databaseRepository.getAllLists();

    // Update state.
    updateState(
      lists: initialLists,
      fullRefreshUpdate: true,
    );
  }

  late AppState oldAppBlocState;

  void _updateOldValuesUsedForComparison({
    dynamic value,
  }) {
    oldAppBlocState = value is AppState ? value : _appBloc.state;
  }

  void _fetchLists({
    required String uid,
    required int limit,
  }) async {
    _updateOldValuesUsedForComparison();

    fetchAllAndUpdate();

    await _listsSubscription?.cancel();

    _listsSubscription = ResilientStreamMerger.merge<dynamic>([
      _appBloc.stream.where(
        (event) =>
            event.removedDocsIDs.lists.length !=
            oldAppBlocState.removedDocsIDs.lists.length,
      ),
      _databaseRepository.listsUpdateStream(
        uid: uid,
      ),
    ]).listen(
      (value) {
        bool isDeletedDocsChanged = value is AppState &&
            value.removedDocsIDs.lists.length !=
                oldAppBlocState.removedDocsIDs.lists.length;
        bool isListsChanged = value is List<ListData>;
        if (isListsChanged) {
          updateState(
            lists: value,
          );
        } else if (isDeletedDocsChanged) {
          updateState(
            lists: [],
          );
        }
      },
    );
  }

  void updateState({
    required List<ListData> lists,
    bool fullRefreshUpdate = false,
  }) {
    // ----------------- Separate compatible and incompatible items early - start with existing collections -----------------
    Map<String, ListData> incompatibleLists = Map.from(oldIncompatibleLists);
    final uid = _appBloc.state.userData?.uid;

    // Process lists: separate compatible from incompatible
    if (uid != null) {
      for (ListData list in lists) {
        if (list.isDataVersionInCompatible) {
          incompatibleLists[list.id] = list;
        } else {
          incompatibleLists.remove(list.id);
        }
      }
      // Filter out incompatible lists from main processing
      lists.removeWhere(
        (list) => list.isDataVersionInCompatible,
      );
    }
    // ----------------- Separate compatible and incompatible items early - end with existing collections -----------------

    Map<String, ListData> onlyNewTasks =
        lists.asMap().map((key, value) => MapEntry(value.id, value));
    (Map<UpdateType, Set<ListData>>, Map<String, ListData>) categorizedRes =
        categorizeNewTasks(
      oldLists,
      onlyNewTasks,
      oldAppBlocState.removedDocsIDs.lists.toSet(),
    );

    Map<UpdateType, Set<ListData>> categorizedTasks = categorizedRes.$1;
    Map<String, ListData> taskIdsToTasks = categorizedRes.$2;

    // Check if incompatible lists have changed
    bool isIncompatibleListsChanged = !const DeepCollectionEquality().equals(
      incompatibleLists,
      oldIncompatibleLists,
    );

    if (isIncompatibleListsChanged) {
      //Remove all incompatible lists from the main collection
      taskIdsToTasks.removeWhere(
        (key, value) => incompatibleLists.containsKey(key),
      );
      fullRefreshUpdate = true;
    }

    // isChanged should be true if we new items are there or if items were empty and now also empty.
    // We follow same scheme in all other top level cubits as lower level cubits won't emit
    // until these top level cubits are in initial state and will indicate loading.
    // So, if the cubit never emits due to no items, then those loader will show infinitely.
    // Also include incompatible lists changes in the condition.
    bool isListsChanged = categorizedTasks.isNotEmpty ||
        isIncompatibleListsChanged ||
        (oldLists.isEmpty && state is MainListsStateInitial);

    // Only update if lists are changed.
    if (isListsChanged) {
      Map<DateTime, List<ListData>> dateBased = taskIdsToTasks.values
          .map((list) => list.localUpdatedAt.onlyDate())
          .toSet()
          .toList()
          .asMap()
          .map((key, value) {
        final list = taskIdsToTasks.values
            .where((list) => list.localUpdatedAt.onlyDate() == value)
            .toList();
        return MapEntry(value, list);
      });
      emit(
        MainListsStateLoaded(
          incompatibleTaskIdsToTasks: incompatibleLists,
          taskIdsToTasks: taskIdsToTasks,
          dateToTasks: dateBased,
          updatedTasks: fullRefreshUpdate
              ? {UpdateType.fullRefreshUpdate: {}}
              : categorizedTasks,
        ),
      );
      oldLists = Map.from(taskIdsToTasks);
      oldIncompatibleLists = incompatibleLists;
    }
  }

  @override
  Future<void> close() async {
    await _listsSubscription?.cancel();
    return super.close();
  }
}

/// Categorizes new tasks into different types of updates, unscheduled tasks, and
/// new all tasks map.
(Map<UpdateType, Set<ListData>>, Map<String, ListData>) categorizeNewTasks(
  Map<String, ListData> allPastTasks,
  Map<String, ListData> onlyNewTasks,
  Set<IdInfo>? removedDocsIDsInfo,
) {
  Map<UpdateType, Set<ListData>> categorizedTasks = {};
  Map<String, ListData> allTasks = Map.from(allPastTasks);
  void updateDataForPermaDelete(ListData task) {
    if (allTasks.containsKey(task.id)) {
      categorizedTasks.putIfAbsent(UpdateType.permaDelete, () => {}).add(task);
      allTasks.remove(task.id);
    }
  }

  // Check for removed, updated, and inserted tasks
  for (var newTaskID in onlyNewTasks.keys) {
    ListData newTask = onlyNewTasks[newTaskID]!;

    if (newTask.permaDeletedAt != null ||
        DocInfo.canConsiderDocForRemove(
              newTask,
              removedDocsIDsInfo?.toList(),
            ) ==
            true) {
      updateDataForPermaDelete(newTask);
      continue;
    }

    allTasks[newTaskID] = newTask;

    if (!allPastTasks.containsKey(newTaskID)) {
      // Inserted task
      categorizedTasks.putIfAbsent(UpdateType.insert, () => {}).add(newTask);
    } else {
      // Updated task
      ListData oldTask = allPastTasks[newTaskID]!;
      if (newTask.deletedAt != oldTask.deletedAt) {
        // Deletion status changes affect basic filtering (sort order)
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingBasicFiltering, () => {})
            .add(newTask);
      } else {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingSpecificFiltering, () => {})
            .add(newTask);
      }
    }
  }

  // Check for removed tasks from removedDocsIDs.
  for (var removedDocIDInfo in removedDocsIDsInfo ?? {}) {
    ListData? removedTask = allTasks[removedDocIDInfo.docId];
    if (removedTask != null &&
        DocInfo.canConsiderDocForRemoveFromInfo(
          removedTask,
          removedDocIDInfo,
        )) {
      updateDataForPermaDelete(removedTask);
    }
  }

  return (categorizedTasks, allTasks);
}
