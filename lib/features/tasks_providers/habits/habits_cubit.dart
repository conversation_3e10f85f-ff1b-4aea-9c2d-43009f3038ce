import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/data/models/habit/habit_action.dart';
import 'package:mevolve/data/models/habit/habit_setup.dart';
import 'package:mevolve/data/models/user/removed_doc_info.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/data/repositories/home_widget_and_deep_link/home_widget.utility.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_cubit.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_state.dart';
import 'package:mevolve/features/hamburger/subscription/effective_subscription_cubit/effective_subscription_cubit.dart';
import 'package:mevolve/features/tasks_providers/common.dart';
import 'package:mevolve/features/tasks_providers/resilient_stream_merger.dart';

part 'habits_state.dart';

class HabitsCubit extends Cubit<HabitsState> {
  HabitsCubit(
    this._databaseRepository,
    this._appBloc,
    this._userResourcesCubit,
    this._effectiveSubscriptionCubit,
  ) : super(const HabitsStateInitial());

  final DatabaseRepository _databaseRepository;
  final AppBloc _appBloc;
  final UserResourcesCubit _userResourcesCubit;
  final EffectiveSubscriptionCubit _effectiveSubscriptionCubit;

  StreamSubscription? _habitSetupSubscription;
  StreamSubscription? _habitActionSubscription;

  Map<String, HabitSetup> oldSetupsIDToASetup = {};
  Map<String, HabitAction> oldActionsIDToActions = {};
  Map<String, Map<DateTime, String?>> oldSetupIdsToActionIds = {};
  Map<String, HabitSetup> oldIncompatibleSetupsIDToASetup = {};
  Map<String, HabitAction> oldIncompatibleActionsIDToActions = {};

  void initialize({required String uid}) {
    _fetchHabits(uid: uid, limit: AppConstant.maxLimit);
  }

  Future<void> fetchAllHabitsAndUpdate([bool fetchActionsAlso = true]) async {
    if (_appBloc.state.userData == null) return;

    // Check if userResources is loaded or not. If not then do nothing.
    // As we will have to do fetch all anyways again later when it does load.
    if (_userResourcesCubit.state is UserResourcesStateInitial) return;

    final mayCreateButCanSeeProFeatures =
        _effectiveSubscriptionCubit.state.eMayCreateButCanSeeProFeatures;

    // Fetch all at once.
    oldSetupsIDToASetup = {};
    oldIncompatibleSetupsIDToASetup = {};
    List<HabitSetup> initialHabitSetups =
        await _databaseRepository.getAllHabitSetups();
    if (!mayCreateButCanSeeProFeatures) {
      initialHabitSetups = initialHabitSetups
          .where(
            (setup) => setup.uid != _appBloc.state.userData?.uid,
          )
          .toList();
    }

    List<HabitAction>? initialHabitActions;
    if (fetchActionsAlso) {
      oldActionsIDToActions = {};
      oldIncompatibleActionsIDToActions = {};
      oldSetupIdsToActionIds = {};
      initialHabitActions = await _databaseRepository.getAllHabitActions();
      if (!mayCreateButCanSeeProFeatures) {
        initialHabitActions = initialHabitActions
            .where(
              (setup) => setup.uid != _appBloc.state.userData?.uid,
            )
            .toList();
      }
    }

    updateState(
      habitSetups: initialHabitSetups,
      habitActions: initialHabitActions,
      fullRefreshUpdate: true,
    );
  }

  late AppState oldAppBlocState;
  late EffectiveSubscriptionState oldEffectiveSubscriptionState;
  late UserResourcesState oldUserResourcesState;

  void _updateOldValuesUsedForComparison({
    dynamic value,
  }) {
    oldAppBlocState = value is AppState ? value : _appBloc.state;
    oldEffectiveSubscriptionState = value is EffectiveSubscriptionState
        ? value
        : _effectiveSubscriptionCubit.state;
    oldUserResourcesState =
        value is UserResourcesState ? value : _userResourcesCubit.state;
  }

  void _fetchHabits({
    required String uid,
    required int limit,
  }) async {
    _updateOldValuesUsedForComparison();

    fetchAllHabitsAndUpdate();

    await _habitSetupSubscription?.cancel();
    await _habitActionSubscription?.cancel();

    // Start listening for setup updates.
    // Using ResilientStreamMerger instead of Rx.merge to prevent completion
    // when individual streams (like EffectiveSubscriptionCubit) complete
    _habitSetupSubscription = ResilientStreamMerger.merge<dynamic>([
      // Listen for timezone changes and discard objects cached values as they are
      // no longer valid and the repeats are also not valid.
      _appBloc.stream.where(
        (event) =>
            event.appDateTimeState.deviceTimezone !=
            oldAppBlocState.appDateTimeState.deviceTimezone,
      ),
      _effectiveSubscriptionCubit.stream.where(
        (event) =>
            event.eMayCreateButCanSeeProFeatures !=
            oldEffectiveSubscriptionState.eMayCreateButCanSeeProFeatures,
      ),
      // Todo: Can we make docs deletion without listening here for doc id? Like
      // by updating the docs permaDeletedAt field before deleting them from DB so that we don't have
      // to listen separately here and the flow will work naturally?
      _appBloc.stream.where(
        (event) =>
            event.removedDocsIDs.habitSetups.length !=
                oldAppBlocState.removedDocsIDs.habitSetups.length ||
            event.removedDocsIDs.habitActions.length !=
                oldAppBlocState.removedDocsIDs.habitActions.length,
      ),
      _databaseRepository.habitSetupsUpdateStream(
        uid: uid,
      ),
      _userResourcesCubit.stream.where(
        (event) =>
            event.userResources?.shared.sharedHabitSetups.length !=
            oldUserResourcesState
                .userResources?.shared.sharedHabitSetups.length,
      ),
    ]).listen(
      (value) {
        if (_appBloc.state.userData == null) return;
        bool isDeviceTimezoneChanged = value is AppState &&
            (value.appDateTimeState.deviceTimezone !=
                oldAppBlocState.appDateTimeState.deviceTimezone);
        bool isDeletedDocsChanged = value is AppState &&
            (value.removedDocsIDs.habitSetups.length !=
                    oldAppBlocState.removedDocsIDs.habitSetups.length ||
                value.removedDocsIDs.habitActions.length !=
                    oldAppBlocState.removedDocsIDs.habitActions.length);
        bool isAfterGracePeriodStateChanged =
            value is EffectiveSubscriptionState;
        bool isSharedSetupsChanged = value is UserResourcesState &&
            value.userResources?.shared.sharedHabitSetups.length !=
                oldUserResourcesState
                    .userResources?.shared.sharedHabitSetups.length;
        bool isAllTasksRepeatsReComputeRequired = isDeviceTimezoneChanged ||
            isSharedSetupsChanged ||
            isAfterGracePeriodStateChanged;
        bool isHabitSetupChanged =
            !isAllTasksRepeatsReComputeRequired && value is List<HabitSetup>;

        _updateOldValuesUsedForComparison(
          value: value,
        );
        if (isHabitSetupChanged) {
          updateState(
            habitSetups: value,
          );
        } else if (isAllTasksRepeatsReComputeRequired) {
          fetchAllHabitsAndUpdate();
        } else if (isDeletedDocsChanged) {
          updateState(
            habitSetups: [],
            habitActions: [],
          );
        }
      },
    );

    // Start listening for setup actions updates.
    _habitActionSubscription = _databaseRepository
        .habitActionsUpdateStream(
      uid: uid,
    )
        .listen(
      (habitActions) {
        if (_appBloc.state.userData == null) return;
        updateState(habitActions: habitActions);
      },
      onDone: () {
        if (isClosed) return;
        // Restart listening if listening is done.
        initialize(uid: uid);
      },
    );
  }

  Future<void> updateState({
    List<HabitSetup>? habitSetups,
    List<HabitAction>? habitActions,
    bool fullRefreshUpdate = false,
  }) async {
    // we make sure that if it is sharedHabits then the userResources
    // should have the ids of those setups
    List<String> sharedHabitSetups =
        _userResourcesCubit.state.userResources?.shared.sharedHabitSetups ?? [];
    habitSetups?.removeWhere(
      (setup) =>
          setup.uid != _appBloc.state.userData!.uid &&
          !sharedHabitSetups.contains(
            setup.id,
          ),
    );
    habitActions?.removeWhere(
      (action) =>
          action.uid != _appBloc.state.userData!.uid &&
          !sharedHabitSetups.contains(
            action.setupId,
          ),
    );

    // ----------------- Separate compatible and incompatible items early - start with existing collections -----------------
    Map<String, HabitSetup> incompatibleSetupsIDToASetup =
        Map.from(oldIncompatibleSetupsIDToASetup);
    Map<String, HabitAction> incompatibleActionsIDToActions =
        Map.from(oldIncompatibleActionsIDToActions);

    // Process setups: separate compatible from incompatible
    if (habitSetups != null) {
      for (HabitSetup setup in habitSetups) {
        if (setup.isDataVersionInCompatible) {
          incompatibleSetupsIDToASetup[setup.id] = setup;
        } else {
          incompatibleSetupsIDToASetup.remove(setup.id);
        }
      }
      // Filter out incompatible setups from main processing
      habitSetups.removeWhere(
        (setup) => setup.isDataVersionInCompatible,
      );
    }

    // Process actions: separate compatible from incompatible
    if (habitActions != null) {
      for (HabitAction action in habitActions) {
        if (action.isDataVersionInCompatible) {
          incompatibleActionsIDToActions[action.id] = action;
        } else {
          incompatibleActionsIDToActions.remove(action.id);
        }
      }
      // Filter out incompatible actions from main processing
      habitActions.removeWhere(
        (action) => action.isDataVersionInCompatible,
      );
    }
    // ----------------- Separate compatible and incompatible items early - end with existing collections -----------------

    Map<String, HabitSetup>? onlyNewSetups =
        habitSetups?.asMap().map((key, value) => MapEntry(value.id, value));

    List<HabitAction>? preparedHabitActions = habitActions;

    if (habitSetups != null &&
        habitSetups.isNotEmpty &&
        preparedHabitActions == null) {
      // For safety always fetch habit actions if a setup is provided as it is possible
      // it's actions reached here before setup reached and they got discard due to absence
      // of setup which is valid in some cases.
      // This case is most reproducible on cloud backup importing actions before setup
      // so actions et listened first without setups and remain discarded inside local db until
      // a full get all of tasks providers is done.
      preparedHabitActions = (await Future.wait<List<HabitAction>>(
        habitSetups.map(
          (setup) async =>
              (await _databaseRepository
                  .getHabitActionsByHabitSetupId(setup.id)) ??
              [],
        ),
      ))
          .expand<HabitAction>((actions) => actions)
          .toList();
    }

    Map<String, HabitAction>? onlyNewActions = preparedHabitActions
        ?.asMap()
        .map((key, value) => MapEntry(value.id, value));

    Set<IdInfo>? removedSetupsIDs =
        oldAppBlocState.removedDocsIDs.habitSetups.toSet();
    Set<IdInfo>? removedSetupsActionsIDsInfo =
        oldAppBlocState.removedDocsIDs.habitActions.toSet();

    bool isHabitSetupsChanged = false;
    Map<UpdateType, Set<HabitSetup>> categorizedTasks = {};
    Map<String, HabitSetup> setupsIDToASetup = Map.from(oldSetupsIDToASetup);
    Map<String, Map<DateTime, String>> setupIdsToActionIds =
        Map.from(oldSetupIdsToActionIds);
    if (onlyNewSetups != null) {
      (
        Map<UpdateType, Set<HabitSetup>>,
        Map<String, HabitSetup>
      ) categorizedRes = categorizeNewTasks(
        oldSetupsIDToASetup,
        onlyNewSetups,
        removedSetupsIDs,
      );

      categorizedTasks = categorizedRes.$1;
      setupsIDToASetup = categorizedRes.$2;

      // isChanged should be true if we new items are there or if items were empty and now also empty.
      // We follow same scheme in all other top level cubits as lower level cubits won't emit
      // until these top level cubits are in initial state and will indicate loading.
      // So, if the cubit never emits due to no items, then those loader will show infinitely.
      isHabitSetupsChanged = categorizedTasks.isNotEmpty ||
          (oldSetupsIDToASetup.isEmpty && state is HabitsStateInitial);

      setupIdsToActionIds = {};
      for (String habitSetupId in setupsIDToASetup.keys) {
        setupIdsToActionIds[habitSetupId] = {};
      }
    }

    bool isHabitActionsChanged = false;
    isHabitActionsChanged = onlyNewActions != null;

    Map<String, HabitAction> actionsIDToActions;
    if (isHabitActionsChanged) {
      // Iterate all removedSetupsActionsIDs and remove them from the actions and add there
      // task in categorizedTasks as UpdateType.updateAffectingSpecificFiltering.
      for (IdInfo habitActionIdInfo in removedSetupsActionsIDsInfo) {
        HabitAction? action = oldActionsIDToActions[habitActionIdInfo.docId] ??
            onlyNewActions[habitActionIdInfo.docId];
        HabitAction? actionForRemove = action == null
            ? null
            : DocInfo.canConsiderDocForRemoveFromInfo(
                action,
                habitActionIdInfo,
              )
                ? action
                : null;
        if (actionForRemove != null) {
          HabitSetup? taskSetup = setupsIDToASetup[actionForRemove.setupId];
          // Remove its id link to object.
          oldActionsIDToActions.remove(actionForRemove.id);
          // Remove them from the entries.
          setupIdsToActionIds[actionForRemove.setupId]
              ?.remove(actionForRemove.tmzSafeDueAt);
          // Remove them from new actions.
          onlyNewActions.remove(actionForRemove.id);
          // Send specific filter update for this.
          if (taskSetup != null) {
            categorizedTasks
                .putIfAbsent(
                  UpdateType.updateAffectingSpecificFiltering,
                  () => {},
                )
                .add(taskSetup);
          }
        }
      }

      // Todo: Create a separate function for this logic. Also look(maybe not good to do that)
      // if storing invalid entries from here in a map for use in app instead of relying on
      // entries cubits state.
      // Remove deleted habitActions from oldActionsIDToActions,
      // setupIdsToActionIds and onlyNewActions to let this reflect in the state.
      for (String habitActionId in onlyNewActions.keys.toList()) {
        HabitAction action = onlyNewActions[habitActionId]!;
        HabitSetup? taskSetup = setupsIDToASetup[action.setupId];
        bool isTaskValidEntry = taskSetup == null
            ? false
            : isTaskValidEntryCompute<HabitSetup>(
                taskSetup,
                action.tmzSafeDueAt,
                _appBloc.state.viewSettings?.featureSettings.hiddenHabits.keys
                        .toSet() ??
                    {},
              );
        if (!isTaskValidEntry) {
          if (taskSetup == null ||
              action.permaDeletedAt != null ||
              action.deletedAt != null ||
              !action.hasAnyUserData(taskSetup)) {
            // Remove its id link to object.
            oldActionsIDToActions.remove(habitActionId);
            // Remove them from the entries.
            setupIdsToActionIds[action.setupId]?.remove(action.tmzSafeDueAt);
            // Remove them from new actions.
            onlyNewActions.remove(habitActionId);
          }
          // Add such a task to the categorized tasks as specific filtering is affected.
          if (taskSetup != null) {
            categorizedTasks
                .putIfAbsent(
                  UpdateType.updateAffectingSpecificFiltering,
                  () => {},
                )
                .add(taskSetup);
          }
        }
      }
      actionsIDToActions = mergeMaps(oldActionsIDToActions, onlyNewActions);
    } else {
      actionsIDToActions = Map.from(oldActionsIDToActions);
    }

    // Check if incompatible setups or actions have changed
    bool isIncompatibleSetupsChanged = !const DeepCollectionEquality().equals(
      incompatibleSetupsIDToASetup,
      oldIncompatibleSetupsIDToASetup,
    );
    bool isIncompatibleActionsChanged = !const DeepCollectionEquality().equals(
      incompatibleActionsIDToActions,
      oldIncompatibleActionsIDToActions,
    );

    if (isIncompatibleSetupsChanged || isIncompatibleActionsChanged) {
      //Remove all incompatible setups and actions from the main collections
      setupsIDToASetup.removeWhere(
        (key, value) => incompatibleSetupsIDToASetup.containsKey(key),
      );
      actionsIDToActions.removeWhere(
        (key, value) => incompatibleActionsIDToActions.containsKey(key),
      );
      fullRefreshUpdate = true;
    }

    // Only update if habit setups and action are changed.
    // Habit setups updates are used for repeats but that is not the only use of updates.
    // Habit setups and actions updates are used anywhere in app to update it's details view also.
    // Also include incompatible items changes in the condition.
    if (isHabitSetupsChanged ||
        isHabitActionsChanged ||
        isIncompatibleSetupsChanged ||
        isIncompatibleActionsChanged) {
      Map<String, HabitAction> newActionsIDToActions = {};
      for (HabitAction habitAction in actionsIDToActions.values) {
        if (setupIdsToActionIds.containsKey(habitAction.setupId)) {
          newActionsIDToActions.putIfAbsent(
            habitAction.id,
            () => habitAction,
          );
          setupIdsToActionIds[habitAction.setupId]!.addAll({
            habitAction.tmzSafeDueAt: habitAction.id,
          });
        }
      }

      final homeWidgetUtility = HomeWidgetUtility(
        databaseRepository: _databaseRepository,
      );

      homeWidgetUtility.updateHomeWidget();

      emit(
        HabitsStateLoaded(
          incompatibleSetupsIDToASetup: incompatibleSetupsIDToASetup,
          setupsIDToASetup: setupsIDToASetup,
          incompatibleActionsIDToActions: incompatibleActionsIDToActions,
          actionsIDToActions: newActionsIDToActions,
          setupIdsToActionIds: setupIdsToActionIds,
          updatedHabitSetups: fullRefreshUpdate
              ? {UpdateType.fullRefreshUpdate: {}}
              : categorizedTasks,
        ),
      );
      oldSetupsIDToASetup = setupsIDToASetup;
      oldActionsIDToActions = newActionsIDToActions;
      oldSetupIdsToActionIds = setupIdsToActionIds;
      oldIncompatibleSetupsIDToASetup = incompatibleSetupsIDToASetup;
      oldIncompatibleActionsIDToActions = incompatibleActionsIDToActions;
    }
  }

  @override
  Future<void> close() async {
    await _habitSetupSubscription?.cancel();
    await _habitActionSubscription?.cancel();
    return super.close();
  }
}

/// Categorizes new tasks into different types of updates, unscheduled tasks, and
/// new all tasks map.
(Map<UpdateType, Set<HabitSetup>>, Map<String, HabitSetup>) categorizeNewTasks(
  Map<String, HabitSetup> allPastTasks,
  Map<String, HabitSetup> onlyNewTasks,
  Set<IdInfo>? removedDocsIDsInfo,
) {
  Map<UpdateType, Set<HabitSetup>> categorizedTasks = {};
  Map<String, HabitSetup> allTasks = Map.from(allPastTasks);

  void updateDataForPermaDelete(HabitSetup task) {
    if (allTasks.containsKey(task.id)) {
      categorizedTasks.putIfAbsent(UpdateType.permaDelete, () => {}).add(task);
      allTasks.remove(task.id);
    }
  }

  // Check for removed, updated, and inserted tasks
  for (var newTaskID in onlyNewTasks.keys) {
    HabitSetup newTask = onlyNewTasks[newTaskID]!;

    if (newTask.permaDeletedAt != null ||
        DocInfo.canConsiderDocForRemove(
              newTask,
              removedDocsIDsInfo?.toList(),
            ) ==
            true) {
      updateDataForPermaDelete(newTask);
      continue;
    }

    allTasks[newTaskID] = newTask;

    if (!allPastTasks.containsKey(newTaskID)) {
      // Inserted task
      categorizedTasks.putIfAbsent(UpdateType.insert, () => {}).add(newTask);
    } else {
      // Updated task
      HabitSetup oldTask = allPastTasks[newTaskID]!;
      if (!(const DeepCollectionEquality().equals(
        newTask.repeat,
        oldTask.repeat,
      ))) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingRepeats, () => {})
            .add(newTask);
      } else if (newTask.startAt != oldTask.startAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingRepeats, () => {})
            .add(newTask);
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingBasicFiltering, () => {})
            .add(newTask);
      } else if (newTask.endAt != oldTask.endAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingRepeats, () => {})
            .add(newTask);
      } else if (newTask.deletedAt != oldTask.deletedAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingBasicFiltering, () => {})
            .add(newTask);
      } else {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingSpecificFiltering, () => {})
            .add(newTask);
      }
    }
  }

  // Check for removed tasks from removedDocsIDs.
  for (IdInfo removedDocIDInfo in removedDocsIDsInfo ?? {}) {
    HabitSetup? removedTask = allTasks[removedDocIDInfo.docId];
    if (removedTask != null &&
        DocInfo.canConsiderDocForRemoveFromInfo(
          removedTask,
          removedDocIDInfo,
        )) {
      updateDataForPermaDelete(removedTask);
    }
  }

  return (categorizedTasks, allTasks);
}
