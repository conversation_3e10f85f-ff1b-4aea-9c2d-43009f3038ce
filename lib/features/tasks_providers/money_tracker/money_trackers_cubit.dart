import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:flutter/foundation.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/data/models/money_tracker/money_tracker_setups.dart';
import 'package:mevolve/data/models/money_tracker/money_tracker_transaction.dart';
import 'package:mevolve/data/models/user/removed_doc_info.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_cubit.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_state.dart';
import 'package:mevolve/features/hamburger/subscription/effective_subscription_cubit/effective_subscription_cubit.dart';
import 'package:mevolve/features/tasks_providers/common.dart';
import 'package:mevolve/features/tasks_providers/resilient_stream_merger.dart';

part 'money_trackers_state.dart';

class MoneyTrackersCubit extends Cubit<MoneyTrackersState> {
  MoneyTrackersCubit(
    this._databaseRepository,
    this._appBloc,
    this.uid,
    this._userResourcesCubit,
    this._effectiveSubscriptionCubit,
  ) : super(const MoneyTrackersStateInitial());

  final DatabaseRepository _databaseRepository;
  final AppBloc _appBloc;
  final String uid;
  final UserResourcesCubit _userResourcesCubit;
  final EffectiveSubscriptionCubit _effectiveSubscriptionCubit;

  StreamSubscription? _moneyTrackerSetupSubscription;
  StreamSubscription? _moneyTrackerActionSubscription;

  Map<String, MoneyTrackerSetup> oldSetupsIDToASetup = {};
  Map<String, MoneyTrackerTransaction> oldActionsIDToActions = {};
  Map<String, Map<DateTime, Set<String>?>> oldSetupIdsToActionIds = {};
  Map<String, MoneyTrackerSetup> oldIncompatibleSetupsIDToASetup = {};
  Map<String, MoneyTrackerTransaction> oldIncompatibleActionsIDToActions = {};

  void initialize({required String uid}) {
    _fetchMoneyTrackers(uid: uid, limit: AppConstant.maxLimit);
  }

  Future<void> fetchAllMoneyTrackersAndUpdate([
    bool fetchActionsAlso = true,
  ]) async {
    if (_appBloc.state.userData == null) return;

    // Check if userResources is loaded or not. If not then do nothing.
    // As we will have to do fetch all anyways again later when it does load.
    if (_userResourcesCubit.state is UserResourcesStateInitial) return;

    final mayCreateButCanSeeProFeatures =
        _effectiveSubscriptionCubit.state.eMayCreateButCanSeeProFeatures;

    // Fetch all at once.
    oldSetupsIDToASetup = {};
    oldIncompatibleSetupsIDToASetup = {};
    List<MoneyTrackerSetup> initialMoneyTrackerSetups =
        await _databaseRepository.getAllMoneyTrackerSetups();

    if (!mayCreateButCanSeeProFeatures) {
      initialMoneyTrackerSetups = initialMoneyTrackerSetups
          .where(
            (setup) => setup.uid != _appBloc.state.userData?.uid,
          )
          .toList();
    }

    List<MoneyTrackerTransaction>? initialMoneyTrackerTransactions;
    if (fetchActionsAlso) {
      oldActionsIDToActions = {};
      oldIncompatibleActionsIDToActions = {};
      oldSetupIdsToActionIds = {};
      initialMoneyTrackerTransactions =
          await _databaseRepository.getAllMoneyTrackerTransactions();
      if (!mayCreateButCanSeeProFeatures) {
        initialMoneyTrackerTransactions = initialMoneyTrackerTransactions
            .where(
              (setup) => setup.uid != _appBloc.state.userData?.uid,
            )
            .toList();
      }
    }

    updateState(
      moneyTrackerSetups: initialMoneyTrackerSetups,
      moneyTrackerTransactions: initialMoneyTrackerTransactions,
      fullRefreshUpdate: true,
    );
  }

  late AppState oldAppBlocState;
  late EffectiveSubscriptionState oldEffectiveSubscriptionState;
  late UserResourcesState oldUserResourcesState;

  void _updateOldValuesUsedForComparison({
    dynamic value,
  }) {
    oldAppBlocState = value is AppState ? value : _appBloc.state;
    oldEffectiveSubscriptionState = value is EffectiveSubscriptionState
        ? value
        : _effectiveSubscriptionCubit.state;
    oldUserResourcesState =
        value is UserResourcesState ? value : _userResourcesCubit.state;
  }

  void _fetchMoneyTrackers({
    required String uid,
    required int limit,
  }) async {
    _updateOldValuesUsedForComparison();

    fetchAllMoneyTrackersAndUpdate();

    await _moneyTrackerSetupSubscription?.cancel();
    await _moneyTrackerActionSubscription?.cancel();

    // Start listening for setup updates.
    _moneyTrackerSetupSubscription = ResilientStreamMerger.merge<dynamic>([
      _appBloc.stream.where(
        (event) =>
            event.appDateTimeState.deviceTimezone !=
            oldAppBlocState.appDateTimeState.deviceTimezone,
      ),
      _effectiveSubscriptionCubit.stream.where(
        (event) =>
            event.eMayCreateButCanSeeProFeatures !=
            oldEffectiveSubscriptionState.eMayCreateButCanSeeProFeatures,
      ),
      // Todo: Can we make docs deletion without listening here for doc id? Like
      // by updating the docs permaDeletedAt field before deleting them from DB so that we don't have
      // to listen separately here and the flow will work naturally?
      _appBloc.stream.where(
        (event) => (!listEquals(
              event.removedDocsIDs.moneyTrackerSetups,
              oldAppBlocState.removedDocsIDs.moneyTrackerSetups,
            ) ||
            !listEquals(
              event.removedDocsIDs.moneyTrackerTransactions,
              oldAppBlocState.removedDocsIDs.moneyTrackerTransactions,
            )),
      ),
      _databaseRepository.moneyTrackerSetupsUpdateStream(),
      _userResourcesCubit.stream.where(
        (event) =>
            event.userResources?.shared.sharedMoneyTrackerSetups.length !=
            oldUserResourcesState
                .userResources?.shared.sharedMoneyTrackerSetups.length,
      ),
    ]).listen(
      (value) {
        bool isDeviceTimezoneChanged = value is AppState &&
            (value.appDateTimeState.deviceTimezone !=
                oldAppBlocState.appDateTimeState.deviceTimezone);
        bool isDeletedDocsChanged = value is AppState &&
            (!listEquals(
                  value.removedDocsIDs.moneyTrackerSetups,
                  oldAppBlocState.removedDocsIDs.moneyTrackerSetups,
                ) ||
                !listEquals(
                  value.removedDocsIDs.moneyTrackerTransactions,
                  oldAppBlocState.removedDocsIDs.moneyTrackerTransactions,
                ));
        bool isAfterGracePeriodStateChanged =
            value is EffectiveSubscriptionState;
        bool isSharedSetupsChanged = value is UserResourcesState &&
            value.userResources?.shared.sharedMoneyTrackerSetups.length !=
                oldUserResourcesState
                    .userResources?.shared.sharedMoneyTrackerSetups.length;
        bool isAllTasksRepeatsReComputeRequired = isDeviceTimezoneChanged ||
            isSharedSetupsChanged ||
            isAfterGracePeriodStateChanged;
        bool isMoneyTrackerSetupChanged = !isAllTasksRepeatsReComputeRequired &&
            value is List<MoneyTrackerSetup>;

        _updateOldValuesUsedForComparison(
          value: value,
        );
        if (isMoneyTrackerSetupChanged) {
          updateState(moneyTrackerSetups: value);
        } else if (isAllTasksRepeatsReComputeRequired) {
          fetchAllMoneyTrackersAndUpdate();
        } else if (isDeletedDocsChanged) {
          updateState(
            moneyTrackerSetups: [],
            moneyTrackerTransactions: [],
          );
        }
      },
    );

    // Start listening for setup actions updates.
    _moneyTrackerActionSubscription = _databaseRepository
        .moneyTrackerTransactionUpdateStream(
      uid: uid,
    )
        .listen(
      (moneyTrackerTransactions) {
        updateState(moneyTrackerTransactions: moneyTrackerTransactions);
      },
      onDone: () {
        if (isClosed) return;
        // Restart listening if listening is done.
        initialize(uid: uid);
      },
    );
  }

  Future<void> updateState({
    List<MoneyTrackerSetup>? moneyTrackerSetups,
    List<MoneyTrackerTransaction>? moneyTrackerTransactions,
    bool fullRefreshUpdate = false,
  }) async {
    // we make sure that if it is sharedMoneyTracker then the userResources
    // should have the ids of those setups
    List<String> sharedTrackerSetups = _userResourcesCubit
            .state.userResources?.shared.sharedMoneyTrackerSetups ??
        [];
    moneyTrackerSetups?.removeWhere(
      (setup) =>
          setup.uid != _appBloc.state.userData!.uid &&
          !sharedTrackerSetups.contains(
            setup.id,
          ),
    );
    moneyTrackerTransactions?.removeWhere(
      (action) =>
          action.uid != _appBloc.state.userData!.uid &&
          !sharedTrackerSetups.contains(
            action.setupId,
          ),
    );

    // ----------------- Separate compatible and incompatible items early - start with existing collections -----------------
    Map<String, MoneyTrackerSetup> incompatibleSetupsIDToASetup =
        Map.from(oldIncompatibleSetupsIDToASetup);
    Map<String, MoneyTrackerTransaction> incompatibleActionsIDToActions =
        Map.from(oldIncompatibleActionsIDToActions);

    // Process setups: separate compatible from incompatible
    if (moneyTrackerSetups != null) {
      for (MoneyTrackerSetup setup in moneyTrackerSetups) {
        if (setup.isDataVersionInCompatible) {
          incompatibleSetupsIDToASetup[setup.id] = setup;
        } else {
          incompatibleSetupsIDToASetup.remove(setup.id);
        }
      }
      // Filter out incompatible setups from main processing
      moneyTrackerSetups.removeWhere(
        (setup) => setup.isDataVersionInCompatible,
      );
    }

    // Process transactions: separate compatible from incompatible
    if (moneyTrackerTransactions != null) {
      for (MoneyTrackerTransaction transaction in moneyTrackerTransactions) {
        if (transaction.isDataVersionInCompatible) {
          incompatibleActionsIDToActions[transaction.id] = transaction;
        } else {
          incompatibleActionsIDToActions.remove(transaction.id);
        }
      }
      // Filter out incompatible transactions from main processing
      moneyTrackerTransactions.removeWhere(
        (transaction) => transaction.isDataVersionInCompatible,
      );
    }
    // ----------------- Separate compatible and incompatible items early - end with existing collections -----------------

    Map<String, MoneyTrackerSetup>? onlyNewSetups = moneyTrackerSetups
        ?.asMap()
        .map((key, value) => MapEntry(value.id, value));

    List<MoneyTrackerTransaction>? preparedMoneyTrackerTransactions =
        moneyTrackerTransactions;
    if (moneyTrackerSetups != null &&
        moneyTrackerSetups.isNotEmpty &&
        preparedMoneyTrackerTransactions == null) {
      // For safety always fetch habit actions if a setup is provided as it is possible
      // it's actions reached here before setup reached and they got discard due to absence
      // of setup which is valid in some cases.
      // This case is most reproducible on cloud backup importing actions before setup
      // so actions et listened first without setups and remain discarded inside local db until
      // a full get all of tasks providers is done.
      preparedMoneyTrackerTransactions =
          (await Future.wait<List<MoneyTrackerTransaction>>(
        moneyTrackerSetups.map(
          (setup) async =>
              (await _databaseRepository
                  .getMoneyActionsByMoneySetupId(setup.id)) ??
              [],
        ),
      ))
              .expand<MoneyTrackerTransaction>((actions) => actions)
              .toList();
    }

    Map<String, MoneyTrackerTransaction>? onlyNewActions =
        preparedMoneyTrackerTransactions
            ?.asMap()
            .map((key, value) => MapEntry(value.id, value));

    Set<IdInfo>? removedSetupsIDsInfo =
        oldAppBlocState.removedDocsIDs.moneyTrackerSetups.toSet();
    List<IdInfo>? removedSetupsActionsIDsInfo =
        oldAppBlocState.removedDocsIDs.moneyTrackerTransactions;

    bool isMoneyTrackerSetupsChanged = false;
    Map<UpdateType, Set<MoneyTrackerSetup>> categorizedTasks = {};
    Map<String, MoneyTrackerSetup> setupsIDToASetup =
        Map.from(oldSetupsIDToASetup);
    Map<String, Map<DateTime, Set<String>>> setupIdsToActionIds =
        Map.from(oldSetupIdsToActionIds);
    if (onlyNewSetups != null) {
      (
        Map<UpdateType, Set<MoneyTrackerSetup>>,
        Map<String, MoneyTrackerSetup>
      ) categorizedRes = categorizeNewTasks(
        oldSetupsIDToASetup,
        onlyNewSetups,
        removedSetupsIDsInfo,
      );

      categorizedTasks = categorizedRes.$1;
      setupsIDToASetup = categorizedRes.$2;

      isMoneyTrackerSetupsChanged = categorizedTasks.isNotEmpty ||
          (oldSetupsIDToASetup.isEmpty && state is MoneyTrackersStateInitial);
      setupIdsToActionIds = {};
      for (String moneyTrackerSetupId in setupsIDToASetup.keys) {
        setupIdsToActionIds[moneyTrackerSetupId] = {};
      }
    }

    bool isMoneyTrackerTransactionsChanged = false;
    isMoneyTrackerTransactionsChanged = onlyNewActions != null;

    Map<String, MoneyTrackerTransaction> actionsIDToActions;

    if (isMoneyTrackerTransactionsChanged) {
      // Iterate all removedSetupsActionsIDs and remove them from the actions and add there
      // task in categorizedTasks as UpdateType.updateAffectingSpecificFiltering.
      for (IdInfo moneyTrackerActionIdInfo in removedSetupsActionsIDsInfo) {
        MoneyTrackerTransaction? action =
            oldActionsIDToActions[moneyTrackerActionIdInfo.docId] ??
                onlyNewActions[moneyTrackerActionIdInfo.docId];
        MoneyTrackerTransaction? actionForRemove = action == null
            ? null
            : DocInfo.canConsiderDocForRemoveFromInfo(
                action,
                moneyTrackerActionIdInfo,
              )
                ? action
                : null;
        if (actionForRemove != null) {
          MoneyTrackerSetup? taskSetup =
              setupsIDToASetup[actionForRemove.setupId];
          // Remove its id link to object.
          oldActionsIDToActions.remove(actionForRemove.id);
          // Remove them from the entries.
          setupIdsToActionIds[actionForRemove.setupId]
              ?.remove(actionForRemove.tmzSafeTransactionDate);
          // Remove them from new actions.
          onlyNewActions.remove(actionForRemove.id);
          // Send specific filter update for this.
          if (taskSetup != null) {
            categorizedTasks
                .putIfAbsent(
                  UpdateType.updateAffectingSpecificFiltering,
                  () => {},
                )
                .add(taskSetup);
          }
        }
      }

      actionsIDToActions = mergeMaps(oldActionsIDToActions, onlyNewActions);
    } else {
      actionsIDToActions = Map.from(oldActionsIDToActions);
    }

    // Check if incompatible setups or actions have changed
    bool isIncompatibleSetupsChanged = !const DeepCollectionEquality().equals(
      incompatibleSetupsIDToASetup,
      oldIncompatibleSetupsIDToASetup,
    );
    bool isIncompatibleActionsChanged = !const DeepCollectionEquality().equals(
      incompatibleActionsIDToActions,
      oldIncompatibleActionsIDToActions,
    );

    if (isIncompatibleSetupsChanged || isIncompatibleActionsChanged) {
      //Remove all incompatible setups and actions from the main collections
      setupsIDToASetup.removeWhere(
        (key, value) => incompatibleSetupsIDToASetup.containsKey(key),
      );
      actionsIDToActions.removeWhere(
        (key, value) => incompatibleActionsIDToActions.containsKey(key),
      );
      fullRefreshUpdate = true;
    }

    // Only update if moneyTracker setups and action are changed.
    // MoneyTracker setups updates are used for repeats but that is not the only use of updates.
    // MoneyTracker setups and actions updates are used anywhere in app to update it's details view also.
    // Also include incompatible items changes in the condition.
    if (isMoneyTrackerSetupsChanged ||
        isMoneyTrackerTransactionsChanged ||
        isIncompatibleSetupsChanged ||
        isIncompatibleActionsChanged) {
      Map<String, MoneyTrackerTransaction> newActionsIDToActions = {};
      for (MoneyTrackerTransaction moneyTrackerAction
          in actionsIDToActions.values) {
        if (setupIdsToActionIds.containsKey(moneyTrackerAction.setupId)) {
          newActionsIDToActions.putIfAbsent(
            moneyTrackerAction.id,
            () => moneyTrackerAction,
          );

          // Ensure the inner map for the date exists
          setupIdsToActionIds[moneyTrackerAction.setupId]!.putIfAbsent(
            moneyTrackerAction.tmzSafeTransactionDate,
            () => <String>{},
          );

          // Add the action ID to the set for the specific date
          setupIdsToActionIds[moneyTrackerAction.setupId]![
                  moneyTrackerAction.tmzSafeTransactionDate]!
              .add(moneyTrackerAction.id);
        }
      }

      emit(
        MoneyTrackersStateLoaded(
          incompatibleSetupsIDToASetup: incompatibleSetupsIDToASetup,
          setupsIDToASetup: setupsIDToASetup,
          incompatibleActionsIDToActions: incompatibleActionsIDToActions,
          actionsIDToActions: newActionsIDToActions,
          setupIdsToActionIds: setupIdsToActionIds,
          updatedMoneyTrackerSetups: fullRefreshUpdate
              ? {UpdateType.fullRefreshUpdate: {}}
              : categorizedTasks,
        ),
      );
      oldSetupsIDToASetup = setupsIDToASetup;
      oldActionsIDToActions = newActionsIDToActions;
      oldSetupIdsToActionIds = setupIdsToActionIds;
      oldIncompatibleSetupsIDToASetup = incompatibleSetupsIDToASetup;
      oldIncompatibleActionsIDToActions = incompatibleActionsIDToActions;
    }
  }

  @override
  Future<void> close() async {
    await _moneyTrackerSetupSubscription?.cancel();
    await _moneyTrackerActionSubscription?.cancel();
    return super.close();
  }
}

/// Categorizes new tasks into different types of updates, unscheduled tasks, and
/// new all tasks map.
(Map<UpdateType, Set<MoneyTrackerSetup>>, Map<String, MoneyTrackerSetup>)
    categorizeNewTasks(
  Map<String, MoneyTrackerSetup> allPastTasks,
  Map<String, MoneyTrackerSetup> onlyNewTasks,
  Set<IdInfo>? removedSetupsIDsInfo,
) {
  Map<UpdateType, Set<MoneyTrackerSetup>> categorizedTasks = {};
  Map<String, MoneyTrackerSetup> allTasks = Map.from(allPastTasks);

  void updateDataForPermaDelete(MoneyTrackerSetup task) {
    if (allTasks.containsKey(task.id)) {
      categorizedTasks.putIfAbsent(UpdateType.permaDelete, () => {}).add(task);
      allTasks.remove(task.id);
    }
  }

  // Check for removed, updated, and inserted tasks
  for (var newTaskID in onlyNewTasks.keys) {
    MoneyTrackerSetup newTask = onlyNewTasks[newTaskID]!;

    if (newTask.permaDeletedAt != null ||
        DocInfo.canConsiderDocForRemove(
              newTask,
              removedSetupsIDsInfo?.toList(),
            ) ==
            true) {
      updateDataForPermaDelete(newTask);
      continue;
    }

    allTasks[newTaskID] = newTask;

    if (!allPastTasks.containsKey(newTaskID)) {
      // Inserted task
      categorizedTasks.putIfAbsent(UpdateType.insert, () => {}).add(newTask);
    } else {
      // Updated task
      MoneyTrackerSetup oldTask = allPastTasks[newTaskID]!;
      if (newTask.deletedAt != oldTask.deletedAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingBasicFiltering, () => {})
            .add(newTask);
      } else {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingSpecificFiltering, () => {})
            .add(newTask);
      }
    }
  }

  // Check for removed tasks from removedDocsIDs.
  for (IdInfo removedDocIDInfo in removedSetupsIDsInfo ?? {}) {
    MoneyTrackerSetup? removedTask = allTasks[removedDocIDInfo.docId];
    if (removedTask != null &&
        DocInfo.canConsiderDocForRemoveFromInfo(
          removedTask,
          removedDocIDInfo,
        )) {
      updateDataForPermaDelete(removedTask);
    }
  }

  return (categorizedTasks, allTasks);
}
