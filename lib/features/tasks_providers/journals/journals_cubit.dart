import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:collection/collection.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/data/models/journal_action.dart';
import 'package:mevolve/data/models/journal_setup.dart';
import 'package:mevolve/data/models/user/removed_doc_info.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/data/repositories/home_widget_and_deep_link/home_widget.utility.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_cubit.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_state.dart';
import 'package:mevolve/features/hamburger/subscription/effective_subscription_cubit/effective_subscription_cubit.dart';
import 'package:mevolve/features/tasks_providers/common.dart';
import 'package:mevolve/features/tasks_providers/resilient_stream_merger.dart';

part 'journals_state.dart';

class JournalsCubit extends Cubit<JournalsState> {
  JournalsCubit(
    this._databaseRepository,
    this._appBloc,
    this._userResourcesCubit,
    this._effectiveSubscriptionCubit,
  ) : super(const JournalsStateInitial());

  final DatabaseRepository _databaseRepository;
  final AppBloc _appBloc;
  final UserResourcesCubit _userResourcesCubit;
  final EffectiveSubscriptionCubit _effectiveSubscriptionCubit;

  StreamSubscription? _journalSetupSubscription;
  StreamSubscription? _journalActionSubscription;

  Map<String, JournalSetup> oldSetupsIDToASetup = {};
  Map<String, JournalAction> oldActionsIDToActions = {};
  Map<String, Map<DateTime, String?>> oldSetupIdsToActionIds = {};
  Map<String, JournalSetup> oldIncompatibleSetupsIDToASetup = {};
  Map<String, JournalAction> oldIncompatibleActionsIDToActions = {};

  void initialize({required String uid}) {
    _fetchJournals(uid: uid, limit: AppConstant.maxLimit);
  }

  Future<void> fetchAllJournalsAndUpdate([bool fetchActionsAlso = true]) async {
    if (_appBloc.state.userData == null) return;

    // Check if userResources is loaded or not. If not then do nothing.
    // As we will have to do fetch all anyways again later when it does load.
    if (_userResourcesCubit.state is UserResourcesStateInitial) return;

    final mayCreateButCanSeeProFeatures =
        _effectiveSubscriptionCubit.state.eMayCreateButCanSeeProFeatures;

    // Fetch all at once.
    oldSetupsIDToASetup = {};
    oldIncompatibleSetupsIDToASetup = {};
    List<JournalSetup> initialJournalSetups =
        await _databaseRepository.getAllJournalSetups();

    if (!mayCreateButCanSeeProFeatures) {
      initialJournalSetups = initialJournalSetups
          .where(
            (setup) => setup.uid != _appBloc.state.userData?.uid,
          )
          .toList();
    }

    List<JournalAction>? initialJournalActions;
    if (fetchActionsAlso) {
      oldActionsIDToActions = {};
      oldIncompatibleActionsIDToActions = {};
      oldSetupIdsToActionIds = {};
      initialJournalActions = await _databaseRepository.getAllJournalActions();
      if (!mayCreateButCanSeeProFeatures) {
        initialJournalActions = initialJournalActions
            .where(
              (setup) => setup.uid != _appBloc.state.userData?.uid,
            )
            .toList();
      }
    }

    updateState(
      journalSetups: initialJournalSetups,
      journalActions: initialJournalActions,
      fullRefreshUpdate: true,
    );
  }

  late AppState oldAppBlocState;
  late EffectiveSubscriptionState oldEffectiveSubscriptionState;
  late UserResourcesState oldUserResourcesState;

  void _updateOldValuesUsedForComparison({
    dynamic value,
  }) {
    oldAppBlocState = value is AppState ? value : _appBloc.state;
    oldEffectiveSubscriptionState = value is EffectiveSubscriptionState
        ? value
        : _effectiveSubscriptionCubit.state;
    oldUserResourcesState =
        value is UserResourcesState ? value : _userResourcesCubit.state;
  }

  void _fetchJournals({
    required String uid,
    required int limit,
  }) async {
    _updateOldValuesUsedForComparison();

    fetchAllJournalsAndUpdate();

    await _journalSetupSubscription?.cancel();
    await _journalActionSubscription?.cancel();

    // Start listening for setup updates.
    // Using ResilientStreamMerger instead of Rx.merge to prevent completion
    // when individual streams (like EffectiveSubscriptionCubit) complete
    _journalSetupSubscription = ResilientStreamMerger.merge<dynamic>([
      // Listen for timezone changes and discard objects cached values as they are
      // no longer valid and the repeats are also not valid.
      _appBloc.stream.where(
        (event) =>
            event.appDateTimeState.deviceTimezone !=
            oldAppBlocState.appDateTimeState.deviceTimezone,
      ),
      _effectiveSubscriptionCubit.stream.where(
        (event) =>
            event.eMayCreateButCanSeeProFeatures !=
            oldEffectiveSubscriptionState.eMayCreateButCanSeeProFeatures,
      ),
      _appBloc.stream.where(
        (event) =>
            event.removedDocsIDs.journalSetups.length !=
                oldAppBlocState.removedDocsIDs.journalSetups.length ||
            event.removedDocsIDs.journalActions.length !=
                oldAppBlocState.removedDocsIDs.journalActions.length,
      ),
      _databaseRepository.journalSetupsUpdateStream(
        uid: uid,
      ),
      _userResourcesCubit.stream.where(
        (event) =>
            event.userResources?.shared.sharedJournalSetups.length !=
            oldUserResourcesState
                .userResources?.shared.sharedJournalSetups.length,
      ),
    ]).listen(
      (value) {
        if (_appBloc.state.userData == null) return;
        bool isDeviceTimezoneChanged = value is AppState &&
            (value.appDateTimeState.deviceTimezone !=
                oldAppBlocState.appDateTimeState.deviceTimezone);
        bool isDeletedDocsChanged = value is AppState &&
            (value.removedDocsIDs.journalSetups.length !=
                    oldAppBlocState.removedDocsIDs.journalSetups.length ||
                value.removedDocsIDs.journalActions.length !=
                    oldAppBlocState.removedDocsIDs.journalActions.length);
        bool isAfterGracePeriodStateChanged =
            value is EffectiveSubscriptionState;
        bool isSharedSetupsChanged = value is UserResourcesState &&
            value.userResources?.shared.sharedJournalSetups.length !=
                oldUserResourcesState
                    .userResources?.shared.sharedJournalSetups.length;
        bool isAllTasksRepeatsReComputeRequired = isDeviceTimezoneChanged ||
            isSharedSetupsChanged ||
            isAfterGracePeriodStateChanged;
        bool isJournalSetupChanged =
            !isAllTasksRepeatsReComputeRequired && value is List<JournalSetup>;

        _updateOldValuesUsedForComparison(
          value: value,
        );
        if (isJournalSetupChanged) {
          updateState(
            journalSetups: value,
          );
        } else if (isAllTasksRepeatsReComputeRequired) {
          fetchAllJournalsAndUpdate();
        } else if (isDeletedDocsChanged) {
          updateState(
            journalSetups: [],
            journalActions: [],
          );
        }
      },
    );

    // Start listening for setup actions updates.
    _journalActionSubscription = _databaseRepository
        .journalActionsUpdateStream(
      uid: uid,
    )
        .listen(
      (journalActions) {
        if (_appBloc.state.userData == null) return;
        updateState(journalActions: journalActions);
      },
      onDone: () {
        if (isClosed) return;
        // Restart listening if listening is done.
        initialize(uid: uid);
      },
    );
  }

  Future<void> updateState({
    List<JournalSetup>? journalSetups,
    List<JournalAction>? journalActions,
    bool fullRefreshUpdate = false,
  }) async {
    // we make sure that if it is sharedJournals then the userResources
    // should have the ids of those setups
    List<String> sharedJournalSetups =
        _userResourcesCubit.state.userResources?.shared.sharedJournalSetups ??
            [];
    journalSetups?.removeWhere(
      (setup) =>
          setup.uid != _appBloc.state.userData!.uid &&
          !sharedJournalSetups.contains(
            setup.id,
          ),
    );
    journalActions?.removeWhere(
      (action) =>
          action.uid != _appBloc.state.userData!.uid &&
          !sharedJournalSetups.contains(
            action.setupId,
          ),
    );

    // ----------------- Separate compatible and incompatible items early - start with existing collections -----------------
    Map<String, JournalSetup> incompatibleSetupsIDToASetup =
        Map.from(oldIncompatibleSetupsIDToASetup);
    Map<String, JournalAction> incompatibleActionsIDToActions =
        Map.from(oldIncompatibleActionsIDToActions);

    // Process setups: separate compatible from incompatible
    if (journalSetups != null) {
      for (JournalSetup setup in journalSetups) {
        if (setup.isDataVersionInCompatible) {
          incompatibleSetupsIDToASetup[setup.id] = setup;
        } else {
          incompatibleSetupsIDToASetup.remove(setup.id);
        }
      }
      // Filter out incompatible setups from main processing
      journalSetups.removeWhere(
        (setup) => setup.isDataVersionInCompatible,
      );
    }

    // Process actions: separate compatible from incompatible
    if (journalActions != null) {
      for (JournalAction action in journalActions) {
        if (action.isDataVersionInCompatible) {
          incompatibleActionsIDToActions[action.id] = action;
        } else {
          incompatibleActionsIDToActions.remove(action.id);
        }
      }
      // Filter out incompatible actions from main processing
      journalActions.removeWhere(
        (action) => action.isDataVersionInCompatible,
      );
    }
    // ----------------- Separate compatible and incompatible items early - end with existing collections -----------------

    Map<String, JournalSetup>? onlyNewSetups =
        journalSetups?.asMap().map((key, value) => MapEntry(value.id, value));

    List<JournalAction>? preparedJournalActions = journalActions;
    if (journalSetups != null &&
        journalSetups.isNotEmpty &&
        preparedJournalActions == null) {
      // For safety always fetch habit actions if a setup is provided as it is possible
      // it's actions reached here before setup reached and they got discard due to absence
      // of setup which is valid in some cases.
      // This case is most reproducible on cloud backup importing actions before setup
      // so actions et listened first without setups and remain discarded inside local db until
      // a full get all of tasks providers is done.
      preparedJournalActions = (await Future.wait<List<JournalAction>>(
        journalSetups.map(
          (setup) async =>
              (await _databaseRepository
                  .getJournalActionsByJournalSetupId(setup.id)) ??
              [],
        ),
      ))
          .expand<JournalAction>((actions) => actions)
          .toList();
    }

    Map<String, JournalAction>? onlyNewActions = preparedJournalActions
        ?.asMap()
        .map((key, value) => MapEntry(value.id, value));

    Set<IdInfo> removedSetupsIDs =
        oldAppBlocState.removedDocsIDs.journalSetups.toSet();
    List<IdInfo> removedSetupsActionsIDsInfo =
        oldAppBlocState.removedDocsIDs.journalActions;

    bool isJournalSetupsChanged = false;
    Map<UpdateType, Set<JournalSetup>> categorizedTasks = {};
    Map<String, JournalSetup> setupsIDToASetup = Map.from(oldSetupsIDToASetup);
    Map<String, Map<DateTime, String>> setupIdsToActionIds =
        Map.from(oldSetupIdsToActionIds);
    if (onlyNewSetups != null) {
      (
        Map<UpdateType, Set<JournalSetup>>,
        Map<String, JournalSetup>
      ) categorizedRes = categorizeNewTasks(
        oldSetupsIDToASetup,
        onlyNewSetups,
        removedSetupsIDs,
      );

      categorizedTasks = categorizedRes.$1;
      setupsIDToASetup = categorizedRes.$2;

      // isChanged should be true if we new items are there or if items were empty and now also empty.
      // We follow same scheme in all other top level cubits as lower level cubits won't emit
      // until these top level cubits are in initial state and will indicate loading.
      // So, if the cubit never emits due to no items, then those loader will show infinitely.
      isJournalSetupsChanged = categorizedTasks.isNotEmpty ||
          (oldSetupsIDToASetup.isEmpty && state is JournalsStateInitial);

      setupIdsToActionIds = {};
      for (String journalSetupId in setupsIDToASetup.keys) {
        setupIdsToActionIds[journalSetupId] = {};
      }
    }

    bool isJournalActionsChanged = false;
    isJournalActionsChanged = onlyNewActions != null;

    Map<String, JournalAction> actionsIDToActions;
    if (isJournalActionsChanged) {
      // Iterate all removedDocsIDs and remove them from the actions and add there
      // task in categorizedTasks as UpdateType.updateAffectingSpecificFiltering.
      for (IdInfo journalActionIdInfo in removedSetupsActionsIDsInfo) {
        JournalAction? action =
            oldActionsIDToActions[journalActionIdInfo.docId] ??
                onlyNewActions[journalActionIdInfo.docId];
        JournalAction? actionForRemove = action == null
            ? null
            : DocInfo.canConsiderDocForRemoveFromInfo(
                action,
                journalActionIdInfo,
              )
                ? action
                : null;
        if (actionForRemove != null) {
          JournalSetup? taskSetup = setupsIDToASetup[actionForRemove.setupId];
          // Remove its id link to object.
          oldActionsIDToActions.remove(actionForRemove.id);
          // Remove them from the entries.
          setupIdsToActionIds[actionForRemove.setupId]
              ?.remove(actionForRemove.tmzSafeDueAt);
          // Remove them from new actions.
          onlyNewActions.remove(actionForRemove.id);
          // Send specific filter update for this.
          if (taskSetup != null) {
            categorizedTasks
                .putIfAbsent(
                  UpdateType.updateAffectingSpecificFiltering,
                  () => {},
                )
                .add(taskSetup);
          }
        }
      }

      // Remove deleted journalActions from oldActionsIDToActions,
      // setupIdsToActionIds and onlyNewActions to let this reflect in the state.
      for (String journalActionId in onlyNewActions.keys.toList()) {
        JournalAction action = onlyNewActions[journalActionId]!;
        JournalSetup? taskSetup = setupsIDToASetup[action.setupId];
        bool isTaskValidEntry = taskSetup == null
            ? false
            : isTaskValidEntryCompute<JournalSetup>(
                taskSetup,
                action.tmzSafeDueAt,
                _appBloc.state.viewSettings?.featureSettings.hiddenJournals.keys
                        .toSet() ??
                    {},
              );
        if (!isTaskValidEntry) {
          if (taskSetup == null ||
              action.permaDeletedAt != null ||
              action.deletedAt != null ||
              !action.hasAnyUserData()) {
            // Remove its id link to object.
            oldActionsIDToActions.remove(journalActionId);
            // Remove them from the entries.
            setupIdsToActionIds[action.setupId]?.remove(action.tmzSafeDueAt);
            // Remove them from new actions.
            onlyNewActions.remove(journalActionId);
          }
          // Add such a task to the categorized tasks as specific filtering is affected.
          if (taskSetup != null) {
            categorizedTasks
                .putIfAbsent(
                  UpdateType.updateAffectingSpecificFiltering,
                  () => {},
                )
                .add(taskSetup);
          }
        }
      }
      actionsIDToActions = mergeMaps(oldActionsIDToActions, onlyNewActions);
    } else {
      actionsIDToActions = Map.from(oldActionsIDToActions);
    }

    // Check if incompatible setups or actions have changed
    bool isIncompatibleSetupsChanged = !const DeepCollectionEquality().equals(
      incompatibleSetupsIDToASetup,
      oldIncompatibleSetupsIDToASetup,
    );
    bool isIncompatibleActionsChanged = !const DeepCollectionEquality().equals(
      incompatibleActionsIDToActions,
      oldIncompatibleActionsIDToActions,
    );

    if (isIncompatibleSetupsChanged || isIncompatibleActionsChanged) {
      //Remove all incompatible setups and actions from the main collections
      setupsIDToASetup.removeWhere(
        (key, value) => incompatibleSetupsIDToASetup.containsKey(key),
      );
      actionsIDToActions.removeWhere(
        (key, value) => incompatibleActionsIDToActions.containsKey(key),
      );
      fullRefreshUpdate = true;
    }

    // Only update if journal setups and action are changed.
    // Journal setups updates are used for repeats but that is not the only use of updates.
    // Journal setups and actions updates are used anywhere in app to update it's details view also.
    // Also include incompatible items changes in the condition.
    if (isJournalSetupsChanged ||
        isJournalActionsChanged ||
        isIncompatibleSetupsChanged ||
        isIncompatibleActionsChanged) {
      Map<String, JournalAction> newActionsIDToActions = {};
      for (JournalAction journalAction in actionsIDToActions.values) {
        if (setupIdsToActionIds.containsKey(journalAction.setupId)) {
          newActionsIDToActions.putIfAbsent(
            journalAction.id,
            () => journalAction,
          );
          setupIdsToActionIds[journalAction.setupId]!.addAll({
            journalAction.tmzSafeDueAt: journalAction.id,
          });
        }
      }

      final homeWidgetUtility = HomeWidgetUtility(
        databaseRepository: _databaseRepository,
      );

      homeWidgetUtility.updateHomeWidget();

      // Check if cubit is still open before emitting new state
      if (!isClosed) {
        emit(
          JournalsStateLoaded(
            incompatibleSetupsIDToASetup: incompatibleSetupsIDToASetup,
            setupsIDToASetup: setupsIDToASetup,
            incompatibleActionsIDToActions: incompatibleActionsIDToActions,
            actionsIDToActions: newActionsIDToActions,
            setupIdsToActionIds: setupIdsToActionIds,
            updatedJournalSetups: fullRefreshUpdate
                ? {UpdateType.fullRefreshUpdate: {}}
                : categorizedTasks,
          ),
        );
      }
      oldSetupsIDToASetup = setupsIDToASetup;
      oldActionsIDToActions = newActionsIDToActions;
      oldSetupIdsToActionIds = setupIdsToActionIds;
      oldIncompatibleSetupsIDToASetup = incompatibleSetupsIDToASetup;
      oldIncompatibleActionsIDToActions = incompatibleActionsIDToActions;
    }
  }

  @override
  Future<void> close() async {
    await _journalSetupSubscription?.cancel();
    await _journalActionSubscription?.cancel();
    return super.close();
  }
}

/// Categorizes new tasks into different types of updates, unscheduled tasks, and
/// new all tasks map.
(Map<UpdateType, Set<JournalSetup>>, Map<String, JournalSetup>)
    categorizeNewTasks(
  Map<String, JournalSetup> allPastTasks,
  Map<String, JournalSetup> onlyNewTasks,
  Set<IdInfo>? removedDocsIDsInfo,
) {
  Map<UpdateType, Set<JournalSetup>> categorizedTasks = {};
  Map<String, JournalSetup> allTasks = Map.from(allPastTasks);

  void updateDataForPermaDelete(JournalSetup task) {
    if (allTasks.containsKey(task.id)) {
      categorizedTasks.putIfAbsent(UpdateType.permaDelete, () => {}).add(task);
      allTasks.remove(task.id);
    }
  }

  // Check for removed, updated, and inserted tasks
  for (var newTaskID in onlyNewTasks.keys) {
    JournalSetup newTask = onlyNewTasks[newTaskID]!;

    if (newTask.permaDeletedAt != null ||
        DocInfo.canConsiderDocForRemove(
              newTask,
              removedDocsIDsInfo?.toList(),
            ) ==
            true) {
      updateDataForPermaDelete(newTask);
      continue;
    }

    allTasks[newTaskID] = newTask;

    if (!allPastTasks.containsKey(newTaskID)) {
      // Inserted task
      categorizedTasks.putIfAbsent(UpdateType.insert, () => {}).add(newTask);
    } else {
      // Updated task
      JournalSetup oldTask = allPastTasks[newTaskID]!;
      if (!(const DeepCollectionEquality().equals(
        newTask.repeat,
        oldTask.repeat,
      ))) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingRepeats, () => {})
            .add(newTask);
      } else if (newTask.startAt != oldTask.startAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingRepeats, () => {})
            .add(newTask);
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingBasicFiltering, () => {})
            .add(newTask);
      } else if (newTask.endAt != oldTask.endAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingRepeats, () => {})
            .add(newTask);
      } else if (newTask.deletedAt != oldTask.deletedAt) {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingBasicFiltering, () => {})
            .add(newTask);
      } else {
        categorizedTasks
            .putIfAbsent(UpdateType.updateAffectingSpecificFiltering, () => {})
            .add(newTask);
      }
    }
  }

  // Check for removed tasks from removedDocsIDs.
  for (IdInfo removedDocIDInfo in removedDocsIDsInfo ?? {}) {
    JournalSetup? removedTask = allTasks[removedDocIDInfo.docId];
    if (removedTask != null &&
        DocInfo.canConsiderDocForRemoveFromInfo(
          removedTask,
          removedDocIDInfo,
        )) {
      updateDataForPermaDelete(removedTask);
    }
  }

  return (categorizedTasks, allTasks);
}
