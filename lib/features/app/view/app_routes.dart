import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:go_router/go_router.dart';
import 'package:mevolve/constants/animation_constants.dart';
import 'package:mevolve/data/enums/habit_type.dart';
import 'package:mevolve/features/app/view/app_view.dart';
import 'package:mevolve/features/app/widgets/passcode/passcode_page.dart';
import 'package:mevolve/features/app/widgets/route_error_page.dart';
import 'package:mevolve/features/app/widgets/splash_screen.dart';
import 'package:mevolve/features/app_update/data_migration_screen.dart';
import 'package:mevolve/features/app_update/update_info_screen.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/deeplink/reset_page.dart';
import 'package:mevolve/features/deeplink/routes/dynamic_auth_route_page.dart';
import 'package:mevolve/features/deeplink/routes/dynamic_home_route_page.dart';
import 'package:mevolve/features/hamburger/account/drawer_account_page.dart';
import 'package:mevolve/features/hamburger/account/widgets/custom_encryption/encryption_password_page.dart';
import 'package:mevolve/features/hamburger/account/widgets/legal_information_widgets/acceptable_use_policy.dart';
import 'package:mevolve/features/hamburger/account/widgets/legal_information_widgets/cokkies_policy.dart';
import 'package:mevolve/features/hamburger/account/widgets/legal_information_widgets/disclaimer.dart';
import 'package:mevolve/features/hamburger/account/widgets/legal_information_widgets/eula.dart';
import 'package:mevolve/features/hamburger/account/widgets/legal_information_widgets/privacy_policy.dart';
import 'package:mevolve/features/hamburger/account/widgets/legal_information_widgets/terms_of_use.dart';
import 'package:mevolve/features/hamburger/add_ons/add_add_ons/add_add_ons.dart';
import 'package:mevolve/features/hamburger/add_ons/manage_add_ons/manage_add_ons.dart';
import 'package:mevolve/features/hamburger/gallery/gallery.dart';
import 'package:mevolve/features/hamburger/notifications/notification_page.dart';
import 'package:mevolve/features/hamburger/settings/drawer_settings_page.dart';
import 'package:mevolve/features/hamburger/subscription/drawer_subscription_page.dart';
import 'package:mevolve/features/hamburger/subscription/purchase_cubit/in_app_purchase_cubit.dart';
import 'package:mevolve/features/hamburger/support_chat/drawer_support_chat_page.dart';
import 'package:mevolve/features/hamburger/trash/drawer_trash_page.dart';
import 'package:mevolve/features/home/<USER>';
import 'package:mevolve/features/insight/view/insight_page.dart';
import 'package:mevolve/features/logger/view/log_screen.dart';
import 'package:mevolve/features/today/widgets/habit/view/setup_habit_widget.dart';
import 'package:mevolve/features/today/widgets/journal/view/setup_journal_widget.dart';
import 'package:mevolve/features/today/widgets/money_tracker/widgets/setup_money_tracker_widget.dart';
import 'package:mevolve/features/welcome_and_guide/welcome_screen.dart';
import 'package:mevolve/features/widgets/custom_page_route_builder.dart';

// Authenticated and non-authenticated routes for use in redirection logic
// to force a reset of the route stack.
const String authenticatedRouteReset = '/rAuth';
const String nonAuthenticatedRouteReset = '/rnAuth';

// Initial route
const String initialPage = '/';

// Unauthenticated route
const String authPage = '/auth';

// Error route
const String errorPage = '/error';

// Home routes with different path parameters
const String homePage = '/home';

String homeTabbedPage([String? page, String? tab]) =>
    '$homePage/${page ?? ':$pagePathParam'}/${tab ?? ':$pageTabPathParam'}';

// Update page
String updateInfoScreen = '/updateInfoScreen';

// Migration Info page
String dataMigrationScreen = '/dataMigrationScreen';

//Terms and conditions page
const String termsAndConditions = '/termsOfUse';

// Drawer routes
const String drawerSettingsPage = '/drawerSettings';
const String notificationPage = '/notification';
const String featuresShowcasePage = '/featuresShowcasePage';
const String drawerTrashPage = '/drawerTrash';
const String insightPage = '/insight';

String insightTabbedPage([String? tab]) =>
    '$insightPage/${tab ?? ':$pageTabPathParam'}';
const String drawerSupportChatPage = '/drawerSupportChat';
const String drawerSubscriptionPage = '/drawerSubscription';
const String drawerAccountPage = '/drawerAccount';
const String logPage = '/log';
const String habitSetupPage = '/habitSetup';
const String journalSetupPage = '/journalSetup';
const String moneyTrackerSetupPage = '/moneyTrackerSetup';
const String passcodePage = '/passcodePage';
const String encryptionPasswordPage = '/encryptionPasswordPage';
const String addAddOnsPage = '/addAddOnsPage';
const String manageAddOnsPage = '/manageAddOnsPage';
const String welcomeSlidesPage = '/welcomeSlidesPage';
const String galleryPage = '/gallery';

// Home route pages
const String todayPage = 'today';
const String notesPage = 'notes';
const String pastPage = 'past';
const String futurePage = 'future';
const String listsPage = 'lists';
const String emptyFeaturePage = 'noFeatures';

// Home route pages tabs
const String pastTodoTab = 'todo';
const String pastHabitTab = 'habit';
const String pastJournalTab = 'journal';
const String pastMoneyTrackerTab = 'money';
const String pastCalendarTab = 'calendar';
const String todayTodayTab = 'today';
const String todayOverdueTab = 'overdue';
const String todayUnscheduledTab = 'unscheduled';
const String futureTodoTab = 'todo';
const String futureHabitTab = 'habitSetup';
const String futureJournalTab = 'journalSetup';
const String futureCalendarTab = 'calendar';
const String futureMoneyTrackerTab = 'money';

// lists tab
const String myList = 'myList';
const String sharedList = 'shared';
//notes tab
const String myNotes = 'myNotes';
const String sharedNotes = 'shared';

// insight tabs
const String insightFavTab = 'fav';

// Drawer Account route pages
const String privacyPolicyPage = '/drawerAccount/privacyPolicy';
const String termsOfUsePage = '/drawerAccount/termsOfUse';
const String cookiePolicyPage = '/drawerAccount/cookiePolicy';
const String eulaPage = '/drawerAccount/eula';
const String disclaimerPage = '/drawerAccount/disclaimer';
const String acceptableUsePolicy = '/drawerAccount/acceptableUsePolicy';

// Query parameters
const String contentTypeParam = 'contentType';
const String actionParam = 'action';
const String dateParam = 'date';
const String idParam = 'id';
const String sheetParam = 'sheet';
const String launchTypeParam = 'launchType';
const String hashParam = 'hash';

// Path params
const String pagePathParam = 'page';
const String pageTabPathParam = 'tab';
const String emptyPathParam = '_';

// Non real routes
// These are not go routes but are widgets switched based on the state.
const String emailOtp = 'emailOtp';

// Show query param values enum
enum ShowQueryParamValue {
  createTodo,
  createNote,
  setupJournal,
  setupHabit,
  todo,
  note
}

enum ListInviteQueryParamValue { email, link }

Set<String> dontHandleHomeScreenPathChangeWhenOnTheseRoutes = {
  manageAddOnsPage,
  drawerSupportChatPage,
};

final GlobalKey<NavigatorState> rootNavKey = GlobalKey<NavigatorState>();
final GlobalKey<NavigatorState> authNavKey = GlobalKey<NavigatorState>();

BuildContext? get authenticatedGlobalContext {
  if (authNavKey.currentContext == null) {
    return null;
  }
  try {
    // Note: Attempts to access a authenticated route cubit. This call is important
    // if we want to verify if the authNavKey is still usable in RELEASE build. The property
    // mounted is not usable because it's intermediate stage between deposed and mounted.
    // If this fails than the widget tree is no longer stable so we return null.
    authNavKey.currentContext?.read<InAppPurchaseCubit>();
    return authNavKey.currentContext;
  } catch (e) {
    log(
      'AuthNavKey not usable so returning null as context. Common case if we are moving away from authenticated route and somewhere we try looking up a deactivated widgets ancestor. Verification was done by trying to access InAppPurchaseCubit.',
    );
    // If an error occurs (e.g., widget deactivated), return null
    return null;
  }
}

NavigatorState? get authenticatedGlobalCurrentState {
  return authNavKey.currentState;
}

// The last location of goRouter.
String? lastRoute = initialPage;

// A flag to keep track of logout for use in app redirection logic.
bool isLogout = false;

// Class for goRouter instance
class AppRouter {
  AppRouter() : goRouter = _router;

  // This instance will be store route state
  final GoRouter goRouter;

  static GoRouter get _router => GoRouter(
        initialLocation: lastRoute,
        navigatorKey: rootNavKey,
        debugLogDiagnostics: true,
        redirect: appRedirection,
        errorBuilder: (BuildContext context, GoRouterState state) {
          return RouteErrorScreen(state: state);
        },
        routes: [
          GoRoute(
            path: nonAuthenticatedRouteReset,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return buildPage(
                pageKey: state.pageKey,
                child: (_) => const ResetPage(),
                showTransition: false,
                constrainPageWidth: false,
              );
            },
          ),
          // Initial route
          GoRoute(
            path: initialPage,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return buildPage(
                pageKey: state.pageKey,
                child: (_) => const SplashScreen(),
                showTransition: false,
                constrainPageWidth: false,
              );
            },
          ),
          GoRoute(
            path: errorPage,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return buildPage(
                pageKey: state.pageKey,
                child: (_) {
                  String? error = state.extra as String?;
                  return RouteErrorScreen(error: error);
                },
                showTransition: false,
                constrainPageWidth: false,
              );
            },
          ),
          GoRoute(
            name: updateInfoScreen,
            path: updateInfoScreen,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return buildPage(
                pageKey: state.pageKey,
                child: (_) => const UpdateInfoScreen(),
                showTransition: true,
                constrainPageWidth: false,
              );
            },
          ),
          GoRoute(
            name: dataMigrationScreen,
            path: dataMigrationScreen,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return buildPage(
                pageKey: state.pageKey,
                child: (_) => const DataMigrationScreen(),
                showTransition: true,
                constrainPageWidth: false,
              );
            },
          ),
          GoRoute(
            name: termsAndConditions,
            path: termsAndConditions,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return buildPage(
                pageKey: state.pageKey,
                child: (_) => const TermsOfUserWidget(),
                showTransition: true,
                constrainPageWidth: false,
              );
            },
          ),
          // Unauthenticated route
          GoRoute(
            path: authPage,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return buildPage(
                pageKey: state.pageKey,
                child: (_) => const DynamicAuthRoutePage(),
                showTransition: false,
                constrainPageWidth: false,
              );
            },
          ),
          GoRoute(
            name: 'encryptionPasswordPage',
            path: encryptionPasswordPage,
            pageBuilder: (BuildContext context, GoRouterState state) {
              return buildPage(
                pageKey: state.pageKey,
                child: (_) => const EncryptionPasswordPage(),
                showTransition: true,
                constrainPageWidth: false,
              );
            },
          ),
          // Authenticated shell route with auth guard
          ShellRoute(
            navigatorKey: authNavKey,
            pageBuilder:
                (BuildContext context, GoRouterState state, Widget child) {
              return buildPage(
                pageKey: state.pageKey,
                child: (_) => AuthenticatedWidget(child: child),
                showTransition: false,
                constrainPageWidth: false,
              );
            },
            routes: [
              // Initial route
              GoRoute(
                path: authenticatedRouteReset,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => const ResetPage(),
                    showTransition: false,
                    constrainPageWidth: false,
                  );
                },
              ),

              // NEW SHELL ROUTE ARCHITECTURE - Clean and maintainable
              ShellRoute(
                parentNavigatorKey: authNavKey,
                pageBuilder: (context, state, child) => buildPage(
                  pageKey: state.pageKey,
                  selectedPage:
                      state.pathParameters[pagePathParam] ?? todayPage,
                  child: (_) => HomeShell(child: child),
                  showTransition: false,
                ),
                routes: [
                  // Default home redirect
                  GoRoute(
                    path: homePage,
                    redirect: (context, state) => homeTabbedPage(
                      todayPage,
                      todayTodayTab,
                    ),
                  ),

                  // Home page without tab
                  GoRoute(
                    path: '$homePage/:page',
                    pageBuilder: (context, state) {
                      return NoTransitionPage(
                        key: state.pageKey,
                        child: const DynamicHomeRoutePage(),
                      );
                    },
                  ),

                  // Dynamic home page routes - handles all pages with single route
                  GoRoute(
                    path: homeTabbedPage(),
                    pageBuilder: (context, state) {
                      return NoTransitionPage(
                        key: state.pageKey,
                        child: const DynamicHomeRoutePage(),
                      );
                    },
                  ),
                ],
              ),
              GoRoute(
                name: '$habitSetupPage/:habitType',
                path: habitSetupPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  String? habitTypeQueryParam =
                      state.uri.queryParameters['habitType'] ?? '';
                  HabitType? habitType =
                      HabitType.fromString(habitTypeQueryParam);
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => SetupHabitWidget.open(habitType: habitType),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: 'passcode-page',
                path: passcodePage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => const PasscodePage(
                      isReAuthAfterResume: true,
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: journalSetupPage,
                path: journalSetupPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => SetupJournalWidget.open(),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: moneyTrackerSetupPage,
                path: moneyTrackerSetupPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => SetupMoneyTrackerWidget.open(),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                path: insightPage,
                parentNavigatorKey: authNavKey,
                redirect: (context, state) => '$insightPage/$insightFavTab',
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    selectedPage: insightPage,
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: InsightPage(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                path: insightTabbedPage(),
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  String pathParam =
                      state.pathParameters[pageTabPathParam] ?? insightFavTab;
                  String? idQueryParamValue =
                      state.uri.queryParameters[idParam];
                  return buildPage(
                    selectedPage: insightPage,
                    pageKey: state.pageKey,
                    child: (_) => TempFixShellRouterScreensPopping(
                      child: InsightPage(
                        path: pathParam,
                        queryParam: idQueryParamValue,
                      ),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                path: addAddOnsPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: AddAddOnsPage(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                path: featuresShowcasePage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    selectedPage: featuresShowcasePage,
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: FeaturesShowcaseScreen(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                path: welcomeSlidesPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    selectedPage: welcomeSlidesPage,
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: WelcomeSlidesScreen(),
                    ),
                    showTransition: false,
                  );
                },
              ),
              GoRoute(
                path: manageAddOnsPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    selectedPage: manageAddOnsPage,
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: AddOnPage(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                path: galleryPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    selectedPage: galleryPage,
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: Gallery(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: drawerSettingsPage,
                path: drawerSettingsPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    selectedPage: drawerSettingsPage,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: DrawerSettingsPage(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: notificationPage,
                path: notificationPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    selectedPage: notificationPage,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: NotificationPage(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: drawerSupportChatPage,
                path: drawerSupportChatPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    selectedPage: drawerSupportChatPage,
                    pageKey: state.pageKey,
                    child: (_) => DrawerSupportChatPage.page(),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: drawerSubscriptionPage,
                path: drawerSubscriptionPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  String isRequestFromAdPopup =
                      state.uri.queryParameters['isRequestFromAdPopup'] ?? '';
                  return buildPage(
                    selectedPage: drawerSubscriptionPage,
                    pageKey: state.pageKey,
                    child: (_) => DrawerSubscriptionPage(
                      isRequestFromAdPopup: isRequestFromAdPopup == 'true',
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: drawerTrashPage,
                path: drawerTrashPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    selectedPage: drawerTrashPage,
                    pageKey: state.pageKey,
                    child: (_) => TempFixShellRouterScreensPopping(
                      child: DrawerTrashPage.page(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: drawerAccountPage,
                path: drawerAccountPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    selectedPage: drawerAccountPage,
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: DrawerAccountPage(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: logPage,
                path: logPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => TempFixShellRouterScreensPopping(
                      child: LogScreen.page(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: privacyPolicyPage,
                path: privacyPolicyPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: PrivacyPolicyWidget(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: termsOfUsePage,
                path: termsOfUsePage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: TermsOfUserWidget(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: cookiePolicyPage,
                path: cookiePolicyPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: CookiesPolicyWidget(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: eulaPage,
                path: eulaPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: Eula(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: disclaimerPage,
                path: disclaimerPage,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: Disclaimer(),
                    ),
                    showTransition: true,
                  );
                },
              ),
              GoRoute(
                name: acceptableUsePolicy,
                path: acceptableUsePolicy,
                parentNavigatorKey: authNavKey,
                pageBuilder: (BuildContext context, GoRouterState state) {
                  return buildPage(
                    pageKey: state.pageKey,
                    child: (_) => const TempFixShellRouterScreensPopping(
                      child: AcceptableUsePolicy(),
                    ),
                    showTransition: true,
                  );
                },
              ),
            ],
          ),
        ],
      );
}

extension CustomContextGo on BuildContext {
  /// Don't use this for inside navigation of mePush routes.
  /// It will destroy the page stack keeping only the current page.
  void meGo(String path) {
    try {
      String currentUri =
          GoRouter.of(this).routeInformationProvider.value.uri.toString();
      if (currentUri != path) {
        go(path);
      }
    } catch (e) {
      // GoRouter not available in context, skip navigation
      debugPrint('GoRouter not available for meGo: $e');
    }
  }

  /// Use this to push routes at the top of the current routes stack.
  void mePush(String path) {
    try {
      String currentUri =
          GoRouter.of(this).routeInformationProvider.value.uri.toString();
      if (currentUri != path) {
        push(path);
      }
    } catch (e) {
      // GoRouter not available in context, skip navigation
      debugPrint('GoRouter not available for mePush: $e');
    }
  }

  /// Use me replace just like meGo but only for navigating inside mePush routes.
  ///
  /// Replaces the top-most page of the page stack with the given one but treats it as the same page.
  ///
  /// The page key will be reused. This will preserve the state and not run any page animation.
  void meReplace(String path) {
    try {
      String currentUri =
          GoRouter.of(this).routeInformationProvider.value.uri.toString();
      if (currentUri != path) {
        replace(path);
      }
    } catch (e) {
      // GoRouter not available in context, skip navigation
      debugPrint('GoRouter not available for meReplace: $e');
    }
  }

  /// Use this to pop routes from the top of the current routes stack.
  /// If no routes to pop then it will navigate to the home page.
  void mePop() {
    if (!mounted) return;

    // String currentUri =
    //     GoRouter.of(this).routeInformationProvider.value.uri.toString();

    bool isNoRouteAvailableBehind = !canPop();

    // If can't pop, go to home page.
    if (isNoRouteAvailableBehind) {
      meGo(homePage);
    } else {
      pop();
    }
  }

  /// Todo: This GoRouter is seriously a big headache. Move To AutoRoute or something else in future
  /// that offers extensive methods for modal navigation. We should also simplify the navigation.
  ///
  /// Use this for modal dismissals that should respect WillPopScope/PopScope conditions.
  /// If pop conditions prevent closing, it won't pop. Otherwise behaves like mePop().
  void mePopModal() {
    if (!mounted) return;

    bool isNoRouteAvailableBehind = !canPop();

    // If can't pop, go to home page.
    if (isNoRouteAvailableBehind) {
      meGo(homePage);
    } else {
      Navigator.maybePop(this);
    }
  }
}

Page<T> buildPage<T>({
  LocalKey? pageKey,
  required WidgetBuilder child,
  required bool showTransition,
  bool fullscreenDialog = true,
  bool constrainPageWidth = true,
  String? selectedPage,
}) {
  bool shouldShowTransition =
      (ScreenSizeState.instance.isBigScreen ? false : showTransition);
  return shouldShowTransition
      ? MeMaterialPage<T>(
          key: pageKey,
          child: (context) => constrainPageWidth
              ? CustomPageRouteBuilder(
                  selectedPage: selectedPage,
                  child: child(context),
                )
              : child(context),
          opaque: false,
          fullscreenDialog: fullscreenDialog,
        )
      : MeCustomPage<T>(
          key: pageKey,
          transitionDuration: const Duration(milliseconds: 0),
          reverseTransitionDuration: const Duration(milliseconds: 0),
          child: (context) => constrainPageWidth
              ? CustomPageRouteBuilder(
                  selectedPage: selectedPage,
                  child: child(context),
                )
              : child(context),
          transitionsBuilder: (context, animation, secondaryAnimation, child) =>
              child,
          opaque: false,
          fullscreenDialog: fullscreenDialog,
        );
}

PageRoute<T> buildPageRoute<T>({
  required WidgetBuilder child,
  bool showTransition = false,
  bool fullscreenDialog = true,
  bool constrainPageWidth = true,
  String? selectedPage,
}) {
  return showTransition
      ? MeMaterialTransitionPageRoute<T>(
          page: buildPage<T>(
            child: child,
            showTransition: showTransition,
            fullscreenDialog: fullscreenDialog,
            selectedPage: selectedPage,
            constrainPageWidth: constrainPageWidth,
          ) as MeMaterialPage<T>,
        )
      : MeCustomTransitionPageRoute<T>(
          page: buildPage<T>(
            child: child,
            showTransition: showTransition,
            fullscreenDialog: fullscreenDialog,
            selectedPage: selectedPage,
            constrainPageWidth: constrainPageWidth,
          ) as MeCustomPage<T>,
        );
}

class NoHistoryUrlStrategy extends PathUrlStrategy {
  @override
  void pushState(Object? state, String title, String url) =>
      replaceState(state, title, url);
}

/// Don't use this once it's resolved: https://github.com/flutter/flutter/issues/139398
/// Popping from the shell router routes using back button is not working due to GoRouter issues.
/// This is a temporary fix listen pop event and pop the screen manually.
class TempFixShellRouterScreensPopping extends StatelessWidget {
  const TempFixShellRouterScreensPopping({super.key, required this.child});

  final Widget child;

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          context.mePop();
        }
      },
      child: child,
    );
  }
}

class MeCustomPage<T> extends Page<T> {
  const MeCustomPage({
    required this.child,
    required this.transitionsBuilder,
    this.transitionDuration =
        AnimationConstants.pageTransitionAnimationDuration,
    this.reverseTransitionDuration =
        AnimationConstants.pageTransitionAnimationDuration,
    this.maintainState = true,
    this.fullscreenDialog = false,
    this.opaque = true,
    this.barrierDismissible = false,
    this.barrierColor,
    this.barrierLabel,
    super.key,
    super.name,
    super.arguments,
    super.restorationId,
  });

  final WidgetBuilder child;
  final Duration transitionDuration;
  final Duration reverseTransitionDuration;
  final bool maintainState;
  final bool fullscreenDialog;
  final bool opaque;
  final bool barrierDismissible;
  final Color? barrierColor;
  final String? barrierLabel;

  final Widget Function(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) transitionsBuilder;

  @override
  Route<T> createRoute(BuildContext context) {
    return MeCustomTransitionPageRoute<T>(page: this);
  }
}

class MeCustomTransitionPageRoute<T> extends PageRoute<T> {
  MeCustomTransitionPageRoute({required MeCustomPage<T> page})
      : super(settings: page);

  MeCustomPage<T> get _page => settings as MeCustomPage<T>;

  @override
  bool get barrierDismissible => _page.barrierDismissible;

  @override
  Color? get barrierColor => _page.barrierColor;

  @override
  String? get barrierLabel => _page.barrierLabel;

  @override
  Duration get transitionDuration => _page.transitionDuration;

  @override
  Duration get reverseTransitionDuration => _page.reverseTransitionDuration;

  @override
  bool get maintainState => _page.maintainState;

  @override
  bool get fullscreenDialog => _page.fullscreenDialog;

  @override
  bool get opaque => _page.opaque;

  @override
  Widget buildPage(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
  ) =>
      Semantics(
        scopesRoute: true,
        explicitChildNodes: true,
        child: _page.child(context),
      );

  @override
  Widget buildTransitions(
    BuildContext context,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) =>
      _page.transitionsBuilder(
        context,
        animation,
        secondaryAnimation,
        child,
      );
}

class MeMaterialPage<T> extends Page<T> {
  const MeMaterialPage({
    required this.child,
    this.maintainState = true,
    this.fullscreenDialog = false,
    this.allowSnapshotting = true,
    this.opaque = true,
    super.key,
    super.name,
    super.arguments,
    super.restorationId,
  });

  final WidgetBuilder child;
  final bool maintainState;
  final bool fullscreenDialog;
  final bool allowSnapshotting;
  final bool opaque;

  @override
  Route<T> createRoute(BuildContext context) {
    return MeMaterialTransitionPageRoute<T>(
      page: this,
      allowSnapshotting: allowSnapshotting,
    );
  }
}

class MeMaterialTransitionPageRoute<T> extends PageRoute<T>
    with MaterialRouteTransitionMixin<T> {
  MeMaterialTransitionPageRoute({
    required MeMaterialPage<T> page,
    super.allowSnapshotting,
  }) : super(settings: page);

  MeMaterialPage<T> get _page => settings as MeMaterialPage<T>;

  @override
  Widget buildContent(BuildContext context) {
    return _page.child(context);
  }

  @override
  Duration get transitionDuration =>
      AnimationConstants.pageTransitionAnimationDuration;

  @override
  bool get maintainState => _page.maintainState;

  @override
  bool get fullscreenDialog => _page.fullscreenDialog;

  @override
  bool get opaque => _page.opaque;

  @override
  String get debugLabel => '${super.debugLabel}(${_page.name})';
}
