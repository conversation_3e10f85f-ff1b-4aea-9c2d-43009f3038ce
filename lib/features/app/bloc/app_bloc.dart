import 'dart:async';

import 'package:easy_debounce/easy_debounce.dart';
import 'package:easy_debounce/easy_throttle.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_database/firebase_database.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:mevolve/data/models/user/feature_settings.dart';
import 'package:mevolve/data/providers/authentication/authentication_service.dart';
import 'package:mevolve/utilities/connectivity/internet_connectivity_service.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/groups_singletons/base_group_manager.dart';
import 'package:mevolve/constants/app_config.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/constants/release_config.dart';
import 'package:mevolve/data/enums/app_language_type.dart';
import 'package:mevolve/data/enums/theme_type.dart';
import 'package:mevolve/data/enums/today_view_type.dart';
import 'package:mevolve/data/models/release_config_model.dart';
import 'package:mevolve/data/models/user/calendar_integration.dart';
import 'package:mevolve/data/models/user/removed_doc_info.dart';
import 'package:mevolve/data/models/user/special_activity.dart';
import 'package:mevolve/data/models/user/user.dart';
import 'package:mevolve/data/models/user/user_metadata.dart';
import 'package:mevolve/data/models/user/user_resources.dart';
import 'package:mevolve/data/models/user/view_settings.dart';
import 'package:mevolve/data/models/list_screens_models/task_lists.dart';
import 'package:mevolve/data/providers/crashlytics.dart';
import 'package:mevolve/data/providers/drift_database.dart';
import 'package:mevolve/data/providers/firebase_database.dart';
import 'package:mevolve/data/providers/firebase_functions.dart';
import 'package:mevolve/data/providers/firebase_messaging.dart';
import 'package:mevolve/data/providers/shared_prefs.dart';
import 'package:mevolve/data/repositories/data_sync/data_sync_service.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/features/hamburger/subscription/effective_subscription_cubit/effective_subscription_helper.dart';
import 'package:mevolve/features/notifications/foreground_service.dart';
import 'package:mevolve/data/repositories/home_widget_and_deep_link/home_widget.utility.dart';
import 'package:mevolve/data/repositories/home_widget_and_deep_link/home_widget_service.dart';
import 'package:mevolve/data/repositories/storage_repository.dart';
import 'package:mevolve/features/app/bloc/cubit/release_config_cubit.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/notifications/notification_service.dart';
import 'package:mevolve/features/timer_habit/cubit/timer_notification/timer_notifications_manager.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/encryption/me_encryption.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';
import 'package:mevolve/utilities/nullable.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:ntp/ntp.dart';

part 'app_event.dart';

part 'app_state.dart';

DateTime get _getCurrentDateTimeInUtc => DateTime.now().toUtc();
const String networkTimeThrottleTag = 'throttle_network_time_update';
const Duration networkTimeThrottleDuration = Duration(seconds: 5);
const String widgetThemeUpdateThrottleTag = 'widget_theme_update_throttle';
const Duration widgetThemeUpdateThrottleDuration = Duration(seconds: 5);
const String fcmStatusCheckThrottleTag = 'throttle_fcm_status_check';
const Duration fcmStatusCheckThrottleDuration = Duration(seconds: 2);
const String userPropUpdateThrottleTag = 'throttle_user_prop_updates';
const Duration userPropUpdateThrottleDuration = Duration(seconds: 2);
const String dataPushAttemptThrottleTag = 'throttle_data_push_attempt';
const Duration dataPushAttemptThrottleDuration = Duration(seconds: 10);
const String dataSyncAttemptThrottleTag = 'throttle_data_sync_attempt';
const Duration dataSyncAttemptThrottleDuration = Duration(minutes: 5);

Future<DateTime?> getCurrentNetworkDateTimeInUtc() async {
  if (MePlatform.isWeb) {
    return DateTime.now().toUtc();
  }
  // If network time close to device time, then use device time as source of truth.
  try {
    return await NTP.now(timeout: const Duration(seconds: 2)).then(
      (value) {
        DateTime currentNetworkDateTime = value.toUtc();
        return currentNetworkDateTime;
      },
    );
  } catch (e) {
    // NOTE: when this goes in bg it will give error since internet wont be available, so we can ignore this error
    // if (!MePlatform.isWeb) {
    //   MeLogger.getLogger(LogTags.appBloc).e('Error getting network time: $e');
    // }
    return null;
  }
}

String currentTimezone = _getCurrentTimezone;

String get _getCurrentTimezone {
  currentTimezone = DateTime.now().timeZoneOffset.toString();
  return currentTimezone;
}

Future<String> get _getCurrentTimezoneLocation async {
  return MePlatform.isWindows
      ? DateTime.now().timeZoneName
      : await FlutterTimezone.getLocalTimezone();
}

class AppBloc extends Bloc<AppEvent, AppState> {
  AppBloc({
    required AuthenticationService authenticationRepository,
    required FirebaseDatabaseRepository firebaseDatabaseRepository,
    required FirebaseMessagingRepository firebaseMessagingRepository,
    required DatabaseRepository databaseRepository,
    required FirebaseFunctionsRepository firebaseFunctionsRepository,
    required SharedPreferencesClient sharedPreferencesClient,
    required ReleaseConfigCubit releaseConfigCubit,
    required StorageRepository storageRepository,
  })  : _authenticationRepository = authenticationRepository,
        _databaseRepository = databaseRepository,
        _sharedPreferencesClient = sharedPreferencesClient,
        _storageRepository = storageRepository,
        _releaseConfigCubit = releaseConfigCubit,
        _firebaseDatabaseRepository = firebaseDatabaseRepository,
        _firebaseMessagingRepository = firebaseMessagingRepository,
        _firebaseFunctionsRepository = firebaseFunctionsRepository,
        super(
          AppState.splash(
            passcodeVerified: false,
            appDateTimeState: AppDateTimeState(
              deviceDateTime: _getCurrentDateTimeInUtc,
              networkDateTime: null,
              deviceTimezone: _getCurrentTimezone,
              deviceTimezoneName: DateTime.now().timeZoneName,
              deviceTimezoneLocation: DateTime.now().timeZoneName,
            ),
            appLanguage: AppConfig.instance.deviceLanguageType,
          ),
        ) {
    _log.i('*********** Starting App *************');
    on<FirebaseUserChanged>(_onFirebaseUserChanged);
    on<AppLoginProcessStatusChanged>(_onAppLoginProcessStatusChanged);
    on<DataSyncServiceStatusChanged>(_onDataSyncServiceStatusChanged);
    on<LocalUserDataChanged>(_onLocalUserDataChanged);
    on<LocalUserResourceChanged>(_onLocalUserResourcesChanged);
    on<LocalUserMetadataChanged>(_onLocalUserMetadataChanged);
    on<SpecialActivitiesChanged>(_onSpecialActivitiesChanged);
    on<LocalViewSettingsChanged>(_onLocalViewSettingsChanged);
    on<LocalCalendarIntegrationChanged>(_onLocalCalendarIntegrationChanged);
    on<PasscodeStatusChanged>(_onPasscodeStatusChanged);
    on<AppLifecycleStateChanged>(_onAppLifecycleStateChanged);
    on<ConnectionStatusChanged>(_onConnectionStatusChanged);
    on<AppLogoutRequested>(_onLogoutRequested);
    on<AppUpdateStatusChanged>(_onAppUpdateStateChanged);
    on<AppUserDeleted>(_onAppUserDeleted);
    on<AppDateTimeStateChanged>(_onUpdateAppDateTimeState);
    on<AppLanguageChanged>(_onAppLanguageChanged);
    on<ColorsVersionChanged>(_onColorsVersionChanged);
    on<TranslationsVersionChanged>(_onTranslationsVersionChanged);
    on<ResetMevolvePinResetFlowOnAction>(_onResetMevolvePinResetFlowOnAction);
    on<PasscodePageDisposedEvent>(_onPasscodePageDisposed);
    on<MediaDataSyncChanged>(_onMediaDataSyncChanged);
    on<WelcomeWidgetsStatusChanged>(_onWelcomeWidgetsStatusChanged);
    _periodicUpdate();
    _listenForFirebaseUser();
    listenForCloudReleaseConfig();

    /// Initialize the AppLifecycleListener class and pass callbacks.
    _appLifecycleListener = AppLifecycleListener(
      onStateChange: _onStateChanged,
    );
  }

  late final AppLifecycleListener _appLifecycleListener;
  final AuthenticationService _authenticationRepository;
  final FirebaseDatabaseRepository _firebaseDatabaseRepository;
  final FirebaseMessagingRepository _firebaseMessagingRepository;
  final DatabaseRepository _databaseRepository;
  final SharedPreferencesClient _sharedPreferencesClient;
  final StorageRepository _storageRepository;
  StreamSubscription<User?>? _firebaseUserSubscription;
  final ReleaseConfigCubit _releaseConfigCubit;
  final FirebaseFunctionsRepository _firebaseFunctionsRepository;
  DataSyncService? _dataSyncService;

  DataSyncService? get dataSyncService => _dataSyncService;

  int? appInBgTimeStamp;

  StreamSubscription<bool>? _internetConnectionSubscription;
  StreamSubscription<DatabaseEvent>? _databaseEventSubscription;
  final Log _log = MeLogger.getLogger(LogTags.appBloc);

  late Timer _timer;
  bool isUserDeleted = false;

  Brightness? oldBrightness;
  ThemeType? oldThemeMode;
  String? oldThemeColor;

  Future<void> _periodicUpdate() async {
    DateTime? networkDateTime;
    String? deviceTimezone;

    _timer = Timer.periodic(const Duration(seconds: 1), (timer) async {
      // Fetch current device and network DateTime details
      DateTime deviceDateTime = _getCurrentDateTimeInUtc;
      // Fetch network datetime periodically using throttling
      EasyThrottle.throttle(
        networkTimeThrottleTag,
        networkTimeThrottleDuration,
        () async {
          networkDateTime = await getCurrentNetworkDateTimeInUtc();
        },
      );
      // If networkDateTime is null then try getting from network without throttling.
      networkDateTime ??= await getCurrentNetworkDateTimeInUtc();

      if (networkDateTime != null) {
        // This logic fixes the negligible difference between the device and network datetime.
        // It follows device datetime as the source of truth when the difference is less than 60 seconds.
        // This avoids unnecessary near emits due to network time minute changing few seconds later the device time.
        int diffInNetworkAndDeviceDateTime =
            networkDateTime!.difference(deviceDateTime).inSeconds.abs();
        bool deviceDateTimeIsCorrect = diffInNetworkAndDeviceDateTime <= 60;
        if (deviceDateTimeIsCorrect) {
          networkDateTime = deviceDateTime;
        }
      }

      // Get the current state
      final AppDateTimeState currentAppDateTimeState = state.appDateTimeState;
      AppDateTimeState newAppDateTimeState = currentAppDateTimeState.copyWith();

      bool isTimeZoneChanged = deviceTimezone != _getCurrentTimezone;

      if (isTimeZoneChanged) {
        deviceTimezone = _getCurrentTimezone;
        newAppDateTimeState = currentAppDateTimeState.copyWith(
          deviceTimezone: deviceTimezone,
          deviceTimezoneName: DateTime.now().timeZoneName,
          deviceTimezoneLocation: await _getCurrentTimezoneLocation,
        );
      }

      // Determine the new state
      newAppDateTimeState = newAppDateTimeState.copyWith(
        networkDateTime: Nullable(networkDateTime),
        deviceDateTime: deviceDateTime,
      );

      // Detect changes in monitored properties
      final bool hasDeviceDateTimeChanged =
          !currentAppDateTimeState.deviceDateTime.isDateTimeEqual(
        newAppDateTimeState.deviceDateTime,
        tillMinutes: true,
      );

      final bool hasNetworkDateTimeChanged =
          !currentAppDateTimeState.networkDateTime.isDateTimeEqual(
        newAppDateTimeState.networkDateTime,
        tillMinutes: true,
      );

      if (hasDeviceDateTimeChanged ||
          hasNetworkDateTimeChanged ||
          isTimeZoneChanged) {
        add(AppDateTimeStateChanged(appDateTimeState: newAppDateTimeState));
      }

      // Attempt data push periodically so that there are less chance of data push failure.
      EasyThrottle.throttle(
        dataPushAttemptThrottleTag,
        dataPushAttemptThrottleDuration,
        () => pushAnyUnSyncedData(),
      );

      // Attempt data sync periodically so that the listeners remain active and updated.
      EasyThrottle.throttle(
        dataSyncAttemptThrottleTag,
        dataSyncAttemptThrottleDuration,
        () => startSyncAgain(false),
      );

      // Check if FCM initialised.
      EasyThrottle.throttle(
        fcmStatusCheckThrottleTag,
        fcmStatusCheckThrottleDuration,
        () {
          if (state.userData != null) {
            if (!isFCMInitialised) {
              _firebaseMessagingRepository.init();
            }
          }
        },
      );

      // Update user properties group.
      EasyThrottle.throttle(
        userPropUpdateThrottleTag,
        userPropUpdateThrottleDuration,
        () {
          EffectiveSubscriptionInfo? effectiveInfo;
          if (state.userData != null && state.userMetadata != null) {
            effectiveInfo =
                EffectiveSubscriptionHelper.getEffectiveSubscription(
              userData: state.userData!,
              userMetadata: state.userMetadata,
            );
          }
          BaseGroupManager().setData(
            language: state.viewSettings?.appSettings.language.toTextString(),
            userPlan: effectiveInfo?.rSubscriptionInfo.entitlement.name,
            userType: state.showWelcomeScreen == true
                ? UserType.newUser
                : UserType.existingUser,
            muid: state.userData?.muid,
          );
        },
      );

      // Commenting as theme getting updated from widget as wrong. So for now
      // just updating it every few seconds.
      EasyThrottle.throttle(
        widgetThemeUpdateThrottleTag,
        widgetThemeUpdateThrottleDuration,
        () async {
          final homeWidgetUtility = HomeWidgetUtility(
            databaseRepository: _databaseRepository,
          );
          homeWidgetUtility.changeHomeWidgetLanguage(state.appLanguage.locale);
          homeWidgetUtility.updateHomeWidget();

          // Save current app language to SharedPreferences for notifications and other services
          await _sharedPreferencesClient.initSharedPrefs();
          await _sharedPreferencesClient
              .setCurrentAppLocale(state.appLanguage.languageCode);
        },
      );
    });
  }

  bool get isFCMInitialised => _firebaseMessagingRepository.inInitialized;

  Future<void> _onFirebaseUserChanged(
    FirebaseUserChanged event,
    Emitter<AppState> emit,
  ) async {
    _log.i('on Firebase User Changed');
    add(const ConnectionStatusChanged(isConnected: true));
    AppConfig.instance.userId = event.user?.uid;
    if (event.user == null) {
      await _sharedPreferencesClient.removeLoggedInUserId();
      _appBlocStateEmitter(
        AppState.unauthenticated(
          isForcedLogOut: state.isForcedLogOut,
          isUserDeleted: isUserDeleted,
          isClickedForgotPin: state.isClickedForgotPin,
          previouslyLoggedInEmail:
              state.appAuthStatus == AppStatus.unauthenticated
                  ? state.previouslyLoggedInEmail
                  : state.userData != null
                      ? state.userData!.userInfo.email
                      : '',
          appDateTimeState: AppDateTimeState(
            deviceDateTime: _getCurrentDateTimeInUtc,
            networkDateTime: await getCurrentNetworkDateTimeInUtc(),
            deviceTimezone: _getCurrentTimezone,
            deviceTimezoneName: DateTime.now().timeZoneName,
            deviceTimezoneLocation: await _getCurrentTimezoneLocation,
          ),
          appUpdateState: state.appUpdateState,
          updateReleaseConfig: state.updateReleaseConfig,
          isUpdateSkipped: state.isUpdateSkipped,
          appLanguage: state.getAppLanguage,
        ),
        emit,
      );
    } else {
      final currentUser = event.user!;
      await Future.wait([
        if (!MePlatform.isWindows)
          CrashlyticsRepository.setCrashlyticsUserIdentifier(currentUser.uid),
        _databaseRepository.openDriftDb(dbType: DbType.main),
      ]);
      _sharedPreferencesClient.updateUid(currentUser.uid);
      await _sharedPreferencesClient.setLoggedInUserId(currentUser.uid);
      _appBlocStateEmitter(
        state.copyWith(user: currentUser),
        emit,
      );
      if (state.loginProcessStatus == LoginProcessStatus.none) {
        add(
          const AppLoginProcessStatusChanged(
            appLoginProcessStatus: LoginProcessStatus.loginCompleted,
          ),
        );
      }

      await HomeWidgetService.saveWidgetData(
        'is_logged_in',
        true,
      );
    }
    await HomeWidgetService.updateWidget(
      name: HomeWidgetUtility.getQualifiedAndroidName(),
      iOSName: 'HomeWidget',
      qualifiedAndroidName: HomeWidgetUtility.getQualifiedAndroidName(),
    );
  }

  Future<void> _listenForFirebaseUser() async {
    await _firebaseUserSubscription?.cancel();
    final userStream = _authenticationRepository.user;
    _firebaseUserSubscription = userStream.listen((user) {
      add(FirebaseUserChanged(user: user));
    });
  }

  Future<void> listenForCloudReleaseConfig() async {
    _dataSyncService?.startSyncingReleaseConfig();
  }

  Future<void> _onAppLoginProcessStatusChanged(
    AppLoginProcessStatusChanged event,
    Emitter<AppState> emit,
  ) async {
    _log.i('on app login process status changed');

    User? loggedInUser = state.user ?? event.user;
    bool isLoginCompleteEvent = event.appLoginProcessStatus ==
            LoginProcessStatus.loginCompleted ||
        event.appLoginProcessStatus == LoginProcessStatus.freshLoginCompleted;
    bool isAppInLoggedState =
        state.loginProcessStatus == LoginProcessStatus.loginCompleted ||
            state.loginProcessStatus == LoginProcessStatus.freshLoginCompleted;

    if (loggedInUser == null && isLoginCompleteEvent) {
      _log.e(
        'Logged in user is null but received successful login event. This should never happen so stopping such event from proceeding.',
      );
      return Future.value();
    }

    _appBlocStateEmitter(
      state.copyWith(
        loginProcessStatus: event.appLoginProcessStatus,
      ),
      emit,
    );

    bool isSuccessfulLoginEvent =
        loggedInUser != null && isLoginCompleteEvent && !isAppInLoggedState;

    if (isSuccessfulLoginEvent) {
      // As user is logged in, we can start the firebase user stream again.
      await _listenForFirebaseUser();
      // This is a new login event so if old data sync service is running, close it first
      // before starting a new one.
      if (_dataSyncService != null) {
        await _dataSyncService?.closeAll();
      }
      _dataSyncService = DataSyncService(
        appBloc: this,
        sharedPreferencesClient: _sharedPreferencesClient,
        firebaseFunctionsRepository: _firebaseFunctionsRepository,
        databaseRepository: _databaseRepository,
        notificationsRepository: NotificationService(),
        releaseConfigCubit: _releaseConfigCubit,
      );
      _firebaseMessagingRepository.init();
      String? loggedInUserId = _sharedPreferencesClient.getLoggedInUserId();
      if (loggedInUserId == null) {
        loggedInUserId = loggedInUser.uid;
        _sharedPreferencesClient.setLoggedInUserId(loggedInUserId);
      }
      _log.i('Logged in with user id: $loggedInUserId. Starting data sync.');
      _dataSyncService?.startMigrationAndSync(
        loggedInUser,
        event.appLoginProcessStatus == LoginProcessStatus.freshLoginCompleted,
        false,
      );
    }
  }

  final List<DataSyncServiceStatusChanged> _statusQueue = [];
  bool _processingStatus = false;

  // Handle the data sync service status change process.
  Future<void> updateDataSyncServiceStatus(
    DataSyncServiceStatusChanged event,
  ) async {
    void addEvent(DataSyncServiceStatusChanged event) {
      debugPrint('Updating to DataSyncService event: $event');
      add(event);
      if (event.dataSyncStatus == DataSyncStatus.forceSyncRequired) {
        HomeWidgetUtility.updateDateMigrationStatus(
          migrating: false,
          migrationRequired: true,
        );
      } else if (event.dataSyncStatus == DataSyncStatus.forceSyncInProgress) {
        HomeWidgetUtility.updateDateMigrationStatus(
          migrating: true,
          migrationRequired: true,
        );
      } else {
        HomeWidgetUtility.updateDateMigrationStatus(
          migrating: false,
          migrationRequired: false,
        );
      }
    }

    // Add the new event to the queue.
    _statusQueue.add(event);

    // If already processing, exit; otherwise, start processing.
    if (_processingStatus) return;

    _processingStatus = true;

    while (_statusQueue.isNotEmpty) {
      final currentEvent = _statusQueue.removeAt(0);

      // If we're moving from forceSyncInProgress to either forceSyncComplete or backgroundSyncRunning,
      // we show forceSyncComplete for a bit, then move on to the next status.
      if ((currentEvent.dataSyncStatus == DataSyncStatus.forceSyncComplete ||
              currentEvent.dataSyncStatus ==
                  DataSyncStatus.backgroundSyncRunning) &&
          state.dataSyncStatus == DataSyncStatus.forceSyncInProgress) {
        _log.i(
          'DataSyncStatus change needs completion path. Past status: '
          '${state.dataSyncStatus}, New status: ${currentEvent.dataSyncStatus} so following completion path.',
        );

        // Show forceSyncComplete status first for the animation.
        addEvent(
          const DataSyncServiceStatusChanged(
            dataSyncStatus: DataSyncStatus.forceSyncComplete,
          ),
        );
        _log.d(
          'Updating status to forceSyncComplete to show the completion animation.',
        );

        // Delay to allow animation to complete.
        await Future.delayed(const Duration(seconds: 3));

        // After delay, _appBlocStateEmitter the actual intended status if it's different.
        if (currentEvent.dataSyncStatus != DataSyncStatus.forceSyncComplete) {
          addEvent(currentEvent);
          _log.d(
            'Updating status to new status: ${currentEvent.dataSyncStatus} after forceSyncComplete completion animation.',
          );
        } else {
          _log.d(
            'Remaining on forceSyncComplete status after completion animation as no new different status to show.',
          );
        }
      } else {
        // Otherwise, just _appBlocStateEmitter the status change.
        if (!isClosed) {
          addEvent(currentEvent);
        }
      }
    }

    // Mark processing as complete.
    _processingStatus = false;
  }

  /// Note: Don't use this directly, use [updateDataSyncServiceStatus] instead
  /// as that handles the completion path status for you.
  /// Only in exceptional case this might be used.
  FutureOr<void> _onDataSyncServiceStatusChanged(
    DataSyncServiceStatusChanged event,
    Emitter<AppState> emit,
  ) {
    _log.i('on data sync service status changed: ${event.dataSyncStatus}');
    if (event.dataSyncStatus == DataSyncStatus.backgroundSyncRunning ||
        event.userData != null) {
      _appBlocStateEmitter(
        state.copyWith(
          appAuthStatus: AppStatus.authenticated,
          userData: event.userData,
          viewSettings: event.viewSettings,
          dataSyncStatus: event.dataSyncStatus,
        ),
        emit,
      );
    } else {
      _appBlocStateEmitter(
        state.copyWith(dataSyncStatus: event.dataSyncStatus),
        emit,
      );
    }
  }

  Future<void> _onLocalUserDataChanged(
    LocalUserDataChanged event,
    Emitter<AppState> emit,
  ) async {
    _log.i('on local user data changed');

    if (event.userData != null) {
      _appBlocStateEmitter(
        state.copyWith(userData: event.userData),
        emit,
      );
    }
  }

  Future<void> _onLocalUserResourcesChanged(
    LocalUserResourceChanged event,
    Emitter<AppState> emit,
  ) async {
    _log.i('on local user resources changed');
    if (event.userResources != null) {
      _appBlocStateEmitter(
        state.copyWith(userResources: event.userResources),
        emit,
      );
    }
  }

  Future<void> _onLocalUserMetadataChanged(
    LocalUserMetadataChanged event,
    Emitter<AppState> emit,
  ) async {
    _log.i('on local user metadata changed');
    if (event.userMetadata != null) {
      _appBlocStateEmitter(
        state.copyWith(userMetadata: event.userMetadata),
        emit,
      );
    }
  }

  Future<void> _onSpecialActivitiesChanged(
    SpecialActivitiesChanged event,
    Emitter<AppState> emit,
  ) async {
    _log.i('on special activities changed');
    _appBlocStateEmitter(
      state.copyWith(
        removedDocsIDs: DocInfo.fromSpecialActivities(event.specialActivities),
      ),
      emit,
    );

    // Check for deleted item ids and remove them from the local storage.
    await Future.delayed(const Duration(seconds: 3));
    EasyDebounce.debounce(
      'special_activities_debounce',
      const Duration(seconds: 5),
      () {
        _log.i('Removing docs from local db');
        UtilityMethods.removeDocsFromLocalDB(
          state.removedDocsIDs,
          _databaseRepository,
        );
      },
    );
  }

  Future<void> _onLocalViewSettingsChanged(
    LocalViewSettingsChanged event,
    Emitter<AppState> emit,
  ) async {
    _log.i('on local view settings changed');
    if (event.viewSettings != null) {
      _onAppLanguageChanged(
        AppLanguageChanged(
          locale: event.viewSettings!.appSettings.language.locale,
        ),
        emit,
      );

      // Update view settings to use shared preferences language if language available in shared preferences.
      String? sharedPrefAppLanguage =
          _sharedPreferencesClient.getTemporaryAppLanguage();
      if (event.viewSettings != null &&
          sharedPrefAppLanguage != null &&
          event.useSharedPerfAppLanguageIfAvailable) {
        // Doing a database update. This update can be lazy and once it's successful
        // state will get updated based by listners.
        updateViewSettings(
          viewSettings: event.viewSettings!.copyWith(
            appSettings: event.viewSettings!.appSettings.copyWith(
              language: state.getAppLanguage,
              // supportLanguage: state.getAppLanguage,
            ),
          ),
        ).whenComplete(() {
          // Then set the shared preferences language to null as once set on account
          // we don't need it in shared preferences.
          _sharedPreferencesClient.setAppLanguageTemporarily(null);
        });
      }

      final homeWidgetUtility = HomeWidgetUtility(
        databaseRepository: _databaseRepository,
      );
      homeWidgetUtility.updateHomeWidget();

      _appBlocStateEmitter(
        state.copyWith(viewSettings: event.viewSettings),
        emit,
      );
    }
  }

  Future<void> _onLocalCalendarIntegrationChanged(
    LocalCalendarIntegrationChanged event,
    Emitter<AppState> emit,
  ) async {
    _log.i('on local calendar integration changed');
    if (event.calendarIntegration != null) {
      _appBlocStateEmitter(
        state.copyWith(calendarIntegrations: event.calendarIntegration),
        emit,
      );
    }
  }

  FutureOr<void> _onPasscodeStatusChanged(
    PasscodeStatusChanged event,
    Emitter<AppState> emit,
  ) {
    _log.i('on passcode status changed');
    _appBlocStateEmitter(
      state.copyWith(
        userData: event.userData,
        passcodeStatus: event.passcodeStatus,
      ),
      emit,
    );
  }

  Future<void> cleanupAfterUnauthenticatedState() async {
    try {
      AppConfig.instance.updateAuthUserDocVerAndSegments(
        const Nullable(null),
        const Nullable(null),
      );
    } catch (e) {
      _log.w(
        '[Logout Cleanup]: Error while updating auth user doc version: $e',
      );
    }

    // Create a list of futures where each future handles its own errors
    final futures = <Future<void>>[];

    if (!MePlatform.isWeb && !MePlatform.isWindows) {
      futures.add(
        NotificationService().removeAllNotifications().catchError((e) {
          _log.w(
            '[Logout Cleanup]: Error while removing all notifications: $e',
          );
        }),
      );
    }

    // Make sure widget data is reset regardless of network status
    futures.add(
      HomeWidgetService.resetHomeWid().catchError((e) {
        _log.w('[Logout Cleanup]: Error while resetting home widget: $e');
      }),
    );

    futures.addAll([
      _databaseRepository.cleanDatabase().catchError((e) {
        _log.w('[Logout Cleanup]: Error while cleaning database: $e');
      }),
      _authenticationRepository.logOut().catchError((e) {
        _log.w('[Logout Cleanup]: Error while logging out: $e');
      }),
      _storageRepository.clearSyncFolder().catchError((e) {
        _log.w('[Logout Cleanup]: Error while clearing sync folder: $e');
      }),
      MeEncryption().reset().catchError((e) {
        _log.w('[Logout Cleanup]: Error while resetting encryption: $e');
      }),
      MeLogger.resetLogs().catchError((e) {
        _log.w('[Logout Cleanup]: Error while resetting logs: $e');
      }),
    ]);

    // Wait for all operations to complete
    await Future.wait(futures);

    try {
      final homeWidgetUtility =
          HomeWidgetUtility(databaseRepository: _databaseRepository);
      homeWidgetUtility.changeHomeWidgetLanguage(state.appLanguage.locale);
      homeWidgetUtility.updateHomeWidget();
    } catch (e) {
      _log.w('[Logout Cleanup]: Error while sending data to home widget: $e');
    }

    try {
      BaseGroupManager().resetData();
    } catch (e) {
      _log.w(
        '[Logout Cleanup]: Error while resetting user properties group: $e',
      );
    }

    try {
      await _releaseConfigCubit.initReleaseConfig(_databaseRepository);
    } catch (e) {
      _log.w('[Logout Cleanup]: Error while initializing release config: $e');
    }

    try {
      DatabaseRepository.currentDbVersion = AppConfig.dbVersion;
      await _sharedPreferencesClient
          .setCurrentSqlDatabaseVersion(AppConfig.dbVersion);
    } catch (e) {
      _log.w(
        '[Logout Cleanup]: Error while setting current SQL database version: $e',
      );
    }
  }

  Future<void> _onLogoutRequested(
    AppLogoutRequested event,
    Emitter<AppState> emit,
  ) async {
    try {
      _log.i('on Logout requested');
      isLogout = true;

      // Immediately update the widget state to prevent widget from showing user data when offline
      try {
        await HomeWidgetService.resetHomeWid();
      } catch (e) {
        _log.e('logOut: Error resetting home widget: $e');
      }

      // Handle notification timers with timeout
      if (state.user != null) {
        try {
          await NotificationActionsManager()
              .stopAllRunningTimersOfThisDevice(
            uid: state.user!.uid,
          )
              .timeout(
            const Duration(seconds: 3),
            onTimeout: () {
              _log.w(
                'logOut: Stopping notification timers timed out, continuing with logout',
              );
              return;
            },
          );
        } catch (e) {
          _log.e('logOut: Error stopping notification timers: $e');
        }
      }

      // Handle operations that might require network with timeouts
      List<Future<void>> futures = [];

      if (_firebaseUserSubscription != null) {
        futures.add(_firebaseUserSubscription!.cancel());
      }

      if (_dataSyncService != null) {
        futures.add(
          _dataSyncService!.closeAll().timeout(
            const Duration(seconds: 3),
            onTimeout: () {
              _log.w(
                'logOut: Closing data sync service timed out, continuing with logout',
              );
              return;
            },
          ),
        );
      }

      futures.addAll([
        _sharedPreferencesClient.setPasscodeAttempts(
          ReleaseConfig.instance.maxPasscodeAttempts,
        ),
        _sharedPreferencesClient.removeDailyAgendaSyncAt(),
        _sharedPreferencesClient.removeLoggedInUserId(),
        _sharedPreferencesClient.removeWasSubsBasicLastTime(),
      ]);

      if (MePlatform.isIOS) {
        futures.add(
          _firebaseMessagingRepository.stopService().timeout(
            const Duration(seconds: 3),
            onTimeout: () {
              _log.w(
                'logOut: Stopping Firebase messaging service timed out, continuing with logout',
              );
              return;
            },
          ),
        );
      }

      try {
        await Future.wait(futures);
      } catch (e) {
        _log.e('logOut: Error in first batch of logout operations: $e');
      }

      _dataSyncService = null;

      try {
        await Future.wait([
          if (!isUserDeleted) ...[
            _sharedPreferencesClient.setAppLanguageTemporarily(null),
          ],
          if (isUserDeleted) ...[
            _sharedPreferencesClient
                .setAppLanguageTemporarily(state.getAppLanguage.toTextString()),
          ],
          _sharedPreferencesClient.setAppThemeType(null),
          _sharedPreferencesClient.setAppThemeColor(null),
          _sharedPreferencesClient.setUserActionCount(0),
          _sharedPreferencesClient.setAdShownDate(null),
          _sharedPreferencesClient.setMuteAdsDialogForFewDays(null),
          _sharedPreferencesClient.setAdsSecurityPassSessionId(null),
          _sharedPreferencesClient.setCurrentAppLocale(null),
          _sharedPreferencesClient.clearViewedState(),
        ]);
      } catch (e) {
        _log.e('logOut: Error in second batch of logout operations: $e');
      }

      try {
        ForegroundService().stopService();
      } catch (e) {
        _log.e('logOut: Error stopping foreground service: $e');
      }
    } catch (e) {
      _log.e('logOut: Error while cleaning before logging out: $e');
    } finally {
      // Make sure we update the app state to unauthenticated regardless of any errors
      try {
        _appBlocStateEmitter(
          AppState.unauthenticated(
            isForcedLogOut: event.isForcedLogout,
            isUserDeleted: isUserDeleted,
            isClickedForgotPin: event.isClickedForgotPin,
            previouslyLoggedInEmail:
                state.appAuthStatus == AppStatus.unauthenticated
                    ? state.previouslyLoggedInEmail
                    : state.userData != null
                        ? state.userData!.userInfo.email
                        : '',
            appDateTimeState: AppDateTimeState(
              deviceDateTime: _getCurrentDateTimeInUtc,
              networkDateTime: await getCurrentNetworkDateTimeInUtc().timeout(
                const Duration(seconds: 2),
                onTimeout: () => _getCurrentDateTimeInUtc,
              ),
              deviceTimezone: _getCurrentTimezone,
              deviceTimezoneName: DateTime.now().timeZoneName,
              deviceTimezoneLocation: await _getCurrentTimezoneLocation,
            ),
            appUpdateState: state.appUpdateState,
            updateReleaseConfig: state.updateReleaseConfig,
            isUpdateSkipped: state.isUpdateSkipped,
            appLanguage: state.getAppLanguage,
          ),
          emit,
        );
      } catch (e) {
        _log.e('logOut: Error emitting unauthenticated state: $e');
        // Last resort - emit a basic unauthenticated state
        _appBlocStateEmitter(
          AppState.unauthenticated(
            appDateTimeState: AppDateTimeState(
              deviceDateTime: _getCurrentDateTimeInUtc,
              networkDateTime: _getCurrentDateTimeInUtc,
              deviceTimezone: _getCurrentTimezone,
              deviceTimezoneName: DateTime.now().timeZoneName,
              deviceTimezoneLocation: '',
            ),
            appUpdateState: state.appUpdateState,
            appLanguage: state.getAppLanguage,
          ),
          emit,
        );
      }

      try {
        await cleanupAfterUnauthenticatedState();
      } catch (e) {
        _log.e('logOut: Error in cleanup after unauthenticated state: $e');
      }

      // Final attempt to reset the widget
      try {
        await HomeWidgetService.resetHomeWid();
      } catch (e) {
        _log.e('logOut: Error in final widget reset: $e');
      }
    }
  }

  @override
  Future<void> close() {
    _timer.cancel();
    _firebaseUserSubscription?.cancel();
    _internetConnectionSubscription?.cancel();
    _dataSyncService?.closeAll();
    _appLifecycleListener.dispose();
    return super.close();
  }

  void changeAppLifecycleState(AppLifecycleState state) {
    _log.i('Lifecycle State change event: ${state.name}');
    if (state == AppLifecycleState.paused) {
      appInBgTimeStamp = DateTime.now().millisecondsSinceEpoch;
    }
    add(AppLifecycleStateChanged(appLifecycleState: state));
  }

  FutureOr<void> _onAppLifecycleStateChanged(
    AppLifecycleStateChanged event,
    Emitter<AppState> emit,
  ) {
    _appBlocStateEmitter(
      state.copyWith(appLifecycleState: event.appLifecycleState),
      emit,
    );
    listenForInternetConnectionChange(
      appLifecycleState: event.appLifecycleState,
    );
  }

  void passcodeVerified(UserData userData) {
    add(
      PasscodeStatusChanged(
        userData: userData,
        passcodeStatus: PasscodeStatus.passcodeVerified,
      ),
    );
  }

  Future<void> updateUser({
    required UserData user,
  }) async {
    return _databaseRepository.saveUser(
      user: user,
    );
  }

  Future<void> updateViewSettings({
    required ViewSettings viewSettings,
    String? colorCode,
    ThemeType? themeType,
  }) async {
    add(
      AppLanguageChanged(
        locale: viewSettings.appSettings.language.locale,
      ),
    );
    return _databaseRepository.saveViewSettings(viewSettings: viewSettings);
  }

  void toggleViewType([TodayViewType? forceSetToViewType]) {
    TodayViewType? currentViewType =
        state.viewSettings?.featureSettings.viewType;
    TodayViewType? viewType;
    bool? showTimebox;
    bool? showFeatureLabels;

    if (forceSetToViewType != null) {
      viewType = forceSetToViewType;
    } else {
      viewType = currentViewType == TodayViewType.chronological
          ? TodayViewType.category
          : TodayViewType.chronological;
    }

    if (viewType == TodayViewType.category) {
      showTimebox = false;
      showFeatureLabels = false;
    }

    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: state.viewSettings!.featureSettings.copyWith(
          viewType: viewType,
          showTimebox: showTimebox,
          showFeatureLabels: showFeatureLabels,
        ),
      ),
    );
  }

  void toggleTimebox() {
    bool? showTimebox;
    if (state.viewSettings?.featureSettings.showTimebox == true) {
      showTimebox = false;
    } else {
      showTimebox = true;
    }
    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: state.viewSettings!.featureSettings
            .copyWith(showTimebox: showTimebox),
      ),
    );
  }

  void toggleUserGoalVisibility() {
    bool? showUserGoal;
    if (state.viewSettings?.featureSettings.showUserGoal == true) {
      showUserGoal = false;
    } else {
      showUserGoal = true;
    }
    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: state.viewSettings!.featureSettings
            .copyWith(showUserGoal: showUserGoal),
      ),
    );
  }

  bool toggleTodoFeature() {
    bool showTodoFeature;
    if (state.viewSettings?.featureSettings.showTodoFeature == true) {
      showTodoFeature = false;
    } else {
      showTodoFeature = true;
    }
    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: state.viewSettings!.featureSettings
            .copyWith(showTodoFeature: showTodoFeature),
      ),
    );

    return showTodoFeature;
  }

  void toggleListFeature() {
    bool? showListFeature;
    if (state.viewSettings?.featureSettings.showListFeature == true) {
      showListFeature = false;
    } else {
      showListFeature = true;
    }
    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: state.viewSettings!.featureSettings
            .copyWith(showListFeature: showListFeature),
      ),
    );
  }

  bool toggleNoteFeature() {
    bool showNoteFeature;
    if (state.viewSettings?.featureSettings.showNoteFeature == true) {
      showNoteFeature = false;
    } else {
      showNoteFeature = true;
    }
    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: state.viewSettings!.featureSettings
            .copyWith(showNoteFeature: showNoteFeature),
      ),
    );

    return showNoteFeature;
  }

  void toggleCalendarViewVisibility() {
    bool? showCalendarView;
    if (state.viewSettings?.featureSettings.showCalendarView == true) {
      showCalendarView = false;
    } else {
      showCalendarView = true;
    }
    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: state.viewSettings!.featureSettings
            .copyWith(showCalendarView: showCalendarView),
      ),
    );
  }

  void toggleCompletedItemsVisibility() {
    bool? hideCompletedItems;
    if (state.viewSettings?.featureSettings.hideCompletedItems == true) {
      hideCompletedItems = false;
    } else {
      hideCompletedItems = true;
    }
    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: state.viewSettings!.featureSettings
            .copyWith(hideCompletedItems: hideCompletedItems),
      ),
    );
  }

  void toggleFeatureLabelsVisibility() {
    bool? showFeatureLabels;
    if (state.viewSettings?.featureSettings.showFeatureLabels == true) {
      showFeatureLabels = false;
    } else {
      showFeatureLabels = true;
    }
    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: state.viewSettings!.featureSettings
            .copyWith(showFeatureLabels: showFeatureLabels),
      ),
    );
  }

  bool isItemHidden(String itemId, TaskType type) {
    final featureSettings = state.viewSettings?.featureSettings;
    if (featureSettings == null) return false;

    switch (type) {
      case TaskType.habit:
        return featureSettings.hiddenHabits.containsKey(itemId);
      case TaskType.journal:
        return featureSettings.hiddenJournals.containsKey(itemId);
      case TaskType.moneyTracker:
        return featureSettings.hiddenMoneytrackers.containsKey(itemId);
      case TaskType.calendar:
        return featureSettings.hiddenCalendars.containsKey(itemId);
      default:
        return false;
    }
  }

  bool toggleItemVisibility(String itemId, TaskType type) {
    final featureSettings = state.viewSettings?.featureSettings;
    if (featureSettings == null) return false;

    bool isHidden;
    FeatureSettings newFeatureSettings;

    switch (type) {
      case TaskType.habit:
        final hiddenHabits =
            Map<String, DateTime>.from(featureSettings.hiddenHabits);
        if (hiddenHabits.containsKey(itemId)) {
          hiddenHabits.remove(itemId);
          isHidden = false;
        } else {
          hiddenHabits[itemId] = DateTime.now();
          isHidden = true;
        }
        newFeatureSettings =
            featureSettings.copyWith(hiddenHabits: hiddenHabits);
        break;
      case TaskType.journal:
        final hiddenJournals =
            Map<String, DateTime>.from(featureSettings.hiddenJournals);
        if (hiddenJournals.containsKey(itemId)) {
          hiddenJournals.remove(itemId);
          isHidden = false;
        } else {
          hiddenJournals[itemId] = DateTime.now();
          isHidden = true;
        }
        newFeatureSettings =
            featureSettings.copyWith(hiddenJournals: hiddenJournals);
        break;
      case TaskType.moneyTracker:
        final hiddenMoneytrackers =
            Map<String, DateTime>.from(featureSettings.hiddenMoneytrackers);
        if (hiddenMoneytrackers.containsKey(itemId)) {
          hiddenMoneytrackers.remove(itemId);
          isHidden = false;
        } else {
          hiddenMoneytrackers[itemId] = DateTime.now();
          isHidden = true;
        }
        newFeatureSettings =
            featureSettings.copyWith(hiddenMoneytrackers: hiddenMoneytrackers);
        break;
      case TaskType.calendar:
        final hiddenCalendars =
            Map<String, DateTime>.from(featureSettings.hiddenCalendars);
        if (hiddenCalendars.containsKey(itemId)) {
          hiddenCalendars.remove(itemId);
          isHidden = false;
        } else {
          hiddenCalendars[itemId] = DateTime.now();
          isHidden = true;
        }
        newFeatureSettings =
            featureSettings.copyWith(hiddenCalendars: hiddenCalendars);
        break;
      default:
        isHidden = false;
        newFeatureSettings = featureSettings;
    }

    updateViewSettings(
      viewSettings: state.viewSettings!.copyWith(
        featureSettings: newFeatureSettings,
      ),
    );

    return isHidden;
  }

  FutureOr<void> _onConnectionStatusChanged(
    ConnectionStatusChanged event,
    Emitter<AppState> emit,
  ) {
    _log.i('on connection status changed with event: ${event.isConnected}');
    if (state.user != null &&
        state.appAuthStatus == AppStatus.authenticated &&
        state.internetConnectionState !=
            (event.isConnected
                ? InternetConnectionState.connected
                : InternetConnectionState.disconnected)) {
      _dataSyncService?.onInternetConnectionChanged(event.isConnected);
    }
    _appBlocStateEmitter(
      state.copyWith(
        internetConnectionState: event.isConnected
            ? InternetConnectionState.connected
            : InternetConnectionState.disconnected,
      ),
      emit,
    );
  }

  FutureOr<void> _onAppUpdateStateChanged(
    AppUpdateStatusChanged event,
    Emitter<AppState> emit,
  ) {
    if (state.appUpdateState != event.appUpdateState) {
      _dataSyncService?.onAppUpdateStateChanged(
        state.user,
        state.userData,
        event.appUpdateState,
      );
    }
    _appBlocStateEmitter(
      state.copyWith(
        appUpdateState: event.appUpdateState,
        updateReleaseConfig: event.updateReleaseConfig,
        colorsVersion: event.colorsVersion,
        translationsVersion: event.translationsVersion,
        isUpdateSkipped: event.isUpdateSkipped,
        currentRCUpdatedAt: event.currectRCUpdatedAt,
      ),
      emit,
    );
    HomeWidgetUtility.updateUpgradeRequiredStatus(
      updateRequired:
          event.appUpdateState == AppUpdateState.mandatoryUpdateWithoutSkip ||
              event.appUpdateState == AppUpdateState.downgradeUpdate,
    );
  }

  void listenForInternetConnectionChange({
    AppLifecycleState appLifecycleState = AppLifecycleState.resumed,
  }) {
    if (state.appAuthStatus != AppStatus.authenticated) {
      return;
    }
    if (appLifecycleState == AppLifecycleState.hidden) {
      add(const ConnectionStatusChanged(isConnected: true));
    } else if (appLifecycleState == AppLifecycleState.resumed) {
      _log.i('Listening for internet connection');
      _databaseEventSubscription?.cancel();
      DatabaseReference ref =
          _firebaseDatabaseRepository.firebaseDatabase.ref('app');
      _databaseEventSubscription = ref.onValue.listen((event) {
        var val = event.snapshot.value;
        _log.d('Realtime Database triggered with value : $val');
      });
      _databaseEventSubscription?.onError((error) {});
      _internetConnectionSubscription?.cancel();
      _internetConnectionSubscription = InternetConnectivityService
          .instance.connectivityStream
          .listen((isConnected) {
        add(ConnectionStatusChanged(isConnected: isConnected));
      });
    }
  }

  FutureOr<void> _onAppUserDeleted(
    AppUserDeleted event,
    Emitter<AppState> emit,
  ) {
    _log.d('Event Received User Deleted: ${event.isUserDeleted}');
    isUserDeleted = event.isUserDeleted;
    _appBlocStateEmitter(
      state.copyWith(isUserDeleted: event.isUserDeleted),
      emit,
    );
  }

  FutureOr<void> _onUpdateAppDateTimeState(
    AppDateTimeStateChanged event,
    Emitter<AppState> emit,
  ) {
    final updatedState =
        state.copyWith(appDateTimeState: event.appDateTimeState);
    _appBlocStateEmitter(
      updatedState,
      emit,
    );
    _updateTimeZoneInViewSettings();
  }

  FutureOr<void> _onAppLanguageChanged(
    AppLanguageChanged event,
    Emitter<AppState> emit,
  ) {
    final updatedState = state.copyWith();
    _appBlocStateEmitter(
      updatedState,
      emit,
    );

    // Always update home widget translations when language changes, regardless of login state
    try {
      final homeWidgetUtility = HomeWidgetUtility(
        databaseRepository: _databaseRepository,
      );
      homeWidgetUtility.changeHomeWidgetLanguage(event.locale);
    } catch (e) {
      _log.w('Error updating home widget language: $e');
    }
  }

  FutureOr<void> _onTranslationsVersionChanged(
    TranslationsVersionChanged event,
    Emitter<AppState> emit,
  ) {
    final updatedState =
        state.copyWith(translationsVersion: event.translationsVersion);
    _appBlocStateEmitter(
      updatedState,
      emit,
    );
  }

  FutureOr<void> _onColorsVersionChanged(
    ColorsVersionChanged event,
    Emitter<AppState> emit,
  ) {
    final updatedState = state.copyWith(colorsVersion: event.colorsVersion);
    _appBlocStateEmitter(
      updatedState,
      emit,
    );
  }

  FutureOr<void> _onResetMevolvePinResetFlowOnAction(
    ResetMevolvePinResetFlowOnAction event,
    Emitter<AppState> emit,
  ) {
    _appBlocStateEmitter(
      state.copyWith(
        isClickedForgotPin: event.isClickedForgotPin,
        isForcedLogOut: event.isForcedLogout,
        previouslyLoggedInEmail: event.previouslyLoggedInEmail,
      ),
      emit,
    );
  }

  FutureOr<void> _onPasscodePageDisposed(
    PasscodePageDisposedEvent event,
    Emitter<AppState> emit,
  ) {
    _appBlocStateEmitter(
      state.copyWith(passcodePageDisposed: event.passcodePageDisposed),
      emit,
    );
  }

  FutureOr<void> _onMediaDataSyncChanged(
    MediaDataSyncChanged event,
    Emitter<AppState> emit,
  ) {
    _appBlocStateEmitter(
      state.copyWith(
        mediaSyncLeft: event.syncLen,
        totalMediaToSync: event.totalMediaToSync,
      ),
      emit,
    );
  }

  FutureOr<void> _onWelcomeWidgetsStatusChanged(
    WelcomeWidgetsStatusChanged event,
    Emitter<AppState> emit,
  ) {
    _appBlocStateEmitter(
      state.copyWith(
        showWelcomeScreen: Nullable(event.showWelcomeScreen),
        showWelcomeUpgradeDialog: Nullable(event.showWelcomeUpgradeDialog),
      ),
      emit,
    );
  }

  void retryOrStartSync() {
    if (state.user == null) return;
    _log.i('retrying migration screen sync process');

    // Even tho we are triggering migration. [startMigrationAndSync] will first
    // Decide if it's required or not. So, this can take few milliseconds to decide.
    // That's why we are setting the status to forceSyncInProgress before calling the function.
    updateDataSyncServiceStatus(
      const DataSyncServiceStatusChanged(
        dataSyncStatus: DataSyncStatus.forceSyncInProgress,
      ),
    );

    _dataSyncService?.startMigrationAndSync(
      state.user,
      false,
      true,
    );
  }

  void startSyncAgain([bool isManualMigration = true]) {
    if (state.user == null) return;
    _log.i('Starting start migration and sync service again');

    _dataSyncService?.startMigrationAndSync(
      state.user,
      false,
      isManualMigration,
    );
  }

  Future<void> pushAnyUnSyncedData() async {
    if (state.user == null) return;
    _log.i('Attempting to push any unsynced data');
    await _dataSyncService?.pushAnyUnSyncedData();
  }

  Future<void> fetchPaginatedInAppNotifications({
    required int limit,
    DateTime? lastCreatedAt,
  }) async {
    if (state.user == null) return;
    _log.i('Fetching paginated in-app notifications with limit $limit');
    await _dataSyncService?.fetchPaginatedInAppNotifications(
      uid: state.user!.uid,
      limit: limit,
      lastCreatedAt: lastCreatedAt,
    );
  }

  Future<void> deleteAllUserData(BuildContext context) async {
    await FirebaseFunctionsRepository().deleteUserData(state.userData!.uid);
  }

  // All state goes through this method before being emitted.
  // We can do any final processing here before emitting the state.
  void _appBlocStateEmitter(
    AppState state,
    Emitter<AppState> emit,
  ) {
    // Sets the new user flag based on isNewUser OR name in db status.
    // Note: The user name not present helps in case the authentication provider
    // didn't provide isNewUser flag.
    bool isUserNamePresent =
        (state.userData?.userInfo.name ?? '').trim().isEmpty;
    bool? isNewUser = state.appAuthStatus != AppStatus.authenticated
        ? null
        : _authenticationRepository.isNewUser || isUserNamePresent;

    state = state.copyWith(
      appLanguage: state.getAppLanguage,
      isNewUser: Nullable(isNewUser),
      showWelcomeScreen: Nullable(state.showWelcomeScreen ?? isNewUser),
      // Force disabled fresh user dialog as not needed but can be re-purposed for future similar use case.
      showWelcomeUpgradeDialog: const Nullable(
        false, // state.showWelcomeUpgradeDialog ?? isNewUser
      ),
    );

    emit(state);
  }

  Future<void> _updateTimeZoneInViewSettings() async {
    if (state.viewSettings == null) return;
    final date = DateTime.now().toIsoDateStringWithOffset();
    final timeZoneOffset = date.substring(date.length - 6);
    if (state.viewSettings!.notificationSettings.emailNotificationTimezone !=
        timeZoneOffset) {
      await updateViewSettings(
        viewSettings: state.viewSettings!.copyWith(
          notificationSettings: state.viewSettings!.notificationSettings
              .copyWith(emailNotificationTimezone: timeZoneOffset),
        ),
      );
    }
  }

  /// Listen to the app lifecycle state changes
  void _onStateChanged(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.detached:
        _onDetached();
      case AppLifecycleState.resumed:
        _onResumed();
      case AppLifecycleState.inactive:
        _onInactive();
      case AppLifecycleState.hidden:
        _onHidden();
      case AppLifecycleState.paused:
        _onPaused();
    }
  }

  void _onDetached() {
    _log.d('App detached');
  }

  void _onResumed() {
    _log.d('App resumed');
    startSyncAgain(false);
  }

  void _onInactive() {
    _log.d('App inactive');
  }

  void _onHidden() {
    _log.d('App hidden');
  }

  void _onPaused() {
    _log.d('App paused');
  }
}
