import 'dart:async';

import 'package:bloc/bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:formz/formz.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/data/providers/authentication/authentication_service.dart';
import 'package:mevolve/data/providers/authentication/exceptions/firebase_auth_exceptions.dart';
import 'package:mevolve/data/providers/firebase_functions.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/widgets/login/view/login_page.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';

part 'login_state.dart';

class LoginCubit extends Cubit<LoginState> {
  LoginCubit(
    this._authenticationRepository,
    this._firebaseFunctionsRepository,
    this._appBloc,
  ) : super(const LoginState());

  final AuthenticationService _authenticationRepository;
  final FirebaseFunctionsRepository _firebaseFunctionsRepository;
  final AppBloc _appBloc;
  Timer? _emailVerificationTimer;
  int _verificationTimeLeft = AppConstant.timeoutLimit;
  final Log _log = MeLogger.getLogger(LogTags.authentication);

  Future<bool> sendOtp(String email) async {
    if (_emailVerificationTimer != null) {
      stopTimer();
    }
    final appLanguage = _appBloc.state.getAppLanguage.name;
    _startTimer();
    String? res =
        await _firebaseFunctionsRepository.sendOtp(email, appLanguage);
    if (res == null) {
      return false;
    }
    emit(state.copyWith(refId: res));
    return res != '';
  }

  Future<void> checkAndVerifyOtp(String otp) async {
    if (state.verifyOtpRes.isNotEmpty) {
      emit(state.copyWith(verifyOtpRes: {}));
    }
    if (state.refId != '') {
      emit(state.copyWith(loading: true));
      Map<String, dynamic> res = await _firebaseFunctionsRepository.verifyOtp(
        otp: otp,
        refId: state.refId,
      );
      if (res.isNotEmpty) {
        emit(state.copyWith(verifyOtpRes: res));
        if (res['error'] != null) {
          emit(state.copyWith(loading: false));
        }
        if (res['error'] == 'exceededRetry') {
          emit(
            state.copyWith(
              status: FormzStatus.submissionFailure,
              errorMessage:
                  MeTranslations.instance.toast_otpAttemptFinished_content.text,
            ),
          );
        }
        if (res['error'] == null &&
            res['token'] != null &&
            res['isNewUser'] != null) {
          stopTimer();
          await validateToken(res);
        }
      }
    }
  }

  Future<void> validateToken(res) async {
    try {
      final user = await _authenticationRepository.logInWithEmailOtp(
        res['token'],
        res['isNewUser'],
      );
      if (user != null) {
        _log.i('Received User Logged in with Email OTP');
        changeLoginInProgressState(
          LoginProcessStatus.freshLoginCompleted,
          user: user,
        );
        emit(state.copyWith(status: FormzStatus.submissionSuccess));
      } else {
        emit(state.copyWith(status: FormzStatus.submissionCanceled));
      }
    } catch (error) {
      _log.e(error);
      emit(
        state.copyWith(
          status: FormzStatus.submissionFailure,
        ),
      );
    }
  }

  Future<bool> logInWithEmailOtp(String email) async {
    changeLoginInProgressState(LoginProcessStatus.loginInProgress);
    emit(
      state.copyWith(
        status: FormzStatus.submissionInProgress,
        loginType: LoginType.emailWithOtp,
        timeLeft: _verificationTimeLeft,
      ),
    );
    return await sendOtp(email);
  }

  void _startTimer() {
    stopTimer();
    _verificationTimeLeft = AppConstant.timeoutLimit;
    _emailVerificationTimer =
        Timer.periodic(const Duration(seconds: 1), (timer) {
      _verificationTimeLeft--;
      emit(
        state.copyWith(
          timeLeft: _verificationTimeLeft,
        ),
      );
      if (_verificationTimeLeft == 0) {
        stopTimer();
      }
    });
  }

  void stopTimer() {
    _emailVerificationTimer?.cancel();
  }

  void revertState() {
    emit(
      const LoginState(),
    );
  }

  Future<void> logInWithGoogle() async {
    Future<(LoginStatus, LoginErrorCode?)> signIn() async {
      LoginStatus loginStatus = LoginStatus.failure;
      LoginErrorCode? errorCode = LoginErrorCode.other;
      changeLoginInProgressState(LoginProcessStatus.loginInProgress);
      emit(
        state.copyWith(
          status: FormzStatus.submissionInProgress,
          loginType: LoginType.googleSignIn,
        ),
      );
      try {
        final user = await _authenticationRepository.logInWithGoogle();
        if (user != null) {
          _log.i('Received google user');
          changeLoginInProgressState(
            LoginProcessStatus.freshLoginCompleted,
            user: user,
          );
          showFullScreenLoginLoader.value = true;
          emit(state.copyWith(status: FormzStatus.submissionSuccess));
          loginStatus = LoginStatus.success;
          errorCode = null;
        } else {
          // User canceled Google sign-in, dismiss loading dialog
          showFullScreenLoginLoader.value = false;
          emit(state.copyWith(status: FormzStatus.submissionCanceled));
        }
      } on LogInWithGoogleFailure catch (e) {
        showFullScreenLoginLoader.value = false;
        _log.e(e.message);
        emit(
          state.copyWith(
            errorMessage: null,
            status: FormzStatus.submissionFailure,
          ),
        );
        errorCode = LoginErrorCode.other;
      } catch (error) {
        showFullScreenLoginLoader.value = false;
        _log.e(error);
        emit(state.copyWith(status: FormzStatus.submissionFailure));
        errorCode = LoginErrorCode.other;
      }
      return (loginStatus, errorCode);
    }

    // Send login event
    await EventFormation().sendLoginEvent(
      loginMethod: LoginMethod.google,
      sendOnFuncComplete: () async {
        return await signIn();
      },
    );
  }

  Future<void> signInWithApple() async {
    Future<(LoginStatus, LoginErrorCode?)> signIn() async {
      LoginStatus loginStatus = LoginStatus.failure;
      LoginErrorCode? errorCode = LoginErrorCode.other;
      showFullScreenLoginLoader.value = true;
      changeLoginInProgressState(LoginProcessStatus.loginInProgress);
      emit(
        state.copyWith(
          status: FormzStatus.submissionInProgress,
          loginType: LoginType.appleSignIn,
        ),
      );
      try {
        final user = await _authenticationRepository.logInWithApple();
        if (user != null) {
          changeLoginInProgressState(
            LoginProcessStatus.freshLoginCompleted,
            user: user.$1,
          );
          emit(state.copyWith(status: FormzStatus.submissionSuccess));
          loginStatus = LoginStatus.success;
          errorCode = null;
        } else {
          showFullScreenLoginLoader.value = false;
          emit(state.copyWith(status: FormzStatus.submissionFailure));
        }
      } on LogInWithAppleFailure catch (e) {
        showFullScreenLoginLoader.value = false;
        _log.e(e.message);
        emit(
          state.copyWith(
            errorMessage: null,
            status: e.status,
          ),
        );
        errorCode = LoginErrorCode.other;
      } catch (_) {
        showFullScreenLoginLoader.value = false;
        emit(state.copyWith(status: FormzStatus.submissionFailure));
        errorCode = LoginErrorCode.other;
      }
      return (loginStatus, errorCode);
    }

    // Send login event
    await EventFormation().sendLoginEvent(
      loginMethod: LoginMethod.apple,
      sendOnFuncComplete: () async {
        return await signIn();
      },
    );
  }

  Future<void>
      reAuthenticateWithAppleAndRevokeTokenWithAuthorizationCode() async {
    try {
      final user = await _authenticationRepository.logInWithApple();
      final String? appleAuthorizationCode = user?.$2;
      if (appleAuthorizationCode != null) {
        await _authenticationRepository
            .revokeAppleTokenWithAuthorizationCode(appleAuthorizationCode);
      }
    } on LogInWithAppleFailure catch (e) {
      _log.e(e.message);
    } catch (_) {}
  }

  void changeLoginInProgressState(LoginProcessStatus status, {User? user}) {
    _appBloc.add(
      AppLoginProcessStatusChanged(appLoginProcessStatus: status, user: user),
    );
  }
}
