import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gal/gal.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/data/enums/common_sharing_model.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/enums/share_type.dart';
import 'package:mevolve/data/models/common_sharing_feat_model.dart';
import 'package:mevolve/data/models/in_app_notification.dart';
import 'package:mevolve/data/models/note.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/lists/functions/collaboration_notification.dart';
import 'package:mevolve/features/lists/widgets/colaborate_sheet/list_collaborate_sheet.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/list_option_sheet.dart';
import 'package:mevolve/features/notes/cubit/my_notes/notes_operations.dart';
import 'package:mevolve/features/notes/sharing/note_to_image.dart';
import 'package:mevolve/features/notes/sharing/note_to_text.dart';
import 'package:mevolve/features/tasks_providers/notes/main_notes_cubit.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/utilities/encryption/me_encryption_core.dart';
import 'package:mevolve/utilities/sharing_utility/sharing_firebase_functions.dart';
import 'package:mevolve/features/lists/widgets/colaborate_sheet/invite_collaborator_textfield.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/utilities/task_component_handler.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:open_file_plus/open_file_plus.dart';

class NoteCollaborateSheetNonOwner extends StatelessWidget {
  const NoteCollaborateSheetNonOwner({
    super.key,
    required this.noteId,
    required this.type,
    required this.note,
  });

  final String noteId;
  final Note note;
  final ShareType type;

  @override
  Widget build(BuildContext context) {
    return type == ShareType.publicTask
        ? PublicVisibilitySheet(
            itemId: note.id,
            isPublic: note.isPublic,
            isOwner: false,
            // Non-owner, so always false
            publicId: note.publicId,
            title: note.title,
            description: getPlainText(note.description),
            ownerName: note.ownerName,
            ownerEmail: note.ownerEmail,
            ownerId: note.uid,
            shareCount: note.members.memberHashedEmails.length,
            sharerUserRole: note.members.getUserRoleType,
            collectionType: FirebaseDocCollectionType.notes,
            lastCloudUpdatedAt: note.lastCloudUpdatedAt,
          )
        : BlocBuilder<MainNotesCubit, MainNotesState>(
            builder: (context, state) {
              final Note? note = state.taskIdsToTasks[noteId];

              if (note == null) {
                return const SizedBox.shrink();
              }
              return PublicVisibilitySheet(
                itemId: note.id,
                isPublic: note.isPublic,
                isOwner: false,
                // Non-owner, so always false
                publicId: note.publicId,
                title: note.title,
                description: getPlainText(note.description),
                ownerName: note.ownerName,
                ownerEmail: note.ownerEmail,
                ownerId: note.uid,
                shareCount: note.members.memberHashedEmails.length,
                sharerUserRole: note.members.getUserRoleType,
                collectionType: FirebaseDocCollectionType.notes,
                lastCloudUpdatedAt: note.lastCloudUpdatedAt,
              );
            },
          );
  }
}

class NoteCollaboratorsSheet extends StatelessWidget {
  const NoteCollaboratorsSheet({
    super.key,
    required this.noteId,
  });

  final String noteId;

  @override
  Widget build(BuildContext context) {
    final String myUid = context.read<AppBloc>().state.user!.uid;

    Note? note = context.watch<MainNotesCubit>().state.taskIdsToTasks[noteId];

    if (note == null) {
      return const SizedBox.shrink();
    }

    final bool isOwner = note.uid == myUid;

    return CollaboratorsSheet(
      isOwner: isOwner,
      ownerEmail: note.ownerEmail,
      ownerName: note.ownerName,
      ownerId: note.uid,
      members: note.members.membersConfig.values.toList(),
      collectionType: FirebaseDocCollectionType.notes,
      maxMembersAllowed: MembersModel.collaboratorLimit,
      onAddMember: (BuildContext context) async {
        await showMeScrollableModalBottomSheet(
          bottomSheetLevel: 1,
          backgroundColor: Colors.transparent,
          shape: const RoundedRectangleBorder(),
          builder: (context, showDiscardDialogCallback) {
            return InviteCollaboratorTextField(
              sharerUserRole: note.members.getUserRoleType,
              collection: FirebaseDocCollectionType.notes,
              showDiscardDialog: showDiscardDialogCallback,
              id: note.id,
              dek: note.encryptionData.dek!,
              alreadyAddedUsers: note.members.membersConfig.values.toList(),
              addMember: (
                dek,
                email,
                role,
                hashedEmail,
                uid,
                decryptedSecret,
              ) async {
                InAppNotification notification =
                    SendNotificationList().notifyUserAdded(
                  title: note.title,
                  taskType: InAppNotificationTaskType.note,
                  taskId: note.id,
                  operationBy:
                      context.read<AppBloc>().state.userData!.userInfo.name,
                  userHashedEmail: hashedEmail,
                );
                String encEmail = await MeEncryptionCore.encryptAesDataCore(
                  email,
                  decryptedSecret: decryptedSecret,
                );
                await SharingFirebaseFunctions().addMemberToTask(
                  collection: FirebaseDocCollectionType.notes.name,
                  title: note.title,
                  adminName: note.ownerName,
                  decryptedEmail: email,
                  notification: notification,
                  setupId: note.id,
                  memberConfig: MembersConfigModel(
                    hashedEmail: hashedEmail,
                    status: MemberStatus.active,
                    role: role,
                    dek: dek,
                    eEmail: encEmail,
                  ),
                  uid: uid,
                );
              },
            );
          },
        );
      },
      onUpdateMemberRole: (member, newRole) async {
        context.read<NotesOperations>().updateNote(
          note: note.copyWith(
            members: note.members.copyWith(
              membersConfig: note.members.membersConfig.map(
                (key, MembersConfigModel value) {
                  if (value.hashedEmail == member.hashedEmail) {
                    return MapEntry(
                      key,
                      value.copyWith(
                        hashedEmail: member.hashedEmail,
                        role: newRole,
                        status: MemberStatus.active,
                      ),
                    );
                  }
                  return MapEntry(key, value);
                },
              ),
            ),
          ),
        );
      },
      onRemoveMember: (member) async {
        await SharingFirebaseFunctions().removeCollaborator(
          note.id,
          member.hashedEmail,
          collection: FirebaseDocCollectionType.notes,
        );
        if (context.mounted) {
          SendNotificationList().notifyUserRemoved(
            title: note.title,
            taskType: InAppNotificationTaskType.note,
            taskId: note.id,
            operationBy: context.read<AppBloc>().state.userData!.userInfo.name,
            userHashedEmail: member.hashedEmail,
          );
        }
      },
    );
  }
}

class NotePublicVisibilitySheet extends StatelessWidget {
  const NotePublicVisibilitySheet({
    super.key,
    required this.noteId,
  });

  final String noteId;

  @override
  Widget build(BuildContext context) {
    final String myUid = context.read<AppBloc>().state.user!.uid;

    return BlocBuilder<MainNotesCubit, MainNotesState>(
      builder: (context, state) {
        Note? note = state.taskIdsToTasks[noteId];
        if (note == null) {
          return const SizedBox();
        }

        NotesOperations notesOperations = context.read<NotesOperations>();

        final bool isOwner = note.uid == myUid;

        return PublicVisibilitySheet(
          itemId: noteId,
          isPublic: note.isPublic,
          isOwner: isOwner,
          publicId: note.publicId,
          title: note.title,
          description: getPlainText(note.description),
          ownerName: note.ownerName,
          ownerEmail: note.ownerEmail,
          ownerId: note.uid,
          shareCount: note.members.memberHashedEmails.length,
          sharerUserRole: note.members.getUserRoleType,
          collectionType: FirebaseDocCollectionType.notes,
          lastCloudUpdatedAt: note.lastCloudUpdatedAt,
          onPublicToggle: (isPublic) async {
            if (isPublic) {
              // Toggle ON logic
              Note? oldNote =
                  context.read<MainNotesCubit>().state.taskIdsToTasks[note.id];
              if (oldNote == null) return null;

              bool isAttachmentNotCloud =
                  oldNote.hasPendingAttachmentsForNewNote(note);

              if (isAttachmentNotCloud) {
                TaskComponentHandler.fieldDialog(
                  context: context,
                  isValidTask: false,
                  dialogTitle: MeTranslations
                      .instance.overlay_calendarSyncInProgress_title,
                  description: MeTranslations
                      .instance.overlay_calendarSyncInProgress_content,
                );
                return null;
              }

              if (note.publicId == null || note.publicId!.isEmpty) {
                Map<String, dynamic>? res =
                    await SharingFirebaseFunctions().updateTaskWithShortLink(
                  note.id,
                  note.ownerName,
                  collection: FirebaseDocCollectionType.notes,
                );
                if (res != null) {
                  final Note customNote = note.copyWith(
                    isPublic: true,
                    publicId: res['publicId'],
                    inviteLink: res['inviteLink'],
                    encryptionData: note.encryptionData.copyWith(
                      encryptedFields: ['members.membersConfig{}.eEmail'],
                    ),
                  );
                  await notesOperations
                      .decryptNotesAttachmentAndReplaceWithEncrypted(
                    note: customNote,
                  );
                  return res;
                }
              } else {
                final Note customNote = note.copyWith(
                  isPublic: true,
                  encryptionData: note.encryptionData.copyWith(
                    encryptedFields: ['members.membersConfig{}.eEmail'],
                  ),
                );
                await notesOperations
                    .decryptNotesAttachmentAndReplaceWithEncrypted(
                  note: customNote,
                );
              }
            } else {
              // Toggle OFF logic
              final Note customNote = note.copyWith(
                isPublic: false,
                encryptionData: note.encryptionData.copyWith(
                  encryptedFields: [
                    'title',
                    'description',
                    'ownerName',
                    'ownerEmail',
                    'members.membersConfig{}.eEmail',
                  ],
                ),
              );
              await notesOperations.updateNote(note: customNote);
              await SharingFirebaseFunctions().makeTaskPrivate(
                note.id,
                collection: FirebaseDocCollectionType.notes,
              );
              await Future.delayed(const Duration(seconds: 1));
              await notesOperations
                  .encryptNotesAttachmentAndReplaceWithDecrypted(
                note: customNote,
              );
            }
            return null;
          },
        );
      },
    );
  }
}

class NoteShareSheet extends StatelessWidget {
  const NoteShareSheet({
    Key? key,
    required this.noteData,
  }) : super(key: key);

  final Note noteData;

  @override
  Widget build(BuildContext context) {
    return ShareSheet(
      itemId: noteData.id,
      copyAsTextTitle: MeTranslations.instance.screen_common_copyText,
      downloadAsImageTitle:
          MeTranslations.instance.screen_common_downloadAsImage,
      shareTitle: MeTranslations.instance.screen_common_share,
      shareCount: noteData.members.memberHashedEmails.length,
      userRole: noteData.members.getUserRoleType,
      trackAction: TrackAction.noteAction,
      showCheckBoxNotifier: null,
      // Add if needed
      onCopyAsText: () async {
        return noteToText(noteData);
      },
      onDownloadAsImage: () async {
        await Future.delayed(const Duration(milliseconds: 500));
        if (context.mounted) {
          String path = await noteToImage(
            noteData,
            context,
          );

          await Gal.putImage(
            path,
            album: 'Mevolve',
          );
          if (context.mounted) {
            showCustomMessageOverlay(
              context,
              MeTranslations.instance.toast_noteDownloaded_content,
              actionText: MeTranslations.instance.screen_imagePreview_view,
              actionFunction: () {
                OpenFile.open(path);
              },
            );
          }
        }
      },
      onShareAsText: () async {
        return noteToText(noteData);
      },
      onShareAsImage: () async {
        return await noteToImage(
          noteData,
          context,
        );
      },
    );
  }
}
