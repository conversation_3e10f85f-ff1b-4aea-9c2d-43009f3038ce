import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:mevolve/constants/animation_constants.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/constants/app_strings.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/share_type.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/note.dart';
import 'package:mevolve/data/models/user/note/note_settings.dart';
import 'package:mevolve/data/models/user/view_settings.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/notes/cubit/my_notes/notes_operations.dart';
import 'package:mevolve/features/notes/cubit/shared_with_me_notes_actions/shared_with_me_notes_actions_cubit.dart';
import 'package:mevolve/features/notes/cubit/shared_with_me_notes_filter/shared_with_me_notes_filter_cubit.dart';
import 'package:mevolve/features/notes/view/note_widget.dart';
import 'package:mevolve/features/notes/widgets/shared_with_me_notes_filter/shared_with_me_notes_filter_widget.dart';
import 'package:mevolve/features/notes/widgets/note_tile.dart';
import 'package:mevolve/features/tasks_providers/notes/main_notes_cubit.dart';
import 'package:mevolve/features/widgets/animated_list.dart';
import 'package:mevolve/features/widgets/custom_reorderable_listview.dart';
import 'package:mevolve/features/widgets/empty_state_widget.dart';
import 'package:mevolve/features/widgets/expandable_items_listview.dart';
import 'package:mevolve/features/widgets/filter_bottomsheets/mood_filter_bottom_sheet.dart';
import 'package:mevolve/features/widgets/go_to_top_button.dart';
import 'package:mevolve/features/widgets/loaders.dart';
import 'package:mevolve/features/widgets/me_groupby_accordion.dart';
import 'package:mevolve/features/widgets/me_scrollbar.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/metranslations.gen.dart';

class SharedWithMeNotesTab extends StatefulWidget {
  const SharedWithMeNotesTab({Key? key}) : super(key: key);

  @override
  State<SharedWithMeNotesTab> createState() => _SharedWithMeNotesTabState();
}

class _SharedWithMeNotesTabState extends State<SharedWithMeNotesTab>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late final AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 0),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    // Reset filter on dispose
    authenticatedGlobalContext
        ?.read<SharedWithMeNotesFilterCubit>()
        .resetFilter();
    super.dispose();
  }

  void _showFilter() {
    if (mounted) {
      _animationController.forward();
    }
  }

  void _hideFilter() {
    if (mounted) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return WidgetTracker(
      trackAction: TrackAction.noteAction,
      child: BlocBuilder<AppBloc, AppState>(
        builder: (context, appState) {
          final viewSettings = appState.viewSettings;
          if (viewSettings == null) {
            return const SizedBox.shrink();
          }

          return BlocBuilder<SharedWithMeNotesActionsCubit,
              SharedWithMeNotesActionsState>(
            builder: (context, state) {
              // If we are fetching notes for the first time show the loading indicator.
              if (state is SharedWithMeNotesActionsStateInitial) {
                return const CustomCircularLoader();
              }

              if (state is SharedWithMeNotesActionsStateLoaded) {
                if (state.showFilterRow) {
                  _showFilter();
                } else {
                  _hideFilter();
                }

                // Handle empty state when no filters applied
                if (state.filteredSharedNoteIds.isEmpty &&
                    !context
                        .read<SharedWithMeNotesFilterCubit>()
                        .isFilterApplied) {
                  return Center(
                    child: EmptyStateWidget(
                      imagePath: AppStrings.notesEmptyIllustration(
                        colorScheme: colorScheme,
                      ),
                      imageText: MeTranslations
                          .instance.screen_notes_emptyScreenContent,
                    ),
                  );
                }

                // Handle empty state when filters applied but no results
                if (state.filteredSharedNoteIds.isEmpty &&
                    context
                        .read<SharedWithMeNotesFilterCubit>()
                        .isFilterApplied) {
                  return Column(
                    children: [
                      SizeTransition(
                        sizeFactor: _animationController,
                        axisAlignment: 1,
                        child: const SharedWithMeNotesFilterWidget(),
                      ),
                      Expanded(
                        child: EmptyStateWidget(
                          imagePath: AppStrings.recordsEmptyIllustration(
                            colorScheme: colorScheme,
                          ),
                          imageText: MeTranslations
                              .instance.screen_common_recordsNotFound,
                        ),
                      ),
                    ],
                  );
                }
              }

              return Column(
                children: [
                  SizeTransition(
                    sizeFactor: _animationController,
                    axisAlignment: 1,
                    child: const SharedWithMeNotesFilterWidget(),
                  ),
                  Expanded(
                    child: SharedWithMeNotesTabViewListView(
                      isFilterRowVisible: state.showFilterRow,
                      viewSettings: viewSettings,
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class SharedWithMeNotesTabViewListView extends StatefulWidget {
  const SharedWithMeNotesTabViewListView({
    super.key,
    required this.isFilterRowVisible,
    required this.viewSettings,
  });

  final bool isFilterRowVisible;
  final ViewSettings viewSettings;

  @override
  State<SharedWithMeNotesTabViewListView> createState() =>
      _SharedWithMeNotesTabViewListViewState();
}

class _SharedWithMeNotesTabViewListViewState
    extends State<SharedWithMeNotesTabViewListView> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();
    context
        .read<SharedWithMeNotesActionsCubit>()
        .attachScrollController(_scrollController, context);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return BlocBuilder<SharedWithMeNotesActionsCubit,
        SharedWithMeNotesActionsState>(
      builder: (context, state) {
        List<String> noteIds = state.filteredSharedNoteIds;

        if (!state.showFilterRow && noteIds.isEmpty) {
          return Center(
            child: EmptyStateWidget(
              imagePath: AppStrings.notesEmptyIllustration(
                colorScheme: colorScheme,
              ),
              imageText:
                  MeTranslations.instance.screen_notes_emptyScreenContent,
            ),
          );
        }

        if (noteIds.isEmpty) {
          return Center(
            child: EmptyStateWidget(
              imagePath: AppStrings.recordsEmptyIllustration(
                colorScheme: colorScheme,
              ),
              imageText: MeTranslations.instance.screen_common_recordsNotFound,
            ),
          );
        }

        return Column(
          children: [
            Expanded(
              child: BlocConsumer<AppBloc, AppState>(
                buildWhen: (previous, current) {
                  return previous.viewSettings?.noteSettings !=
                      current.viewSettings?.noteSettings;
                },
                listener: (context, state) {
                  if (state.viewSettings == null) {
                    return;
                  }
                },
                builder: (context, appState) {
                  final viewSettings = appState.viewSettings;
                  if (viewSettings == null) {
                    return const SizedBox.shrink();
                  }

                  return Stack(
                    children: [
                      _buildNotesView(noteIds, viewSettings),
                      GoToTopButton(
                        scrollController: _scrollController,
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildNotesView(List<String> noteIds, ViewSettings viewSettings) {
    final groupByType =
        viewSettings.noteSettings.sharedNoteSettings.notesGroupBy;

    if (groupByType == NotesGroupBy.none) {
      return _buildSimpleView(noteIds, viewSettings);
    } else {
      return _buildGroupByView(noteIds, viewSettings);
    }
  }

  Widget _buildSimpleView(List<String> noteIds, ViewSettings viewSettings) {
    return BlocBuilder<MainNotesCubit, MainNotesState>(
      builder: (context, notesState) {
        if (notesState is! MainNotesStateLoaded) {
          return const SizedBox.shrink();
        }

        // Get actual note data from provider
        List<Note> notes = noteIds
            .map((id) => notesState.taskIdsToTasks[id])
            .whereType<Note>()
            .toList();

        return MeScrollbar(
          controller: _scrollController,
          length: notes.length,
          child: SimpleNotesView(
            scrollController: _scrollController,
            notes: notes,
          ),
        );
      },
    );
  }

  Widget _buildGroupByView(List<String> noteIds, ViewSettings viewSettings) {
    return BlocBuilder<MainNotesCubit, MainNotesState>(
      builder: (context, notesState) {
        if (notesState is! MainNotesStateLoaded) {
          return const SizedBox.shrink();
        }

        // Get actual note data from provider
        List<Note> notes = noteIds
            .map((id) => notesState.taskIdsToTasks[id])
            .whereType<Note>()
            .toList();

        return GroupByView(
          scrollController: _scrollController,
          notes: notes,
          viewSettings: viewSettings,
          isFilterRowVisible: widget.isFilterRowVisible,
        );
      },
    );
  }
}

class SimpleNotesView extends StatefulWidget {
  const SimpleNotesView({
    super.key,
    required this.scrollController,
    required this.notes,
  });

  final ScrollController scrollController;
  final List<Note> notes;

  @override
  State<SimpleNotesView> createState() => _SimpleNotesViewState();
}

class _SimpleNotesViewState extends State<SimpleNotesView> {
  List<Note>? _optimisticNotes;

  @override
  void didUpdateWidget(SimpleNotesView oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reset optimistic state when new data comes from the server
    if (oldWidget.notes != widget.notes) {
      _optimisticNotes = null;
    }
  }

  List<Note> get _displayNotes => _optimisticNotes ?? widget.notes;

  @override
  Widget build(BuildContext context) {
    final isVibrate = context
        .read<AppBloc>()
        .state
        .viewSettings!
        .appSettings
        .isVibrationEnabled;

    return SlidableAutoCloseBehavior(
      child: AnimationLimiter(
        child: CustomReorderableListView.separated(
          scrollController: widget.scrollController,
          itemCount: _displayNotes.length,
          onReorder: (oldIndex, newIndex) {
            if (isVibrate) {
              HapticFeedback.heavyImpact();
            }

            final originalNewIndex = newIndex;

            // Optimistic UI update - immediately show the reordered note
            setState(() {
              _optimisticNotes = List.from(widget.notes);

              if (oldIndex < newIndex) {
                newIndex--;
              }

              if (oldIndex != newIndex) {
                final item = _optimisticNotes!.removeAt(oldIndex);
                _optimisticNotes!.insert(newIndex, item);
              }
            });

            // Handle reordering through repository (background update)
            context.read<NotesOperations>().reorderRawNote(
                  note: widget.notes[oldIndex],
                  allNotes: widget.notes,
                  oldIndex: oldIndex,
                  newIndex: originalNewIndex,
                );
          },
          proxyDecorator: (child, index, animation) {
            return AnimatedBuilder(
              animation: animation,
              builder: (BuildContext context, Widget? child) {
                final animValue = Curves.easeInOut.transform(animation.value);
                final elevation = lerpDouble(0, 6, animValue)!;
                final scale = lerpDouble(1, 1.02, animValue)!;
                return Transform.scale(
                  scale: scale,
                  child: Material(
                    elevation: elevation,
                    color: Colors.transparent,
                    shadowColor: Colors.black26,
                    child: child,
                  ),
                );
              },
              child: child,
            );
          },
          separatorBuilder: (context, index) => const SizedBox(
            height: 1,
          ),
          itemBuilder: (context, index) {
            final note = _displayNotes[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(
                milliseconds: AnimationConstants.listFadeDuration,
              ),
              child: SlideAnimation(
                verticalOffset: 50,
                child: FadeInAnimation(
                  child: NoteTileWidget(
                    key: ValueKey(note.id),
                    title: note.title,
                    shareType: ShareType.sharedTask,
                    note: note,
                    description:
                        note.description.isEmpty ? null : note.description,
                    time: note.noteUpdatedAt ?? note.localUpdatedAt,
                    hashTags: note.tags,
                    emotion: note.emotion,
                    onTap: () {
                      showMeScrollableModalBottomSheet(
                        bottomSheetLevel: SizeConstants.level2BottomSheet,
                        useSafeArea: true,
                        builder: (_, showDiscardDialogCallback) => NoteWidget(
                          noteId: note.id,
                          bottomSheetLevel: SizeConstants.level2BottomSheet,
                          setShowDiscardDialogFlagValue:
                              showDiscardDialogCallback,
                        ),
                      );
                    },
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

class GroupByView extends StatefulWidget {
  const GroupByView({
    super.key,
    required this.scrollController,
    required this.notes,
    required this.viewSettings,
    required this.isFilterRowVisible,
  });

  final ScrollController scrollController;
  final List<Note> notes;
  final ViewSettings viewSettings;
  final bool isFilterRowVisible;

  @override
  State<GroupByView> createState() => _GroupByViewState();
}

class _GroupByViewState extends State<GroupByView> {
  late ExpandableListController _expandableListController;

  // GlobalKeys for tracking header positions
  final List<GlobalKey> headerKeys = [];
  ValueNotifier<NoteHeaderData>? headerDataNotifier;

  @override
  void initState() {
    super.initState();
    widget.scrollController.addListener(_onScroll);
    _expandableListController = ExpandableListController(
      defaultExpanded:
          !widget.viewSettings.noteSettings.sharedNoteSettings.collapsedView,
    );
    _setupGroupedNotes();
  }

  @override
  void didUpdateWidget(covariant GroupByView oldWidget) {
    if (oldWidget.notes != widget.notes ||
        oldWidget.viewSettings.noteSettings.sharedNoteSettings.notesGroupBy !=
            widget.viewSettings.noteSettings.sharedNoteSettings.notesGroupBy) {
      _setupGroupedNotes();
    }

    // Update expandable controller when collapsed view setting changes
    if (oldWidget.viewSettings.noteSettings.sharedNoteSettings.collapsedView !=
        widget.viewSettings.noteSettings.sharedNoteSettings.collapsedView) {
      final bool isCollapsed =
          widget.viewSettings.noteSettings.sharedNoteSettings.collapsedView;
      _expandableListController.updateDefault(!isCollapsed);
      if (isCollapsed) {
        _expandableListController.collapseAll();
      } else {
        _expandableListController.expandAll();
      }
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    widget.scrollController.removeListener(_onScroll);
    _expandableListController.dispose();
    super.dispose();
  }

  void _setupGroupedNotes() {
    // Group notes based on groupBy type
    final groupedNotes = _groupNotes(
      widget.notes,
      widget.viewSettings.noteSettings.sharedNoteSettings.notesGroupBy,
    );

    if (groupedNotes.isEmpty) return;

    headerKeys.clear();
    for (int i = 0; i < groupedNotes.length; i++) {
      headerKeys.add(GlobalKey());
    }

    headerDataNotifier = ValueNotifier(
      NoteHeaderData(
        headingText: MeString(groupedNotes.first.groupKey),
        totalCount: groupedNotes.first.notes.length,
        currentIndex: 0,
      ),
    );
  }

  List<GroupedNotes> _groupNotes(List<Note> notes, NotesGroupBy groupBy) {
    var notesMap = <String, List<Note>>{};

    if (groupBy == NotesGroupBy.date) {
      for (var note in notes) {
        final key =
            (note.noteUpdatedAt ?? note.localUpdatedAt).onlyDate().toString();
        notesMap.putIfAbsent(key, () => []).add(note);
      }
    } else if (groupBy == NotesGroupBy.mood) {
      for (var note in notes) {
        final key = note.emotion?.toString() ??
            MeTranslations.instance.screen_notes_groupByNoMoods.text;
        notesMap.putIfAbsent(key, () => []).add(note);
      }
    } else if (groupBy == NotesGroupBy.hashtag) {
      for (var note in notes) {
        if (note.tags.isEmpty) {
          notesMap
              .putIfAbsent(
                MeTranslations.instance.screen_common_groupByNoHashtags.text,
                () => [],
              )
              .add(note);
        } else {
          for (var tag in note.tags) {
            notesMap.putIfAbsent(tag, () => []).add(note);
          }
        }
      }
    }

    final groupedNotes = notesMap.entries
        .map((e) => GroupedNotes(groupKey: e.key, notes: e.value))
        .toList();

    // Sort groups based on groupBy type
    if (groupBy == NotesGroupBy.date) {
      groupedNotes.sort((a, b) {
        final aDate = DateTime.parse(a.groupKey);
        final bDate = DateTime.parse(b.groupKey);
        return bDate.compareTo(aDate);
      });
    } else if (groupBy == NotesGroupBy.mood) {
      groupedNotes.sort((a, b) {
        if (a.groupKey ==
            MeTranslations.instance.screen_notes_groupByNoMoods.text) {
          return 1;
        }
        if (b.groupKey ==
            MeTranslations.instance.screen_notes_groupByNoMoods.text) {
          return -1;
        }
        return a.groupKey.compareTo(b.groupKey);
      });
    }

    return groupedNotes;
  }

  void _onScroll() {
    final groupedNotes = _groupNotes(
      widget.notes,
      widget.viewSettings.noteSettings.sharedNoteSettings.notesGroupBy,
    );

    for (int i = 0; i < headerKeys.length; i++) {
      final RenderBox? renderBox =
          headerKeys[i].currentContext?.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        final position = renderBox.localToGlobal(Offset.zero).dy;

        if (position <=
            SizeConstants.stickyHeaderExtent(widget.isFilterRowVisible)) {
          if (headerDataNotifier == null) return;
          headerDataNotifier!.value = NoteHeaderData(
            headingText: MeString(groupedNotes[i].groupKey),
            totalCount: groupedNotes[i].notes.length,
            currentIndex: i,
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final groupedNotes = _groupNotes(
      widget.notes,
      widget.viewSettings.noteSettings.sharedNoteSettings.notesGroupBy,
    );

    return MeScrollbar(
      controller: widget.scrollController,
      length: groupedNotes.length,
      totalLenIncludingNestedListItems: groupedNotes.fold<int>(
        0,
        (previousValue, element) => previousValue + element.notes.length,
      ),
      child: Stack(
        children: [
          ExpandableItemsListView<Note, MeString>(
            controller: _expandableListController,
            scrollController: widget.scrollController,
            allowInsertRemoveAnimation: ListAnimationsStatus.disable,
            items: List.generate(
              groupedNotes.length,
              (i) => ListItem(
                id: groupedNotes[i].groupKey,
                data: MeString(groupedNotes[i].groupKey),
                subDataList: List.generate(
                  groupedNotes[i].notes.length,
                  (j) => ListSubItem(
                    id: groupedNotes[i].notes[j].id,
                    subData: groupedNotes[i].notes[j],
                    parentData: MeString(groupedNotes[i].groupKey),
                  ),
                ),
              ),
            ),
            headerBuilder: (context, index, headerItem, isExpanded) {
              final groupKey = headerItem.data;
              final headingText = _getHeaderText(
                groupKey,
                widget
                    .viewSettings.noteSettings.sharedNoteSettings.notesGroupBy,
              );

              return ExpandableListHeader(
                key: headerKeys[index],
                headingText: headingText,
                totalCount: headerItem.subDataList.length,
                showCount: widget
                    .viewSettings.noteSettings.sharedNoteSettings.showCounts,
              );
            },
            itemBuilder: (context, item) {
              final Note note = item.subData;
              return NoteTileWidget(
                title: note.title,
                shareType: ShareType.sharedTask,
                note: note,
                description: note.description.isEmpty ? null : note.description,
                time: note.noteUpdatedAt ?? note.localUpdatedAt,
                hashTags: note.tags,
                emotion: note.emotion,
                onTap: () {
                  showMeScrollableModalBottomSheet(
                    bottomSheetLevel: SizeConstants.level2BottomSheet,
                    useSafeArea: true,
                    builder: (_, showDiscardDialogCallback) => NoteWidget(
                      noteId: note.id,
                      bottomSheetLevel: SizeConstants.level2BottomSheet,
                      setShowDiscardDialogFlagValue: showDiscardDialogCallback,
                    ),
                  );
                },
              );
            },
            footer: SizedBox(
              height: groupedNotes.isNotEmpty ? 100 : 0,
            ),
            headerSeparator: const ListHeaderSeparator(),
            subItemSeparator: const ListSubItemSeparator(),
          ),
          if (headerDataNotifier != null)
            ValueListenableBuilder<NoteHeaderData>(
              valueListenable: headerDataNotifier!,
              builder: (context, headerData, child) {
                final headingText = _getHeaderText(
                  headerData.headingText,
                  widget.viewSettings.noteSettings.sharedNoteSettings
                      .notesGroupBy,
                );

                return InkWell(
                  onTap: () {
                    _expandableListController.setExpandedId(
                      headerData.headingText.text,
                      !_expandableListController
                          .isExpanded(headerData.headingText.text),
                    );
                  },
                  child: ExpandableListHeader(
                    headingText: headingText,
                    totalCount: headerData.totalCount,
                    showCount: widget.viewSettings.noteSettings
                        .sharedNoteSettings.showCounts,
                  ),
                );
              },
            ),
        ],
      ),
    );
  }

  MeString _getHeaderText(MeString groupKey, NotesGroupBy groupBy) {
    try {
      if (groupBy == NotesGroupBy.mood) {
        if (groupKey.text ==
            MeTranslations.instance.screen_notes_groupByNoMoods.text) {
          return MeTranslations.instance.screen_notes_groupByNoMoods;
        } else {
          return moodList[int.parse(groupKey.text)].values.first;
        }
      }
      if (groupBy == NotesGroupBy.date) {
        DateTime date = DateTime.parse(groupKey.text);
        return date.formattedDayDate();
      }
      if (groupBy == NotesGroupBy.hashtag) {
        if (groupKey.text ==
            MeTranslations.instance.screen_common_groupByNoHashtags.text) {
          return MeTranslations.instance.screen_common_groupByNoHashtags;
        }
        return MeString('#${groupKey.text}');
      }
    } catch (e) {
      // Return the original if parsing fails
    }
    return groupKey;
  }
}

class NoteHeaderData {
  const NoteHeaderData({
    required this.headingText,
    required this.totalCount,
    required this.currentIndex,
  });

  final MeString headingText;
  final int totalCount;
  final int currentIndex;
}

class GroupedNotes<T> {
  GroupedNotes({required this.groupKey, required this.notes});

  final T groupKey;
  final List<Note> notes;
}
