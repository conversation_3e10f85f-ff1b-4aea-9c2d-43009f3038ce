import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/description_type.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/user/note/my_note_settings.dart';
import 'package:mevolve/data/models/user/note/note_settings.dart';
import 'package:mevolve/data/models/user/view_settings.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/app/widgets/styles/me_text_icon.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/me_checkbox_list_tile.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/features/widgets/tab_kebab_widgets.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';

bool shouldBuild(
  AppState previous,
  AppState current,
) {
  return previous.viewSettings?.noteSettings.myNoteSettings !=
      current.viewSettings?.noteSettings.myNoteSettings;
}

class MyNotesKebabMenu extends StatelessWidget {
  const MyNotesKebabMenu({
    super.key,
    required this.tabIndex,
  });

  final int tabIndex;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        MeIconButton(
          key: const ValueKey('kebabMenu'),
          isCircularIconButton: true,
          iconPath: Assets.svg.moreVertical.path,
          buttonColor: colorScheme.color9,
          iconColor: ScreenSizeState.instance.isBigScreen
              ? colorScheme.color2
              : colorScheme.color12,
          iconContainerSize: const SizedBox(
            width: SizeConstants.iconButtonIconContainerSize,
            height: SizeConstants.iconButtonIconContainerSize,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 12,
          ),
          onPressed: () {
            showMeScrollableModalBottomSheet(
              bottomSheetLevel: 1,
              builder: (_, __) {
                return const NoteTabSettingWidget();
              },
            );
          },
        ),
        const SizedBox(width: 4),
      ],
    );
  }
}

Future descriptionSheet(colorScheme, context) {
  return showMeScrollableModalBottomSheet(
    bottomSheetLevel: 1,
    builder: (_, __) {
      return Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(
              SizeConstants.bottomSheetBorderRadius,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            DialogAndBottomSheetAppBar(
              title: MeTranslations.instance.screen_common_description,
            ),
            BlocBuilder<AppBloc, AppState>(
              buildWhen: (previous, current) =>
                  previous.viewSettings?.noteSettings.myNoteSettings
                      .noteDescriptionType !=
                  current.viewSettings?.noteSettings.myNoteSettings
                      .noteDescriptionType,
              builder: (context, state) {
                ViewSettings? viewSettings = state.viewSettings;

                return ListView.separated(
                  itemCount: DescriptionType.values.length,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    DescriptionType descriptionType =
                        DescriptionType.values[index];

                    return MeCheckBoxListTile(
                      title: MeText(
                        text: descriptionType.toMeString(),
                        meFontStyle: MeFontStyle.D8,
                      ),
                      showDisabledCheckbox: false,
                      value: viewSettings?.noteSettings.myNoteSettings
                              .noteDescriptionType ==
                          descriptionType,
                      onChanged: (value) {
                        context.read<AppBloc>().updateViewSettings(
                              viewSettings: viewSettings!.copyWith(
                                noteSettings:
                                    viewSettings.noteSettings.copyWith(
                                  myNoteSettings: viewSettings
                                      .noteSettings.myNoteSettings
                                      .copyWith(
                                    noteDescriptionType: descriptionType,
                                  ),
                                ),
                              ),
                            );
                        Navigator.pop(context);
                      },
                    );
                  },
                  separatorBuilder: (context, index) {
                    return const SizedBox(height: 0.5);
                  },
                );
              },
            ),
          ],
        ),
      );
    },
  );
}

class NoteTabViewSettingWidget extends StatelessWidget {
  const NoteTabViewSettingWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            SizeConstants.bottomSheetBorderRadius,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          DialogAndBottomSheetAppBar(
            title: MeTranslations
                .instance.screen_common_kebabMenuViewConfiguration,
          ),
          BlocBuilder<AppBloc, AppState>(
            buildWhen: (previous, current) => shouldBuild(
              previous,
              current,
            ),
            builder: (context, state) {
              ViewSettings? viewSettings = state.viewSettings;
              DescriptionType? descriptionType =
                  viewSettings?.noteSettings.myNoteSettings.noteDescriptionType;

              bool? showTime =
                  viewSettings?.noteSettings.myNoteSettings.noteViewTime;
              bool? showDate =
                  viewSettings?.noteSettings.myNoteSettings.noteViewDate;

              bool? showImage =
                  viewSettings?.noteSettings.myNoteSettings.noteViewImage;

              bool? showMemberCount =
                  viewSettings?.noteSettings.myNoteSettings.showMemberCount;
              bool? showMood =
                  viewSettings?.noteSettings.myNoteSettings.noteViewMood;

              bool? showHashtag =
                  viewSettings?.noteSettings.myNoteSettings.noteViewTags;

              return Flexible(
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    ViewSettingDescriptionTile<DescriptionType>(
                      selectedPopMenuItem: descriptionType!,
                      popMenuValues: DescriptionType.values,
                      yOffset: -120,
                      onPopMenuItemSelected: (DescriptionType item) {
                        context.read<AppBloc>().updateViewSettings(
                              viewSettings: viewSettings!.copyWith(
                                noteSettings:
                                    viewSettings.noteSettings.copyWith(
                                  myNoteSettings: viewSettings
                                      .noteSettings.myNoteSettings
                                      .copyWith(
                                    noteDescriptionType: item,
                                  ),
                                ),
                              ),
                            );
                      },
                    ),
                    const SizedBox(height: 0.5),
                    MeCheckBoxListTile(
                      title: MeTextIcon(
                        text: MeTranslations
                            .instance.bottomSheet_viewConfiguration_mood,
                        iconPath: Assets.svg.emojiIcon.path,
                        iconColor: colorScheme.color35,
                      ),
                      value: showMood,
                      onChanged: (value) {
                        context.read<AppBloc>().updateViewSettings(
                              viewSettings: viewSettings!.copyWith(
                                noteSettings:
                                    viewSettings.noteSettings.copyWith(
                                  // noteViewMood: value,
                                  myNoteSettings: viewSettings
                                      .noteSettings.myNoteSettings
                                      .copyWith(
                                    noteViewMood: value,
                                  ),
                                ),
                              ),
                            );
                      },
                    ),
                    const SizedBox(height: 0.5),
                    MeCheckBoxListTile(
                      title: MeTextIcon(
                        text: MeTranslations
                            .instance.bottomSheet_viewConfiguration_date,
                        iconPath: Assets.svg.calendarIcon.path,
                        iconColor: colorScheme.color35,
                      ),
                      value: showDate,
                      onChanged: (value) {
                        context.read<AppBloc>().updateViewSettings(
                              viewSettings: viewSettings!.copyWith(
                                noteSettings:
                                    viewSettings.noteSettings.copyWith(
                                  myNoteSettings: viewSettings
                                      .noteSettings.myNoteSettings
                                      .copyWith(
                                    noteViewDate: value,
                                  ),
                                ),
                              ),
                            );
                      },
                    ),
                    const SizedBox(height: 0.5),
                    MeCheckBoxListTile(
                      title: MeTextIcon(
                        text: MeTranslations.instance.screen_common_time,
                        iconPath: Assets.svg.clockIcon.path,
                        iconColor: colorScheme.color35,
                      ),
                      value: showTime,
                      onChanged: (value) {
                        context.read<AppBloc>().updateViewSettings(
                              viewSettings: viewSettings!.copyWith(
                                noteSettings:
                                    viewSettings.noteSettings.copyWith(
                                  myNoteSettings: viewSettings
                                      .noteSettings.myNoteSettings
                                      .copyWith(
                                    noteViewTime: value,
                                  ),
                                ),
                              ),
                            );
                      },
                    ),
                    const SizedBox(height: 0.5),
                    MeCheckBoxListTile(
                      title: MeTextIcon(
                        text: MeTranslations.instance.screen_common_attachment,
                        iconPath: Assets.svg.attachmentIcon.path,
                        iconColor: colorScheme.color35,
                      ),
                      value: showImage,
                      onChanged: (value) {
                        context.read<AppBloc>().updateViewSettings(
                              viewSettings: viewSettings!.copyWith(
                                noteSettings:
                                    viewSettings.noteSettings.copyWith(
                                  // noteViewImage: value,
                                  myNoteSettings: viewSettings
                                      .noteSettings.myNoteSettings
                                      .copyWith(
                                    noteViewImage: value,
                                  ),
                                ),
                              ),
                            );
                      },
                    ),
                    const SizedBox(height: 0.5),
                    MeCheckBoxListTile(
                      title: MeTextIcon(
                        text: MeTranslations
                            .instance.bottomSheet_viewConfiguration_memberCount,
                        iconPath: Assets.svg.people.path,
                        iconColor: colorScheme.color35,
                      ),
                      value: showMemberCount,
                      onChanged: (value) {
                        context.read<AppBloc>().updateViewSettings(
                              viewSettings: viewSettings!.copyWith(
                                noteSettings:
                                    viewSettings.noteSettings.copyWith(
                                  myNoteSettings: viewSettings
                                      .noteSettings.myNoteSettings
                                      .copyWith(
                                    showMemberCount: value,
                                  ),
                                ),
                              ),
                            );
                      },
                    ),
                    if (showHashtag != null) ...[
                      const SizedBox(height: 0.5),
                      MeCheckBoxListTile(
                        title: MeTextIcon(
                          text: MeTranslations.instance.screen_common_hashtag,
                          iconPath: Assets.svg.hashtagIcon.path,
                          iconColor: colorScheme.color35,
                        ),
                        value: showHashtag,
                        onChanged: (value) {
                          context.read<AppBloc>().updateViewSettings(
                                viewSettings: viewSettings!.copyWith(
                                  noteSettings:
                                      viewSettings.noteSettings.copyWith(
                                    // noteViewTags: value,
                                    myNoteSettings: viewSettings
                                        .noteSettings.myNoteSettings
                                        .copyWith(
                                      noteViewTags: value,
                                    ),
                                  ),
                                ),
                              );
                        },
                      ),
                    ],
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }
}

class NoteTabSettingWidget extends StatelessWidget {
  const NoteTabSettingWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            SizeConstants.bottomSheetBorderRadius,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          DialogAndBottomSheetAppBar(
            title:
                MeTranslations.instance.bottomSheet_noteKebabMenu_titleTopBar,
          ),
          BlocBuilder<AppBloc, AppState>(
            buildWhen: (previous, current) => shouldBuild(previous, current),
            builder: (context, state) {
              ViewSettings? viewSettings = state.viewSettings;
              DescriptionType? descriptionType =
                  viewSettings?.noteSettings.myNoteSettings.noteDescriptionType;
              bool? showTime =
                  viewSettings?.noteSettings.myNoteSettings.noteViewTime;

              bool? showDate =
                  viewSettings?.noteSettings.myNoteSettings.noteViewDate;
              bool? showImage =
                  viewSettings?.noteSettings.myNoteSettings.noteViewImage;
              bool? showMemberCount =
                  viewSettings?.noteSettings.myNoteSettings.showMemberCount;
              bool? showMood =
                  viewSettings?.noteSettings.myNoteSettings.noteViewMood;
              bool? showHashtag =
                  viewSettings?.noteSettings.myNoteSettings.noteViewTags;
              NotesGroupBy? notesGroupBy =
                  viewSettings?.noteSettings.myNoteSettings.notesGroupBy;

              bool? isCollapsedView =
                  viewSettings?.noteSettings.myNoteSettings.collapsedView;

              bool? showCounts =
                  viewSettings?.noteSettings.myNoteSettings.showCounts;

              MeString getViewConfigText() {
                String text = '';
                if (descriptionType != DescriptionType.none) {
                  text +=
                      '${MeTranslations.instance.screen_common_description.text}, ';
                }
                if (showMood!) {
                  text +=
                      '${MeTranslations.instance.bottomSheet_viewConfiguration_mood.text}, ';
                }
                if (showDate!) {
                  text +=
                      '${MeTranslations.instance.screen_common_date.text}, ';
                }
                if (showTime!) {
                  text +=
                      '${MeTranslations.instance.screen_common_time.text}, ';
                }
                if (showImage!) {
                  text +=
                      '${MeTranslations.instance.screen_common_attachment.text}, ';
                }
                if (showMemberCount == true) {
                  text +=
                      '${MeTranslations.instance.bottomSheet_viewConfiguration_memberCount.text}, ';
                }

                if (showHashtag == true) {
                  text +=
                      '${MeTranslations.instance.screen_common_hashtag.text}, ';
                }
                return text.isEmpty
                    ? MeTranslations.instance.screen_common_none
                    : MeString(text.substring(0, text.length - 2));
              }

              ViewSettingType viewSettingType =
                  viewSettings!.noteSettings.myNoteSettings.viewType;

              return ListView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  GroupByWithConfig<NotesGroupBy, NoteGroupConfig>(
                    selectedTrailingPopMenuItem: notesGroupBy!,
                    trailingPopMenuValues: NotesGroupBy.values,
                    onTrailingPopMenuItemSelected: (NotesGroupBy item) {
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: viewSettings.copyWith(
                              noteSettings: viewSettings.noteSettings.copyWith(
                                myNoteSettings: viewSettings
                                    .noteSettings.myNoteSettings
                                    .copyWith(
                                  notesGroupBy: item,
                                ),
                              ),
                            ),
                          );
                    },
                    selectedSubtitlePopMenuItem: {
                      if (isCollapsedView!) NoteGroupConfig.collapsedView,
                      if (showCounts!) NoteGroupConfig.showCounts,
                    },
                    subtitlePopMenuValues: NoteGroupConfig.values,
                    onSubtitlePopMenuItemSelected: (Set<NoteGroupConfig> item) {
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: viewSettings.copyWith(
                              noteSettings: viewSettings.noteSettings.copyWith(
                                myNoteSettings: viewSettings
                                    .noteSettings.myNoteSettings
                                    .copyWith(
                                  collapsedView: item.contains(
                                    NoteGroupConfig.collapsedView,
                                  ),
                                  showCounts: item.contains(
                                    NoteGroupConfig.showCounts,
                                  ),
                                ),
                              ),
                            ),
                          );
                    },
                    hideSubtitle: notesGroupBy == NotesGroupBy.none,
                    leadingTapDisabled: notesGroupBy == NotesGroupBy.none,
                    trailingOffset: -168,
                  ),
                  const SizedBox(height: 1),
                  ViewSettingsWidget<ViewSettingType>(
                    offset: -120,
                    subtitleText: getViewConfigText(),
                    leadingOnTap: () {
                      showMeScrollableModalBottomSheet(
                        bottomSheetLevel: 1,
                        builder: (_, __) {
                          return const NoteTabViewSettingWidget();
                        },
                      );
                    },
                    selectedTrailingPopMenuItem: viewSettingType,
                    disableLeadingIfTrailingSelectedAs: ViewSettingType
                        .getValues
                        .where((element) => element != ViewSettingType.custom)
                        .toList(),
                    trailingPopMenuValues: ViewSettingType.getValues,
                    onTrailingPopMenuItemSelected: (ViewSettingType item) {
                      MyNoteSettings settings =
                          viewSettings.noteSettings.myNoteSettings;

                      if (item == ViewSettingType.defaultView) {
                        settings = settings.copyWith(
                          noteViewTime: true,
                          noteViewDate: true,
                          noteViewMood: true,
                          noteViewImage: true,
                          noteViewTags: false,
                          showMemberCount: true,
                          noteDescriptionType: DescriptionType.none,
                        );
                      } else if (item == ViewSettingType.custom) {
                        settings = settings.copyWith(
                          noteViewTime: true,
                          noteViewDate: true,
                          noteViewMood: true,
                          noteViewImage: true,
                          noteViewTags: false,
                          showMemberCount: true,
                          noteDescriptionType: DescriptionType.none,
                        );
                      } else {
                        settings = settings.copyWith(
                          noteViewTime: false,
                          noteViewDate: true,
                          noteViewMood: false,
                          noteViewImage: false,
                          noteViewTags: false,
                          showMemberCount: false,
                          noteDescriptionType: DescriptionType.none,
                        );
                      }

                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: viewSettings.copyWith(
                              noteSettings: viewSettings.noteSettings.copyWith(
                                myNoteSettings: settings.copyWith(
                                  viewType: item,
                                ),
                              ),
                            ),
                          );
                    },
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
