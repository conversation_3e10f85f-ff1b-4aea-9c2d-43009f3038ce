import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/constants/animation_constants.dart';
import 'package:mevolve/constants/app_strings.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/common_sharing_model.dart';
import 'package:mevolve/data/enums/document_type.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/enums/share_type.dart';
import 'package:mevolve/data/models/in_app_notification.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/list_item.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/public/public_users.dart';
import 'package:mevolve/data/models/user/user_resources.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/public_user_cubit.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_cubit.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_state.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/app/widgets/styles/me_text_icon.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/hamburger/subscription/effective_subscription_cubit/effective_subscription_cubit.dart';
import 'package:mevolve/features/hamburger/subscription/helpers/subscription_helper.dart';
import 'package:mevolve/features/lists/cubit/copy_move_list/copy_move_list_cubit.dart';
import 'package:mevolve/features/lists/cubit/my_list/list_operations.dart';
import 'package:mevolve/features/lists/functions/collaboration_notification.dart';
import 'package:mevolve/features/lists/functions/list_to_image.dart';
import 'package:mevolve/features/lists/widgets/colaborate_sheet/list_collaborate_sheet.dart';
import 'package:mevolve/features/lists/widgets/copy_move/copy_move_item_sheet.dart';
import 'package:mevolve/features/lists/widgets/list_filter_chip.dart';
import 'package:mevolve/features/lists/widgets/list_item_sheet/add_item_textfields_widget.dart';
import 'package:mevolve/features/lists/widgets/list_item_sheet/add_item_widget.dart';
import 'package:mevolve/features/lists/widgets/list_item_widget.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/functions/list_option_functions.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/list_option_sheet.dart';
import 'package:mevolve/features/public_profile/widgets/add_mevolve_id.dart';
import 'package:mevolve/features/tasks_providers/lists/main_lists_cubit.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/custom_fab_location.dart';
import 'package:mevolve/features/widgets/custom_reorderable_listview.dart';
import 'package:mevolve/features/widgets/empty_state_widget.dart';
import 'package:mevolve/features/widgets/filter_widgets/filter_widget_wrapper.dart';
import 'package:mevolve/features/widgets/filter_widgets/search_filter_input_chip.dart';
import 'package:mevolve/features/widgets/go_to_top_button.dart';
import 'package:mevolve/features/widgets/horizontal_scroll_row.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/show_me_discard_dialog.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/me_menu_anchor.dart';
import 'package:mevolve/utilities/sharing_utility/sharing_firebase_functions.dart';
import 'package:mevolve/utilities/task_component_handler.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:modal_bottom_sheet/modal_bottom_sheet.dart';

enum TypeOfItemSheet {
  normal,
  copy,
  move,
}

ValueNotifier<TypeOfItemSheet> typeOfItemSheet = ValueNotifier(
  TypeOfItemSheet.normal,
);
ValueNotifier<List<ListItem>> filteredItems = ValueNotifier([]);

class ListItemSheet extends StatefulWidget {
  const ListItemSheet._({
    Key? key,
    required this.listId,
  }) : super(key: key);

  final String listId;

  static Widget open({required String listId}) => WidgetTracker(
        trackAction: TrackAction.lists,
        child: MultiBlocProvider(
          providers: [
            BlocProvider.value(
              value: authenticatedGlobalContext!.read<MainListsCubit>(),
            ),
            BlocProvider.value(
              value: authenticatedGlobalContext!.read<PublicUserCubit>(),
            ),
          ],
          child: ListItemSheet._(listId: listId),
        ),
      );

  @override
  State<ListItemSheet> createState() => _ListItemSheetState();
}

class _ListItemSheetState extends State<ListItemSheet>
    with TickerProviderStateMixin {
  late TextEditingController _textEditingController;
  late ScrollController _scrollController;
  late TextEditingController _itemEditTextController;
  late TextEditingController _itemEditDescriptionController;
  late TextEditingController _itemEditCustomTextController;
  late AnimationController _animationController;
  late final Animation<double> _animation = CurvedAnimation(
    parent: _animationController,
    curve: Curves.fastOutSlowIn,
  );

  final CustomStandardFabLocation instance = CustomStandardFabLocation.endFloat(
    floatingActionButtonXMargin: SizeConstants.homeFABXMargin,
    floatingActionButtonYMargin: SizeConstants.homeFABYMargin,
    marginBetweenFabAndSnackBar: SizeConstants.marginBetweenFabAndSnackBar,
  );
  late String listId;
  late ShareType shareType;
  late ListData _currentListData;
  late ListData _defaultListData;

  @override
  void initState() {
    _scrollController = ScrollController();
    listId = widget.listId;
    _textEditingController = TextEditingController();
    _itemEditTextController = TextEditingController();
    _itemEditDescriptionController = TextEditingController();
    _itemEditCustomTextController = TextEditingController();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(
        milliseconds: AnimationConstants.hideBottomBarOnScrollDuration,
      ),
      value: 1,
    );

    super.initState();
    final state = context.read<MainListsCubit>().state;
    _defaultListData = state.taskIdsToTasks[listId]!;
    _currentListData = _defaultListData.copyWith();
    shareType =
        _currentListData.uid != context.read<AppBloc>().state.userData!.uid
            ? ShareType.sharedTask
            : ShareType.myTask;
    // Send read event to analytics.
    // Fetch the list data and send analytics event once during initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (state.taskIdsToTasks.isNotEmpty) {
        bool isShared = _currentListData.members.memberHashedEmails.isNotEmpty;
        // Send read event to analytics.
        EventFormation().sendFeatureCrudActivityEvent(
          actionType: ActionType.read,
          trackAction: TrackAction.lists,
          sharingStatus: isShared ? ShareStatus.shared : ShareStatus.private,
          sharedCount: _currentListData.members.memberHashedEmails.length,
          shareMethod: null,
          userRole: _currentListData.members.getRoleForAnalytics,
        );
      }
    });
  }

  @override
  void dispose() {
    _textEditingController.dispose();
    _itemEditTextController.dispose();
    _itemEditDescriptionController.dispose();
    _itemEditCustomTextController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  bool loading = false;

  // Local filter state management
  bool _showListItemFilterRow = false;
  bool _isItemFilterApplied = false;
  String _searchQuery = '';
  ListItemFilterType _filterType = ListItemFilterType.all;

  void _toggleFilterRowVisibility() {
    setState(() {
      _showListItemFilterRow = !_showListItemFilterRow;
    });
  }

  void _resetFilter() {
    setState(() {
      _isItemFilterApplied = false;
      _showListItemFilterRow = false;
      _searchQuery = '';
      _filterType = ListItemFilterType.all;
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      _searchQuery = query;
      _isItemFilterApplied =
          query.isNotEmpty || _filterType != ListItemFilterType.all;
    });
  }

  void _onFilterChanged(ListItemFilterType type) {
    setState(() {
      _filterType = type;
      _isItemFilterApplied =
          _searchQuery.isNotEmpty || type != ListItemFilterType.all;
    });
  }

  void _onResetFilter() {
    setState(() {
      _searchQuery = '';
      _filterType = ListItemFilterType.all;
      _isItemFilterApplied = false;
    });
  }

  void _initializeFilterState(List<ListItem> items) {
    // Ensure filter row is hidden when list is empty and no filter is applied
    if (items.isEmpty && !_isItemFilterApplied && _showListItemFilterRow) {
      setState(() {
        _showListItemFilterRow = false;
      });
    }
  }

  List<ListItem> _applyFilters(List<ListItem> items) {
    List<ListItem> filteredItems = List.from(items);

    // Apply search filter
    if (_searchQuery.isNotEmpty) {
      filteredItems = filteredItems.where((item) {
        return item.item.toLowerCase().contains(_searchQuery.toLowerCase()) ||
            (item.description
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false) ||
            (item.customText
                    ?.toLowerCase()
                    .contains(_searchQuery.toLowerCase()) ??
                false);
      }).toList();
    }

    // Apply status filter
    switch (_filterType) {
      case ListItemFilterType.checked:
        filteredItems = filteredItems.where((item) => item.done).toList();
        break;
      case ListItemFilterType.unchecked:
        filteredItems = filteredItems.where((item) => !item.done).toList();
        break;
      case ListItemFilterType.all:
        break;
    }

    return filteredItems;
  }

  void _updateUneditedValue({required ListData listData}) {
    // IMP: If a list is popped and new is opened then there might still be an
    // ongoing event happening from previous list in MainListsCubit leading to
    // state emit and calling this update. So, check if that list is same as
    // the current list.
    if (listData.id != _defaultListData.id) {
      return;
    }

    if (_currentListData.deletedAt != listData.deletedAt &&
        listData.deletedAt != null &&
        context.mounted) {
      // Used in case a delete happened from another device.
      TaskComponentHandler.showMeDeleteDialog(
        context: context,
        docType: DocumentType.list,
        title: MeTranslations.instance.overlay_listDelete_title,
        description: MeTranslations.instance.overlay_listDelete_content,
        list: _currentListData,
        isNewTask: false,
        isChangeMade: false,
        handleOnlyAfterDelete: true,
      );
    }

    // Update the current list data with new data from remote
    _currentListData = listData.copyWith();

    _defaultListData = listData.copyWith();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    final listCubit = context.read<ListOperations>();

    return WidgetTracker(
      trackAction: TrackAction.lists,
      child: BlocConsumer<UserResourcesCubit, UserResourcesState>(
        listener: (context, state) {
          if (shareType == ShareType.sharedTask) {
            UserResources? userResources = state.userResources;
            if (userResources != null) {
              if (!userResources.shared.sharedLists.contains(listId)) {
                Navigator.maybePop(context);
              }
            }
          }
        },
        builder: (context, state) {
          return BlocProvider(
            create: (context) => CopyMoveListCubit(
              listCubit: listCubit,
            ),
            child: BlocConsumer<MainListsCubit, MainListsState>(
              listenWhen: (previous, current) => previous != current,
              listener: (context, state) {
                final ListData? list =
                    state.taskIdsToTasks[_defaultListData.id];

                if (list != null) {
                  _updateUneditedValue(listData: list);
                }
              },
              buildWhen: (previous, current) => previous != current,
              builder: (context, state) {
                List<ListItem> listItems = _currentListData.listItems
                    .where((item) => !item.isDeleted)
                    .toList();

                // Initialize filter state based on list content
                _initializeFilterState(listItems);

                // Apply filters
                listItems = _applyFilters(listItems);
                filteredItems.value = listItems;

                final String uid = context.read<AppBloc>().state.userData!.uid;
                bool isVibrate = context
                    .read<AppBloc>()
                    .state
                    .viewSettings!
                    .appSettings
                    .isVibrationEnabled;
                // A scaffold here is needed to show snackBar over bottom-sheet.

                String hashEmail = hashData(getCurrentUserEmail()!);
                bool isEditor = uid == _currentListData.uid
                    ? true
                    : _currentListData.members.membersConfig[hashEmail]!.role ==
                            MemberRole.editor ||
                        _currentListData.uid == uid;

                void clearFields() {
                  _itemEditTextController.clear();
                  _itemEditDescriptionController.clear();
                  _itemEditCustomTextController.clear();
                }

                return ValueListenableBuilder<TypeOfItemSheet>(
                  valueListenable: typeOfItemSheet,
                  builder: (context, type, child) {
                    return type == TypeOfItemSheet.copy ||
                            type == TypeOfItemSheet.move
                        ? CopyMoveItemSheet(
                            parentContext: context,
                            listId: listId,
                            items: listItems,
                            isCopy:
                                typeOfItemSheet.value == TypeOfItemSheet.copy,
                            myUid: uid,
                          )
                        : Stack(
                            children: [
                              // this widget is hidden and used to take
                              // screenshot of the list items
                              ListImage(
                                uid: uid,
                                list: _currentListData,
                                widgetContext: context,
                                listItems: listItems,
                              ),
                              SafeArea(
                                child: Scaffold(
                                  // This is to avoid the keyboard pushing the content up which
                                  // causes the FAB to be hidden.
                                  resizeToAvoidBottomInset: false,
                                  body: Column(
                                    children: [
                                      DialogAndBottomSheetAppBar(
                                        widgetTitle: GestureDetector(
                                          onTap: () {
                                            ListOptionFunctions().update(
                                              _currentListData,
                                              context,
                                            );
                                          },
                                          child: MeText(
                                            text: MeString(
                                              _currentListData.title,
                                            ),
                                            meFontStyle: MeFontStyle.A12,
                                          ),
                                        ),
                                        suffixActions: [
                                          Row(
                                            children: [
                                              listItems.isEmpty &&
                                                      !_isItemFilterApplied
                                                  ? const SizedBox.shrink()
                                                  : MeIconButton(
                                                      key: const ValueKey(
                                                        'filterBtn',
                                                      ),
                                                      isCircularIconButton:
                                                          true,
                                                      iconPath:
                                                          _showListItemFilterRow
                                                              ? Assets
                                                                  .svg
                                                                  .filterRowVisible
                                                                  .path
                                                              : Assets
                                                                  .svg
                                                                  .filterRowHidden
                                                                  .path,
                                                      buttonColor:
                                                          colorScheme.color9,
                                                      iconColor:
                                                          colorScheme.color12,
                                                      iconContainerSize:
                                                          const SizedBox(
                                                        width: SizeConstants
                                                            .iconButtonIconContainerSize,
                                                        height: SizeConstants
                                                            .iconButtonIconContainerSize,
                                                      ),
                                                      padding: const EdgeInsets
                                                          .symmetric(
                                                        horizontal: 12,
                                                        vertical: 12,
                                                      ),
                                                      onPressed: () {
                                                        if (_isItemFilterApplied &&
                                                            _showListItemFilterRow) {
                                                          showDialog<bool>(
                                                            useRootNavigator:
                                                                false,
                                                            context: context,
                                                            builder: (
                                                              BuildContext
                                                                  childContext,
                                                            ) {
                                                              return MeDialog(
                                                                tertiaryText:
                                                                    MeTranslations
                                                                        .instance
                                                                        .overlay_filterApplied_proceed,
                                                                secondaryText:
                                                                    MeTranslations
                                                                        .instance
                                                                        .screen_common_buttonCancel,
                                                                title: MeTranslations
                                                                    .instance
                                                                    .overlay_filterApplied_title,
                                                                description:
                                                                    MeTranslations
                                                                        .instance
                                                                        .overlay_filterApplied_content,
                                                                showBottomDivider:
                                                                    false,
                                                                secondaryOnTap:
                                                                    () {
                                                                  Navigator.of(
                                                                    childContext,
                                                                  ).pop();
                                                                },
                                                                tertiaryOnTap:
                                                                    () {
                                                                  _resetFilter();
                                                                  Navigator.of(
                                                                    childContext,
                                                                  ).pop();
                                                                },
                                                              );
                                                            },
                                                          );
                                                          return;
                                                        }
                                                        _toggleFilterRowVisibility();
                                                      },
                                                    ),

                                              // If collaborators are there or is public
                                              if (_currentListData
                                                      .members
                                                      .memberHashedEmails
                                                      .isNotEmpty ||
                                                  (_currentListData.isPublic &&
                                                      uid ==
                                                          _currentListData.uid))
                                                MeIconButton(
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                    horizontal: 12,
                                                    vertical: 12,
                                                  ),
                                                  iconColor:
                                                      colorScheme.color12,
                                                  key: const ValueKey(
                                                    'collabOrPublicIcon',
                                                  ),
                                                  iconPath: (_currentListData
                                                              .isPublic &&
                                                          uid ==
                                                              _currentListData
                                                                  .uid)
                                                      ? Assets
                                                          .svg.triPeople.path
                                                      : Assets.svg.people.path,
                                                  onPressed: () async {
                                                    // Buttons do the same action as KebabMenuAnchor onTapCollaborate and onTapPublic
                                                    if (_currentListData
                                                            .isPublic &&
                                                        shareType ==
                                                            ShareType.myTask) {
                                                      showMeScrollableModalBottomSheet(
                                                        bottomSheetLevel: 1,
                                                        builder: (_, __) {
                                                          return ListPublicVisibilitySheet(
                                                            listId: listId,
                                                          );
                                                        },
                                                      );
                                                    } else {
                                                      // Same as onTapCollaborate
                                                      showMeScrollableModalBottomSheet(
                                                        bottomSheetLevel: 3,
                                                        builder: (_, __) {
                                                          return ListCollaboratorsSheet(
                                                            listCubit:
                                                                listCubit,
                                                            listId: listId,
                                                          );
                                                        },
                                                      );
                                                    }
                                                  },
                                                ),
                                              KebabMenuAnchor(
                                                isOwner:
                                                    uid == _currentListData.uid,
                                                isFav: _currentListData.isFav,
                                                collaboratorsCount:
                                                    _currentListData
                                                        .members
                                                        .memberHashedEmails
                                                        .length,
                                                isPublic:
                                                    _currentListData.isPublic,
                                                kebabButtonOnTap: (controller) {
                                                  if (controller.isOpen) {
                                                    controller.close();
                                                  } else {
                                                    controller.open();
                                                  }
                                                },
                                                onTapFavourites: () {
                                                  listCubit.updateListFav(
                                                    _currentListData,
                                                  );
                                                },
                                                onTapActions: () async {
                                                  final res =
                                                      await showMeScrollableModalBottomSheet(
                                                    bottomSheetLevel: 1,
                                                    builder: (_, __) {
                                                      return ListActionsSheet(
                                                        listCubit: listCubit,
                                                        copyMoveListCubit:
                                                            CopyMoveListCubit(
                                                          listCubit: listCubit,
                                                        ),
                                                        listData:
                                                            _currentListData,
                                                      );
                                                    },
                                                  );
                                                  if (!context.mounted) {
                                                    return;
                                                  }
                                                  switch (res) {
                                                    case ListOptionTypes
                                                          .listCloneDone:
                                                      Navigator.pop(context);
                                                    default:
                                                      break;
                                                  }
                                                },
                                                onTapManage: () async {
                                                  await showMeScrollableModalBottomSheet(
                                                    bottomSheetLevel: 1,
                                                    builder: (_, __) {
                                                      return ListManageSheet(
                                                        listData:
                                                            _currentListData,
                                                        listCubit: listCubit,
                                                        listId: listId,
                                                      );
                                                    },
                                                  );
                                                },
                                                onTapShare: () async {
                                                  await showMeScrollableModalBottomSheet(
                                                    bottomSheetLevel: 1,
                                                    builder: (_, __) {
                                                      return ListShareSheet(
                                                        listData:
                                                            _currentListData,
                                                        listCubit: listCubit,
                                                      );
                                                    },
                                                  );
                                                },
                                                onTapCollaborate: () async {
                                                  showMeScrollableModalBottomSheet(
                                                    bottomSheetLevel: 3,
                                                    builder: (_, __) {
                                                      return ListCollaboratorsSheet(
                                                        listCubit: listCubit,
                                                        listId: listId,
                                                      );
                                                    },
                                                  );
                                                },
                                                onTapPublic: () async {
                                                  if (shareType ==
                                                      ShareType.myTask) {
                                                    final createProFeatures =
                                                        context
                                                            .read<
                                                                EffectiveSubscriptionCubit>()
                                                            .state
                                                            .eCreateProFeatures;

                                                    if (!createProFeatures) {
                                                      SubscriptionHelper
                                                          .showFeatureLockedDialog(
                                                        context,
                                                      );

                                                      return;
                                                    }
                                                    PublicUser? publicUser =
                                                        context
                                                            .read<
                                                                PublicUserCubit>()
                                                            .state
                                                            .publicUser;
                                                    final canUserShareFunctionality =
                                                        publicUser != null;
                                                    if (!canUserShareFunctionality) {
                                                      bool? res =
                                                          await UtilityMethods
                                                              .showCompletePublicProfileDialog(
                                                        context: context,
                                                      );
                                                      if (res == null || !res) {
                                                        return;
                                                      }
                                                      if (res) {
                                                        String? idAdded =
                                                            await addMevolveIdBottomSheet();
                                                        if (idAdded == null) {
                                                          return;
                                                        }
                                                      }
                                                    }
                                                  }

                                                  showMeScrollableModalBottomSheet(
                                                    bottomSheetLevel: 1,
                                                    builder: (_, __) {
                                                      return ListPublicVisibilitySheet(
                                                        listId: listId,
                                                      );
                                                    },
                                                  );
                                                },
                                                onTapBlock: () async {
                                                  bool isOffline = context
                                                          .read<AppBloc>()
                                                          .state
                                                          .internetConnectionState ==
                                                      InternetConnectionState
                                                          .disconnected;

                                                  if (isOffline) {
                                                    UtilityMethods
                                                        .showOfflineDialogOrPerformAction(
                                                      internetActiveFunction:
                                                          () {},
                                                      internetConnectionState:
                                                          context
                                                              .read<AppBloc>()
                                                              .state
                                                              .internetConnectionState,
                                                      context: context,
                                                    );
                                                    return;
                                                  }
                                                  bool? res = await showDialog(
                                                    context: context,
                                                    builder: (context) =>
                                                        MeDialog(
                                                      title: MeTranslations
                                                          .instance
                                                          .overlay_blockList_block,
                                                      description: MeTranslations
                                                          .instance
                                                          .overlay_blockList_content,
                                                      quaternaryText: MeTranslations
                                                          .instance
                                                          .overlay_blockList_blockbutton,
                                                      quaternaryOnTap: () =>
                                                          Navigator.pop(
                                                        context,
                                                        true,
                                                      ),
                                                      secondaryOnTap: () =>
                                                          Navigator.pop(
                                                        context,
                                                        false,
                                                      ),
                                                      secondaryText: MeTranslations
                                                          .instance
                                                          .screen_common_buttonCancel,
                                                      primaryButtonFontStyle:
                                                          MeFontStyle.C11,
                                                    ),
                                                  );
                                                  if (res == true &&
                                                      context.mounted) {
                                                    UtilityMethods
                                                        .showMeLoaderDialog(
                                                      context,
                                                    );
                                                    await SharingFirebaseFunctions()
                                                        .leaveOrBlockTask(
                                                      listId,
                                                      context
                                                          .read<AppBloc>()
                                                          .state
                                                          .user!
                                                          .uid,
                                                      FirebaseDocCollectionType
                                                          .lists,
                                                      MemberStatus.blocked,
                                                    );
                                                    if (!context.mounted) {
                                                      return;
                                                    }
                                                    SendNotificationList()
                                                        .notifyOwnerUserBlocked(
                                                      title: _currentListData
                                                          .title,
                                                      taskType:
                                                          InAppNotificationTaskType
                                                              .list,
                                                      taskId:
                                                          _currentListData.id,
                                                      operationBy: context
                                                          .read<AppBloc>()
                                                          .state
                                                          .userData!
                                                          .userInfo
                                                          .name,
                                                      uid: _currentListData.uid,
                                                      ownerHashedEmail:
                                                          hashData(
                                                        _currentListData
                                                            .ownerEmail,
                                                      ),
                                                    );
                                                    if (context.mounted) {
                                                      Navigator.pop(context);
                                                    }
                                                  }
                                                },
                                                onTapLeave: () async {
                                                  bool isOffline = context
                                                          .read<AppBloc>()
                                                          .state
                                                          .internetConnectionState ==
                                                      InternetConnectionState
                                                          .disconnected;
                                                  if (isOffline) {
                                                    UtilityMethods
                                                        .showOfflineDialogOrPerformAction(
                                                      internetActiveFunction:
                                                          () {},
                                                      internetConnectionState:
                                                          context
                                                              .read<AppBloc>()
                                                              .state
                                                              .internetConnectionState,
                                                      context: context,
                                                    );
                                                    return;
                                                  }
                                                  bool res = await showDialog(
                                                    context: context,
                                                    builder: (context) =>
                                                        MeDialog(
                                                      title: MeTranslations
                                                          .instance
                                                          .overlay_leaveList_title,
                                                      description: MeTranslations
                                                          .instance
                                                          .overlay_leaveList_content,
                                                      tertiaryOnTap: () =>
                                                          Navigator.pop(
                                                        context,
                                                        true,
                                                      ),
                                                      tertiaryText: MeTranslations
                                                          .instance
                                                          .screen_common_sharingLeave,
                                                      secondaryOnTap: () =>
                                                          Navigator.pop(
                                                        context,
                                                        false,
                                                      ),
                                                      secondaryText: MeTranslations
                                                          .instance
                                                          .screen_common_buttonCancel,
                                                    ),
                                                  );
                                                  if (res && context.mounted) {
                                                    UtilityMethods
                                                        .showMeLoaderDialog(
                                                      context,
                                                    );
                                                    await SharingFirebaseFunctions()
                                                        .leaveOrBlockTask(
                                                      listId,
                                                      context
                                                          .read<AppBloc>()
                                                          .state
                                                          .user!
                                                          .uid,
                                                      FirebaseDocCollectionType
                                                          .lists,
                                                      MemberStatus.left,
                                                    );
                                                    if (!context.mounted) {
                                                      return;
                                                    }
                                                    SendNotificationList()
                                                        .notifyOwnerUserLeft(
                                                      title: _currentListData
                                                          .title,
                                                      taskType:
                                                          InAppNotificationTaskType
                                                              .list,
                                                      taskId:
                                                          _currentListData.id,
                                                      operationBy: context
                                                          .read<AppBloc>()
                                                          .state
                                                          .userData!
                                                          .userInfo
                                                          .name,
                                                      uid: _currentListData.uid,
                                                      ownerHashedEmail:
                                                          hashData(
                                                        _currentListData
                                                            .ownerEmail,
                                                      ),
                                                    );
                                                    if (context.mounted) {
                                                      Navigator.pop(context);
                                                    }
                                                  }
                                                },
                                              ),
                                            ],
                                          ),
                                        ],
                                      ),
                                      _showListItemFilterRow
                                          ? !_isItemFilterApplied &&
                                                  listItems.isEmpty
                                              ? const SizedBox.shrink()
                                              : ListItemsFilterWidget(
                                                  onSearchChanged:
                                                      _onSearchChanged,
                                                  onFilterChanged:
                                                      _onFilterChanged,
                                                  onResetFilter: _onResetFilter,
                                                  initialSearchQuery:
                                                      _searchQuery,
                                                  initialFilterType:
                                                      _filterType,
                                                )
                                          : const SizedBox.shrink(),
                                      Expanded(
                                        child:
                                            _isItemFilterApplied &&
                                                    listItems.isEmpty
                                                ? EmptyStateWidget(
                                                    imagePath: AppStrings
                                                        .recordsEmptyIllustration(
                                                      colorScheme: colorScheme,
                                                    ),
                                                    imageText: MeTranslations
                                                        .instance
                                                        .screen_common_recordsNotFound,
                                                  )
                                                : listItems.isEmpty &&
                                                        !_isItemFilterApplied
                                                    ? Center(
                                                        child: EmptyStateWidget(
                                                          imagePath: AppStrings
                                                              .listOfItemsEmptyIllustration(
                                                            colorScheme:
                                                                colorScheme,
                                                          ),
                                                          imageTextStyle:
                                                              MeFontStyle.C8,
                                                          imageText: MeTranslations
                                                              .instance
                                                              .bottomSheet_listItems_emptyScreenContent,
                                                        ),
                                                      )
                                                    : NotificationListener<
                                                        UserScrollNotification>(
                                                        onNotification:
                                                            (notification) {
                                                          if (notification
                                                                  .direction ==
                                                              ScrollDirection
                                                                  .forward) {
                                                            _animationController
                                                                .forward();
                                                          }
                                                          if (notification
                                                                  .direction ==
                                                              ScrollDirection
                                                                  .reverse) {
                                                            _animationController
                                                                .reverse();
                                                          }
                                                          if (notification
                                                                  .metrics
                                                                  .pixels ==
                                                              notification
                                                                  .metrics
                                                                  .maxScrollExtent) {
                                                            _animationController
                                                                .forward();
                                                          }
                                                          return false;
                                                        },
                                                        child: Stack(
                                                          children: [
                                                            SlidableAutoCloseBehavior(
                                                              child:
                                                                  CustomReorderableListView
                                                                      .separated(
                                                                proxyDecorator:
                                                                    (
                                                                  originalWidget,
                                                                  index,
                                                                  proxy,
                                                                ) {
                                                                  return Transform(
                                                                    transform: MePlatform
                                                                            .isWeb
                                                                        ? Matrix4
                                                                            .translationValues(
                                                                            0,
                                                                            0,
                                                                            0,
                                                                          )
                                                                        : Matrix4
                                                                            .translationValues(
                                                                            10,
                                                                            10,
                                                                            0,
                                                                          ),
                                                                    child:
                                                                        originalWidget,
                                                                  );
                                                                },
                                                                buildDefaultDragHandles: uid ==
                                                                        _currentListData
                                                                            .uid
                                                                    ? listItems.length >
                                                                            1
                                                                        ? true
                                                                        : false
                                                                    : false,
                                                                scrollController:
                                                                    ModalScrollController
                                                                        .of(
                                                                  context,
                                                                ),
                                                                physics:
                                                                    const ClampingScrollPhysics(),
                                                                footer:
                                                                    const Column(
                                                                  mainAxisSize:
                                                                      MainAxisSize
                                                                          .min,
                                                                  children: [
                                                                    SizedBox(
                                                                      height:
                                                                          100,
                                                                    ),
                                                                  ],
                                                                ),
                                                                separatorBuilder:
                                                                    (
                                                                  _,
                                                                  __,
                                                                ) =>
                                                                        const SizedBox
                                                                            .shrink(),
                                                                itemBuilder:
                                                                    (_, index) {
                                                                  final listItem =
                                                                      listItems
                                                                              .toList()[
                                                                          index];
                                                                  final String
                                                                      keyString =
                                                                      '${listItem.id}_$index';
                                                                  return Material(
                                                                    key:
                                                                        ValueKey(
                                                                      keyString,
                                                                    ),
                                                                    child:
                                                                        Column(
                                                                      mainAxisSize:
                                                                          MainAxisSize
                                                                              .min,
                                                                      key:
                                                                          ValueKey(
                                                                        keyString,
                                                                      ),
                                                                      children: [
                                                                        ListItemWidget(
                                                                          key:
                                                                              ValueKey(
                                                                            keyString,
                                                                          ),
                                                                          list:
                                                                              _currentListData,
                                                                          uid:
                                                                              uid,
                                                                          listItem:
                                                                              listItem,
                                                                          onEdit:
                                                                              () async {
                                                                            _itemEditTextController.text =
                                                                                listItem.item;
                                                                            _itemEditDescriptionController.text =
                                                                                listItem.description ?? '';
                                                                            _itemEditCustomTextController.text =
                                                                                listItem.customText ?? '';
                                                                            Future<bool?>?
                                                                                onFieldSubmitted(
                                                                              TextEditingController textEditingController,
                                                                              TextEditingController descriptionEditingController,
                                                                              TextEditingController customTextEditingController,
                                                                            ) {
                                                                              bool isItemChanged = listItem.item.toLowerCase() != textEditingController.text.toLowerCase();
                                                                              bool isDescriptionChanged = listItem.description != descriptionEditingController.text;
                                                                              bool isCustomTextChanged = listItem.customText != customTextEditingController.text;
                                                                              if (isItemChanged || isDescriptionChanged || isCustomTextChanged) {
                                                                                int itemIndexFromSelectedList = _currentListData.listItems.indexWhere(
                                                                                  (element) => element.item.toLowerCase() == textEditingController.text.toLowerCase(),
                                                                                );
                                                                                if (itemIndexFromSelectedList != -1 && itemIndexFromSelectedList != index) {
                                                                                  showDialog(
                                                                                    context: context,
                                                                                    builder: (context) => MeDialog(
                                                                                      title: MeTranslations.instance.overlay_listItemAddDuplicate_title,
                                                                                      description: MeTranslations.instance.overlay_listItemAddDuplicate_content,
                                                                                      titleFontStyle: MeFontStyle.C8,
                                                                                      primaryText: MeTranslations.instance.screen_common_ok,
                                                                                      primaryOnTap: () => {
                                                                                        Navigator.pop(
                                                                                          context,
                                                                                          true,
                                                                                        ),
                                                                                      },
                                                                                    ),
                                                                                  );
                                                                                  return null;
                                                                                }
                                                                                int indexFromSelectedList = _currentListData.listItems.indexOf(
                                                                                  listItems[index],
                                                                                );
                                                                                final originalItem = _currentListData.listItems[indexFromSelectedList];
                                                                                final updatedItem = originalItem.copyWith(
                                                                                  item: textEditingController.text,
                                                                                  lastUpdatedBy: LastUpdatedByUtils.createLastUpdatedBy(),
                                                                                  description: descriptionEditingController.text,
                                                                                  customText: customTextEditingController.text,
                                                                                  lastUpdatedAt: DateTime.now(),
                                                                                  position: originalItem.position,
                                                                                );
                                                                                _currentListData = _currentListData.copyWith(
                                                                                  listItems: _currentListData.listItems..[indexFromSelectedList] = updatedItem,
                                                                                );
                                                                                // Update the list without reordering to preserve position
                                                                                listCubit.updateList(
                                                                                  list: _currentListData,
                                                                                );
                                                                                clearFields();
                                                                                Navigator.pop(
                                                                                  context,
                                                                                );
                                                                              } else {
                                                                                clearFields();
                                                                                Navigator.pop(
                                                                                  context,
                                                                                );
                                                                              }
                                                                              return null;
                                                                            }

                                                                            ItemNameDescCustomTextWidget
                                                                                wid =
                                                                                ItemNameDescCustomTextWidget(
                                                                              listData: _currentListData,
                                                                              textEditingController: _itemEditTextController,
                                                                              descriptionEditingController: _itemEditDescriptionController,
                                                                              customTextEditingController: _itemEditCustomTextController,
                                                                              onFieldSubmitted: (c) {
                                                                                return onFieldSubmitted(
                                                                                  _itemEditTextController,
                                                                                  _itemEditDescriptionController,
                                                                                  _itemEditCustomTextController,
                                                                                );
                                                                              },
                                                                              popVoid: () async {
                                                                                if (_itemEditTextController.text.isEmpty && _itemEditDescriptionController.text.isEmpty && _itemEditCustomTextController.text.isEmpty) {
                                                                                  Navigator.pop(context);
                                                                                } else if (_itemEditTextController.text == listItem.item && _itemEditDescriptionController.text == listItem.description && _itemEditCustomTextController.text == listItem.customText) {
                                                                                  Navigator.pop(context);
                                                                                } else {
                                                                                  DiscardDialogActionType? res = await showMeDiscardDialogV2(
                                                                                    context,
                                                                                    allowActionType: {
                                                                                      DiscardDialogActionType.discard,
                                                                                      DiscardDialogActionType.save,
                                                                                    },
                                                                                  );
                                                                                  if (!context.mounted) return;
                                                                                  if (res == DiscardDialogActionType.save) {
                                                                                    onFieldSubmitted(
                                                                                      _itemEditTextController,
                                                                                      _itemEditDescriptionController,
                                                                                      _itemEditCustomTextController,
                                                                                    );
                                                                                  } else if (res == DiscardDialogActionType.discard) {
                                                                                    clearFields();
                                                                                    Navigator.pop(context);
                                                                                  }
                                                                                }
                                                                              },
                                                                              bottomSheetLevel: 1,
                                                                            );
                                                                            if (ScreenSizeState.instance.isBigScreen) {
                                                                              await showDialog(
                                                                                useRootNavigator: false,
                                                                                context: context,
                                                                                builder: (_) {
                                                                                  return Dialog(
                                                                                    insetPadding: ScreenSizeState.instance.getDialogInsetPadding(DialogLevel.level1),
                                                                                    backgroundColor: Colors.transparent,
                                                                                    child: Container(
                                                                                      decoration: const BoxDecoration(
                                                                                        borderRadius: BorderRadius.all(
                                                                                          Radius.circular(16),
                                                                                        ),
                                                                                      ),
                                                                                      clipBehavior: Clip.hardEdge,
                                                                                      child: wid,
                                                                                    ),
                                                                                  );
                                                                                },
                                                                              );
                                                                            } else {
                                                                              await showMeScrollableModalBottomSheet<String?>(
                                                                                bottomSheetLevel: 1,
                                                                                backgroundColor: Colors.transparent,
                                                                                shape: const RoundedRectangleBorder(),
                                                                                builder: (_, showDiscardDialogCallback) {
                                                                                  return wid;
                                                                                },
                                                                              );
                                                                            }
                                                                          },
                                                                          onDoneChanged:
                                                                              (value) {
                                                                            final updatedItems =
                                                                                List<ListItem>.from(_currentListData.listItems);
                                                                            final itemIndex =
                                                                                updatedItems.indexOf(listItems[index]);

                                                                            if (itemIndex !=
                                                                                -1) {
                                                                              updatedItems[itemIndex] = updatedItems[itemIndex].copyWith(
                                                                                done: value,
                                                                                position: updatedItems[itemIndex].position,
                                                                                lastUpdatedAt: DateTime.now(),
                                                                                lastUpdatedBy: LastUpdatedByUtils.createLastUpdatedBy(),
                                                                              );
                                                                            }

                                                                            final updatedListData =
                                                                                _currentListData.copyWith(
                                                                              listItems: updatedItems,
                                                                            );

                                                                            _currentListData =
                                                                                updatedListData;
                                                                            listItems =
                                                                                updatedItems;

                                                                            listCubit.updateList(
                                                                              list: updatedListData,
                                                                            );
                                                                            if (updatedListData.isCompleted) {
                                                                              checkUserFeedback();
                                                                            }
                                                                          },
                                                                          onDelete:
                                                                              () async {
                                                                            ListItem
                                                                                deletedItem =
                                                                                listItem;
                                                                            deletedItem =
                                                                                deletedItem.markAsDeleted();
                                                                            // find the items with the same name and mark it deleted
                                                                            for (int i = 0;
                                                                                i < _currentListData.listItems.length;
                                                                                i++) {
                                                                              if (_currentListData.listItems[i].item == deletedItem.item) {
                                                                                _currentListData.listItems[i] = _currentListData.listItems[i].markAsDeleted();
                                                                              }
                                                                            }
                                                                            // update items with deletedItem
                                                                            final updatedItems =
                                                                                List<ListItem>.from(_currentListData.listItems);
                                                                            updatedItems[index] =
                                                                                deletedItem;
                                                                            _currentListData =
                                                                                _currentListData.copyWith(
                                                                              listItems: updatedItems,
                                                                            );
                                                                            listItems =
                                                                                updatedItems;
                                                                            listCubit.updateList(
                                                                              list: _currentListData,
                                                                            );

                                                                            showMeSnackbar(
                                                                              MeTranslations.instance.toast_listItemDeleted_content(
                                                                                itemName: deletedItem.item,
                                                                              ),
                                                                              MeTranslations.instance.screen_common_undo,
                                                                              () {
                                                                                final restoredItem = deletedItem.restore();
                                                                                final updatedItems = List<ListItem>.from(_currentListData.listItems);
                                                                                final itemIndex = updatedItems.indexWhere((item) => item.id == deletedItem.id);
                                                                                if (itemIndex != -1) {
                                                                                  updatedItems[itemIndex] = restoredItem;
                                                                                  _currentListData = _currentListData.copyWith(listItems: updatedItems);
                                                                                  listItems = updatedItems;
                                                                                  listCubit.updateList(list: _currentListData);
                                                                                }
                                                                              },
                                                                            );
                                                                            // }
                                                                          },
                                                                        ),
                                                                        Divider(
                                                                          height:
                                                                              1,
                                                                          thickness:
                                                                              1,
                                                                          color: context
                                                                              .meColorScheme
                                                                              .color6,
                                                                        ),
                                                                      ],
                                                                    ),
                                                                  );
                                                                },
                                                                itemCount:
                                                                    listItems
                                                                        .length,
                                                                onReorderStart:
                                                                    (s) {
                                                                  if (isVibrate) {
                                                                    HapticFeedback
                                                                        .vibrate();
                                                                  }
                                                                },
                                                                onReorder: (
                                                                  int oldIndex,
                                                                  int newIndex,
                                                                ) async {
                                                                  if (newIndex >
                                                                      oldIndex) {
                                                                    newIndex -=
                                                                        1;
                                                                  }

                                                                  // Get the ordered list of items
                                                                  var orderedItems =
                                                                      List<
                                                                          ListItem>.from(
                                                                    listItems,
                                                                  );

                                                                  // Get the item being moved
                                                                  var movedItem =
                                                                      orderedItems[
                                                                          oldIndex];

                                                                  // Remove from old position and insert at new position
                                                                  orderedItems
                                                                      .removeAt(
                                                                    oldIndex,
                                                                  );
                                                                  orderedItems
                                                                      .insert(
                                                                    newIndex,
                                                                    movedItem,
                                                                  );

                                                                  // Update positions for all affected items
                                                                  if (newIndex ==
                                                                      0) {
                                                                    // Moving to the start
                                                                    orderedItems[
                                                                            0] =
                                                                        movedItem
                                                                            .copyWith(
                                                                      position: orderedItems.length >
                                                                              1
                                                                          ? orderedItems[1].position -
                                                                              ListItem.positionStep
                                                                          : ListItem.firstPosition,
                                                                    );
                                                                  } else if (newIndex >=
                                                                      orderedItems
                                                                              .length -
                                                                          1) {
                                                                    // Moving to the end
                                                                    orderedItems[
                                                                            newIndex] =
                                                                        movedItem
                                                                            .copyWith(
                                                                      position: orderedItems[newIndex - 1]
                                                                              .position +
                                                                          ListItem
                                                                              .positionStep,
                                                                    );
                                                                  } else {
                                                                    // Moving between two items
                                                                    orderedItems[
                                                                            newIndex] =
                                                                        movedItem
                                                                            .copyWith(
                                                                      position:
                                                                          ListItem
                                                                              .getPositionBetween(
                                                                        orderedItems[newIndex -
                                                                                1]
                                                                            .position,
                                                                        orderedItems[newIndex +
                                                                                1]
                                                                            .position,
                                                                      ),
                                                                    );
                                                                  }

                                                                  // Update the list data with new order
                                                                  _currentListData =
                                                                      _currentListData
                                                                          .copyWith(
                                                                    listItems:
                                                                        orderedItems,
                                                                  );

                                                                  // Update the filtered items for display
                                                                  listItems =
                                                                      orderedItems;

                                                                  // Persist changes
                                                                  listCubit
                                                                      .updateList(
                                                                    list:
                                                                        _currentListData,
                                                                  );

                                                                  if (isVibrate) {
                                                                    HapticFeedback
                                                                        .vibrate();
                                                                  }
                                                                },
                                                              ),
                                                            ),
                                                            GoToTopButton(
                                                              scrollController:
                                                                  _scrollController,
                                                            ),
                                                          ],
                                                        ),
                                                      ),
                                      ),
                                    ],
                                  ),
                                  floatingActionButtonLocation: instance,
                                  floatingActionButton: isEditor
                                      ? AddItemWidget(
                                          uid: uid,
                                          listId: listId,
                                          animation: _animation,
                                          listCubit: listCubit,
                                          listData: _currentListData,
                                          selectedListData: _currentListData,
                                          userName: context
                                              .read<AppBloc>()
                                              .state
                                              .userData!
                                              .userInfo
                                              .name,
                                          onListDataChanged: (listData) {
                                            _currentListData = listData;
                                          },
                                        )
                                      : const SizedBox.shrink(),
                                ),
                              ),
                            ],
                          );
                  },
                );
              },
            ),
          );
        },
      ),
    );
  }
}

class KebabMenuAnchor extends StatefulWidget {
  const KebabMenuAnchor({
    Key? key,
    required this.isOwner,
    required this.isFav,
    required this.isPublic,
    required this.collaboratorsCount,
    this.onTapFavourites,
    this.onTapActions,
    this.onTapManage,
    this.onTapShare,
    this.onTapCollaborate,
    this.onTapPublic,
    this.onTapLeave,
    this.onTapBlock,
    required this.kebabButtonOnTap,
  }) : super(key: key);

  final bool isFav;
  final bool isOwner;
  final bool isPublic;
  final int collaboratorsCount;
  final void Function()? onTapFavourites;
  final void Function()? onTapActions;
  final void Function()? onTapManage;
  final void Function()? onTapShare;
  final void Function()? onTapCollaborate;
  final void Function()? onTapPublic;
  final void Function()? onTapLeave;
  final void Function()? onTapBlock;
  final void Function(CustomMenuController controller) kebabButtonOnTap;

  @override
  State<KebabMenuAnchor> createState() => _KebabMenuAnchorState();
}

class _KebabMenuAnchorState extends State<KebabMenuAnchor> {
  final CustomMenuController controller = CustomMenuController();
  final double menuItemHeight = 44.0;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    // Build menu items
    final menuItems = _buildMenuItems(colorScheme);
    final totalHeightOfMenu =
        menuItemHeight * menuItems.length + menuItems.length - 1;

    return CustomMenuAnchor(
      controller: controller,
      alignmentOffset: const Offset(-116, -10),
      style: _buildMenuStyle(colorScheme, totalHeightOfMenu),
      builder: (
        BuildContext context,
        CustomMenuController controller,
        Widget? child,
      ) {
        return MeIconButton(
          key: const ValueKey('listKebabMenuButton'),
          onPressed: () => widget.kebabButtonOnTap(controller),
          iconPath: Assets.svg.moreVertical.path,
          buttonColor: colorScheme.color9,
          iconColor: colorScheme.color12,
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 12),
        );
      },
      menuChildren: [
        const SizedBox(width: 155),
        ...menuItems.asMap().entries.map((entry) {
          final isLast = entry.key == menuItems.length - 1;
          return _wrapWithDivider(entry.value, colorScheme, isLast);
        }),
      ],
    );
  }

  List<Widget> _buildMenuItems(MeColorScheme colorScheme) {
    final menuItems = <Widget>[];

    if (widget.isOwner) {
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.screen_common_favourite,
          iconPath: widget.isFav
              ? Assets.svg.favoriteStar.path
              : Assets.svg.starIcon.path,
          iconColor: colorScheme.color35,
          onTap: widget.onTapFavourites,
          padding: 14.5,
        ),
      );
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.bottomSheet_actions_titleTopBar,
          iconPath: Assets.svg.actions.path,
          iconColor: colorScheme.color35,
          onTap: widget.onTapActions,
          padding: 14.5,
        ),
      );
    }
    menuItems.add(
      _buildMenuItem(
        text: MeTranslations.instance.bottomSheet_manageList_titleTopBar,
        iconPath: Assets.svg.manage.path,
        iconColor: colorScheme.color35,
        onTap: widget.onTapManage,
        padding: 14.5,
      ),
    );
    menuItems.add(
      _buildMenuItem(
        text: MeTranslations.instance.screen_common_share,
        iconPath: Assets.svg.icShare.path,
        iconColor: colorScheme.color35,
        onTap: widget.onTapShare,
        padding: 14.5,
      ),
    );
    if (widget.isOwner) {
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.screen_common_collaborate,
          iconPath: Assets.svg.people.path,
          iconColor: colorScheme.color35,
          onTap: widget.onTapCollaborate,
          padding: 14.5,
          // Display collaborator count if available
          trailing: Container(
            width: 18,
            height: 18,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorScheme.color10,
              border: Border.all(
                color: colorScheme.color1,
                width: 0.5,
              ),
            ),
            child: Center(
              child: widget.collaboratorsCount != 0
                  ? MeText(
                      text: MeString.fromVariable(
                        widget.collaboratorsCount.toString(),
                      ),
                      meFontStyle: MeFontStyle.I35,
                    )
                  : MeIcon(
                      iconPath: Assets.svg.plusIcon.path,
                      iconColor: colorScheme.color35,
                      iconSize: const SizedBox(height: 8, width: 8),
                    ),
            ),
          ),
        ),
      );

      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.dropdown_listItemKebabMenu_public,
          iconPath: Assets.svg.triPeople.path,
          iconColor: colorScheme.color35,
          onTap: widget.onTapPublic,
          padding: 14.5,
          // Display if public or not
          trailing: Container(
            width: 18,
            height: 18,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: colorScheme.color10,
              border: Border.all(
                color: colorScheme.color1,
                width: 0.5,
              ),
            ),
            child: Center(
              child: MeIcon(
                iconPath: widget.isPublic
                    ? Assets.svg.checkmark.path
                    : Assets.svg.closeIcon.path,
                iconColor: colorScheme.color35,
                iconSize: const SizedBox(height: 8, width: 8),
              ),
            ),
          ),
        ),
      );
    }

    if (!widget.isOwner) {
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.screen_common_leave,
          iconPath: Assets.svg.leaveIcon.path,
          iconColor: colorScheme.color14,
          onTap: widget.onTapLeave,
          padding: 14.5,
        ),
      );
      menuItems.add(
        _buildMenuItem(
          text: MeTranslations.instance.screen_common_block,
          iconPath: Assets.svg.blockIcon.path,
          iconColor: colorScheme.color11,
          onTap: widget.onTapBlock,
          padding: 14.5,
        ),
      );
    }

    return menuItems;
  }

  Widget _buildMenuItem({
    required MeString text,
    required String iconPath,
    required Color iconColor,
    Widget? trailing,
    required void Function()? onTap,
    required double padding,
  }) {
    return SizedBox(
      height: menuItemHeight,
      child: InkWell(
        splashFactory: InkSparkle.splashFactory,
        onTap: () {
          controller.close();
          onTap?.call();
        },
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: padding),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              MeTextIcon(
                text: text,
                meFontStyle: MeFontStyle.F8,
                iconPath: iconPath,
                iconColor: iconColor,
                iconContainerSize: const SizedBox(width: 18, height: 18),
                verticallyCenterTitleText: true,
              ),
              if (trailing != null) ...[
                trailing,
              ],
            ],
          ),
        ),
      ),
    );
  }

  MenuStyle _buildMenuStyle(MeColorScheme colorScheme, double totalHeight) {
    return MenuStyle(
      alignment: Alignment.bottomLeft,
      backgroundColor:
          WidgetStateProperty.resolveWith((_) => colorScheme.color5),
      padding: WidgetStateProperty.all(
        const EdgeInsets.symmetric(vertical: 0, horizontal: 0),
      ),
      shape: WidgetStateProperty.all(
        RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
          side: BorderSide(color: colorScheme.color6, width: 2.0),
        ),
      ),
      maximumSize: WidgetStateProperty.all(Size(200, totalHeight)),
    );
  }

  Widget _wrapWithDivider(Widget item, MeColorScheme colorScheme, bool isLast) {
    return Column(
      children: [
        item,
        if (!isLast)
          Divider(color: colorScheme.color6, height: 1, thickness: 1),
      ],
    );
  }
}

enum ListItemFilterType {
  all,
  checked,
  unchecked,
}

class ListItemsFilterWidget extends StatefulWidget {
  const ListItemsFilterWidget({
    Key? key,
    required this.onSearchChanged,
    required this.onFilterChanged,
    required this.onResetFilter,
    this.initialSearchQuery = '',
    this.initialFilterType = ListItemFilterType.all,
  }) : super(key: key);

  final Function(String) onSearchChanged;
  final Function(ListItemFilterType) onFilterChanged;
  final VoidCallback onResetFilter;
  final String initialSearchQuery;
  final ListItemFilterType initialFilterType;

  @override
  State<ListItemsFilterWidget> createState() => _ListItemsFilterWidgetState();
}

class _ListItemsFilterWidgetState extends State<ListItemsFilterWidget> {
  late final ScrollController _scrollController;
  late String _searchQuery;
  late ListItemFilterType _filterType;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _searchQuery = widget.initialSearchQuery;
    _filterType = widget.initialFilterType;
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  bool get isFilterApplied =>
      _searchQuery.isNotEmpty || _filterType != ListItemFilterType.all;

  void _updateSearchQuery(String query) {
    setState(() {
      _searchQuery = query;
    });
    widget.onSearchChanged(query);
  }

  void _updateFilterType(ListItemFilterType type) {
    setState(() {
      _filterType = type;
    });
    widget.onFilterChanged(type);
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener(
      onNotification: (notification) => true,
      child: FilterWidgetWrapper(
        child: Align(
          alignment: Alignment.centerLeft,
          child: HorizontalScrollRow(
            key: const ValueKey('listItemsFilterList'),
            data: [
              SearchFilterInputChip(
                searchQuery: _searchQuery,
                onSearchQueryChanged: (value) {
                  _updateSearchQuery(value ?? '');
                },
              ),
              ListFilterChip(
                title: MeTranslations.instance.bottomSheet_listItems_unchecked,
                isSelected: _filterType == ListItemFilterType.unchecked,
                onPressed: () {
                  if (_filterType == ListItemFilterType.unchecked) {
                    _updateFilterType(ListItemFilterType.all);
                  } else {
                    _updateFilterType(ListItemFilterType.unchecked);
                  }
                },
              ),
              ListFilterChip(
                title: MeTranslations.instance.bottomSheet_listItems_checked,
                isSelected: _filterType == ListItemFilterType.checked,
                onPressed: () {
                  if (_filterType == ListItemFilterType.checked) {
                    _updateFilterType(ListItemFilterType.all);
                  } else {
                    _updateFilterType(ListItemFilterType.checked);
                  }
                },
              ),
            ],
            spacing: 8,
            horizontalPadding: 16,
          ),
        ),
      ),
    );
  }
}
