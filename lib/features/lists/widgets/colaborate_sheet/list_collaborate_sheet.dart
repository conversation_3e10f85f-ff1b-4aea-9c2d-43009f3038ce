import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gal/gal.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/common_sharing_model.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/enums/share_type.dart';
import 'package:mevolve/data/enums/theme_type.dart';
import 'package:mevolve/data/models/common_sharing_feat_model.dart';
import 'package:mevolve/data/models/in_app_notification.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/public/public_users.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/public_user_cubit.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/hamburger/subscription/effective_subscription_cubit/effective_subscription_cubit.dart';
import 'package:mevolve/features/hamburger/subscription/helpers/subscription_helper.dart';
import 'package:mevolve/features/lists/cubit/my_list/list_operations.dart';
import 'package:mevolve/features/lists/functions/collaboration_notification.dart';
import 'package:mevolve/features/lists/functions/list_to_image.dart';
import 'package:mevolve/features/lists/widgets/colaborate_sheet/shareable_image_widget.dart';
import 'package:mevolve/features/publicTasks/handle_public_link.dart';
import 'package:mevolve/features/public_profile/widgets/add_mevolve_id.dart';
import 'package:mevolve/features/tasks_providers/lists/main_lists_cubit.dart';
import 'package:mevolve/features/widgets/custom_fab_location.dart';
import 'package:mevolve/features/widgets/owner_initial_avatar.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/utilities/encryption/me_encryption_core.dart';
import 'package:mevolve/utilities/overlay_loader.dart';
import 'package:mevolve/utilities/sharing_utility/sharing_firebase_functions.dart';
import 'package:mevolve/features/lists/widgets/colaborate_sheet/invite_collaborator_textfield.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/me_checkbox_list_tile.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/me_list_tile.dart';
import 'package:mevolve/features/widgets/share_link_qr_sheet.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/list_option_sheet.dart'
    show getSharePositionWithFallback;
import 'package:open_file_plus/open_file_plus.dart';
import 'package:screenshot/screenshot.dart';
import 'package:share_plus/share_plus.dart';
import 'package:mevolve/features/widgets/tab_kebab_widgets.dart';

class ListCollaborateSheetNonOwner extends StatelessWidget {
  const ListCollaborateSheetNonOwner({
    super.key,
    required this.listId,
    required this.type,
    required this.list,
  });

  final String listId;
  final ListData list;
  final ShareType type;

  @override
  Widget build(BuildContext context) {
    return type == ShareType.publicTask
        ? PublicVisibilitySheet(
            itemId: list.id,
            isPublic: list.isPublic,
            isOwner: false,
            // Non-owner, so always false
            publicId: list.publicId,
            title: list.title,
            description: list.description,
            ownerName: list.ownerName,
            ownerEmail: list.ownerEmail,
            ownerId: list.uid,
            shareCount: list.members.memberHashedEmails.length,
            sharerUserRole: list.members.getUserRoleType,
            collectionType: FirebaseDocCollectionType.lists,
            lastCloudUpdatedAt: list.lastCloudUpdatedAt,
          )
        : BlocBuilder<MainListsCubit, MainListsState>(
            builder: (context, state) {
              final ListData? list = state.taskIdsToTasks[listId];

              if (list == null) {
                return const SizedBox.shrink();
              }
              return PublicVisibilitySheet(
                itemId: list.id,
                isPublic: list.isPublic,
                isOwner: false,
                // Non-owner, so always false
                publicId: list.publicId,
                title: list.title,
                description: list.description,
                ownerName: list.ownerName,
                ownerEmail: list.ownerEmail,
                ownerId: list.uid,
                shareCount: list.members.memberHashedEmails.length,
                sharerUserRole: list.members.getUserRoleType,
                collectionType: FirebaseDocCollectionType.lists,
                lastCloudUpdatedAt: list.lastCloudUpdatedAt,
              );
            },
          );
  }
}

class ListCollaboratorsSheet extends StatelessWidget {
  const ListCollaboratorsSheet({
    super.key,
    required this.listId,
    required this.listCubit,
  });

  final String listId;
  final ListOperations listCubit;

  @override
  Widget build(BuildContext context) {
    final String myUid = context.read<AppBloc>().state.user!.uid;

    return BlocBuilder<MainListsCubit, MainListsState>(
      builder: (context, state) {
        ListData? list = state.taskIdsToTasks[listId];

        if (list == null) {
          return const SizedBox();
        }

        final bool isOwner = list.uid == myUid;

        return CollaboratorsSheet(
          isOwner: isOwner,
          ownerEmail: list.ownerEmail,
          ownerName: list.ownerName,
          ownerId: list.uid,
          members: list.members.membersConfig.values.toList(),
          collectionType: FirebaseDocCollectionType.lists,
          maxMembersAllowed: MembersModel.collaboratorLimit,
          onAddMember: (BuildContext context) async {
            await showMeScrollableModalBottomSheet(
              bottomSheetLevel: 1,
              backgroundColor: Colors.transparent,
              shape: const RoundedRectangleBorder(),
              builder: (context, showDiscardDialogCallback) {
                return InviteCollaboratorTextField(
                  sharerUserRole: list.members.getUserRoleType,
                  collection: FirebaseDocCollectionType.lists,
                  showDiscardDialog: showDiscardDialogCallback,
                  id: listId,
                  dek: list.encryptionData.dek!,
                  alreadyAddedUsers: list.members.membersConfig.values.toList(),
                  addMember: (
                    dek,
                    email,
                    role,
                    hashedEmail,
                    uid,
                    decryptedSecret,
                  ) async {
                    InAppNotification notification =
                        SendNotificationList().notifyUserAdded(
                      title: list.title,
                      taskType: InAppNotificationTaskType.list,
                      taskId: list.id,
                      operationBy:
                          context.read<AppBloc>().state.userData!.userInfo.name,
                      userHashedEmail: hashedEmail,
                    );
                    String encEmail = await MeEncryptionCore.encryptAesDataCore(
                      email,
                      decryptedSecret: decryptedSecret,
                    );
                    await SharingFirebaseFunctions().addMemberToTask(
                      collection: FirebaseDocCollectionType.lists.name,
                      title: list.title,
                      adminName: list.ownerName,
                      decryptedEmail: email,
                      notification: notification,
                      setupId: list.id,
                      memberConfig: MembersConfigModel(
                        hashedEmail: hashedEmail,
                        status: MemberStatus.active,
                        role: role,
                        dek: dek,
                        eEmail: encEmail,
                      ),
                      uid: uid,
                      description: list.description,
                    );
                  },
                );
              },
            );
          },
          onUpdateMemberRole: (member, newRole) async {
            context.read<ListOperations>().updateList(
              list: list.copyWith(
                members: list.members.copyWith(
                  membersConfig: list.members.membersConfig.map(
                    (key, MembersConfigModel value) {
                      if (value.hashedEmail == member.hashedEmail) {
                        return MapEntry(
                          key,
                          value.copyWith(
                            hashedEmail: member.hashedEmail,
                            role: newRole,
                            status: MemberStatus.active,
                          ),
                        );
                      }
                      return MapEntry(key, value);
                    },
                  ),
                ),
              ),
            );
          },
          onRemoveMember: (member) async {
            await SharingFirebaseFunctions().removeCollaborator(
              list.id,
              member.hashedEmail,
              collection: FirebaseDocCollectionType.lists,
            );
            if (context.mounted) {
              SendNotificationList().notifyUserRemoved(
                title: list.title,
                taskType: InAppNotificationTaskType.list,
                taskId: list.id,
                operationBy:
                    context.read<AppBloc>().state.userData!.userInfo.name,
                userHashedEmail: member.hashedEmail,
              );
            }
          },
        );
      },
    );
  }
}

class ListPublicVisibilitySheet extends StatelessWidget {
  const ListPublicVisibilitySheet({
    super.key,
    required this.listId,
  });

  final String listId;

  @override
  Widget build(BuildContext context) {
    final String myUid = context.read<AppBloc>().state.user!.uid;

    return BlocBuilder<MainListsCubit, MainListsState>(
      builder: (context, state) {
        ListData? list = state.taskIdsToTasks[listId];

        if (list == null) {
          return const SizedBox();
        }

        final bool isOwner = list.uid == myUid;

        ListOperations listCubit = context.read<ListOperations>();

        return PublicVisibilitySheet(
          itemId: listId,
          isPublic: list.isPublic,
          isOwner: isOwner,
          publicId: list.publicId,
          title: list.title,
          description: list.description,
          ownerName: list.ownerName,
          ownerEmail: list.ownerEmail,
          ownerId: list.uid,
          shareCount: list.members.memberHashedEmails.length,
          sharerUserRole: list.members.getUserRoleType,
          collectionType: FirebaseDocCollectionType.lists,
          lastCloudUpdatedAt: list.lastCloudUpdatedAt,
          onPublicToggle: (isPublic) async {
            if (isPublic) {
              // Toggle ON logic
              if (list.publicId == null) {
                Map<String, dynamic>? res =
                    await SharingFirebaseFunctions().updateTaskWithShortLink(
                  list.id,
                  list.ownerName,
                  collection: FirebaseDocCollectionType.lists,
                );
                if (res != null) {
                  listCubit.updateList(
                    list: list.copyWith(
                      isPublic: true,
                      publicId: res['publicId'],
                      inviteLink: res['inviteLink'],
                      encryptionData: list.encryptionData.copyWith(
                        encryptedFields: ['members.membersConfig{}.eEmail'],
                      ),
                    ),
                  );
                  return res;
                }
              } else {
                listCubit.updateList(
                  list: list.copyWith(
                    isPublic: true,
                    encryptionData: list.encryptionData.copyWith(
                      encryptedFields: ['members.membersConfig{}.eEmail'],
                    ),
                  ),
                );
              }
            } else {
              // Toggle OFF logic
              listCubit.updateList(
                list: list.copyWith(
                  isPublic: false,
                  encryptionData: list.encryptionData.copyWith(
                    encryptedFields: [
                      'title',
                      'description',
                      'ownerName',
                      'ownerEmail',
                      'listItems{}.item',
                      'members.membersConfig{}.eEmail',
                    ],
                  ),
                ),
              );
              await SharingFirebaseFunctions().makeTaskPrivate(
                list.id,
                collection: FirebaseDocCollectionType.lists,
              );
              await Future.delayed(const Duration(seconds: 1));
            }
            return null;
          },
        );
      },
    );
  }
}

class PublicVisibilitySheet extends StatefulWidget {
  const PublicVisibilitySheet({
    super.key,
    required this.itemId,
    required this.isPublic,
    required this.isOwner,
    required this.publicId,
    required this.title,
    required this.ownerName,
    required this.ownerEmail,
    required this.ownerId,
    required this.shareCount,
    required this.sharerUserRole,
    required this.collectionType,
    required this.lastCloudUpdatedAt,
    this.onPublicToggle,
    this.description,
  });

  final String itemId;
  final bool isPublic;
  final bool isOwner;
  final String? publicId;
  final String title;
  final String? description;
  final String ownerName;
  final String ownerEmail;
  final String ownerId;
  final int shareCount;
  final MemberRole sharerUserRole;
  final FirebaseDocCollectionType collectionType;
  final DateTime? lastCloudUpdatedAt;

  // Callback for toggling public visibility - returns updated data when turning on, null when turning off
  final Future<Map<String, dynamic>?> Function(bool isPublic)? onPublicToggle;

  @override
  State<PublicVisibilitySheet> createState() => _PublicVisibilitySheetState();
}

class _PublicVisibilitySheetState extends State<PublicVisibilitySheet> {
  late ScreenshotController _screenshotController;
  late String _collectionPath;
  late String _shareableLink;

  // Current state values
  late String _currentTitle;
  late String? _currentDescription;
  late String? _currentPublicId;
  String? _shortCode;

  @override
  void initState() {
    super.initState();
    _screenshotController = ScreenshotController();

    // Initialize current values from widget
    _currentTitle = widget.title;
    _currentDescription = widget.description;
    _currentPublicId = widget.publicId;

    // Determine collection path based on type
    _collectionPath = _getCollectionPath(widget.collectionType);
    _updateShareableLink();
  }

  String _getCollectionPath(FirebaseDocCollectionType type) {
    switch (type) {
      case FirebaseDocCollectionType.lists:
        return 'list';
      case FirebaseDocCollectionType.notes:
        return 'note';
      default:
        return type.name.toLowerCase();
    }
  }

  void _updateShareableLink() {
    _shortCode = _currentPublicId?.split('/').last;
    _shareableLink =
        '${AppConstant.sharingSiteBaseUrl}/$_collectionPath/$_shortCode';
  }

  void _updateShortCode(String newShortCode) {
    setState(() {
      _shortCode = newShortCode;
      _shareableLink =
          '${AppConstant.sharingSiteBaseUrl}/$_collectionPath/$_shortCode';
    });
  }

  TrackAction _getTrackAction() {
    switch (widget.collectionType) {
      case FirebaseDocCollectionType.lists:
        return TrackAction.myListsItem;
      case FirebaseDocCollectionType.notes:
        return TrackAction.noteAction;
      default:
        return TrackAction.myListsItem;
    }
  }

  // Common restriction check for public sharing
  static Future<bool> checkRestrictionsAndProceed(
    BuildContext context, {
    required bool isOwner,
  }) async {
    // Check subscription
    final createProFeatures =
        context.read<EffectiveSubscriptionCubit>().state.eCreateProFeatures;
    if (!createProFeatures) {
      SubscriptionHelper.showFeatureLockedDialog(context);
      return false;
    }

    // Check profile completion for owners
    if (isOwner) {
      PublicUser? publicUser = context.read<PublicUserCubit>().state.publicUser;
      final canUserShareFunctionality = publicUser != null;
      if (!canUserShareFunctionality) {
        bool? res = await UtilityMethods.showCompletePublicProfileDialog(
          context: context,
        );
        if (res == null || !res) {
          return false;
        }
        if (res) {
          String? idAdded = await addMevolveIdBottomSheet();
          if (idAdded == null) {
            return false;
          }
        }
      }
    }

    if (!context.mounted) {
      return false;
    }

    // Check internet connectivity
    final InternetConnectionState internetConnectionState =
        context.read<AppBloc>().state.internetConnectionState;
    bool isOffline =
        internetConnectionState == InternetConnectionState.disconnected;
    if (isOffline) {
      UtilityMethods.showOfflineDialogOrPerformAction(
        internetActiveFunction: () {},
        internetConnectionState: internetConnectionState,
        context: context,
      );
      return false;
    }

    return true;
  }

  Future<void> _handlePublicToggle(bool newValue) async {
    // Check if callback is provided
    if (widget.onPublicToggle == null) return;

    // Check restrictions first
    bool canProceed = await checkRestrictionsAndProceed(
      context,
      isOwner: widget.isOwner,
    );
    if (!canProceed || !mounted) return;

    context.showLoader();

    // Add delay if document is not in cloud yet
    bool isDocumentNotInCloud = widget.lastCloudUpdatedAt == null;
    if (isDocumentNotInCloud) {
      await Future.delayed(const Duration(seconds: 5));
    }

    try {
      final result = await widget.onPublicToggle!(newValue);
      if (mounted) {
        setState(() {
          if (newValue && result != null) {
            _currentPublicId = result['publicId'];
            if (result['publicId'] != null) {
              final newShortCode = result['publicId'].split('/').last;
              _updateShortCode(newShortCode);
            }
          }
        });
      }
    } finally {
      if (mounted) {
        context.hideLoader();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final ThemeType currentTheme = context.select(
      (TodaySettingsCubit cubit) => cubit.state.appThemeMode,
    );

    return SafeArea(
      child: Container(
        width: double.maxFinite,
        color: colorScheme.color6,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DialogAndBottomSheetAppBar(
              title: MeTranslations.instance.dropdown_listItemKebabMenu_public,
            ),
            ListView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                // Public toggle (only for owners and when callback is provided)
                if (widget.isOwner && widget.onPublicToggle != null) ...[
                  MeCheckBoxListTile(
                    title: MeText(
                      text: MeTranslations
                          .instance.bottomSheet_featureSharing_visibleToPublic,
                      meFontStyle: MeFontStyle.D8,
                    ),
                    isToggle: true,
                    value: widget.isPublic,
                    onChanged: (value) {
                      if (value != null) {
                        _handlePublicToggle(value);
                      }
                    },
                  ),
                  const SizedBox(height: 1),
                ],

                // Copy Link Tile
                MeListTile(
                  onTap: !widget.isPublic
                      ? null
                      : () async {
                          await Clipboard.setData(
                            ClipboardData(text: _shareableLink),
                          );
                          if (context.mounted) {
                            showCustomMessageOverlay(
                              context,
                              MeTranslations.instance.toast_linkCopied_content,
                            );
                          }
                          EventFormation().sendFeatureSharedEvent(
                            trackAction: _getTrackAction(),
                            sharingStatus: ShareStatus.public,
                            sharedCount: widget.shareCount,
                            shareMethod: ShareMethod.link,
                            userRole: UserRoleOfSharer.getUserRoleByMemberRole(
                              widget.sharerUserRole,
                            ),
                          );
                        },
                  title: MeText(
                    text: MeTranslations
                        .instance.bottomSheet_publicVisibility_copyLink,
                    meFontStyle:
                        widget.isPublic ? MeFontStyle.D8 : MeFontStyle.D7,
                  ),
                  trailing: MeIcon(
                    iconPath: Assets.svg.copyIcon.path,
                    iconColor: !widget.isPublic
                        ? colorScheme.color7
                        : colorScheme.color35,
                    iconSize: const SizedBox(height: 20, width: 20),
                    iconContainerSize: const SizedBox(height: 24, width: 24),
                  ),
                ),
                const SizedBox(height: 1),

                // Download QR Tile
                MeListTile(
                  title: MeText(
                    text: MeTranslations
                        .instance.bottomSheet_publicVisibility_downloadQr,
                    meFontStyle:
                        widget.isPublic ? MeFontStyle.D8 : MeFontStyle.D7,
                  ),
                  onTap: !widget.isPublic
                      ? null
                      : () async {
                          EventFormation().sendFeatureSharedEvent(
                            trackAction: _getTrackAction(),
                            sharingStatus: ShareStatus.public,
                            sharedCount: widget.shareCount,
                            shareMethod: ShareMethod.qrcode,
                            userRole: UserRoleOfSharer.getUserRoleByMemberRole(
                              widget.sharerUserRole,
                            ),
                          );

                          UtilityMethods.showMeLoaderDialog(context);
                          _screenshotController
                              .captureFromWidget(
                            DownloadableQrImageRaw(
                              shareableLink: _shareableLink,
                              args: ShareLinkQrData(
                                link: _shareableLink,
                                title: _currentTitle,
                                id: widget.itemId,
                                ownerName: widget.ownerName,
                                collection: widget.collectionType,
                                description: _currentDescription,
                              ),
                              colorScheme: colorScheme,
                              currentTheme: currentTheme,
                              isNote: widget.collectionType ==
                                  FirebaseDocCollectionType.notes,
                            ),
                          )
                              .then((value) async {
                            if (!context.mounted) return;
                            Navigator.of(context).pop();
                            String path = await imagePathFromUint8List(
                              widget.itemId,
                              context,
                              value,
                            );
                            await Gal.putImage(
                              path,
                              album: 'Mevolve',
                            );
                            if (context.mounted) {
                              showCustomMessageOverlay(
                                context,
                                MeTranslations
                                    .instance.toast_qrDownload_content,
                                actionText: MeTranslations
                                    .instance.screen_imagePreview_view,
                                actionFunction: () {
                                  OpenFile.open(path);
                                },
                              );
                            }
                          });
                        },
                  trailing: MeIcon(
                    iconPath: Assets.svg.icDownload.path,
                    iconColor: !widget.isPublic
                        ? colorScheme.color7
                        : colorScheme.color35,
                    iconSize: const SizedBox(height: 16.7, width: 15),
                    iconContainerSize: const SizedBox(height: 24, width: 24),
                  ),
                ),
                const SizedBox(height: 1),

                // Share Tile
                MeListTile(
                  title: MeText(
                    text: MeTranslations
                        .instance.bottomSheet_publicVisibility_shareLink,
                    meFontStyle:
                        widget.isPublic ? MeFontStyle.D8 : MeFontStyle.D7,
                  ),
                  onTap: !widget.isPublic
                      ? null
                      : () async {
                          EventFormation().sendFeatureSharedEvent(
                            trackAction: _getTrackAction(),
                            sharingStatus: ShareStatus.public,
                            sharedCount: widget.shareCount,
                            shareMethod: ShareMethod.link,
                            userRole: UserRoleOfSharer.getUserRoleByMemberRole(
                              widget.sharerUserRole,
                            ),
                          );

                          await Share.share(
                            _shareableLink,
                            subject: _currentTitle,
                            sharePositionOrigin:
                                getSharePositionWithFallback(context),
                          );
                        },
                  trailing: MeIcon(
                    iconPath: Assets.svg.icShare.path,
                    iconColor: !widget.isPublic
                        ? colorScheme.color7
                        : colorScheme.color35,
                    iconSize: const SizedBox(height: 16.7, width: 15),
                    iconContainerSize: const SizedBox(height: 24, width: 24),
                  ),
                ),

                const SizedBox(height: 1),

                // Public Profile Tile
                IgnorePointer(
                  ignoring: !widget.isPublic,
                  child: MeListTile(
                    onTap: !widget.isPublic
                        ? null
                        : () {
                            bool isOffline = context
                                    .read<AppBloc>()
                                    .state
                                    .internetConnectionState ==
                                InternetConnectionState.disconnected;
                            if (isOffline) {
                              final InternetConnectionState
                                  internetConnectionState = context
                                      .read<AppBloc>()
                                      .state
                                      .internetConnectionState;
                              UtilityMethods.showOfflineDialogOrPerformAction(
                                internetActiveFunction: () {},
                                internetConnectionState:
                                    internetConnectionState,
                                context: context,
                              );
                              return;
                            }
                            HandlePublicLink().handleUserLink(
                              id: widget.ownerId,
                              context: context,
                              sheetLevel: 3,
                              isMevolveId: false,
                            );
                          },
                    title: MeText(
                      text: MeTranslations
                          .instance.bottomSheet_publicProfile_titleTopBar,
                      meFontStyle:
                          widget.isPublic ? MeFontStyle.D8 : MeFontStyle.D7,
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    trailing: SizedBox(
                      height: 24,
                      width: 24,
                      child: Center(
                        child: OwnerInitialAvatar(
                          isDisabled: !widget.isPublic,
                          name: widget.ownerName,
                          email: widget.ownerEmail,
                          isLarge: false,
                          ownerId: widget.ownerId,
                          sheetLevel: 3,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class CollaboratorsSheet extends StatelessWidget {
  const CollaboratorsSheet({
    super.key,
    required this.isOwner,
    required this.ownerEmail,
    required this.ownerName,
    required this.ownerId,
    required this.members,
    required this.collectionType,
    required this.maxMembersAllowed,
    required this.onAddMember,
    required this.onUpdateMemberRole,
    required this.onRemoveMember,
  });

  final bool isOwner;
  final String ownerEmail;
  final String ownerName;
  final String ownerId;
  final List<MembersConfigModel> members;
  final FirebaseDocCollectionType collectionType;
  final int maxMembersAllowed;

  // Callbacks
  final Future<void> Function(BuildContext context) onAddMember;
  final Future<void> Function(MembersConfigModel member, MemberRole newRole)
      onUpdateMemberRole;
  final Future<void> Function(MembersConfigModel member) onRemoveMember;

  static final CustomStandardFabLocation _fabLocation =
      CustomStandardFabLocation.endFloat(
    floatingActionButtonXMargin: SizeConstants.homeFABXMargin,
    floatingActionButtonYMargin: SizeConstants.homeFABYMargin,
    marginBetweenFabAndSnackBar: SizeConstants.marginBetweenFabAndSnackBar,
  );

  // Common restriction check for all operations
  static Future<bool> checkRestrictionsAndProceed(
    BuildContext context, {
    required bool isOwner,
  }) async {
    // Check subscription
    final createProFeatures =
        context.read<EffectiveSubscriptionCubit>().state.eCreateProFeatures;
    if (!createProFeatures) {
      SubscriptionHelper.showFeatureLockedDialog(context);
      return false;
    }

    // Check profile completion for owners
    if (isOwner) {
      PublicUser? publicUser = context.read<PublicUserCubit>().state.publicUser;
      final canUserShareFunctionality = publicUser != null;
      if (!canUserShareFunctionality) {
        bool? res = await UtilityMethods.showCompletePublicProfileDialog(
          context: context,
        );
        if (res == null || !res) {
          return false;
        }
        if (res) {
          String? idAdded = await addMevolveIdBottomSheet();
          if (idAdded == null) {
            return false;
          }
        }
      }
    }

    if (!context.mounted) {
      return false;
    }

    // Check internet connectivity
    final InternetConnectionState internetConnectionState =
        context.read<AppBloc>().state.internetConnectionState;
    bool isOffline =
        internetConnectionState == InternetConnectionState.disconnected;
    if (isOffline) {
      UtilityMethods.showOfflineDialogOrPerformAction(
        internetActiveFunction: () {},
        internetConnectionState: internetConnectionState,
        context: context,
      );
      return false;
    }

    return true;
  }

  Future<void> _handleAddMember(BuildContext context) async {
    // Check restrictions at tap
    bool canProceed = await checkRestrictionsAndProceed(
      context,
      isOwner: isOwner,
    );
    if (!canProceed || !context.mounted) return;

    // Check member limit
    final activeMembers =
        members.where((m) => m.status == MemberStatus.active).toList();
    int currentMemberCount = activeMembers.length + 1; // +1 for owner
    if (currentMemberCount >= maxMembersAllowed) {
      showDialog(
        context: context,
        builder: (context) {
          return MeDialog(
            title: MeTranslations.instance.overlay_memberLimitReached_title,
            description:
                MeTranslations.instance.overlay_memberLimitReached_content,
            primaryText: MeTranslations.instance.screen_common_ok,
            primaryOnTap: () {
              Navigator.pop(context);
            },
          );
        },
      );
      return;
    }

    if (!context.mounted) return;
    onAddMember(context);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final activeMembers =
        members.where((m) => m.status == MemberStatus.active).toList();

    return SafeArea(
      child: Scaffold(
        body: Container(
          width: double.maxFinite,
          color: colorScheme.color6,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DialogAndBottomSheetAppBar(
                title: MeTranslations.instance.screen_common_collaborate,
                suffixActions: [
                  if (!isOwner)
                    OwnerInitialAvatar(
                      name: ownerName,
                      email: ownerEmail,
                      isLarge: true,
                      ownerId: ownerId,
                      sheetLevel: 3,
                    ),
                ],
              ),

              // Owner tile
              MeListTile(
                title: MeText(
                  text: MeString(ownerEmail),
                  meFontStyle: MeFontStyle.D8,
                ),
                trailing: MeText(
                  text: MeString(MemberRole.owner.toString()),
                  meFontStyle: MeFontStyle.D35,
                ),
              ),

              if (activeMembers.isNotEmpty) const SizedBox(height: 1),

              // Members list
              Expanded(
                child: ListView.separated(
                  shrinkWrap: false,
                  physics: ScreenSizeState.instance.height < 600
                      ? null
                      : const NeverScrollableScrollPhysics(),
                  itemCount: activeMembers.length,
                  separatorBuilder: (context, index) =>
                      const SizedBox(height: 1),
                  itemBuilder: (context, index) {
                    MembersConfigModel member = activeMembers[index];
                    return !isOwner
                        ? MeListTile(
                            title: MeText(
                              text: MeString(member.eEmail),
                              meFontStyle: MeFontStyle.D8,
                            ),
                            trailing: MeText(
                              text: MeString(member.role.toString()),
                              meFontStyle: MeFontStyle.D35,
                            ),
                          )
                        : MemberActionTile(
                            member: member,
                            onActionSelected: (MemberAction action) async {
                              // Check restrictions first
                              bool canProceed =
                                  await checkRestrictionsAndProceed(
                                context,
                                isOwner: isOwner,
                              );
                              if (!canProceed || !context.mounted) return;

                              if (action == MemberAction.remove) {
                                bool? res = await showDialog(
                                  context: context,
                                  builder: (context) => MeDialog(
                                    title: MeTranslations.instance
                                        .overlay_removeUser_title(
                                      name: member.eEmail,
                                    ),
                                    primaryText: MeTranslations
                                        .instance.overlay_removeUser_remove,
                                    description: MeTranslations
                                        .instance.overlay_removeUser_content,
                                    primaryOnTap: () => Navigator.pop(
                                      context,
                                      true,
                                    ),
                                    secondaryOnTap: () => Navigator.pop(
                                      context,
                                      false,
                                    ),
                                    secondaryText: MeTranslations
                                        .instance.screen_common_buttonCancel,
                                    primaryButtonFontStyle: MeFontStyle.C11,
                                  ),
                                );
                                if (res == true && context.mounted) {
                                  // Show loader dialog like PermissionSettingSheet
                                  UtilityMethods.showMeLoaderDialog(context);
                                  await onRemoveMember(member);
                                  if (context.mounted) {
                                    Navigator.pop(context); // Close loader
                                  }
                                }
                              } else {
                                final memberRole = action.toMemberRole();
                                if (memberRole != null &&
                                    memberRole != member.role) {
                                  // Show loader dialog like PermissionSettingSheet
                                  UtilityMethods.showMeLoaderDialog(context);
                                  await onUpdateMemberRole(member, memberRole);
                                  if (context.mounted) {
                                    Navigator.pop(context); // Close loader
                                  }
                                }
                              }
                            },
                          );
                  },
                ),
              ),
            ],
          ),
        ),
        floatingActionButtonLocation: _fabLocation,
        floatingActionButton: SizedBox(
          height: 50,
          width: 50,
          child: FloatingActionButton(
            key: const ValueKey('addCollaboratorButton'),
            shape: const RoundedRectangleBorder(
              borderRadius: BorderRadius.all(
                Radius.circular(8),
              ),
            ),
            elevation: 0,
            onPressed: () => _handleAddMember(context),
            backgroundColor: colorScheme.color1,
            child: MeIcon(
              iconPath: Assets.svg.acountPlus.path,
              iconColor: colorScheme.color12,
              iconSize: const SizedBox(
                height: 14.62,
                width: 21.12,
              ),
              iconContainerSize: const SizedBox(
                height: 24,
                width: 24,
              ),
            ),
          ),
        ),
      ),
    );
  }
}

enum MemberAction {
  viewer,
  editor,
  remove;

  String iconPath() {
    switch (this) {
      case MemberAction.viewer:
        return Assets.svg.passwordEyeOpen.path;
      case MemberAction.editor:
        return Assets.svg.editorRole.path;
      case MemberAction.remove:
        return Assets.svg.removeMember.path;
    }
  }

  Color iconColor(MeColorScheme colorScheme) {
    switch (this) {
      case MemberAction.viewer:
        return colorScheme.color35;
      case MemberAction.editor:
        return colorScheme.color35;
      case MemberAction.remove:
        return colorScheme.color14;
    }
  }

  MeString toMeString() {
    switch (this) {
      case MemberAction.viewer:
        return MeString(
          MeTranslations.instance.dropdown_permission_viewer.text,
        );
      case MemberAction.editor:
        return MeString(
          MeTranslations.instance.dropdown_permission_editor.text,
        );
      case MemberAction.remove:
        return MeTranslations.instance.overlay_removeConfirmation_remove;
    }
  }

  static MemberAction fromMemberRole(MemberRole role) {
    switch (role) {
      case MemberRole.viewer:
        return MemberAction.viewer;
      case MemberRole.editor:
        return MemberAction.editor;
      case MemberRole.owner:
        return MemberAction
            .editor; // Fallback, though owner shouldn't be selectable
    }
  }

  MemberRole? toMemberRole() {
    switch (this) {
      case MemberAction.viewer:
        return MemberRole.viewer;
      case MemberAction.editor:
        return MemberRole.editor;
      case MemberAction.remove:
        return null; // Remove action doesn't have a corresponding role
    }
  }
}

class MemberActionTile extends StatelessWidget {
  const MemberActionTile({
    super.key,
    required this.member,
    required this.onActionSelected,
  });

  final MembersConfigModel member;
  final ValueChanged<MemberAction> onActionSelected;

  @override
  Widget build(BuildContext context) {
    final currentAction = MemberAction.fromMemberRole(member.role);

    return CustomPopMenuButton<MemberAction>(
      selectedItemPopMenuItems: [currentAction],
      allPopMenuValues: const [
        MemberAction.viewer,
        MemberAction.editor,
        MemberAction.remove,
      ],
      offset: const Offset(9999, 40),
      position: PopupMenuPosition.over,
      minWidth: 150,
      onPopMenuItemTap: onActionSelected,
      hideTrailingWidgets: true,
      iconColor: (MemberAction action, MeColorScheme colorScheme) =>
          action.iconColor(colorScheme),
      child: MeListTile(
        title: MeText(
          text: MeString(member.eEmail),
          meFontStyle: MeFontStyle.D8,
        ),
        trailing: MeText(
          text: MeString(member.role.toString()),
          meFontStyle: MeFontStyle.D35,
        ),
        onTap: null,
      ),
    );
  }
}
