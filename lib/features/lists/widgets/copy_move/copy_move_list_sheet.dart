import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/common_sharing_model.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/lists/cubit/my_lists_actions/my_lists_actions_cubit.dart';
import 'package:mevolve/features/lists/cubit/shared_with_me_lists_actions/shared_with_me_lists_actions_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/lists/cubit/copy_move_list/copy_move_list_cubit.dart';
import 'package:mevolve/features/lists/cubit/copy_move_list/copy_move_list_state.dart';
import 'package:mevolve/features/lists/widgets/list_dialog_checkbox.dart';
import 'package:mevolve/features/lists/widgets/list_item_sheet.dart';
import 'package:mevolve/features/tasks_providers/lists/main_lists_cubit.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/me_checkbox_list_tile.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/show_me_discard_dialog.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/utility_methods.dart';

class CopyMoveListSheet extends StatefulWidget {
  const CopyMoveListSheet({
    super.key,
    required this.parentContext,
    required this.listId,
    required this.isCopy,
    required this.myUid,
  });

  final BuildContext parentContext;
  final String listId;
  final bool isCopy;
  final String myUid;

  @override
  State<CopyMoveListSheet> createState() => _CopyMoveListSheetState();
}

class _CopyMoveListSheetState extends State<CopyMoveListSheet> {
  late CopyMoveListCubit cubit;
  List<ListData> lists = [];
  late ScrollController scrollController;
  bool isListScrollable = false;
  final ValueNotifier<bool> showCheckboxVal = ValueNotifier<bool>(true);

  @override
  void initState() {
    cubit = widget.parentContext.read<CopyMoveListCubit>();
    lists = widget.parentContext.read<CopyMoveListCubit>().state.lists;
    scrollController = ScrollController();
    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      if (scrollController.position.maxScrollExtent > 0) {
        isListScrollable = true;
      } else {
        isListScrollable = false;
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    cubit.clearList();
    super.dispose();
  }

  Future<bool> resVal() async {
    bool isSame = const ListEquality().equals(
      lists,
      widget.parentContext.read<CopyMoveListCubit>().state.lists,
    );
    if (!isSame) {
      final pop = await showMeDiscardDialog(
            context,
          ) ??
          false;
      return pop;
    }
    return true;
  }

  void action(copyMoveState, BuildContext context, listState) async {
    bool? res = await showDialog(
      context: context,
      builder: (childContext) => MeDialog(
        insetPadding:
            ScreenSizeState.instance.getDialogInsetPadding(DialogLevel.level1),
        title: widget.isCopy
            ? copyMoveState.items.length > 1
                ? MeTranslations.instance.overlay_copyItem_multipleItemsTitle
                : MeTranslations.instance.overlay_copyItem_singleItemTitle
            : copyMoveState.items.length > 1
                ? MeTranslations.instance.overlay_moveItem_multipleItemsTitle
                : MeTranslations.instance.overlay_moveItem_singleItemTitle,
        description: widget.isCopy
            ? copyMoveState.items.length > 1
                ? MeTranslations.instance.overlay_copyItem_multipleItemsContent
                : MeTranslations.instance.overlay_copyItem_singleItemContent
            : copyMoveState.items.length > 1
                ? MeTranslations.instance.overlay_moveItem_multipleItemsContent
                : MeTranslations.instance.overlay_moveItem_singleItemContent,
        primaryText: widget.isCopy
            ? MeTranslations.instance.bottomSheet_listSelectItems_copy
            : MeTranslations.instance.bottomSheet_listSelectItems_move,
        customContentAfterDescriptionPadding: const EdgeInsets.only(top: 0),
        customContentAfterDescription: ListDialogCheckbox(
          showCheckboxVal: showCheckboxVal,
        ),
        primaryOnTap: () {
          typeOfItemSheet.value = TypeOfItemSheet.normal;
          Navigator.pop(childContext, true);
        },
        secondaryText: MeTranslations.instance.screen_common_buttonCancel,
        secondaryOnTap: () {
          Navigator.pop(
            childContext,
            false,
          );
        },
      ),
    );
    if (res == true && context.mounted) {
      // Get current user info (same pattern as in ListCubit)
      final String? name =
          context.read<AppBloc>().state.userData?.userInfo.name;
      final String email = context.read<AppBloc>().state.user!.email!;
      final String userName = (name == null || name == '') ? email : name;

      if (widget.isCopy) {
        await context.read<CopyMoveListCubit>().createCopyOfItemsInTheLists(
              uid: widget.myUid,
              userName: userName,
              keepStatusSame: showCheckboxVal.value,
            );
      } else {
        await context.read<CopyMoveListCubit>().createMoveOfItemsInTheLists(
              uid: widget.myUid,
              userName: userName,
              fromList: listState.taskIdsToTasks[widget.listId]!,
              keepStatusSame: showCheckboxVal.value,
            );
      }
      showMeSnackbar(
        widget.isCopy
            ? copyMoveState.items.length > 1
                ? MeTranslations.instance.toast_itemCopied_multipleItemsContent
                : MeTranslations.instance.toast_itemCopied_singleItemContent
            : copyMoveState.items.length > 1
                ? MeTranslations.instance.toast_itemMoved_multipleItemsContent
                : MeTranslations.instance.toast_itemMoved_singleItemContent,
      );
      if (context.mounted) {
        Navigator.pop(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (v, result) async {
        if (!v && await resVal() && context.mounted) {
          Navigator.pop(context);
        }
      },
      child: BlocBuilder<MyListsActionsCubit, MyListsActionsState>(
        builder: (context, myListsState) {
          return BlocBuilder<SharedWithMeListsActionsCubit,
              SharedWithMeListsActionsState>(
            builder: (context, sharedListsState) {
              return BlocBuilder<MainListsCubit, MainListsState>(
                builder: (context, mainListsState) {
                  List<ListData> lists = [];

                  // Get my lists
                  if (myListsState is MyListsActionsStateLoaded) {
                    for (String listId in myListsState.filteredMyListIds) {
                      final list = mainListsState.taskIdsToTasks[listId];
                      if (list != null && list.id != widget.listId) {
                        lists.add(list);
                      }
                    }
                  }

                  // Get shared lists
                  if (sharedListsState is SharedWithMeListsActionsStateLoaded) {
                    for (String listId
                        in sharedListsState.filteredSharedListIds) {
                      final list = mainListsState.taskIdsToTasks[listId];
                      if (list != null && list.id != widget.listId) {
                        // Check if user has editor permissions on shared list
                        final String? currentUserEmail = getCurrentUserEmail();
                        final String? hashedEmail = currentUserEmail != null
                            ? hashData(currentUserEmail)
                            : null;

                        if (hashedEmail != null) {
                          final memberConfig =
                              list.members.membersConfig[hashedEmail];
                          if (memberConfig != null &&
                              memberConfig.role == MemberRole.editor) {
                            lists.add(list);
                          }
                        }
                      }
                    }
                  }

                  return BlocProvider.value(
                    value: BlocProvider.of<CopyMoveListCubit>(
                      widget.parentContext,
                    ),
                    child: BlocBuilder<CopyMoveListCubit, CopyMoveListState>(
                      builder: (context, copyMoveState) {
                        return Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            DialogAndBottomSheetAppBar(
                              title: widget.isCopy
                                  ? MeTranslations.instance
                                      .bottomSheet_listCopyTo_titleTopBar
                                  : MeTranslations.instance
                                      .bottomSheet_listMoveTo_titleTopBar,
                              onTap: () async {
                                if (await resVal() && context.mounted) {
                                  Navigator.pop(context);
                                }
                              },
                            ),
                            Flexible(
                              child: ListView.separated(
                                shrinkWrap: true,
                                controller: scrollController,
                                scrollDirection: Axis.vertical,
                                itemCount: lists.length,
                                separatorBuilder: (context, index) =>
                                    const SizedBox(
                                  height: 1,
                                ),
                                itemBuilder: (context, index) {
                                  ListData list = lists[index];

                                  final MeString itemCountString =
                                      list.itemCount == 0
                                          ? MeTranslations.instance
                                              .screen_lists_listsSubContent
                                          : MeString.fromVariable(
                                              '${list.completedItemCount}/${list.itemCount}',
                                            );

                                  return Column(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      MeCheckBoxListTile(
                                        onChanged: (value) async {
                                          if (!widget.isCopy) {
                                            context
                                                .read<CopyMoveListCubit>()
                                                .selectList(
                                                  list,
                                                );
                                            action(
                                              copyMoveState,
                                              context,
                                              mainListsState,
                                            );
                                            return;
                                          }
                                          if (value == true) {
                                            context
                                                .read<CopyMoveListCubit>()
                                                .selectList(
                                                  list,
                                                );
                                          } else if (value == false) {
                                            context
                                                .read<CopyMoveListCubit>()
                                                .removeList(
                                                  list,
                                                );
                                          }
                                        },
                                        title: MeText(
                                          text: MeString(list.title),
                                          meFontStyle: MeFontStyle.D8,
                                        ),
                                        subtitlePadding: 12,
                                        subtitle: Row(
                                          children: [
                                            MeIcon(
                                              iconPath:
                                                  Assets.svg.circleCheck.path,
                                              iconColor: colorScheme.color7,
                                            ),
                                            const SizedBox(width: 6),
                                            MeText(
                                              text: itemCountString,
                                              meFontStyle: MeFontStyle.F7,
                                            ),
                                          ],
                                        ),
                                        value: widget.isCopy
                                            ? copyMoveState.lists.contains(list)
                                            : null,
                                      ),
                                      if (isListScrollable &&
                                          index == lists.length - 1 &&
                                          widget.isCopy)
                                        const SizedBox(height: 24),
                                    ],
                                  );
                                },
                              ),
                            ),
                            if (widget.isCopy) ...[
                              const SizedBox(height: 2),
                              Container(
                                padding: const EdgeInsets.all(16),
                                color: colorScheme.color5,
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    MeText(
                                      text: MeString(
                                        '${copyMoveState.lists.length}/${lists.length}',
                                      ),
                                      meFontStyle: MeFontStyle.F7,
                                    ),
                                    const SizedBox(width: 24),
                                    Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        splashFactory: InkRipple.splashFactory,
                                        onTap: copyMoveState.lists.isNotEmpty
                                            ? () async {
                                                action(
                                                  copyMoveState,
                                                  context,
                                                  mainListsState,
                                                );
                                              }
                                            : null,
                                        child: MeText(
                                          text: widget.isCopy
                                              ? MeTranslations.instance
                                                  .bottomSheet_listSelectItems_copy
                                              : MeTranslations.instance
                                                  .bottomSheet_listSelectItems_move,
                                          meFontStyle:
                                              copyMoveState.lists.isNotEmpty
                                                  ? MeFontStyle.C1
                                                  : MeFontStyle.C4,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ],
                        );
                      },
                    ),
                  );
                },
              );
            },
          );
        },
      ),
    );
  }
}
