import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import 'package:mevolve/constants/animation_constants.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/constants/app_strings.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/user/list/my_list_settings.dart';
import 'package:mevolve/data/models/user/view_settings.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/user_resources_cubit.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/lists/cubit/my_list/list_operations.dart';
import 'package:mevolve/features/lists/cubit/my_lists_actions/my_lists_actions_cubit.dart';
import 'package:mevolve/features/lists/cubit/my_lists_filter/my_lists_filter_cubit.dart';
import 'package:mevolve/features/lists/widgets/list_data_widget.dart';
import 'package:mevolve/features/lists/widgets/my_lists_filter/my_lists_filter_widget.dart';
import 'package:mevolve/features/tasks_providers/lists/main_lists_cubit.dart';
import 'package:mevolve/features/widgets/animated_list.dart';
import 'package:mevolve/features/widgets/custom_reorderable_listview.dart';
import 'package:mevolve/features/widgets/empty_state_widget.dart';
import 'package:mevolve/features/widgets/expandable_items_listview.dart';
import 'package:mevolve/features/widgets/go_to_top_button.dart';
import 'package:mevolve/features/widgets/loaders.dart';
import 'package:mevolve/features/widgets/me_groupby_accordion.dart';
import 'package:mevolve/features/widgets/me_scrollbar.dart';
import 'package:mevolve/generated/metranslations.gen.dart';

class MyListsTab extends StatefulWidget {
  const MyListsTab({Key? key}) : super(key: key);

  @override
  State<MyListsTab> createState() => _MyListsTabState();
}

class _MyListsTabState extends State<MyListsTab>
    with SingleTickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late final AnimationController _animationController;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 0),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    // Reset filter on dispose
    authenticatedGlobalContext?.read<MyListsFilterCubit>().resetFilter();
    super.dispose();
  }

  void _showFilter() {
    if (mounted) {
      _animationController.forward();
    }
  }

  void _hideFilter() {
    if (mounted) {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return WidgetTracker(
      trackAction: TrackAction.lists,
      child: BlocBuilder<AppBloc, AppState>(
        builder: (context, appState) {
          final viewSettings = appState.viewSettings;
          if (viewSettings == null) {
            return const SizedBox.shrink();
          }

          return BlocBuilder<MyListsActionsCubit, MyListsActionsState>(
            builder: (context, state) {
              // If we are fetching lists for the first time show the loading indicator.
              if (state is MyListsActionsStateInitial) {
                return const CustomCircularLoader();
              }

              if (state is MyListsActionsStateLoaded) {
                if (state.showFilterRow) {
                  _showFilter();
                } else {
                  _hideFilter();
                }

                // Handle empty state when no filters applied
                if (state.filteredMyListIds.isEmpty &&
                    !context.read<MyListsFilterCubit>().isFilterApplied) {
                  return Center(
                    child: EmptyStateWidget(
                      imagePath: AppStrings.listsMyListsTabEmptyIllustration(
                        colorScheme: colorScheme,
                      ),
                      imageText: MeTranslations
                          .instance.tab_listsMyLists_emptyScreenContent,
                    ),
                  );
                }

                // Handle empty state when filters applied but no results
                if (state.filteredMyListIds.isEmpty &&
                    context.read<MyListsFilterCubit>().isFilterApplied) {
                  return Column(
                    children: [
                      SizeTransition(
                        sizeFactor: _animationController,
                        axisAlignment: 1,
                        child: const MyListsFilterWidget(),
                      ),
                      Expanded(
                        child: EmptyStateWidget(
                          imagePath: AppStrings.recordsEmptyIllustration(
                            colorScheme: colorScheme,
                          ),
                          imageText: MeTranslations
                              .instance.screen_common_recordsNotFound,
                        ),
                      ),
                    ],
                  );
                }
              }

              return Column(
                children: [
                  SizeTransition(
                    sizeFactor: _animationController,
                    axisAlignment: 1,
                    child: const MyListsFilterWidget(),
                  ),
                  Expanded(
                    child: MyListsTabViewListView(
                      isFilterRowVisible: state.showFilterRow,
                      viewSettings: viewSettings,
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class MyListsTabViewListView extends StatefulWidget {
  const MyListsTabViewListView({
    super.key,
    required this.isFilterRowVisible,
    required this.viewSettings,
  });

  final bool isFilterRowVisible;
  final ViewSettings viewSettings;

  @override
  State<MyListsTabViewListView> createState() => _MyListsTabViewListViewState();
}

class _MyListsTabViewListViewState extends State<MyListsTabViewListView> {
  late ScrollController _scrollController;

  @override
  void initState() {
    _scrollController = ScrollController();
    context
        .read<MyListsActionsCubit>()
        .attachScrollController(_scrollController, context);
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return BlocBuilder<MyListsActionsCubit, MyListsActionsState>(
      builder: (context, state) {
        List<String> listIds = state.filteredMyListIds;

        if (!state.showFilterRow && listIds.isEmpty) {
          return Center(
            child: EmptyStateWidget(
              imagePath: AppStrings.listsMyListsTabEmptyIllustration(
                colorScheme: colorScheme,
              ),
              imageText:
                  MeTranslations.instance.tab_listsMyLists_emptyScreenContent,
            ),
          );
        }

        if (listIds.isEmpty) {
          return Center(
            child: EmptyStateWidget(
              imagePath: AppStrings.recordsEmptyIllustration(
                colorScheme: colorScheme,
              ),
              imageText: MeTranslations.instance.screen_common_recordsNotFound,
            ),
          );
        }

        return Column(
          children: [
            Expanded(
              child: BlocConsumer<AppBloc, AppState>(
                buildWhen: (previous, current) {
                  return previous.viewSettings?.listSettings !=
                      current.viewSettings?.listSettings;
                },
                listener: (context, state) {
                  if (state.viewSettings == null) {
                    return;
                  }
                },
                builder: (context, appState) {
                  final viewSettings = appState.viewSettings;
                  if (viewSettings == null) {
                    return const SizedBox.shrink();
                  }

                  return Stack(
                    children: [
                      // Use the existing UI components from my_lists.dart
                      _buildListView(listIds, viewSettings),

                      // Go to top button
                      GoToTopButton(
                        scrollController: _scrollController,
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildListView(List<String> listIds, ViewSettings viewSettings) {
    // Check if we should group by hashtag or show normal view
    final groupByType = viewSettings.listSettings.myListSettings.groupByType;

    if (groupByType == MyListGroupByType.hashtag) {
      return _buildGroupByHashtagView(listIds, viewSettings);
    } else {
      return _buildNormalView(listIds, viewSettings);
    }
  }

  Widget _buildNormalView(List<String> listIds, ViewSettings viewSettings) {
    return BlocBuilder<MainListsCubit, MainListsState>(
      builder: (context, listsState) {
        if (listsState is! MainListsStateLoaded) {
          return const SizedBox.shrink();
        }

        // Get actual list data from provider
        List<ListData> lists = listIds
            .map((id) => listsState.taskIdsToTasks[id])
            .whereType<ListData>()
            .toList();

        final isVibrate = context
            .read<AppBloc>()
            .state
            .viewSettings!
            .appSettings
            .isVibrationEnabled;

        return NotificationListener<UserScrollNotification>(
          onNotification: (notification) {
            // Handle scroll for FAB animation control
            return false;
          },
          child: MeScrollbar(
            controller: _scrollController,
            length: lists.length,
            child: NormalViewMyList(
              scrollController: _scrollController,
              lists: lists,
              isVibrate: isVibrate,
            ),
          ),
        );
      },
    );
  }

  Widget _buildGroupByHashtagView(
    List<String> listIds,
    ViewSettings viewSettings,
  ) {
    return BlocBuilder<MainListsCubit, MainListsState>(
      builder: (context, listsState) {
        if (listsState is! MainListsStateLoaded) {
          return const SizedBox.shrink();
        }

        // Get actual list data from provider
        List<ListData> lists = listIds
            .map((id) => listsState.taskIdsToTasks[id])
            .whereType<ListData>()
            .toList();

        // Group lists by hashtags (following the pattern from my_lists.dart)
        Map<String, List<ListData>> hashtagGrouped =
            _groupListsByHashtags(lists);

        return MyListsGroupByHashtagView(
          scrollController: _scrollController,
          hashtagGrouped: hashtagGrouped,
          lists: lists,
          viewSettings: viewSettings,
          isFilterRowVisible: widget.isFilterRowVisible,
        );
      },
    );
  }

  Map<String, List<ListData>> _groupListsByHashtags(List<ListData> lists) {
    Map<String, List<ListData>> groupedLists = {};

    for (var list in lists) {
      List<String> hashtags = list.hashtags.isNotEmpty
          ? list.hashtags
              .map(
                (hashtag) =>
                    context
                        .read<UserResourcesCubit>()
                        .state
                        .userResources
                        ?.getTagById(hashtag) ??
                    '',
              )
              .toList()
          : [''];

      for (var tag in hashtags) {
        groupedLists.putIfAbsent(tag, () => []).add(list);
      }
    }

    // Sort lists within each group using ordering from view settings
    // Lists without ordering appear at top, then ordered lists (higher values first)
    groupedLists.forEach((key, value) {
      value.sort((a, b) {
        final orderingMap =
            context.read<AppBloc>().state.viewSettings?.listSettings.ordering ??
                <String, double>{};
        final aOrdering = orderingMap[a.id];
        final bOrdering = orderingMap[b.id];

        // If both have no ordering, maintain original order
        if (aOrdering == null && bOrdering == null) return 0;
        // If only a has no ordering, a comes first
        if (aOrdering == null) return -1;
        // If only b has no ordering, b comes first
        if (bOrdering == null) return 1;
        // Both have ordering, higher value comes first
        return bOrdering.compareTo(aOrdering);
      });
    });

    // Sort the keys and move empty string to the end
    var sortedKeys = groupedLists.keys.toList()..sort((a, b) => a.compareTo(b));
    if (sortedKeys.contains('')) {
      sortedKeys.remove('');
      sortedKeys.add('');
    }

    return {for (var key in sortedKeys) key: groupedLists[key]!};
  }
}

class MyListsGroupByHashtagView extends StatefulWidget {
  const MyListsGroupByHashtagView({
    super.key,
    required this.scrollController,
    required this.hashtagGrouped,
    required this.lists,
    required this.viewSettings,
    required this.isFilterRowVisible,
  });

  final ScrollController scrollController;
  final Map<String, List<ListData>> hashtagGrouped;
  final List<ListData> lists;
  final ViewSettings viewSettings;
  final bool isFilterRowVisible;

  @override
  State<MyListsGroupByHashtagView> createState() =>
      _MyListsGroupByHashtagViewState();
}

class _MyListsGroupByHashtagViewState extends State<MyListsGroupByHashtagView> {
  late ExpandableListController _expandableListController;

  @override
  void initState() {
    super.initState();
    _expandableListController = ExpandableListController(
      defaultExpanded:
          !widget.viewSettings.listSettings.myListSettings.collapsedView,
    );
  }

  @override
  void didUpdateWidget(covariant MyListsGroupByHashtagView oldWidget) {
    // Update expandable controller when collapsed view setting changes
    if (oldWidget.viewSettings.listSettings.myListSettings.collapsedView !=
        widget.viewSettings.listSettings.myListSettings.collapsedView) {
      final bool isCollapsed =
          widget.viewSettings.listSettings.myListSettings.collapsedView;
      _expandableListController.updateDefault(!isCollapsed);
      if (isCollapsed) {
        _expandableListController.collapseAll();
      } else {
        _expandableListController.expandAll();
      }
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  void dispose() {
    _expandableListController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return MeScrollbar(
      controller: widget.scrollController,
      length: widget.hashtagGrouped.length,
      totalLenIncludingNestedListItems: widget.hashtagGrouped.values.fold<int>(
        0,
        (previousValue, element) => previousValue + element.length,
      ),
      child: ExpandableItemsListView<ListData, String>(
        scrollController: widget.scrollController,
        controller: _expandableListController,
        allowInsertRemoveAnimation: ListAnimationsStatus.disable,
        items: List.generate(
          widget.hashtagGrouped.length,
          (i) => ListItem(
            id: _getHeaderText(widget.hashtagGrouped.keys.elementAt(i)).text,
            data: widget.hashtagGrouped.keys.elementAt(i),
            subDataList: List.generate(
              widget.hashtagGrouped.values.elementAt(i).length,
              (j) => ListSubItem(
                id: widget.hashtagGrouped.values.elementAt(i)[j].id,
                subData: widget.hashtagGrouped.values.elementAt(i)[j],
                parentData: widget.hashtagGrouped.keys.elementAt(i),
              ),
            ),
          ),
        ),
        headerBuilder: (context, index, headerItem, isExpanded) {
          return ExpandableListHeader(
            headingText: _getHeaderText(headerItem.data),
            totalCount: headerItem.subDataList.length,
            showCount:
                widget.viewSettings.listSettings.myListSettings.showCounts,
          );
        },
        itemBuilder: (context, item) {
          final list = item.subData;
          return ListDataWidget(
            key: ValueKey(list.id),
            listData: list,
          );
        },
        footer: SizedBox(
          height: widget.hashtagGrouped.isEmpty ? 100 : 0,
        ),
        headerSeparator: const ListHeaderSeparator(),
        subItemSeparator: const ListSubItemSeparator(),
      ),
    );
  }

  MeString _getHeaderText(String key) {
    return key.isEmpty
        ? MeTranslations.instance.screen_common_groupByNoHashtags
        : MeTranslations.instance.toast_hashtagDeleted_singleContent(oil: key);
  }
}

class NormalViewMyList extends StatefulWidget {
  const NormalViewMyList({
    super.key,
    required this.scrollController,
    required this.lists,
    required this.isVibrate,
  });

  final ScrollController scrollController;
  final List<ListData> lists;
  final bool isVibrate;

  @override
  State<NormalViewMyList> createState() => _NormalViewMyListState();
}

class _NormalViewMyListState extends State<NormalViewMyList> {
  List<ListData>? _optimisticLists;

  @override
  void didUpdateWidget(NormalViewMyList oldWidget) {
    super.didUpdateWidget(oldWidget);
    // Reset optimistic state when new data comes from the server
    if (oldWidget.lists != widget.lists) {
      _optimisticLists = null;
    }
  }

  List<ListData> get _displayLists => _optimisticLists ?? widget.lists;

  @override
  Widget build(BuildContext context) {
    return SlidableAutoCloseBehavior(
      child: AnimationLimiter(
        child: CustomReorderableListView.separated(
          scrollController: widget.scrollController,
          itemCount: _displayLists.length,
          onReorder: (oldIndex, newIndex) {
            if (widget.isVibrate) {
              HapticFeedback.heavyImpact();
            }

            final originalNewIndex = newIndex;

            // Optimistic UI update - immediately show the reordered list
            setState(() {
              _optimisticLists = List.from(widget.lists);

              if (oldIndex < newIndex) {
                newIndex--;
              }

              if (oldIndex != newIndex) {
                final item = _optimisticLists!.removeAt(oldIndex);
                _optimisticLists!.insert(newIndex, item);
              }
            });

            // Handle reordering through repository (background update)
            context.read<ListOperations>().reorderRawList(
                  list: widget.lists[oldIndex],
                  allLists: widget.lists,
                  oldIndex: oldIndex,
                  newIndex: originalNewIndex,
                );
          },
          proxyDecorator: (child, index, animation) {
            return AnimatedBuilder(
              animation: animation,
              builder: (BuildContext context, Widget? child) {
                final animValue = Curves.easeInOut.transform(animation.value);
                final elevation = lerpDouble(0, 6, animValue)!;
                final scale = lerpDouble(1, 1.02, animValue)!;
                return Transform.scale(
                  scale: scale,
                  child: Material(
                    elevation: elevation,
                    color: Colors.transparent,
                    shadowColor: Colors.black26,
                    child: child,
                  ),
                );
              },
              child: child,
            );
          },
          separatorBuilder: (context, index) {
            return const ListSubItemSeparator();
          },
          itemBuilder: (context, index) {
            final list = _displayLists[index];
            return AnimationConfiguration.staggeredList(
              position: index,
              duration: const Duration(
                milliseconds: AnimationConstants.listFadeDuration,
              ),
              child: SlideAnimation(
                verticalOffset: 50,
                child: FadeInAnimation(
                  child: ListDataWidget(
                    key: ValueKey(list.id),
                    listData: list,
                  ),
                ),
              ),
            );
          },
          footer: SizedBox(
            height: _displayLists.isNotEmpty ? SizeConstants.listEndMargin : 0,
          ),
        ),
      ),
    );
  }
}
