import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/user/list/my_list_settings.dart';
import 'package:mevolve/data/models/user/view_settings.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_text_icon.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/me_checkbox_list_tile.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/features/widgets/tab_kebab_widgets.dart';

class MyListViewConfigKebab extends StatelessWidget {
  const MyListViewConfigKebab({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        MeIconButton(
          key: const ValueKey('kebabMenu'),
          isCircularIconButton: true,
          iconPath: Assets.svg.moreVertical.path,
          buttonColor: colorScheme.color9,
          iconColor: ScreenSizeState.instance.isBigScreen
              ? colorScheme.color2
              : colorScheme.color12,
          iconContainerSize: const SizedBox(
            width: SizeConstants.iconButtonIconContainerSize,
            height: SizeConstants.iconButtonIconContainerSize,
          ),
          padding: const EdgeInsets.symmetric(
            horizontal: 12,
            vertical: 12,
          ),
          onPressed: () {
            showMeScrollableModalBottomSheet(
              bottomSheetLevel: SizeConstants.level2BottomSheet,
              builder: (_, __) {
                return const MyListTabSettingWidget();
              },
            );
          },
        ),
        const SizedBox(width: 4),
      ],
    );
  }
}

class MyListTabViewSettingWidget extends StatelessWidget {
  const MyListTabViewSettingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            SizeConstants.bottomSheetBorderRadius,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          DialogAndBottomSheetAppBar(
            title: MeTranslations
                .instance.screen_common_kebabMenuViewConfiguration,
          ),
          BlocBuilder<AppBloc, AppState>(
            buildWhen: (
              previous,
              current,
            ) =>
                previous.viewSettings?.listSettings.myListSettings !=
                current.viewSettings?.listSettings.myListSettings,
            builder: (context, state) {
              MyListSettings? myList =
                  state.viewSettings!.listSettings.myListSettings;
              bool showDescription = myList.showDescription;
              bool itemCount = myList.itemCount;
              bool showCollaboratorsCount = myList.showCollaboratorsCount;
              bool showHashtags = myList.showHashtags;

              return ListView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  MeCheckBoxListTile(
                    title: MeTextIcon(
                      text: MeTranslations.instance.screen_common_description,
                      iconPath: Assets.svg.icLines.path,
                    ),
                    value: showDescription,
                    onChanged: (value) {
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: state.viewSettings!.copyWith(
                              listSettings:
                                  state.viewSettings!.listSettings.copyWith(
                                myListSettings: myList.copyWith(
                                  showDescription: value,
                                ),
                              ),
                            ),
                          );
                    },
                  ),
                  const SizedBox(height: 0.5),
                  MeCheckBoxListTile(
                    title: MeTextIcon(
                      text: MeTranslations
                          .instance.bottomSheet_viewConfiguration_itemCount,
                      iconPath: Assets.svg.circleCheck.path,
                    ),
                    value: itemCount,
                    onChanged: (value) {
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: state.viewSettings!.copyWith(
                              listSettings:
                                  state.viewSettings!.listSettings.copyWith(
                                myListSettings: myList.copyWith(
                                  itemCount: value,
                                ),
                              ),
                            ),
                          );
                    },
                  ),
                  const SizedBox(height: 0.5),
                  MeCheckBoxListTile(
                    title: MeTextIcon(
                      text: MeTranslations.instance
                          .bottomSheet_viewConfiguration_collaborationCount,
                      iconPath: Assets.svg.people.path,
                    ),
                    value: showCollaboratorsCount,
                    onChanged: (value) {
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: state.viewSettings!.copyWith(
                              listSettings:
                                  state.viewSettings!.listSettings.copyWith(
                                myListSettings: myList.copyWith(
                                  showCollaboratorsCount: value,
                                ),
                              ),
                            ),
                          );
                    },
                  ),
                  const SizedBox(height: 0.5),
                  MeCheckBoxListTile(
                    title: MeTextIcon(
                      text: MeTranslations.instance.screen_common_hashtag,
                      iconPath: Assets.svg.hashtagIcon.path,
                    ),
                    value: showHashtags,
                    onChanged: (value) {
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: state.viewSettings!.copyWith(
                              listSettings:
                                  state.viewSettings!.listSettings.copyWith(
                                myListSettings: myList.copyWith(
                                  showHashtags: value,
                                ),
                              ),
                            ),
                          );
                    },
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}

class MyListGroupBySettingWidget extends StatefulWidget {
  const MyListGroupBySettingWidget({super.key});

  @override
  State<MyListGroupBySettingWidget> createState() =>
      _MyListGroupBySettingWidgetState();
}

class _MyListGroupBySettingWidgetState
    extends State<MyListGroupBySettingWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            SizeConstants.bottomSheetBorderRadius,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          DialogAndBottomSheetAppBar(
            title: MeTranslations.instance.screen_common_groupBy,
          ),
          BlocBuilder<AppBloc, AppState>(
            // buildWhen: (previous, current) => shouldBuild(previous, current),
            builder: (context, state) {
              ViewSettings? viewSettings = state.viewSettings;
              MyListGroupByType? listGroupBy =
                  viewSettings?.listSettings.myListSettings.groupByType;

              return ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: MyListGroupByType.values.length,
                separatorBuilder: (context, index) {
                  return const SizedBox(height: 0.5);
                },
                itemBuilder: (context, index) {
                  MyListGroupByType groupBy = MyListGroupByType.values[index];

                  return MeCheckBoxListTile(
                    title: MeTextIcon(
                      text: groupBy.toMeString(),
                      meFontStyle: MeFontStyle.D8,
                      iconPath: groupBy.iconPath(),
                      iconSize: const SizedBox(
                        height: 14,
                        width: 14,
                      ),
                    ),
                    showDisabledCheckbox: false,
                    value: listGroupBy == groupBy,
                    onChanged: (value) {
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: viewSettings!.copyWith(
                              listSettings: viewSettings.listSettings.copyWith(
                                myListSettings: viewSettings
                                    .listSettings.myListSettings
                                    .copyWith(
                                  groupByType: groupBy,
                                  collapsedView:
                                      groupBy == MyListGroupByType.none
                                          ? false
                                          : true,
                                  showCounts: groupBy == MyListGroupByType.none
                                      ? false
                                      : true,
                                ),
                              ),
                            ),
                          );
                      Navigator.pop(context);
                    },
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }
}

class MyListTabSettingWidget extends StatelessWidget {
  const MyListTabSettingWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(
            SizeConstants.bottomSheetBorderRadius,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          DialogAndBottomSheetAppBar(
            title:
                MeTranslations.instance.bottomSheet_listKebabMenu_titleTopBar,
          ),
          BlocBuilder<AppBloc, AppState>(
            buildWhen: (previous, current) =>
                previous.viewSettings?.listSettings.myListSettings !=
                current.viewSettings?.listSettings.myListSettings,
            builder: (context, state) {
              MyListSettings? myList =
                  state.viewSettings!.listSettings.myListSettings;
              bool collapseView = myList.collapsedView;
              bool showCount = myList.showCounts;
              bool showDescription = myList.showDescription;
              bool itemCount = myList.itemCount;
              bool showCollaboratorsCount = myList.showCollaboratorsCount;
              bool showHashtags = myList.showHashtags;
              ViewSettingType myListViewSettings = myList.viewType;

              MeString getViewConfigText() {
                String text = '';
                if (showDescription) {
                  text +=
                      '${MeTranslations.instance.screen_common_description.text}, ';
                }
                if (itemCount) {
                  text +=
                      '${MeTranslations.instance.bottomSheet_viewConfiguration_itemCount.text}, ';
                }
                if (showCollaboratorsCount) {
                  text +=
                      '${MeTranslations.instance.bottomSheet_viewConfiguration_collaborationCount.text}, ';
                }
                if (showHashtags) {
                  text +=
                      '${MeTranslations.instance.screen_common_hashtag.text}, ';
                }
                return text.isEmpty
                    ? MeTranslations.instance.screen_common_none
                    : MeString(text.substring(0, text.length - 2));
              }

              return ListView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  GroupByWithConfig<MyListGroupByType, MyListGroupConfig>(
                    trailingOffset: -75,
                    selectedTrailingPopMenuItem: myList.groupByType,
                    trailingPopMenuValues: MyListGroupByType.values,
                    onTrailingPopMenuItemSelected: (MyListGroupByType item) {
                      MyListSettings settings = myList;
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: state.viewSettings!.copyWith(
                              listSettings:
                                  state.viewSettings!.listSettings.copyWith(
                                myListSettings: settings.copyWith(
                                  groupByType: item,
                                  collapsedView: true,
                                  showCounts: false,
                                ),
                              ),
                            ),
                          );
                    },
                    leadingTapDisabled:
                        myList.groupByType == MyListGroupByType.none,
                    hideSubtitle: myList.groupByType == MyListGroupByType.none,
                    selectedSubtitlePopMenuItem: {
                      if (collapseView) MyListGroupConfig.collapsedView,
                      if (showCount) MyListGroupConfig.showCounts,
                    },
                    subtitlePopMenuValues: MyListGroupConfig.values,
                    onSubtitlePopMenuItemSelected:
                        (Set<MyListGroupConfig> item) {
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: state.viewSettings!.copyWith(
                              listSettings:
                                  state.viewSettings!.listSettings.copyWith(
                                myListSettings: myList.copyWith(
                                  collapsedView: item.contains(
                                    MyListGroupConfig.collapsedView,
                                  ),
                                  showCounts: item.contains(
                                    MyListGroupConfig.showCounts,
                                  ),
                                ),
                              ),
                            ),
                          );
                    },
                  ),
                  const SizedBox(height: 1),
                  ViewSettingsWidget<ViewSettingType>(
                    offset: -120,
                    subtitleText: getViewConfigText(),
                    leadingOnTap: () {
                      showMeScrollableModalBottomSheet(
                        bottomSheetLevel: SizeConstants.level3BottomSheet,
                        builder: (_, __) {
                          return const MyListTabViewSettingWidget();
                        },
                      );
                    },
                    selectedTrailingPopMenuItem: myListViewSettings,
                    disableLeadingIfTrailingSelectedAs: ViewSettingType
                        .getValues
                        .where((element) => element != ViewSettingType.custom)
                        .toList(),
                    trailingPopMenuValues: ViewSettingType.getValues,
                    onTrailingPopMenuItemSelected: (ViewSettingType item) {
                      MyListSettings settings = myList;

                      if (item == ViewSettingType.defaultView) {
                        settings = settings.copyWith(
                          viewType: item,
                          showDescription: false,
                          showHashtags: false,
                          showCollaboratorsCount: true,
                          itemCount: true,
                        );
                      } else if (item == ViewSettingType.custom) {
                        settings = settings.copyWith(
                          viewType: item,
                          showDescription: false,
                          showHashtags: false,
                          showCollaboratorsCount: true,
                          itemCount: true,
                        );
                      } else {
                        settings = settings.copyWith(
                          viewType: item,
                          showDescription: false,
                          showHashtags: false,
                          showCollaboratorsCount: false,
                          itemCount: true,
                        );
                      }
                      context.read<AppBloc>().updateViewSettings(
                            viewSettings: state.viewSettings!.copyWith(
                              listSettings:
                                  state.viewSettings!.listSettings.copyWith(
                                myListSettings: settings.copyWith(
                                  viewType: item,
                                ),
                              ),
                            ),
                          );
                    },
                  ),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}
