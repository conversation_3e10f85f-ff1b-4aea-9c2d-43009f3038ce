import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/features/tasks_providers/lists/main_lists_cubit.dart';

import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/lists/cubit/my_list/list_operations.dart';
import 'package:mevolve/features/lists/widgets/list_dialog_checkbox.dart';
import 'package:mevolve/features/lists/widgets/list_item_sheet.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/list_option_sheet.dart';
import 'package:mevolve/features/lists/widgets/my_lists_fab.dart';
import 'package:mevolve/features/widgets/me_custom_bottomsheet.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_list_tile.dart';
import 'package:mevolve/features/widgets/show_me_discard_dialog.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';

class ListMoveItemsTile extends StatelessWidget {
  const ListMoveItemsTile({
    super.key,
    required this.listData,
  });

  final ListData listData;

  @override
  Widget build(BuildContext context) {
    int totalListLen =
        context.read<MainListsCubit>().state.taskIdsToTasks.values.length;
    return MeListTile(
      padding: const EdgeInsets.only(
        left: 16,
        right: 4,
      ),
      title: MeText(
        text: MeTranslations.instance.bottomSheet_listItemsKebabMenu_moveItems,
        meFontStyle: MeFontStyle.D8,
      ),
      trailing: Row(
        children: [
          MeIconButton(
            iconPath: Assets.svg.copyMoveIcon.path,
            iconColor: context.meColorScheme.color35,
            iconSize: const SizedBox(height: 16, width: 16),
            iconContainerSize: const SizedBox(height: 24, width: 24),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            isCircularIconButton: false,
            onPressed: () {
              if (listData.listItems.isEmpty) {
                showDialog(
                  context: context,
                  builder: (childContext) => MeDialog(
                    insetPadding: ScreenSizeState.instance
                        .getDialogInsetPadding(DialogLevel.level1),
                    title: MeTranslations.instance.overlay_emptyList_title,
                    description:
                        MeTranslations.instance.overlay_emptyList_contentOfCopy,
                    primaryText: MeTranslations.instance.screen_common_ok,
                    primaryOnTap: () {
                      Navigator.pop(childContext, true);
                    },
                  ),
                );
              } else if (totalListLen == 1) {
                showDialog(
                  context: context,
                  builder: (childContext) => MeDialog(
                    insetPadding: ScreenSizeState.instance
                        .getDialogInsetPadding(DialogLevel.level1),
                    title: MeTranslations.instance.overlay_noList_title,
                    description:
                        MeTranslations.instance.overlay_noList_contentOfCopy,
                    primaryText: MeTranslations.instance.screen_common_ok,
                    primaryOnTap: () {
                      Navigator.pop(childContext, true);
                    },
                  ),
                );
              } else {
                typeOfItemSheet.value = TypeOfItemSheet.copy;
                Navigator.pop(context);
              }
            },
          ),
          MeIconButton(
            iconPath: Assets.svg.moveIcon.path,
            iconColor: context.meColorScheme.color35,
            iconSize: const SizedBox(height: 15, width: 15),
            iconContainerSize: const SizedBox(height: 24, width: 24),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            isCircularIconButton: false,
            onPressed: () {
              if (listData.listItems.isEmpty) {
                showDialog(
                  context: context,
                  builder: (childContext) => MeDialog(
                    insetPadding: ScreenSizeState.instance
                        .getDialogInsetPadding(DialogLevel.level1),
                    title: MeTranslations.instance.overlay_emptyList_title,
                    description:
                        MeTranslations.instance.overlay_emptyList_contentOfMove,
                    primaryText: MeTranslations.instance.screen_common_ok,
                    primaryOnTap: () {
                      Navigator.pop(childContext, true);
                    },
                  ),
                );
              } else if (totalListLen == 1) {
                showDialog(
                  context: context,
                  builder: (childContext) => MeDialog(
                    insetPadding: ScreenSizeState.instance
                        .getDialogInsetPadding(DialogLevel.level1),
                    title: MeTranslations.instance.overlay_noList_title,
                    description:
                        MeTranslations.instance.overlay_noList_contentOfMove,
                    primaryText: MeTranslations.instance.screen_common_ok,
                    primaryOnTap: () {
                      Navigator.pop(childContext, true);
                    },
                  ),
                );
              } else {
                typeOfItemSheet.value = TypeOfItemSheet.move;
                Navigator.pop(context);
              }
            },
          ),
        ],
      ),
    );
  }
}

class CloneListTile extends StatefulWidget {
  const CloneListTile({
    super.key,
    required this.listData,
    required this.listCubit,
  });

  final ListData listData;
  final ListOperations listCubit;

  @override
  State<CloneListTile> createState() => _CloneListTileState();
}

class _CloneListTileState extends State<CloneListTile> {
  final TextEditingController _textEditingController = TextEditingController();
  final TextEditingController _descriptionEditingController =
      TextEditingController();
  List<String> selectedTags = [];
  bool isEdited = false;
  ValueNotifier<bool> showCheckboxVal = ValueNotifier<bool>(false);

  @override
  void initState() {
    setValues();
    super.initState();
  }

  void setValues() {
    _descriptionEditingController.text = widget.listData.description ?? '';
    selectedTags = widget.listData.hashtags;
  }

  @override
  Widget build(BuildContext context) {
    AppState appState = context.read<AppBloc>().state;
    return BlocBuilder<MainListsCubit, MainListsState>(
      builder: (context, state) {
        return MeListTile(
          title: MeText(
            text: MeTranslations
                .instance.bottomSheet_listItemsKebabMenu_cloneList,
            meFontStyle: MeFontStyle.D8,
          ),
          onTap: () async {
            if (widget.listData.listItems.isEmpty) {
              showDialog(
                context: context,
                builder: (childContext) => MeDialog(
                  insetPadding: ScreenSizeState.instance
                      .getDialogInsetPadding(DialogLevel.level1),
                  title: MeTranslations.instance.overlay_emptyList_title,
                  description: MeTranslations
                      .instance.overlay_emptyList_cloneListContent,
                  primaryText: MeTranslations.instance.screen_common_ok,
                  primaryOnTap: () {
                    Navigator.pop(childContext, true);
                  },
                ),
              );
              return;
            }
            bool? showCheckbox = await showDialog(
              context: context,
              builder: (childContext) => MeDialog(
                insetPadding: ScreenSizeState.instance
                    .getDialogInsetPadding(DialogLevel.level1),
                title: MeTranslations.instance.overlay_cloneList_title,
                description: MeTranslations.instance.overlay_cloneList_content,
                primaryText: MeTranslations.instance.overlay_cloneList_proceed,
                secondaryText:
                    MeTranslations.instance.screen_common_buttonCancel,
                customContentAfterDescriptionPadding:
                    const EdgeInsets.only(top: 0),
                customContentAfterDescription: ListDialogCheckbox(
                  showCheckboxVal: showCheckboxVal,
                ),
                primaryOnTap: () {
                  Navigator.pop(childContext, true);
                },
                secondaryOnTap: () {
                  Navigator.pop(childContext, false);
                },
              ),
            );

            void popVoid() async {
              bool isSameAsInitial = _textEditingController.text == '' &&
                  _descriptionEditingController.text ==
                      widget.listData.description &&
                  selectedTags == widget.listData.hashtags;
              if (isSameAsInitial) {
                showCheckboxVal.value = false;
                Navigator.pop(context, ListOptionTypes.listCloneDone);
                setValues();
                return;
              } else if (_descriptionEditingController.text == '' &&
                  _textEditingController.text == '' &&
                  selectedTags.isEmpty) {
                showCheckboxVal.value = false;
                Navigator.pop(context, ListOptionTypes.listCloneDone);
                if (isEdited == true) {
                  Navigator.pop(context, ListOptionTypes.listCloneDone);
                }
                setValues();
              } else {
                bool? val = await showMeDiscardDialog(
                  context,
                );
                if (val != null && val && context.mounted) {
                  _textEditingController.clear();
                  _descriptionEditingController.clear();
                  selectedTags = [];
                  showCheckboxVal.value = false;
                  Navigator.pop(context, false);
                  setValues();
                }
              }
            }

            void Function(String)? onFieldSubmitted(v) {
              context.read<ListOperations>().cloneList(
                    uid: appState.user!.uid,
                    description: _descriptionEditingController.text == ''
                        ? null
                        : _descriptionEditingController.text,
                    hashtags: selectedTags,
                    listItems: widget.listData.listItems,
                    title: _textEditingController.text,
                    adminName: appState.userData?.userInfo.name ?? '',
                    keyStatusSame: showCheckboxVal.value,
                  );
              _textEditingController.clear();
              _descriptionEditingController.clear();
              selectedTags = [];
              isEdited = true;
              popVoid();
              return null;
            }

            if (!context.mounted) return;
            ListNameAndDescriptionField data(
              SetShowDiscardDialogFlagValue? dis,
            ) =>
                ListNameAndDescriptionField(
                  sheetTitle:
                      MeTranslations.instance.bottomSheet_cloneList_titleTopBar,
                  onFieldSubmitted: (String v) {
                    return onFieldSubmitted(_textEditingController.text);
                  },
                  setShowDiscardDialogFlagValue: dis,
                  textEditingController: _textEditingController,
                  descriptionEditingController: _descriptionEditingController,
                  popVoid: () {
                    popVoid();
                  },
                  selectedTags: selectedTags,
                  onTagsChanged: (tags) {
                    selectedTags = tags;
                  },
                  bottomSheetLevel: SizeConstants.level2BottomSheet,
                  appBarWidget: widget.listData.activeCollaboratorCount > 0
                      ? MeIconButton(
                          onPressed: () {
                            showDialog(
                              context: context,
                              builder: (_) {
                                return MeDialog(
                                  insetPadding: ScreenSizeState.instance
                                      .getDialogInsetPadding(
                                    DialogLevel.level1,
                                  ),
                                  title: MeTranslations
                                      .instance.overlay_privateList_title,
                                  description: MeTranslations
                                      .instance.overlay_privateList_content,
                                  primaryText:
                                      MeTranslations.instance.screen_common_ok,
                                  primaryOnTap: () {
                                    Navigator.pop(_);
                                  },
                                );
                              },
                            );
                          },
                          iconPath: Assets.svg.circleFilledInfoIcon.path,
                          noIconColor: true,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                        )
                      : null,
                );
            if (showCheckbox != true) return;
            if (ScreenSizeState.instance.isBigScreen && context.mounted) {
              await showDialog(
                useRootNavigator: false,
                context: context,
                builder: (_) {
                  return Dialog(
                    insetPadding: ScreenSizeState.instance
                        .getDialogInsetPadding(DialogLevel.level1),
                    backgroundColor: Colors.transparent,
                    child: Container(
                      decoration: const BoxDecoration(
                        borderRadius: BorderRadius.all(Radius.circular(16)),
                      ),
                      clipBehavior: Clip.hardEdge,
                      child: data(null),
                    ),
                  );
                },
              );
            } else {
              if (!context.mounted) return;
              showMeScrollableModalBottomSheet(
                widgetContext: context,
                bottomSheetLevel: 1,
                backgroundColor: Colors.transparent,
                shape: const RoundedRectangleBorder(),
                builder: (_, __) {
                  return data(__);
                },
              );
            }
          },
        );
      },
    );
  }
}
