import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gal/gal.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/common_sharing_model.dart';
import 'package:mevolve/data/enums/document_type.dart';
import 'package:mevolve/data/enums/list_item_add_on_type.dart';
import 'package:mevolve/data/enums/list_item_view_type.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/user/list/my_list_item_sheet_settings.dart';
import 'package:mevolve/data/models/user/view_settings.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/app/widgets/styles/me_text_icon.dart';
import 'package:mevolve/features/lists/cubit/copy_move_list/copy_move_list_cubit.dart';
import 'package:mevolve/features/lists/cubit/my_list/list_operations.dart';
import 'package:mevolve/features/lists/functions/list_to_image.dart';
import 'package:mevolve/features/lists/functions/list_to_text.dart';
import 'package:mevolve/features/lists/widgets/list_item_sheet/send_list_widget.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/functions/list_option_functions.dart';
import 'package:mevolve/features/lists/widgets/list_option_sheet/sheets/share_sheet.dart';
import 'package:mevolve/features/tasks_providers/lists/main_lists_cubit.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/me_checkbox_list_tile.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_list_tile.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/features/widgets/tab_kebab_widgets.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/task_component_handler.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:share_plus/share_plus.dart';

/// Helper function to get share position from button context with fallback
Rect getSharePositionWithFallback(BuildContext? buttonContext) {
  try {
    if (buttonContext != null) {
      final box = buttonContext.findRenderObject() as RenderBox?;
      if (box != null && box.hasSize) {
        final position = box.localToGlobal(Offset.zero);
        final size = box.size;
        if (size.width > 0 && size.height > 0) {
          return position & size;
        }
      }
    }
  } catch (e) {
    // Silently handle positioning errors
  }
  // Fallback to a safe position for iPad iOS 17 compatibility
  return const Rect.fromLTWH(500, 600, 100, 100);
}

enum ListOptionTypes {
  // update,
  delete,
  listCloneDone;

  MeString toMeString() {
    switch (this) {
      case ListOptionTypes.delete:
        return MeTranslations
            .instance.bottomSheet_listItemsKebabMenu_deleteList;
      case ListOptionTypes.listCloneDone:
        return MeTranslations.instance.bottomSheet_listItemsKebabMenu_cloneList;
    }
  }
}

class ListItemSheetViewSetting extends StatelessWidget {
  const ListItemSheetViewSetting({
    super.key,
    required this.isMyList,
    this.isPublicList = false,
  });

  final bool isMyList;
  final bool isPublicList;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return SafeArea(
      child: Container(
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(
              SizeConstants.bottomSheetBorderRadius,
            ),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            DialogAndBottomSheetAppBar(
              title: MeTranslations
                  .instance.screen_common_kebabMenuViewConfiguration,
            ),
            BlocBuilder<AppBloc, AppState>(
              buildWhen: (previous, current) =>
                  previous.viewSettings?.listSettings.myListItemSheetSettings !=
                      current
                          .viewSettings?.listSettings.myListItemSheetSettings ||
                  previous.viewSettings?.listSettings.sharedListItemSettings !=
                      current
                          .viewSettings?.listSettings.sharedListItemSettings ||
                  previous.viewSettings?.listSettings
                          .publicListItemSheetSettings !=
                      current.viewSettings?.listSettings
                          .publicListItemSheetSettings,
              builder: (context, state) {
                ViewSettings? viewSettings = state.viewSettings;
                ListItemSheetSettings myListItemSheetSettings =
                    viewSettings!.listSettings.myListItemSheetSettings;
                ListItemSheetSettings sharedListItemSettings =
                    viewSettings.listSettings.sharedListItemSettings;
                ListItemSheetSettings publicListItemSheetSettings =
                    viewSettings.listSettings.publicListItemSheetSettings;

                bool isDescriptionEnable = isMyList
                    ? myListItemSheetSettings.showDescription
                    : isPublicList
                        ? publicListItemSheetSettings.showDescription
                        : sharedListItemSettings.showDescription;
                bool isLastUpdatedByEnabled = isMyList
                    ? myListItemSheetSettings.showLastUpdatedBy
                    : isPublicList
                        ? false
                        : sharedListItemSettings.showLastUpdatedBy;
                bool isLastUpdatedAtEnabled = isMyList
                    ? myListItemSheetSettings.showLastUpdatedAt
                    : isPublicList
                        ? false
                        : sharedListItemSettings.showLastUpdatedAt;

                return Column(
                  children: [
                    MeCheckBoxListTile(
                      title: MeTextIcon(
                        text: MeTranslations.instance.screen_common_description,
                        meFontStyle: MeFontStyle.D8,
                        iconPath: Assets.svg.icLines.path,
                        iconColor: colorScheme.color35,
                        iconContainerSize: const SizedBox(
                          width: 17,
                          height: 14.77,
                        ),
                      ),
                      value: isDescriptionEnable,
                      onChanged: (value) {
                        context.read<AppBloc>().updateViewSettings(
                              viewSettings: viewSettings.copyWith(
                                listSettings:
                                    viewSettings.listSettings.copyWith(
                                  myListItemSheetSettings: isMyList
                                      ? myListItemSheetSettings.copyWith(
                                          showDescription: value,
                                        )
                                      : null,
                                  sharedListItemSettings:
                                      !isMyList && !isPublicList
                                          ? sharedListItemSettings.copyWith(
                                              showDescription: value,
                                            )
                                          : null,
                                  publicListItemSheetSettings: isPublicList
                                      ? publicListItemSheetSettings.copyWith(
                                          showDescription: value,
                                        )
                                      : null,
                                ),
                              ),
                            );
                      },
                    ),
                    if (!isPublicList) ...[
                      const SizedBox(height: 0.5),
                      MeCheckBoxListTile(
                        title: MeTextIcon(
                          text: MeTranslations.instance
                              .bottomSheet_viewConfiguration_lastUpdateBy,
                          meFontStyle: MeFontStyle.D8,
                          iconPath: Assets.svg.pencil.path,
                          iconColor: colorScheme.color35,
                          iconSize: const SizedBox(
                            width: 15,
                            height: 14.98,
                          ),
                          iconContainerSize: const SizedBox(
                            width: 18,
                            height: 18,
                          ),
                        ),
                        value: isLastUpdatedByEnabled,
                        onChanged: (value) {
                          context.read<AppBloc>().updateViewSettings(
                                viewSettings: viewSettings.copyWith(
                                  listSettings:
                                      viewSettings.listSettings.copyWith(
                                    myListItemSheetSettings: isMyList
                                        ? myListItemSheetSettings.copyWith(
                                            showLastUpdatedBy: value,
                                          )
                                        : null,
                                    sharedListItemSettings: !isMyList
                                        ? sharedListItemSettings.copyWith(
                                            showLastUpdatedBy: value,
                                          )
                                        : null,
                                  ),
                                ),
                              );
                        },
                      ),
                    ],
                    if (!isPublicList) ...[
                      const SizedBox(height: 0.5),
                      MeCheckBoxListTile(
                        title: MeTextIcon(
                          text: MeTranslations.instance
                              .bottomSheet_viewConfiguration_lastUpdateTime,
                          meFontStyle: MeFontStyle.D8,
                          iconPath: Assets.svg.clockIcon.path,
                          iconColor: colorScheme.color35,
                          iconContainerSize: const SizedBox(
                            width: 17,
                            height: 14.77,
                          ),
                        ),
                        value: isLastUpdatedAtEnabled,
                        onChanged: (value) {
                          context.read<AppBloc>().updateViewSettings(
                                viewSettings: viewSettings.copyWith(
                                  listSettings:
                                      viewSettings.listSettings.copyWith(
                                    myListItemSheetSettings: isMyList
                                        ? myListItemSheetSettings.copyWith(
                                            showLastUpdatedAt: value,
                                          )
                                        : null,
                                    sharedListItemSettings: !isMyList
                                        ? sharedListItemSettings.copyWith(
                                            showLastUpdatedAt: value,
                                          )
                                        : null,
                                  ),
                                ),
                              );
                        },
                      ),
                    ],
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}

class MePopupMenuItem extends PopupMenuItem<String> {
  MePopupMenuItem({
    super.key,
    required String value,
    required MeString title,
    bool? isActive,
    Widget? leadingWidget,
    bool enabled = true,
    VoidCallback? onTap,
  }) : super(
          onTap: onTap,
          value: value,
          enabled: enabled,
          padding: EdgeInsets.zero,
          // Customize height as needed
          child: Builder(
            builder: (context) {
              return GestureDetector(
                onTap: onTap,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  constraints: const BoxConstraints(
                    minWidth:
                        118, // total minWidth = 150, including 32px padding
                  ),
                  child: Row(
                    children: [
                      if (leadingWidget != null) ...[
                        leadingWidget,
                        const SizedBox(width: 12),
                      ],
                      MeText(
                        text: title,
                        meFontStyle: MeFontStyle.F8,
                      ),
                      const Spacer(),
                      if (isActive != null)
                        Container(
                          width: 16.2,
                          height: 16.2,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            border: Border.all(
                              color: isActive == true
                                  ? Theme.of(context)
                                      .extension<MeColorScheme>()!
                                      .color35
                                  : Theme.of(context)
                                      .extension<MeColorScheme>()!
                                      .color7,
                              width: 1.5, // Reduced from 2
                            ),
                          ),
                          child: isActive == true
                              ? Container(
                                  margin: const EdgeInsets.all(3.5),
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: Theme.of(context)
                                        .extension<MeColorScheme>()!
                                        .color35,
                                  ),
                                )
                              : null,
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
}

class MePopupMenuDivider extends PopupMenuEntry<String> {
  const MePopupMenuDivider({super.key});

  @override
  double get height => 1.0;

  @override
  bool represents(String? value) => false;

  @override
  State<MePopupMenuDivider> createState() => _MePopupMenuDividerState();
}

class _MePopupMenuDividerState extends State<MePopupMenuDivider> {
  @override
  Widget build(BuildContext context) {
    return Divider(
      height: 1,
      thickness: 1,
      color: Theme.of(context).extension<MeColorScheme>()!.color6,
    );
  }
}

class ListActionsSheet extends StatelessWidget {
  const ListActionsSheet({
    Key? key,
    required this.listData,
    required this.listCubit,
    required this.copyMoveListCubit,
  }) : super(key: key);

  final ListData listData;
  final ListOperations listCubit;
  final CopyMoveListCubit copyMoveListCubit;

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: copyMoveListCubit,
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            DialogAndBottomSheetAppBar(
              title: MeTranslations.instance.bottomSheet_actions_titleTopBar,
            ),
            ListView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                ListMoveItemsTile(listData: listData),
                const SizedBox(height: 1),
                CloneListTile(
                  listData: listData,
                  listCubit: listCubit,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}

class ListManageSheet extends StatelessWidget {
  const ListManageSheet({
    Key? key,
    required this.listId,
    required this.listCubit,
    required this.listData,
  }) : super(key: key);

  final String listId;
  final ListOperations listCubit;
  final ListData listData;

  @override
  Widget build(BuildContext context) {
    ViewSettings? userData =
        context.select((AppBloc cubit) => cubit.state.viewSettings);
    bool isOwner = listData.uid == userData!.uid;

    ListItemSheetSettings myListItemSheetSettings =
        userData.listSettings.myListItemSheetSettings;
    ListItemSheetSettings sharedListItemSettings =
        userData.listSettings.sharedListItemSettings;

    bool isDescriptionEnabled = isOwner
        ? myListItemSheetSettings.showDescription
        : sharedListItemSettings.showDescription;
    bool isLastUpdatedByEnabled = isOwner
        ? myListItemSheetSettings.showLastUpdatedBy
        : sharedListItemSettings.showLastUpdatedBy;
    bool isLastUpdatedAtEnabled = isOwner
        ? myListItemSheetSettings.showLastUpdatedAt
        : sharedListItemSettings.showLastUpdatedAt;

    MeString getViewSettingText() {
      String text = '';
      if (isDescriptionEnabled) {
        text += MeTranslations.instance.screen_common_description.text;
      }
      if (isLastUpdatedByEnabled) {
        text += text.isEmpty
            ? MeTranslations
                .instance.bottomSheet_viewConfiguration_lastUpdateBy.text
            : ', ${MeTranslations.instance.bottomSheet_viewConfiguration_lastUpdateBy.text}';
      }
      if (isLastUpdatedAtEnabled) {
        text += text.isEmpty
            ? MeTranslations
                .instance.bottomSheet_viewConfiguration_lastUpdateTime.text
            : ', ${MeTranslations.instance.bottomSheet_viewConfiguration_lastUpdateTime.text}';
      }
      if (text.isEmpty) {
        text = 'None';
      }
      return MeString(text);
    }

    return BlocBuilder<MainListsCubit, MainListsState>(
      builder: (context, state) {
        ListData? currentListData = state.taskIdsToTasks[listId];
        if (currentListData == null) {
          return const SizedBox();
        }

        String hashedEmail = hashData(getCurrentUserEmail()!);
        bool hasEditAccess =
            currentListData.members.membersConfig[hashedEmail]?.role ==
                MemberRole.editor;

        return SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              DialogAndBottomSheetAppBar(
                title:
                    MeTranslations.instance.bottomSheet_manageList_titleTopBar,
              ),
              ListView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  // List Info Tile
                  if (isOwner) ...[
                    MeListTile(
                      title: MeText(
                        text: MeTranslations
                            .instance.bottomSheet_listItemsKebabMenu_listinfo,
                        meFontStyle: MeFontStyle.D8,
                      ),
                      onTap: () {
                        ListOptionFunctions().update(
                          currentListData,
                          context,
                        );
                      },
                    ),
                    const SizedBox(height: 1),
                  ],

                  // Add-on Tile
                  if (isOwner || hasEditAccess) ...[
                    GroupBy<ListItemAddOnType>(
                      selectedPopMenuItem: currentListData.addOnType,
                      listTileCrossAxisAlignment: CrossAxisAlignment.start,
                      title: MeTranslations
                          .instance.bottomSheet_manageList_customise,
                      offsetDy: -125,
                      subTitle: MeTranslations
                          .instance.bottomSheet_listItemsKebabMenu_addOnContent,
                      popMenuValues: ListItemAddOnType.values,
                      isDisabled: false,
                      onPopMenuItemSelected: (ListItemAddOnType val) async {
                        bool isNoneSelected =
                            currentListData.addOnType == ListItemAddOnType.none;
                        if (isNoneSelected) {
                          context.read<ListOperations>().updateList(
                                list: currentListData.copyWith(
                                  addOnType: val,
                                ),
                              );
                          return;
                        }
                        ListItemAddOnType currentAddOnType =
                            currentListData.addOnType;
                        bool? canSwitch = await showDialog<bool>(
                          context: context,
                          builder: (BuildContext _) {
                            return MeDialog(
                              title: val.toSwitchString(),
                              description: val.dialogDescription(
                                currentAddOnType,
                              ),
                              primaryText: MeTranslations
                                  .instance.overlay_listSwitch_switch,
                              primaryOnTap: () {
                                Navigator.pop(_, true);
                              },
                              secondaryText: MeTranslations
                                  .instance.screen_common_buttonCancel,
                              secondaryOnTap: () {
                                Navigator.pop(_, false);
                              },
                            );
                          },
                        );
                        if (canSwitch == true && context.mounted) {
                          context.read<ListOperations>().updateList(
                                list: currentListData.copyWith(
                                  addOnType: val,
                                ),
                              );
                        }
                      },
                    ),
                    const SizedBox(height: 1),
                  ],

                  // View Settings (Show) Tile
                  BlocBuilder<AppBloc, AppState>(
                    builder: (context, appState) {
                      return ViewSettingsWidget<ViewType>(
                        offset: -125,
                        subtitleText: getViewSettingText(),
                        leadingOnTap: () {
                          showMeScrollableModalBottomSheet(
                            bottomSheetLevel: 1,
                            builder: (_, __) {
                              return ListItemSheetViewSetting(
                                isMyList: isOwner,
                              );
                            },
                          );
                        },
                        selectedTrailingPopMenuItem: isOwner
                            ? appState.viewSettings!.listSettings
                                .myListItemSheetSettings.viewType!
                            : appState.viewSettings!.listSettings
                                .sharedListItemSettings.viewType!,
                        disableLeadingIfTrailingSelectedAs: ViewType.getValues
                            .where((element) => element != ViewType.custom)
                            .toList(),
                        trailingPopMenuValues: ViewType.getValues,
                        onTrailingPopMenuItemSelected: (ViewType viewType) {
                          ListItemSheetSettings myListItemSettings;
                          if (viewType == ViewType.compact) {
                            myListItemSettings = isOwner
                                ? appState.viewSettings!.listSettings
                                    .myListItemSheetSettings
                                    .copyWith(
                                    showDescription: false,
                                    showLastUpdatedBy: false,
                                    showLastUpdatedAt: false,
                                    viewType: viewType,
                                  )
                                : appState.viewSettings!.listSettings
                                    .sharedListItemSettings
                                    .copyWith(
                                    showDescription: false,
                                    showLastUpdatedAt: false,
                                    showLastUpdatedBy: false,
                                    viewType: viewType,
                                  );
                          } else {
                            myListItemSettings = isOwner
                                ? appState.viewSettings!.listSettings
                                    .myListItemSheetSettings
                                    .copyWith(
                                    viewType: viewType,
                                    showDescription: true,
                                    showLastUpdatedAt: false,
                                    showLastUpdatedBy: false,
                                  )
                                : appState.viewSettings!.listSettings
                                    .sharedListItemSettings
                                    .copyWith(
                                    viewType: viewType,
                                    showDescription: true,
                                    showLastUpdatedAt: false,
                                    showLastUpdatedBy: false,
                                  );
                          }
                          if (isOwner) {
                            context.read<AppBloc>().updateViewSettings(
                                  viewSettings: appState.viewSettings!.copyWith(
                                    listSettings: appState
                                        .viewSettings!.listSettings
                                        .copyWith(
                                      myListItemSheetSettings:
                                          myListItemSettings,
                                    ),
                                  ),
                                );
                          } else {
                            context.read<AppBloc>().updateViewSettings(
                                  viewSettings: appState.viewSettings!.copyWith(
                                    listSettings: appState
                                        .viewSettings!.listSettings
                                        .copyWith(
                                      sharedListItemSettings:
                                          myListItemSettings,
                                    ),
                                  ),
                                );
                          }
                        },
                      );
                    },
                  ),
                  const SizedBox(height: 1),

                  // Delete List Tile
                  if (isOwner) ...[
                    InkWell(
                      splashFactory: InkRipple.splashFactory,
                      onTap: () async {
                        // pop
                        Navigator.of(context).pop();
                        TaskComponentHandler.showMeDeleteDialog(
                          context: context,
                          docType: DocumentType.list,
                          title: MeString.empty,
                          description: MeTranslations
                              .instance.overlay_listDelete_content,
                          isNewTask: false,
                          isChangeMade: false,
                          list: currentListData,
                          handleOnlyDelete: false,
                        );
                      },
                      child: Container(
                        width: double.infinity,
                        color: context.meColorScheme.color5,
                        padding: const EdgeInsets.all(16),
                        child: MeText(
                          text: MeTranslations.instance
                              .bottomSheet_listItemsKebabMenu_deleteList,
                          meFontStyle: MeFontStyle.C11,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ],
          ),
        );
      },
    );
  }
}

class ListShareSheet extends StatelessWidget {
  const ListShareSheet({
    Key? key,
    required this.listData,
    required this.listCubit,
  }) : super(key: key);

  final ListData listData;
  final ListOperations listCubit;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<MainListsCubit, MainListsState>(
      builder: (context, state) {
        ListData? currentListData = state.taskIdsToTasks[listData.id];

        if (currentListData == null) {
          return const SizedBox();
        }

        return ShareSheet(
          itemId: currentListData.id,
          copyAsTextTitle: MeTranslations.instance.screen_common_copyText,
          downloadAsImageTitle:
              MeTranslations.instance.screen_common_downloadAsImage,
          shareTitle: MeTranslations.instance.screen_common_share,
          shareCount: currentListData.members.memberHashedEmails.length,
          userRole: currentListData.members.getUserRoleType,
          trackAction: TrackAction.myListsItem,
          showCheckBoxNotifier: showCheckBoxNotifier,
          onCopyAsText: () async {
            return listToText(currentListData);
          },
          onDownloadAsImage: () async {
            await Future.delayed(const Duration(milliseconds: 500));
            if (context.mounted) {
              String path = await listToImage(
                currentListData,
                context,
              );
              await Gal.putImage(
                path,
                album: 'Mevolve',
              );
              if (context.mounted) {
                showCustomMessageOverlay(
                  context,
                  MeTranslations.instance.toast_listImageDownload_content,
                  actionText: MeTranslations.instance.screen_imagePreview_view,
                  actionFunction: () {
                    OpenFile.open(path);
                  },
                );
              }
            }
          },
          onShareAsText: () async {
            return listToText(currentListData);
          },
          onShareAsImage: () async {
            return await listToImage(
              currentListData,
              context,
            );
          },
        );
      },
    );
  }
}

class ShareSheet extends StatelessWidget {
  const ShareSheet({
    Key? key,
    required this.itemId,
    required this.copyAsTextTitle,
    required this.downloadAsImageTitle,
    required this.shareTitle,
    required this.shareCount,
    required this.userRole,
    required this.trackAction,
    required this.onCopyAsText,
    required this.onDownloadAsImage,
    required this.onShareAsText,
    required this.onShareAsImage,
    this.showCheckBoxNotifier,
    this.textShareIconPath,
    this.imageShareIconPath,
  }) : super(key: key);

  final String itemId;
  final MeString copyAsTextTitle;
  final MeString downloadAsImageTitle;
  final MeString shareTitle;
  final int shareCount;
  final MemberRole userRole;
  final TrackAction trackAction;
  final ValueNotifier<bool>? showCheckBoxNotifier;
  final String? textShareIconPath;
  final String? imageShareIconPath;

  // Callbacks
  final Future<String> Function() onCopyAsText;
  final Future<void> Function() onDownloadAsImage;
  final Future<String> Function() onShareAsText;
  final Future<String> Function() onShareAsImage;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return SafeArea(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DialogAndBottomSheetAppBar(
            title: MeTranslations.instance.screen_common_share,
          ),
          ListView(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              // Copy Text Tile
              MeListTile(
                title: MeText(
                  text: copyAsTextTitle,
                  meFontStyle: MeFontStyle.D8,
                ),
                onTap: () async {
                  EventFormation().sendFeatureSharedEvent(
                    trackAction: trackAction,
                    sharingStatus: ShareStatus.public,
                    sharedCount: shareCount,
                    shareMethod: ShareMethod.text,
                    userRole:
                        UserRoleOfSharer.getUserRoleByMemberRole(userRole),
                  );

                  final text = await onCopyAsText();
                  await Clipboard.setData(
                    ClipboardData(text: text),
                  );
                  if (context.mounted) {
                    showCustomMessageOverlay(
                      context,
                      MeTranslations.instance.toast_listCopied_content,
                    );
                  }
                },
                trailing: MeIcon(
                  iconPath: Assets.svg.copyIcon.path,
                  iconColor: colorScheme.color35,
                  iconSize: const SizedBox(height: 20, width: 20),
                  iconContainerSize: const SizedBox(height: 24, width: 24),
                ),
              ),
              const SizedBox(height: 1),

              // Download as Image Tile
              MeListTile(
                title: MeText(
                  text: downloadAsImageTitle,
                  meFontStyle: MeFontStyle.D8,
                ),
                onTap: () async {
                  EventFormation().sendFeatureSharedEvent(
                    trackAction: trackAction,
                    sharingStatus: ShareStatus.public,
                    sharedCount: shareCount,
                    shareMethod: ShareMethod.image,
                    userRole:
                        UserRoleOfSharer.getUserRoleByMemberRole(userRole),
                  );

                  showCheckBoxNotifier?.value = true;
                  UtilityMethods.showMeLoaderDialog(context);

                  try {
                    await onDownloadAsImage();
                  } finally {
                    if (context.mounted) {
                      Navigator.of(context).pop();
                    }
                  }
                },
                trailing: MeIcon(
                  iconPath: Assets.svg.icDownload.path,
                  iconColor: colorScheme.color35,
                  iconSize: const SizedBox(height: 16.7, width: 15),
                  iconContainerSize: const SizedBox(height: 24, width: 24),
                ),
              ),
              const SizedBox(height: 1),

              // Share Tile with two trailing icons
              MeListTile(
                title: MeText(
                  text: shareTitle,
                  meFontStyle: MeFontStyle.D8,
                ),
                padding: const EdgeInsets.only(
                  left: 16,
                  right: 8,
                  top: 8,
                  bottom: 8,
                ),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Share as Text Icon
                    Builder(
                      builder: (buttonContext) => MeIconButton(
                        iconPath: textShareIconPath ?? Assets.svg.text.path,
                        iconColor: colorScheme.color35,
                        iconSize: const SizedBox(height: 16, width: 16),
                        iconContainerSize:
                            const SizedBox(height: 24, width: 24),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 8,
                        ),
                        onPressed: () async {
                          EventFormation().sendFeatureSharedEvent(
                            trackAction: trackAction,
                            sharingStatus: ShareStatus.public,
                            sharedCount: shareCount,
                            shareMethod: ShareMethod.text,
                            userRole: UserRoleOfSharer.getUserRoleByMemberRole(
                              userRole,
                            ),
                          );

                          final text = await onShareAsText();
                          if (!buttonContext.mounted) return;
                          final shareRect =
                              getSharePositionWithFallback(buttonContext);

                          await Share.share(
                            text,
                            sharePositionOrigin: shareRect,
                          );
                        },
                      ),
                    ),
                    const SizedBox(width: 8),

                    // Share as Image Icon
                    Builder(
                      builder: (buttonContext) => MeIconButton(
                        iconPath:
                            imageShareIconPath ?? Assets.svg.imageGallery.path,
                        iconColor: colorScheme.color35,
                        iconSize: const SizedBox(height: 16, width: 16),
                        iconContainerSize:
                            const SizedBox(height: 24, width: 24),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                          vertical: 8,
                        ),
                        onPressed: () async {
                          EventFormation().sendFeatureSharedEvent(
                            trackAction: trackAction,
                            sharingStatus: ShareStatus.public,
                            sharedCount: shareCount,
                            shareMethod: ShareMethod.image,
                            userRole: UserRoleOfSharer.getUserRoleByMemberRole(
                              userRole,
                            ),
                          );

                          showCheckBoxNotifier?.value = true;
                          UtilityMethods.showMeLoaderDialog(context);

                          try {
                            final imagePath = await onShareAsImage();
                            if (!buttonContext.mounted) return;
                            final shareRect =
                                getSharePositionWithFallback(buttonContext);

                            await Share.shareXFiles(
                              [XFile(imagePath)],
                              text: 'Mevolve.app',
                              sharePositionOrigin: shareRect,
                            );
                          } finally {
                            if (context.mounted) {
                              Navigator.of(context).pop();
                            }
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
