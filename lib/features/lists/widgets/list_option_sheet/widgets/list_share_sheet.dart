import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:gal/gal.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/widgets/tab_kebab_widgets.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:open_file_plus/open_file_plus.dart';
import 'package:share_plus/share_plus.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/data/enums/list_item_add_on_type.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/lists/functions/list_to_image.dart';
import 'package:mevolve/features/lists/functions/list_to_text.dart';
import 'package:mevolve/features/lists/widgets/list_item_sheet/send_list_widget.dart';
import 'package:mevolve/features/widgets/me_list_tile.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';

enum _TextConfig {
  text;

  MeString toMeString() {
    switch (this) {
      case _TextConfig.text:
        return MeTranslations.instance.dropdown_shareCopyAs_text;
    }
  }

  String iconPath() {
    switch (this) {
      case _TextConfig.text:
        return Assets.svg.text.path;
    }
  }
}

enum _DownloadConfig {
  image;

  MeString toMeString() {
    switch (this) {
      case _DownloadConfig.image:
        return MeTranslations.instance.dropdown_shareDownloadAs_image;
    }
  }

  String iconPath() {
    switch (this) {
      case _DownloadConfig.image:
        return Assets.svg.imageGallery.path;
    }
  }
}

enum _ShareConfig {
  text,
  image;

  MeString toMeString() {
    switch (this) {
      case _ShareConfig.text:
        return MeTranslations.instance.dropdown_shareAs_text;
      case _ShareConfig.image:
        return MeTranslations.instance.dropdown_shareAs_image;
    }
  }

  String iconPath() {
    switch (this) {
      case _ShareConfig.text:
        return Assets.svg.text.path;
      case _ShareConfig.image:
        return Assets.svg.imageGallery.path;
    }
  }
}

class ListOptionShareWidget extends StatefulWidget {
  const ListOptionShareWidget({
    super.key,
    required this.listData,
  });

  final ListData listData;

  @override
  State<ListOptionShareWidget> createState() => _ListOptionShareWidgetState();
}

class _ListOptionShareWidgetState extends State<ListOptionShareWidget> {
  @override
  Widget build(BuildContext context) {
    return MeListTile(
      padding: const EdgeInsets.only(
        left: 16,
        right: 4,
      ),
      title: MeText(
        text: MeTranslations.instance.screen_common_share,
        meFontStyle: MeFontStyle.D8,
      ),
      trailing: Row(
        children: [
          CustomPopMenuButton<_TextConfig>(
            offset: const Offset(0, -35),
            selectedItemPopMenuItems: const [],
            hideTrailingWidgets: true,
            enableMultiSelect: false,
            allPopMenuValues: _TextConfig.values,
            position: PopupMenuPosition.over,
            onPopMenuItemTap: (_TextConfig item) async {
              // Send analytics event for share by link
              EventFormation().sendFeatureSharedEvent(
                trackAction: TrackAction.noteAction,
                sharingStatus: ShareStatus.public,
                sharedCount: widget.listData.members.memberHashedEmails.length,
                shareMethod: ShareMethod.text,
                userRole: widget.listData.members.getRoleForAnalytics,
              );

              Clipboard.setData(
                ClipboardData(
                  text: listToText(widget.listData),
                ),
              );
              showCustomMessageOverlay(
                context,
                MeTranslations.instance.toast_listCopied_content,
              );
            },
            child: MeIcon(
              iconPath: Assets.svg.copyIcon.path,
              iconColor: context.meColorScheme.color35,
              iconSize: const SizedBox(height: 20, width: 20),
              iconContainerSize: const SizedBox(height: 24, width: 24),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            ),
          ),
          // const SizedBox(width: 24),
          CustomPopMenuButton<_DownloadConfig>(
            offset: const Offset(0, -35),
            selectedItemPopMenuItems: const [],
            hideTrailingWidgets: true,
            enableMultiSelect: false,
            allPopMenuValues: _DownloadConfig.values,
            position: PopupMenuPosition.over,
            onPopMenuItemTap: (_DownloadConfig item) async {
              // Send analytics event for share by link
              EventFormation().sendFeatureSharedEvent(
                trackAction: TrackAction.noteAction,
                sharingStatus: ShareStatus.public,
                sharedCount: widget.listData.members.memberHashedEmails.length,
                shareMethod: ShareMethod.image,
                userRole: widget.listData.members.getRoleForAnalytics,
              );

              showCheckBoxNotifier.value =
                  widget.listData.addOnType == ListItemAddOnType.checkbox;
              UtilityMethods.showMeLoaderDialog(context);
              await Future.delayed(
                const Duration(milliseconds: 500),
              );
              if (context.mounted) {
                String path = await listToImage(
                  widget.listData,
                  context,
                );
                if (!context.mounted) return;
                Navigator.of(context).pop();
                await Gal.putImage(
                  path,
                  album: 'Mevolve',
                );
                if (context.mounted) {
                  showCustomMessageOverlay(
                    context,
                    MeTranslations.instance.toast_listImageDownload_content,
                    actionText:
                        MeTranslations.instance.screen_imagePreview_view,
                    actionFunction: () {
                      OpenFile.open(path);
                    },
                  );
                }
              }
            },
            child: MeIcon(
              iconPath: Assets.svg.icDownload.path,
              iconColor: context.meColorScheme.color35,
              iconSize: const SizedBox(height: 16, width: 16),
              iconContainerSize: const SizedBox(height: 24, width: 24),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            ),
          ),
          // const SizedBox(width: 24),
          CustomPopMenuButton<_ShareConfig>(
            offset: const Offset(0, -80),
            selectedItemPopMenuItems: const [],
            hideTrailingWidgets: true,
            enableMultiSelect: false,
            allPopMenuValues: _ShareConfig.values,
            position: PopupMenuPosition.over,
            onPopMenuItemTap: (_ShareConfig item) async {
              switch (item) {
                case _ShareConfig.text:
                  // Send analytics event for share by link
                  EventFormation().sendFeatureSharedEvent(
                    trackAction: TrackAction.noteAction,
                    sharingStatus: ShareStatus.public,
                    sharedCount:
                        widget.listData.members.memberHashedEmails.length,
                    shareMethod: ShareMethod.text,
                    userRole: widget.listData.members.getRoleForAnalytics,
                  );

                  Share.share(listToText(widget.listData));
                  break;
                case _ShareConfig.image:
                  // Send analytics event for share by link
                  EventFormation().sendFeatureSharedEvent(
                    trackAction: TrackAction.noteAction,
                    sharingStatus: ShareStatus.public,
                    sharedCount:
                        widget.listData.members.memberHashedEmails.length,
                    shareMethod: ShareMethod.image,
                    userRole: widget.listData.members.getRoleForAnalytics,
                  );

                  showCheckBoxNotifier.value =
                      widget.listData.addOnType == ListItemAddOnType.checkbox;
                  UtilityMethods.showMeLoaderDialog(context);
                  await Future.delayed(
                    const Duration(milliseconds: 500),
                  );
                  if (context.mounted) {
                    final image = await listToImage(
                      widget.listData,
                      context,
                    );
                    if (!context.mounted) return;
                    Navigator.of(context).pop();
                    await Share.shareXFiles(
                      [
                        XFile(image),
                      ],
                      text: 'Mevolve.app',
                    );
                  }
                  break;
              }
            },
            child: MeIcon(
              iconPath: Assets.svg.icShare.path,
              iconColor: context.meColorScheme.color35,
              iconSize: const SizedBox(height: 16, width: 16),
              iconContainerSize: const SizedBox(height: 24, width: 24),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
            ),
          ),
        ],
      ),
    );
  }
}
