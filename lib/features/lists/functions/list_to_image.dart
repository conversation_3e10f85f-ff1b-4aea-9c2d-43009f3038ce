import 'package:mevolve/constants/app_strings.dart';
import 'package:mevolve/features/widgets/empty_state_widget.dart';
import 'package:universal_io/io.dart';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/data/enums/list_item_add_on_type.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/list_item.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/lists/widgets/list_item_sheet/send_list_widget.dart';
import 'package:mevolve/features/widgets/bottom_sheet_appbar.dart';
import 'package:mevolve/features/widgets/me_checkbox.dart';
import 'package:mevolve/features/widgets/me_list_tile.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:path_provider/path_provider.dart';
import 'package:screenshot/screenshot.dart';

class ListImage extends StatefulWidget {
  const ListImage({
    super.key,
    required this.list,
    required this.uid,
    required this.widgetContext,
    required this.listItems,
  });

  final ListData list;
  final List<ListItem> listItems;
  final String uid;
  final BuildContext widgetContext;

  @override
  State<ListImage> createState() => _ListImageState();
}

class _ListImageState extends State<ListImage> {
  @override
  Widget build(BuildContext context) {
    final BuildContext context = widget.widgetContext;
    final colorScheme = context.meColorScheme;
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DialogAndBottomSheetAppBar(
            title: MeString(
              widget.list.title,
            ), //? MeString
            showCloseButton: false,
            showRoundedTop: false,
          ),
          widget.list.description != null && widget.list.description != ''
              ? Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      width: double.infinity,
                      color: context.meColorScheme.color5,
                      padding: const EdgeInsets.all(16),
                      child: MeText(
                        text: MeString(
                          widget.list.description!,
                        ), //? MeString
                        meFontStyle: MeFontStyle.F7,
                        textOverflow: TextOverflow.visible,
                      ),
                    ),
                    Divider(
                      height: 2,
                      thickness: 2,
                      color: context.meColorScheme.color6,
                    ),
                  ],
                )
              : const SizedBox.shrink(),
          Container(
            color: context.meColorScheme.color6,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                widget.listItems.isEmpty
                    ? Column(
                        children: [
                          const SizedBox(height: 60),
                          Center(
                            child: EmptyStateWidget(
                              imagePath:
                                  AppStrings.listOfItemsEmptyIllustration(
                                colorScheme: colorScheme,
                              ),
                              imageTextStyle: MeFontStyle.C8,
                              imageText: MeTranslations.instance
                                  .bottomSheet_listItems_emptyScreenContent,
                            ),
                          ),
                        ],
                      )
                    : ListView.separated(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        separatorBuilder: (_, __) => Divider(
                          height: 1,
                          thickness: 1,
                          color: context.meColorScheme.color6,
                        ),
                        itemBuilder: (_, index) {
                          final listItem = widget.listItems[index];
                          return ValueListenableBuilder<bool>(
                            valueListenable: showCheckBoxNotifier,
                            builder: (context, value, child) {
                              return _SimpleListItemWidget(
                                listItem: listItem,
                                showCheckbox: value,
                                addOnType: widget.list.addOnType,
                              );
                            },
                          );
                        },
                        itemCount: widget.listItems.length,
                      ),
                () {
                  final String name =
                      context.read<AppBloc>().state.userData!.userInfo.name;
                  return Padding(
                    padding: const EdgeInsets.only(
                      top: 60,
                      left: 16,
                      right: 16,
                      bottom: 2,
                    ),
                    child: MeText(
                      text: MeString(
                        '$name, ${DateFormat('dd MMM yyyy').format(DateTime.now())}',
                      ),
                      meFontStyle: MeFontStyle.I7,
                    ),
                  );
                }(),
                Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: MeText(
                    text: MeTranslations
                        .instance.screen_notApplicable_listCreatedWithMevolve,
                    meFontStyle: MeFontStyle.I7,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

Future<String> listToImage(
  ListData listData,
  BuildContext context,
) async {
  try {
    final String uid = context.read<AppBloc>().state.user!.uid;
    final ScreenshotController screenshotController = ScreenshotController();

    // Get theme, media query, and localization data from the current context
    final ThemeData theme = Theme.of(context);
    final MediaQueryData mediaQuery = MediaQuery.of(context);
    final Locale locale = Localizations.localeOf(context);

    // Use captureFromWidget to directly render the widget without needing it in the tree
    final Uint8List imageBytes = await screenshotController.captureFromWidget(
      MediaQuery(
        data: mediaQuery,
        child: Localizations(
          locale: locale,
          delegates: const [
            DefaultMaterialLocalizations.delegate,
            DefaultWidgetsLocalizations.delegate,
          ],
          child: Theme(
            data: theme,
            child: Material(
              child: SizedBox(
                width: 428,
                child: ListImage(
                  list: listData,
                  uid: uid,
                  widgetContext: context,
                  listItems: listData.validListItems,
                ),
              ),
            ),
          ),
        ),
      ),
      pixelRatio: 4.0,
    );
    if (context.mounted == false) {
      return '';
    }
    return await imagePathFromUint8List(listData.id, context, imageBytes);
  } catch (e) {
    return '';
  }
}

Future<String> imagePathFromUint8List(
  String fileName,
  BuildContext context,
  Uint8List png,
) async {
  try {
    // download the image
    final directory = await getApplicationDocumentsDirectory();
    final pathOfImage = await File('${directory.path}/$fileName.png').create();
    final Uint8List bytes = png;
    await pathOfImage.writeAsBytes(bytes);
    return pathOfImage.path;
  } catch (e) {
    return '';
  }
}

class _SimpleListItemWidget extends StatelessWidget {
  const _SimpleListItemWidget({
    required this.listItem,
    required this.showCheckbox,
    required this.addOnType,
  });

  final ListItem listItem;
  final bool showCheckbox;
  final ListItemAddOnType addOnType;

  @override
  Widget build(BuildContext context) {
    final colorScheme = context.meColorScheme;

    return MeListTile(
      padding: const EdgeInsets.only(top: 16, bottom: 16, left: 16),
      onTap: () {},
      tileColor: colorScheme.color5,
      title: MeText(
        text: MeString(listItem.item),
        meFontStyle: MeFontStyle.C8,
        textOverflow: null,
      ),
      subtitlePadding: 0,
      subtitle: const SizedBox.shrink(),
      trailingLeftPadding: 0,
      listTileCrossAxisAlignment: CrossAxisAlignment.center,
      trailing: Padding(
        padding: EdgeInsets.zero,
        child: () {
          if (addOnType == ListItemAddOnType.customText) {
            // Show custom text
            String customText = listItem.customText == null ||
                    listItem.customText!.isEmpty
                ? '---'
                : listItem.customText!;
            return Padding(
              padding: const EdgeInsets.only(right: 16, left: 16),
              child: MeText(
                text: MeString(customText),
                meFontStyle: MeFontStyle.D7,
                textOverflow: TextOverflow.visible,
              ),
            );
          } else if (addOnType == ListItemAddOnType.checkbox && showCheckbox) {
            // Show checkbox
            return Container(
              padding: const EdgeInsets.only(
                right: 16,
                left: 16,
              ),
              child: MeCheckBox(
                value: listItem.done,
                isDisabled: false,
                isDue: false,
                borderWidth: 2.0,
              ),
            );
          } else {
            // Show nothing (None mode or checkbox disabled)
            return const SizedBox(
              height: 40,
              width: 40,
            );
          }
        }(),
      ),
    );
  }
}
