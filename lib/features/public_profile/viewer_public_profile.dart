import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/constants/app_strings.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/data/enums/common_sharing_model.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/public/public_list_metadata.dart';
import 'package:mevolve/data/models/public/public_note_metadata.dart';
import 'package:mevolve/features/app/bloc/cubit/public_user_cubit.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/login/view/login_page.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/publicTasks/handle_public_link.dart';
import 'package:mevolve/features/publicTasks/public_data_service.dart';
import 'package:mevolve/features/publicTasks/widgets/rotating_sync_icon.dart';
import 'package:mevolve/features/public_profile/model/public_viewer_user.dart';
import 'package:mevolve/features/public_profile/owner_public_profile.dart';
import 'package:mevolve/features/public_profile/widgets/follow_unfollow_button.dart';
import 'package:mevolve/features/public_profile/widgets/followers_followings_list_widget.dart';
import 'package:mevolve/features/public_profile/widgets/public_count_container.dart';
import 'package:mevolve/features/public_profile/widgets/public_list_tile.dart';
import 'package:mevolve/features/public_profile/widgets/public_note_tile.dart';
import 'package:mevolve/features/widgets/empty_state_widget.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_title_bar.dart';
import 'package:mevolve/features/widgets/share_link_qr_sheet.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';

class ViewerPublicProfile extends StatefulWidget {
  const ViewerPublicProfile({
    super.key,
    required this.profileUser,
    this.appUserUid,
    this.bottomSheetLevel,
  });

  final PublicViewerUser profileUser;
  final String? appUserUid;
  final double? bottomSheetLevel;

  @override
  State<ViewerPublicProfile> createState() => _ViewerPublicProfileState();
}

class _ViewerPublicProfileState extends State<ViewerPublicProfile> {
  bool _isRefreshing = false;
  late PublicViewerUser _currentUser;
  late String _profileUserId;

  @override
  void initState() {
    super.initState();
    // Initialize refresh state
    _currentUser = widget.profileUser;
    _profileUserId =
        widget.profileUser.uid; // Use profile user's uid for refresh

    // Send analytics screen view event
    EventFormation()
        .sendPublicScreenViewedEvent(trackAction: TrackAction.publicProfile);
  }

  /// Refresh the user data
  Future<void> _refreshUser() async {
    if (_isRefreshing) return;

    setState(() {
      _isRefreshing = true;
    });

    try {
      // Check internet first
      if (!await HandlePublicLink.checkInternetConnection(context)) {
        return;
      }

      if (_profileUserId.isEmpty) {
        if (mounted) {
          await HandlePublicLink.showLoadFailedDialog(
            context,
            onRetry: () => _refreshUser(),
          );
        }
        return;
      }

      // Refresh the data using the user's uid
      final refreshedUser =
          await PublicDataService().getCompleteUser(_profileUserId);

      if (refreshedUser != null && mounted) {
        setState(() {
          _currentUser = refreshedUser;
        });
      } else {
        if (mounted) {
          await HandlePublicLink.showLoadFailedDialog(
            context,
            onRetry: () => _refreshUser(),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        await HandlePublicLink.showLoadFailedDialog(
          context,
          onRetry: () => _refreshUser(),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isRefreshing = false;
        });
      }
    }
  }

  Widget titleWidget() {
    final String? currentUserName =
        FirebaseAuth.instance.currentUser?.displayName;
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    return MeTitleBar(
      title: MeTranslations.instance.screen_account_profileHeader,
      titleButton: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Refresh button with rotating animation like PublicListView
          Visibility(
            visible: false,
            child: MeIconButton(
              isCircularIconButton: true,
              iconPath: _isRefreshing ? null : Assets.svg.icResync.path,
              icon: _isRefreshing
                  ? RotatingSyncIcon(
                      iconColor: colorScheme.color8,
                      iconSize: const SizedBox(
                        width: SizeConstants.iconButtonIconContainerSize,
                        height: SizeConstants.iconButtonIconContainerSize,
                      ),
                    )
                  : null,
              iconColor: colorScheme.color12,
              iconSize: const SizedBox(
                height: 14,
                width: 14,
              ),
              iconContainerSize: const SizedBox(
                width: SizeConstants.iconButtonIconContainerSize,
                height: SizeConstants.iconButtonIconContainerSize,
              ),
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 12,
              ),
              onPressed: () => _refreshUser(),
            ),
          ),
          MeIconButton(
            onPressed: () {
              // Send analytics CTA click event for profile share
              EventFormation().sendPublicProfileCtaClicksEvent(
                trackAction: TrackAction.publicProfileShare,
              );

              showMeScrollableModalBottomSheet(
                widgetContext: context,
                bottomSheetLevel: 1,
                useSafeArea: true,
                builder: (_, __) {
                  final String shareableLink =
                      '${AppConstant.sharingSiteBaseUrl}/user/${_currentUser.mevolveId}';
                  return ShareQrAndLinkSheet(
                    args: ShareLinkQrData(
                      link: shareableLink,
                      title: '',
                      id: _currentUser.mevolveId,
                      ownerName: currentUserName ?? '',
                      collection: FirebaseDocCollectionType.publicUsers,
                      secondaryName: _currentUser.name,
                    ),
                    shareableLink: shareableLink,
                    sharerUserRole: MemberRole.viewer,
                    shareCount: 0,
                  );
                },
              );
            },
            iconPath: Assets.svg.icShare.path,
            iconSize: const SizedBox(
              height: 17,
            ),
            iconColor: colorScheme.color12,
            padding: const EdgeInsets.symmetric(
              horizontal: 12,
              vertical: 12,
            ),
          ),
        ],
      ),
      onCloseClick: () {
        Navigator.of(context).pop();
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final isAuthenticated = FirebaseAuth.instance.currentUser != null;
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    final user = _currentUser;

    List<PublicListMetadata> publicListsMetadata = user.publicListsMetadata;
    List<PublicNoteMetadata> publicNotesMetadata = user.publicNotesMetadata;
    List<dynamic> listAndNoteMetaData = [
      ...publicListsMetadata,
      ...publicNotesMetadata,
    ];
    listAndNoteMetaData.sort(
      (a, b) => b.updatedAt.compareTo(a.updatedAt),
    );

    return SafeArea(
      child: Column(
        children: [
          titleWidget(),
          Expanded(
            child: CustomScrollView(
              slivers: [
                SliverToBoxAdapter(
                  child: Padding(
                    padding: const EdgeInsets.only(
                      top: 24,
                      left: 16,
                      right: 16,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                CircleAvatar(
                                  radius: 25,
                                  backgroundColor: colorScheme.color1,
                                  child: Center(
                                    child: MeText(
                                      text: MeString.fromVariable(
                                        user.name.split('')[0].toUpperCase(),
                                      ),
                                      meFontStyle: MeFontStyle.R12,
                                    ),
                                  ),
                                ),
                                const SizedBox(height: 12),
                                MeText(
                                  text: MeString.fromVariable(user.name),
                                  meFontStyle: MeFontStyle.A8,
                                ),
                                const SizedBox(height: 8),
                                MeText(
                                  text: MeString.fromVariable(
                                    '@${user.mevolveId}',
                                  ),
                                  meFontStyle: MeFontStyle.F8,
                                ),
                              ],
                            ),
                            const Spacer(),
                            Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        onTap: () {
                                          if (isAuthenticated == false) {
                                            return;
                                          }
                                          openFollowerFollowingSheet(
                                            isFollowers: true,
                                            userId: user.uid,
                                            // Profile user's uid for followers
                                            ctx: context,
                                            sheetLevel: widget.bottomSheetLevel,
                                            followersCount: user.followers,
                                            followingCount: user.following,
                                          );
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 8.0,
                                            horizontal: 4.0,
                                          ),
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              MeText(
                                                text: MeString.fromVariable(
                                                  user.followers.toString(),
                                                ),
                                                meFontStyle: MeFontStyle.C7,
                                              ),
                                              const SizedBox(height: 8),
                                              MeText(
                                                text: MeTranslations.instance
                                                    .bottomSheet_publicProfile_followers,
                                                meFontStyle: MeFontStyle.I7,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 24),
                                    Material(
                                      color: Colors.transparent,
                                      child: InkWell(
                                        borderRadius:
                                            BorderRadius.circular(8.0),
                                        onTap: () {
                                          if (isAuthenticated == false) {
                                            return;
                                          }
                                          openFollowerFollowingSheet(
                                            isFollowers: false,
                                            userId: user.uid,
                                            // Profile user's uid for following
                                            ctx: context,
                                            sheetLevel: widget.bottomSheetLevel,
                                            followersCount: user.followers,
                                            followingCount: user.following,
                                          );
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 8.0,
                                            horizontal: 4.0,
                                          ),
                                          child: Column(
                                            mainAxisAlignment:
                                                MainAxisAlignment.start,
                                            children: [
                                              MeText(
                                                text: MeString.fromVariable(
                                                  user.following.toString(),
                                                ),
                                                meFontStyle: MeFontStyle.C7,
                                              ),
                                              const SizedBox(height: 8),
                                              MeText(
                                                text: MeTranslations.instance
                                                    .bottomSheet_publicProfile_following,
                                                meFontStyle: MeFontStyle.I7,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                                if (FirebaseAuth.instance.currentUser?.uid ==
                                    null) ...[
                                  GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () async {
                                      await showLoginFormDialog(context);
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 2,
                                      ),
                                      margin: const EdgeInsets.only(top: 16),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: colorScheme.color35,
                                        ),
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                      child: Center(
                                        child: MeText(
                                          text: MeTranslations.instance
                                              .bottomSheet_publicProfile_follow,
                                          meFontStyle: MeFontStyle.H35,
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                                if (widget.appUserUid != null &&
                                    widget.appUserUid != user.uid) ...[
                                  // Check if current user is not the profile user
                                  const SizedBox(height: 22),
                                  FollowButton(
                                    targetUserId: user.uid,
                                    // Profile user's uid for follow actions
                                    targetMevolveId: user.mevolveId,
                                    currentMevolveId:
                                        authenticatedGlobalContext!
                                            .read<PublicUserCubit>()
                                            .state
                                            .publicUser
                                            ?.mevolveId,
                                    currentName: authenticatedGlobalContext!
                                        .read<PublicUserCubit>()
                                        .state
                                        .publicUser
                                        ?.name,
                                    targetName: user.name,
                                    onAction: () async {
                                      // Send analytics CTA click event for profile follow
                                      EventFormation()
                                          .sendPublicProfileCtaClicksEvent(
                                        trackAction:
                                            TrackAction.publicProfileFollow,
                                      );

                                      await _refreshUser();
                                    },
                                  ),
                                ],
                              ],
                            ),
                          ],
                        ),
                        if (user.bio != null) ...[
                          const SizedBox(height: 8),
                          MeText(
                            text: MeString.fromVariable(
                              user.bio!,
                            ),
                            meFontStyle: MeFontStyle.F8,
                            textOverflow: TextOverflow.visible,
                          ),
                        ],
                        if (user.profileTags.isNotEmpty) ...[
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              ...user.profileTags
                                  .map(
                                    (e) => MeText(
                                      text: MeString.fromVariable(
                                        '#$e',
                                      ),
                                      meFontStyle: MeFontStyle.F35,
                                    ),
                                  )
                                  .toList(),
                            ],
                          ),
                          const SizedBox(height: 8),
                        ],
                        if (user.socialLinks.isNotEmpty) ...[
                          const SizedBox(height: 20),
                          Center(
                            child: Wrap(
                              spacing: 20,
                              runSpacing: 20,
                              alignment: WrapAlignment.center,
                              verticalDirection: VerticalDirection.down,
                              direction: Axis.horizontal,
                              runAlignment: WrapAlignment.center,
                              children: [
                                ...user.socialLinks
                                    .map(
                                      (e) => GestureDetector(
                                        onTap: () {
                                          final Uri uri = Uri.parse(e)
                                              .replace(scheme: 'https');
                                          launchUrl(uri);
                                        },
                                        child: socialToIconMapper(e) == null
                                            ? Container(
                                                height: 22,
                                                width: 22,
                                                decoration: BoxDecoration(
                                                  color: colorScheme.color21,
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                    4,
                                                  ),
                                                ),
                                                child: Center(
                                                  child: MeText(
                                                    text: MeString.fromVariable(
                                                      getUrlFirstCharacter(
                                                        e,
                                                      ),
                                                    ),
                                                    meFontStyle: MeFontStyle.C1,
                                                  ),
                                                ),
                                              )
                                            : SizedBox(
                                                height: 22,
                                                width: 22,
                                                child: MeIcon(
                                                  iconPath: socialToIconMapper(
                                                    e,
                                                  ),
                                                  iconSize: const SizedBox(
                                                    width: 22,
                                                    height: 22,
                                                  ),
                                                ),
                                              ),
                                      ),
                                    )
                                    .toList(),
                              ],
                            ),
                          ),
                        ],
                        // Only show count containers if there are items to display
                        if (publicListsMetadata.isNotEmpty ||
                            publicNotesMetadata.isNotEmpty) ...[
                          const SizedBox(height: 24),
                          Row(
                            children: [
                              if (publicListsMetadata.isNotEmpty)
                                Expanded(
                                  child: PublicCountContainer(
                                    count: publicListsMetadata.length,
                                    title: MeTranslations.instance
                                        .bottomSheet_publicListsAndNotes_listTitleTopBar,
                                    onTap: () {
                                      double sheetLevel =
                                          (widget.bottomSheetLevel != null
                                                  ? widget.bottomSheetLevel! + 1
                                                  : null) ??
                                              SizeConstants.level3BottomSheet;
                                      showMeScrollableModalBottomSheet(
                                        widgetContext: context,
                                        bottomSheetLevel: sheetLevel,
                                        useSafeArea: true,
                                        builder: (
                                          _,
                                          setShowDiscardDialogFlagValue,
                                        ) {
                                          return PublicListOfListsWidget(
                                            user: user,
                                            sheetLevel: sheetLevel,
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ),
                              if (publicListsMetadata.isNotEmpty &&
                                  publicNotesMetadata.isNotEmpty)
                                const SizedBox(width: 16),
                              if (publicNotesMetadata.isNotEmpty)
                                Expanded(
                                  child: PublicCountContainer(
                                    count: publicNotesMetadata.length,
                                    title: MeTranslations.instance
                                        .bottomSheet_publicProfile_notes,
                                    onTap: () {
                                      double sheetLevel =
                                          (widget.bottomSheetLevel != null
                                                  ? widget.bottomSheetLevel! + 1
                                                  : null) ??
                                              SizeConstants.level3BottomSheet;
                                      showMeScrollableModalBottomSheet(
                                        widgetContext: context,
                                        bottomSheetLevel: sheetLevel,
                                        useSafeArea: true,
                                        builder: (
                                          _,
                                          setShowDiscardDialogFlagValue,
                                        ) {
                                          return PublicListOfNotesWidget(
                                            user: user,
                                            sheetLevel: sheetLevel,
                                          );
                                        },
                                      );
                                    },
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 24),
                        ],
                      ],
                    ),
                  ),
                ),
                // Show empty state only if user has no public content at all
                if (publicListsMetadata.isEmpty && publicNotesMetadata.isEmpty)
                  SliverFillRemaining(
                    child: Center(
                      child: EmptyStateWidget(
                        imageHeight: 84,
                        imageWidth: 57,
                        imagePath: AppStrings.notesEmptyIllustration(
                          colorScheme: colorScheme,
                        ),
                        imageText: MeTranslations
                            .instance.bottomSheet_publicProfile_emptyContent,
                      ),
                    ),
                  )
                // Show recent items section only if there are items to display
                else if (listAndNoteMetaData.isNotEmpty) ...[
                  SliverToBoxAdapter(
                    child: Column(
                      children: [
                        Container(
                          color: colorScheme.color5,
                          width: double.infinity,
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: 12,
                              horizontal: 16,
                            ),
                            child: MeText(
                              text: MeTranslations.instance
                                  .bottomSheet_publicProfile_mostRecent,
                              meFontStyle: MeFontStyle.T8,
                            ),
                          ),
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                  SliverList(
                    delegate: SliverChildBuilderDelegate(
                      (context, index) {
                        dynamic item = listAndNoteMetaData[index];
                        bool isLastIndex =
                            index == listAndNoteMetaData.length - 1;
                        return Column(
                          children: [
                            item is PublicNoteMetadata
                                ? PublicNoteTile(
                                    metaData: item,
                                    sheetLevel: widget.bottomSheetLevel,
                                  )
                                : PublicListTile(
                                    metaData: item,
                                    sheetLevel: widget.bottomSheetLevel,
                                  ),
                            if (index < listAndNoteMetaData.length - 1)
                              const SizedBox(height: 1),
                            if (isLastIndex) const SizedBox(height: 24),
                          ],
                        );
                      },
                      childCount: listAndNoteMetaData.length > 10
                          ? 10
                          : listAndNoteMetaData.length,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}
