import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/constants/app_strings.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/constants/validation_regexp.dart';
import 'package:mevolve/data/enums/common_sharing_model.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/public/public_list_metadata.dart';
import 'package:mevolve/data/models/public/public_note_metadata.dart';
import 'package:mevolve/data/models/public/public_users.dart';
import 'package:mevolve/data/models/user/user.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/public_user_cubit.dart';
import 'package:mevolve/features/app/bloc/cubit/public_user_state.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_icon.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/public_profile/model/public_viewer_user.dart';
import 'package:mevolve/features/public_profile/widgets/add_mevolve_id.dart';
import 'package:mevolve/features/public_profile/widgets/followers_followings_list_widget.dart';
import 'package:mevolve/features/public_profile/widgets/public_count_container.dart';
import 'package:mevolve/features/public_profile/widgets/public_list_tile.dart';
import 'package:mevolve/features/public_profile/widgets/public_note_tile.dart';
import 'package:mevolve/features/widgets/empty_state_widget.dart';
import 'package:mevolve/features/widgets/me_custom_bottomsheet.dart';
import 'package:mevolve/features/widgets/me_icon_button.dart';
import 'package:mevolve/features/widgets/me_text_input.dart';
import 'package:mevolve/features/widgets/me_title_bar.dart';
import 'package:mevolve/features/widgets/share_link_qr_sheet.dart';
import 'package:mevolve/features/widgets/show_me_modal_bottom_sheet.dart';
import 'package:mevolve/generated/assets.gen.dart';
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/nullable.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';

String? socialToIconMapper(String url) {
  if (url.contains('facebook')) {
    return Assets.social.facebookIcon.path;
  } else if (url.contains('twitter') || url.contains('x.com')) {
    return Assets.social.xIcon.path;
  } else if (url.contains('instagram')) {
    return Assets.social.instagramIcon.path;
  } else if (url.contains('linkedin')) {
    return Assets.social.linkedinIcon.path;
  } else if (url.contains('youtube')) {
    return Assets.social.youtubeIcon.path;
  } else if (url.contains('github')) {
    return Assets.social.githubIcon.path;
  } else if (url.contains('dribbble')) {
    return Assets.social.dribbbleIcon.path;
  } else if (url.contains('discord')) {
    return Assets.social.discordIcon.path;
  } else if (url.contains('medium')) {
    return Assets.social.mediumIcon.path;
  } else if (url.contains('pinterest')) {
    return Assets.social.pinterestIcon.path;
  } else if (url.contains('snapchat')) {
    return Assets.social.snapchatIcon.path;
  } else {
    return null;
  }
}

Future<void> openOwnerPublicProfile({UserData? user}) async {
  return await showMeScrollableModalBottomSheet(
    bottomSheetLevel: 2,
    builder: (_, setShowDiscardDialogFlagValue) {
      return OwnerPublicProfile(
        setShowDiscardDialogFlagValue: setShowDiscardDialogFlagValue,
        mainUser: user,
      );
    },
  );
}

class OwnerPublicProfile extends StatefulWidget {
  const OwnerPublicProfile({
    super.key,
    required this.setShowDiscardDialogFlagValue,
    this.mainUser,
  });

  final SetShowDiscardDialogFlagValue setShowDiscardDialogFlagValue;
  final UserData? mainUser;

  @override
  State<OwnerPublicProfile> createState() => _OwnerPublicProfileState();
}

class _OwnerPublicProfileState extends State<OwnerPublicProfile> {
  @override
  void initState() {
    super.initState();

    // Send analytics screen view event
    EventFormation()
        .sendPublicScreenViewedEvent(trackAction: TrackAction.publicProfile);
  }

  Future<void> addUpdateBio(String? bio) async {
    await showMeScrollableModalBottomSheet(
      bottomSheetLevel: 1,
      builder: (_, setShowDiscardDialogFlagValue) {
        return _AddBioSheetComponent(
          setShowDiscardDialogFlagValue: setShowDiscardDialogFlagValue,
          bio: bio,
          cubit: context.read<PublicUserCubit>(),
        );
      },
    );
  }

  Future<void> addLink(Set<String> socials) async {
    await showMeScrollableModalBottomSheet(
      bottomSheetLevel: 1,
      builder: (_, setShowDiscardDialogFlagValue) {
        return _AddSocialLinkSheetComponent(
          setShowDiscardDialogFlagValue: setShowDiscardDialogFlagValue,
          socials: socials,
          cubit: context.read<PublicUserCubit>(),
        );
      },
    );
  }

  Future<void> updateLink(String social) async {
    await showMeScrollableModalBottomSheet(
      bottomSheetLevel: 1,
      builder: (_, setShowDiscardDialogFlagValue) {
        return _UpdateSocialLinkComponent(
          setShowDiscardDialogFlagValue: setShowDiscardDialogFlagValue,
          social: social,
          cubit: context.read<PublicUserCubit>(),
        );
      },
    );
  }

  Future<bool?> checkIfIdExists() async {
    PublicUser? publicUser = context.read<PublicUserCubit>().state.publicUser;
    bool? isValid;
    final canUserShareFunctionality = publicUser != null;
    if (!canUserShareFunctionality) {
      bool? res = await UtilityMethods.showCompletePublicProfileDialog(
        context: context,
      );
      if (res == null || !res) {
        isValid = false;
        return false;
      }
      if (res) {
        String? res = await addMevolveIdBottomSheet();
        isValid = res != null;
        return res != null;
      }
    }
    return isValid;
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return BlocBuilder<PublicUserCubit, PublicUserState>(
      builder: (context, state) {
        PublicUser? user = state.publicUser;
        List<PublicListMetadata> publicListsMetadata =
            user?.publicListsMetadata ?? [];
        List<PublicNoteMetadata> publicNotesMetadata =
            user?.publicNotesMetadata ?? [];
        List<dynamic> listAndNoteMetaData = [
          ...publicListsMetadata,
          ...publicNotesMetadata,
        ];
        // sort with updateAt
        listAndNoteMetaData.sort(
          (a, b) => b.updatedAt.compareTo(a.updatedAt),
        );
        return Scaffold(
          backgroundColor: colorScheme.color6,
          resizeToAvoidBottomInset: false,
          body: SafeArea(
            child: Column(
              children: [
                MeTitleBar(
                  title: MeTranslations
                      .instance.bottomSheet_publicProfile_titleTopBar,
                  titleButton: user?.mevolveId == null
                      ? null
                      : MeIconButton(
                          onPressed: () {
                            showMeScrollableModalBottomSheet(
                              widgetContext: context,
                              bottomSheetLevel: 1,
                              useSafeArea: true,
                              builder: (_, __) {
                                final String shareableLink =
                                    '${AppConstant.sharingSiteBaseUrl}/user/${user!.mevolveId}';
                                return ShareQrAndLinkSheet(
                                  args: ShareLinkQrData(
                                    link: shareableLink,
                                    title: '',
                                    id: user.mevolveId,
                                    ownerName: user.name,
                                    collection:
                                        FirebaseDocCollectionType.publicUsers,
                                    secondaryName: user.name,
                                  ),
                                  shareableLink: shareableLink,
                                  sharerUserRole: MemberRole.owner,
                                  shareCount: 0,
                                );
                              },
                            );
                          },
                          iconPath: Assets.svg.icShare.path,
                          iconSize: const SizedBox(
                            height: 17,
                          ),
                          iconColor: Theme.of(context)
                              .extension<MeColorScheme>()!
                              .color12,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                        ),
                  onCloseClick: () {
                    Navigator.of(context).pop();
                  },
                ),
                Expanded(
                  child: CustomScrollView(
                    slivers: [
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: const EdgeInsets.only(
                            top: 24,
                            left: 16,
                            right: 16,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Row(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      CircleAvatar(
                                        radius: 25,
                                        backgroundColor: colorScheme.color1,
                                        child: Center(
                                          child: MeText(
                                            text: MeString.fromVariable(
                                              user == null
                                                  ? widget.mainUser?.userInfo
                                                          .name
                                                          .split('')[0]
                                                          .toUpperCase() ??
                                                      ''
                                                  : user.name
                                                      .split('')[0]
                                                      .toUpperCase(),
                                            ),
                                            meFontStyle: MeFontStyle.R12,
                                          ),
                                        ),
                                      ),
                                      const Spacer(),
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.end,
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          GestureDetector(
                                            behavior: HitTestBehavior.opaque,
                                            onTap: () {
                                              final InternetConnectionState
                                                  internetConnectionState =
                                                  context
                                                      .read<AppBloc>()
                                                      .state
                                                      .internetConnectionState;
                                              bool isOffline = context
                                                      .read<AppBloc>()
                                                      .state
                                                      .internetConnectionState ==
                                                  InternetConnectionState
                                                      .disconnected;
                                              if (isOffline) {
                                                UtilityMethods
                                                    .showOfflineDialogOrPerformAction(
                                                  internetActiveFunction: () {},
                                                  internetConnectionState:
                                                      internetConnectionState,
                                                  context: context,
                                                );
                                                return;
                                              }
                                              openFollowerFollowingSheet(
                                                isFollowers: true,
                                                userId: user?.uid ??
                                                    widget.mainUser?.uid ??
                                                    '',
                                                followersCount:
                                                    user?.followers ?? 0,
                                                followingCount:
                                                    user?.following ?? 0,
                                              );
                                            },
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                MeText(
                                                  text: MeString.fromVariable(
                                                    user?.followers
                                                            .toString() ??
                                                        '0',
                                                  ),
                                                  meFontStyle: MeFontStyle.C7,
                                                ),
                                                const SizedBox(height: 8),
                                                MeText(
                                                  text: MeTranslations.instance
                                                      .bottomSheet_publicProfile_followers,
                                                  meFontStyle: MeFontStyle.I7,
                                                ),
                                              ],
                                            ),
                                          ),
                                          const SizedBox(width: 24),
                                          GestureDetector(
                                            behavior: HitTestBehavior.opaque,
                                            onTap: () {
                                              final InternetConnectionState
                                                  internetConnectionState =
                                                  context
                                                      .read<AppBloc>()
                                                      .state
                                                      .internetConnectionState;
                                              bool isOffline = context
                                                      .read<AppBloc>()
                                                      .state
                                                      .internetConnectionState ==
                                                  InternetConnectionState
                                                      .disconnected;
                                              if (isOffline) {
                                                UtilityMethods
                                                    .showOfflineDialogOrPerformAction(
                                                  internetActiveFunction: () {},
                                                  internetConnectionState:
                                                      internetConnectionState,
                                                  context: context,
                                                );
                                                return;
                                              }
                                              openFollowerFollowingSheet(
                                                isFollowers: false,
                                                userId: user?.uid ??
                                                    widget.mainUser?.uid ??
                                                    '',
                                                followersCount:
                                                    user?.followers ?? 0,
                                                followingCount:
                                                    user?.following ?? 0,
                                              );
                                            },
                                            child: Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.start,
                                              children: [
                                                MeText(
                                                  text: MeString.fromVariable(
                                                    user?.following
                                                            .toString() ??
                                                        '0',
                                                  ),
                                                  meFontStyle: MeFontStyle.C7,
                                                ),
                                                const SizedBox(height: 8),
                                                MeText(
                                                  text: MeTranslations.instance
                                                      .bottomSheet_publicProfile_following,
                                                  meFontStyle: MeFontStyle.I7,
                                                ),
                                              ],
                                            ),
                                          ),
                                        ],
                                      ),
                                    ],
                                  ),
                                  const SizedBox(height: 12),
                                  MeText(
                                    text: MeString.fromVariable(
                                      user?.name ??
                                          widget.mainUser?.userInfo.name ??
                                          '',
                                    ),
                                    meFontStyle: MeFontStyle.A8,
                                  ),
                                  const SizedBox(height: 8),
                                  GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () async {
                                      await addMevolveIdBottomSheet(
                                        currentValue: user?.mevolveId,
                                      );
                                    },
                                    child: MeText(
                                      text: MeString.fromVariable(
                                        '@${user?.mevolveId ?? 'mevolveid'}',
                                      ),
                                      meFontStyle: user == null
                                          ? MeFontStyle.F7
                                          : MeFontStyle.F8,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  GestureDetector(
                                    behavior: HitTestBehavior.opaque,
                                    onTap: () async {
                                      if (user == null) {
                                        bool? res = await checkIfIdExists();
                                        if (res != true) {
                                          return;
                                        }
                                      }
                                      addUpdateBio(user?.bio);
                                    },
                                    child: MeText(
                                      text: user?.bio == null
                                          ? MeTranslations.instance
                                              .bottomSheet_publicProfileBio_placeholder
                                          : MeString.fromVariable(
                                              user!.bio!,
                                            ),
                                      meFontStyle: user?.bio == null
                                          ? MeFontStyle.F7
                                          : MeFontStyle.F8,
                                      textOverflow: TextOverflow.visible,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  GestureDetector(
                                    onTap: () async {
                                      if (user == null) {
                                        bool? res = await checkIfIdExists();
                                        if (res != true) {
                                          return;
                                        }
                                      }

                                      await showMeScrollableModalBottomSheet(
                                        bottomSheetLevel: 1,
                                        builder:
                                            (_, setShowDiscardDialogFlagValue) {
                                          return _HashtagInputComponent(
                                            setShowDiscardDialogFlagValue:
                                                setShowDiscardDialogFlagValue,
                                            cubit:
                                                context.read<PublicUserCubit>(),
                                            existingTags:
                                                user?.profileTags ?? [],
                                          );
                                        },
                                      );
                                    },
                                    child: user?.profileTags == null ||
                                            (user != null &&
                                                user.profileTags.isEmpty)
                                        ? MeText(
                                            text: MeTranslations.instance
                                                .bottomSheet_publicProfile_hashtagPlaceholder,
                                            textOverflow: TextOverflow.visible,
                                            meFontStyle: user == null ||
                                                    user.profileTags.isEmpty
                                                ? MeFontStyle.F7
                                                : MeFontStyle.F35,
                                          )
                                        : Wrap(
                                            spacing: 8,
                                            runSpacing: 8,
                                            children: [
                                              ...user!.profileTags
                                                  .map(
                                                    (e) => MeText(
                                                      text:
                                                          MeString.fromVariable(
                                                        '#$e',
                                                      ),
                                                      meFontStyle:
                                                          MeFontStyle.F35,
                                                    ),
                                                  )
                                                  .toList(),
                                            ],
                                          ),
                                  ),
                                ],
                              ),
                              user!.socialLinks.isEmpty
                                  ? const SizedBox(height: 8)
                                  : const SizedBox(height: 20),
                              user.socialLinks.isEmpty
                                  ? GestureDetector(
                                      onTap: () async {
                                        addLink(user.socialLinks);
                                      },
                                      child: MeText(
                                        text: MeTranslations.instance
                                            .bottomSheet_publicProfileSocialLink_placeholder,
                                        textOverflow: TextOverflow.visible,
                                        meFontStyle: MeFontStyle.F7,
                                      ),
                                    )
                                  : Center(
                                      child: Wrap(
                                        spacing: 20,
                                        runSpacing: 20,
                                        alignment: WrapAlignment.center,
                                        verticalDirection:
                                            VerticalDirection.down,
                                        direction: Axis.horizontal,
                                        runAlignment: WrapAlignment.center,
                                        children: [
                                          ...user.socialLinks
                                              .map(
                                                (e) => GestureDetector(
                                                  onTap: () {
                                                    updateLink(e);
                                                  },
                                                  child:
                                                      socialToIconMapper(e) ==
                                                              null
                                                          ? Container(
                                                              height: 22,
                                                              width: 22,
                                                              decoration:
                                                                  BoxDecoration(
                                                                color:
                                                                    colorScheme
                                                                        .color21,
                                                                borderRadius:
                                                                    BorderRadius
                                                                        .circular(
                                                                  4,
                                                                ),
                                                              ),
                                                              child: Center(
                                                                child: MeText(
                                                                  text: MeString
                                                                      .fromVariable(
                                                                    getUrlFirstCharacter(
                                                                      e,
                                                                    ),
                                                                  ),
                                                                  meFontStyle:
                                                                      MeFontStyle
                                                                          .C1,
                                                                ),
                                                              ),
                                                            )
                                                          : SizedBox(
                                                              height: 22,
                                                              width: 22,
                                                              child: MeIcon(
                                                                iconPath:
                                                                    socialToIconMapper(
                                                                  e,
                                                                ),
                                                                iconSize:
                                                                    const SizedBox(
                                                                  width: 22,
                                                                  height: 22,
                                                                ),
                                                              ),
                                                            ),
                                                ),
                                              )
                                              .toList(),
                                          if (user.socialLinks.length < 20)
                                            GestureDetector(
                                              onTap: () async {
                                                addLink(user.socialLinks);
                                              },
                                              child: Container(
                                                height: 22,
                                                width: 22,
                                                decoration: BoxDecoration(
                                                  color: colorScheme.color26,
                                                  borderRadius:
                                                      BorderRadius.circular(4),
                                                ),
                                                child: MeIcon(
                                                  iconPath:
                                                      Assets.svg.plusIcon.path,
                                                  iconSize: const SizedBox(
                                                    width: 13,
                                                    height: 13,
                                                  ),
                                                  iconColor:
                                                      colorScheme.color12,
                                                ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                              const SizedBox(height: 24),
                              Row(
                                children: [
                                  Expanded(
                                    child: PublicCountContainer(
                                      count: publicListsMetadata.length,
                                      title: MeTranslations.instance
                                          .bottomSheet_publicListsAndNotes_listTitleTopBar,
                                      onTap: () async {
                                        showMeScrollableModalBottomSheet(
                                          bottomSheetLevel:
                                              SizeConstants.level3BottomSheet,
                                          builder: (
                                            _,
                                            setShowDiscardDialogFlagValue,
                                          ) {
                                            return PublicListOfListsWidget(
                                              isOwner: true,
                                              user: PublicViewerUser(
                                                id: user.id,
                                                name: user.name,
                                                mevolveId: user.mevolveId,
                                                followers: user.followers,
                                                following: user.following,
                                                socialLinks:
                                                    user.socialLinks.toList(),
                                                profileTags: user.profileTags,
                                                publicListsMetadata:
                                                    publicListsMetadata,
                                                publicNotesMetadata:
                                                    publicNotesMetadata,
                                                uid: user.uid,
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: PublicCountContainer(
                                      count: publicNotesMetadata.length,
                                      title: MeTranslations.instance
                                          .bottomSheet_publicProfile_notes,
                                      onTap: () async {
                                        showMeScrollableModalBottomSheet(
                                          bottomSheetLevel:
                                              SizeConstants.level3BottomSheet,
                                          builder: (
                                            _,
                                            setShowDiscardDialogFlagValue,
                                          ) {
                                            return PublicListOfNotesWidget(
                                              isOwner: true,
                                              user: PublicViewerUser(
                                                id: user.id,
                                                name: user.name,
                                                mevolveId: user.mevolveId,
                                                followers: user.followers,
                                                following: user.following,
                                                socialLinks:
                                                    user.socialLinks.toList(),
                                                profileTags: user.profileTags,
                                                publicListsMetadata:
                                                    publicListsMetadata,
                                                publicNotesMetadata:
                                                    publicNotesMetadata,
                                                uid: user.uid,
                                              ),
                                            );
                                          },
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 24),
                            ],
                          ),
                        ),
                      ),
                      if (listAndNoteMetaData.isEmpty)
                        SliverFillRemaining(
                          child: Center(
                            child: EmptyStateWidget(
                              imageHeight: 84,
                              imageWidth: 57,
                              imagePath: AppStrings.notesEmptyIllustration(
                                colorScheme: colorScheme,
                              ),
                              imageText: MeTranslations.instance
                                  .bottomSheet_publicProfile_emptyContent,
                            ),
                          ),
                        )
                      else
                        SliverToBoxAdapter(
                          child: Column(
                            children: [
                              Container(
                                color: colorScheme.color5,
                                width: double.infinity,
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    vertical: 12,
                                    horizontal: 16,
                                  ),
                                  child: MeText(
                                    text: MeTranslations.instance
                                        .bottomSheet_publicProfile_mostRecent,
                                    meFontStyle: MeFontStyle.T8,
                                  ),
                                ),
                              ),
                              const SizedBox(height: 4),
                              ListView.separated(
                                shrinkWrap: true,
                                physics: const NeverScrollableScrollPhysics(),
                                itemCount: listAndNoteMetaData.length > 10
                                    ? 10
                                    : listAndNoteMetaData.length,
                                itemBuilder: (context, index) {
                                  dynamic item = listAndNoteMetaData[index];
                                  return item is PublicNoteMetadata
                                      ? OwnerNoteTile(metaData: item)
                                      : OwnerListTile(metaData: item);
                                },
                                separatorBuilder: (context, index) {
                                  return const SizedBox(height: 1);
                                },
                              ),
                              const SizedBox(height: 24),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

class _AddBioSheetComponent extends StatefulWidget {
  const _AddBioSheetComponent({
    required this.setShowDiscardDialogFlagValue,
    this.bio,
    required this.cubit,
  });

  final SetShowDiscardDialogFlagValue setShowDiscardDialogFlagValue;
  final String? bio;
  final PublicUserCubit cubit;

  @override
  State<_AddBioSheetComponent> createState() => _AddBioSheetComponentState();
}

class _AddBioSheetComponentState extends State<_AddBioSheetComponent> {
  final TextEditingController textEditingController = TextEditingController();

  @override
  void initState() {
    if (widget.bio != null) {
      textEditingController.text = widget.bio!;
    }
    widget.setShowDiscardDialogFlagValue(
      () => textEditingController.text != (widget.bio ?? ''),
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MeTextInput(
      title: MeTranslations.instance.bottomSheet_publicProfileBio_titleTopBar,
      deleteDialogConfig: widget.bio != null
          ? DeleteDialogConfig(
              title: MeTranslations.instance.overlay_deleteBio_title,
              description: MeTranslations.instance.overlay_deleteBio_content,
            )
          : null,
      showDeleteButton: widget.bio != null ? true : false,
      onDelete: () {
        widget.cubit.updatePublicUser(
          publicUser:
              context.read<PublicUserCubit>().state.publicUser!.copyWith(
                    bio: const Nullable(null),
                  ),
        );
        Navigator.of(context).pop();
      },
      maxLength: 200,
      textEditingController: textEditingController,
      keyboardType: TextInputType.emailAddress,
      suffixIconPath: Assets.svg.icSend.path,
      hintText:
          MeTranslations.instance.bottomSheet_publicProfileBio_placeholder,
      allowKBPop: false,
      normalDiscardDialogLevel: DialogLevel.level1,
      allowEmptyTextSubmit: false,
      initialValue: MeString.fromVariable(widget.bio ?? ''),
      onFieldSubmitted: (value) async {
        widget.cubit.updatePublicUser(
          publicUser: context
              .read<PublicUserCubit>()
              .state
              .publicUser!
              .copyWith(bio: Nullable(value)),
        );
        return;
      },
    );
  }
}

class _AddSocialLinkSheetComponent extends StatefulWidget {
  const _AddSocialLinkSheetComponent({
    required this.setShowDiscardDialogFlagValue,
    required this.socials,
    required this.cubit,
  });

  final SetShowDiscardDialogFlagValue setShowDiscardDialogFlagValue;
  final Set<String> socials;
  final PublicUserCubit cubit;

  @override
  State<_AddSocialLinkSheetComponent> createState() =>
      _AddSocialLinkSheetComponentState();
}

class _AddSocialLinkSheetComponentState
    extends State<_AddSocialLinkSheetComponent> {
  final TextEditingController textEditingController = TextEditingController();

  @override
  void initState() {
    widget.setShowDiscardDialogFlagValue(
      () => textEditingController.text.isNotEmpty,
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MeTextInput(
      title: MeTranslations
          .instance.bottomSheet_publicProfileSocialLink_titleTopBar,
      maxLength: null,
      textEditingController: textEditingController,
      keyboardType: TextInputType.url,
      validationRegExp: ValidationRegexp.validateLinkRegEx,
      suffixIconPath: Assets.svg.icSend.path,
      allowKBPop: false,
      initialValue: MeString.empty,
      textCapitalization: TextCapitalization.none,
      inputTextStyle: MeFontStyle.D8,
      hintText: MeTranslations
          .instance.bottomSheet_publicProfileSocialLink_placeholder,
      allowNewLineText: false,
      onFieldSubmitted: (value) async {
        Set<String> socials = widget.socials;
        socials.add(value);
        widget.cubit.updatePublicUser(
          publicUser: context
              .read<PublicUserCubit>()
              .state
              .publicUser!
              .copyWith(socialLinks: socials),
        );
        return;
      },
    );
  }
}

class _UpdateSocialLinkComponent extends StatefulWidget {
  const _UpdateSocialLinkComponent({
    required this.social,
    required this.setShowDiscardDialogFlagValue,
    required this.cubit,
  });

  final String social;
  final SetShowDiscardDialogFlagValue setShowDiscardDialogFlagValue;
  final PublicUserCubit cubit;

  @override
  State<_UpdateSocialLinkComponent> createState() =>
      _UpdateSocialLinkComponentState();
}

class _UpdateSocialLinkComponentState
    extends State<_UpdateSocialLinkComponent> {
  final TextEditingController textEditingController = TextEditingController();
  bool isDeleting = false;

  @override
  void initState() {
    textEditingController.text = widget.social;
    widget.setShowDiscardDialogFlagValue(
      () => textEditingController.text != widget.social,
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return MeTextInput(
      title: MeTranslations
          .instance.bottomSheet_publicProfileSocialLink_titleTopBar,
      textEditingController: textEditingController,
      validationRegExp: ValidationRegexp.validateLinkRegEx,
      suffixIconPath: Assets.svg.icSend.path,
      inputTextStyle: MeFontStyle.D8,
      hintText: MeTranslations
          .instance.bottomSheet_publicProfileSocialLink_placeholder,
      allowEmptyTextSubmit: false,
      normalDiscardDialogLevel: DialogLevel.level1,
      allowKBPop: false,
      keyboardType: TextInputType.url,
      textCapitalization: TextCapitalization.none,
      initialValue: MeString.fromVariable(widget.social),
      showDeleteButton: true,
      deleteDialogConfig: DeleteDialogConfig(
        title: MeTranslations.instance.overlay_deleteSocialProfile_title,
        description:
            MeTranslations.instance.overlay_deleteSocialProfile_content,
      ),
      onDelete: () {
        Set<String> socials = widget.cubit.state.publicUser!.socialLinks;
        socials.remove(widget.social);
        if (!context.mounted) return;
        widget.cubit.updatePublicUser(
          publicUser: context
              .read<PublicUserCubit>()
              .state
              .publicUser!
              .copyWith(socialLinks: socials),
        );
        Navigator.of(context).pop();
      },
      onFieldSubmitted: (value) async {
        Set<String> socials = widget.cubit.state.publicUser!.socialLinks;
        socials.remove(widget.social);
        socials.add(value);
        widget.cubit.updatePublicUser(
          publicUser: context
              .read<PublicUserCubit>()
              .state
              .publicUser!
              .copyWith(socialLinks: socials),
        );
        return;
      },
    );
  }
}

String getUrlFirstCharacter(String url) {
  if (url.isEmpty) {
    return '';
  }

  bool isHttp = url.contains('http://');
  bool isHttps = url.contains('https://');
  bool isWWW = url.contains('www.');

  // Remove protocol prefixes if present
  String cleanUrl = url;
  if (isHttp) {
    cleanUrl = url.replaceFirst('http://', '');
  } else if (isHttps) {
    cleanUrl = url.replaceFirst('https://', '');
  }

  // Remove www. if present
  if (isWWW) {
    cleanUrl = cleanUrl.replaceFirst('www.', '');
  }

  // Return the first character of the cleaned URL
  return cleanUrl.isNotEmpty ? cleanUrl[0].toUpperCase() : '';
}

class _HashtagInputComponent extends StatefulWidget {
  const _HashtagInputComponent({
    required this.setShowDiscardDialogFlagValue,
    required this.cubit,
    required this.existingTags,
  });

  final SetShowDiscardDialogFlagValue setShowDiscardDialogFlagValue;
  final PublicUserCubit cubit;
  final List<String> existingTags;

  @override
  State<_HashtagInputComponent> createState() => _HashtagInputComponentState();
}

class _HashtagInputComponentState extends State<_HashtagInputComponent> {
  final TextEditingController textEditingController = TextEditingController();
  List<String> hashtags = [];
  bool isSame = true;
  bool isHashtagsValid = true;

  @override
  void initState() {
    super.initState();
    hashtags = List.from(widget.existingTags);
    // Join existing tags with commas for initial display
    if (hashtags.isNotEmpty) {
      textEditingController.text =
          hashtags.map((tag) => tag.startsWith('#') ? tag : '#$tag').join(', ');
    }

    widget.setShowDiscardDialogFlagValue(
      () => !listEquals(hashtags, widget.existingTags),
    );
  }

  void _updateHashtags(String value) {
    // Split the input by commas and clean up each tag
    if (value.contains(',,') || value.contains('##')) {
      isHashtagsValid = false;
      setState(() {});
      return;
    }
    final List<String> newTags = value
        .split(',')
        .map((tag) {
          // Clean up each tag and handle # symbol
          String cleanTag = tag.trim();
          if (cleanTag.startsWith('#')) {
            cleanTag = cleanTag.substring(1);
          }
          bool hasSpace = cleanTag.contains(' ');
          if (hasSpace) {
            cleanTag = cleanTag.split(' ')[0];
          }
          return cleanTag;
        })
        .where((tag) => tag.isNotEmpty)
        .toList();

    if (!listEquals(newTags, widget.existingTags)) {
      isSame = false;
    } else {
      isSame = true;
    }
    hashtags = newTags;
    isHashtagsValid = true;
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return MeTextInput(
      title: MeTranslations.instance.bottomSheet_publicHashtags_titleTopBar,
      setShowDiscardDialogFlagValue: widget.setShowDiscardDialogFlagValue,
      textEditingController: textEditingController,
      keyboardType: TextInputType.text,
      hintText: MeTranslations.instance.bottomSheet_publicHashtags_placeholder,
      allowKBPop: false,
      textCapitalization: TextCapitalization.none,
      normalDiscardDialogLevel: DialogLevel.level1,
      allowEmptyTextSubmit: true,
      initialValue: MeString.fromVariable(
        widget.existingTags.map((tag) => '#$tag').join(', '),
      ),
      allowMultiLine: true,
      canHaveCapitalLetters: false,
      onChanged: _updateHashtags,
      inputFormatters: [
        HashtagLimitFormatter(10),
        TextInputFormatter.withFunction((oldValue, newValue) {
          final newText =
              newValue.text.replaceAll(RegExp(r'[^a-zA-Z0-9_#,\s]'), '');

          if (newText != newValue.text) {
            return TextEditingValue(
              text: newText,
              selection: TextSelection.collapsed(offset: newText.length),
            );
          }

          return newValue;
        }),
      ],
      showDeleteButton: widget.existingTags.isNotEmpty,
      deleteDialogConfig: widget.existingTags.isNotEmpty
          ? DeleteDialogConfig(
              title: MeTranslations.instance.overlay_deletePublicHashtags_title,
              description:
                  MeTranslations.instance.overlay_deletePublicHashtags_content,
            )
          : null,
      onDelete: () {
        widget.cubit.updatePublicUser(
          publicUser:
              context.read<PublicUserCubit>().state.publicUser!.copyWith(
            profileTags: [],
          ),
        );
        Navigator.of(context).pop();
      },
      onFieldSubmitted: (value) async {
        if (!isHashtagsValid) return;
        widget.cubit.updatePublicUser(
          publicUser:
              context.read<PublicUserCubit>().state.publicUser!.copyWith(
                    profileTags: hashtags,
                  ),
        );
        return;
      },
      widgetBelowTextField: (ctx, fn) => Container(
        color: colorScheme.color6,
        child: Column(
          children: [
            const SizedBox(height: 1),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              width: double.infinity,
              color: colorScheme.color5,
              child: Row(
                children: [
                  const Spacer(),
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      hashtags.length > 5
                          ? MeText(
                              text: MeString(
                                '${hashtags.length}/10',
                              ),
                              meFontStyle: MeFontStyle.I7,
                            )
                          : const SizedBox.shrink(),
                      const SizedBox(width: 8),
                      MeIconButton(
                        iconPath: Assets.svg.icSend.path,
                        iconSize: const SizedBox(
                          height: 18,
                          width: 18,
                        ),
                        iconContainerSize: const SizedBox(
                          height: 24,
                          width: 24,
                        ),
                        iconColor: isSame || !isHashtagsValid
                            ? colorScheme.color4
                            : colorScheme.color35,
                        onPressed: () async {
                          if (isSame) return;
                          if (!isHashtagsValid) return;
                          widget.cubit.updatePublicUser(
                            publicUser: context
                                .read<PublicUserCubit>()
                                .state
                                .publicUser!
                                .copyWith(
                                  profileTags: hashtags,
                                ),
                          );
                          Navigator.of(context).pop();
                        },
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class HashtagLimitFormatter extends TextInputFormatter {
  HashtagLimitFormatter(this.maxTags);

  final int? maxTags;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (maxTags == null) return newValue;

    // Count commas to determine number of tags
    final commaCount = ','.allMatches(newValue.text).length;
    final tagCount = commaCount + 1;

    // If exceeding max tags, return old value to prevent change
    if (tagCount > maxTags!) {
      return oldValue;
    }

    return newValue;
  }
}
