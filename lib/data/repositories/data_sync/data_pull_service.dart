import 'dart:async';
import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/constants/app_config.dart';
import 'package:mevolve/data/enums/app_language_type.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/models/attachment_info.dart';
import 'package:mevolve/data/models/calendar_event_action.dart';
import 'package:mevolve/data/models/calendar_event_setup.dart';
import 'package:mevolve/data/models/chat_message.dart';
import 'package:mevolve/data/models/chat_user.dart';
import 'package:mevolve/data/models/habit/habit_action.dart';
import 'package:mevolve/data/models/habit/habit_setup.dart';
import 'package:mevolve/data/models/in_app_notification.dart';
import 'package:mevolve/data/models/journal_action.dart';
import 'package:mevolve/data/models/journal_setup.dart';
import 'package:mevolve/data/models/list.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/money_tracker/money_tracker_setups.dart';
import 'package:mevolve/data/models/money_tracker/money_tracker_transaction.dart';
import 'package:mevolve/data/models/note.dart';
import 'package:mevolve/data/models/public/public_users.dart';
import 'package:mevolve/data/models/release_config_model.dart';
import 'package:mevolve/data/models/snippet.dart';
import 'package:mevolve/data/models/todo.dart';
import 'package:mevolve/data/models/user/calendar_integration.dart';
import 'package:mevolve/data/models/user/special_activity.dart';
import 'package:mevolve/data/models/user/user.dart';
import 'package:mevolve/data/models/user/user_metadata.dart';
import 'package:mevolve/data/models/user/user_resources.dart';
import 'package:mevolve/data/models/user/view_settings.dart';
import 'package:mevolve/data/models/viewed_snippet.dart';
import 'package:mevolve/data/models/webservice/me_base_model.dart';
import 'package:mevolve/data/providers/drift_database.dart';
import 'package:mevolve/data/providers/firebase_firestore.dart';
import 'package:mevolve/data/providers/firebase_functions.dart';
import 'package:mevolve/data/providers/firebase_storage.dart';
import 'package:mevolve/data/providers/shared_prefs.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/features/notifications/notification_service.dart';
import 'package:mevolve/features/widgets/show_me_snackbar.dart';
import 'package:mevolve/utilities/encryption/me_encryption.dart';
import 'package:mevolve/utilities/logger/log.dart';
import 'package:mevolve/utilities/logger/log_tags.dart';
import 'package:mevolve/utilities/logger/me_logger.dart';
import 'package:mevolve/utilities/nullable.dart';
import 'package:mevolve/utilities/sharing_utility/sharing_cleanup_functions.dart';
import 'package:mevolve/utilities/utility_methods.dart';

class DataPullService {
  DataPullService._internal({
    required NotificationService notificationsRepository,
    required DatabaseRepository databaseRepository,
  })  : _firebaseFirestoreRepository = FirebaseFirestoreRepository(),
        _databaseRepository = databaseRepository;

  factory DataPullService({
    required NotificationService notificationsRepository,
    required DatabaseRepository databaseRepository,
  }) {
    _dataPullService ??= DataPullService._internal(
      notificationsRepository: notificationsRepository,
      databaseRepository: databaseRepository,
    );
    return _dataPullService!;
  }

  static DataPullService? _dataPullService;
  bool _isRunning = false;
  late String _uid;
  Timer? _timer;
  final DebounceAction debounceSuccessfulSyncTasks =
      DebounceAction(const Duration(seconds: 5));

  final Log _log = MeLogger.getLogger(LogTags.dataPullService);
  final Log _logDEBUG = MeLogger.getLogger(LogTags.dataPullServiceDEBUG);

  StreamSubscription<List<Map<String, dynamic>>>? _userSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _userMetadataSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _specialActivitiesSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _publicUserSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _viewSettingsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _userResourcesSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _todosSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _habitSetupsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _habitSharedSetupsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _habitActionSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _habitSharedActionSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _journalSetupsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _journalSharedSetupsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _journalActionSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _journalSharedActionSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _chatUsersSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _chatMessagesSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _issueReportsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _snippetsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _viewedSnippetsSubscription;
  StreamSubscription<List<ReleaseConfigModel?>>? _releaseConfigsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _inAppNotificationsSubscription;

  StreamSubscription<List<Map<String, dynamic>>>? _moneySetupsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _moneySharedSetupsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _moneyTransactionsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _moneySharedTransactionsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _calendarSetupsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _calendarActionsSubscription;
  StreamSubscription<List<Map<String, dynamic>>>?
      _calendarIntegrationSubscription;

  //private
  StreamSubscription<List<Map<String, dynamic>>>? _listDataSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _notesSubscription;

  //shared
  StreamSubscription<List<Map<String, dynamic>>>? _sharedListDataSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _sharedNotesSubscription;
  StreamSubscription<List<Map<String, dynamic>>>? _sharedTodosSubscription;

  late DriftDatabaseRepository _driftDatabaseRepository;
  final DatabaseRepository _databaseRepository;
  late final FirebaseFirestoreRepository _firebaseFirestoreRepository;

  Future<(Map<String, dynamic>?, Map<String, dynamic>?)> getRawUserDataWithKeys(
    User user, {
    LanguageType? language,
    bool refreshSecret = false,
  }) async {
    _countLog(FirebaseDocCollectionType.users, 1);
    final result = await Future.wait([
      FirebaseFunctionsRepository().getRawUserDataWithKeys(
        user.uid,
        user.displayName,
        user.email,
        language,
      ),
      if (refreshSecret) MeEncryption().fetchUserSecret(user.email!),
    ]);
    final userDataWithKeys = result[0] as Map<String, dynamic>?;
    if (userDataWithKeys == null) {
      return (null, null);
    }
    final userSecret = !refreshSecret ? null : result[1] as String?;

    final passwordType =
        jsonDecode(userDataWithKeys['userKey'])['privateKeyPasswordType'];
    final userKey = jsonDecode(userDataWithKeys['userKey']);
    await MeEncryption().saveSecrets(
      user.uid,
      userKey['encPrivateKey'],
      userKey['publicKey'],
    );
    if (userSecret != null && passwordType == 'KMS') {
      await MeEncryption().decryptAndSavePrivateKey(user.uid, userSecret);
    }

    return (
      jsonDecode(userDataWithKeys['user']) as Map<String, dynamic>,
      jsonDecode(userDataWithKeys['viewSettings']) as Map<String, dynamic>
    );
  }

  SharedPreferencesClient? sharedPrefsClient;

  void startListeningCloudRC(
    DriftDatabaseRepository driftDatabaseRepository,
  ) {
    _releaseConfigsSubscription?.cancel();
    _log.i('Started listening to cloud RC');
    _driftDatabaseRepository = driftDatabaseRepository;
    _listenReleaseConfigs();
  }

  Future<void> startListeningCloudData(
    String uid,
    DriftDatabaseRepository driftDatabaseRepository,
    SharedPreferencesClient sharedPrefs,
  ) async {
    _log.i('Started listening to data');
    sharedPrefsClient ??= sharedPrefs;
    _driftDatabaseRepository = driftDatabaseRepository;
    _uid = uid;
    if (_isRunning) {
      _log.i('Already running');
      return;
    }
    _isRunning = true;
    await listenCloudData(uid);
  }

  Future<void> listenCloudData(String uid) async {
    _log.i('Initializing data listeners');
    _listenUser();
    _listenUserMetadata();
    _listenSpecialActivities();
    _listenPublicUser();
    _listenUserViewSettings();
    _listenUserResources();
    _listenToDos();
    _listenSharedToDos();
    _listenNotes();
    _listenSharedNotes();
    _listenHabitSetups();
    _listenHabitSharedSetups();
    _listenHabitActions();
    _listenHabitSharedActions();
    _listenJournalSetups();
    _listenJournalSharedSetups();
    _listenJournalActions();
    _listenJournalSharedActions();
    _listenLists();
    _listenSharedLists();
    _listenMessages();
    _listenChatUser();
    _listenSnippets();
    _listenViewedSnippets();
    _startResetServicePeriodicallyTimer();
    _listenInAppNotifications();
    _listenMoneySetups();
    _listenSharedMoneySetups();
    _listenMoneyTransactions();
    _listenSharedMoneyTransactions();
    _listenCalendarSetups();
    _listenCalendarActions();
    _listenCalendarIntegration();
  }

  /// This method is responsible for resetting the service every 25 minutes.
  /// This is done because Firestore listeners resets after every 30 minutes
  /// which results in fetching all the documents again.
  void _startResetServicePeriodicallyTimer() {
    _stopTimer();
    _log.i('Starting timer for resetting listeners periodically');
    _timer = Timer.periodic(const Duration(minutes: 25), (timer) async {
      _log.i('Resetting data pull service after timeout');
      await listenCloudData(_uid);
    });
  }

  void _stopTimer() {
    _timer?.cancel();
    _timer = null;
  }

  Future<void> _listenUser() async {
    _log.i('Starting user listener');
    await _userSubscription?.cancel();

    _userSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.users,
      uid: _uid,
      lastSyncDate: DateTime.fromMicrosecondsSinceEpoch(0),
    )
        .listen(
      (List<Map<String, dynamic>> users) async {
        _countLog(FirebaseDocCollectionType.users, users.length);
        final user = users.isNotEmpty ? users.first : null;
        if (user != null) {
          UserData userData = UserData.fromMap(user);
          _updateUser(userData);
        } else {
          _log.e('User not found');
        }
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.users,
        );
      },
    );
  }

  Future<void> _updateUser(UserData userFirestore) async {
    final UserData? userSql =
        await _driftDatabaseRepository.fetchUser(uid: _uid);
    try {
      if (allowPullToInsertInDB(
        dbData: userSql,
        pullData: userFirestore,
      )) {
        await _driftDatabaseRepository.saveUser(
          user: userFirestore,
          triggerUnSyncedStream: false,
        );
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenUserMetadata() async {
    _log.i('Starting user metadata listener');
    final lastSyncTime = DateTime(2000, 1, 1);
    await _userMetadataSubscription?.cancel();
    _userMetadataSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.usersMetadata,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> users) async {
        _countLog(FirebaseDocCollectionType.usersMetadata, users.length);
        final user = users.isNotEmpty ? users.first : null;
        if (user != null) {
          // From here inject the userSegments and docVer to the appBloc
          // If doc version is not same as that in appConfig then don't proceed
          // object formation as that can fail.
          int? docVerFromMap = user['docVer'];
          List<String> userSegmentsFromMap = user['userSegments'] != null
              ? List<String>.from(user['userSegments'])
              : [];
          AppConfig.instance.updateAuthUserDocVerAndSegments(
            Nullable(docVerFromMap),
            Nullable(userSegmentsFromMap),
          );
          if (AppConfig.dbVersion == docVerFromMap) {
            _updateUserMetadata(UsersMetadata.fromMap(user));
          } else {
            _log.w(
              'Rejected listened userMetadata as docVer not match and may fail object formation.',
            );
          }
        } else {
          _log.e('User not found');
        }
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.usersMetadata,
        );
      },
    );
  }

  Future<void> _updateUserMetadata(UsersMetadata userFirestore) async {
    final UsersMetadata? userSql =
        await _driftDatabaseRepository.fetchUserMetadata(uid: userFirestore.id);
    try {
      if (allowPullToInsertInDB(
        dbData: userSql,
        pullData: userFirestore,
      )) {
        await _driftDatabaseRepository.saveUserMetadata(
          userMetadata: userFirestore,
          triggerUnSyncedStream: false,
        );
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _updateSpecialActivities(SpecialActivity specialActivity) async {
    final SpecialActivity? specialActivitySql = await _driftDatabaseRepository
        .fetchSpecialActivity(id: specialActivity.id);
    try {
      if (specialActivitySql == null ||
          (specialActivity.cloudUpdatedAt!.millisecondsSinceEpoch >
              (specialActivitySql.cloudUpdatedAt?.millisecondsSinceEpoch ??
                  0))) {
        await _driftDatabaseRepository.saveSpecialActivity(
          specialActivity: specialActivity,
          triggerUnSyncedStream: false,
        );
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenPublicUser() async {
    _log.i('Starting public user listener');
    final lastSyncTime = DateTime(2000, 1, 1);
    await _publicUserSubscription?.cancel();
    _publicUserSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.publicUsers,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> users) async {
        _countLog(
          FirebaseDocCollectionType.publicUsers,
          users.length,
          'Public',
        );
        final user = users.isNotEmpty ? users.first : null;
        if (user != null) {
          PublicUser publicUser = PublicUser.fromMap(user);
          _updatePublicUser(publicUser);
        } else {
          _log.e('Public User not found');
        }
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.publicUsers,
        );
      },
    );
  }

  Future<void> _updatePublicUser(PublicUser publicUserFirestore) async {
    final PublicUser? publicUserSql = await _driftDatabaseRepository
        .fetchPublicUser(uid: publicUserFirestore.uid);
    try {
      if (allowPullToInsertInDB(
        dbData: publicUserSql,
        pullData: publicUserFirestore,
      )) {
        await _driftDatabaseRepository.savePublicUser(
          publicUser: publicUserFirestore,
          triggerUnSyncedStream: false,
        );
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenUserViewSettings() async {
    _log.i('Starting user view settings listener');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeViewSettings(uid: _uid) ??
            DateTime(2000, 1, 1);
    await _viewSettingsSubscription?.cancel();
    _viewSettingsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.viewSettings,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      getOnlyLastCloudUpdatedAtDoc: true,
    )
        .listen(
      (List<Map<String, dynamic>> viewSetting) async {
        _countLog(FirebaseDocCollectionType.viewSettings, viewSetting.length);
        final viewSettings =
            viewSetting.map((e) => ViewSettings.fromMap(e)).toList();
        if (viewSettings.isNotEmpty) {
          await _updateViewSettings(viewSettings.first);
        }
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.viewSettings,
        );
      },
    );
  }

  Future<void> _updateViewSettings(
    ViewSettings viewSettingsFromFirestore,
  ) async {
    final ViewSettings? viewSettingsSql = await _driftDatabaseRepository
        .fetchViewSettingsById(id: viewSettingsFromFirestore.id);
    try {
      if (allowPullToInsertInDB(
        dbData: viewSettingsSql,
        pullData: viewSettingsFromFirestore,
      )) {
        await _driftDatabaseRepository.saveViewSettings(
          viewSettings: viewSettingsFromFirestore,
          triggerUnSyncedStream: false,
        );
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenUserResources() async {
    _log.i('Starting user userResources listener');
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeUserResources(uid: _uid) ??
        DateTime(2000, 1, 1);
    await _userResourcesSubscription?.cancel();
    _userResourcesSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.userResources,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> userResourcesMap) async {
        _countLog(
          FirebaseDocCollectionType.userResources,
          userResourcesMap.length,
        );
        final userResources =
            userResourcesMap.map((e) => UserResources.fromMap(e)).toList();
        if (userResources.isNotEmpty) {
          await _updateUserResources(userResources.first);
        }
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.userResources,
        );
      },
    );
  }

  Future<void> _updateUserResources(
    UserResources userResourcesFromFirestore,
  ) async {
    final UserResources? userResourcesFromSql = await _driftDatabaseRepository
        .fetchUserResourcesById(id: userResourcesFromFirestore.id);
    bool isSharedListIdsChanged = !const ListEquality().equals(
      userResourcesFromFirestore.shared.sharedLists,
      userResourcesFromSql?.shared.sharedLists,
    );
    bool isSharedNoteIdsChanged = !const ListEquality().equals(
      userResourcesFromFirestore.shared.sharedNotes,
      userResourcesFromSql?.shared.sharedNotes,
    );
    bool isSharedTodoIdsChanged = !const ListEquality().equals(
      userResourcesFromFirestore.shared.sharedTodos,
      userResourcesFromSql?.shared.sharedTodos,
    );
    bool isSharedMoneyTrackerIdsChanged = !const ListEquality().equals(
      userResourcesFromFirestore.shared.sharedMoneyTrackerSetups,
      userResourcesFromSql?.shared.sharedMoneyTrackerSetups,
    );
    bool isSharedHabitSetupIdsChanged = !const ListEquality().equals(
      userResourcesFromFirestore.shared.sharedHabitSetups,
      userResourcesFromSql?.shared.sharedHabitSetups,
    );
    bool isSharedJournalSetupIdsChanged = !const ListEquality().equals(
      userResourcesFromFirestore.shared.sharedJournalSetups,
      userResourcesFromSql?.shared.sharedJournalSetups,
    );
    SharingCleanupFunctions sharingCleanupFunctions = SharingCleanupFunctions(
      databaseRepository: _databaseRepository,
      uid: _uid,
    );
    if (isSharedListIdsChanged) {
      sharingCleanupFunctions.cleanSharedList(
        userSql: userResourcesFromSql,
        userFirestore: userResourcesFromFirestore,
      );
    }
    if (isSharedNoteIdsChanged) {
      sharingCleanupFunctions.cleanSharedNotes(
        userSql: userResourcesFromSql,
        userFirestore: userResourcesFromFirestore,
      );
    }
    if (isSharedTodoIdsChanged) {
      sharingCleanupFunctions.cleanSharedTodos(
        userSql: userResourcesFromSql,
        userFirestore: userResourcesFromFirestore,
      );
    }
    if (isSharedMoneyTrackerIdsChanged) {
      sharingCleanupFunctions.cleanSharedMoneyTrackerSetups(
        userSql: userResourcesFromSql,
        userFirestore: userResourcesFromFirestore,
        listen: _listenMoneySetups,
      );
    }
    if (isSharedHabitSetupIdsChanged) {
      sharingCleanupFunctions.cleanSharedHabitSetups(
        userSql: userResourcesFromSql,
        userFirestore: userResourcesFromFirestore,
        listen: _listenHabitSharedSetups,
      );
    }
    if (isSharedJournalSetupIdsChanged) {
      sharingCleanupFunctions.cleanSharedJournalSetups(
        userSql: userResourcesFromSql,
        userFirestore: userResourcesFromFirestore,
        listen: _listenJournalSharedSetups,
      );
    }
    try {
      if (allowPullToInsertInDB(
        dbData: userResourcesFromSql,
        pullData: userResourcesFromFirestore,
      )) {
        await _driftDatabaseRepository.saveUserResources(
          userResources: userResourcesFromFirestore,
          triggerUnSyncedStream: false,
        );
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenCalendarIntegration() async {
    _log.i('Starting calendar integration listener');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeCalendarInt(uid: _uid) ??
            DateTime(2000, 1, 1);
    await _calendarIntegrationSubscription?.cancel();
    _calendarIntegrationSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.calendarIntegrations,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> calendarInts) async {
        _countLog(
          FirebaseDocCollectionType.calendarIntegrations,
          calendarInts.length,
        );
        await _updateCalendarIntegration(
          calendarInts.map((e) => CalendarIntegration.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.calendarIntegrations,
        );
      },
    );
  }

  Future<void> _updateCalendarIntegration(
    List<CalendarIntegration> calendarInts,
  ) async {
    try {
      List<CalendarIntegration> calendarsToUpdate = [];
      for (var calendar in calendarInts) {
        final sqlCalendar =
            await _driftDatabaseRepository.fetchSpecificCalendarIntegration(
          calendarId: calendar.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlCalendar,
          pullData: calendar,
        )) {
          calendarsToUpdate.add(calendar);
        }
      }
      await _driftDatabaseRepository.addOrUpdateCalendarIntegration(
        calendarIntegration: calendarsToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenToDos() async {
    _log.i('Starting todo listener');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeTodo(uid: _uid) ??
            DateTime(2000, 1, 1);
    await _todosSubscription?.cancel();
    _todosSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.todos,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> todos) async {
        _countLog(FirebaseDocCollectionType.todos, todos.length);
        await _updateSqlTodo(todos.map((e) => Todo.fromMap(e)).toList());
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.todos,
        );
      },
    );
  }

  Future<void> _updateSqlTodo(
    List<Todo> toDos, {
    bool setNotifications = true,
  }) async {
    try {
      List<Todo> todosToUpdate = [];
      for (var todo in toDos) {
        final sqlTodo = await _driftDatabaseRepository.fetchSpecificTodo(
          uid: _uid,
          todoId: todo.id,
        );
        if (sqlTodo == null) {
          todosToUpdate.add(todo);
        } else {
          if (allowPullToInsertInDB(
            dbData: sqlTodo,
            pullData: todo,
          )) {
            if (sqlTodo.attachments.isNotEmpty) {
              for (var item in sqlTodo.attachments) {
                var index = todo.attachments
                    .firstWhereOrNull((element) => item.id == element.id);
                if (index == null) {
                  if (item.status == AttachmentUploadStatus.temporary) {
                    todo.attachments.insert(0, item);
                  }
                }
              }
              todo.attachments.sort((b, a) => a.id!.compareTo(b.id!));
            }
            todosToUpdate.add(todo);
          }
        }
      }
      await _driftDatabaseRepository.addOrUpdateTodo(
        todos: todosToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenSharedToDos() async {
    _log.i('Starting shared todo listener');
    final lastSyncTime = await _driftDatabaseRepository.getLastSyncTimeTodo(
          uid: _uid,
          isShared: true,
        ) ??
        DateTime(2000, 1, 1);
    await _sharedTodosSubscription?.cancel();
    _sharedTodosSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.todos,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      isShared: true,
    )
        .listen(
      (List<Map<String, dynamic>> todos) async {
        _countLog(
          FirebaseDocCollectionType.todos,
          todos.length,
          'Shared',
        );
        await _updateSqlTodo(
          todos.map((e) {
            if (e['docVer'] != DatabaseRepository.currentDbVersion) {
              return Todo.fromMapCompatible(e);
            }
            return Todo.fromMap(e);
          }).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.todos,
        );
      },
    );
  }

  Future<void> _listenNotes() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeNote(uid: _uid) ??
            DateTime(2000, 1, 1);
    await _notesSubscription?.cancel();
    _notesSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.notes,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> notes) async {
        _countLog(FirebaseDocCollectionType.notes, notes.length);
        await _updateSqlNotes(notes.map((e) => Note.fromMap(e)).toList());
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.notes,
        );
      },
    );
  }

  Future<void> _listenSharedNotes() async {
    final lastSyncTime = await _driftDatabaseRepository.getLastSyncTimeNote(
          uid: _uid,
          isShared: true,
        ) ??
        DateTime(2000, 1, 1);
    await _sharedNotesSubscription?.cancel();
    _sharedNotesSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.notes,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      isShared: true,
    )
        .listen(
      (List<Map<String, dynamic>> data) async {
        _countLog(
          FirebaseDocCollectionType.notes,
          data.length,
          'Shared',
        );
        await _updateSqlNotes(
          data.map((e) {
            if (e['docVer'] != DatabaseRepository.currentDbVersion) {
              return Note.fromMapCompatible(e);
            }
            return Note.fromMap(e);
          }).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.notes,
        );
      },
    );
  }

  Future<Map<String, dynamic>?> getEncryptedUserMetaDataMap(User user) async {
    _log.i('Fetching encrypted user metadata');
    Map<String, dynamic>? userMetaDataMap =
        await _firebaseFirestoreRepository.fetchEncryptedUserMetaData(
      uid: user.uid,
    );
    _log.i('Fetched encrypted user metadata');
    return userMetaDataMap;
  }

  Future<void> _updateSqlNotes(List<Note> notes) async {
    try {
      List<Note> notesToUpdate = [];
      for (var note in notes) {
        final sqlNote = await _driftDatabaseRepository.fetchSpecificNote(
          uid: _uid,
          noteId: note.id,
        );
        if (sqlNote == null) {
          notesToUpdate.add(note);
        } else {
          if (allowPullToInsertInDB(
            dbData: sqlNote,
            pullData: note,
          )) {
            if (sqlNote.attachments.isNotEmpty) {
              for (var item in sqlNote.attachments) {
                var index = note.attachments
                    .firstWhereOrNull((element) => item.id == element.id);
                if (index == null) {
                  if (item.status == AttachmentUploadStatus.temporary) {
                    note.attachments.insert(0, item);
                  }
                }
              }
              note.attachments.sort((b, a) => a.id!.compareTo(b.id!));
            }
            notesToUpdate.add(note);
          }
        }
      }
      await _driftDatabaseRepository.addOrUpdateNotes(
        notes: notesToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenHabitSetups() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeHabitSetup(uid: _uid) ??
            DateTime(2000, 1, 1);
    await _habitSetupsSubscription?.cancel();
    _habitSetupsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.habitSetups,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> habitSetup) async {
        _countLog(FirebaseDocCollectionType.habitSetups, habitSetup.length);
        await _updateSqlHabitSetup(
          habitSetup.map((e) => HabitSetup.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.habitSetups,
        );
      },
    );
  }

  Future<void> _listenHabitSharedSetups() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeHabitSetup(
              uid: _uid,
              isShared: true,
            ) ??
            DateTime(2000, 1, 1);
    await _habitSharedSetupsSubscription?.cancel();
    _habitSharedSetupsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.habitSetups,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      isShared: true,
    )
        .listen(
      (List<Map<String, dynamic>> habitSetup) async {
        _countLog(
          FirebaseDocCollectionType.habitSetups,
          habitSetup.length,
          'Shared',
        );
        await _updateSqlHabitSetup(
          habitSetup.map((e) {
            if (e['docVer'] != DatabaseRepository.currentDbVersion) {
              return HabitSetup.fromMapCompatible(e);
            }
            return HabitSetup.fromMap(e);
          }).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.habitSetups,
        );
      },
    );
  }

  Future<void> _updateSqlHabitSetup(
    List<HabitSetup> habitSetups, {
    bool setNotifications = true,
  }) async {
    try {
      List<HabitSetup> habitSetupsToUpdate = [];
      List<HabitSetup> memberConfigUpdates = [];

      for (var habitSetup in habitSetups) {
        final sqlHabitSetup = await _driftDatabaseRepository
            .fetchSpecificHabitSetup(uid: _uid, habitSetupId: habitSetup.id);
        if (sqlHabitSetup == null) {
          habitSetupsToUpdate.add(habitSetup);
        } else {
          if (allowPullToInsertInDB(
            dbData: sqlHabitSetup,
            pullData: habitSetup,
          )) {
            habitSetupsToUpdate.add(habitSetup);
            bool isMemberConfigSame = const DeepCollectionEquality().equals(
              habitSetup.members.toMap(),
              sqlHabitSetup.members.toMap(),
            );
            if (!isMemberConfigSame) {
              memberConfigUpdates.add(habitSetup);
            }
          }
        }
      }

      await _driftDatabaseRepository.addOrUpdateHabitSetup(
        habitSetups: habitSetupsToUpdate,
        triggerUnSyncedStream: false,
      );

      for (var habitSetup in memberConfigUpdates) {
        _driftDatabaseRepository.updateMemberConfigHabitActionsSetupById(
          habitSetup.id,
          habitSetup.members,
          fromPullService: true,
          triggerUnSyncedStream: false,
        );
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenHabitActions() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeHabitActions(uid: _uid) ??
            DateTime(2000, 1, 1);
    await _habitActionSubscription?.cancel();
    _habitActionSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.habitActions,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> habitActions) async {
        _countLog(FirebaseDocCollectionType.habitActions, habitActions.length);
        _updateSqlHabitActions(
          habitActions.map((e) => HabitAction.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.habitActions,
        );
      },
    );
  }

  Future<void> _listenHabitSharedActions() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeHabitActions(
              uid: _uid,
              isShared: true,
            ) ??
            DateTime(2000, 1, 1);
    await _habitSharedActionSubscription?.cancel();
    _habitSharedActionSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.habitActions,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      isShared: true,
    )
        .listen(
      (List<Map<String, dynamic>> habitActions) async {
        _countLog(
          FirebaseDocCollectionType.habitActions,
          habitActions.length,
          'Shared',
        );
        _updateSqlHabitActions(
          habitActions.map((e) {
            final docVer = e['docVer'];
            final currentVer = DatabaseRepository.currentDbVersion;
            if (docVer != currentVer) {
              return HabitAction.fromMapCompatible(e);
            }
            return HabitAction.fromMap(e);
          }).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.habitActions,
        );
      },
    );
  }

  Future<void> _updateSqlHabitActions(List<HabitAction> habitActions) async {
    try {
      List<HabitAction> habitActionsToUpdate = [];
      for (var habitAction in habitActions) {
        final sqlHabitAction =
            await _driftDatabaseRepository.fetchSpecificHabitAction(
          uid: _uid,
          habitActionId: habitAction.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlHabitAction,
          pullData: habitAction,
        )) {
          if (sqlHabitAction != null && sqlHabitAction.attachments.isNotEmpty) {
            for (var item in sqlHabitAction.attachments) {
              var index = habitAction.attachments
                  .firstWhereOrNull((element) => item.id == element.id);
              if (index == null) {
                if (item.status == AttachmentUploadStatus.temporary) {
                  habitAction.attachments.insert(0, item);
                }
              }
            }
            habitAction.attachments.sort((b, a) => a.id!.compareTo(b.id!));
          }
          habitActionsToUpdate.add(habitAction);
        }
      }

      await _driftDatabaseRepository.addOrUpdateHabitAction(
        habitActions: habitActionsToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenJournalSetups() async {
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeJournalSetups(uid: _uid) ??
        DateTime(2000, 1, 1);
    await _journalSetupsSubscription?.cancel();
    _journalSetupsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.journalSetups,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> journalSetups) async {
        _countLog(
          FirebaseDocCollectionType.journalSetups,
          journalSetups.length,
        );
        await _updateSqlJournalSetups(
          journalSetups.map((e) => JournalSetup.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.journalSetups,
        );
      },
    );
  }

  Future<void> _listenJournalSharedSetups() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeJournalSetups(
              uid: _uid,
              isShared: true,
            ) ??
            DateTime(2000, 1, 1);
    await _journalSharedSetupsSubscription?.cancel();
    _journalSharedSetupsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.journalSetups,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      isShared: true,
    )
        .listen(
      (List<Map<String, dynamic>> journalSetups) async {
        _countLog(
          FirebaseDocCollectionType.journalSetups,
          journalSetups.length,
          'Shared',
        );
        await _updateSqlJournalSetups(
          journalSetups.map((e) {
            if (e['docVer'] != DatabaseRepository.currentDbVersion) {
              return JournalSetup.fromMapCompatible(e);
            }
            return JournalSetup.fromMap(e);
          }).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.journalSetups,
        );
      },
    );
  }

  Future<void> _updateSqlJournalSetups(
    List<JournalSetup> journalSetups, {
    bool setNotifications = true,
  }) async {
    try {
      List<JournalSetup> journalSetupsToUpdate = [];
      List<JournalSetup> memberConfigUpdates = [];

      for (var journalSetup in journalSetups) {
        final sqlJournalSetup =
            await _driftDatabaseRepository.fetchSpecificJournalSetup(
          uid: _uid,
          journalSetupId: journalSetup.id,
        );
        if (sqlJournalSetup == null) {
          journalSetupsToUpdate.add(journalSetup);
        } else {
          if (allowPullToInsertInDB(
            dbData: sqlJournalSetup,
            pullData: journalSetup,
          )) {
            journalSetupsToUpdate.add(journalSetup);
            bool isMemberConfigSame = const DeepCollectionEquality().equals(
              journalSetup.members.toMap(),
              sqlJournalSetup.members.toMap(),
            );
            if (!isMemberConfigSame) {
              memberConfigUpdates.add(journalSetup);
            }
          }
        }
      }

      await _driftDatabaseRepository.addOrUpdateJournalSetup(
        journalSetups: journalSetupsToUpdate,
        triggerUnSyncedStream: false,
      );

      for (var journalSetup in memberConfigUpdates) {
        _driftDatabaseRepository.updateMemberConfigJournalActionsSetupById(
          journalSetup.id,
          journalSetup.members,
          triggerUnSyncedStream: false,
          fromPullService: true,
        );
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenJournalActions() async {
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeJournalActions(uid: _uid) ??
        DateTime(2000, 1, 1);
    await _journalActionSubscription?.cancel();
    _journalActionSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.journalActions,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> journalActions) async {
        _countLog(
          FirebaseDocCollectionType.journalActions,
          journalActions.length,
        );
        _updateSqlJournalActions(
          journalActions.map((e) => JournalAction.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.journalActions,
        );
      },
    );
  }

  Future<void> _listenJournalSharedActions() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeJournalActions(
              uid: _uid,
              isShared: true,
            ) ??
            DateTime(2000, 1, 1);
    await _journalSharedActionSubscription?.cancel();
    _journalSharedActionSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.journalActions,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      isShared: true,
    )
        .listen(
      (List<Map<String, dynamic>> journalActions) async {
        _countLog(
          FirebaseDocCollectionType.journalActions,
          journalActions.length,
          'Shared',
        );
        _updateSqlJournalActions(
          journalActions.map((e) {
            final docVer = e['docVer'];
            final currentVer = DatabaseRepository.currentDbVersion;
            if (docVer != currentVer) {
              return JournalAction.fromMapCompatible(e);
            }
            return JournalAction.fromMap(e);
          }).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.journalActions,
        );
      },
    );
  }

  Future<void> _updateSqlJournalActions(
    List<JournalAction> journalActions,
  ) async {
    try {
      List<JournalAction> journalActionsToUpdate = [];
      for (var journalAction in journalActions) {
        final sqlJournalAction =
            await _driftDatabaseRepository.fetchSpecificJournalAction(
          uid: _uid,
          journalActionId: journalAction.id,
        );
        if (sqlJournalAction == null) {
          journalActionsToUpdate.add(journalAction);
        } else {
          if (allowPullToInsertInDB(
            dbData: sqlJournalAction,
            pullData: journalAction,
          )) {
            if (sqlJournalAction.attachments.isNotEmpty) {
              for (var item in sqlJournalAction.attachments) {
                var index = journalAction.attachments
                    .firstWhereOrNull((element) => item.id == element.id);
                if (index == null) {
                  if (item.status == AttachmentUploadStatus.temporary) {
                    journalAction.attachments.insert(0, item);
                  }
                }
              }
              journalAction.attachments.sort((b, a) => a.id!.compareTo(b.id!));
            }
            journalActionsToUpdate.add(journalAction);
          }
        }
      }
      await _driftDatabaseRepository.addOrUpdateJournalActions(
        journalActions: journalActionsToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenLists() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeList(uid: _uid) ??
            DateTime(2000, 1, 1);
    await _listDataSubscription?.cancel();
    _listDataSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.lists,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> listData) async {
        _countLog(FirebaseDocCollectionType.lists, listData.length);
        await _updateSqlListData(
          listData.map((e) => ListData.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.lists,
        );
      },
    );
  }

  Future<void> _listenSharedLists() async {
    final lastSyncTime = await _driftDatabaseRepository.getLastSyncTimeList(
          uid: _uid,
          isShared: true,
        ) ??
        DateTime(2000, 1, 1);
    await _sharedListDataSubscription?.cancel();
    _sharedListDataSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.lists,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      isShared: true,
    )
        .listen(
      (List<Map<String, dynamic>> listData) async {
        _countLog(
          FirebaseDocCollectionType.lists,
          listData.length,
          'Shared',
        );
        _updateSqlListData(
          listData.map((e) {
            if (e['docVer'] != DatabaseRepository.currentDbVersion) {
              return ListData.fromMapCompatible(e);
            }
            return ListData.fromMap(e);
          }).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.lists,
        );
      },
    );
  }

  Future<void> _updateSqlListData(List<ListData> lists) async {
    try {
      List<ListData> listToUpdate = [];
      for (var listData in lists) {
        final sqlList = await _driftDatabaseRepository.fetchSpecificListData(
          uid: _uid,
          listDataId: listData.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlList,
          pullData: listData,
        )) {
          listToUpdate.add(listData);
        }
      }
      await _driftDatabaseRepository.addOrUpdateLists(
        listData: listToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenMessages() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeMessage(uid: _uid) ??
            DateTime(2000, 1, 1);
    await _chatMessagesSubscription?.cancel();
    _chatMessagesSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.chatMessages,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> chatMessages) async {
        _countLog(FirebaseDocCollectionType.chatMessages, chatMessages.length);
        _updateSqlChatMsg(
          chatMessages.map((e) => ChatMessage.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.chatMessages,
        );
      },
    );
  }

  Future<void> _listenChatUser() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeChatUser(uid: _uid) ??
            DateTime(2000, 1, 1);
    await _chatUsersSubscription?.cancel();
    _chatUsersSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.chatUsers,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> chatUsers) async {
        _countLog(FirebaseDocCollectionType.chatUsers, chatUsers.length);
        _updateSqlChatUser(
          chatUsers.map((e) => ChatUser.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.chatUsers,
        );
      },
    );
  }

  Future<void> _updateSqlChatMsg(List<ChatMessage> chatMessages) async {
    try {
      List<ChatMessage> chatMessagesToUpdate = [];
      for (var msgData in chatMessages) {
        final sqlMsg = await _driftDatabaseRepository.fetchSpecificChatMessage(
          chatMessageId: msgData.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlMsg,
          pullData: msgData,
          dontUseLocalUpdatedAt:
              true, // Skip localUpdatedAt check for chat messages due to translation timestamp updates
        )) {
          chatMessagesToUpdate.add(msgData);
        }
      }
      await _driftDatabaseRepository.addorUpdateChatMessage(
        chatMessages: chatMessagesToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _updateSqlChatUser(List<ChatUser> chatUsers) async {
    try {
      List<ChatUser> chatUsersToUpdate = [];
      for (var chatUserData in chatUsers) {
        final sqlMsg = await _driftDatabaseRepository.fetchSpecificChatUser(
          chaUserId: chatUserData.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlMsg,
          pullData: chatUserData,
        )) {
          chatUsersToUpdate.add(chatUserData);
        }
      }
      await _driftDatabaseRepository.addorUpdateChatUser(
        chatUsers: chatUsers,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenSnippets() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeSnippet() ??
            DateTime(2000, 1, 1);
    await _snippetsSubscription?.cancel();
    _snippetsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.snippets,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      addUIdFilter: false,
    )
        .listen(
      (List<Map<String, dynamic>> snippets) async {
        _countLog(FirebaseDocCollectionType.snippets, snippets.length);
        _updateSqlSnippetsData(
          snippets.map((e) => Snippet.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.snippets,
        );
      },
    );
  }

  Future<void> _updateSqlSnippetsData(List<Snippet> snippets) async {
    try {
      for (var snippet in snippets) {
        final sqlSnippet = await _driftDatabaseRepository.fetchSpecificSnippet(
          snippetId: snippet.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlSnippet,
          pullData: snippet,
        )) {
          await _driftDatabaseRepository.addOrUpdateSnippet(
            snippet: snippet,
            triggerUnSyncedStream: false,
          );
        }
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenViewedSnippets() async {
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeViewedSnippet(uid: _uid) ??
        DateTime(2000, 1, 1);
    await _viewedSnippetsSubscription?.cancel();
    _viewedSnippetsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.snippets,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> viewedSnippets) async {
        _countLog(
          FirebaseDocCollectionType.viewedSnippets,
          viewedSnippets.length,
        );
        _updateSqlViewedSnippetsData(
          viewedSnippets.map((e) => ViewedSnippet.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.viewedSnippets,
        );
      },
    );
  }

  Future<void> _updateSqlViewedSnippetsData(
    List<ViewedSnippet> snippets,
  ) async {
    try {
      for (var viewedSnippet in snippets) {
        final sqlViewedSnippet =
            await _driftDatabaseRepository.fetchSpecificViewedSnippet(
          viewedSnippetId: viewedSnippet.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlViewedSnippet,
          pullData: viewedSnippet,
        )) {
          await _driftDatabaseRepository.addOrUpdateViewedSnippet(
            viewedSnippet: viewedSnippet,
            triggerUnSyncedStream: false,
          );
        }
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenReleaseConfigs() async {
    _releaseConfigsSubscription =
        _firebaseFirestoreRepository.listenReleaseConfigs().listen(
      (List<ReleaseConfigModel?> releaseConfig) async {
        releaseConfig.removeWhere((element) => element == null);
        List<ReleaseConfigModel> list =
            List<ReleaseConfigModel>.from(releaseConfig);
        _countLog(FirebaseDocCollectionType.releaseConfigs, list.length);
        _updateSqlReleaseConfigsData(list);
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.releaseConfigs,
        );
      },
    );
  }

  void _updateSqlReleaseConfigsData(
    List<ReleaseConfigModel> configs,
  ) async {
    try {
      final list = await _driftDatabaseRepository.getAllReleaseConfigs();
      List<ReleaseConfigModel> addConfigs = [];
      for (var config in configs) {
        final List<String> deleteList = [];
        final result = list.where(
          (item) =>
              item.rid == config.rid &&
              item.dbVersion == config.dbVersion &&
              item.id != config.id,
        );
        for (var item in result) {
          deleteList.add(item.id!);
        }
        if (deleteList.isNotEmpty) {
          await _driftDatabaseRepository.deleteReleaseConfigModel(
            idList: deleteList,
          );
        }
        final sqlReleaseConfig = config.id != null
            ? await _driftDatabaseRepository.fetchSpecificReleaseConfig(
                id: config.id!,
              )
            : null;
        if (sqlReleaseConfig == null) {
          addConfigs.add(config);
        } else {
          if (config.cloudUpdatedAt!.millisecondsSinceEpoch >
              (sqlReleaseConfig.cloudUpdatedAt?.millisecondsSinceEpoch ?? 0)) {
            addConfigs.add(config);
          }
        }
      }
      await _driftDatabaseRepository.addOrUpdateReleaseConfigModel(
        releaseConfigModel: addConfigs,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenSpecialActivities() async {
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeSpecialActivity(uid: _uid) ??
        DateTime(2000, 1, 1);

    _specialActivitiesSubscription?.cancel();

    _specialActivitiesSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.specialActivities,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      addUIdFilter: false,
    )
        .listen(
      (List<Map<String, dynamic>> specialActivitiesMaps) async {
        _countLog(
          FirebaseDocCollectionType.specialActivities,
          specialActivitiesMaps.length,
        );
        List<SpecialActivity> specialActivities = specialActivitiesMaps
            .map((e) => SpecialActivity.fromMap(e))
            .toList();
        for (var specialActivity in specialActivities) {
          await _updateSpecialActivities(specialActivity);
        }
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.specialActivities,
        );
      },
    );
  }

  Future<void> _listenInAppNotifications() async {
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeInAppNotification(uid: _uid) ??
        DateTime(2000, 1, 1);
    await _inAppNotificationsSubscription?.cancel();
    _inAppNotificationsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.inAppNotifications,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> inAppNotifications) async {
        _countLog(
          FirebaseDocCollectionType.inAppNotifications,
          inAppNotifications.length,
        );
        _updateSqlInAppNotifications(
          inAppNotifications.map((e) => InAppNotification.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.inAppNotifications,
        );
      },
    );
  }

  Future<void> _updateSqlInAppNotifications(
    List<InAppNotification> inAppNotifications,
  ) async {
    try {
      List<InAppNotification> inAppNotificationsToUpdate = [];
      for (var inAppNotification in inAppNotifications) {
        final sqlInAppNotification =
            await _driftDatabaseRepository.fetchSpecificInAppNotification(
          uid: _uid,
          inAppNotificationId: inAppNotification.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlInAppNotification,
          pullData: inAppNotification,
        )) {
          inAppNotificationsToUpdate.add(inAppNotification);
        }
      }
      await _driftDatabaseRepository.addOrUpdateInAppNotifications(
        inAppNotifications: inAppNotificationsToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenMoneySetups() async {
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeMoneyTrackerSetup(uid: _uid) ??
        DateTime(2000, 1, 1);
    await _moneySetupsSubscription?.cancel();
    _moneySetupsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.moneyTrackerSetups,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> moneySetups) async {
        _countLog(
          FirebaseDocCollectionType.moneyTrackerSetups,
          moneySetups.length,
        );
        await _updateSqlMoneySetups(
          moneySetups.map((e) => MoneyTrackerSetup.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.moneyTrackerSetups,
        );
      },
    );
  }

  Future<void> _listenSharedMoneySetups() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeMoneyTrackerSetup(
              uid: _uid,
              isShared: true,
            ) ??
            DateTime(2000, 1, 1);
    await _moneySharedSetupsSubscription?.cancel();
    _moneySharedSetupsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.moneyTrackerSetups,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      isShared: true,
    )
        .listen(
      (List<Map<String, dynamic>> moneySetups) async {
        _countLog(
          FirebaseDocCollectionType.moneyTrackerSetups,
          moneySetups.length,
          'Shared',
        );
        await _updateSqlMoneySetups(
          moneySetups.map((e) {
            if (e['docVer'] != DatabaseRepository.currentDbVersion) {
              return MoneyTrackerSetup.fromMapCompatible(e);
            }
            return MoneyTrackerSetup.fromMap(e);
          }).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.moneyTrackerSetups,
        );
      },
    );
  }

  Future<void> _updateSqlMoneySetups(
    List<MoneyTrackerSetup> moneySetups,
  ) async {
    try {
      List<MoneyTrackerSetup> moneySetupsToUpdate = [];
      List<MoneyTrackerSetup> memberConfigUpdates = [];

      for (var moneySetup in moneySetups) {
        final sqlMoneySetup =
            await _driftDatabaseRepository.fetchSpecificMoneyTrackerSetup(
          uid: _uid,
          setupId: moneySetup.id,
        );
        if (sqlMoneySetup == null) {
          moneySetupsToUpdate.add(moneySetup);
        } else {
          if (allowPullToInsertInDB(
            dbData: sqlMoneySetup,
            pullData: moneySetup,
          )) {
            moneySetupsToUpdate.add(moneySetup);
            bool isMemberConfigSame = const DeepCollectionEquality().equals(
              sqlMoneySetup.members.toMap(),
              moneySetup.members.toMap(),
            );
            if (!isMemberConfigSame) {
              memberConfigUpdates.add(moneySetup);
            }
          }
        }
      }

      await _driftDatabaseRepository.addOrUpdateMoneyTrackerSetups(
        setups: moneySetupsToUpdate,
        triggerUnSyncedStream: false,
      );

      for (var moneySetup in memberConfigUpdates) {
        await _driftDatabaseRepository
            .updateMemberConfigMoneyTrackerActionsSetupById(
          moneySetup.id,
          moneySetup.members,
          triggerUnSyncedStream: false,
          fromPullService: true,
        );
      }
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenMoneyTransactions() async {
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeMoneyTrackerTransaction(uid: _uid) ??
        DateTime(2000, 1, 1);
    await _moneyTransactionsSubscription?.cancel();
    _moneyTransactionsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.moneyTrackerTransactions,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> moneyTracker) async {
        _countLog(
          FirebaseDocCollectionType.moneyTrackerTransactions,
          moneyTracker.length,
        );
        _updateSqlMoneyTransaction(
          moneyTracker.map((e) => MoneyTrackerTransaction.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.moneyTrackerTransactions,
        );
      },
    );
  }

  Future<void> _listenSharedMoneyTransactions() async {
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeMoneyTrackerTransaction(
              uid: _uid,
              isShared: true,
            ) ??
            DateTime(2000, 1, 1);
    await _moneySharedTransactionsSubscription?.cancel();
    _moneySharedTransactionsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.moneyTrackerTransactions,
      uid: _uid,
      lastSyncDate: lastSyncTime,
      isShared: true,
    )
        .listen(
      (List<Map<String, dynamic>> moneyTracker) async {
        _countLog(
          FirebaseDocCollectionType.moneyTrackerTransactions,
          moneyTracker.length,
          'Shared',
        );
        _updateSqlMoneyTransaction(
          moneyTracker.map((e) {
            if (e['docVer'] != DatabaseRepository.currentDbVersion) {
              return MoneyTrackerTransaction.fromMapCompatible(e);
            }
            return MoneyTrackerTransaction.fromMap(e);
          }).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.moneyTrackerTransactions,
        );
      },
    );
  }

  Future<void> _updateSqlMoneyTransaction(
    List<MoneyTrackerTransaction> moneyTrackerTransactions,
  ) async {
    try {
      List<MoneyTrackerTransaction> moneyTrackerTransactionToUpdate = [];
      for (var transaction in moneyTrackerTransactions) {
        final sqlMoneyTrackerTransactions =
            await _driftDatabaseRepository.fetchSpecificMoneyTrackerTransaction(
          uid: _uid,
          id: transaction.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlMoneyTrackerTransactions,
          pullData: transaction,
        )) {
          moneyTrackerTransactionToUpdate.add(transaction);
        }
      }
      await _driftDatabaseRepository.addOrUpdateMoneyTrackerTransactions(
        transactions: moneyTrackerTransactionToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenCalendarSetups() async {
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeCalendarSetup(uid: _uid) ??
        DateTime(2000, 1, 1);
    await _calendarSetupsSubscription?.cancel();

    _calendarSetupsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.calendarEventSetups,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> events) async {
        _countLog(FirebaseDocCollectionType.calendarEventSetups, events.length);
        await _updateSqlCalendarSetups(
          events.map((e) => CalendarEventSetup.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.calendarEventSetups,
        );
      },
    );
  }

  Future<void> _updateSqlCalendarSetups(
    List<CalendarEventSetup> events,
  ) async {
    try {
      List<CalendarEventSetup> calendarEventsToUpdate = [];
      for (var event in events) {
        final sqlEvent =
            await _driftDatabaseRepository.fetchSpecificCalendarSetup(
          calendarSetupId: event.id,
        );
        if (allowPullToInsertInDB(
          dbData: sqlEvent,
          pullData: event,
        )) {
          calendarEventsToUpdate.add(event);
        }
      }
      await _driftDatabaseRepository.addOrUpdateCalendarSetups(
        calendarSetup: calendarEventsToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> _listenCalendarActions() async {
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeCalendarAction(uid: _uid) ??
        DateTime(2000, 1, 1);
    await _calendarActionsSubscription?.cancel();

    _calendarActionsSubscription = _firebaseFirestoreRepository
        .listenCollection(
      collectionName: FirebaseDocCollectionType.calendarEventActions,
      uid: _uid,
      lastSyncDate: lastSyncTime,
    )
        .listen(
      (List<Map<String, dynamic>> events) async {
        _countLog(
          FirebaseDocCollectionType.calendarEventActions,
          events.length,
        );
        await _updateSqlCalendarActions(
          events.map((e) => CalendarEventAction.fromMap(e)).toList(),
        );
      },
      onError: (error) {
        _firebaseFirestoreRepository.onFirestoreListenerError(
          error,
          FirebaseDocCollectionType.calendarEventActions,
        );
      },
    );
  }

  Future<void> _updateSqlCalendarActions(
    List<CalendarEventAction> events,
  ) async {
    try {
      List<CalendarEventAction> calendarEventsToUpdate = [];
      for (var calendarAction in events) {
        final sqlEvent =
            await _driftDatabaseRepository.fetchSpecificCalendarAction(
          eventId: calendarAction.id,
        );

        if (allowPullToInsertInDB(
          dbData: sqlEvent,
          pullData: calendarAction,
        )) {
          calendarEventsToUpdate.add(calendarAction);
        }
      }
      await _driftDatabaseRepository.addOrUpdateCalendarActions(
        calendarActions: calendarEventsToUpdate,
        triggerUnSyncedStream: false,
      );
    } catch (e) {
      _log.e(e);
    }
  }

  Future<void> stopListeners() async {
    _log.i('Stopping pull service');
    _stopTimer();
    await Future.wait<void>([
      _userSubscription?.cancel() ?? Future.value(),
      _userMetadataSubscription?.cancel() ?? Future.value(),
      _publicUserSubscription?.cancel() ?? Future.value(),
      _todosSubscription?.cancel() ?? Future.value(),
      _sharedTodosSubscription?.cancel() ?? Future.value(),
      _notesSubscription?.cancel() ?? Future.value(),
      _sharedNotesSubscription?.cancel() ?? Future.value(),
      _habitSetupsSubscription?.cancel() ?? Future.value(),
      _habitActionSubscription?.cancel() ?? Future.value(),
      _journalSetupsSubscription?.cancel() ?? Future.value(),
      _journalActionSubscription?.cancel() ?? Future.value(),
      _listDataSubscription?.cancel() ?? Future.value(),
      _sharedListDataSubscription?.cancel() ?? Future.value(),
      _chatMessagesSubscription?.cancel() ?? Future.value(),
      _chatUsersSubscription?.cancel() ?? Future.value(),
      _issueReportsSubscription?.cancel() ?? Future.value(),
      _snippetsSubscription?.cancel() ?? Future.value(),
      _viewedSnippetsSubscription?.cancel() ?? Future.value(),
      _inAppNotificationsSubscription?.cancel() ?? Future.value(),
      _moneySetupsSubscription?.cancel() ?? Future.value(),
      _moneySharedSetupsSubscription?.cancel() ?? Future.value(),
      _moneyTransactionsSubscription?.cancel() ?? Future.value(),
      _moneySharedTransactionsSubscription?.cancel() ?? Future.value(),
      _calendarSetupsSubscription?.cancel() ?? Future.value(),
      _calendarActionsSubscription?.cancel() ?? Future.value(),
      _calendarIntegrationSubscription?.cancel() ?? Future.value(),
      _habitSharedSetupsSubscription?.cancel() ?? Future.value(),
      _journalSharedSetupsSubscription?.cancel() ?? Future.value(),
      _habitSharedActionSubscription?.cancel() ?? Future.value(),
      _journalSharedActionSubscription?.cancel() ?? Future.value(),
    ]);
    _isRunning = false;
  }

  void _countLog(
    FirebaseDocCollectionType collectionName,
    int count, [
    String collectionExtra = '',
  ]) {
    final prefix = collectionExtra.isNotEmpty ? '$collectionExtra ' : '';
    final logMessage =
        'Received_$prefix${collectionName.name}_${count}_records';

    _logDEBUG.d(logMessage);

    if (sharedPrefsClient == null) return;

    sharedPrefsClient!.setLastSync(DateTime.now().toString());

    if (sharedPrefsClient!.getShowDataPullSnackBarsStatus()) {
      try {
        showMeSnackbar(
          MeString.fromVariable(
            '$logMessage by cloud DB listeners',
          ),
        );
      } catch (e) {
        // Intentionally left empty
      }
    }
  }

  // Syncs all collections from last sync time. Mainly used by background service
  Future<void> syncAll(
    String uid,
    DriftDatabaseRepository driftDatabaseRepository,
    UserData? userData,
  ) async {
    _log.i('syncing all data');
    _driftDatabaseRepository = driftDatabaseRepository;
    _uid = uid;
    await Future.wait([
      _syncUser(),
      _syncUserMetadata(),
      _syncSpecialActivities(),
      _syncPublicUser(),
      _syncViewSettings(),
      _syncUserResources(),
      _syncTodos(),
      _syncHabitSetups(),
      _syncHabitActions(),
      _syncJournalSetups(),
      _syncJournalActions(),
      _syncMessages(),
      _syncChatUsers(),
      // _syncSnippets(),
      // _syncViewedSnippets(),
      _syncInAppNotifications(),
      _syncMoneyTrackerSetups(),
      _syncMoneyTrackerTransactions(),
      _syncCalendarEventSetups(),
      _syncCalendarEventActions(),
      //private and shared
      _syncLists(),
      _syncNotes(),
    ]);
  }

  // Used to get all data in one shot after login
  Future<UserData> getAll(
    User user,
    DriftDatabaseRepository driftDatabaseRepository, {
    Map<String, dynamic>? rawUserData,
  }) async {
    final stopwatch = Stopwatch()..start();
    _driftDatabaseRepository = driftDatabaseRepository;
    UserData? userData;
    Map<String, dynamic>? userDataMap = rawUserData ??
        (await getRawUserDataWithKeys(user, refreshSecret: true)).$1;
    if (userDataMap == null) {
      throw Exception('Unable to getAll as userDataMap is null');
    }
    await MeEncryption().initializeMeEncryption(user.uid);
    userDataMap = await MeBaseModel.decryptFirestoreData(
      userDataMap,
      user.uid,
    );
    userData = (UserData.fromMap(userDataMap));
    final backupSyncStart = DateTime.now();
    final List<MeBaseModel> allUserData =
        await FirebaseStorageRepository().downloadBackedUpUserData(user.uid);
    _uid = user.uid;
    if (allUserData.isNotEmpty) {
      _log.i('received data from backup');
      await _driftDatabaseRepository.saveUser(
        user: userData,
        triggerUnSyncedStream: false,
      );
      await _updateAllData(allUserData);

      Map<TrackAction?, int> syncedActionsItems = {};

      for (MeBaseModel item in allUserData) {
        final trackAction =
            TrackAction.getTrackActionsFromFirestoreCollectionName(
          firestoreCollectionName: item.docCollection.name,
        );

        syncedActionsItems[trackAction] =
            (syncedActionsItems[trackAction] ?? 0) + 1;
      }

      // Send backup sync complete event for successful backup restore
      EventFormation().sendBackupSyncEvent(
        syncedActionsItems: syncedActionsItems,
        syncStatus: SyncStatus.success,
        errorCode: SyncErrorCode.noError,
        start: backupSyncStart,
      );
      _log.i('sent backup sync complete event for ${allUserData.length} items');
    } else {
      // Send backup sync complete event for no backup data found
      EventFormation().sendBackupSyncEvent(
        syncedActionsItems: {},
        syncStatus: SyncStatus.success,
        errorCode: SyncErrorCode.noError,
        start: backupSyncStart,
      );
      _log.i('sent backup sync complete event - no backup data found');
    }
    Future.wait([
      getMyAndSharedData(
        FirebaseDocCollectionType.lists,
        (e) => ListData.fromMap(e),
      ),
      getMyAndSharedData(
        FirebaseDocCollectionType.notes,
        (e) => Note.fromMap(e),
      ),
      getMyAndSharedData(
        FirebaseDocCollectionType.todos,
        (e) => Todo.fromMap(e),
      ),
      getMyAndSharedData(
        FirebaseDocCollectionType.moneyTrackerSetups,
        (e) => MoneyTrackerSetup.fromMap(e),
      ),
      getMyAndSharedData(
        FirebaseDocCollectionType.moneyTrackerTransactions,
        (e) => MoneyTrackerTransaction.fromMap(e),
      ),
      getMyAndSharedData(
        FirebaseDocCollectionType.habitSetups,
        (e) => HabitSetup.fromMap(e),
      ),
      getMyAndSharedData(
        FirebaseDocCollectionType.journalSetups,
        (e) => JournalSetup.fromMap(e),
      ),
      getMyAndSharedData(
        FirebaseDocCollectionType.habitActions,
        (e) => HabitAction.fromMap(e),
      ),
      getMyAndSharedData(
        FirebaseDocCollectionType.journalActions,
        (e) => JournalAction.fromMap(e),
      ),
    ]);
    await syncAll(user.uid, driftDatabaseRepository, userData);
    _log.i('Time taken for get all: ${stopwatch.elapsed}');
    return userData;
  }

  Future<void> _updateAllData(List<MeBaseModel> allUserData) async {
    allUserData.removeWhere(
      (element) => element.docCollection == FirebaseDocCollectionType.users,
    );
    await _driftDatabaseRepository.saveBaseModelData(data: allUserData);
    _log.i('saved all data from backup');
  }

  // Future<void> _getUser() async {
  //   _log.i('getting user data');
  //   final user =
  //       await _firebaseFirestoreRepository.fetchUpdatedUserData(uid: _uid);
  //   if (user != null) {
  //     _driftDatabaseRepository.saveUser(
  //       user: UserData.fromMap(user),
  //       triggerUnSyncedStream: false,
  //     );
  //   }
  // }

  // we are doing this below function because the backup currently doesnt save the shared list
  Future<void> getMyAndSharedData<T extends MeBaseModel>(
    // List<MeBaseModel> allUserData,
    FirebaseDocCollectionType collectionType,
    T Function(Map<String, dynamic>) fromMap,
  ) async {
    _log.i('getting all my and shared ${collectionType.name}');

    final sharedData = await _firebaseFirestoreRepository.getSharedData(
      _uid,
      DateTime(2000, 1, 1),
      collectionType,
    );

    // final ownedData = allUserData
    //     .where((item) => item.docCollection == collectionType)
    //     .toList();

    final sharedDataMapped = sharedData.map((e) => fromMap(e)).toList();
    // final combinedData = [...ownedData, ...sharedDataMapped];

    _driftDatabaseRepository.saveBaseModelData(data: sharedDataMapped);
  }

  Future<void> _syncUser() async {
    _log.i('Syncing user');
    final users = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.users,
      _uid,
    );
    if (users.isEmpty) return;
    await _updateUser(users.map((e) => UserData.fromMap(e)).toList().first);
  }

  Future<void> _syncUserMetadata() async {
    _log.i('Syncing user metadata');
    final users = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.usersMetadata,
      _uid,
    );
    if (users.isEmpty) return;
    await _updateUserMetadata(
      users.map((e) => UsersMetadata.fromMap(e)).toList().first,
    );
  }

  Future<void> _syncSpecialActivities() async {
    _log.i('Syncing special activities');

    final specialActivities = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.specialActivities,
      _uid,
      addUIdFilter: false,
    );
    if (specialActivities.isEmpty) return;
    await _updateSpecialActivities(
      specialActivities.map((e) => SpecialActivity.fromMap(e)).toList().first,
    );
  }

  Future<void> _syncPublicUser() async {
    _log.i('Syncing public user');
    final publicUser = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.publicUsers,
      _uid,
    );
    if (publicUser.isEmpty) return;
    PublicUser? pbUser =
        publicUser.map((e) => PublicUser.fromMap(e)).toList().first;
    await _updatePublicUser(pbUser);
  }

  Future<void> _syncViewSettings() async {
    _log.i('Syncing user view settings');
    final users = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.viewSettings,
      _uid,
      getOnlyLastCloudUpdatedAtDoc: true,
    );
    if (users.isEmpty) return;
    await _updateViewSettings(
      users.map((e) => ViewSettings.fromMap(e)).toList().first,
    );
  }

  Future<void> _syncUserResources() async {
    _log.i('Syncing user resources');
    final users = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.userResources,
      _uid,
    );
    if (users.isEmpty) return;
    UserResources userResource =
        users.map((e) => UserResources.fromMap(e)).toList().first;
    await _updateUserResources(userResource);
  }

  Future<void> _syncTodos() async {
    _log.i('Syncing todos');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeTodo(uid: _uid) ??
            DateTime(2000, 1, 1);
    final todos = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.todos,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlTodo(
      todos.map((e) => Todo.fromMap(e)).toList(),
      setNotifications: false,
    );
  }

  Future<void> _syncNotes() async {
    _log.i('Syncing notes');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeNote(uid: _uid) ??
            DateTime(2000, 1, 1);
    final notes = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.notes,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlNotes(notes.map((e) => Note.fromMap(e)).toList());
  }

  Future<void> _syncHabitSetups() async {
    _log.i('Syncing habit setups');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeHabitSetup(uid: _uid) ??
            DateTime(2000, 1, 1);
    final habitSetups = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.habitSetups,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlHabitSetup(
      habitSetups.map((e) => HabitSetup.fromMap(e)).toList(),
      setNotifications: false,
    );
  }

  Future<void> _syncHabitActions() async {
    _log.i('Syncing habit actions');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeHabitActions(uid: _uid) ??
            DateTime(2000, 1, 1);
    final habitActions = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.habitActions,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlHabitActions(
      habitActions.map((e) => HabitAction.fromMap(e)).toList(),
    );
  }

  Future<void> _syncJournalSetups() async {
    _log.i('Syncing journal setups');
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeJournalSetups(uid: _uid) ??
        DateTime(2000, 1, 1);
    final journalSetups = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.journalSetups,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlJournalSetups(
      journalSetups.map((e) => JournalSetup.fromMap(e)).toList(),
      setNotifications: false,
    );
  }

  Future<void> _syncJournalActions() async {
    _log.i('Syncing journal actions');
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeJournalActions(uid: _uid) ??
        DateTime(2000, 1, 1);
    final journalActions = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.journalActions,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlJournalActions(
      journalActions.map((e) => JournalAction.fromMap(e)).toList(),
    );
  }

  Future<void> _syncLists() async {
    _log.i('Syncing lists');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeList(uid: _uid) ??
            DateTime(2000, 1, 1);
    final lists = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.lists,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlListData(lists.map((e) => ListData.fromMap(e)).toList());
  }

  Future<void> _syncMessages() async {
    _log.i('Syncing messages');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeMessage(uid: _uid) ??
            DateTime(2000, 1, 1);
    final messages = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.chatMessages,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlChatMsg(
      messages.map((e) => ChatMessage.fromMap(e)).toList(),
    );
  }

  Future<void> _syncChatUsers() async {
    _log.i('Syncing chatUser');
    final lastSyncTime =
        await _driftDatabaseRepository.getLastSyncTimeChatUser(uid: _uid) ??
            DateTime(2000, 1, 1);
    final messages = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.chatUsers,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlChatUser(
      messages.map((e) => ChatUser.fromMap(e)).toList(),
    );
  }

  Future<void> _syncInAppNotifications() async {
    _log.i(
      'Syncing in app notifications - fetching latest 20 with deletedAt null',
    );
    final inAppNotifications = await _firebaseFirestoreRepository.getPaginated(
      FirebaseDocCollectionType.inAppNotifications,
      _uid,
      limit: 20,
      lastCreatedAt: null, // null means start from the newest
    );
    await _updateSqlInAppNotifications(
      inAppNotifications.map((e) => InAppNotification.fromMap(e)).toList(),
    );
  }

  Future<void> fetchPaginatedInAppNotifications({
    required String uid,
    required int limit,
    DateTime? lastCreatedAt,
  }) async {
    _log.i('Fetching paginated in app notifications');
    final notifications = await _firebaseFirestoreRepository.getPaginated(
      FirebaseDocCollectionType.inAppNotifications,
      uid,
      limit: limit,
      lastCreatedAt: lastCreatedAt,
    );
    await _updateSqlInAppNotifications(
      notifications.map((e) => InAppNotification.fromMap(e)).toList(),
    );
  }

  Future<void> _syncMoneyTrackerSetups() async {
    _log.i('Syncing money tracker setups');
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeMoneyTrackerSetup(uid: _uid) ??
        DateTime(2000, 1, 1);
    final transactions = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.moneyTrackerSetups,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlMoneySetups(
      transactions.map((e) => MoneyTrackerSetup.fromMap(e)).toList(),
    );
  }

  Future<void> _syncMoneyTrackerTransactions() async {
    _log.i('Syncing money tracker transactions');
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeMoneyTrackerTransaction(uid: _uid) ??
        DateTime(2000, 1, 1);
    final transactions = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.moneyTrackerTransactions,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlMoneyTransaction(
      transactions.map((e) => MoneyTrackerTransaction.fromMap(e)).toList(),
    );
  }

  Future<void> _syncCalendarEventSetups() async {
    _log.i('Syncing calendar events');
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeCalendarSetup(uid: _uid) ??
        DateTime(2000, 1, 1);
    final events = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.calendarEventSetups,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlCalendarSetups(
      events.map((e) => CalendarEventSetup.fromMap(e)).toList(),
    );
  }

  Future<void> _syncCalendarEventActions() async {
    _log.i('Syncing calendar events');
    final lastSyncTime = await _driftDatabaseRepository
            .getLastSyncTimeCalendarAction(uid: _uid) ??
        DateTime(2000, 1, 1);
    final events = await _firebaseFirestoreRepository.getAll(
      FirebaseDocCollectionType.calendarEventActions,
      _uid,
      lastSyncDate: lastSyncTime,
    );
    await _updateSqlCalendarActions(
      events.map((e) => CalendarEventAction.fromMap(e)).toList(),
    );
  }

  bool allowPullToInsertInDB({
    required MeBaseModel? dbData,
    required MeBaseModel pullData,
    bool dontUseLocalUpdatedAt = false,
  }) {
    String collectionName = pullData.docCollection.name;
    String docId = pullData.id;

    if (dbData == null) {
      _log.i(
        '[PULL_SYNC][ACCEPTED][Collection:$collectionName][DocID:$docId] Reason: No existing data in DB.',
      );
      return true;
    }

    // Check cloud timestamp condition
    bool cloudTimestampValid = pullData.cloudUpdatedAt!.millisecondsSinceEpoch >
        (dbData.cloudUpdatedAt?.millisecondsSinceEpoch ?? 0);

    // Check local timestamp condition (skip if dontUseLocalUpdatedAt is true)
    bool localTimestampValid = dontUseLocalUpdatedAt ||
        pullData.localUpdatedAt.millisecondsSinceEpoch >=
            dbData.localUpdatedAt.millisecondsSinceEpoch;

    bool canBeAccepted = cloudTimestampValid && localTimestampValid;

    if (canBeAccepted) {
      _log.i(
        '[PULL_SYNC][ACCEPTED][Collection:$collectionName][DocID:$docId] Reason: Cloud timestamp newer and local timestamp is up-to-date.',
      );
    } else {
      if (!cloudTimestampValid && !localTimestampValid) {
        _log.i(
          '[PULL_SYNC][REJECTED][Collection:$collectionName][DocID:$docId] Reason: Both cloud and local timestamps are outdated.',
        );
      } else if (!cloudTimestampValid) {
        _log.i(
          '[PULL_SYNC][REJECTED][Collection:$collectionName][DocID:$docId] Reason: Cloud timestamp is not newer than DB.',
        );
      } else {
        _log.i(
          '[PULL_SYNC][REJECTED][Collection:$collectionName][DocID:$docId] Reason: Local timestamp is older than DB.',
        );
      }
    }

    return canBeAccepted;
  }
}
