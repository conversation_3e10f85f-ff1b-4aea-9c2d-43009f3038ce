import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart' hide UserInfo;
import 'package:flutter_timezone/flutter_timezone.dart';
import 'package:mevolve/constants/app_config.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/models/attachment_info.dart';
import 'package:mevolve/data/models/media_to_sync.dart';
import 'package:mevolve/data/models/release_config_model.dart';
import 'package:mevolve/data/models/user/calendar_integration.dart';
import 'package:mevolve/data/models/user/special_activity.dart';
import 'package:mevolve/data/models/user/user.dart';
import 'package:mevolve/data/models/user/user_metadata.dart';
import 'package:mevolve/data/models/user/user_resources.dart';
import 'package:mevolve/data/models/user/view_settings.dart';
import 'package:mevolve/data/models/webservice/me_base_model.dart';
import 'package:mevolve/data/providers/drift_database.dart';
import 'package:mevolve/data/providers/firebase_functions.dart';
import 'package:mevolve/data/providers/local_storage.dart';
import 'package:mevolve/data/providers/shared_prefs.dart';
import 'package:mevolve/data/repositories/data_sync/data_pull_service.dart';
import 'package:mevolve/data/repositories/data_sync/data_push_service.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/data/repositories/home_widget_and_deep_link/home_widget.utility.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/bloc/cubit/release_config_cubit.dart';
import 'package:mevolve/features/app/widgets/login/view/login_page.dart';
import 'package:mevolve/features/notifications/notification_service.dart';
import 'package:mevolve/utilities/connectivity/internet_connectivity_service.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/encryption/me_encryption.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';
import 'package:mevolve/utilities/nullable.dart';

const maxMigrationCheckRetries = 3;
int failedMigrationCheckAttempt = 0;
Duration attemptIntervalDuration = const Duration(seconds: 5);

class DataSyncService {
  DataSyncService({
    required SharedPreferencesClient sharedPreferencesClient,
    required FirebaseFunctionsRepository firebaseFunctionsRepository,
    required DatabaseRepository databaseRepository,
    required NotificationService notificationsRepository,
    required ReleaseConfigCubit releaseConfigCubit,
    AppBloc? appBloc,
  }) {
    _appBloc = appBloc;
    _databaseRepository = databaseRepository;
    _sharedPreferencesClient = sharedPreferencesClient;
    _dataPushService = DataPushService(
      firebaseFunctionsRepository: firebaseFunctionsRepository,
      databaseRepository: databaseRepository,
      sharedPreferencesClient: sharedPreferencesClient,
      appBloc: appBloc,
    );
    _dataPullService = DataPullService(
      notificationsRepository: notificationsRepository,
      databaseRepository: databaseRepository,
    );
    _releaseConfigCubit = releaseConfigCubit;
  }

  //Making it nullable so that data sync service can be called from background also
  AppBloc? _appBloc;
  late SharedPreferencesClient _sharedPreferencesClient;
  late DataPushService _dataPushService;
  late DataPullService _dataPullService;
  late DatabaseRepository _databaseRepository;
  late DriftDatabaseRepository _sqlDatabaseRepository;
  late ReleaseConfigCubit _releaseConfigCubit;
  StreamSubscription<UserData?>? _userDataSubscription;
  StreamSubscription<UserResources?>? _userResourceSubscription;
  StreamSubscription<UsersMetadata?>? _userMetadataSubscription;
  StreamSubscription<List<SpecialActivity>?>? _specialActivitiesSubscription;
  StreamSubscription<ViewSettings?>? _viewSettingsSubscription;
  StreamSubscription<List<CalendarIntegration>?>?
      _calendarIntegrationSubscription;
  bool _isInitialized = false;

  // bool _isDeviceInfoUpdated = false;
  final Log _log = MeLogger.getLogger(LogTags.dataSyncService);

  void startMigrationAndSync(
    User? user,
    bool isFreshLogin,
    bool isManualMigration,
  ) async {
    if (user == null) {
      return;
    }
    //-------------------Get User inside app-------------------
    // Opening the database even with old version is not a problem as
    // data is stored as JSON.
    _log.i(
      'Opening database - App supported DB version = ${AppConfig.dbVersion}',
    );
    await _openDatabase();

    //-------------------Establish Encrypted Environment-------------------
    bool encryptionEstablished = await _establishEncryptedEnv(user);
    if (!encryptionEstablished) {
      _log.i(
        'Encryption environment not established so stopping proceeding further.',
      );
      return;
    }

    // Make app interactive ASAP if possible.
    if (_appBloc?.state.appAuthStatus != AppStatus.authenticated) {
      await setAppUserDataIfPossible(user);
    }

    //-------------------Check for Migration-------------------
    // Add operation in try catch as migrationSync check may fail.
    bool shouldProceedWithSyncOrMigration;
    MigrationRequiredType requiresMigration;
    bool showErrorScreenOnMigrationCheckFailure(Object error) {
      const String permissionDeniedError =
          '[cloud_firestore/permission-denied]';

      if (error.toString().contains(permissionDeniedError)) {
        return false;
      }
      return true;
    }

    try {
      //-------------------↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑-------------------
      // Todo: We can move this to where we verify that user is authenticated.
      // This will remove dependency from here. But make sure that part of code is
      // run before migration decision is made.
      // Re-init release config as authenticated.
      _releaseConfigCubit.initReleaseConfig(
        _databaseRepository,
        isUnAuthenticated: false,
      );
      // Migration Check
      (
        MigrationRequiredType,
        bool,
        bool
      ) isRequireMigrationAndShouldItBeAllowed =
          await isMigrationRequired(user);

      shouldProceedWithSyncOrMigration = _shouldProceedWithSyncOrMigration(
        isRequireMigrationAndShouldItBeAllowed,
        isManualMigration,
      );
      requiresMigration = isRequireMigrationAndShouldItBeAllowed.$1;
      _log.i('Migration check of app is successful: $requiresMigration');
    } catch (error) {
      failedMigrationCheckAttempt++;
      _log.e('Error while checking migration with error: $error');
      if (failedMigrationCheckAttempt <= maxMigrationCheckRetries) {
        _log.w(
          'Retrying data sync service start as migration check failed. Attempt: $failedMigrationCheckAttempt',
        );
        // Refreshing firebase auth token.
        await Future.wait([
          Future.delayed(attemptIntervalDuration),
          if (FirebaseAuth.instance.currentUser != null)
            FirebaseAuth.instance.currentUser!.getIdToken(true).catchError((e) {
              _log.w('Failed to refresh auth token: $e');
              return null;
            }),
        ]);

        // Retry migration check from dataSyncService from appBloc.
        _appBloc?.dataSyncService?.startMigrationAndSync(
          _appBloc?.state.user,
          isFreshLogin,
          isManualMigration,
        );
        return;
      }
      if (showErrorScreenOnMigrationCheckFailure(error)) {
        // Remove full screen login loader.
        showFullScreenLoginLoader.value = false;
        _log.w('Showing manual sync error screen as migration check failed');
        _appBloc?.updateDataSyncServiceStatus(
          const DataSyncServiceStatusChanged(
            dataSyncStatus: DataSyncStatus.forceSyncError,
          ),
        );
        return;
      } else {
        _log.w(
          'Migration check failed but not showing error screen and proceeding with sync',
        );
        requiresMigration = MigrationRequiredType.none;
        shouldProceedWithSyncOrMigration = true;
      }
    }
    // Reset attempt counter as we moved forward.
    failedMigrationCheckAttempt = 0;
    //-------------------↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑-------------------

    //-------------------Start/Ask Migration(if required) and then start Sync-------------------
    // Check if migration is needed but if not allowed then stop here.
    if (!shouldProceedWithSyncOrMigration) {
      // Remove full screen login loader.
      showFullScreenLoginLoader.value = false;
      _log.i('Show manual migration OR Update screen');
      _appBloc?.updateDataSyncServiceStatus(
        const DataSyncServiceStatusChanged(
          dataSyncStatus: DataSyncStatus.forceSyncRequired,
        ),
      );
      return;
    } else {
      // If migration is needed and allowed then start migration.
      if (requiresMigration == MigrationRequiredType.onlyLocalMigration) {
        // After above checks we are sure that no specific direction migration is needed.
        // Only local migration is needed.
        await _executeMigration(
          null,
          user,
        );
      } else if (requiresMigration == MigrationRequiredType.downward) {
        // After above checks we are sure that if downward migration is needed.
        await _executeMigration(
          migrateDataDownward,
          user,
        );
      } else if (requiresMigration == MigrationRequiredType.upward) {
        // After above checks we are sure that if upward migration is needed.
        await _executeMigration(
          migrateDataUpward,
          user,
        );
      }
      // At this point we are sure that migration are done so we can
      // start sync process.
      try {
        await _commonSyncProcess(user, isFreshLogin);
      } catch (error) {
        _log.e('Error while running sync process $error');
        _log.i('Forcing app visibility without sync if possible');
        bool isUserDataSet = await setAppUserDataIfPossible(user);
        if (!isUserDataSet) {
          _log.i(
            'Showing force sync error screen as app can\'t be interactive without a sync',
          );
          _appBloc?.updateDataSyncServiceStatus(
            const DataSyncServiceStatusChanged(
              dataSyncStatus: DataSyncStatus.forceSyncError,
            ),
          );
        }
        return;
      }
    }
    //-------------------↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑↑-------------------
  }

  Future<(Map<String, dynamic>?, Map<String, dynamic>?)> getUserFromCloudTask(
    User user, {
    forceRefreshSecret = false,
  }) async {
    bool areSecretsAvailable = forceRefreshSecret
        ? false
        : await MeEncryption().areSecretsAvailable(user.uid);
    return _dataPullService.getRawUserDataWithKeys(
      user,
      refreshSecret: !areSecretsAvailable,
      language: _appBloc?.state.getAppLanguage,
    );
  }

  Future<bool> _establishEncryptedEnv(User user) async {
    try {
      // Fetch the secrets, initialize MeEncryption and fetch user data and start the app
      // as soon as possible.
      bool areSecretsAvailable =
          await MeEncryption().areSecretsAvailable(user.uid);
      if (!areSecretsAvailable) {
        // Gets secrets from cloud and saves them.
        final results = await getUserFromCloudTask(user);

        bool shouldAskCustomSecretFromUser =
            await _askCustomSecretFromUser(results.$1!);
        if (shouldAskCustomSecretFromUser) {
          _log.i('Custom encryption password required');
          _appBloc?.updateDataSyncServiceStatus(
            const DataSyncServiceStatusChanged(
              dataSyncStatus: DataSyncStatus.encryptionKeyRequired,
            ),
          );
          return false;
        }
      }

      // Initialize MeEncryption with secrets.
      await MeEncryption().initializeMeEncryption(user.uid);

      return true;
    } catch (error) {
      _log.e('Error while establishing encrypted environment $error');
      return false;
    }
  }

  // A function that makes app interactive by setting the userData if it's possible.
  // Once user data is set then app bloc auth state becomes authenticated.
  Future<bool> setAppUserDataIfPossible(User user) async {
    bool isAppSetActive = false;

    int? localDBDataVersion =
        _sharedPreferencesClient.getCurrentSqlDatabaseVersion();

    int appDbVersion = AppConfig.dbVersion;

    UserData? userData;
    ViewSettings? viewSettings;
    if (localDBDataVersion == appDbVersion) {
      final localUserDataResult = await Future.wait([
        _databaseRepository.fetchUserData(uid: user.uid),
        _databaseRepository.fetchViewSettingsByUid(uid: user.uid),
      ]);
      userData = localUserDataResult[0] as UserData?;
      viewSettings = localUserDataResult[1] as ViewSettings?;
    }

    if (userData != null) {
      // Remove full screen login loader.
      showFullScreenLoginLoader.value = false;
      _appBloc?.updateDataSyncServiceStatus(
        DataSyncServiceStatusChanged(
          userData: userData,
          viewSettings: viewSettings,
          dataSyncStatus: DataSyncStatus.none,
        ),
      );
      isAppSetActive = true;
    } else {
      _log.d(
        'User data and view settings from DB not useful or unavailable. So, app can\'t be interactive for user yet.',
      );
    }

    return isAppSetActive;
  }

  /// This function should never handle migration related code.
  /// Its purpose is to start sync process after migration is done.
  /// It should only be called by [startMigrationAndSync] function.
  Future<void> _commonSyncProcess(
    User user,
    bool isFreshLogin,
  ) async {
    String uid = user.uid;

    // We may fetch user data map from cloud. It is used if fresh login or if user data
    // is not available in local db or if secrets are not available to initialize
    // MeEncryption.
    Map<String, dynamic>? userDataMap;

    UserData? userData = await _databaseRepository.fetchUserData(uid: user.uid);
    ViewSettings? viewSettings =
        await _databaseRepository.fetchViewSettingsByUid(uid: user.uid);

    if (userData == null || viewSettings == null) {
      final cloudUserResponse = await getUserFromCloudTask(user);
      userDataMap = cloudUserResponse.$1;
      Map<String, dynamic>? viewSettingsMap = cloudUserResponse.$2;
      if (userDataMap == null || viewSettingsMap == null) {
        _log.e(
          'Getting null userDataMap OR null viewSettingsMap from cloud so not able to proceed with sync.',
        );
        return;
      }

      // If not available in local db then update the local db with cloud data.
      final userDataAndViewSettings = await _updateUserDetailsAndViewSettings(
        user,
        _sqlDatabaseRepository,
        userDataMap,
        viewSettingsMap,
      );
      userData = userDataAndViewSettings?.$1;
      viewSettings = userDataAndViewSettings?.$2;
    }

    if (userData == null) {
      // This should never be true. If this gets true for any XY reason then
      // the app would be stuck here.
      _log.e('Cant form UserData object');
      return;
    }

    if (viewSettings == null) {
      // This should never be true. If this gets true for any XY reason then
      // the app would be stuck here.
      _log.e('Cant form ViewSettings object');
      return;
    }

    _appBloc?.add(
      LocalViewSettingsChanged(
        viewSettings: viewSettings,
        useSharedPerfAppLanguageIfAvailable: true,
      ),
    );

    // Handle Fresh Login
    if (isFreshLogin) {
      if (userDataMap == null) {
        final cloudUserResponse = await getUserFromCloudTask(user);
        userDataMap = cloudUserResponse.$1;
      }

      await _dataPullService.getAll(
        user,
        _sqlDatabaseRepository,
        rawUserData: userDataMap,
      );
      if (!MePlatform.isWeb && !MePlatform.isWindows && !MePlatform.isMacOS) {
        final homeWidgetUtility = HomeWidgetUtility(
          databaseRepository: _databaseRepository,
        );
        homeWidgetUtility.updateHomeWidget();
      }
    }

    // Initialize Stuff.
    _dataPushService.initializeService(
      _appBloc?.state.internetConnectionState ==
          InternetConnectionState.connected,
      uid,
      _appBloc?.state.appUpdateState !=
          AppUpdateState.mandatoryUpdateWithoutSkip,
    );

    _isInitialized = true;
    _startSyncProcess(uid);
    _appBloc?.listenForInternetConnectionChange();

    _appBloc?.updateDataSyncServiceStatus(
      DataSyncServiceStatusChanged(
        dataSyncStatus: DataSyncStatus.backgroundSyncRunning,
        userData: userData,
        viewSettings: viewSettings,
      ),
    );
    _listenForLocalUserData(uid);
    _listenForViewSettings(uid);
    _listenForLocalUserResource(uid);
    _listenForLocalUserMetadata(uid);
    _listenAllSpecialActivity();
    _listenForLocalCalendarIntegration(uid);
    _deleteSyncedMedia();
    showFullScreenLoginLoader.value = false;
  }

  Future<void> startSyncingReleaseConfig() async {
    _log.i('Starting RC sync process');
    await _databaseRepository.openDatabase();

    _dataPullService.startListeningCloudRC(
      _sqlDatabaseRepository,
    );
  }

  void _startSyncProcess(String uid) {
    // Start normal sync process.
    _log.i('Starting sync process');
    if (_isInitialized) {
      if (_appBloc?.state.appUpdateState !=
          AppUpdateState.mandatoryUpdateWithoutSkip) {
        _dataPushService.startPushingDataToCloud();
        _dataPullService.startListeningCloudData(
          uid,
          _sqlDatabaseRepository,
          _sharedPreferencesClient,
        );
      } else {
        _log.d('app needs mandatory update');
      }
    }
  }

  Future<bool> _openDatabase() async {
    bool dbOpened = await _databaseRepository.openDatabase(forceOpen: true);
    if (!dbOpened) {
      throw Exception('Drift Database failed to open');
    }
    _sqlDatabaseRepository = _databaseRepository.driftDatabaseRepository;
    return dbOpened;
  }

  // TODO: enable this later, currently it is resetting the user data values
  // Future<void> _updateDeviceInfo(UserData userData) async {
  //   if (!_isDeviceInfoUpdated) {
  //     var devicesList = userData.userInfo.devicesList;
  //     devicesList = await getUpdatedDeviceInfo(devicesList);
  //     userData = userData.copyWith(
  //       userInfo: userData.userInfo.copyWith(devicesList: devicesList),
  //     );
  //     _isDeviceInfoUpdated = true;
  //     _appBloc?.updateUser(user: userData);
  //   }
  // }

  Future<void> _listenForLocalUserData(String uid) async {
    await _userDataSubscription?.cancel();
    final userDataStream = _databaseRepository.listenUserData(uid: uid);
    _userDataSubscription = userDataStream.listen((userData) async {
      if (userData != null) {
        _appBloc?.add(LocalUserDataChanged(userData: userData));
      }
    });
  }

  Future<void> _listenForLocalUserMetadata(String uid) async {
    await _userMetadataSubscription?.cancel();
    final userDataStream = _databaseRepository.listenUserMetadata(uid: uid);
    _userMetadataSubscription = userDataStream.listen((userMetadata) {
      if (userMetadata != null) {
        _appBloc?.add(LocalUserMetadataChanged(userMetadata: userMetadata));
      }
    });
  }

  Future<void> _listenAllSpecialActivity() async {
    await _specialActivitiesSubscription?.cancel();
    final specialActivitiesStream =
        _databaseRepository.listenAllSpecialActivity();
    _specialActivitiesSubscription =
        specialActivitiesStream.listen((specialActivities) {
      _appBloc
          ?.add(SpecialActivitiesChanged(specialActivities: specialActivities));
    });
  }

  Future<void> _listenForLocalUserResource(String uid) async {
    await _userResourceSubscription?.cancel();
    final userDataStream = _databaseRepository.listenUserResources();
    _userResourceSubscription = userDataStream.listen((userData) async {
      if (userData != null) {
        _appBloc?.add(LocalUserResourceChanged(userResources: userData));
      }
    });
  }

  Future<void> _listenForViewSettings(String uid) async {
    await _viewSettingsSubscription?.cancel();
    final viewSettingStream = _databaseRepository.listenViewSettings(uid: uid);
    _viewSettingsSubscription = viewSettingStream.listen((viewSettings) {
      if (viewSettings != null) {
        _appBloc?.add(
          LocalViewSettingsChanged(
            viewSettings: viewSettings,
            // Setting useSharedPerfAppLanguageIfAvailable to false as we are
            // listening for viewSettings updates and if it's a update then we should
            // not override the language with shared pref language.
            useSharedPerfAppLanguageIfAvailable: false,
          ),
        );
      }
    });
  }

  Future<void> _listenForLocalCalendarIntegration(String uid) async {
    await _calendarIntegrationSubscription?.cancel();
    final calendarDataStream =
        _databaseRepository.calendarIntegrationsStream(limit: 1000);
    final calendarIntegration =
        await _databaseRepository.getAllCalendarIntegrations();
    if (calendarIntegration.isNotEmpty) {
      _appBloc?.add(
        LocalCalendarIntegrationChanged(
          calendarIntegration: calendarIntegration,
        ),
      );
    }
    _calendarIntegrationSubscription =
        calendarDataStream.listen((calendarIntegration) {
      if (calendarIntegration.isNotEmpty) {
        _appBloc?.add(
          LocalCalendarIntegrationChanged(
            calendarIntegration: calendarIntegration,
          ),
        );
      }
    });
  }

  void onInternetConnectionChanged(bool isConnected) {
    _log.i('Internet connection changed to: $isConnected');
    _dataPushService.setInternetConnectionValue(isConnected);
  }

  void onAppUpdateStateChanged(
    User? user,
    UserData? userData,
    AppUpdateState appUpdateState,
  ) {
    if (userData != null) {
      _log.i('Update Allow Sync status due to RC update');
      _dataPushService.updateDataChangedListener(
        userData.uid,
        appUpdateState != AppUpdateState.mandatoryUpdateWithoutSkip ||
            appUpdateState != AppUpdateState.downgradeUpdate,
      );
    }
    // As app update state is changed we call startMigrationAndSync to check if
    // migration is needed.
    startMigrationAndSync(user, false, false);
  }

  Future<void> pushAnyUnSyncedData() async {
    await _dataPushService.pushAnyUnSyncedData();
  }

  Future<void> closeAll({bool isCalledFromConnectivityChanged = false}) async {
    _log.i('Closing sync service');
    await Future.wait(
      [
        _dataPushService.stopPushingDataToCloud(),
        _dataPullService.stopListeners(),
        if (!isCalledFromConnectivityChanged && _userDataSubscription != null)
          _userDataSubscription?.cancel() ?? Future.value(),
        _userMetadataSubscription?.cancel() ?? Future.value(),
        _userResourceSubscription?.cancel() ?? Future.value(),
        _calendarIntegrationSubscription?.cancel() ?? Future.value(),
      ],
    );
    _isInitialized = false;
  }

  bool _shouldProceedWithSyncOrMigration(
    (MigrationRequiredType, bool, bool) isRequireMigrationAndShouldItBeAllowed,
    bool isManualMigration,
  ) {
    MigrationRequiredType requiresMigration =
        isRequireMigrationAndShouldItBeAllowed.$1;
    bool shouldMigrationBeAllowed = isRequireMigrationAndShouldItBeAllowed.$2;
    bool shouldAllowAutoMigration = isRequireMigrationAndShouldItBeAllowed.$3;

    // Check if migration is needed but if not allowed then stop here.
    if (requiresMigration != MigrationRequiredType.none &&
        !shouldMigrationBeAllowed) {
      return false;
    }
    // Check if migration is needed and allowed but can not start then show force sync required.
    if (requiresMigration != MigrationRequiredType.none &&
        shouldMigrationBeAllowed &&
        (!isManualMigration && !shouldAllowAutoMigration)) {
      return false;
    }

    if (requiresMigration != MigrationRequiredType.none &&
        shouldMigrationBeAllowed &&
        (isManualMigration || shouldAllowAutoMigration)) {
      if (isManualMigration) {
        _log.i('User is doing manual migration');
      } else {
        _log.i('Auto migration is triggering migration');
      }
    }

    return true;
  }

  /// Returns Record of (requiresMigration, shouldAllowMigration, shouldAllowAutoMigration)
  Future<(MigrationRequiredType, bool, bool)> isMigrationRequired(
    User user,
  ) async {
    int localDBDataVersion =
        _sharedPreferencesClient.getCurrentSqlDatabaseVersion()!;
    int appDbVersion = AppConfig.dbVersion;
    MigrationRequiredType requiresMigration = MigrationRequiredType.none;
    bool canUpwardMigrationDone = false;
    bool canDownwardMigrationDone = false;
    bool shouldAllowMigration = false;
    bool shouldAllowAutoMigration = false;

    Map<String, dynamic> encryptedUserMetaDataMap =
        (await _dataPullService.getEncryptedUserMetaDataMap(user))!;
    int cloudDBDataVersion = encryptedUserMetaDataMap['docVer'];
    List<String> userSegmentsFromMap =
        encryptedUserMetaDataMap['userSegments'] != null
            ? List<String>.from(encryptedUserMetaDataMap['userSegments'])
            : [];
    AppConfig.instance.updateAuthUserDocVerAndSegments(
      Nullable(cloudDBDataVersion),
      Nullable(userSegmentsFromMap),
    );
    if (localDBDataVersion < appDbVersion) {
      _log.i(
        'Local DB version ($localDBDataVersion) is lower than app-supported version ($appDbVersion)',
      );
      requiresMigration = MigrationRequiredType.onlyLocalMigration;
    } else if (localDBDataVersion > appDbVersion) {
      _log.i(
        'Local DB version ($localDBDataVersion) is higher than app-supported version ($appDbVersion)',
      );
      requiresMigration = MigrationRequiredType.onlyLocalMigration;
    }

    if (cloudDBDataVersion < appDbVersion) {
      _log.i(
        'Cloud DB version ($cloudDBDataVersion) is lower than app-supported version ($appDbVersion)',
      );
      requiresMigration = MigrationRequiredType.upward;
    } else if (cloudDBDataVersion > appDbVersion) {
      _log.i(
        'Cloud DB version ($cloudDBDataVersion) is higher than app-supported version ($appDbVersion)',
      );
      requiresMigration = MigrationRequiredType.downward;
      canDownwardMigrationDone = true;
    }

    (List<ReleaseConfigModel>, AppUpdateState) appUpdateStateAndReleaseConfigs;
    if (requiresMigration == MigrationRequiredType.none) {
      _log.i(
        'Both local and cloud DB versions are same as app-supported version ($appDbVersion)',
      );
    } else {
      appUpdateStateAndReleaseConfigs = await _releaseConfigCubit
          .getAppUpdateStateAndReleaseConfigs(_databaseRepository);

      canUpwardMigrationDone =
          requiresMigration == MigrationRequiredType.upward &&
              !canDownwardMigrationDone;

      if (canDownwardMigrationDone) {
        _log.i('Downward migration can be done.');
        _log.i('Deciding if downward migration should be done.');
        if (appUpdateStateAndReleaseConfigs.$2 == AppUpdateState.none) {
          _log.i(
            'No available updates. So, downward migration should be done.',
          );
          shouldAllowMigration = true;
          // I think downward migrations is unsafe to run auto. So, commented out.
          // _log.i('Auto downward migration should be done.');
          // shouldAllowAutoMigration = true;
        } else {
          _log.i(
            'Client update is available. So, no downward migration needed.',
          );
        }
      }

      if (canUpwardMigrationDone) {
        _log.i('Upward migration can be done.');
        _log.i('Deciding if Upward migration should be done.');
        bool isCurrentReleaseConfigAvailable =
            _releaseConfigCubit.isReleaseConfigAvailableForApp(
          false,
          appUpdateStateAndReleaseConfigs.$1,
        );
        if (!isCurrentReleaseConfigAvailable) {
          _log.i(
            'Release config is considered not available for this app version. So, no upward migration should be done.',
          );
        } else {
          shouldAllowMigration = true;
          _log.i(
            'Release config is considered available for this app version. So, upward migration should be done.',
          );
          _log.i('Deciding if auto migration should be done.');
          // Get RC for the cloud DB version to verify if it is still supported.
          bool isReleaseConfigAvailableForLowerDBVersion =
              (await _releaseConfigCubit.isReleaseConfigAvailable(
            cloudDBDataVersion,
            _databaseRepository,
          ))
                  .$1;
          if (cloudDBDataVersion == appDbVersion) {
            _log.i(
              'User is already migrated on cloud. So, we can allow auto migration of this device data.',
            );
            shouldAllowAutoMigration = true;
          } else if (!isReleaseConfigAvailableForLowerDBVersion) {
            _log.i(
              'User Cloud DB version($cloudDBDataVersion) is not supported now. So, we can allow auto migration of this device data to version($appDbVersion).',
            );
            shouldAllowAutoMigration = true;
          } else {
            _log.i('We can not allow auto migration of this device data.');
          }
        }
      }

      // If this is the case then the cloud data is already migrated to the required version
      // and only local data is unfit. In this case we will do just local migration.
      if (requiresMigration == MigrationRequiredType.onlyLocalMigration) {
        _log.i(
          'Only local Data migration should be done cloud is already migrated.',
        );
        shouldAllowMigration = true;
        _log.i('Auto migration should be done as cloud version is different.');
        shouldAllowAutoMigration = true;
      }
    }

    return (requiresMigration, shouldAllowMigration, shouldAllowAutoMigration);
  }

  Future<(UserData, ViewSettings)?> _updateUserDetailsAndViewSettings(
    User user,
    DriftDatabaseRepository sqlDbRepository,
    Map<String, dynamic> userDataMap,
    Map<String, dynamic> viewSettingsMap,
  ) async {
    String timezoneLocation = await FlutterTimezone.getLocalTimezone();
    if (timezoneLocation == 'Asia/Calcutta') {
      timezoneLocation = 'Asia/Kolkata';
    }
    if (userDataMap['docVer'] == DatabaseRepository.currentDbVersion) {
      await MeEncryption().initializeMeEncryption(user.uid);
      userDataMap = await MeBaseModel.decryptFirestoreData(
        userDataMap,
        user.uid,
      );
      viewSettingsMap = await MeBaseModel.decryptFirestoreData(
        viewSettingsMap,
        user.uid,
      );
      final userData = UserData.fromMap(userDataMap);
      final viewSettings = ViewSettings.fromMap(viewSettingsMap);
      await _databaseRepository.saveUser(user: userData).onError(
            (error, stackTrace) => _log.e('Save user data error $error'),
          );
      await _databaseRepository
          .saveViewSettings(viewSettings: viewSettings)
          .onError(
            (error, stackTrace) => _log.e('Save viewSettings error $error'),
          );
      _log.i('Local database updated with new user and viewSettings!');
      return (userData, viewSettings);
    } else if (userDataMap['docVer'] != DatabaseRepository.currentDbVersion) {
      _log.e(
        'userDataMap doc version(${userDataMap['docVer']}) is not same as current db version(${DatabaseRepository.currentDbVersion})',
      );
      return null;
    } else {
      return null;
    }
  }

  Future<void> _deleteSyncedMedia() async {
    _log.i('deleting synced media');
    List<MediaToSyncInfo> mediaToSyncInfo =
        await _databaseRepository.getUploadedItems();
    if (mediaToSyncInfo.isNotEmpty) {
      for (MediaToSyncInfo mediaToSync in mediaToSyncInfo) {
        List<MeAttachmentInfo>? attachments;
        if (mediaToSync.documentType == FirebaseDocCollectionType.todos) {
          final todo =
              await _databaseRepository.getTodoById(mediaToSync.documentId);
          attachments = todo?.attachments;
        } else if (mediaToSync.documentType ==
            FirebaseDocCollectionType.notes) {
          final note =
              await _databaseRepository.getNoteById(mediaToSync.documentId);
          attachments = note?.attachments;
        } else if (mediaToSync.documentType ==
            FirebaseDocCollectionType.habitActions) {
          final habitAction = await _databaseRepository
              .getHabitActionById(mediaToSync.documentId);
          attachments = habitAction?.attachments;
        } else if (mediaToSync.documentType ==
            FirebaseDocCollectionType.journalActions) {
          final journalAction = await _databaseRepository
              .getJournalActionById(mediaToSync.documentId);
          attachments = journalAction?.attachments;
        } else if (mediaToSync.documentType ==
            FirebaseDocCollectionType.chatMessages) {
          final chatMessage = await _databaseRepository
              .getChatMessageById(mediaToSync.documentId);
          attachments = chatMessage?.attachments;
        } else if (mediaToSync.documentType ==
            FirebaseDocCollectionType.moneyTrackerTransactions) {
          final moneyTracker = await _databaseRepository
              .getMoneyTrackerTransactionById(mediaToSync.documentId);
          attachments = moneyTracker?.attachments;
        }
        if (attachments != null && attachments.isNotEmpty) {
          final file =
              attachments.firstWhere((element) => element.id == mediaToSync.id);
          if (file.status == AttachmentUploadStatus.cloud) {
            _log.i(
              'deleting entry from sync database for file id ${mediaToSync.id}',
            );
            await _databaseRepository.deleteMediaToSyncInfo(mediaToSync.id);

            if (MePlatform.isMacOS) {
              // Only delete files that are in the sync folder to avoid deleting original files
              if (mediaToSync.filePath.contains('/filesToSync/')) {
                await LocalStorageRepository()
                    .deleteFiles([mediaToSync.filePath]);
              } else {
                _log.w(
                  'WARNING: Skipping deletion of file outside sync folder: ${mediaToSync.filePath}',
                );
              }
            } else {
              await LocalStorageRepository()
                  .deleteFiles([mediaToSync.filePath]);
            }
          }
        }
      }
    }
  }

  Future<void> syncAllInBackground(User user) async {
    _log.i('Syncing all data in background');
    (MigrationRequiredType, bool, bool) isRequireMigrationAndShouldItBeAllowed =
        await isMigrationRequired(user);
    if (isRequireMigrationAndShouldItBeAllowed.$1 !=
        MigrationRequiredType.none) {
      startMigrationAndSync(user, false, false);
    } else {
      await _openDatabase();
      onInternetConnectionChanged(true);
      await pushAnyUnSyncedData();
      await _dataPushService.pushAnyUnSyncedCalendarData();
      await _dataPullService.syncAll(
        user.uid,
        _sqlDatabaseRepository,
        null,
      );
    }
  }

  Future<void> fetchPaginatedInAppNotifications({
    required String uid,
    required int limit,
    DateTime? lastCreatedAt,
  }) async {
    if (!_isInitialized) {
      _log.w(
        'DataSyncService not initialized, cannot fetch paginated notifications',
      );
      return;
    }
    await _dataPullService.fetchPaginatedInAppNotifications(
      uid: uid,
      limit: limit,
      lastCreatedAt: lastCreatedAt,
    );
  }

  Future<void> migrateDataDownward(
    User user,
  ) async {
    try {
      int? currentDbVersion = AppConfig.instance.authUserDocVer;
      int migrateToVersion = AppConfig.dbVersion;

      if (currentDbVersion == null) {
        throw Exception('Current db version is null. Cannot downgrade');
      }
      while (currentDbVersion! > migrateToVersion) {
        // Define the version to run downgrade for
        int versionToDowngrade = currentDbVersion;
        await FirebaseFunctionsRepository()
            .downgradeVersion(versionToDowngrade, versionToDowngrade - 1);
        // Todo: After downgrade check cloud data version and if it is same as
        // the version from which we downgraded then throw error.
        // Decrement the current version after successful downgrade to run the next downgrade.
        currentDbVersion--;
      }
    } catch (error) {
      throw 'Error while downgrading db $error';
    }
  }

  Future<void> migrateDataUpward(
    User user,
  ) async {
    try {
      await _dataPushService.migrateDataInCloud(user.uid);
    } catch (error) {
      throw 'Error while upgrading db $error';
    }
  }

  Future<void> _executeMigration(
    Future<void> Function(User user)? migrationLogic,
    User user,
  ) async {
    try {
      // Start status updates
      _appBloc?.updateDataSyncServiceStatus(
        const DataSyncServiceStatusChanged(
          dataSyncStatus: DataSyncStatus.forceSyncInProgress,
        ),
      );

      _log.i('Started migration process for user: ${user.uid}');

      // Sync unsynced data before migrating.
      _log.i('Pushing unsynced data before migration');
      await pushAnyUnSyncedData();
      _log.i('Pushed unsynced data before migration');

      // Uncomment to test error state:
      // await Future.delayed(const Duration(seconds: 3));
      // throw 'Fake migration error';

      // Execute the specific migration logic.
      if (migrationLogic != null) {
        await migrationLogic(user);
        _log.i('Cloud migration complete for user: ${user.uid}');
      } else {
        _log.i('Skipping cloud migration as not needed');
      }

      // Delete all local user data after migration.
      await _databaseRepository.deleteAllUserData();
      _log.i('Local data deleted after migration');

      // Update version after successful migration.
      DatabaseRepository.currentDbVersion = AppConfig.dbVersion;
      await _sharedPreferencesClient
          .setCurrentSqlDatabaseVersion(AppConfig.dbVersion);
      _log.i('Local db version updated to ${AppConfig.dbVersion}');

      // Pull all user data after migration.
      final userData =
          await _dataPullService.getAll(user, _sqlDatabaseRepository);

      _log.i(
        'Pulled all user data after migration. Doc version: ${userData.docVer}',
      );

      _log.i('Migration process complete for user: ${user.uid}');

      bool result = await InternetConnectivityService.instance.hasInternet;
      if (!result) {
        // If migration completes but for some reason internet is not available
        // then throw error as sync will fail after this.
        // Why? The migration will be successful if internet is available is
        // gone after reading download backup data. But sync function will fail
        // after it so it's weird to show migration success and then sync error,
        // so we throw error here.
        throw 'Internet connection not available after migration';
      }
    } catch (error) {
      _log.e('Error during migration: $error');
      _appBloc?.updateDataSyncServiceStatus(
        const DataSyncServiceStatusChanged(
          dataSyncStatus: DataSyncStatus.forceSyncError,
        ),
      );
    }
  }

  Future<bool> _askCustomSecretFromUser(
    Map<String, dynamic> userDataMap,
  ) async {
    final areSecretsAvailable =
        await MeEncryption().areSecretsAvailable(userDataMap['uid']);
    if (!areSecretsAvailable && userDataMap['userInfo']['isUsingCustomKey']) {
      return true;
    } else {
      return false;
    }
  }
}
