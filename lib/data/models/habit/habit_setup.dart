import 'package:uuid/uuid.dart';

import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/data/enums/alarm_sound_type.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/enums/habit_type.dart';
import 'package:mevolve/data/enums/timer_stop_type.dart';
import 'package:mevolve/data/models/common_sharing_feat_model.dart';
import 'package:mevolve/data/models/custom/me_date_time.dart';
import 'package:mevolve/data/models/habit/habit_option.dart';
import 'package:mevolve/data/models/webservice/me_base_model.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/utilities/encryption/me_encryption.dart';
import 'package:mevolve/utilities/logger/me_logger.dart';
import 'package:mevolve/utilities/nullable.dart';
import 'package:mevolve/utilities/utility_methods.dart';

class HabitSetup extends MeBaseModel {
  HabitSetup({
    required this.title,
    required this.tags,
    required this.repeat,
    required this.startAt,
    required this.habitType,
    required this.habitOptions,
    required DateTime localUpdatedAt,
    required String uid,
    this.endAt,
    this.isStartTimeSet = false,
    this.isTmzAffected = false,
    this.reminderAt = const [],
    this.duration = 15,
    this.numericGoal,
    this.timerGoal,
    this.alarmSoundType = AlarmSoundType.mevolve_1,
    this.isVibrationEnabled = true,
    this.alarmSoundVolume = 100,
    this.durationRepeatCount = 1,
    this.durationRepeatType = 0,
    this.timerStopType = TimerStopType.onRoundEnd,
    this.numericUnit,
    int? docVer,
    String? id,
    EncryptionData? encryptionData,
    DateTime? cloudUpdatedAt,
    DateTime? lastCloudUpdatedAt,
    DateTime? deletedAt,
    DateTime? permaDeletedAt,
    DateTime? createdAt,
    required this.ownerName,
    required this.ownerEmail,
    MembersModel? members,
  })  : members = members ??
            const MembersModel(
              memberHashedEmails: [],
              membersConfig: {},
            ),
        super(
          id: id ?? const Uuid().v1(),
          docVer: docVer ?? DatabaseRepository.currentDbVersion,
          docCollection: FirebaseDocCollectionType.habitSetups,
          uid: uid,
          localUpdatedAt: localUpdatedAt,
          cloudUpdatedAt: cloudUpdatedAt,
          lastCloudUpdatedAt: lastCloudUpdatedAt,
          deletedAt: deletedAt,
          permaDeletedAt: permaDeletedAt,
          createdAt: createdAt ?? DateTime.now(),
          sessionId: MeLogger.sessionId,
          source: 'client',
          encryptionData: encryptionData ??
              EncryptionData(
                encryptedFields: [
                  'title',
                  'habitOptions[].value',
                  'ownerName',
                  'ownerEmail',
                  'members.membersConfig{}.eEmail',
                ],
              ),
        );

  /// Creates a HabitSetup object from Firebase map with full data processing
  factory HabitSetup.fromMap(Map<String, dynamic> map) {
    List<int> reminderAt = [];
    List<String> tags = [];
    List<String> repeat = [];
    List<HabitOption> habitOptions = [];

    reminderAt = List<int>.from((map['reminderAt'] as List));
    tags = List<String>.from(map['tags']);
    repeat = List<String>.from(map['repeat']);
    habitOptions = List<HabitOption>.from(
      (map['habitOptions'] as List).map((e) => HabitOption.fromMap(e)),
    );
    final habitSetup = HabitSetup(
      id: map['id'],
      docVer: map['docVer'],
      uid: map['uid'],
      title: map['title'],
      createdAt: parseDate(map['createdAt'])!,
      deletedAt: parseDate(map['deletedAt']),
      permaDeletedAt: parseDate(map['permaDeletedAt']),
      startAt: MeDateTime.fromFirestore(map['startAt']),
      endAt:
          map['endAt'] != null ? MeDateTime.fromFirestore(map['endAt']) : null,
      tags: tags,
      repeat: repeat,
      isStartTimeSet: parseBool(map['isStartTimeSet'])!,
      isTmzAffected: parseBool(map['isTmzAffected']) ?? false,
      reminderAt: reminderAt,
      duration: map['duration'] ?? 15,
      numericGoal: map['numericGoal'],
      timerGoal: map['timerGoal'],
      numericUnit: map['numericUnit'] as String?,
      alarmSoundType: AlarmSoundType.values.byName(map['alarmSoundType']),
      isVibrationEnabled: parseBool(map['isVibrationEnabled'])!,
      alarmSoundVolume: map['alarmSoundVolume'] ?? 100,
      habitType: HabitType.values.byName(map['habitType']),
      habitOptions: habitOptions,
      durationRepeatCount: map['durationRepeatCount'],
      durationRepeatType: map['durationRepeatType'] ?? 0,
      timerStopType: TimerStopType.values
          .byName(map['timerStopType'] ?? TimerStopType.onRoundEnd.name),
      localUpdatedAt: parseDate(map['localUpdatedAt'])!,
      cloudUpdatedAt: parseDate(map['cloudUpdatedAt']),
      lastCloudUpdatedAt: parseDate(
        map['lastCloudUpdatedAt'] ?? map['cloudUpdatedAt'],
      ),
      encryptionData: EncryptionData.fromMap(map['encData']),
      ownerName: map['ownerName'] ?? '',
      ownerEmail: map['ownerEmail'] ?? '',
      members: MembersModel.fromMap(map['members']),
    );
    return habitSetup;
  }

  /// Creates a HabitSetup object from Firebase map with minimal data for version compatibility
  factory HabitSetup.fromMapCompatible(Map<String, dynamic> map) {
    return HabitSetup(
      id: map['id'],
      title: map['title'] as String,
      docVer: map['docVer'],
      members: null,
      uid: map['uid'],
      tags: [],
      repeat: [],
      startAt: MeDateTime.fromFirestore(map['startAt'] ?? DateTime.now()),
      habitType: () {
        try {
          return HabitType.values.byName(map['habitType']);
        } catch (e) {
          return HabitType.boolean;
        }
      }(),
      habitOptions: [],
      ownerName: map['ownerName'] ?? '',
      ownerEmail: map['ownerEmail'] ?? '',
      permaDeletedAt: parseDate(map['permaDeletedAt']),
      localUpdatedAt: parseDate(map['localUpdatedAt']) ?? DateTime.now(),
      cloudUpdatedAt: parseDate(map['cloudUpdatedAt']) ?? DateTime.now(),
      lastCloudUpdatedAt: parseDate(
        map['lastCloudUpdatedAt'] ?? map['cloudUpdatedAt'] ?? DateTime.now(),
      ),
      createdAt: parseDate(map['createdAt']) ?? DateTime.now(),
      deletedAt: parseDate(map['deletedAt']),
    );
  }

  final String title;
  final MeDateTime startAt;
  final HabitType habitType;
  final MeDateTime? endAt;
  final bool isStartTimeSet;
  final bool isTmzAffected;
  final int duration;
  final int? numericGoal;
  final String? timerGoal;
  final AlarmSoundType alarmSoundType;
  final bool isVibrationEnabled;
  final int alarmSoundVolume;
  final String? numericUnit;
  final List<HabitOption> habitOptions;
  final List<int> reminderAt;
  final List<String> tags;
  final List<String> repeat;
  final int durationRepeatCount;
  final int durationRepeatType;
  final TimerStopType timerStopType;

  // sharing model
  final String ownerName;
  final String ownerEmail;
  final MembersModel members;

  List<int> get reminders {
    List<int> reminders = reminderAt.toSet().toList();
    if (reminders.length > 3) {
      // Keep the smallest 3 reminders
      reminders.sort();
      reminders = reminders.sublist(0, 3);
    }
    return reminders;
  }

  @override
  Map<String, dynamic> toMap({bool isFirestore = false}) {
    return <String, dynamic>{
      ...getBaseMap(),
      'title': title,
      'startAt': startAt.toMap(),
      'endAt': endAt?.toMap(),
      'tags': tags,
      'repeat': repeat,
      'isStartTimeSet': isStartTimeSet,
      'isTmzAffected': isTmzAffected,
      'reminderAt': reminderAt,
      'duration': duration,
      'numericGoal': numericGoal,
      'timerGoal': timerGoal,
      'alarmSoundType': alarmSoundType.name,
      'isVibrationEnabled': isVibrationEnabled,
      'alarmSoundVolume': alarmSoundVolume,
      'numericUnit': numericUnit,
      'habitType': habitType.name,
      'habitOptions': habitOptions.map((e) => e.toMap()).toList(),
      'durationRepeatCount': durationRepeatCount,
      'durationRepeatType': durationRepeatType,
      'timerStopType': timerStopType.name,
      'ownerName': ownerName,
      'ownerEmail': ownerEmail,
      'members': members.toMap(),
    };
  }

  HabitSetup copyWith({
    String? title,
    DateTime? localUpdatedAt,
    MeDateTime? startAt,
    Nullable<MeDateTime?>? endAt,
    List<String>? tags,
    List<String>? repeat,
    Nullable<DateTime?>? deletedAt,
    Nullable<DateTime?>? permaDeletedAt,
    bool? isStartTimeSet,
    bool? isTmzAffected,
    List<int>? reminderAt,
    int? duration,
    Nullable<int?>? numericGoal,
    Nullable<String?>? timerGoal,
    AlarmSoundType? alarmSoundType,
    bool? isVibrationEnabled,
    int? alarmSoundVolume,
    Nullable<String?>? numericUnit,
    HabitType? habitType,
    List<HabitOption>? habitOptions,
    int? durationRepeatCount,
    int? durationRepeatType,
    TimerStopType? timerStopType,
    Nullable<DateTime?>? cloudUpdatedAt,
    String? ownerName,
    String? ownerEmail,
    MembersModel? members,
  }) =>
      HabitSetup(
        id: id,
        docVer: docVer,
        encryptionData: encryptionData,
        uid: uid,
        title: title ?? this.title,
        createdAt: createdAt,
        localUpdatedAt: localUpdatedAt ?? this.localUpdatedAt,
        startAt: startAt ?? this.startAt,
        deletedAt: deletedAt == null ? this.deletedAt : deletedAt.value,
        permaDeletedAt:
            permaDeletedAt == null ? this.permaDeletedAt : permaDeletedAt.value,
        endAt: endAt == null ? this.endAt : endAt.value,
        tags: tags ?? this.tags,
        repeat: repeat ?? this.repeat,
        isStartTimeSet: isStartTimeSet ?? this.isStartTimeSet,
        isTmzAffected: isTmzAffected ?? this.isTmzAffected,
        reminderAt: reminderAt ?? this.reminderAt,
        duration: duration ?? this.duration,
        numericGoal: numericGoal == null ? this.numericGoal : numericGoal.value,
        timerGoal: timerGoal == null ? this.timerGoal : timerGoal.value,
        alarmSoundType: alarmSoundType ?? this.alarmSoundType,
        isVibrationEnabled: isVibrationEnabled ?? this.isVibrationEnabled,
        alarmSoundVolume: alarmSoundVolume ?? this.alarmSoundVolume,
        numericUnit: numericUnit == null ? this.numericUnit : numericUnit.value,
        habitType: habitType ?? this.habitType,
        habitOptions: habitOptions ?? this.habitOptions,
        durationRepeatCount: durationRepeatCount ?? this.durationRepeatCount,
        durationRepeatType: durationRepeatType ?? this.durationRepeatType,
        timerStopType: timerStopType ?? this.timerStopType,
        cloudUpdatedAt:
            cloudUpdatedAt == null ? this.cloudUpdatedAt : cloudUpdatedAt.value,
        lastCloudUpdatedAt: lastCloudUpdatedAt,
        ownerName: ownerName ?? this.ownerName,
        ownerEmail: ownerEmail ?? this.ownerEmail,
        members: members ?? this.members,
      );

  //toString
  @override
  String toString() {
    return toMap().toString();
  }

  List<String>? _titleWords;

  List<String> get titleWords {
    _titleWords ??= title.split(' ');
    return _titleWords!;
  }

  bool? _isCompleted;

  /// A getter to check if the habit is active or not(completed)
  bool get isCompleted {
    _isCompleted ??= tmzSafeEndAt != null &&
        tmzSafeEndAt!.isBefore(DateTime.now().onlyDate());
    return _isCompleted!;
  }

  DateTime? _tmzSafeStartAt;

  DateTime get tmzSafeStartAt {
    _tmzSafeStartAt ??= UtilityMethods.getDateTimeForTask(
      startAt,
      isStartTimeSet,
      !(isStartTimeSet && isTmzAffected),
    );
    return _tmzSafeStartAt!;
  }

  DateTime? _tmzSafeEndAt;

  DateTime? get tmzSafeEndAt {
    _tmzSafeEndAt ??= UtilityMethods.getDateTimeForTask(
      endAt,
      isStartTimeSet,
      !(isStartTimeSet && isTmzAffected),
    );
    return _tmzSafeEndAt;
  }

  Duration? _timerGoalWithRepetition;

  Duration get timerGoalWithRepetition {
    _timerGoalWithRepetition ??= () {
      if (timerGoal == null) {
        return Duration.zero;
      }
      return Duration(
        seconds: iso8601ToDuration(timerGoal)!.inSeconds * durationRepeatCount,
      );
    }();
    return _timerGoalWithRepetition!;
  }
}
