import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:mevolve/constants/app_config.dart';
import 'package:mevolve/data/providers/authentication/platform/platform_auth_handler.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:universal_html/html.dart' as html;
import 'package:uuid/uuid.dart';
import 'dart:async';

class PlatformAuthHandlerWeb implements PlatformAuthHandler {
  StreamSubscription<html.MessageEvent>? _messageSubscription;

  final String _calendarScopes =
      (AppConfig.googleSensitiveScopes + AppConfig.googleNonSensitiveScopes)
          .join(' ');

  @override
  Future<UserCredential?> signInWithGoogle(FirebaseAuth firebaseAuth) async {
    final GoogleAuthProvider authProvider = GoogleAuthProvider();
    return await firebaseAuth.signInWithPopup(authProvider);
  }

  @override
  Future<Map<String, dynamic>?> signInWithGoogleCalendar() async {
    final completer = Completer<Map<String, dynamic>?>();

    // Build OAuth URL for Google
    final redirectUri = 'https://${getMeApiBaseDomain()}/me-calendar-auth';
    final authUrl =
        Uri.parse('https://accounts.google.com/o/oauth2/v2/auth').replace(
      queryParameters: {
        'client_id': AppConfig.googleCalendarClientId,
        'redirect_uri': redirectUri,
        'response_type': 'code',
        'scope': _calendarScopes,
        'access_type': 'offline',
        'prompt': 'consent', // Forces refresh token generation
        'state': 'google', // To identify provider in callback
      },
    );

    debugPrint('Google Calendar OAuth URL: $authUrl');

    // Set up PostMessage listener to receive auth results from cloud function
    _messageSubscription =
        html.window.onMessage.listen((html.MessageEvent event) {
      debugPrint('Received PostMessage: ${event.data}');

      // Check if this is our OAuth callback message
      if (event.data is Map &&
          (event.data['type'] == 'microsoft_oauth_callback' ||
              event.data['type'] == 'google_oauth_callback')) {
        final data = Map<String, dynamic>.from(event.data as Map);

        // Clean up listener
        _messageSubscription?.cancel();
        _messageSubscription = null;

        if (data['error'] != null) {
          // Handle OAuth error
          debugPrint('OAuth error received: ${data['error']}');
          completer.complete(null);
        } else if (data['code'] != null) {
          // Success! Return minimal auth map like Microsoft does
          debugPrint(
            'OAuth code received: ${data['code'].toString().substring(0, 10)}...',
          );

          // Return minimal map - backend will handle everything else
          var map = {
            'serverAuthCode': data['code'],
            'type': 'google',
            'scopes': _calendarScopes,
            'id': const Uuid().v1(),
            // Backend will get these via token exchange:
            'email': null,
            'name': null,
            'accessToken': null,
            'refreshToken': null,
            'idToken': null,
            'expiry': null,
          };
          completer.complete(map);
        } else {
          // Unexpected message format
          debugPrint('No code or error in PostMessage data');
          completer.complete(null);
        }
      }
    });

    // Open popup window
    final popup = html.window.open(
      authUrl.toString(),
      'google_oauth',
      'width=500,height=600,scrollbars=yes,resizable=yes',
    );

    debugPrint('Google OAuth popup opened: ${popup.closed == false}');

    // Monitor popup for closure (fallback if PostMessage fails)
    _monitorPopupClosure(popup, completer);

    return completer.future;
  }

  void _monitorPopupClosure(
    html.WindowBase popup,
    Completer<Map<String, dynamic>?> completer,
  ) {
    const checkInterval = Duration(milliseconds: 500);

    void checkPopup() {
      try {
        // Check if popup is closed
        if (popup.closed == true) {
          debugPrint('OAuth popup was closed by user');
          _messageSubscription?.cancel();
          _messageSubscription = null;
          if (!completer.isCompleted) {
            completer.complete(null);
          }
          return;
        }

        // Continue monitoring if completer hasn't completed
        if (!completer.isCompleted) {
          Future.delayed(checkInterval, checkPopup);
        }
      } catch (e) {
        debugPrint('Error monitoring popup closure: $e');
        _messageSubscription?.cancel();
        _messageSubscription = null;
        if (!completer.isCompleted) {
          completer.complete(null);
        }
      }
    }

    // Start monitoring
    Future.delayed(checkInterval, checkPopup);
  }

  @override
  Future<(UserCredential, String)?> signInWithApple(
    FirebaseAuth firebaseAuth,
  ) async {
    // Use the same redirect URI pattern as Android
    final redirectUri = 'https://${getMeApiBaseDomain()}/me-login-appleSignIn';

    final appleCredential = await SignInWithApple.getAppleIDCredential(
      webAuthenticationOptions: WebAuthenticationOptions(
        clientId:
            'web.mevolve.daily.${AppConfig.instance.environmentType.name}',
        redirectUri: Uri.parse(redirectUri),
      ),
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );

    final oauthCredential = OAuthProvider('apple.com').credential(
      idToken: appleCredential.identityToken,
      accessToken: appleCredential.authorizationCode,
    );

    final userCredential =
        await firebaseAuth.signInWithCredential(oauthCredential);
    return (userCredential, appleCredential.authorizationCode);
  }

  @override
  Future<void> signOut() async {
    // Clean up any active listeners
    _messageSubscription?.cancel();
    _messageSubscription = null;
    // Web doesn't need platform-specific sign out
  }
}
