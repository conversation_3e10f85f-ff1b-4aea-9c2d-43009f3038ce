import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolve/data/providers/authentication/platform/platform_auth_handler_stub.dart'
    if (dart.library.io) 'package:mevolve/data/providers/authentication/platform/platform_auth_handler_io.dart'
    if (dart.library.html) 'package:mevolve/data/providers/authentication/platform/platform_auth_handler_web.dart';

abstract class PlatformAuthHandler {
  factory PlatformAuthHandler.create() => createPlatformAuthHandler();

  Future<UserCredential?> signInWithGoogle(FirebaseAuth firebaseAuth);

  Future<Map<String, dynamic>?> signInWithGoogleCalendar();

  Future<(UserCredential, String)?> signInWithApple(FirebaseAuth firebaseAuth);

  Future<void> signOut();
}
