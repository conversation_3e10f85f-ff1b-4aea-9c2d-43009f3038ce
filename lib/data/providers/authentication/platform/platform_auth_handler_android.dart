import 'dart:developer';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:mevolve/constants/app_config.dart';
import 'package:mevolve/utilities/utility_methods.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:uuid/uuid.dart';
import 'package:mevolve/data/providers/authentication/platform/platform_auth_handler.dart';

class PlatformAuthHandlerAndroid implements PlatformAuthHandler {
  bool _isInitializedForAuth = false;
  bool _isInitializedForCalendar = false;

  final List<String> calendarScopes =
      AppConfig.googleSensitiveScopes + AppConfig.googleNonSensitiveScopes;

  Future<void> _ensureInitializedForAuth() async {
    if (!_isInitializedForAuth) {
      // Android requires serverClientId for authenticate() method
      await GoogleSignIn.instance.initialize(
        serverClientId: UtilityMethods.getFirebaseOptions().androidClientId,
      );
      _isInitializedForAuth = true;
      _isInitializedForCalendar = false; // Reset calendar initialization
    }
  }

  Future<void> _ensureInitializedForCalendar() async {
    if (!_isInitializedForCalendar) {
      await GoogleSignIn.instance.initialize(
        clientId: UtilityMethods.getFirebaseOptions().androidClientId,
        serverClientId: AppConfig.googleCalendarClientId,
      );
      _isInitializedForCalendar = true;
      _isInitializedForAuth = false; // Reset auth initialization
    }
  }

  @override
  Future<UserCredential?> signInWithGoogle(FirebaseAuth firebaseAuth) async {
    await _ensureInitializedForAuth();

    // First authenticate
    final GoogleSignInAccount googleUser =
        await GoogleSignIn.instance.authenticate();

    // CRITICAL: Validate email BEFORE creating Firebase user
    if (googleUser.email.isEmpty || !googleUser.email.contains('@')) {
      log(
        'DEBUG: BLOCKING Firebase user creation - Invalid email by google: "${googleUser.email}"',
      );
      await GoogleSignIn.instance.signOut(); // Clean up Google session
      // Throw specific error that shows user-friendly message
      throw FirebaseAuthException(
        code: 'google-email-not-found',
        message:
            'Please disconnect this app from your Google account settings and sign in again after some time.',
      );
    }

    log(
      'DEBUG: Email validation passed, proceeding with Firebase sign-in',
    );

    final googleAuth = googleUser.authentication;
    final credential = GoogleAuthProvider.credential(
      accessToken:
          null, // Access token comes from authorization, not authentication
      idToken: googleAuth.idToken,
    );

    // NOW create Firebase user - only if email is valid
    final userCredential = await firebaseAuth.signInWithCredential(credential);

    // Double check - if Firebase user still doesn't have email, fail the sign-in
    if (userCredential.user?.email == null ||
        userCredential.user!.email!.isEmpty) {
      log(
        'DEBUG: Firebase user has no email after sign-in, signing out',
      );
      await firebaseAuth.signOut();
      await GoogleSignIn.instance.signOut();
      return null;
    }

    if (userCredential.user!.displayName == null &&
        googleUser.displayName != null) {
      await userCredential.user!.updateDisplayName(googleUser.displayName);
    }

    return userCredential;
  }

  @override
  Future<Map<String, dynamic>?> signInWithGoogleCalendar() async {
    final scopes = calendarScopes;

    // Initialize with clientId and serverClientId for calendar access
    await _ensureInitializedForCalendar();

    await GoogleSignIn.instance.signOut();

    // Authenticate with scope hint to request all scopes in one flow
    final GoogleSignInAccount googleUser =
        await GoogleSignIn.instance.authenticate(scopeHint: scopes);

    // Check if authorization was included with authentication (due to scopeHint) because it's not guaranteed
    final authorization =
        await googleUser.authorizationClient.authorizationForScopes(scopes);

    // If no authorization available, request it explicitly (this will not prompt on android) but no future guarantees
    final GoogleSignInClientAuthorization finalAuthorization = authorization ??
        await googleUser.authorizationClient.authorizeScopes(scopes);

    // Get server auth code (this will not prompt on android again. Depends on platform behavior)
    final serverAuth =
        await googleUser.authorizationClient.authorizeServer(scopes);

    // // Use the extension method on the authorization object to create an authenticated client
    // final client = authorization.authClient(scopes: scopes);

    // Get the authentication details
    final GoogleSignInAuthentication auth = googleUser.authentication;

    return {
      'accessToken': finalAuthorization.accessToken,
      'refreshToken':
          null, // Handle refresh token via server-side flow with serverAuthCode
      'idToken': auth.idToken,
      'expiry': null, // Access tokens expire after 1 hour
      'scopes': scopes,
      'email': googleUser.email,
      'id': const Uuid().v1(),
      'name': googleUser.displayName,
      'serverAuthCode': serverAuth?.serverAuthCode,
      'type': 'google',
    };
  }

  @override
  Future<(UserCredential, String)?> signInWithApple(
    FirebaseAuth firebaseAuth,
  ) async {
    String redirectUri = '${getMeApiBaseDomain()}/me-login-appleSignIn';

    final appleCredential = await SignInWithApple.getAppleIDCredential(
      webAuthenticationOptions: WebAuthenticationOptions(
        clientId:
            'web.mevolve.daily.${AppConfig.instance.environmentType.name}',
        redirectUri: Uri.parse(redirectUri),
      ),
      scopes: [
        AppleIDAuthorizationScopes.email,
        AppleIDAuthorizationScopes.fullName,
      ],
    );

    final oauthCredential = OAuthProvider('apple.com').credential(
      idToken: appleCredential.identityToken,
      accessToken: appleCredential.authorizationCode,
    );

    final userCredential =
        await firebaseAuth.signInWithCredential(oauthCredential);
    return (userCredential, appleCredential.authorizationCode);
  }

  @override
  Future<void> signOut() async {
    await GoogleSignIn.instance.signOut();
  }
}
