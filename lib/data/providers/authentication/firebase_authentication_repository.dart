import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/constants/app_config.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/data/providers/authentication/authentication_repository.dart';
import 'package:mevolve/data/providers/authentication/platform/platform_auth_handler.dart';
import 'package:mevolve/data/providers/authentication/exceptions/firebase_auth_exceptions.dart';
import 'package:mevolve/data/repositories/home_widget_and_deep_link/home_widget_service.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';

class FirebaseAuthenticationRepository implements AuthenticationRepository {
  FirebaseAuthenticationRepository({
    FirebaseAuth? firebaseAuth,
    PlatformAuthHandler? platformHandler,
  })  : _firebaseAuth = firebaseAuth ?? FirebaseAuth.instance,
        _platformHandler = platformHandler ?? PlatformAuthHandler.create();

  final FirebaseAuth _firebaseAuth;
  final PlatformAuthHandler _platformHandler;
  final Log _log = MeLogger.getLogger(LogTags.authentication);

  bool _isNewUser = false;

  @override
  bool get isNewUser => _isNewUser;

  @override
  Stream<User?> get user {
    return _firebaseAuth.userChanges().asyncMap((user) async {
      if (user != null && (user.email == null || user.email!.isEmpty)) {
        _log.w('User has no email, signing out: ${user.uid}');
        await _firebaseAuth.signOut();
        return null;
      }
      return user;
    });
  }

  @override
  User? get currentUser => _firebaseAuth.currentUser;

  @override
  Future<void> useEmulator() async {
    await _firebaseAuth.useAuthEmulator(
      AppConstant.firebaseHost,
      FirebaseConstants.firebaseAuthenticatorPort,
    );
  }

  @override
  Future<void> updateUserDisplayName(String displayName) async {
    await _firebaseAuth.currentUser!.updateDisplayName(displayName);
  }

  @override
  Future<User?> logInWithGoogle() async {
    try {
      _log.i('Starting google sign in');

      final userCredential =
          await _platformHandler.signInWithGoogle(_firebaseAuth);
      if (userCredential == null) {
        _log.e(
          'Error in google sign in. Did not received any google credentials',
        );
        return null;
      }

      _isNewUser = (currentUser?.providerData.length ?? 0) > 1
          ? false
          : userCredential.additionalUserInfo!.isNewUser;

      _log.i('Google sign-in successful');
      return userCredential.user;
    } on FirebaseAuthException catch (e) {
      _log.e(e);
      throw LogInWithGoogleFailure.fromCode(e.code);
    } catch (e) {
      _log.e(e);
      throw LogInWithGoogleFailure.fromCode(e.toString());
    }
  }

  @override
  Future<Map<String, dynamic>?> logInWithGoogleCalendar() async {
    try {
      if (AppConfig.googleCalendarClientId.isEmpty) {
        throw Exception(
          'Google Calendar client ID not configured. Please set googleClientId in environment.',
        );
      }

      _log.i('Starting google calendar sign in');
      return await _platformHandler.signInWithGoogleCalendar();
    } on FirebaseAuthException catch (e) {
      _log.e(e);
      throw LogInWithGoogleFailure.fromCode(e.code);
    } catch (e) {
      _log.e(e);
      throw LogInWithGoogleFailure.fromCode(e.toString());
    }
  }

  @override
  Future<DateTime?> getTokenAuthTime() async {
    final user = _firebaseAuth.currentUser;
    final token = await user?.getIdTokenResult();
    return token?.authTime;
  }

  @override
  Future<(User?, String?)?> logInWithApple() async {
    try {
      final result = await _platformHandler.signInWithApple(_firebaseAuth);
      if (result == null) return null;

      final (userCredential, authorizationCode) = result;
      _isNewUser = userCredential.additionalUserInfo!.isNewUser;

      if (userCredential.user?.email == null && MePlatform.isIOS) {
        _log.i('Email is null while logging in with apple');
        _log.i('User is new: $_isNewUser');

        await _firebaseAuth.revokeTokenWithAuthorizationCode(authorizationCode);
        await _firebaseAuth.signOut();
        throw const LogInWithAppleFailure();
      }

      return (userCredential.user, authorizationCode);
    } on FirebaseAuthException catch (e) {
      _log.e(e);
      throw LogInWithAppleFailure.fromCode(e.code);
    } catch (ex) {
      _log.e(ex);
      if (ex is LogInWithAppleFailure) rethrow;
      throw const LogInWithAppleFailure();
    }
  }

  @override
  Future<User?> logInWithEmailOtp(String token, bool isNewUser) async {
    UserCredential? userCredential;

    await EventFormation().sendLoginEvent(
      loginMethod: LoginMethod.email,
      sendOnFuncComplete: () async {
        LoginStatus loginStatus = LoginStatus.failure;
        LoginErrorCode? errorCode = LoginErrorCode.other;
        try {
          userCredential = await _firebaseAuth.signInWithCustomToken(token);
          loginStatus = LoginStatus.success;
          errorCode = null;
        } catch (e) {
          _log.e('Error in email OTP sign in: $e');
          errorCode = LoginErrorCode.other;
        }
        return (loginStatus, errorCode);
      },
    );

    return userCredential?.user;
  }

  @override
  Future<void> deleteAccount() async {
    await _firebaseAuth.currentUser?.delete();
  }

  @override
  Future<void> revokeAppleTokenWithAuthorizationCode(
    String authorizationCode,
  ) async {
    await _firebaseAuth.revokeTokenWithAuthorizationCode(authorizationCode);
  }

  @override
  Future<void> logOut() async {
    try {
      await HomeWidgetService.saveWidgetData('is_logged_in', false);
    } catch (e) {
      _log.e('logOut: Error updating widget data: $e');
    }

    try {
      await Future.wait([
        _firebaseAuth.signOut().timeout(
          const Duration(seconds: 3),
          onTimeout: () {
            _log.w(
              'logOut: Firebase signOut timed out, continuing with logout',
            );
            return;
          },
        ),
        _platformHandler.signOut().timeout(
          const Duration(seconds: 3),
          onTimeout: () {
            _log.w(
              'logOut: Platform signOut timed out, continuing with logout',
            );
            return;
          },
        ),
      ]);
    } catch (e) {
      _log.e('logOut: Error in sign out: $e');
    }

    try {
      await HomeWidgetService.saveWidgetData('is_logged_in', false);
    } catch (e) {
      _log.e('logOut: Error in final widget data update: $e');
    }
  }
}
