import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolve/data/providers/authentication/authentication_repository.dart';

/// Main service for handling all authentication operations in the application.
///
/// This service provides a unified API for authentication functionality
/// across different platforms. It uses platform-specific implementations
/// via the [AuthenticationRepository] interface.
class AuthenticationService {
  /// Get the singleton instance of the authentication service
  factory AuthenticationService() => _instance;

  /// Private constructor for singleton pattern
  AuthenticationService._internal()
      : _repository = AuthenticationRepository.create();

  // Core components
  final AuthenticationRepository _repository;

  // Singleton instance
  static final AuthenticationService _instance =
      AuthenticationService._internal();

  // Delegate all authentication methods to the repository

  Stream<User?> get user => _repository.user;

  User? get currentUser => _repository.currentUser;

  bool get isNewUser => _repository.isNewUser;

  Future<void> useEmulator() => _repository.useEmulator();

  Future<void> updateUserDisplayName(String displayName) =>
      _repository.updateUserDisplayName(displayName);

  Future<User?> logInWithGoogle() => _repository.logInWithGoogle();

  Future<Map<String, dynamic>?> logInWithGoogleCalendar() =>
      _repository.logInWithGoogleCalendar();

  Future<DateTime?> getTokenAuthTime() => _repository.getTokenAuthTime();

  Future<(User?, String?)?> logInWithApple() => _repository.logInWithApple();

  Future<User?> logInWithEmailOtp(String token, bool isNewUser) =>
      _repository.logInWithEmailOtp(token, isNewUser);

  Future<void> deleteAccount() => _repository.deleteAccount();

  Future<void> revokeAppleTokenWithAuthorizationCode(
    String authorizationCode,
  ) =>
      _repository.revokeAppleTokenWithAuthorizationCode(authorizationCode);

  Future<void> logOut() => _repository.logOut();
}
