import 'package:formz/formz.dart';

class ResetPasswordFailure implements Exception {
  const ResetPasswordFailure([
    this.message = 'An unknown exception occurred.',
  ]);

  factory ResetPasswordFailure.fromCode(String code) {
    switch (code) {
      case 'invalid-email':
        return const ResetPasswordFailure(
          'The email address is badly formatted.',
        );
      case 'user-not-found':
        return const ResetPasswordFailure(
          'Email is not found, please create an account.',
        );
      default:
        return const ResetPasswordFailure();
    }
  }

  final String message;
}

class ChangeEmailFailure implements Exception {}

class SignUpWithEmailAndPasswordFailure implements Exception {
  const SignUpWithEmailAndPasswordFailure([
    this.message = 'An unknown exception occurred.',
  ]);

  factory SignUpWithEmailAndPasswordFailure.fromCode(String code) {
    switch (code) {
      case 'invalid-email':
        return const SignUpWithEmailAndPasswordFailure(
          'Email is not valid or badly formatted.',
        );
      case 'user-disabled':
        return const SignUpWithEmailAndPasswordFailure(
          'This user has been disabled. Please contact support for help.',
        );
      case 'email-already-in-use':
        return const SignUpWithEmailAndPasswordFailure(
          'An account already exists for that email.',
        );
      case 'operation-not-allowed':
        return const SignUpWithEmailAndPasswordFailure(
          'Operation is not allowed.  Please contact support.',
        );
      case 'weak-password':
        return const SignUpWithEmailAndPasswordFailure(
          'Please enter a stronger password.',
        );
      default:
        return const SignUpWithEmailAndPasswordFailure();
    }
  }

  final String message;
}

class LogInWithEmailAndPasswordFailure implements Exception {
  const LogInWithEmailAndPasswordFailure([
    this.message = 'An unknown exception occurred.',
  ]);

  factory LogInWithEmailAndPasswordFailure.fromCode(String code) {
    switch (code) {
      case 'invalid-email':
        return const LogInWithEmailAndPasswordFailure(
          'Email is not valid or badly formatted.',
        );
      case 'user-disabled':
        return const LogInWithEmailAndPasswordFailure(
          'This user has been disabled. Please contact support for help.',
        );
      case 'user-not-found':
        return const LogInWithEmailAndPasswordFailure(
          'Email is not found, please create an account.',
        );
      case 'wrong-password':
        return const LogInWithEmailAndPasswordFailure(
          'Incorrect password, please try again.',
        );
      default:
        return const LogInWithEmailAndPasswordFailure();
    }
  }

  final String message;
}

class LogInWithEmailLinkFailure implements Exception {
  const LogInWithEmailLinkFailure([
    this.message = 'An unknown exception occurred.',
  ]);

  factory LogInWithEmailLinkFailure.fromCode(String code) {
    switch (code) {
      case 'invalid-email':
        return const LogInWithEmailLinkFailure(
          'Email is not valid or badly formatted.',
        );
      case 'user-disabled':
        return const LogInWithEmailLinkFailure(
          'This user has been disabled. Please contact support for help.',
        );
      case 'user-not-found':
        return const LogInWithEmailLinkFailure(
          'Email is not found, please create an account.',
        );
      case 'wrong-password':
        return const LogInWithEmailLinkFailure(
          'Incorrect password, please try again.',
        );
      case 'invalid-action-code':
        return const LogInWithEmailLinkFailure(
          'The email link is invalid. This can happen if the link is expired, or has already been used.',
        );
      default:
        return const LogInWithEmailLinkFailure();
    }
  }

  final String message;
}

class LogInWithGoogleFailure implements Exception {
  const LogInWithGoogleFailure([
    this.message = 'An unknown exception occurred.',
  ]);

  factory LogInWithGoogleFailure.fromCode(String code) {
    switch (code) {
      case 'google-email-not-found':
        return const LogInWithGoogleFailure(
          'Please disconnect this app from your Google account settings and sign in again after some time.',
        );
      case 'account-exists-with-different-credential':
        return const LogInWithGoogleFailure(
          'Account exists with different credentials.',
        );
      case 'invalid-credential':
        return const LogInWithGoogleFailure(
          'The credential received is malformed or has expired.',
        );
      case 'operation-not-allowed':
        return const LogInWithGoogleFailure(
          'Operation is not allowed.  Please contact support.',
        );
      case 'user-disabled':
        return const LogInWithGoogleFailure(
          'This user has been disabled. Please contact support for help.',
        );
      case 'user-not-found':
        return const LogInWithGoogleFailure(
          'Email is not found, please create an account.',
        );
      case 'wrong-password':
        return const LogInWithGoogleFailure(
          'Incorrect password, please try again.',
        );
      case 'invalid-verification-code':
        return const LogInWithGoogleFailure(
          'The credential verification code received is invalid.',
        );
      case 'invalid-verification-id':
        return const LogInWithGoogleFailure(
          'The credential verification ID received is invalid.',
        );
      default:
        return LogInWithGoogleFailure(
          code,
        );
    }
  }

  final String message;

  //toString
  @override
  String toString() {
    return 'LogInWithGoogleFailure: $message';
  }
}

class LogInWithAppleFailure implements Exception {
  const LogInWithAppleFailure([
    this.message = 'An unknown exception occurred.',
    this.status = FormzStatus.submissionFailure,
  ]);

  factory LogInWithAppleFailure.fromCode(String code) {
    switch (code) {
      case 'canceled':
        return const LogInWithAppleFailure('', FormzStatus.submissionCanceled);
      default:
        return LogInWithAppleFailure(code);
    }
  }

  final String message;
  final FormzStatus status;
}
