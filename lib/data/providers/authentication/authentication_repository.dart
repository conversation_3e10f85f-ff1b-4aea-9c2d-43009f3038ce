import 'dart:async';

import 'package:firebase_auth/firebase_auth.dart';
import 'package:mevolve/data/providers/authentication/config/auth_repository_config.dart';

abstract class AuthenticationRepository {
  /// Factory constructor to create the appropriate authentication implementation
  factory AuthenticationRepository.create() {
    return AuthRepositoryConfig.currentRepositoryFactory();
  }

  Stream<User?> get user;

  User? get currentUser;

  bool get isNewUser;

  Future<void> useEmulator();

  Future<void> updateUserDisplayName(String displayName);

  Future<User?> logInWithGoogle();

  Future<Map<String, dynamic>?> logInWithGoogleCalendar();

  Future<DateTime?> getTokenAuthTime();

  Future<(User?, String?)?> logInWithApple();

  Future<User?> logInWithEmailOtp(String token, bool isNewUser);

  Future<void> deleteAccount();

  Future<void> revokeAppleTokenWithAuthorizationCode(String authorizationCode);

  Future<void> logOut();
}
