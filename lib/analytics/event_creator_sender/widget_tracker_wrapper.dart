import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_formation.dart';
import 'package:mevolve/data/enums/firebase_document_type.dart';
import 'package:mevolve/data/enums/habit_type.dart';
import 'package:mevolve/data/enums/theme_type.dart';
import 'package:mevolve/utilities/logger/me_logger_pkg.dart';
import 'package:visibility_detector/visibility_detector.dart';

enum TrackAction {
  // ---- Main Screens ----
  notes,
  pastTodo,
  pastHabit,
  pastJournal,
  pastMoneyTracker,
  pastCalendar,
  today,
  unscheduled,
  overdue,
  futureTodo,
  futureHabit,
  futureJournal,
  futureMoneyTracker,
  futureCalendar,
  lists,
  // ---- Main Screens ----

  // ---- Drawer Screens ----
  drawerFeatures,
  drawerInsights,
  drawerAttachment,
  drawerTrash,
  drawerAccount,
  drawerApp,
  drawerNotifications,
  drawerSubscription,
  drawerSupport,
  drawerFeatureGuide,
  drawerFeedback,
  // ---- Drawer Screens ----

  // ---- Other Screens ----
  updateInfo,
  dataMigrationORSyncing,
  termsOfUse,
  encryptionPassword,
  passcode,
  privacyPolicy,
  cookiesPolicy,
  // ---- Other Screens ----

  // ---- No screen /Start screen ----
  startScreen,

  // ---- Features Item Widgets ----
  todoAction,
  myListsItem,
  noteAction,
  habitAction,
  habitYesOrNoAction,
  habitNumberAction,
  habitTimeTypeAction,
  habitSingleChoiceAction,
  habitMultiChoiceAction,
  journalAction,
  moneyTrackerAction,
  calendarAction,
  // ---- Features Item Widgets ----

  // ---- Features Setup Widgets ----
  habitSetup,
  habitYesOrNoSetup,
  habitNumberSetup,
  habitTimeTypeSetup,
  habitSingleChoiceSetup,
  habitMultiChoiceSetup,
  journalSetup,
  moneyTrackerSetup,
  calendarSetup,
  calendarIntegrations,
  // ---- Features Setup Widgets ----

  // ---- User Preferences ----
  dark,
  light,
  systemDefault,
  greenDark,
  greenLight,
  theme1Dark,
  theme1Light,
  theme2Dark,
  theme2Light,
  theme3Dark,
  theme3Light,
  theme4Dark,
  theme4Light,
  theme5Dark,
  theme5Light,
  theme6Dark,
  theme6Light,
  theme7Dark,
  theme7Light,
  theme8Dark,
  theme8Light,
  theme9Dark,
  theme9Light,
  theme10Dark,
  theme10Light,
  // ---- User Preferences ----

  // ---- Other actions ----
  addFeaturesSheet,

  // ---- Public page actions ----
  publicProfile,
  publicList,
  publicNote,
  publicProfileFollow,
  publicProfileShare,
  // ---- Public page actions ----

  // ---- Data actions ----
  dataDelete,
  dataImport,
  dataExport,
  // ---- Data actions ----

  // ---- Other actions ----

  // ---- Firebase Collection Docs ----
  usersCollectionDoc,
  specialActivitiesCollectionDoc,
  usersMetadataCollectionDoc,
  publicUsersCollectionDoc,
  viewSettingsCollectionDoc,
  userResourcesCollectionDoc,
  todosCollectionDoc,
  habitSetupsCollectionDoc,
  habitActionsCollectionDoc,
  journalSetupsCollectionDoc,
  journalActionsCollectionDoc,
  notesCollectionDoc,
  listsCollectionDoc,
  chatUsersCollectionDoc,
  chatMessagesCollectionDoc,
  snippetsCollectionDoc,
  viewedSnippetsCollectionDoc,
  usersFeedbackCollectionDoc,
  inAppNotificationsCollectionDoc,
  moneyTrackerTransactionsCollectionDoc,
  moneyTrackerSetupsCollectionDoc,
  calendarEventSetupsCollectionDoc,
  calendarEventActionsCollectionDoc,
  calendarIntegrationsCollectionDoc,
  releaseConfigsCollectionDoc;
  // ---- Firebase Collection Docs ----

  static TrackAction trackActionTypeFromHabitSetupActionType(
    HabitType habitType,
  ) {
    switch (habitType) {
      case HabitType.boolean:
        return habitYesOrNoAction;
      case HabitType.timer:
        return habitTimeTypeAction;
      case HabitType.numeric:
        return habitNumberAction;
      case HabitType.single:
        return habitSingleChoiceAction;
      case HabitType.multiple:
        return habitMultiChoiceAction;
    }
  }

  static TrackAction trackActionTypeFromHabitSetupType(HabitType habitType) {
    switch (habitType) {
      case HabitType.boolean:
        return habitYesOrNoSetup;
      case HabitType.timer:
        return habitTimeTypeSetup;
      case HabitType.numeric:
        return habitNumberSetup;
      case HabitType.single:
        return habitSingleChoiceSetup;
      case HabitType.multiple:
        return habitMultiChoiceSetup;
    }
  }

  String? get trackSubActionValue {
    switch (this) {
      // ---- Features Item Widgets ----
      case habitYesOrNoAction:
        return 'yesOrNo';
      case habitNumberAction:
        return 'number';
      case habitTimeTypeAction:
        return 'time';
      case habitSingleChoiceAction:
        return 'singleChoice';
      case habitMultiChoiceAction:
        return 'multiChoice';

      // ---- Features Setup Widgets ----
      case habitYesOrNoSetup:
        return 'yesOrNo';
      case habitNumberSetup:
        return 'number';
      case habitTimeTypeSetup:
        return 'time';
      case habitSingleChoiceSetup:
        return 'singleChoice';
      case habitMultiChoiceSetup:
        return 'multiChoice';

      default:
        return null;
    }
  }

  String get trackActionValue {
    switch (this) {
      // ---- Main Screens ----
      case notes:
        return 'notes';
      case pastTodo:
        return 'pastTodo';
      case pastHabit:
        return 'pastHabit';
      case pastJournal:
        return 'pastJournal';
      case pastMoneyTracker:
        return 'pastMoneyTracker';
      case pastCalendar:
        return 'pastCalendar';
      case today:
        return 'today';
      case unscheduled:
        return 'unscheduled';
      case overdue:
        return 'overdue';
      case futureTodo:
        return 'futureTodo';
      case futureHabit:
        return 'futureHabit';
      case futureJournal:
        return 'futureJournal';
      case futureMoneyTracker:
        return 'futureMoneyTracker';
      case futureCalendar:
        return 'futureCalendar';
      case lists:
        return 'lists';

      // ---- Drawer Screens ----
      case drawerFeatures:
        return 'drawerFeatures';
      case drawerInsights:
        return 'drawerInsights';
      case drawerAttachment:
        return 'drawerAttachment';
      case drawerTrash:
        return 'drawerTrash';
      case drawerAccount:
        return 'drawerAccount';
      case drawerApp:
        return 'drawerApp';
      case drawerNotifications:
        return 'drawerNotifications';
      case drawerSubscription:
        return 'drawerSubscription';
      case drawerSupport:
        return 'drawerSupport';
      case drawerFeatureGuide:
        return 'drawerFeatureGuide';
      case drawerFeedback:
        return 'drawerFeedback';

      // ---- Other Screens ----
      case updateInfo:
        return 'updateInfo';
      case dataMigrationORSyncing:
        return 'dataMigrationORSyncing';
      case termsOfUse:
        return 'termsOfUse';
      case encryptionPassword:
        return 'encryptionPassword';
      case passcode:
        return 'passcode';
      case privacyPolicy:
        return 'privacyPolicy';
      case cookiesPolicy:
        return 'cookiesPolicy';

      case startScreen:
        return 'startScreen';

      // ---- Features Item Widgets ----
      case todoAction:
        return 'todoAction';
      case myListsItem:
        return 'myListItem';
      case noteAction:
        return 'noteAction';
      case habitAction:
        return 'habitAction';
      case habitYesOrNoAction:
        return 'habitAction';
      case habitNumberAction:
        return 'habitAction';
      case habitTimeTypeAction:
        return 'habitAction';
      case habitSingleChoiceAction:
        return 'habitAction';
      case habitMultiChoiceAction:
        return 'habitAction';
      case journalAction:
        return 'journalAction';
      case moneyTrackerAction:
        return 'moneyTrackerAction';
      case calendarAction:
        return 'calendarAction';

      // ---- Features Setup Widgets ----
      case habitSetup:
        return 'habitSetup';
      case habitYesOrNoSetup:
        return 'habitSetup';
      case habitNumberSetup:
        return 'habitSetup';
      case habitTimeTypeSetup:
        return 'habitSetup';
      case habitSingleChoiceSetup:
        return 'habitSetup';
      case habitMultiChoiceSetup:
        return 'habitSetup';
      case journalSetup:
        return 'journalSetup';
      case moneyTrackerSetup:
        return 'moneyTrackerSetup';
      case calendarSetup:
        return 'calendarSetup';
      case calendarIntegrations:
        return 'calendarIntegrations';

      // ---- User Preferences ----
      case dark:
        return 'dark';
      case light:
        return 'light';
      case systemDefault:
        return 'systemDefault';
      case greenDark:
        return 'greenDark';
      case greenLight:
        return 'greenLight';
      case theme1Dark:
        return 'theme1Dark';
      case theme1Light:
        return 'theme1Light';
      case theme2Dark:
        return 'theme2Dark';
      case theme2Light:
        return 'theme2Light';
      case theme3Dark:
        return 'theme3Dark';
      case theme3Light:
        return 'theme3Light';
      case theme4Dark:
        return 'theme4Dark';
      case theme4Light:
        return 'theme4Light';
      case theme5Dark:
        return 'theme5Dark';
      case theme5Light:
        return 'theme5Light';
      case theme6Dark:
        return 'theme6Dark';
      case theme6Light:
        return 'theme6Light';
      case theme7Dark:
        return 'theme7Dark';
      case theme7Light:
        return 'theme7Light';
      case theme8Dark:
        return 'theme8Dark';
      case theme8Light:
        return 'theme8Light';
      case theme9Dark:
        return 'theme9Dark';
      case theme9Light:
        return 'theme9Light';
      case theme10Dark:
        return 'theme10Dark';
      case theme10Light:
        return 'theme10Light';

      // ---- Other actions ----
      case addFeaturesSheet:
        return 'addFeaturesSheet';

      // ---- Public page actions ----
      case publicProfile:
        return 'profile';
      case publicList:
        return 'list';
      case publicNote:
        return 'note';
      case publicProfileFollow:
        return 'follow';
      case publicProfileShare:
        return 'profile_share';

      // ---- Data actions ----
      case dataDelete:
        return 'delete';
      case dataImport:
        return 'import';
      case dataExport:
        return 'export';

      // ---- Firebase Collection Docs ----
      case usersCollectionDoc:
        return 'usersCollectionDoc';
      case specialActivitiesCollectionDoc:
        return 'specialActivitiesCollectionDoc';
      case usersMetadataCollectionDoc:
        return 'usersMetadataCollectionDoc';
      case publicUsersCollectionDoc:
        return 'publicUsersCollectionDoc';
      case viewSettingsCollectionDoc:
        return 'viewSettingsCollectionDoc';
      case userResourcesCollectionDoc:
        return 'userResourcesCollectionDoc';
      case todosCollectionDoc:
        return 'todosCollectionDoc';
      case habitSetupsCollectionDoc:
        return 'habitSetupsCollectionDoc';
      case habitActionsCollectionDoc:
        return 'habitActionsCollectionDoc';
      case journalSetupsCollectionDoc:
        return 'journalSetupsCollectionDoc';
      case journalActionsCollectionDoc:
        return 'journalActionsCollectionDoc';
      case notesCollectionDoc:
        return 'notesCollectionDoc';
      case listsCollectionDoc:
        return 'listsCollectionDoc';
      case chatUsersCollectionDoc:
        return 'chatUsersCollectionDoc';
      case chatMessagesCollectionDoc:
        return 'chatMessagesCollectionDoc';
      case snippetsCollectionDoc:
        return 'snippetsCollectionDoc';
      case viewedSnippetsCollectionDoc:
        return 'viewedSnippetsCollectionDoc';
      case usersFeedbackCollectionDoc:
        return 'usersFeedbackCollectionDoc';
      case inAppNotificationsCollectionDoc:
        return 'inAppNotificationsCollectionDoc';
      case moneyTrackerTransactionsCollectionDoc:
        return 'moneyTrackerTransactionsCollectionDoc';
      case moneyTrackerSetupsCollectionDoc:
        return 'moneyTrackerSetupsCollectionDoc';
      case calendarEventSetupsCollectionDoc:
        return 'calendarEventSetupsCollectionDoc';
      case calendarEventActionsCollectionDoc:
        return 'calendarEventActionsCollectionDoc';
      case calendarIntegrationsCollectionDoc:
        return 'calendarIntegrationsCollectionDoc';
      case releaseConfigsCollectionDoc:
        return 'releaseConfigsCollectionDoc';
    }
  }

  String get id {
    // Create ids for all enums in TrackScreen
    // Like for screens use prefix 's_' and for widgets use prefix 'w_' and after that just use
    // a consecutive number and pad number by 000
    switch (this) {
      // ---- Main Screens ----
      case notes:
        return 's_001';
      case pastTodo:
        return 's_002';
      case pastHabit:
        return 's_003';
      case pastJournal:
        return 's_004';
      case pastMoneyTracker:
        return 's_005';
      case pastCalendar:
        return 's_006';
      case today:
        return 's_007';
      case unscheduled:
        return 's_008';
      case overdue:
        return 's_009';
      case futureTodo:
        return 's_010';
      case futureHabit:
        return 's_011';
      case futureJournal:
        return 's_012';
      case futureMoneyTracker:
        return 's_013';
      case futureCalendar:
        return 's_014';
      case lists:
        return 's_015';

      // ---- Drawer Screens ----
      case drawerFeatures:
        return 's_014';
      case drawerInsights:
        return 's_015';
      case drawerAttachment:
        return 's_016';
      case drawerTrash:
        return 's_017';
      case drawerAccount:
        return 's_018';
      case drawerApp:
        return 's_019';
      case drawerNotifications:
        return 's_020';
      case drawerSubscription:
        return 's_021';
      case drawerSupport:
        return 's_022';
      case drawerFeatureGuide:
        return 's_023';
      case drawerFeedback:
        return 's_024';

      // ---- Other Screens ----
      case updateInfo:
        return 's_025';
      case dataMigrationORSyncing:
        return 's_026';
      case termsOfUse:
        return 's_027';
      case encryptionPassword:
        return 's_028';
      case passcode:
        return 's_029';
      case privacyPolicy:
        return 's_030';
      case cookiesPolicy:
        return 's_031';

      case startScreen:
        return 's_032';

      // ---- Features Item Widgets ----
      // Using same id for same value TrackAction
      case todoAction:
        return 'w_001';
      case myListsItem:
        return 'w_002';
      case noteAction:
        return 'w_003';
      case habitAction:
        return 'w_004';
      case habitYesOrNoAction:
        return 'w_004';
      case habitNumberAction:
        return 'w_004';
      case habitTimeTypeAction:
        return 'w_004';
      case habitSingleChoiceAction:
        return 'w_004';
      case habitMultiChoiceAction:
        return 'w_004';
      case journalAction:
        return 'w_005';
      case moneyTrackerAction:
        return 'w_006';
      case calendarAction:
        return 'w_007';

      // ---- Features Setup Widgets ----
      // Using same id for same value TrackAction
      case habitSetup:
        return 'w_008';
      case habitYesOrNoSetup:
        return 'w_008';
      case habitNumberSetup:
        return 'w_008';
      case habitTimeTypeSetup:
        return 'w_008';
      case habitSingleChoiceSetup:
        return 'w_008';
      case habitMultiChoiceSetup:
        return 'w_008';
      case journalSetup:
        return 'w_009';
      case moneyTrackerSetup:
        return 'w_010';
      case calendarSetup:
        return 'w_011';
      case calendarIntegrations:
        return 'w_012';

      // ---- User Preferences ----
      case dark:
        return 'p_001';
      case light:
        return 'p_002';
      case systemDefault:
        return 'p_003';
      case greenDark:
        return 'p_004';
      case greenLight:
        return 'p_005';
      case theme1Dark:
        return 'p_006';
      case theme1Light:
        return 'p_007';
      case theme2Dark:
        return 'p_008';
      case theme2Light:
        return 'p_009';
      case theme3Dark:
        return 'p_010';
      case theme3Light:
        return 'p_011';
      case theme4Dark:
        return 'p_012';
      case theme4Light:
        return 'p_013';
      case theme5Dark:
        return 'p_014';
      case theme5Light:
        return 'p_015';
      case theme6Dark:
        return 'p_016';
      case theme6Light:
        return 'p_017';
      case theme7Dark:
        return 'p_018';
      case theme7Light:
        return 'p_019';
      case theme8Dark:
        return 'p_020';
      case theme8Light:
        return 'p_021';
      case theme9Dark:
        return 'p_022';
      case theme9Light:
        return 'p_023';
      case theme10Dark:
        return 'p_024';
      case theme10Light:
        return 'p_025';

      // ---- Other actions ----
      case addFeaturesSheet:
        return 'o_001';

      // ---- Public page actions ----
      case publicProfile:
        return 'pp_001';
      case publicList:
        return 'pp_002';
      case publicNote:
        return 'pp_003';
      case publicProfileFollow:
        return 'pp_004';
      case publicProfileShare:
        return 'pp_005';

      // ---- Data actions ----
      case dataDelete:
        return 'da_001';
      case dataImport:
        return 'da_002';
      case dataExport:
        return 'da_003';

      // ---- Firebase Collection Docs ----
      case usersCollectionDoc:
        return 'f_001';
      case specialActivitiesCollectionDoc:
        return 'f_002';
      case usersMetadataCollectionDoc:
        return 'f_003';
      case publicUsersCollectionDoc:
        return 'f_004';
      case viewSettingsCollectionDoc:
        return 'f_005';
      case userResourcesCollectionDoc:
        return 'f_006';
      case todosCollectionDoc:
        return 'f_007';
      case habitSetupsCollectionDoc:
        return 'f_008';
      case habitActionsCollectionDoc:
        return 'f_009';
      case journalSetupsCollectionDoc:
        return 'f_010';
      case journalActionsCollectionDoc:
        return 'f_011';
      case notesCollectionDoc:
        return 'f_012';
      case listsCollectionDoc:
        return 'f_013';
      case chatUsersCollectionDoc:
        return 'f_014';
      case chatMessagesCollectionDoc:
        return 'f_015';
      case snippetsCollectionDoc:
        return 'f_016';
      case viewedSnippetsCollectionDoc:
        return 'f_017';
      case usersFeedbackCollectionDoc:
        return 'f_018';
      case inAppNotificationsCollectionDoc:
        return 'f_019';
      case moneyTrackerTransactionsCollectionDoc:
        return 'f_020';
      case moneyTrackerSetupsCollectionDoc:
        return 'f_021';
      case calendarEventSetupsCollectionDoc:
        return 'f_022';
      case calendarEventActionsCollectionDoc:
        return 'f_023';
      case calendarIntegrationsCollectionDoc:
        return 'f_024';
      case releaseConfigsCollectionDoc:
        return 'f_025';
    }
  }

  static TrackAction getThemeColorAction({
    required ThemeType themeModeType,
    required String themeColorType,
    required ActionType actionType,
  }) {
    TrackAction trackAction;

    if (actionType == ActionType.colourSelect) {
      // Initialize default values for color selection
      TrackAction darkFallback = greenDark;
      TrackAction lightFallback = greenLight;

      ThemeType tempThemeModeType = themeModeType;
      if (tempThemeModeType == ThemeType.systemDefault) {
        // Current theme of device
        var brightness =
            SchedulerBinding.instance.platformDispatcher.platformBrightness;
        bool isDarkMode = brightness == Brightness.dark;
        tempThemeModeType = isDarkMode ? ThemeType.dark : ThemeType.light;
      }

      switch (tempThemeModeType) {
        case ThemeType.dark:
          switch (themeColorType) {
            case 'green':
              trackAction = greenDark;
              break;
            case 'theme1':
              trackAction = theme1Dark;
              break;
            case 'theme2':
              trackAction = theme2Dark;
              break;
            case 'theme3':
              trackAction = theme3Dark;
              break;
            case 'theme4':
              trackAction = theme4Dark;
              break;
            case 'theme5':
              trackAction = theme5Dark;
              break;
            case 'theme6':
              trackAction = theme6Dark;
              break;
            case 'theme7':
              trackAction = theme7Dark;
              break;
            case 'theme8':
              trackAction = theme8Dark;
              break;
            case 'theme9':
              trackAction = theme9Dark;
              break;
            case 'theme10':
              trackAction = theme10Dark;
              break;
            default:
              trackAction = darkFallback; // Fallback to greenDark
              break;
          }
          break;
        case ThemeType.light:
          switch (themeColorType) {
            case 'green':
              trackAction = greenLight;
              break;
            case 'theme1':
              trackAction = theme1Light;
              break;
            case 'theme2':
              trackAction = theme2Light;
              break;
            case 'theme3':
              trackAction = theme3Light;
              break;
            case 'theme4':
              trackAction = theme4Light;
              break;
            case 'theme5':
              trackAction = theme5Light;
              break;
            case 'theme6':
              trackAction = theme6Light;
              break;
            case 'theme7':
              trackAction = theme7Light;
              break;
            case 'theme8':
              trackAction = theme8Light;
              break;
            case 'theme9':
              trackAction = theme9Light;
              break;
            case 'theme10':
              trackAction = theme10Light;
              break;
            default:
              trackAction = lightFallback; // Fallback to greenLight
              break;
          }
          break;
        // This case should not be reached after the above check
        case ThemeType.systemDefault:
          trackAction = darkFallback; // Adding additional safety
          break;
      }
    } else {
      // For theme selection, use systemDefault as fallback
      switch (themeModeType) {
        case ThemeType.dark:
          trackAction = dark;
          break;
        case ThemeType.light:
          trackAction = light;
          break;
        case ThemeType.systemDefault:
          trackAction = systemDefault; // Fallback for theme selection
          break;
      }
    }

    return trackAction;
  }

  static TrackAction? getTrackActionsFromFirestoreCollectionName({
    required String? firestoreCollectionName,
  }) {
    if (firestoreCollectionName == null) return null;
    // Check if firestoreCollectionName is in FirebaseDocCollectionType
    // if in then get the type
    FirebaseDocCollectionType? docCollectionType =
        FirebaseDocCollectionType.values.byName(firestoreCollectionName);
    switch (docCollectionType) {
      case FirebaseDocCollectionType.users:
        return usersCollectionDoc;
      case FirebaseDocCollectionType.usersMetadata:
        return usersMetadataCollectionDoc;
      case FirebaseDocCollectionType.viewSettings:
        return viewSettingsCollectionDoc;
      case FirebaseDocCollectionType.userResources:
        return userResourcesCollectionDoc;
      case FirebaseDocCollectionType.todos:
        return todosCollectionDoc;
      case FirebaseDocCollectionType.habitSetups:
        return habitSetupsCollectionDoc;
      case FirebaseDocCollectionType.habitActions:
        return habitActionsCollectionDoc;
      case FirebaseDocCollectionType.journalSetups:
        return journalSetupsCollectionDoc;
      case FirebaseDocCollectionType.journalActions:
        return journalActionsCollectionDoc;
      case FirebaseDocCollectionType.notes:
        return notesCollectionDoc;
      case FirebaseDocCollectionType.lists:
        return listsCollectionDoc;
      case FirebaseDocCollectionType.chatUsers:
        return chatUsersCollectionDoc;
      case FirebaseDocCollectionType.chatMessages:
        return chatMessagesCollectionDoc;
      case FirebaseDocCollectionType.snippets:
        return snippetsCollectionDoc;
      case FirebaseDocCollectionType.viewedSnippets:
        return viewedSnippetsCollectionDoc;
      case FirebaseDocCollectionType.usersFeedback:
        return usersFeedbackCollectionDoc;
      case FirebaseDocCollectionType.inAppNotifications:
        return inAppNotificationsCollectionDoc;
      case FirebaseDocCollectionType.moneyTrackerTransactions:
        return moneyTrackerTransactionsCollectionDoc;
      case FirebaseDocCollectionType.moneyTrackerSetups:
        return moneyTrackerSetupsCollectionDoc;
      case FirebaseDocCollectionType.calendarEventSetups:
        return calendarEventSetupsCollectionDoc;
      case FirebaseDocCollectionType.calendarEventActions:
        return calendarEventActionsCollectionDoc;
      case FirebaseDocCollectionType.calendarIntegrations:
        return calendarIntegrationsCollectionDoc;
      case FirebaseDocCollectionType.publicUsers:
        return publicUsersCollectionDoc;
      case FirebaseDocCollectionType.releaseConfigs:
        return releaseConfigsCollectionDoc;
      case FirebaseDocCollectionType.specialActivities:
        return specialActivitiesCollectionDoc;
    }
  }
}

class WidgetTracker extends StatefulWidget {
  const WidgetTracker({
    Key? key,
    required this.child,
    required this.trackAction,
  }) : super(key: key);

  final Widget child;
  final TrackAction trackAction;

  @override
  State<WidgetTracker> createState() => _WidgetTrackerState();
}

class _WidgetTrackerState extends State<WidgetTracker> {
  final Log _log = MeLogger.getLogger(LogTags.analytics);

  DateTime? enterTime;

  late String widgetId = 'widget_${widget.trackAction.trackActionValue}';

  @override
  void initState() {
    super.initState();
    _trackWidgetVisibleOrVisit();
  }

  @override
  void dispose() {
    _trackWidgetNotVisibleOrDispose();
    super.dispose();
  }

  void _trackWidgetVisibleOrVisit() {
    // If widget is already visited or visible then return.
    if (enterTime != null) return;
    // Log widget if visit or visible
    enterTime = DateTime.now();
    EventFormation().sendWidgetEvent(
      trackAction: widget.trackAction,
      enterTime: enterTime!,
      exitTime: null,
    );
    _log.d('Widget ID: $widgetId visited');
  }

  void _trackWidgetNotVisibleOrDispose() {
    // If widget is not visited or visible then return.
    if (enterTime == null) return;
    // Log widget if not visible or disposed
    EventFormation().sendWidgetEvent(
      trackAction: widget.trackAction,
      enterTime: enterTime!,
      exitTime: DateTime.now(),
    );
    enterTime = null;
    _log.d('Widget ID: $widgetId disposed and event sent');
  }

  @override
  Widget build(BuildContext context) {
    return VisibilityDetector(
      key: Key(widgetId),
      onVisibilityChanged: (visibilityInfo) {
        var visiblePercentage = visibilityInfo.visibleFraction * 100;
        // _log.d('Widget ID: $widgetId is $visiblePercentage% visible');
        if (visiblePercentage == 0) {
          _trackWidgetNotVisibleOrDispose();
        } else if (visiblePercentage == 100) {
          _trackWidgetVisibleOrVisit();
        }
      },
      child: widget.child,
    );
  }
}
