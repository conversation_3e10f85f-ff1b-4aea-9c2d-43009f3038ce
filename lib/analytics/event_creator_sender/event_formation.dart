import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/event_queue_manager.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/analytics/events/base_event.dart';
import 'package:mevolve/analytics/events/p_app_exit_event.dart';
import 'package:mevolve/analytics/events/p_app_open_event.dart';
import 'package:mevolve/analytics/events/p_app_subscribed_event.dart';
import 'package:mevolve/analytics/events/p_backup_sync_complete_event.dart';
import 'package:mevolve/analytics/events/p_crud_action_event.dart';
import 'package:mevolve/analytics/events/p_data_action_event.dart';
import 'package:mevolve/analytics/events/p_feature_shared_event.dart';
import 'package:mevolve/analytics/events/p_filter_applied_event.dart';
import 'package:mevolve/analytics/events/p_functions_call_event.dart';
import 'package:mevolve/analytics/events/p_screen_enter_event.dart';
import 'package:mevolve/analytics/events/p_screen_exit_event.dart';
import 'package:mevolve/analytics/events/p_sync_complete_event.dart';
import 'package:mevolve/analytics/events/p_user_login_event.dart';
import 'package:mevolve/analytics/events/p_user_preference_event.dart';
import 'package:mevolve/analytics/events/pp_public_screen_viewed_event.dart';
import 'package:mevolve/analytics/events/pp_public_profile_cta_clicks_event.dart';
import 'package:mevolve/analytics/events/pp_public_screen_save_event.dart';
import 'package:mevolve/analytics/install_attribution/campaign_tracking_service.dart';
import 'package:mevolve/data/enums/theme_type.dart';
import 'package:mevolve/features/logger/view/debug_settings_tab.dart';

import 'dart:async';

import 'package:mevolve/utilities/logger/me_logger_pkg.dart';

class EventFormation {
  // Singleton pattern
  EventFormation._internal();

  factory EventFormation() => _instance;
  final Log _log = MeLogger.getLogger(LogTags.analytics);

  static final EventFormation _instance = EventFormation._internal();

  DateTime appEnterTime = DateTime.now();

  // Stack to track currently active TrackActions
  final List<TrackAction> _activeTrackActionsStack = [];

  void sendFilterEvent({
    required TrackFilterType filterType,
    required TrackFilterTypeValue filterTypeValue,
  }) async {
    AnalyticsEvent event = PFilterAppliedEvent(
      filterType: filterType,
      filterValue: filterTypeValue,
    );

    _addEvent(event);
  }

  void sendBackupSyncEvent({
    required Map<TrackAction?, int> syncedActionsItems,
    required SyncStatus syncStatus,
    required SyncErrorCode errorCode,
    required DateTime? start,
  }) async {
    AnalyticsDuration syncDuration = AnalyticsDuration(
      start == null ? null : DateTime.now().difference(start),
    );
    AnalyticsEvent event = PBackupSyncCompleteEvent(
      syncDuration: syncDuration,
      syncStatus: syncStatus,
      errorCode: errorCode,
      syncedActionsItems: syncedActionsItems,
    );

    _addEvent(event);
  }

  void sendSyncEvent({
    required SyncDirection syncDirection,
    required List<(TrackAction?, int)> syncedActionsItems,
    required SyncStatus syncStatus,
    required SyncErrorCode errorCode,
    required DateTime? start,
  }) async {
    if (syncedActionsItems.isEmpty) return;
    AnalyticsDuration syncDuration = AnalyticsDuration(
      start == null ? null : DateTime.now().difference(start),
    );
    for ((TrackAction?, int) syncedActionItems in syncedActionsItems) {
      if (syncedActionItems.$2 == 0) {
        _log.d('No items synced, skipping sync event');
        continue;
      }

      // --------- For debugging purposes only ---------
      if (syncDirection == SyncDirection.cloudToDevice) {
        // Add to readsCalledListForDebug as a Map with string key and bool value
        if (syncedActionItems.$1 != null) {
          readsCalledListForDebug.add(
            {syncedActionItems.$1!.name: syncStatus == SyncStatus.success},
          );
        }
      } else {
        // For device to cloud, add to writesCalledListForDebug
        if (syncedActionItems.$1 != null) {
          writesCalledListForDebug.add(
            {syncedActionItems.$1!.name: syncStatus == SyncStatus.success},
          );
        }
      }
      // --------- For debugging purposes only ---------
      AnalyticsEvent event = PSyncCompleteEvent(
        syncDirection: syncDirection,
        itemSyncedCount: syncedActionItems.$2,
        syncDuration: syncDuration,
        syncStatus: syncStatus,
        errorCode: errorCode,
        trackAction: syncedActionItems.$1,
      );

      _addEvent(event);
    }
  }

  void sendFunctionCallEvent({
    required String functionName,
    required bool isSuccess,
    required DateTime? start,
  }) async {
    // --------- For debugging purposes only ---------
    functionsCalledListForDebug.add({functionName: isSuccess});
    // --------- For debugging purposes only ---------

    AnalyticsDuration duration = AnalyticsDuration(
      start == null ? null : DateTime.now().difference(start),
    );
    AnalyticsEvent event = PFunctionCallEvent(
      name: functionName,
      duration: duration,
      result:
          isSuccess ? FunctionCallResult.success : FunctionCallResult.failure,
    );

    _addEvent(event);
  }

  void sendSubscriptionEvent({
    required double subscriptionAmount,
    required String currency,
  }) async {
    AnalyticsEvent event = PAppSubscribeEvent(
      subscriptionAmount: subscriptionAmount,
      currency: currency,
    );

    _addEvent(event);
  }

  void sendFeatureSharedEvent({
    required TrackAction trackAction,
    required ShareMethod? shareMethod,
    required ShareStatus sharingStatus,
    required UserRoleOfSharer? userRole,
    required int? sharedCount,
  }) {
    AnalyticsEvent event = PFeatureSharedEvent(
      shareMethod: shareMethod,
      sharingStatus: sharingStatus,
      sharedCount: sharedCount,
      userRole: userRole,
      trackAction: trackAction,
    );

    _addEvent(event);
  }

  void sendThemeColorChangeEvent({
    required ThemeType themeModeType,
    required String themeColorType,
    required ActionType actionType,
  }) {
    TrackAction trackAction = TrackAction.getThemeColorAction(
      themeModeType: themeModeType,
      themeColorType: themeColorType,
      actionType: actionType,
    );
    if (actionType == ActionType.colourSelect &&
        trackAction == TrackAction.systemDefault) {
      // Don't log wrong trackAction.
      // This happens if the color is not tracked for theme events. Add that theme.
      return;
    }
    AnalyticsEvent event = PUserPreferenceEvent(
      actionType: actionType,
      trackAction: trackAction,
    );

    _addEvent(event);
  }

  Future<void> sendLoginEvent({
    required LoginMethod loginMethod,
    required Future<(LoginStatus, LoginErrorCode?)> Function()
        sendOnFuncComplete,
  }) async {
    // Start the duration
    DateTime start = DateTime.now();
    (LoginStatus, LoginErrorCode?) res = await sendOnFuncComplete();
    AnalyticsDuration loginDuration =
        AnalyticsDuration(DateTime.now().difference(start));
    AnalyticsEvent event = PUserLoginEvent(
      loginMethod: loginMethod,
      duration: loginDuration,
      loginStatus: res.$1,
      errorCode: res.$2,
    );

    _addEvent(event);
  }

  void sendFeatureCrudActivityEvent({
    required ActionType actionType,
    required TrackAction trackAction,
    required ShareMethod? shareMethod,
    required ShareStatus sharingStatus,
    required UserRoleOfSharer? userRole,
    required int? sharedCount,
  }) {
    AnalyticsEvent event = PCrudActionEvent(
      actionType: actionType,
      trackAction: trackAction,
      shareMethod: shareMethod,
      sharingStatus: sharingStatus,
      userRole: userRole,
      sharedCount: sharedCount,
    );

    _addEvent(event);
  }

  void sendDataActionEvent({
    required TrackAction trackAction,
    required DateTime startTime,
    required DateTime endTime,
  }) {
    AnalyticsEvent event = PDataActionEvent(
      trackAction: trackAction,
      startTime: AnalyticsTimeStamp(startTime),
      endTime: AnalyticsTimeStamp(endTime),
    );

    _addEvent(event);
  }

  void sendAppOpenExitEvent([bool openEvent = true]) {
    if (openEvent) {
      appEnterTime = DateTime.now();
    }

    _addEvent(
      openEvent
          ? PAppOpenEvent(
              firstOpen: _isFirstAppOpen(),
            )
          : PAppExitEvent(
              trackAction: _activeTrackActionsStack.isNotEmpty
                  ? _activeTrackActionsStack.last
                  : TrackAction.today,
            ),
    );
  }

  bool _isFirstAppOpen() {
    // Check if this is the first app open using m_first_visit from campaign attribution
    try {
      final attribution = CampaignTrackingService().attributionModel;
      return attribution.m_first_visit == 'true';
    } catch (e) {
      _log.e('Error determining first app open: $e');
      return false;
    }
  }

  void sendWidgetEvent({
    required TrackAction trackAction,
    required DateTime enterTime,
    DateTime? exitTime,
  }) {
    AnalyticsEvent event;
    if (exitTime == null) {
      // Get the previous screen from the stack (last active widget/screen)
      TrackAction? previousTrackAction = _activeTrackActionsStack.isNotEmpty
          ? _activeTrackActionsStack.last
          : TrackAction.startScreen;

      event = PScreenEnterEvent(
        trackAction: trackAction,
        lastTrackAction: previousTrackAction,
      );

      // Add current trackAction to the stack
      _activeTrackActionsStack.add(trackAction);
    } else {
      // Remove from stack when widget exits
      _activeTrackActionsStack.remove(trackAction);

      event = PScreenExitEvent(
        enterTime: AnalyticsTimeStamp(enterTime),
        exitTime: AnalyticsTimeStamp(exitTime),
        trackAction: trackAction,
      );
    }

    // Add event to the list for processing
    _addEvent(event);
  }

  void sendPublicScreenViewedEvent({
    required TrackAction trackAction,
  }) {
    AnalyticsEvent event = PPPublicScreenViewedEvent(
      trackAction: trackAction,
    );

    _addEvent(event);
  }

  void sendPublicProfileCtaClicksEvent({
    required TrackAction trackAction,
  }) {
    AnalyticsEvent event = PPPublicProfileCtaClicksEvent(
      trackAction: trackAction,
    );

    _addEvent(event);
  }

  void sendPublicScreenSaveEvent({
    required TrackAction trackAction,
  }) {
    AnalyticsEvent event = PPPublicScreenSaveEvent(
      trackAction: trackAction,
    );

    _addEvent(event);
  }

  void _addEvent(AnalyticsEvent event) {
    EventQueueManager().addEvent(event);
  }
}
