import 'package:mevolve/analytics/event_creator_sender/event_enums.dart';
import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/analytics/events/base_event.dart';
import 'package:mevolve/constants/app_config.dart';

class ParamActionType {
  const ParamActionType({
    this.actionType,
  });

  final ActionType? actionType;

  Map<String, String?> toMap() {
    return {
      'action_type': actionType?.name,
    };
  }
}

class ParamActionValue {
  const ParamActionValue({
    this.trackAction,
  });

  final TrackAction? trackAction;

  Map<String, String?> toMap() {
    return {
      'action_value': trackAction?.trackActionValue,
    };
  }
}

class ParamItemId {
  const ParamItemId({
    this.trackAction,
  });

  final TrackAction? trackAction;

  Map<String, String?> toMap() {
    return {
      'item_id': trackAction?.id,
    };
  }
}

class ParamEnterTime {
  const ParamEnterTime({
    this.enterTime,
  });

  final AnalyticsTimeStamp? enterTime;

  Map<String, int?> toMap() {
    return {
      'enter_time': enterTime?.value,
    };
  }
}

class ParamExitTime {
  const ParamExitTime({
    this.exitTime,
  });

  final AnalyticsTimeStamp? exitTime;

  Map<String, int?> toMap() {
    return {
      'exit_time': exitTime?.value,
    };
  }
}

class ParamSyncDuration {
  const ParamSyncDuration({
    this.syncDuration,
  });

  final AnalyticsDuration? syncDuration;

  Map<String, int?> toMap() {
    return {
      'sync_duration': syncDuration?.value,
    };
  }
}

class ParamShareStatus {
  const ParamShareStatus({
    this.shareStatus,
  });

  final ShareStatus? shareStatus;

  Map<String, String?> toMap() {
    return {
      'share_status': shareStatus?.name,
    };
  }
}

class ParamShareCount {
  const ParamShareCount({
    this.shareCount,
  });

  final int? shareCount;

  Map<String, int?> toMap() {
    return {
      'share_count': shareCount,
    };
  }
}

class ParamShareMethod {
  const ParamShareMethod({
    this.shareMethod,
  });

  final ShareMethod? shareMethod;

  Map<String, String?> toMap() {
    return {
      'share_method': shareMethod?.name,
    };
  }
}

class ParamLoginMethod {
  const ParamLoginMethod({
    this.loginMethod,
  });

  final LoginMethod? loginMethod;

  Map<String, String?> toMap() {
    return {
      'login_method': loginMethod?.name,
    };
  }
}

class ParamLoginStatus {
  const ParamLoginStatus({
    this.loginStatus,
  });

  final LoginStatus? loginStatus;

  Map<String, String?> toMap() {
    return {
      'login_status': loginStatus?.name,
    };
  }
}

class ParamErrorCode {
  const ParamErrorCode({
    this.errorCode,
  });

  final String? errorCode;

  Map<String, String?> toMap() {
    return {
      'error_code': errorCode,
    };
  }
}

class ParamSyncDirection {
  const ParamSyncDirection({
    this.syncDirection,
  });

  final SyncDirection? syncDirection;

  Map<String, String?> toMap() {
    return {
      'sync_direction': syncDirection?.name,
    };
  }
}

class ParamItemSyncedCount {
  const ParamItemSyncedCount({
    this.itemSyncedCount,
  });

  final int? itemSyncedCount;

  Map<String, int?> toMap() {
    return {
      'item_synced_count': itemSyncedCount,
    };
  }
}

class ParamBackupSyncedCount {
  const ParamBackupSyncedCount({
    this.itemsSyncedCount,
  });

  final Map<TrackAction?, int>? itemsSyncedCount;

  Map<String, dynamic> toMap() {
    return {
      'itemsSyncedCount': itemsSyncedCount?.map(
            (trackAction, count) => MapEntry(
              trackAction?.name ??
                  'unknown', // Use 'unknown' for unidentified actions
              count,
            ),
          ) ??
          {},
    };
  }
}

class ParamSyncStatus {
  const ParamSyncStatus({
    this.syncStatus,
  });

  final SyncStatus? syncStatus;

  Map<String, String?> toMap() {
    return {
      'sync_status': syncStatus?.name,
    };
  }
}

class ParamName {
  const ParamName({
    this.name,
  });

  final String? name;

  Map<String, String?> toMap() {
    return {
      'name': name,
    };
  }
}

class ParamVersion {
  const ParamVersion({
    this.version,
  });

  final String? version;

  Map<String, String?> toMap() {
    return {
      'version': version ?? AppConfig.dbVersion.toString(),
    };
  }
}

class ParamTrigger {
  const ParamTrigger({
    this.trigger,
  });

  final String? trigger;

  Map<String, String?> toMap() {
    return {
      'trigger': trigger ?? 'app',
    };
  }
}

class ParamResult {
  const ParamResult({
    this.result,
  });

  final FunctionCallResult? result;

  Map<String, String?> toMap() {
    return {
      'result': result?.name,
    };
  }
}

class ParamFilterType {
  const ParamFilterType({
    this.filterType,
  });

  final TrackFilterType? filterType;

  Map<String, String?> toMap() {
    return {
      'filter_type': filterType?.name,
    };
  }
}

class ParamFilterValue {
  const ParamFilterValue({
    this.filterValue,
  });

  final TrackFilterTypeValue? filterValue;

  Map<String, String?> toMap() {
    return {
      'filter_value': filterValue?.name,
    };
  }
}

class ParamSubscriptionAmount {
  const ParamSubscriptionAmount({
    this.subscriptionAmount,
  });

  final double? subscriptionAmount;

  Map<String, double?> toMap() {
    return {
      'subscription_amount': subscriptionAmount,
    };
  }
}

class ParamCurrency {
  const ParamCurrency({
    this.currency,
  });

  final String? currency;

  Map<String, String?> toMap() {
    return {
      'currency': currency,
    };
  }
}

class ParamDuration {
  const ParamDuration({
    this.duration,
  });

  final AnalyticsDuration? duration;

  Map<String, int?> toMap() {
    return {
      'duration': duration?.value,
    };
  }
}

class ParamSubtype {
  const ParamSubtype({
    this.trackAction,
  });

  final TrackAction? trackAction;

  Map<String, String?> toMap() {
    return {
      'subtype': trackAction?.trackSubActionValue ?? 'None',
    };
  }
}

class ParamUserRole {
  const ParamUserRole({
    this.userRole,
  });

  final UserRoleOfSharer? userRole;

  Map<String, String?> toMap() {
    return {
      'user_role':
          (userRole == null ? UserRoleOfSharer.viewer : userRole!).name,
    };
  }
}

class ParamUserType {
  const ParamUserType({
    this.userType,
  });

  final UserType? userType;

  Map<String, String?> toMap() {
    return {
      'user_type': userType?.name,
    };
  }
}

class ParamPreviousScreen {
  const ParamPreviousScreen({
    this.lastTrackAction,
  });

  final TrackAction? lastTrackAction;

  Map<String, String?> toMap() {
    return {
      'previous_screen': lastTrackAction?.trackActionValue,
    };
  }
}

class ParamFirstOpen {
  const ParamFirstOpen({
    this.firstOpen,
  });

  final bool? firstOpen;

  Map<String, bool?> toMap() {
    return {
      'first_open': firstOpen,
    };
  }
}

class ParamStartTime {
  const ParamStartTime({
    this.startTime,
  });

  final AnalyticsTimeStamp? startTime;

  Map<String, int?> toMap() {
    return {
      'start_time': startTime?.value,
    };
  }
}

class ParamEndTime {
  const ParamEndTime({
    this.endTime,
  });

  final AnalyticsTimeStamp? endTime;

  Map<String, int?> toMap() {
    return {
      'end_time': endTime?.value,
    };
  }
}

class ParamPublic {
  const ParamPublic({
    this.isPublic,
  });

  final bool? isPublic;

  Map<String, bool?> toMap() {
    return {
      'public': isPublic,
    };
  }
}

class ParamItemIdString {
  const ParamItemIdString({
    this.itemId,
  });

  final String? itemId;

  Map<String, String?> toMap() {
    return {
      'item_id': itemId,
    };
  }
}
