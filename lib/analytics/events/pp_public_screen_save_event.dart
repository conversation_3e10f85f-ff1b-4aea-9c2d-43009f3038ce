import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/analytics/events/base_event.dart';
import 'package:mevolve/analytics/parameter_groups/param_classes.dart';

class PPPublicScreenSaveEvent extends AnalyticsEvent {
  const PPPublicScreenSaveEvent({
    required this.trackAction,
  }) : super(eventId: 25, eventType: EventType.userAction);

  @override
  String get eventName => 'pp_public_screen_save';

  final TrackAction trackAction;

  @override
  Map<String, dynamic> getEventParameters() {
    return {
      ...ParamActionValue(trackAction: trackAction).toMap(),
      ...ParamItemId(trackAction: trackAction).toMap(),
    };
  }

  // Now use toMapWithParams() instead of manually creating the map
  @override
  Map<String, dynamic> toMap() => toMapWithParams();
}
