import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/analytics/events/base_event.dart';
import 'package:mevolve/analytics/parameter_groups/param_classes.dart';

class PDataActionEvent extends AnalyticsEvent {
  const PDataActionEvent({
    required this.trackAction,
    required this.startTime,
    required this.endTime,
  }) : super(eventId: 22, eventType: EventType.userAction);

  @override
  String get eventName => 'p_data_actions';

  final TrackAction trackAction;
  final AnalyticsTimeStamp startTime;
  final AnalyticsTimeStamp endTime;

  @override
  Map<String, dynamic> getEventParameters() {
    return {
      ...ParamActionValue(trackAction: trackAction).toMap(),
      ...ParamStartTime(startTime: startTime).toMap(),
      ...ParamEndTime(endTime: endTime).toMap(),
      ...ParamDuration(
        duration: AnalyticsDuration.fromTimeStamps(startTime, endTime),
      ).toMap(),
    };
  }

  // Now use toMapWithParams() instead of manually creating the map
  @override
  Map<String, dynamic> toMap() => toMapWithParams();
}
