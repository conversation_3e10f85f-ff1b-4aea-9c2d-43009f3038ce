import 'package:mevolve/analytics/event_creator_sender/widget_tracker_wrapper.dart';
import 'package:mevolve/analytics/events/base_event.dart';
import 'package:mevolve/analytics/parameter_groups/param_classes.dart';

class PPPublicProfileCtaClicksEvent extends AnalyticsEvent {
  const PPPublicProfileCtaClicksEvent({
    required this.trackAction,
  }) : super(eventId: 24, eventType: EventType.userAction);

  @override
  String get eventName => 'pp_public_profile_cta_clicks';

  final TrackAction trackAction;

  @override
  Map<String, dynamic> getEventParameters() {
    return {
      ...ParamActionValue(trackAction: trackAction).toMap(),
      ...ParamItemId(trackAction: trackAction).toMap(),
    };
  }

  // Now use toMapWithParams() instead of manually creating the map
  @override
  Map<String, dynamic> toMap() => toMapWithParams();
}
