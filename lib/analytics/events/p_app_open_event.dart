import 'package:mevolve/analytics/events/base_event.dart';
import 'package:mevolve/analytics/parameter_groups/param_classes.dart';

class PAppOpenEvent extends AnalyticsEvent {
  const PAppOpenEvent({
    this.firstOpen,
  }) : super(eventId: 3, eventType: EventType.userAction);

  final bool? firstOpen;

  @override
  String get eventName => 'p_app_open';

  @override
  bool get needBaseNullablesGroup => true;

  @override
  bool get needBaseParamNonNullablesGroup => true;

  @override
  Map<String, dynamic> getEventParameters() {
    return {
      ...ParamFirstOpen(firstOpen: firstOpen).toMap(),
    };
  }

  // Now use toMapWithParams() instead of manually creating the map
  @override
  Map<String, dynamic> toMap() => toMapWithParams();
}
