import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';
import 'package:mevolve/constants/app_config.dart';
import 'package:mevolve/data/providers/drift_database.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/logger/cloud_logger/cloud_logger.dart';
import 'package:mevolve/utilities/logger/file_logger.dart';
import 'package:mevolve/utilities/logger/log.dart';
import 'package:mevolve/utilities/logger/log_tags.dart';

class MeLogger {
  MeLogger._();

  static String sessionId = AppConfig.instance.sessionId;

  /// Global flag to enable cloud logging and Crashlytics on the build.
  /// Defaults to !kDebugMode and !kProfileMode (enabled in release)
  /// Set this to true when you need to test logging in debug or profile builds
  static bool enableCrashlyticsAndCloudLoggingOnBuild =
      !kDebugMode && !kProfileMode;

  /// Helper method to check if Crashlytics and cloud logging should be enabled.
  /// Combines platform checks, debug mode, profile mode, and global flag
  static bool get shouldEnableCrashlyticsAndCloudLogging =>
      enableCrashlyticsAndCloudLoggingOnBuild &&
      (MePlatform.isAndroid || MePlatform.isIOS || MePlatform.isMacOS);

  static FileLogger? _fileLogger;
  static CloudLogger? _cloudLogger;

  static void init() {
    if (!kDebugMode) Logger.level = Level.warning;
    Logger.level = Level.nothing;
    _fileLogger = FileLogger();
    if (shouldEnableCrashlyticsAndCloudLogging) {
      _cloudLogger = CloudLogger();
    }
  }

  static void initializeCloudLogger() {
    if (shouldEnableCrashlyticsAndCloudLogging && _cloudLogger != null) {
      _cloudLogger!.init();
    }
  }

  static Log getLogger(LogTags tag) {
    if (_fileLogger == null) {
      init();
    }
    return Log(
      cloudLogger: _cloudLogger,
      fileLogger: _fileLogger!,
      tag: tag,
    );
  }

  static Future<String?> getLogDumpFilePath() async {
    if (_fileLogger == null) {
      init();
    }
    return await _fileLogger!.getSingleLoggerFile();
  }

  static Future<void> resetLogs() async {
    if (_fileLogger == null) {
      init();
    }
    return await _fileLogger!.resetLogs();
  }

  static void setLoggerDb(DriftDatabaseRepository driftDatabaseRepository) {
    if (_fileLogger == null) {
      init();
    }
    _fileLogger!.setLoggerDb(driftDatabaseRepository);
  }
}
