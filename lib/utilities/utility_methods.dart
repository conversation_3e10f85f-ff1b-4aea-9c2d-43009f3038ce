import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;

import 'package:app_settings/app_settings.dart' as settings;
import 'package:audio_session/audio_session.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:collection/collection.dart';
import 'package:crypto/crypto.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_quill/flutter_quill.dart';
import 'package:get_time_ago/get_time_ago.dart';
import 'package:html2md/html2md.dart' as html2md;
import 'package:intl/intl.dart';
import 'package:jiffy/jiffy.dart';
import 'package:just_audio/just_audio.dart' as ja;
import 'package:loading_animation_widget/loading_animation_widget.dart';
import 'package:mevolve/constants/app_config.dart';
import 'package:mevolve/constants/app_constant.dart';
import 'package:mevolve/constants/extensions/extensions.dart';
import 'package:mevolve/constants/extensions/extensions_core.dart';
import 'package:mevolve/constants/size_constants.dart';
import 'package:mevolve/constants/sound_constants.dart';
import 'package:mevolve/data/enums/alarm_sound_type.dart';
import 'package:mevolve/data/enums/config_type.dart';
import 'package:mevolve/data/enums/environment_type.dart';
import 'package:mevolve/data/models/attachment_info.dart';
import 'package:mevolve/data/models/calendar_event_setup.dart';
import 'package:mevolve/data/models/custom/me_date_time.dart';
import 'package:mevolve/data/models/habit/habit_action.dart';
import 'package:mevolve/data/models/habit/habit_setup.dart';
import 'package:mevolve/data/models/journal_action.dart';
import 'package:mevolve/data/models/journal_setup.dart';
import 'package:mevolve/data/models/list_screens_models/habit_lists.dart';
import 'package:mevolve/data/models/list_screens_models/journal_lists.dart';
import 'package:mevolve/data/models/me_string.dart';
import 'package:mevolve/data/models/note.dart';
import 'package:mevolve/data/models/todo.dart';
import 'package:mevolve/data/models/user/removed_doc_info.dart';
import 'package:mevolve/data/providers/authentication/authentication_service.dart';
import 'package:mevolve/data/providers/firebase_functions.dart';
import 'package:mevolve/data/providers/local_storage.dart';
import 'package:mevolve/data/repositories/database_repository.dart';
import 'package:mevolve/data/repositories/storage_repository.dart';
import 'package:mevolve/features/app/bloc/app_bloc.dart';
import 'package:mevolve/features/app/view/app_routes.dart';
import 'package:mevolve/features/app/widgets/feedback/cubit/feedback_cubit.dart';
import 'package:mevolve/features/app/widgets/feedback/widgets/show_feedback_dialog.dart';
import 'package:mevolve/features/app/widgets/settings/cubit/today_settings_cubit.dart';
import 'package:mevolve/features/app/widgets/styles/me_text.dart';
import 'package:mevolve/features/audio/audio_handler.dart';
import 'package:mevolve/features/audio/audio_service.dart';
import 'package:mevolve/features/common/screen_size/screen_size_state.dart';
import 'package:mevolve/features/hamburger/offline_alert_dialog.dart';
import 'package:mevolve/features/widgets/me_dialog.dart';
import 'package:mevolve/features/widgets/quill_editor.dart';
import 'package:mevolve/firebase_options_dev.dart' as firebase_dev;
import 'package:mevolve/firebase_options_hotfix.dart' as firebase_hotfix;
import 'package:mevolve/firebase_options_prod.dart' as firebase_prod;
import 'package:mevolve/firebase_options_qa.dart' as firebase_qa;
import 'package:mevolve/firebase_options_staging.dart' as firebase_staging;
import 'package:mevolve/generated/metranslations.gen.dart';
import 'package:mevolve/main.dart';
import 'package:mevolve/themes/text_themes.dart';
import 'package:mevolve/utilities/connectivity/internet_connectivity_service.dart';
import 'package:mevolve/utilities/cross_platform/me_file_system.dart';
import 'package:mevolve/utilities/cross_platform/platform.dart';
import 'package:mevolve/utilities/encryption/me_encryption.dart';
import 'package:mevolve/utilities/logger/log.dart';
import 'package:mevolve/utilities/logger/log_tags.dart';
import 'package:mevolve/utilities/logger/me_logger.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:restart_app/restart_app.dart';
import 'package:rrule/rrule.dart';
import 'package:universal_html/html.dart' as html;
import 'package:url_launcher/url_launcher.dart';
import 'package:vibration/vibration.dart';
import 'package:vsc_quill_delta_to_html/vsc_quill_delta_to_html.dart';

typedef Habit = Map<HabitSetup, HabitAction>;
typedef Journal = Map<JournalSetup, JournalAction>;
typedef Habits = List<Habit>;
typedef Journals = List<Journal>;

ja.AudioPlayer iosPlayer = ja.AudioPlayer();

bool recoverAppFromCriticalStateInProg = false;

enum MeLoaderDialog {
  loggingInWait,
  checkingAvailablePurchase;
}

class UtilityMethods {
  static final Log _log = MeLogger.getLogger(LogTags.utilityMethods);
  static DateTime? clearDebugLogsTimestamp; // used in debug screen
  static String? documentDirectoryPath;

  static MeString getTimerPercentCompleted({
    required Duration totalDuration,
    required Duration elapsedDuration,
  }) {
    int percent = (calculatePercent(
              targetDuration: totalDuration,
              elapsedDuration: elapsedDuration,
            ) *
            100)
        .toInt();
    return MeString.fromVariable('$percent%');
  }

  // It is being used in Habit item and habit notification timer result string
  // Like: 1h 2m, 1h 2m / 2h 3m, 1h 2m / 3h
  static MeString getDurationString({
    // Supports DateTime for providing duration.
    DateTime? startDateTime,
    Duration? startDuration,
    Duration? endDuration,
    int repeatCount = 1,
    bool withSlash = false,
  }) {
    // assert that both cant be null with a meaningful error message
    assert(
      !(startDateTime == null && startDuration == null),
      'Both startDateTime and startDuration cannot be null',
    );

    // If string is like 0h 45m 59s then we want 45m 59s or if string is like 0h 0m 59s then we want 59s
    // or if string is like 0h 0m 0s then we want 0s or if string is like 4h 0m 59s then we want 4h 59s
    // or if string is like 0h 59m 0s then we want 59m or if string is like 4h 59m 0s then we want 4h 59m
    String simpleFormat(
      int hours,
      int minutes,
      int seconds,
      String hourString,
      String minuteString,
      String secondString,
    ) {
      if (hours == 0 && minutes == 0 && seconds == 0) {
        return secondString;
      }

      String hoursString = hours > 0 ? '$hourString ' : '';
      String minutesString = minutes > 0 ? '$minuteString ' : '';
      String secondsString = seconds > 0 ? secondString : '';
      return hoursString + minutesString + secondsString;
    }

    if (startDateTime != null) {
      startDuration = Duration(
        hours: startDateTime.hour,
        minutes: startDateTime.minute,
        seconds: startDateTime.second,
      );
    }

    int totalSeconds = startDuration!.inSeconds * repeatCount;
    startDuration = Duration(seconds: totalSeconds);

    int hours = startDuration.inHours;
    int minutes = startDuration.inMinutes % 60;
    int seconds = startDuration.inSeconds % 60;

    MeString elapsedString = MeTranslations.instance
        .overlay_habitSetupAddTimerSelect_previewForHourMinSec(
      hour: hours.toString(),
      minute: minutes.toString(),
      seconds: seconds.toString(),
    );

    List<String> elapsedStringSplit = elapsedString.text.split(' ');

    elapsedString = MeString(
      simpleFormat(
        hours,
        minutes,
        seconds,
        elapsedStringSplit[0],
        elapsedStringSplit[1],
        elapsedStringSplit[2],
      ),
    );

    if (endDuration == null || endDuration == Duration.zero) {
      return elapsedString;
    }

    totalSeconds = endDuration.inSeconds * repeatCount;
    endDuration = Duration(seconds: totalSeconds);

    hours = endDuration.inHours;
    minutes = endDuration.inMinutes % 60;
    seconds = endDuration.inSeconds % 60;

    MeString totalString = MeTranslations.instance
        .overlay_habitSetupAddTimerSelect_previewForHourMinSec(
      hour: hours.toString(),
      minute: minutes.toString(),
      seconds: seconds.toString(),
    );

    List<String> totalStringSplit = totalString.text.split(' ');

    totalString = MeString(
      simpleFormat(
        hours,
        minutes,
        seconds,
        totalStringSplit[0],
        totalStringSplit[1],
        totalStringSplit[2],
      ),
    );

    return MeString.fromVariable('${elapsedString.text} / ${totalString.text}');
  }

  static MeString getRemindLongString(
    BuildContext context,
    int? reminderTime,
    DateTime startTime, {
    bool useOperators = false,
    bool onlyTime = false,
  }) {
    startTime = DateTime(
      startTime.year,
      startTime.month,
      startTime.day,
      startTime.hour,
      startTime.minute,
    );
    if (reminderTime == null) {
      return MeTranslations.instance.screen_common_noReminder;
    } else {
      int diffInMinutes = reminderTime.abs();
      int hours = diffInMinutes ~/ 60;
      int minutes = diffInMinutes < 0
          ? (60 - (diffInMinutes % 60))
          : (diffInMinutes % 60);
      String timeString = '';
      if (hours | minutes == 0) {
        return MeTranslations.instance.dropdown_remindOption_onTime;
      } else if (hours == 0) {
        timeString = '${minutes}m';
      } else if (minutes == 0) {
        timeString = '${hours}h';
      } else {
        timeString = '${hours}h ${minutes}m';
      }
      return reminderTime > 0
          ? useOperators
              ? MeString.fromVariable('+$timeString')
              : onlyTime
                  ? MeString.fromVariable(timeString)
                  : MeTranslations.instance
                      .overlay_reminderSelect_previewAfterTime(time: timeString)
          : useOperators
              ? MeString.fromVariable('-$timeString')
              : onlyTime
                  ? MeString.fromVariable(timeString)
                  : MeTranslations.instance
                      .overlay_reminderSelect_previewBeforeTime(
                      time: timeString,
                    );
    }
  }

  static MeString getDurationWidgetString(
    BuildContext context,
    int? reminderTime,
  ) {
    if (reminderTime == null) {
      return MeTranslations.instance.screen_common_noReminder;
    } else {
      int diffInMinutes = reminderTime.abs();
      int hours = diffInMinutes ~/ 60;
      int minutes = diffInMinutes < 0
          ? (60 - (diffInMinutes % 60))
          : (diffInMinutes % 60);
      String timeString = '';
      if (hours | minutes == 0) {
        timeString = '0';
      } else if (hours == 0) {
        timeString = '${minutes}m';
      } else if (minutes == 0) {
        timeString = '${hours}h';
      } else {
        timeString = '${hours}h ${minutes}m';
      }
      return MeString.fromVariable(timeString);
    }
  }

  Future<void> openStoreListing() async {
    if (MePlatform.isAndroid || MePlatform.isIOS) {
      final appId =
          MePlatform.isAndroid ? AppConfig.instance.packageName : '6477729724';
      final url = Uri.parse(
        MePlatform.isAndroid
            ? 'market://details?id=$appId'
            : 'https://apps.apple.com/app/id$appId',
      );
      if (await canLaunchUrl(url)) {
        launchUrl(
          url,
          mode: LaunchMode.externalApplication,
        );
      }
    }
  }

  /// Compares this DateTime object to other, returning zero if the values are equal.
  static int compareNullableDateTime(DateTime? date1, DateTime? date2) {
    if (date1 == null && date2 == null) {
      return 0;
    } else if (date1 == null) {
      return -1;
    } else if (date2 == null) {
      return 1;
    } else {
      return date1.compareTo(date2);
    }
  }

  bool isRemindersDifferent(
    List<int> remList1,
    List<int> remList2,
  ) {
    return !listEquals(remList1, remList2);
  }

  /// Returns the list of months in short form
  static List<String> getMonthList() {
    return [
      'jan',
      'feb',
      'mar',
      'apr',
      'may',
      'jun',
      'jul',
      'aug',
      'sep',
      'oct',
      'nov',
      'dec',
    ];
  }

  static List<int> getMonthListV2() {
    return [for (var i = 1; i <= 12; i += 1) i];
  }

  /// Converts the '1' to 'mon' and '2' to 'tue' and so on.
  static MeString getDayFromInt(int weekDay) {
    switch (weekDay) {
      case 1:
        return MeTranslations.instance.screen_common_dayMonday;
      case 2:
        return MeTranslations.instance.screen_common_dayTuesday;
      case 3:
        return MeTranslations.instance.screen_common_dayWednesday;
      case 4:
        return MeTranslations.instance.screen_common_dayThursday;
      case 5:
        return MeTranslations.instance.screen_common_dayFriday;
      case 6:
        return MeTranslations.instance.screen_common_daySaturday;
      case 7:
        return MeTranslations.instance.screen_common_daySunday;
      default:
        return MeTranslations.instance.screen_common_dayMonday;
    }
  }

  /// Converts the '1' to 'jan' and '2' to 'feb' and so on.
  static String getMonthFromInt(int month) {
    switch (month) {
      case 1:
        return 'jan';
      case 2:
        return 'feb';
      case 3:
        return 'mar';
      case 4:
        return 'apr';
      case 5:
        return 'may';
      case 6:
        return 'jun';
      case 7:
        return 'jul';
      case 8:
        return 'aug';
      case 9:
        return 'sep';
      case 10:
        return 'oct';
      case 11:
        return 'nov';
      case 12:
        return 'dec';
      default:
        return '';
    }
  }

  ///Converts the 'jan' to '1' and 'feb' to '2' and so on.
  static int getMonthFromStr(String month) {
    switch (month.toLowerCase()) {
      case 'jan':
        return 1;
      case 'feb':
        return 2;
      case 'mar':
        return 3;
      case 'apr':
        return 4;
      case 'may':
        return 5;
      case 'jun':
        return 6;
      case 'jul':
        return 7;
      case 'aug':
        return 8;
      case 'sep':
        return 9;
      case 'oct':
        return 10;
      case 'nov':
        return 11;
      case 'dec':
        return 12;
      default:
        return 0;
    }
  }

  static int getWeekDay(String repeatStr) {
    switch (repeatStr) {
      case 'weekly:mon':
        return 1;
      case 'weekly:tue':
        return 2;
      case 'weekly:wed':
        return 3;
      case 'weekly:thu':
        return 4;
      case 'weekly:fri':
        return 5;
      case 'weekly:sat':
        return 6;
      case 'weekly:sun':
        return 7;
    }
    return 1;
  }

  static int getWeekDayV2(String repeatStr) {
    final day = repeatStr.split(repeatStr)[1].substring(0, 2).toUpperCase();
    switch (day) {
      case 'MO':
        return 1;
      case 'TU':
        return 2;
      case 'WE':
        return 3;
      case 'TH':
        return 4;
      case 'FR':
        return 5;
      case 'SA':
        return 6;
      case 'SU':
        return 7;
    }
    return 1;
  }

  int differenceInMonths(DateTime start, DateTime end) {
    // Calculate the difference in days
    int differenceInDays = end.difference(start).inDays;
    // Convert days to months (assuming 30 days per month for simplicity)
    int differenceInMonths = (differenceInDays / 30).round();
    return differenceInMonths;
  }

  static void patternVibrate() {
    if (MePlatform.isAndroid || MePlatform.isIOS) {
      Vibration.vibrate(
        pattern: [500, 1000],
        repeat: 0,
      );
    }
  }

  static Future<void> playSoundAndVibration({
    required AlarmSoundType alarmSoundType,

    /// VOLUME BETWEEN 0 AND 100.
    required double volume,
    Duration soundDuration = SoundConstants.timerHabitSoundDuration,
    bool playVibration = true,
    AudioFor audioFor = AudioFor.other,
  }) async {
    try {
      await stopAlarmSoundAndVibration();
      if (MePlatform.isIOS) {
        final session = await AudioSession.instance;
        await session.setActive(false);
        // Configure the audio session
        await session.configure(
          const AudioSessionConfiguration(
            avAudioSessionCategory: AVAudioSessionCategory.playback,
            avAudioSessionCategoryOptions:
                AVAudioSessionCategoryOptions.mixWithOthers,
            avAudioSessionMode: AVAudioSessionMode.defaultMode,
          ),
        );
        // Then activate the session
        await session.setActive(true);
        iosPlayer.setVolume(volume / 100);
        iosPlayer.setLoopMode(ja.LoopMode.one);
        if (alarmSoundType == AlarmSoundType.none) {
          iosPlayer.stop();
          return;
        }
        await iosPlayer.setAsset(
          alarmSoundType.assetPath,
        );
        iosPlayer.play();
      } else {
        AudioService audio = AudioService();
        AudioPlayerHandler audioHandler =
            audio.audioHandler(alarmSoundType.assetPath);
        if (alarmSoundType == AlarmSoundType.none) {
          audioHandler.stop();
          return;
        }
        await audioHandler.setVolume(volume / 100);
        audioHandler.loop(soundDuration);
        await audioHandler.setAsset();
        audioHandler.play(audioFor);
      }
    } catch (e) {
      _log.e('Error playing sound: $e');
    }
    if (playVibration) {
      try {
        patternVibrate();

        // Stop vibration after sound duration.
        Future.delayed(soundDuration, () {
          stopAlarmSoundAndVibration();
        });
      } catch (e) {
        _log.e('Error playing vibration: $e');
      }
    }
  }

  static Future<void> stopAlarmSoundAndVibration() async {
    AudioService audio = AudioService();
    if (MePlatform.isIOS) {
      iosPlayer.stop();
    } else {
      audio.stopAllPlayers();
    }

    if (MePlatform.isAndroid || MePlatform.isIOS) {
      Vibration.cancel();
    }
  }

  static void showOfflineDialogOrPerformAction({
    required VoidCallback internetActiveFunction,
    required InternetConnectionState internetConnectionState,
    required BuildContext context,
  }) async {
    if (internetConnectionState == InternetConnectionState.disconnected) {
      await offlineAlertDialog(context: context, hideDescription: false);
    } else {
      internetActiveFunction.call();
    }
  }

  static Future<bool?> showCompletePublicProfileDialog({
    required BuildContext context,
  }) async {
    return await showDialog(
      context: context,
      builder: (context) => MeDialog(
        title: MeTranslations.instance.overlay_completePublicProfile_title,
        description:
            MeTranslations.instance.overlay_completePublicProfile_content,
        primaryText: MeTranslations.instance.overlay_completePublicProfile_add,
        primaryOnTap: () => Navigator.pop(context, true),
        secondaryText: MeTranslations.instance.screen_common_buttonCancel,
        secondaryOnTap: () => Navigator.pop(context, false),
      ),
    );
  }

  static Future<bool> showAccessDeniedPopup({
    required BuildContext context,
  }) async {
    bool? res = await showDialog(
      context: context,
      builder: (context) => MeDialog(
        title: MeTranslations.instance.overlay_accessDenied_title,
        description: MeTranslations.instance.overlay_accessDenied_content,
        primaryOnTap: () => Navigator.pop(context, true),
        primaryText: MeTranslations.instance.screen_common_ok,
      ),
    );
    return res ?? false;
  }

  static Future<void> showMeLoaderDialog(
    BuildContext context, [
    MeLoaderDialog? loaderDialog,
  ]) async {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;
    return await showDialog(
      context: context,
      useRootNavigator: false,
      barrierDismissible: false,
      barrierColor: colorScheme.color13.withValues(
        alpha: ScreenSizeState.instance.loaderModalBarrierOpacity,
      ),
      builder: (context) {
        return MeLoader(loaderDialog: loaderDialog);
      },
    );
  }

  static Future<bool> isNotificationAllowed() async {
    if (MePlatform.isWeb) {
      html.PermissionStatus? permissionStatus = await html
          .window.navigator.permissions
          ?.query({'name': 'push', 'userVisibleOnly': true});
      return permissionStatus?.state == 'granted';
    } else {
      PermissionStatus notificationPermission =
          await Permission.notification.status;
      if (MePlatform.isIOS || MePlatform.isAndroid || MePlatform.isMacOS) {
        return notificationPermission == PermissionStatus.granted;
      } else {
        return false;
      }
    }
  }

  static Future<bool> isCameraPermissionAllowed() async {
    PermissionStatus permission = await Permission.camera.status;
    if (MePlatform.isIOS || MePlatform.isAndroid || MePlatform.isMacOS) {
      return permission == PermissionStatus.granted;
    } else {
      return false;
    }
  }

  static Future<bool> isCameraPermanentlyDenied() async {
    PermissionStatus permission = await Permission.camera.status;
    if (MePlatform.isIOS || MePlatform.isAndroid) {
      return permission == PermissionStatus.permanentlyDenied;
    } else {
      return false;
    }
  }

  static Future<bool> isMicPermanentlyDenied() async {
    PermissionStatus permission = await Permission.microphone.status;
    if (MePlatform.isIOS || MePlatform.isAndroid) {
      return permission == PermissionStatus.permanentlyDenied;
    } else {
      return false;
    }
  }

  static Future<bool> isExactAlarmAllowed() async {
    if (MePlatform.isWeb) {
      return true;
    } else {
      PermissionStatus exactAlarmPermission =
          await Permission.scheduleExactAlarm.status;
      if (MePlatform.isIOS) {
        return true;
      } else if (MePlatform.isAndroid) {
        var androidInfo = await DeviceInfoPlugin().androidInfo;
        var androidVersion = androidInfo.version.sdkInt;
        if (androidVersion >= 33) {
          return exactAlarmPermission == PermissionStatus.granted;
        } else {
          return true;
        }
      } else {
        return false;
      }
    }
  }

  static Future<bool> isMicrophoneAllowed() async {
    if (MePlatform.isWeb) {
      html.PermissionStatus? permissionStatus = await html
          .window.navigator.permissions
          ?.query({'name': 'microphone'});
      return permissionStatus?.state == 'granted';
    } else {
      PermissionStatus permissionStatus = await Permission.microphone.status;
      return permissionStatus == PermissionStatus.granted;
    }
  }

  static Future<bool> isVoiceRecognitionAllowed() async {
    PermissionStatus permissionStatus = await Permission.speech.status;
    return permissionStatus == PermissionStatus.granted;
  }

  static Future<bool> shouldShowMicrophoneRequestRationale() async {
    const permission = Permission.microphone;

    return await permission.shouldShowRequestRationale;
  }

  static Future<void> askMicrophonePermission() async {
    bool deniedPermanently = false;
    if (MePlatform.isWeb) {
      bool? res = await showDialog(
        context: authenticatedGlobalContext!,
        barrierDismissible: true,
        builder: (BuildContext ctx) {
          return MeDialog(
            insetPadding: ScreenSizeState.instance
                .getDialogInsetPadding(DialogLevel.level1),
            title:
                MeTranslations.instance.overlay_micPermissionBeforeDeny_title,
            description:
                MeTranslations.instance.overlay_micPermissionBeforeDeny_content,
            primaryText:
                MeTranslations.instance.screen_common_permissionContinue,
            primaryOnTap: () {
              Navigator.pop(ctx, true);
            },
          );
        },
      );
      if (res == true) {
        await html.window.navigator.getUserMedia(audio: true);
      }
    } else {
      // Did the user denied permission before?
      // bool userDeniedPermissionBefore = await shouldShowMicrophoneRequestRationale();

      const permission = Permission.microphone;

      deniedPermanently = await permission.isPermanentlyDenied;

      if (!deniedPermanently) {
        bool? res = await showDialog(
          context: authenticatedGlobalContext!,
          barrierDismissible: true,
          builder: (BuildContext ctx) {
            return MeDialog(
              insetPadding: ScreenSizeState.instance
                  .getDialogInsetPadding(DialogLevel.level1),
              title:
                  MeTranslations.instance.overlay_micPermissionBeforeDeny_title,
              description: MeTranslations
                  .instance.overlay_micPermissionBeforeDeny_content,
              primaryText:
                  MeTranslations.instance.screen_common_permissionContinue,
              primaryOnTap: () async {
                Navigator.pop(ctx, true);
              },
            );
          },
        );
        if (res == true) {
          await permission.request();
        }
      } else {
        // Permission is permanently denied
        bool? res = await showDialog(
          context: authenticatedGlobalContext!,
          barrierDismissible: true,
          builder: (BuildContext ctx) {
            return MeDialog(
              insetPadding: ScreenSizeState.instance
                  .getDialogInsetPadding(DialogLevel.level1),
              blockBackButton: false,
              title:
                  MeTranslations.instance.overlay_micPermissionAfterDeny_title,
              description: MeTranslations
                  .instance.overlay_micPermissionAfterDeny_content,
              primaryText: MeTranslations
                  .instance.overlay_micPermissionAfterDeny_goToSettings,
              primaryOnTap: () async {
                Navigator.pop(ctx, true);
              },
            );
          },
        );
        if (res == true) {
          await openAppSettings();
        }
      }
    }
  }

  static Future<bool> isAttachmentAvailableOffline({
    required MeAttachmentInfo attachment,
    required BuildContext context,
    bool allowImageCheck = false,
  }) async {
    bool isNotImage = attachment.fileType != FileType.image && !allowImageCheck;

    String? originalFileName = attachment.originalFileName;
    Directory tempDir = await getTemporaryDirectory();
    String basePath = '${tempDir.path}/download_';
    final File decryptedFile = originalFileName != null
        ? File(
            '$basePath${originalFileName.replaceAll(" ", "_")}',
          )
        : File(
            '$basePath${attachment.fileType.name}_${attachment.id}.${attachment.format}',
          );
    bool exists = await decryptedFile.exists();
    bool isOffline = !(await InternetConnectivityService.instance.hasInternet);

    if (isOffline && isNotImage && !exists && context.mounted) {
      await offlineAlertDialog(
        context: context,
      );
      return false;
    }
    return true;
  }

  static Future<void> askVoiceRecognitionPermission() async {
    bool deniedPermanently = false;
    // Did the user denied permission before?
    // bool userDeniedPermissionBefore = await shouldShowMicrophoneRequestRationale();

    const permission = Permission.speech;

    deniedPermanently = await permission.isPermanentlyDenied;

    if (!deniedPermanently) {
      bool? res = await showDialog(
        context: authenticatedGlobalContext!,
        barrierDismissible: true,
        builder: (BuildContext ctx) {
          return MeDialog(
            insetPadding: ScreenSizeState.instance
                .getDialogInsetPadding(DialogLevel.level1),
            title: MeTranslations
                .instance.overlay_speechRecognitionBeforeDeny_title,
            description: MeTranslations
                .instance.overlay_speechRecognitionBeforeDeny_content,
            primaryText:
                MeTranslations.instance.screen_common_permissionContinue,
            primaryOnTap: () async {
              Navigator.pop(ctx, true);
            },
          );
        },
      );
      if (res == true) {
        await permission.request();
      }
    } else {
      // Permission is permanently denied
      bool? res = await showDialog(
        context: authenticatedGlobalContext!,
        barrierDismissible: true,
        builder: (BuildContext ctx) {
          return MeDialog(
            insetPadding: ScreenSizeState.instance
                .getDialogInsetPadding(DialogLevel.level1),
            blockBackButton: false,
            title: MeTranslations
                .instance.overlay_speechRecognitionAfterDeny_title,
            description: MeTranslations
                .instance.overlay_speechRecognitionAfterDeny_content,
            primaryText: MeTranslations
                .instance.overlay_speechRecognitionAfterDeny_goToSettings,
            primaryOnTap: () async {
              Navigator.pop(ctx, true);
            },
          );
        },
      );
      if (res == true) {
        await openAppSettings();
      }
    }
  }

  String getLastUpdatedByString({
    required String taskUid,
    required String myUid,
    String? lastUpdatedBy,
    required String myName,
    required String ownerName,
    required Duration lastUpdatedDuration,
  }) {
    // Determine who updated the task
    final isUpdatedByMe = lastUpdatedBy == null
        ? taskUid == myUid
        : LastUpdatedByUtils.getDisplayName(
              lastUpdatedBy: lastUpdatedBy,
              currentUserEmail: getCurrentUserEmail(),
              ownerName: ownerName,
            ).text ==
            MeTranslations.instance.bottomSheet_listItems_updateByYou.text;

    final updaterName = isUpdatedByMe
        ? 'You'
        : (lastUpdatedBy == null
            ? ownerName
            : LastUpdatedByUtils.getDisplayName(
                lastUpdatedBy: lastUpdatedBy,
                currentUserEmail: getCurrentUserEmail(),
                ownerName: ownerName,
              ).text);

    // Calculate time components
    final days = lastUpdatedDuration.inDays;
    final hours = lastUpdatedDuration.inHours;
    final minutes = lastUpdatedDuration.inMinutes;
    // final seconds =
    //     lastUpdatedDuration.inSeconds <= 0 ? 1 : lastUpdatedDuration.inSeconds;

    // Format the time difference message
    final translations = MeTranslations.instance;

    if (days > 29) {
      final date =
          DateTime.now().subtract(lastUpdatedDuration).formattedDate().text;
      return isUpdatedByMe
          ? translations.screen_common_lastUpdatedByOwnerInDate(date: date).text
          : translations
              .screen_common_lastUpdatedByMemberInDate(
                name: updaterName,
                date: date,
              )
              .text;
    }

    if (days > 0) {
      final dayCount = days.toString();
      return isUpdatedByMe
          ? translations
              .screen_common_lastUpdatedByOwnerInDays(dayCount: dayCount)
              .text
          : translations
              .screen_common_lastUpdatedByMemberInDays(
                name: updaterName,
                dayCount: dayCount,
              )
              .text;
    }

    if (hours > 0) {
      final hourCount = hours.toString();
      return isUpdatedByMe
          ? translations
              .screen_common_lastUpdatedByOwnerInHours(hourCount: hourCount)
              .text
          : translations
              .screen_common_lastUpdatedByMemberInHours(
                name: updaterName,
                hourCount: hourCount,
              )
              .text;
    }

    if (minutes > 0) {
      final minuteCount = minutes.toString();
      return isUpdatedByMe
          ? translations
              .screen_common_lastUpdatedByOwnerInMinutes(
                minuteCount: minuteCount,
              )
              .text
          : translations
              .screen_common_lastUpdatedByMemberInMinutes(
                name: updaterName,
                minuteCount: minuteCount,
              )
              .text;
    }

    // Default to seconds
    // final secondCount = seconds.toString();
    return isUpdatedByMe
        ? translations.screen_common_lastUpdatedByOwnerInSeconds.text
        : translations
            .screen_common_lastUpdatedByMemberInSeconds(
              name: updaterName,
            )
            .text;
  }

  static Future<void> askCameraPermission() async {
    bool deniedPermanently = false;
    const permission = Permission.camera;
    deniedPermanently = await permission.isPermanentlyDenied;

    if (!deniedPermanently) {
      bool? res = await showDialog(
        context: authenticatedGlobalContext!,
        barrierDismissible: true,
        builder: (BuildContext ctx) {
          return MeDialog(
            insetPadding: ScreenSizeState.instance
                .getDialogInsetPadding(DialogLevel.level1),
            title: MeTranslations
                .instance.overlay_cameraPermissionBeforeDeny_title,
            description: MeTranslations
                .instance.overlay_cameraPermissionBeforeDeny_content,
            primaryText:
                MeTranslations.instance.screen_common_permissionContinue,
            primaryOnTap: () async {
              Navigator.pop(ctx, true);
            },
          );
        },
      );
      if (res == true) {
        await permission.request();
      }
    } else {
      // Permission is permanently denied
      bool? res = await showDialog(
        context: authenticatedGlobalContext!,
        barrierDismissible: true,
        builder: (BuildContext ctx) {
          return MeDialog(
            insetPadding: ScreenSizeState.instance
                .getDialogInsetPadding(DialogLevel.level1),
            blockBackButton: false,
            title:
                MeTranslations.instance.overlay_cameraPermissionAfterDeny_title,
            description: MeTranslations
                .instance.overlay_cameraPermissionAfterDeny_content,
            primaryText: MeTranslations
                .instance.overlay_cameraPermissionAfterDeny_goToSettings,
            primaryOnTap: () async {
              openAppSettings();
            },
          );
        },
      );
      if (res == true) {
        await openAppSettings();
      }
    }
  }

  static FirebaseOptions getFirebaseOptions() {
    switch (AppConfig.instance.environmentType) {
      case EnvironmentType.dev:
        return firebase_dev.DefaultFirebaseOptions.currentPlatform;
      case EnvironmentType.qa:
        return firebase_qa.DefaultFirebaseOptions.currentPlatform;
      case EnvironmentType.staging:
        return firebase_staging.DefaultFirebaseOptions.currentPlatform;
      case EnvironmentType.prod:
        return firebase_prod.DefaultFirebaseOptions.currentPlatform;
      case EnvironmentType.hotfix:
        return firebase_hotfix.DefaultFirebaseOptions.currentPlatform;
    }
  }

  static Future<void> showAppNotifSettingDialog({
    required BuildContext navContext,
    bool noDialogs = false,
  }) async {
    bool? res = noDialogs
        ? true
        : await showDialog(
            context: navContext,
            barrierDismissible: true,
            builder: (BuildContext ctx) {
              return MeDialog(
                insetPadding: ScreenSizeState.instance
                    .getDialogInsetPadding(DialogLevel.level1),
                title: MeTranslations
                    .instance.overlay_notificationPermissionAfterDeny_title,
                description: MeTranslations
                    .instance.overlay_notificationPermissionAfterDeny_content,
                primaryText: MeTranslations
                    .instance.overlay_notificationPermissionAfterDeny_settings,
                primaryOnTap: () async {
                  Navigator.pop(ctx, true);
                },
              );
            },
          );
    if (res == true) {
      await settings.AppSettings.openAppSettings(
        type: settings.AppSettingsType.notification,
      );
    }
  }

  static Future<bool> isNotificationPermPermanentlyDenied([
    AndroidDeviceInfo? androidInfo,
  ]) async {
    const notificationPermission = Permission.notification;

    bool deniedPermanently = MePlatform.isWeb
        ? false
        : await notificationPermission.isPermanentlyDenied;

    if (MePlatform.isAndroid) {
      var andInfo = androidInfo ?? await DeviceInfoPlugin().androidInfo;
      var androidVersion = andInfo.version.sdkInt;
      deniedPermanently = deniedPermanently || androidVersion < 33;
    } else {
      deniedPermanently = deniedPermanently || MePlatform.isIOS || MePlatform.isMacOS;
    }

    return deniedPermanently;
  }

  static Future<void> askNotificationPermission({
    bool askExactAlarmAlsoTogether = true,
    bool noDialogs = false,
  }) async {
    if (MePlatform.isWeb) {
      bool? res = noDialogs
          ? true
          : await showDialog(
              barrierDismissible: true,
              context: authenticatedGlobalContext!,
              builder: (BuildContext ctx) {
                return MeDialog(
                  insetPadding: ScreenSizeState.instance
                      .getDialogInsetPadding(DialogLevel.level1),
                  title: MeTranslations
                      .instance.overlay_notificationPermissionBeforeDeny_title,
                  description: MeTranslations.instance
                      .overlay_notificationPermissionBeforeDeny_content,
                  primaryText:
                      MeTranslations.instance.screen_common_permissionContinue,
                  primaryOnTap: () {
                    Navigator.pop(ctx, true);
                  },
                );
              },
            );
      if (res == true) {
        await html.window.navigator.permissions
            ?.query({'name': 'push', 'userVisibleOnly': true});
      }
    } else {
      BuildContext navContext =
          authenticatedGlobalContext ?? rootNavKey.currentContext!;

      const notificationPermission = Permission.notification;
      const exactAlarmPermission = Permission.scheduleExactAlarm;

      if (MePlatform.isAndroid) {
        var androidInfo = await DeviceInfoPlugin().androidInfo;
        var androidVersion = androidInfo.version.sdkInt;
        bool deniedPermanently =
            await isNotificationPermPermanentlyDenied(androidInfo);
        if (!deniedPermanently && navContext.mounted) {
          bool? res = noDialogs
              ? true
              : await showDialog(
                  context: navContext,
                  barrierDismissible: true,
                  builder: (BuildContext ctx) {
                    return MeDialog(
                      insetPadding: ScreenSizeState.instance
                          .getDialogInsetPadding(DialogLevel.level1),
                      title: MeTranslations.instance
                          .overlay_notificationPermissionBeforeDeny_title,
                      description: MeTranslations.instance
                          .overlay_notificationPermissionBeforeDeny_content,
                      primaryText: MeTranslations
                          .instance.screen_common_permissionContinue,
                      primaryOnTap: () async {
                        Navigator.pop(ctx, true);
                      },
                    );
                  },
                );
          if (res == true) {
            final result = await notificationPermission.request();
            if (askExactAlarmAlsoTogether &&
                MePlatform.isAndroid &&
                result.isGranted) {
              if (androidVersion >= 33) {
                await exactAlarmPermission.request();
              }
            }
          }
        } else {
          // Permission is permanently denied
          if (navContext.mounted) {
            await showAppNotifSettingDialog(
              navContext: navContext,
              noDialogs: noDialogs,
            );
          }
        }
      }
      if (MePlatform.isIOS || MePlatform.isMacOS) {
        if (navContext.mounted) {
          await showAppNotifSettingDialog(
            navContext: navContext,
            noDialogs: noDialogs,
          );
        }
      }
    }
  }

  // Ask permission for notification, update the permission requirement status and configure the notifications.
  static Future<bool> manageAppNotificationPermission({
    bool noDialogs = false,
  }) async {
    final Log log = MeLogger.getLogger(LogTags.notification);

    final bool previousPermissionValue =
        await UtilityMethods.isNotificationAllowed();

    if (previousPermissionValue) {
      return true;
    }

    bool askPermission;
    BuildContext? context = authenticatedGlobalCurrentState?.context;

    askPermission = (MePlatform.isIOS || MePlatform.isAndroid || MePlatform.isMacOS) && true;

    log.i(
      'Asking for Permission: $askPermission',
    );

    if (askPermission && context != null && context.mounted) {
      bool isNotificationAllowed = await UtilityMethods.isNotificationAllowed();
      if (!isNotificationAllowed) {
        await UtilityMethods.askNotificationPermission(noDialogs: noDialogs);
        isNotificationAllowed = await UtilityMethods.isNotificationAllowed();
        if (isNotificationAllowed) {
          bool isExactAlarmAllowed = await UtilityMethods.isExactAlarmAllowed();
          if (!isExactAlarmAllowed) {
            await UtilityMethods.askExactAlarmPermission();
          }
        }
      }
    }
    bool endStatus = await UtilityMethods.isNotificationAllowed();
    // Give 1 second to let the periodic timers in NotificationPermCubit update
    // themselves with the new permission status if needed.
    await Future.delayed(const Duration(seconds: 1));
    return endStatus;
  }

  // Ask permission for notification, update the permission requirement status and configure the notifications.
  static Future<bool> manageCameraPermission() async {
    final Log log = MeLogger.getLogger(LogTags.camera);

    final bool previousCameraPermissionValue =
        await UtilityMethods.isCameraPermissionAllowed();

    final bool previousMicPermissionValue =
        await UtilityMethods.isMicrophoneAllowed();

    if (previousCameraPermissionValue && previousMicPermissionValue) {
      return true;
    }

    bool askPermission = (MePlatform.isIOS || MePlatform.isAndroid || MePlatform.isMacOS);
    BuildContext? context = authenticatedGlobalCurrentState?.context;

    log.i('Asking for Camera and Microphone Permission');

    if (askPermission && context != null && context.mounted) {
      bool isCameraAllowed = await UtilityMethods.isCameraPermissionAllowed();
      bool isMicAllowed = await UtilityMethods.isMicrophoneAllowed();

      bool isCameraPermanentlyDenied =
          await UtilityMethods.isCameraPermanentlyDenied();
      bool isMicPermanentlyDenied =
          await UtilityMethods.isMicPermanentlyDenied();

      if (!isCameraAllowed || !isMicAllowed) {
        // Ask for camera permission
        if (!isCameraAllowed && !isCameraPermanentlyDenied) {
          await UtilityMethods.askCameraPermission();
          isCameraAllowed = await UtilityMethods.isCameraPermissionAllowed();
        }

        // Ask for microphone permission
        if (!isMicAllowed && !isMicPermanentlyDenied) {
          await UtilityMethods.askMicrophonePermission();
          isMicAllowed = await UtilityMethods.isMicrophoneAllowed();
        }

        //check if permamently denied
        isCameraPermanentlyDenied =
            await UtilityMethods.isCameraPermanentlyDenied();
        isMicPermanentlyDenied = await UtilityMethods.isMicPermanentlyDenied();
        // If either permission is still not granted, show a dialog explaining why both are needed
        if (isCameraPermanentlyDenied || isMicPermanentlyDenied) {
          if (context.mounted) {
            bool isMicPermissionAllowed =
                await UtilityMethods.isMicrophoneAllowed();
            bool? res = await showDialog(
              context: authenticatedGlobalContext!,
              barrierDismissible: true,
              builder: (BuildContext ctx) {
                return MeDialog(
                  insetPadding: ScreenSizeState.instance
                      .getDialogInsetPadding(DialogLevel.level1),
                  blockBackButton: false,
                  title: isMicPermissionAllowed
                      ? MeTranslations
                          .instance.overlay_cameraPermissionAfterDeny_title
                      : MeTranslations
                          .instance.overlay_cameraAndMicAccessRequired_title,
                  description: isMicPermissionAllowed
                      ? MeTranslations
                          .instance.overlay_cameraPermissionAfterDeny_content
                      : MeTranslations
                          .instance.overlay_cameraAndMicAccessRequired_content,
                  primaryText: isMicPermissionAllowed
                      ? MeTranslations.instance
                          .overlay_cameraPermissionAfterDeny_goToSettings
                      : MeTranslations.instance
                          .overlay_cameraAndMicAccessRequired_goToSettings,
                  primaryOnTap: () async {
                    openAppSettings();
                  },
                );
              },
            );
            if (res == true) {
              await openAppSettings();
            }
          }
        }
      }
    }

    final bool currentCameraPermissionValue =
        await UtilityMethods.isCameraPermissionAllowed();
    final bool currentMicPermissionValue =
        await UtilityMethods.isMicrophoneAllowed();
    bool endStatus = currentCameraPermissionValue && currentMicPermissionValue;
    return endStatus;
  }

  static Future<void> askExactAlarmPermission() async {
    bool deniedPermanently = false;
    if (MePlatform.isWeb) {
      bool? res = await showDialog(
        context: authenticatedGlobalContext!,
        barrierDismissible: true,
        builder: (BuildContext ctx) {
          return MeDialog(
            title: MeTranslations
                .instance.overlay_notificationPermissionBeforeDeny_title,
            description: MeTranslations
                .instance.overlay_notificationPermissionBeforeDeny_content,
            primaryText:
                MeTranslations.instance.screen_common_permissionContinue,
            primaryOnTap: () {
              Navigator.pop(ctx, true);
            },
          );
        },
      );
      if (res == true) {
        await html.window.navigator.permissions
            ?.query({'name': 'push', 'userVisibleOnly': true});
      }
    } else {
      // // Did the user denied permission before?
      // bool userDeniedPermissionBefore = await shouldShowNotificationRequestRationale();

      BuildContext navContext =
          authenticatedGlobalContext ?? rootNavKey.currentContext!;

      const exactAlarmPermission = Permission.scheduleExactAlarm;

      deniedPermanently = await exactAlarmPermission.isPermanentlyDenied;

      if (navContext.mounted) {
        if (!deniedPermanently) {
          bool? res = await showDialog(
            context: navContext,
            barrierDismissible: true,
            builder: (BuildContext ctx) {
              return MeDialog(
                title: MeTranslations
                    .instance.overlay_notificationPermissionBeforeDeny_title,
                description: MeTranslations
                    .instance.overlay_notificationPermissionBeforeDeny_content,
                primaryText:
                    MeTranslations.instance.screen_common_permissionContinue,
                primaryOnTap: () async {
                  Navigator.pop(ctx, true);
                },
              );
            },
          );
          if (res == true) {
            if (MePlatform.isAndroid) {
              var androidInfo = await DeviceInfoPlugin().androidInfo;
              var androidVersion = androidInfo.version.sdkInt;
              if (androidVersion >= 33) {
                exactAlarmPermission.request();
              }
            }
          }
        } else {
          // Permission is permanently denied
          bool? res = await showDialog(
            context: navContext,
            barrierDismissible: true,
            builder: (BuildContext ctx) {
              return MeDialog(
                title: MeTranslations
                    .instance.overlay_notificationPermissionAfterDeny_title,
                description: MeTranslations
                    .instance.overlay_notificationPermissionAfterDeny_content,
                primaryText: MeTranslations
                    .instance.overlay_notificationPermissionAfterDeny_settings,
                primaryOnTap: () async {
                  Navigator.pop(ctx, true);
                },
              );
            },
          );
          if (res == true) {
            await settings.AppSettings.openAppSettings(
              type: settings.AppSettingsType.alarm,
            );
          }
        }
      }
    }
  }

  static T measureExecutionTimeSync<T>(T Function() function) {
    Stopwatch stopwatch = Stopwatch();
    stopwatch.start();

    T value = function();

    stopwatch.stop();
    int milliseconds = stopwatch.elapsedMilliseconds;
    double seconds = milliseconds / 1000.0;

    String timeTaken = milliseconds < 1000
        ? '$milliseconds ms'
        : '${seconds.toStringAsFixed(2)} seconds ($milliseconds ms)';

    _log.d('${function.runtimeType} executed in $timeTaken');

    return value;
  }

  static bool hasSwitchedBetweenNullAndNonNull(
    dynamic oldState,
    dynamic newState,
  ) {
    return (oldState == null && newState != null) ||
        (oldState != null && newState == null);
  }

  static double calculatePercent({
    required Duration targetDuration,
    required Duration elapsedDuration,
  }) {
    final total = targetDuration.inMilliseconds;
    final elapsed = elapsedDuration.inMilliseconds;
    double percent = total == 0 ? 1 : (elapsed / total);
    return percent > 1 ? 1 : percent;
  }

  static int getCharactersCount(String text) {
    return text.characters.length;
  }

  static (bool, double) showMaxLimitLeft(int charactersTyped, int maxLength) {
    if (maxLength == 0) return (false, 0.0);

    // Ensure charLeft is never negative
    int charLeft = math.max(0, maxLength - charactersTyped);

    // For large maxLength (≥ 20000):
    // - The overlay appears when less than 1000 characters left.
    // - Here progress calculation threshold is 1000 and overlay visibility threshold is 999.
    // - Progress starts from ((1000 - 999) / 1000) at charLeft = 999 and reaches 1.0 when charLeft is 0.
    if (maxLength >= 20000 && charLeft < 1000) {
      double progress = (1000 - charLeft) / 1000.0;
      return (true, progress);
    }

    // For moderate maxLength (> 20):
    // - The overlay appears when fewer than or equal to 10 characters left.
    // - Here progress calculation threshold is 10 and overlay visibility threshold is also 10.
    // - Progress starts from 0.0 at characters left = 10 and reaches 1.0 at characters left = 0.
    if (maxLength > 20 && charLeft <= 10) {
      double progress = (10 - charLeft) / 10.0;
      return (true, progress);
    }

    // For small maxLength (≤ 20):
    // - The overlay appears once characters left is less than maxLength.
    // - Here progress calculation threshold is maxLength and overlay visibility threshold is (maxLength - 1).
    // - Progress starts from ((maxLength-19)/maxLength) at charLeft = 19 and reaches 1.0 at charLeft = 0.
    if (maxLength <= 20 && maxLength > charLeft) {
      double progress = (maxLength - charLeft) / maxLength;
      return (true, progress);
    }

    // If none of the conditions match, we don’t show the overlay.
    return (false, 0.0);
  }

  static Future<MeDirectory> getDocumentDirectoryPath() async {
    if (kIsWeb) {
      return currentDirectory;
    }
    return meDirectory((await getApplicationDocumentsDirectory()).path);
  }

  static Future<String> initializeDocumentDirectoryPath() async =>
      documentDirectoryPath = (await getDocumentDirectoryPath()).path;

  static DateTime? getDateTimeForTask(
    MeDateTime? meDateTime,
    bool isStartTimeSet,
    bool tmzUnAffected,
  ) {
    if (meDateTime == null) {
      return null;
    }
    if (tmzUnAffected) {
      DateTime date = DateTime.parse(meDateTime.dateString);
      if (isStartTimeSet) {
        List<String> timeParts = meDateTime.timeString.split(':');
        if (timeParts.length < 2) {
          timeParts = meDateTime.timeString.split('-');
          if (timeParts.length < 2) {
            timeParts = ['00', '00'];
          }
        }
        int hour = int.tryParse(timeParts[0]) ?? 0;
        int minute = int.tryParse(timeParts[1]) ?? 0;
        return DateTime(
          date.year,
          date.month,
          date.day,
          hour,
          minute,
        );
      } else {
        return date;
      }
    } else {
      return meDateTime.dateTime;
    }
  }

  /// Returns a sublist of [sortedList] based on optional [startDate] and [endDate].
  ///
  /// If both [startDate] and [endDate] are null, returns the entire [sortedList].
  /// If [startDate] is provided and [endDate] is null, returns sublist from [startDate] to end of [sortedList].
  /// If [endDate] is provided and [startDate] is null, returns sublist from start of [sortedList] to [endDate].
  /// If both [startDate] and [endDate] are provided, returns sublist from [startDate] to [endDate].
  ///
  /// If [startDate] or [endDate] are not exact matches in [sortedList], selects the nearest dates accordingly.
  static List<DateTime> getSublist(
    List<DateTime> sortedList, {
    DateTime? startDate,
    DateTime? endDate,
  }) {
    if (startDate == null && endDate == null) {
      return sortedList;
    }

    int startIndex = 0;
    int endIndex = sortedList.length - 1;

    // Binary search for startIndex
    if (startDate != null) {
      int left = 0;
      int right = sortedList.length - 1;
      while (left <= right) {
        int mid = left + ((right - left) ~/ 2);
        if (sortedList[mid].isBefore(startDate)) {
          left = mid + 1;
        } else {
          right = mid - 1;
        }
      }
      startIndex = left;
    }

    // Binary search for endIndex
    if (endDate != null) {
      int left = 0;
      int right = sortedList.length - 1;
      while (left <= right) {
        int mid = left + ((right - left) ~/ 2);
        if (sortedList[mid].isAfter(endDate)) {
          right = mid - 1;
        } else {
          left = mid + 1;
        }
      }
      endIndex = right;
    }

    // Return sublist from startIndex to endIndex
    return sortedList.sublist(startIndex, endIndex + 1);
  }

  static Future<void> removeDocsFromLocalDB(
    DocInfo removedDocsIDs,
    DatabaseRepository? databaseRepository,
  ) async {
    final Log log = MeLogger.getLogger(LogTags.dataSyncService);

    if (databaseRepository == null) {
      Future<void> openDatabase() async {
        databaseRepository = DatabaseRepository();
        await databaseRepository!.openDriftDb();
      }

      await Future.wait([
        openDatabase(),
      ]);
    }
    // Todo: As we are doing direct delete here. Synced devices won't get changed docs.
    if (databaseRepository != null) {
      log.d('Removing docs from local db: $removedDocsIDs');
      // Todo: First need to fetch the doc to confirm if that doc can be deleted or not.
      // Or improve query to use the createdAt from docInfo to fetch doc if found delete.
      // if (removedDocsIDs.todos.isNotEmpty) {
      //
      //   await databaseRepository!.deleteTodosByIds(removedDocsIDs.todos);
      // }
      // if (removedDocsIDs.notes.isNotEmpty) {
      //   await databaseRepository!.deleteNotesByIds(removedDocsIDs.notes);
      // }
      // if (removedDocsIDs.habitSetups.isNotEmpty) {
      //   await databaseRepository!
      //       .deleteHabitSetupsByIds(removedDocsIDs.habitSetups);
      // }
      // if (removedDocsIDs.journalSetups.isNotEmpty) {
      //   await databaseRepository!
      //       .deleteJournalSetupsByIds(removedDocsIDs.todos);
      // }
      // if (removedDocsIDs.moneyTrackerTransactions.isNotEmpty) {
      //   await databaseRepository!.deleteMoneyTrackerEventsByIds(
      //     removedDocsIDs.moneyTrackerTransactions,
      //   );
      // }
      //
      // if (removedDocsIDs.calendarIds.isNotEmpty ||
      //     removedDocsIDs.calendarAccountIds.isNotEmpty) {
      //   final actions = await databaseRepository!.getAllCalendarEventActions();
      //
      //   final setups = await databaseRepository!.getAllCalendarEventSetups();
      //   List<CalendarEventAction> actionsList = [];
      //   List<CalendarEventSetup> setupsList = [];
      //   List<String> actionIds = [];
      //   List<String> setupIds = [];
      //   if (actions.length >= setups.length) {
      //     for (var i = 0; i < actions.length; i++) {
      //       if (removedDocsIDs.calendarIds.contains(actions[i].calendarId) ||
      //           removedDocsIDs.calendarAccountIds
      //               .contains(actions[i].calendarAccountId)) {
      //         actionIds.add(actions[i].id);
      //         actionsList.add(actions[i]);
      //       }
      //
      //       if (i < setups.length) {
      //         if (removedDocsIDs.calendarIds.contains(setups[i].calendarId) ||
      //             removedDocsIDs.calendarAccountIds
      //                 .contains(setups[i].calendarAccountId)) {
      //           setupIds.add(setups[i].id);
      //           setupsList.add(setups[i]);
      //         }
      //       }
      //     }
      //   } else {
      //     for (var i = 0; i < setups.length; i++) {
      //       if (removedDocsIDs.calendarIds.contains(setups[i].calendarId) ||
      //           removedDocsIDs.calendarAccountIds
      //               .contains(setups[i].calendarAccountId)) {
      //         setupIds.add(setups[i].id);
      //         setupsList.add(setups[i]);
      //       }
      //
      //       if (i < actions.length) {
      //         if (removedDocsIDs.calendarIds.contains(actions[i].calendarId) ||
      //             removedDocsIDs.calendarAccountIds
      //                 .contains(actions[i].calendarAccountId)) {
      //           actionIds.add(actions[i].id);
      //           actionsList.add(actions[i]);
      //         }
      //       }
      //     }
      //   }
      //   await databaseRepository!.deleteCalendarEvents(
      //     calendarActions: actionsList,
      //     calendarSetups: setupsList,
      //   );
      //
      //   if (setupIds.isNotEmpty) {
      //     await databaseRepository!.deleteCalendarSetupsByIds(setupIds);
      //     // final setups = await databaseRepository!.getAllCalendarEventSetups();
      //     // final list = setups.map((item) => item.id).toList();
      //     // if (list.isNotEmpty) {
      //     //   print('cal: yo more' + list.toString());
      //     //   await databaseRepository!.deleteCalendarSetupsByIds(list);
      //     //   final setups =
      //     //       await databaseRepository!.getAllCalendarEventSetups();
      //     //   final temp = setups.map((item) => item.id).toList();
      //     //   print('cal: yo last' + temp.toString());
      //     // }
      //     // print('cal: yo done');
      //   }
      //   if (actionIds.isNotEmpty) {
      //     await databaseRepository!.deleteCalendarActionsByIds(actionIds);
      //   }
      // }
      log.d('Removed docs from local db: $removedDocsIDs');
    }
  }

  static List<List<T>> distributeIntoBatches<T>(List<T> items, int maxBatches) {
    if (maxBatches <= 0) {
      throw ArgumentError('Number of batches must be greater than zero.');
    }

    int numItems = items.length;
    if (numItems == 0) return [];

    int itemsPerBatch = numItems ~/ maxBatches;
    int remainder = numItems % maxBatches;

    List<List<T>> batches = [];
    int start = 0;

    for (int i = 0; i < maxBatches; i++) {
      int batchSize = itemsPerBatch + (i < remainder ? 1 : 0);
      if (start >= numItems) {
        break;
      }
      batches.add(items.sublist(start, start + batchSize));
      start += batchSize;
    }

    // Remove empty batches
    batches.removeWhere((batch) => batch.isEmpty);

    return batches;
  }

  static Future<bool> checkInternetForced() async {
    return await InternetConnectivityService.instance.hasInternet;
  }

  /// Useful in case for some reason database got corrupted or some very
  /// critical unrecoverable error occurs.
  static Future<void> recoverAppFromCriticalState({
    bool logoutUser = true,
  }) async {
    if (recoverAppFromCriticalStateInProg) return;
    try {
      recoverAppFromCriticalStateInProg = true;
      await Future.wait([
        DatabaseRepository().cleanDatabase(),
        if (logoutUser) AuthenticationService().logOut(),
        MeEncryption().reset(),
        MeLogger.resetLogs(),
        StorageRepository(
          localStorageRepository: LocalStorageRepository(),
          databaseRepository: DatabaseRepository(),
        ).clearSyncFolder(),
      ]);
    } catch (e) {
      final Log log = MeLogger.getLogger(LogTags.appBloc);
      log.e('Error while app cleanup to reset it for app recovery: $e');
    } finally {
      // Restarts the app on the native side but may not reset flutter activity.
      // Note: restart_app package doesn't support macOS and Windows
      if (!MePlatform.isMacOS && !MePlatform.isWindows && !MePlatform.isIOS) {
        await Restart.restartApp();
      } else {
        goRouter.go(authPage);
      }
      // Restarts the app by rebuilding the widget provided on runApp forcing a tree rebuild.
      recoverAppFromCriticalStateInProg = false;
      appRestartNotifier.value = UniqueKey();
    }
  }

  String formatTimezone(String timezone) {
    // Regular expression to match offset patterns like +11, +5:30, -5:30, +0630
    final regex = RegExp(r'([+-])(\d{1,2})(?::?(\d{2}))?');

    // Check if the input matches the offset format
    final match = regex.firstMatch(timezone);

    if (match != null) {
      // Extract components
      String sign = match.group(1)!; // + or -
      String hours = match.group(2)!.padLeft(2, '0'); // Ensure 2 digits
      String minutes = match.group(3)?.padLeft(2, '0') ??
          '00'; // Default to 00 if not provided

      // Return formatted timezone
      return 'GMT$sign$hours:$minutes';
    }

    // If the input is not in offset format, assume it's already a valid timezone name
    return timezone;
  }
}

class Debouncer {
  Debouncer({
    required this.delay,
  });

  final Duration delay;
  Timer? _timer;

  void debounce(void Function() onAction) {
    if (_timer != null) {
      _timer!.cancel();
    }

    _timer = Timer(delay, () {
      onAction.call();
    });
  }
}

/// Todo: This method is under performing, deprecated and replaced at almost all places with [getTasksSortedIds]
/// DON'T USE THIS METHOD.
class TasksSorter {
  static TimeOfDay? getStartTime(dynamic object) {
    if (object is Todo && object.isStartTimeSet) {
      return TimeOfDay.fromDateTime(object.tmzSafeStartAt!);
    }
    if (object is HabitSetupAndAction && object.habitSetup.isStartTimeSet) {
      return TimeOfDay.fromDateTime(object.habitSetup.tmzSafeStartAt);
    }
    if (object is HabitSetup && object.isStartTimeSet) {
      return TimeOfDay.fromDateTime(object.tmzSafeStartAt);
    }
    if (object is JournalSetupAndAction && object.journalSetup.isStartTimeSet) {
      return TimeOfDay.fromDateTime(object.journalSetup.tmzSafeStartAt);
    }
    if (object is JournalSetup && object.isStartTimeSet) {
      return TimeOfDay.fromDateTime(object.tmzSafeStartAt);
    }
    if (object is CalendarEventSetup && object.isStartTimeSet) {
      return TimeOfDay.fromDateTime(object.tmzSafeStartAt!);
    }
    if (object is Note) {
      return TimeOfDay.fromDateTime(object.localUpdatedAt);
    }
    return null;
  }

  static DateTime getCreatedTime(dynamic object) {
    // Don't use TimeOfDay as that only uses hour and minutes.
    // That will cause some tasks to have same created time that should not be
    // possible.
    if (object is Todo) {
      return object.createdAt.onlyTime();
    } else if (object is HabitSetupAndAction) {
      return object.habitSetup.createdAt.onlyTime();
    } else if (object is HabitSetup) {
      return object.createdAt.onlyTime();
    } else if (object is JournalSetupAndAction) {
      return object.journalSetup.createdAt.onlyTime();
    } else if (object is JournalSetup) {
      return object.createdAt.onlyTime();
    } else if (object is CalendarEventSetup) {
      return object.createdAt.onlyTime();
    } else {
      return object.createdAt.onlyTime();
    }
  }

  static String getTitle(dynamic object) {
    if (object is Todo) {
      return object.title;
    }
    if (object is HabitSetupAndAction) {
      return object.habitSetup.title;
    }
    if (object is HabitSetup) {
      return object.title;
    }
    if (object is JournalSetupAndAction) {
      return object.journalSetup.title;
    }
    if (object is JournalSetup) {
      return object.title;
    }
    if (object is Note) {
      return object.title;
    }
    if (object is CalendarEventSetup) {
      return object.title;
    }
    return '';
  }

  static bool areTypesEqual<T1, T2>(T1 a, T2 b) {
    if (a is Type && b is Type) {
      return a == b;
    } else if (a is Type && b is! Type) {
      return a == b.runtimeType;
    } else if (a is! Type && b is Type) {
      return a.runtimeType == b;
    } else {
      return a.runtimeType == b.runtimeType;
    }
  }

  static int sortByTypeAndTitle({
    required dynamic a,
    required dynamic b,
    required List<dynamic> typesOrVars,
  }) {
    if (typesOrVars.isEmpty) {
      return 0;
    }
    dynamic typeOrVar = typesOrVars.first;

    if (areTypesEqual(a, typeOrVar) && areTypesEqual(b, typeOrVar)) {
      return customTitleCompare(a, b);
    } else if (areTypesEqual(a, typeOrVar) && !areTypesEqual(b, typeOrVar)) {
      return -1;
    } else if (!areTypesEqual(a, typeOrVar) && areTypesEqual(b, typeOrVar)) {
      return 1;
    } else {
      return sortByTypeAndTitle(
        a: a,
        b: b,
        typesOrVars: List.from(typesOrVars)..remove(typeOrVar),
      );
    }
  }

  // Custom rules comparator function.
  static int customTitleCompare(dynamic a, dynamic b) {
    String aTitle = getTitle(a);
    String bTitle = getTitle(b);

    if (aTitle.toLowerCase().trim() == bTitle.toLowerCase().trim()) {
      // If both title are same then sort by created time in ascending order.
      return getCreatedTime(a).compareTo(getCreatedTime(b));
    }

    // Testing compare natural instead of below custom logic.
    // compareNatural will help sort numbers in a better way.
    return compareNatural(aTitle, bTitle);

    // bool isNumber(String s) => double.tryParse(s) != null;
    // bool isAlphabet(String s) =>
    //     s.isNotEmpty && s.toLowerCase() != s.toUpperCase();
    // bool isSpecialCharacter(String s) => !isNumber(s) && !isAlphabet(s);
    //
    // int aIndex = 0, bIndex = 0;
    //
    // while (aIndex < aTitle.length && bIndex < bTitle.length) {
    //   String aSubstring = aTitle.substring(aIndex, aIndex + 1).toLowerCase();
    //   String bSubstring = bTitle.substring(bIndex, bIndex + 1).toLowerCase();
    //
    //   bool aIsNumber = isNumber(aSubstring);
    //   bool bIsNumber = isNumber(bSubstring);
    //   bool aIsAlphabet = isAlphabet(aSubstring);
    //   bool bIsAlphabet = isAlphabet(bSubstring);
    //   bool aIsSpecialChar = isSpecialCharacter(aSubstring);
    //   bool bIsSpecialChar = isSpecialCharacter(bSubstring);
    //
    //   if (aIsAlphabet && bIsAlphabet) {
    //     int charComparison = aSubstring.compareTo(bSubstring);
    //
    //     if (charComparison != 0) {
    //       return charComparison;
    //     }
    //
    //     // If the characters are the same, skip the character part and continue
    //     // comparing the remaining substrings.
    //     aIndex += 1;
    //     bIndex += 1;
    //   } else if (aIsNumber && bIsNumber) {
    //     int aNum = int.parse(aSubstring);
    //     int bNum = int.parse(bSubstring);
    //     int numComparison = aNum.compareTo(bNum);
    //
    //     if (numComparison != 0) {
    //       return numComparison;
    //     }
    //
    //     // If the numbers are the same, skip the number part and continue
    //     // comparing the remaining substrings.
    //     aIndex += aNum.toString().length;
    //     bIndex += bNum.toString().length;
    //   } else if (aIsAlphabet && !bIsAlphabet) {
    //     return -1; // Prioritize alphabet over special character.
    //   } else if (!aIsAlphabet && bIsAlphabet) {
    //     return 1; // Prioritize alphabet over special character.
    //   } else if (aIsNumber && !bIsNumber) {
    //     return -1; // Prioritize number over special character.
    //   } else if (!aIsNumber && bIsNumber) {
    //     return 1; // Prioritize number over special character.
    //   } else if (aIsSpecialChar && !bIsSpecialChar) {
    //     return -1; // Prioritize special character over the alphabet and number.
    //   } else if (!aIsSpecialChar && bIsSpecialChar) {
    //     return 1; // Prioritize special character over the alphabet and number.
    //   } else if (aIsSpecialChar && bIsSpecialChar) {
    //     // If both are special character then just compare the created time in ascending order.
    //     return getCreatedTime(a).compareTo(getCreatedTime(b));
    //   }
    // }
    //
    // return aTitle.length -
    //     aIndex; // Prioritize shorter strings if one is a prefix of the other.
  }

  /// IMP note: This doesn't take dates in consideration. So only use it for sorting
  /// when you are sure that all the items are of the same date.
  /// Acceptable tasks type are Todo, HabitSetupAndAction, HabitSetup, JournalSetupAndAction, JournalSetup and Note.
  static List<dynamic> chronologicalSortedList(List<dynamic> listOfTasks) {
    // assert
    assert(
      listOfTasks.every(
        (element) =>
            element is Todo ||
            element is HabitSetupAndAction ||
            element is HabitSetup ||
            element is JournalSetupAndAction ||
            element is JournalSetup ||
            element is Note ||
            element is CalendarEventSetup,
      ),
      'Invalid type of tasks.',
    );

    List<dynamic> list = List.from(listOfTasks);

    list.sort((a, b) {
      final time1 = getStartTime(a);
      final time2 = getStartTime(b);

      List<dynamic> typesSortOrder = [
        Todo,
        HabitSetupAndAction,
        HabitSetup,
        JournalSetupAndAction,
        JournalSetup,
        Note,
        CalendarEventSetup,
      ];

      // Rules of sorting:
      // 0. By ascending order of time we mean before time to after time.
      // 1. Move all time ones to the list top in ascending order.
      // 2. Sort by types if no time like 1st-todos 2nd-habits 3rd-journals 4th-notes.
      // 3. In the types sort by title.
      if (time1 == null && time2 == null) {
        // Sorting by Type and title:
        // 1. Sort for Todos:
        //    1. If a is Todo and b is Todo then sort by title in ascending order.
        //       1. For title the priority order is 1st-alphabets 2nd-numbers 3rd-specialCharacters.
        //       2. If two title here has the same name then sort by their created time in ascending order.
        //    2. If a is Todo and b is not Todo then move a to the top of b.
        //    3. If a is not Todo and b is Todo then move b to the top of a.
        // 2. Do the same like Todos for Habits, Journals and Notes respectively.
        return sortByTypeAndTitle(a: a, b: b, typesOrVars: typesSortOrder);
      } else if (time1 == null) {
        // If time1 is null then move b to the top of a.
        return 1;
      } else if (time2 == null) {
        // If time2 is null then move a to the top of b.
        return -1;
      } else if (time1 == time2) {
        // If same time then sort by title in ascending order.
        return sortByTypeAndTitle(a: a, b: b, typesOrVars: typesSortOrder);
      }
      // If both are not null and not equal then sort by time in ascending order.
      return time1.compareTo(time2);
    });
    return list;
  }
}

String removeNewLinesAndReplaceWithSpaces(String? text) {
  if (text == null) return '';
  String newText = text.replaceAll('\n', ' ');
  return newText.trim();
}

String getPlainText(String? text) {
  if (text == null) return '';
  if (text.contains('"insert":')) {
    final List<Map<String, dynamic>> ops = jsonStrToJsonObj(text);
    String html = QuillDeltaToHtmlConverter(
      ops,
      ConverterOptions(sanitizerOptions: OpAttributeSanitizerOptions()),
    ).convert();
    RegExp olExp = RegExp(r'(?<=<ol>).*?(?=</ol>)');
    Iterable<Match> olMatches = olExp.allMatches(html);
    for (final Match m in olMatches) {
      String match = m[0]!;
      RegExp liExp = RegExp(r'<li>');
      int num = 0;
      match = match.replaceAllMapped(liExp, (Match m) {
        num++;
        return '\n$num. ';
      });
      html = html.replaceFirst(m[0]!, match);
    }
    RegExp ulExp = RegExp(r'(?<=<ul>).*?(?=</ul>)');
    Iterable<Match> ulMatches = ulExp.allMatches(html);
    for (final Match m in ulMatches) {
      String match = m[0]!;
      RegExp liExp = RegExp(r'<li>');
      match = match.replaceAll(liExp, '\n•');
      html = html.replaceFirst(m[0]!, match);
    }
    html = html.replaceAll('&#47;', '/');
    String desc = html.replaceAll(RegExp(r'<[^>]*>|&[^;]+;'), ' ');
    return desc.trim();
  } else {
    return text;
  }
}

String getTextForCopying(String? text) {
  if (text == null) return '';

  if (!text.contains('"insert":')) return text;

  final doc = Document.fromJson(jsonDecode(text));
  try {
    debugPrint(
      'PlainTextConverter: Starting Delta→HTML→Markdown conversion',
    );

    // Step 1: Delta → HTML (using existing vsc_quill_delta_to_html)

    final List<Map<String, dynamic>> ops = doc.toDelta().toJson();
    debugPrint('PlainTextConverter: Parsed ${ops.length} delta operations');

    final converter = QuillDeltaToHtmlConverter(
      ops,
      ConverterOptions(
        sanitizerOptions: OpAttributeSanitizerOptions(),
        multiLineBlockquote: true,
        multiLineParagraph: true,
      ),
    );
    final html = converter.convert();
    debugPrint(
      'PlainTextConverter: Delta→HTML conversion complete (${html.length} chars): ${html.substring(0, math.min(html.length, 200))}...',
    );

    // Step 2: HTML → Markdown (using html2md with default formatting)
    final markdown = html2md.convert(html);
    debugPrint(
      'PlainTextConverter: HTML→Markdown conversion complete (${markdown.length} chars): ${markdown.substring(0, math.min(markdown.length, 200))}...',
    );

    debugPrint(
      'PlainTextConverter: Conversion successful, final result (${markdown.length} chars)',
    );
    return markdown;
  } catch (e) {
    // Fallback to current implementation if html2md fails
    debugPrint(
      'PlainTextConverter: Conversion failed, falling back to original implementation. Error: $e',
    );

    return doc.toPlainText();
  }
}

int getDiffInDaysReal(DateTime startDate, DateTime endDate) {
  int difference = endDate.onlyDate().difference(startDate.onlyDate()).inDays;
  // Return absolute value.
  return difference.abs() + 1;
}

int getDiffInDaysInt(DateTime startDate, DateTime endDate) {
  int difference = endDate
      .toLocal()
      .onlyDate()
      .difference(startDate.toLocal().onlyDate())
      .inDays;
  return difference + 1;
}

MeString getDiffInDays(DateTime startDate, DateTime endDate) {
  int difference = getDiffInDaysReal(startDate, endDate);
  return difference > 1
      ? MeTranslations.instance.screen_common_daysCount(
          daysCount: difference.toString(),
        )
      : MeTranslations
          .instance.overlay_dateRangePicker_previewSameDateToDateSelect;
}

bool isSameDateTime(DateTime? startDate, DateTime? endDate) {
  if (startDate == null && endDate == null) {
    return true;
  } else if (startDate == null && endDate != null) {
    return false;
  } else if (startDate != null && endDate == null) {
    return false;
  } else if (startDate!.isAtSameMomentAs(endDate!)) {
    return true;
  } else {
    return false;
  }
}

MeString getRepeatText(List<String>? repeatData, {bool isListItem = false}) {
  MeString repeatText = MeString.empty;

  if (repeatData == null || repeatData.isEmpty) {
    repeatText = MeTranslations.instance.bottomSheet_actionCommon_noRepeat;
  } else {
    final rrule = RecurrenceRule.fromString(
      repeatData[0].replaceAll('WKST=SU', 'WKST=MO'),
    );
    if (rrule.frequency == Frequency.daily) {
      repeatText = MeTranslations.instance.bottomSheet_actionCommon_repeatDaily;
    } else if (rrule.frequency == Frequency.weekly) {
      if (rrule.byWeekDays.length == 7) {
        repeatText = MeTranslations.instance.overlay_repeatSelect_weeklyAllDays;
      } else if (isListItem) {
        repeatText = MeTranslations
            .instance.bottomSheet_notificationsMuteDuration_muteForCustom;
      } else {
        // DateFormat format = DateFormat.E('en');
        String dayText = '';
        for (ByWeekDayEntry element in rrule.byWeekDays) {
          dayText += UtilityMethods.getDayFromInt(element.day).text +
              (element == rrule.byWeekDays.last ? '' : ', ');
        }
        repeatText = rrule.byWeekDays.length == 1
            ? MeTranslations.instance
                .overlay_repeatSelect_weeklyOneDay(day: dayText)
            : MeTranslations.instance
                .overlay_repeatSelect_weeklyMultipleDays(days: dayText);
      }
    } else if (rrule.frequency == Frequency.yearly) {
      String month = '';
      int yearlyDay = 1;
      yearlyDay = rrule.byMonthDays[0];

      DateFormat format = DateFormat.MMM('en');
      month = UtilityMethods.getMonthFromInt(rrule.byMonths[0]).capitalize();
      final monthDate = format.parse(month);
      final dateString = '${monthDate.formattedMonth().text} $yearlyDay';
      repeatText = MeTranslations.instance
          .overlay_repeatSelect_previewMonthlyOneMonthAndOneDay(
        date: dateString,
      );
    } else if (rrule.frequency == Frequency.monthly &&
        rrule.byMonthDays.first == -1) {
      List<String> monthlyMonths = [];
      // checking if repeatData[0] contains one ":"
      // This tells that not all months are selected
      // If true then we need to set _monthlyMonths
      if (rrule.hasByMonths) {
        monthlyMonths = rrule.byMonths
            .map((month) => UtilityMethods.getMonthFromInt(month))
            .toList();
      }
      repeatText = !rrule.hasByMonths
          ? MeTranslations.instance.overlay_repeatSelect_monthlyLastDayAllMonths
          : isListItem
              ? MeTranslations
                  .instance.bottomSheet_notificationsMuteDuration_muteForCustom
              : MeTranslations.instance
                  .overlay_repeatSelect_previewMonthlyOneMonthAndOneDay(
                  date: capitalizeFirstLetter(
                    monthlyMonths
                        .toString()
                        .replaceFirst('[', '')
                        .replaceFirst(']', ''),
                  ),
                );
    } else if (rrule.frequency == Frequency.monthly) {
      List<int> monthlyDays = rrule.byMonthDays;
      // checking if repeatData[0] contains two ":"
      // This tells that not all months are selected
      // If true then we need to set _monthlyMonths
      List<String> monthlyMonths = [];
      if (rrule.hasByMonths) {
        monthlyMonths = rrule.byMonths
            .map((month) => UtilityMethods.getMonthFromInt(month))
            .toList();
      }

      // sort monthlyDays
      monthlyDays.sort();
      repeatText = MeString.empty;
      if (!rrule.hasByMonths && monthlyDays.length == 31) {
        repeatText = MeTranslations.instance.overlay_repeatSelect_weeklyAllDays;
      } else if (monthlyMonths.length == 1 && monthlyDays.length == 1) {
        final monthDay = getLangMonthFromMonth(monthlyMonths.first).text +
            monthlyDays.first.toString();
        repeatText = MeTranslations.instance
            .overlay_repeatSelect_previewMonthlyOneMonthAndOneDay(
          date: monthDay,
        );
      } else if (isListItem) {
        repeatText = MeTranslations
            .instance.bottomSheet_notificationsMuteDuration_muteForCustom;
      } else if (monthlyMonths.length == 1 &&
          allDaysSelectedInMonth(monthlyMonths[0], monthlyDays)) {
        repeatText = MeTranslations.instance
            .overlay_repeatSelect_previewMonthlyOneMonthAndAllDay(
          month: getLangMonthFromMonth(monthlyMonths.first).text,
        );
      } else if (rrule.hasByMonths) {
        String monthText = monthlyMonths.map((e) {
          return capitalizeFirstLetter(e);
        }).join(', ');
        String dayText = monthlyDays.map((e) => e.toString()).join(', ');
        if (monthlyDays.length == 31) {
          repeatText = MeTranslations.instance
              .overlay_repeatSelect_previewMonthlyOneMonthAndAllDay(
            month: monthText,
          );
        } else {
          repeatText = MeTranslations.instance
              .overlay_repeatSelect_previewMonthlyOneMonthAndMultipleDay(
            month: monthText,
            dates: dayText,
          );
        }
      } else {
        String dayText =
            monthlyDays.toString().replaceAll('[', '').replaceFirst(']', '');
        repeatText = MeTranslations.instance
            .overlay_repeatSelect_previewMonthlyDates(dates: dayText);
      }
    }
  }
  return repeatText;
}

String capitalizeFirstLetter(String input) {
  List<String> words = input.split(', ');
  List<String> capitalizedWords = words.map((word) {
    if (word.isNotEmpty) {
      word = getLangMonthFromMonth(word).text;
      return word[0].toUpperCase() + word.substring(1);
    } else {
      return word;
    }
  }).toList();
  return capitalizedWords.join(' ');
}

MeString getLangMonthFromMonth(String month) {
  switch (month) {
    case 'jan':
      return MeTranslations.instance.screen_common_monthJanuary;
    case 'feb':
      return MeTranslations.instance.screen_common_monthFebruary;
    case 'mar':
      return MeTranslations.instance.screen_common_monthMarch;
    case 'apr':
      return MeTranslations.instance.screen_common_monthApril;
    case 'may':
      return MeTranslations.instance.screen_common_monthMay;
    case 'jun':
      return MeTranslations.instance.screen_common_monthJune;
    case 'jul':
      return MeTranslations.instance.screen_common_monthJuly;
    case 'aug':
      return MeTranslations.instance.screen_common_monthAugust;
    case 'sep':
      return MeTranslations.instance.screen_common_monthSeptember;
    case 'oct':
      return MeTranslations.instance.screen_common_monthOctober;
    case 'nov':
      return MeTranslations.instance.screen_common_monthNovember;
    case 'dec':
      return MeTranslations.instance.screen_common_monthDecember;
    default:
      return MeTranslations.instance.screen_common_monthJanuary;
  }
}

MeString getShortMonthFromInt(int month) {
  switch (month) {
    case 1:
      return MeTranslations.instance.screen_common_monthJanuary;
    case 2:
      return MeTranslations.instance.screen_common_monthFebruary;
    case 3:
      return MeTranslations.instance.screen_common_monthMarch;
    case 4:
      return MeTranslations.instance.screen_common_monthApril;
    case 5:
      return MeTranslations.instance.screen_common_monthMay;
    case 6:
      return MeTranslations.instance.screen_common_monthJune;
    case 7:
      return MeTranslations.instance.screen_common_monthJuly;
    case 8:
      return MeTranslations.instance.screen_common_monthAugust;
    case 9:
      return MeTranslations.instance.screen_common_monthSeptember;
    case 10:
      return MeTranslations.instance.screen_common_monthOctober;
    case 11:
      return MeTranslations.instance.screen_common_monthNovember;
    case 12:
      return MeTranslations.instance.screen_common_monthDecember;
    default:
      return MeTranslations.instance.screen_common_monthJanuary;
  }
}

MeString getShortDayFromInt(int day) {
  switch (day) {
    case 0:
      return MeTranslations.instance.screen_common_daySunday;
    case 1:
      return MeTranslations.instance.screen_common_dayMonday;
    case 2:
      return MeTranslations.instance.screen_common_dayTuesday;
    case 3:
      return MeTranslations.instance.screen_common_dayWednesday;
    case 4:
      return MeTranslations.instance.screen_common_dayThursday;
    case 5:
      return MeTranslations.instance.screen_common_dayFriday;
    case 6:
      return MeTranslations.instance.screen_common_daySaturday;
    default:
      return MeTranslations.instance.screen_common_daySunday;
  }
}

int getDaysInMonth(int year, int month) {
  if (month < 1 || month > 12) {
    throw ArgumentError('Invalid month: $month');
  }

  if (month == 2) {
    // Check for leap year
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
      return 29; // February has 29 days in a leap year
    } else {
      return 28; // February has 28 days in a non-leap year
    }
  } else if ([4, 6, 9, 11].contains(month)) {
    return 30; // April, June, September, November have 30 days
  } else {
    return 31; // January, March, May, July, August, October, December have 31 days
  }
}

bool allDaysSelectedInMonth(String month, List<int> repeatData) {
  List<String> monthlyMonths = UtilityMethods.getMonthList();
  int index = monthlyMonths.indexOf(month) + 1;
  if (index == -1) {
    return false;
  } else {
    if (getDaysInMonth(
          DateTime.now().year,
          index,
        ) ==
        repeatData.length) {
      return true;
    }
    return false;
  }
}

MeString getTimeAgo(DateTime? dateTime) {
  if (dateTime == null) {
    return MeString.empty;
  }
  Duration duration = DateTime.now().difference(dateTime);
  String timeUnit;
  int timeValue;

  if (duration.inDays > 0) {
    timeUnit = 'd';
    timeValue = duration.inDays;
  } else if (duration.inHours > 0) {
    timeUnit = 'h';
    timeValue = duration.inHours;
  } else if (duration.inMinutes > 0) {
    timeUnit = 'm';
    timeValue = duration.inMinutes;
  } else {
    timeUnit = 's';
    timeValue = duration.inSeconds;
  }

  if (timeUnit == 'd') {
    return MeTranslations.instance.screen_common_updatedInDays(
      days: timeValue.toString(),
    );
  } else if (timeUnit == 'h') {
    return MeTranslations.instance.screen_common_updatedInHours(
      hours: timeValue.toString(),
    );
  } else if (timeUnit == 'm') {
    return MeTranslations.instance.screen_common_updatedInMinutes(
      minutes: timeValue.toString(),
    );
  } else {
    return MeTranslations.instance.screen_common_updatedInSeconds;
  }
}

class CustomMessages implements Messages {
  @override
  String prefixAgo() => '';

  @override
  String suffixAgo() => 'ago';

  @override
  String secsAgo(int seconds) => 'a few secs';

  @override
  String minAgo(int minutes) => 'a minute';

  @override
  String minsAgo(int minutes) => '$minutes mins';

  @override
  String hourAgo(int minutes) => 'an hour';

  @override
  String hoursAgo(int hours) => '$hours hours';

  @override
  String dayAgo(int hours) => 'a day';

  @override
  String daysAgo(int days) => '$days days';

  @override
  String wordSeparator() => ' ';

  @override
  String justNow(int seconds) => '$seconds secs';
}

DateTime combineDateTime(DateTime dateOnly, DateTime timeOnly) {
  // Extract the year, month, and day from dateOnly
  int year = dateOnly.year;
  int month = dateOnly.month;
  int day = dateOnly.day;

  // Extract the hour and minute from timeOnly
  int hour = timeOnly.hour;
  int minute = timeOnly.minute;
  int second = timeOnly.second;
  int millisecond = timeOnly.millisecond;
  int microsecond = timeOnly.microsecond;

  // Create a new DateTime with the combined date and time
  DateTime combinedDateTime = DateTime(
    year,
    month,
    day,
    hour,
    minute,
    second,
    millisecond,
    microsecond,
  );

  return combinedDateTime;
}

BoxFit getBoxFit(SizedBox? iconSize) {
  return (iconSize != null && iconSize.width != null && iconSize.height != null)
      ? (iconSize.width! > iconSize.height!
          ? BoxFit.fitWidth
          : BoxFit.fitHeight)
      : (iconSize != null && iconSize.width != null && iconSize.height == null)
          ? BoxFit.fitWidth
          : (iconSize != null &&
                  iconSize.width == null &&
                  iconSize.height != null)
              ? BoxFit.fitHeight
              : BoxFit.scaleDown;
}

///Parse the string in PnYnMnDTnHnMnS or PnW format and returns the result in ISODuration format.
Duration? iso8601ToDuration(String? isoDurationString) {
  if (isoDurationString == null) return null;

  late int start;
  // ignore: unused_local_variable
  num year = 0, month = 0, week = 0, day = 0, min = 0, hour = 0, seconds = 0;

  ///Extracts the duration from string using position identifier
  num getDuration(int start, int durationPosition) {
    try {
      return num.parse(isoDurationString.substring(start, durationPosition));
    } catch (e) {
      throw Exception(e);
    }
  }

  //Identify duration positions
  int periodPos = isoDurationString.indexOf('P');
  int yearPos = isoDurationString.indexOf('Y', periodPos);
  int weekPos = isoDurationString.indexOf('W');
  int dayPos = isoDurationString.indexOf('D');
  int timePos = isoDurationString.indexOf('T');
  int hourPos =
      isoDurationString.indexOf('H', timePos.isNegative ? 0 : timePos);
  int minutePos =
      isoDurationString.indexOf('M', timePos.isNegative ? 0 : timePos);
  int monthPos = 0;
  if (minutePos.isNegative) {
    monthPos = isoDurationString.indexOf('M', 0);
  } else if (!minutePos.isNegative) {
    monthPos = isoDurationString.indexOf('M', 0);
    if (minutePos == monthPos) {
      monthPos = -1;
    }
  }
  int secondsPos =
      isoDurationString.indexOf('S', timePos.isNegative ? 0 : timePos);

  //Year
  if (!yearPos.isNegative && !periodPos.isNegative) {
    year = getDuration(1, yearPos);
  }
  //Month
  if (!periodPos.isNegative && !monthPos.isNegative) {
    start = yearPos.isNegative ? periodPos + 1 : yearPos + 1;
    month = getDuration(start, monthPos);
  }
  //Week
  if (!periodPos.isNegative && !weekPos.isNegative) {
    start = monthPos.isNegative
        ? yearPos.isNegative
            ? periodPos + 1
            : yearPos + 1
        : monthPos + 1;
    week = getDuration(start, weekPos);
  }
  //Day
  if (!periodPos.isNegative && !dayPos.isNegative) {
    start = weekPos.isNegative //yes
        ? monthPos.isNegative
            ? yearPos.isNegative
                ? periodPos + 1
                : yearPos + 1
            : monthPos + 1
        : weekPos + 1;
    day = getDuration(start, dayPos);
  }
  //Hour
  if (!timePos.isNegative && !hourPos.isNegative) {
    start = timePos + 1;
    hour = getDuration(start, hourPos);
  }
  //Minute
  if (!timePos.isNegative && !minutePos.isNegative) {
    start = hourPos.isNegative ? timePos + 1 : hourPos + 1;
    min = getDuration(start, minutePos);
  }
  //Seconds
  if (!timePos.isNegative && !secondsPos.isNegative) {
    start = minutePos.isNegative
        ? hourPos.isNegative
            ? timePos + 1
            : hourPos + 1
        : minutePos + 1;
    seconds = getDuration(start, secondsPos);
  }

  return Duration(
    days: day.toInt(),
    hours: hour.toInt(),
    minutes: min.toInt(),
    seconds: seconds.toInt(),
  );
}

DateTime? parseDate(dynamic date) {
  DateTime? res;
  if (date == null) {
    res = null;
  } else if (date is Timestamp) {
    res = date.toDate();
  } else if (date is String) {
    res = DateTime.parse(date).toLocal();
  } else if (date is Map) {
    if (date.isEmpty) {
      res = null;
    } else {
      res = Timestamp(date['_seconds'], date['_nanoseconds']).toDate();
    }
  }
  res = res.toMillisecondPrecision;
  return res;
}

TimeOfDay? extractTimeFromStringDate(String date) {
  if (date.isEmpty) return null;
  return TimeOfDay.fromDateTime(DateTime.parse(date));
}

bool? parseBool(dynamic value) {
  if (value == null) return null;
  if (value is bool) return value;
  if (value is int) return value == 1;
  if (value is String) return value == 'true';
  return null;
}

int? storeBool(dynamic value) {
  if (value == null) return null;
  if (value is bool) return (value ? 1 : 0);
  if (value is int) return value;
  if (value is String) return (value == 'true' ? 1 : 0);
  return null;
}

/// Assumes the given path is a text-file-asset.
Future<Map<String, dynamic>> getAssetConfigMap(
  ConfigType type, {
  bool onlyData = false,
}) async {
  Map<String, dynamic> json = jsonDecode(
    await rootBundle.loadString('mevolve_generator_configs/${type.name}.json'),
  );
  return onlyData ? json['data'] as Map<String, dynamic> : json;
}

Map<String, dynamic> mergeTranslations({
  required Map<String, dynamic> oldTranslations,
  required Map<String, dynamic> newTranslations,
}) {
  // Ensure both parameters are not null and have the expected structure
  if (oldTranslations.isEmpty || newTranslations.isEmpty) {
    return oldTranslations;
  }

  // Ensure data maps exist
  if (!oldTranslations.containsKey('data') ||
      !newTranslations.containsKey('data')) {
    return oldTranslations;
  }

  Map<String, dynamic> dataMap =
      oldTranslations['data'] as Map<String, dynamic>? ?? <String, dynamic>{};
  Map<String, dynamic> newDataMap =
      newTranslations['data'] as Map<String, dynamic>? ?? <String, dynamic>{};

  // Handle upgrade case: update pid to toPid if fromPid and toPid exist
  if (newTranslations.containsKey('fromPid') &&
      newTranslations.containsKey('toPid')) {
    int? fromPid = newTranslations['fromPid'];
    int? toPid = newTranslations['toPid'];

    // If the old pid matches fromPid, update it to toPid
    if (oldTranslations['pid'] == fromPid) {
      oldTranslations['pid'] = toPid;
    }
  }

  // Merge data for different languages
  for (var language in ['en', 'fr', 'de', 'it', 'pt', 'es']) {
    if (dataMap.containsKey(language) && dataMap[language] != null) {
      // Ensure both maps are not null before calling addAll
      final existingLangMap = dataMap[language] as Map<String, dynamic>?;
      final newLangMap = newDataMap[language] as Map<String, dynamic>?;

      if (existingLangMap != null && newLangMap != null) {
        existingLangMap.addAll(newLangMap);
      }
    } else if (newDataMap.containsKey(language) &&
        newDataMap[language] != null) {
      dataMap[language] = newDataMap[language];
    }
  }

  // Merge the rest of the newTranslations into oldTranslations
  oldTranslations.addAll(newTranslations);
  oldTranslations.update('data', (value) => dataMap);

  return oldTranslations;
}

Map<String, dynamic> mergeColors({
  required Map<String, dynamic> oldColors,
  required Map<String, dynamic> newColors,
}) {
  // Handle upgrade case: update pid to toPid if fromPid and toPid exist
  if (newColors.containsKey('fromPid') && newColors.containsKey('toPid')) {
    int? fromPid = newColors['fromPid'];
    int? toPid = newColors['toPid'];

    // If the old pid matches fromPid, update it to toPid
    if (oldColors['pid'] == fromPid) {
      oldColors['pid'] = toPid;
    }
  }

  // Merge themes data
  Map<String, dynamic> dataMap = oldColors['data']?['themes'] ?? {};
  Map<String, dynamic> newDataMap = newColors['data']['themes'] ?? {};

  for (String colorName in newDataMap.keys) {
    for (String themeType
        in (newDataMap[colorName] as Map<String, dynamic>).keys) {
      if (dataMap.containsKey(colorName) &&
          (dataMap[colorName] as Map<String, dynamic>).containsKey(themeType) &&
          newDataMap.containsKey(colorName) &&
          (newDataMap[colorName] as Map<String, dynamic>)
              .containsKey(themeType)) {
        // Merge the theme type data
        var sourceMap = newDataMap[colorName][themeType];

        // Convert to Map<String, dynamic> if needed
        Map<String, dynamic> sourceMapDynamic;
        if (sourceMap is Map<String, String>) {
          sourceMapDynamic = Map<String, dynamic>.from(sourceMap);
        } else {
          sourceMapDynamic = sourceMap as Map<String, dynamic>;
        }

        (dataMap[colorName][themeType] as Map<String, dynamic>)
            .addAll(sourceMapDynamic);
      } else if (newDataMap.containsKey(colorName) &&
          (newDataMap[colorName] as Map<String, dynamic>)
              .containsKey(themeType)) {
        if (!dataMap.containsKey(colorName)) {
          dataMap[colorName] = <String, dynamic>{};
        }
        dataMap[colorName][themeType] = newDataMap[colorName][themeType];
      }
    }
  }

  // Merge the rest of the newColors into oldColors
  oldColors.addAll(newColors);
  oldColors['data'].update('themes', (value) => dataMap);

  return oldColors;
}

Color? parseHextoColor(String? colorString) {
  if (colorString == null) return null;
  if (colorString.length == 7) {
    colorString = 'FF${colorString.substring(1)}';
  } else if (colorString.length == 9) {
    colorString = colorString.substring(7) + colorString.substring(1, 7);
  }
  final colorInt = int.tryParse(colorString, radix: 16);
  if (colorInt == null) {
    return null;
  }
  return Color(colorInt);
}

String getMeBaseDomain() {
  String envName = AppConfig.instance.environmentType.name;
  if (envName == 'prod') {
    return 'web.mevolve.app';
  }
  return '$envName-mevolve.pages.dev';
}

String getMeApiBaseDomain([bool shouldAffectFromEmulator = false]) {
  String envName = AppConfig.instance.environmentType.name;
  String region = FirebaseFunctionsRepository.functionsLocation;

  String functionUrlBase = AppConfig.instance.usingFirebaseEmulator &&
          shouldAffectFromEmulator
      ? 'http://${AppConstant.firebaseHost}:${FirebaseConstants.firebaseFunctionsPort}'
      : 'https://$region-mevolve-$envName.cloudfunctions.net';

  return functionUrlBase;
}

// index 1 is for invalidCombination for dateRange
// index 2 is for invalid year for month selected like 31 feb
List<bool> isRepeatValidForDateRange(
  Map<String, List<String>> repeatData,
  DateTime? startDate,
  DateTime? endDate,
) {
  endDate ??= DateTime(
    3000,
    12,
    31,
    23,
    59,
    59,
  );
  bool isForever = endDate.year == 3000;
  final repeat = repeatData['data'] ?? [];
  if (repeat.isEmpty) {
    return [true, true];
  }
  final rrule = RecurrenceRule.fromString(repeat[0]);
  if (rrule.frequency == Frequency.daily) {
    int days = endDate.difference(startDate!).inDays + 1;
    return [days >= 1, true];
  } else if (rrule.frequency == Frequency.weekly) {
    List<int> daysInNum = rrule.byWeekDays.map((day) => day.day).toList();
    repeat.map((day) => UtilityMethods.getWeekDay(day)).toList();
    DateTime end = endDate;
    DateTime start = startDate!;
    int daysLeft = end.difference(start).inDays;
    if (daysLeft >= 7) {
      return [true, true];
    } else {
      List<int> weekDays = [];
      while (start.isBefore(end.add(const Duration(days: 1)))) {
        int weekday = start.weekday;
        weekDays.add(weekday);
        start = start.add(const Duration(days: 1));
      }
      if (daysInNum.every((element) => weekDays.contains(element))) {
        return [true, true];
      }
      return [false, true];
    }
  } else if (rrule.frequency == Frequency.yearly) {
    int date = rrule.byMonthDays[0];
    int monthInNum = rrule.byMonths[0];
    int startYear = startDate!.year;
    int endYear = endDate.year;
    int monthGap = Jiffy.parseFromDateTime(endDate)
        .diff(Jiffy.parseFromDateTime(startDate), unit: Unit.month)
        .toInt();
    var startJiffyParsed = Jiffy.parseFromDateTime(startDate);
    var endJiffyParsed = Jiffy.parseFromDateTime(endDate);
    int? validYear;
    for (int i = startYear; i <= endYear; i++) {
      DateTime current = DateTime(i, monthInNum, date);
      var currentJiffyParsed = Jiffy.parseFromDateTime(current);
      bool isBetween = currentJiffyParsed.isBetween(
        startJiffyParsed,
        endJiffyParsed,
        unit: Unit.day,
      );
      if (isBetween) {
        validYear = i;
      }
    }
    DateTime newDate = DateTime(
      validYear ?? (monthGap <= 12 ? startDate.year : endDate.year),
      monthInNum,
      date,
    );
    if (newDate.isBeforeOrEqualTo(endDate) &&
        newDate.isAfterOrEqualTo(startDate)) {
      if (isForever && monthInNum == 2) {
        if (date > 29) {
          return [false, false];
        }
      }
      if (!isForever) {
        for (int i = startYear; i <= endYear; i++) {
          int maxDayOfMonthInYear = getDaysInMonth(i, monthInNum);
          if (date > maxDayOfMonthInYear) {
            return [false, false];
          }
        }
      }
      return [true, true];
    } else {
      return [false, true];
    }
  } else if (rrule.frequency == Frequency.monthly &&
      rrule.byMonthDays.isNotEmpty &&
      rrule.byMonthDays.first == -1) {
    int monthsDiff = differenceInMonths(startDate!, endDate);
    if (!rrule.hasByMonths && monthsDiff >= 1) {
      if (monthsDiff >= 12) {
        return [true, true];
      } else {
        return [false, true];
      }
    } else if (!rrule.hasByMonths && monthsDiff == 0) {
      if (endDate.day == endDate.lastDay()) {
        return [true, true];
      } else {
        return [false, true];
      }
    } else {
      List<int> monthInNum = rrule.byMonths;
      DateTime start = startDate;
      DateTime end = endDate;
      if (end.day != end.lastDay()) {
        return [false, true];
      }
      List<int> rangeMonths = [];
      int yearDiff = end.year - start.year;
      if (yearDiff == 0) {
        for (int i = start.month; i <= end.month; i++) {
          rangeMonths.add(i);
        }
      } else {
        for (int i = start.month; i <= 12; i++) {
          rangeMonths.add(i);
        }
        for (int i = 1; i <= end.month; i++) {
          rangeMonths.add(i);
        }
      }
      rangeMonths = rangeMonths.toSet().toList();
      rangeMonths.sort();
      bool isMonthsValid = monthInNum.every((element) {
        return rangeMonths.contains(element);
      });
      if (!isMonthsValid) {
        return [false, true];
      } else {
        return [true, true];
      }
    }
  } else if (rrule.frequency == Frequency.monthly) {
    bool isAllMonth = !rrule.hasByMonths;
    if (isAllMonth) {
      List<int> months = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
      List<int> days = rrule.byMonthDays;
      List<int> rangeMonths = [];
      int yearDiff = endDate.year - startDate!.year;
      if (yearDiff == 0) {
        for (int i = startDate.month; i <= endDate.month; i++) {
          rangeMonths.add(i);
        }
      } else {
        for (int i = startDate.month; i <= 12; i++) {
          rangeMonths.add(i);
        }
        for (int i = 1; i <= endDate.month; i++) {
          rangeMonths.add(i);
        }
      }
      int maxDay = days.reduce(math.max);
      List<int> years = [];
      for (int i = startDate.year; i <= endDate.year; i++) {
        years.add(i);
      }
      for (int year in years) {
        if (year == years.last) {
          int endMonth = endDate.month;
          for (int i = 1; i <= endMonth; i++) {
            int daysInMonth = getDaysInMonth(year, i);
            if (maxDay > daysInMonth) {
              return [false, false];
            }
          }
        } else {
          for (var month in months) {
            int daysInMonth = getDaysInMonth(year, month);
            if (maxDay > daysInMonth) {
              return [false, false];
            }
          }
        }
      }
      // duplicates from rangeMonths
      rangeMonths = rangeMonths.toSet().toList();
      bool isMonthsValid = months.every((element) {
        return rangeMonths.contains(element);
      });
      return [isMonthsValid, true];
    }
    List<int> selectedMonths = rrule.byMonths;
    List<int> selectedDays = rrule.byMonthDays;
    List<int> rangeMonths = [];
    int yearDiff = endDate.year - startDate!.year;
    if (yearDiff == 0) {
      for (int i = startDate.month; i <= endDate.month; i++) {
        rangeMonths.add(i);
      }
    } else {
      for (int i = startDate.month; i <= 12; i++) {
        rangeMonths.add(i);
      }
      for (int i = 1; i <= endDate.month; i++) {
        rangeMonths.add(i);
      }
    }
    bool isMonthsValid = selectedMonths.every((element) {
      return rangeMonths.contains(element);
    });
    List<int> daysBetween = getDaysInRangeAsInt(startDate, endDate);
    bool isDaysValid = selectedDays.every((element) {
      return daysBetween.contains(element);
    });
    int year = startDate.year;
    if (isMonthsValid && isDaysValid) {
      int maxDay = selectedDays.reduce(math.max);
      for (var month in selectedMonths) {
        int daysInMonth = getDaysInMonth(year, month);
        if (maxDay > daysInMonth) {
          return [false, false];
        }
      }
      return [true, true];
    } else {
      return [false, true];
    }
  }
  return [false, false];
}

String getFullWeekdayName(String shortDayName) {
  switch (shortDayName.toLowerCase()) {
    // Using toLowerCase to make it case-insensitive
    case 'mon':
      return 'Monday';
    case 'tue':
      return 'Tuesday';
    case 'wed':
      return 'Wednesday';
    case 'thu':
      return 'Thursday';
    case 'fri':
      return 'Friday';
    case 'sat':
      return 'Saturday';
    case 'sun':
      return 'Sunday';
    default:
      return 'Unknown'; // Return 'Unknown' or throw an exception if the day is not recognized
  }
}

DateTime getLastDateOfMonth(int year, int month) {
  // Create a DateTime object for the first day of the following month
  DateTime nextMonth = DateTime(year, month + 1, 1);
  // Subtract one day to get the last day of the given month
  DateTime lastDayOfMonth = nextMonth.subtract(const Duration(days: 1));
  return lastDayOfMonth;
}

List<int> getDaysInRangeAsInt(DateTime startDate, DateTime endDate) {
  List<int> daysList = [];
  DateTime currentDate = startDate;
  while (
      currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
    daysList = daysList.toSet().toList();
    daysList.sort();
    if (daysList.length >= 31) {
      break;
    }
    daysList.add(currentDate.day);
    currentDate = currentDate.add(const Duration(days: 1));
  }
  return daysList;
}

List<int> getMonthsInRangeAsInt(DateTime startDate, DateTime endDate) {
  List<int> monthsList = [];
  DateTime currentDate = startDate;
  while (
      currentDate.isBefore(endDate) || currentDate.isAtSameMomentAs(endDate)) {
    monthsList = monthsList.toSet().toList();
    monthsList.sort();
    if (monthsList.length >= 12) {
      break;
    }
    monthsList.add(currentDate.month);
    currentDate = DateTime(currentDate.year, currentDate.month + 1, 1);
  }
  return monthsList;
}

String truncateWithEmojis(String text, int maxLength) {
  // Convert the string into Unicode code points
  List<int> codeUnits = text.runes.toList();

  // Check if we need to truncate
  if (codeUnits.length <= maxLength) {
    return text; // No need to truncate
  }

  // Truncate to the desired length
  List<int> truncated = codeUnits.take(maxLength).toList();

  // Convert back to a string
  return String.fromCharCodes(truncated);
}

int differenceInMonths(DateTime date1, DateTime date2) {
  int yearDiff = date2.year - date1.year;
  int monthDiff = date2.month - date1.month;
  // Include the first month if the end date day is greater than or equal to the start date day
  if (date2.day >= date1.day) {
    return yearDiff * 12 + monthDiff + 1;
  } else {
    return yearDiff * 12 + monthDiff;
  }
}

String getThreeDecimalDigits(double number) {
  String stringNumber = number.toString();
  int decimalIndex = stringNumber.indexOf('.');

  if (decimalIndex == -1) {
    // No decimal point, add .000
    return '$stringNumber.000';
  } else {
    String decimalPart = stringNumber.substring(decimalIndex + 1);
    if (decimalPart.length >= 3) {
      // Truncate to 3 decimal places
      return stringNumber.substring(0, decimalIndex + 4);
    } else {
      // Pad with zeros if less than 3 decimal places
      return stringNumber.padRight(decimalIndex + 4, '0');
    }
  }
}

MeString formatIncomeExpenseAmount(num value, {String currencySymbol = ''}) {
  // Determine the suffix and divisor based on the magnitude of the value
  String suffix;
  num divisor;
  if (value.abs() >= 1e12) {
    suffix = 'T';
    divisor = 1e12;
  } else if (value.abs() >= 1e9) {
    suffix = 'B';
    divisor = 1e9;
  } else if (value.abs() >= 1e6) {
    suffix = 'M';
    divisor = 1e6;
  } else if (value.abs() >= 1e3) {
    suffix = 'K';
    divisor = 1e3;
  } else {
    suffix = '';
    divisor = 1;
  }
  // Divide the value
  String formattedValue = getThreeDecimalDigits(value / divisor);
  // if last 4 is .000 then remove it
  if (formattedValue.substring(formattedValue.length - 4) == '.000') {
    formattedValue = formattedValue.substring(0, formattedValue.length - 4);
  }

  // Combine the currency symbol, formatted value, and suffix
  return MeString.fromVariable(
    '$currencySymbol $formattedValue$suffix'.replaceFirst('-', ''),
  );
}

Future<String> getVersionBuild() async {
  String appVersionText = AppConfig.instance.environmentType !=
          EnvironmentType.prod
      ? 'v${AppConfig.instance.version}+${AppConfig.instance.buildNumber}(${AppConfig.instance.environmentType.name})'
      : 'v${AppConfig.instance.version}+${AppConfig.instance.buildNumber}';
  return appVersionText;
}

void checkUserFeedback() {
  BuildContext? context = authenticatedGlobalContext;
  context!.read<FeedbackCubit>().updateTaskCount(count: 1);
  if (!(context.read<AppBloc>().state.userMetadata?.isStoreFeedbackGiven ??
          false) &&
      (context.read<FeedbackCubit>().state.taskCount % 10 == 0) &&
      (MePlatform.isAndroid || MePlatform.isIOS || MePlatform.isMacOS)) {
    showFeedbackDialog(context);
  }
}

String getAppStoreId() {
  switch (AppConfig.instance.environmentType) {
    case EnvironmentType.dev:
      return '6477729761';
    case EnvironmentType.qa:
      return '6477729716';
    case EnvironmentType.staging:
      return '6477729685';
    case EnvironmentType.prod:
      return '6477729724';
    case EnvironmentType.hotfix:
      return '6477729516';
  }
}

String getMeFileUrl(String url, AttachmentUploadStatus storageType) {
  if (storageType == AttachmentUploadStatus.local) {
    const String marker = '/filesToSync';
    int index = url.indexOf(marker);
    if (index != -1) {
      String relativePath = url.substring(index + 1);
      final basePath = UtilityMethods.documentDirectoryPath;

      if (basePath != null) {
        String resUrl = path.normalize(path.join(basePath, relativePath));
        return resUrl;
      }
    }
  }
  return url;
}

bool haveOriginalFileNamesChanged(
  List<MeAttachmentInfo>? currentAttachments,
  List<MeAttachmentInfo>? newAttachments,
) {
  // First, check if the lists have the same length
  if (currentAttachments?.length != newAttachments?.length) {
    return true;
  }
  if (currentAttachments == null) return false;
  // Compare original file names
  for (int i = 0; i < currentAttachments.length; i++) {
    if (currentAttachments[i].originalFileName !=
        newAttachments?[i].originalFileName) {
      return true;
    }
  }
  return false;
}

/// Compares attachment lists for user-initiated change detection, excluding
/// timestamp fields that change during sync operations.
///
/// This function checks if attachments have meaningfully changed from a user's
/// perspective, ignoring automatic timestamp updates from sync operations.
///
/// Fields compared:
/// - id, size, metadata, status, deletedAt, localFilePath, format, fileType, originalFileName
///
/// Fields excluded:
/// - createdAt (changes during sync/restore operations)
///
/// Returns true if attachments have meaningfully changed, false otherwise.
bool haveAttachmentsChangedForUser(
  List<MeAttachmentInfo>? currentAttachments,
  List<MeAttachmentInfo>? newAttachments,
) {
  // Check if both lists are null
  if (currentAttachments == null && newAttachments == null) {
    return false;
  }

  // Check if one is null and the other isn't
  if (currentAttachments == null || newAttachments == null) {
    return true;
  }

  // Check if lengths are different
  if (currentAttachments.length != newAttachments.length) {
    return true;
  }

  // Compare each attachment's meaningful fields
  for (int i = 0; i < currentAttachments.length; i++) {
    final current = currentAttachments[i];
    final updated = newAttachments[i];

    // Compare all fields except createdAt
    if (current.id != updated.id ||
        current.size != updated.size ||
        current.metadata != updated.metadata ||
        current.status != updated.status ||
        current.deletedAt != updated.deletedAt ||
        current.localFilePath != updated.localFilePath ||
        current.format != updated.format ||
        current.fileType != updated.fileType ||
        current.originalFileName != updated.originalFileName) {
      return true;
    }
  }

  return false;
}

MeString getBulletChar() {
  return MeString(String.fromCharCode(0x2022));
}

class NoScaling extends FloatingActionButtonAnimator {
  @override
  Offset getOffset({
    required Offset begin,
    required Offset end,
    required double progress,
  }) {
    if (progress < 0.5) {
      return begin;
    } else {
      return end;
    }
  }

  @override
  Animation<double> getRotationAnimation({required Animation<double> parent}) {
    return Tween<double>(begin: 1.0, end: 1.0).animate(parent);
  }

  @override
  Animation<double> getScaleAnimation({required Animation<double> parent}) {
    return Tween<double>(begin: 1.0, end: 1.0).animate(parent);
  }
}

class DebounceAction {
  DebounceAction(this.delay);

  final Duration delay;
  DateTime? _lastActionTime;
  Duration lastElapsed = Duration.zero;
  final stopwatch = Stopwatch();

  bool shouldRun() {
    DateTime now = DateTime.now().toUtc();
    if (_lastActionTime == null || now.difference(_lastActionTime!) > delay) {
      _lastActionTime = now;
      return true;
    }
    return false;
  }

  bool shouldRunDateTimeIndependent() {
    stopwatch.start();
    Duration difference = stopwatch.elapsed - lastElapsed;
    if (difference > delay) {
      lastElapsed = stopwatch.elapsed;
      return true;
    }
    return false;
  }
}

String hashData(String data, {String algorithm = 'sha256'}) {
  late Digest digest;

  switch (algorithm.toLowerCase()) {
    case 'sha256':
      digest = sha256.convert(utf8.encode(data));
      break;
    case 'sha512':
      digest = sha512.convert(utf8.encode(data));
      break;
    case 'md5':
      digest = md5.convert(utf8.encode(data));
      break;
    // Add more cases for other algorithms if needed
    default:
      throw ArgumentError('Unsupported algorithm: $algorithm');
  }

  return digest.toString();
}

String hashDataInBase64(String data, {String algorithm = 'sha256'}) {
  late Digest digest;

  switch (algorithm.toLowerCase()) {
    case 'sha256':
      digest = sha256.convert(utf8.encode(data));
      break;
    case 'sha512':
      digest = sha512.convert(utf8.encode(data));
      break;
    case 'md5':
      digest = md5.convert(utf8.encode(data));
      break;
    // Add more cases for other algorithms if needed
    default:
      throw ArgumentError('Unsupported algorithm: $algorithm');
  }

  return base64Encode(digest.bytes);
}

String? getCurrentUserEmail() {
  return AuthenticationService().currentUser?.email;
}

String? getCurrentUserUid() {
  try {
    return AuthenticationService().currentUser?.uid;
  } catch (e) {
    // Handle the case where the current user is not authenticated
    return null;
  }
}

String? get getCurrentUserHashedEmail {
  return getCurrentUserEmail() != null
      ? hashData(getCurrentUserEmail()!)
      : null;
}

String? lastCalledId;

/// Wraps a function to ignore repeated calls with the same ID until a different ID is used
void callWithSwitchableUniqueId(String id, Function targetFunction) {
  // Execute the function only if the new ID is different from the last ID
  if (id != lastCalledId) {
    lastCalledId = id; // Update the last ID
    targetFunction(); // Call the target function
  }
}

class MeLoader extends StatelessWidget {
  const MeLoader({super.key, this.loaderDialog});

  final MeLoaderDialog? loaderDialog;

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).extension<MeColorScheme>()!;

    List<MeString> messages = loaderDialog == MeLoaderDialog.loggingInWait
        ? [
            MeTranslations.instance.screen_login_loadingContent,
            MeTranslations.instance.screen_common_loadingHangTight,
          ]
        : loaderDialog == MeLoaderDialog.checkingAvailablePurchase
            ? [
                MeTranslations.instance.screen_subscription_loadingContent,
                MeTranslations.instance.screen_common_loadingHangTight,
              ]
            : [];

    return Material(
      color: Colors.transparent,
      child: PopScope(
        canPop: false,
        child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              LoadingAnimationWidget.threeRotatingDots(
                color: colorScheme.color1,
                size: 40,
              ),
              if (messages.isNotEmpty)
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const SizedBox(
                      height: 32,
                    ),
                    ...messages.map(
                      (item) =>
                          MeText(text: item, meFontStyle: MeFontStyle.A12),
                    ),
                  ],
                ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Utility functions for lastUpdatedBy field management
/// Format: "displayName⟨hashedEmail⟩" where ⟨⟩ are uncommon delimiters
class LastUpdatedByUtils {
  static const String _delimiter = '⟨⟩';

  /// Creates a lastUpdatedBy string with name and hashedEmail
  /// Format: "John Doe⟨abc123hashedemail⟩"
  static String createLastUpdatedBy() {
    String? displayName;

    try {
      displayName = authenticatedGlobalContext
          ?.read<AppBloc>()
          .state
          .userData
          ?.userInfo
          .name;
    } catch (e) {
      //
    }

    if (displayName == null || displayName.isEmpty) {
      displayName = AuthenticationService().currentUser?.displayName ?? 'NA';
    }

    String? userEmail = getCurrentUserEmail();

    if (userEmail == null || userEmail.isEmpty) {
      return displayName; // Return only display name if email is not available
    }

    final hashedEmail = hashData(userEmail);
    return '$displayName$_delimiter$hashedEmail';
  }

  /// Gets display name for UI - returns appropriate MeString
  static MeString getDisplayName({
    required String lastUpdatedBy,
    String? currentUserEmail,
    String? ownerName,
  }) {
    final parts = lastUpdatedBy.split(_delimiter);
    final displayName = parts.first;
    final hashedEmail = parts.length > 1 ? parts[1] : null;

    // Check if it's the current user
    if (hashedEmail != null && currentUserEmail != null) {
      final currentUserHashedEmail = hashData(currentUserEmail);
      if (hashedEmail == currentUserHashedEmail) {
        return MeTranslations.instance.bottomSheet_listItems_updateByYou;
      }
    }

    return MeString.fromVariable(displayName);
  }
}
